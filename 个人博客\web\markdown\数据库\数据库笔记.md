# 1.建表时

1.not null非空，default(0)默认等约束写在数据类型后面

2.主键 PRIMARY KEY(数据名)

3.foreign key（外键名) references 要连接的表名(数据名)

4.写表时注意保存查询

5.用小括号而不是大括号

![image-20241124235901930](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241124235901930.png)

# 2.单表查询时

1.where语句中 is一般用来判断数值是否为空，不是作为等号作用

2.in用法，后面给一个括号，表示可以选择的值

```mysql
select rdName,rdDept from reader where rdDept not in('管理学院','物理学院');
```



3.where语句中Like用作模糊匹配,_是通配符，但它表示只匹配单个任意字符。它主要用于当你确切知道要匹配的字符数量，但不确定具体字符是什么的情况，每个 _ 代表一个未知的单个字符位置
select rdName,rdDept from re

```mysql
select rdName,rdDept from reader where rdName like '王_';
```

4.% 通常被用作通配符，表示匹配任意长度（包括零长度）的字符序列。它可以出现在查询条件中字符串的开头、中间或结尾位置，用于查找包含特定字符模式的记录，不管该模式前后有多少其他字符存在

```mysql
select rdName,rdDept from reader where rdName not like '王%';
```

5.使用 REGEXP 操作符可以在 WHERE 子句中对字符串类型的字段进行正则表达式匹配筛选,这里的 ^ 是正则表达式中的起始位置锚定符，表示匹配以 “王” 开头的字符串，所以只要学生姓名是以 “王” 开头的记录都会被查询出来,目前还没懂^起个什么作用

```mysql
select rdName,rdDept from reader where rdName regexp '^王';
select rdName,rdDept from reader where rdName  regexp '^.敏';
```

```mysql
SELECT * FROM students
WHERE student_id REGEXP '^1.3.';
#在这个正则表达式中，^ 表示开头，1 匹配学号开头的数字 1，. 表示匹配任意一个字符（除了换行符），中间的 . 匹配学号的第二位任意字符，3 匹配第三位数字为 3，最后的 . 匹配第四位的任意字符，这样就能筛选出符合学号特定格式要求的记录了。
```

6.case when 语句 then num1 else num2 end

```
SELECT rdDept,
       SUM(CASE WHEN rdSex = '男' THEN 1 ELSE 0 END) AS male_count,
       SUM(CASE WHEN rdSex = '女' THEN 1 ELSE 0 END) AS female_count
FROM Reader
GROUP BY rdDept;#sum中case when then NUM1 else num2 end使用方法
```

7.查询语句内部执行顺序:条件(where条件，分组条件)-筛选（from)-排序-分页

# 3.多表查询

1.内连接：将a表和b表交集部分查询出来:此处将reader表和readertype表满足r.rdType=rt.rdType关系部分查询出来

这是显示内连接：a inner join b on 条件 where

```mysql
SELECT
	r.rdName,
	r.rdDept 
FROM
	reader r
	INNER JOIN readertype rt ON r.rdType = rt.rdType 
```

```mysql
SELECT
	r.rdID,
	r.rdName,
	rt.canLendQty 
FROM
	reader r
	INNER JOIN borrow b ON r.rdID = b.rdID
	INNER JOIN readertype rt ON r.rdType = rt.rdType 
WHERE
	b.bkID = 'bk2024001';
```

```mysql
SELECT
	r.rdID,
	r.rdName 
FROM
	reader r
	INNER JOIN borrow b ON r.rdID = b.rdID
	INNER JOIN book bk ON b.bkID = bk.bkID 
WHERE
	bk.bkName = '数据库原理及应用';
```

```mysql
SELECT
	r.rdID,
	r.rdName,
	bk.bkName,
	b.DateBorrow 
FROM
	reader r
	INNER JOIN readertype rt ON r.rdType = rt.rdType
	INNER JOIN borrow b ON r.rdID = b.rdID
	INNER JOIN book bk ON b.bkID = bk.bkID 
WHERE
	rt.rdTypeName = '博士';
```



下面是隐式内连接

```mysql
select
 a.name,
 a.id
 from
 aod a,bod b on a.id=b.id;
```

2.左外连接:将左表的全部内容和满足on后面的交集部分查询出来

```mysql
SELECT r.rdID, r.rdName
FROM reader r
LEFT JOIN borrow b ON r.rdID = b.rdID
WHERE b.rdID IS NULL;#左外连接
```

3.右外连接同左外连接一致

# 子查询:某一个查询的结果作为另一个查询的条件使用等情况

1.相关子查询与无关子查询:

无关子查询：子查询的结果与外部查询无关，是独立外部查询来计算

相关子查询：子查询的结果会与外部查询一一匹配

```mysql
#查询没有借阅过图书书号为“bk2023004”的读者姓名。（请使用无关子查询和相关子查询两种方法实现）
#第一步查询借了bk2023004的读者的Id
select rdID from borrow where bkID='bk2023001';
#查询没有借过bk2023004的读者姓名
select rdName from reader where rdID not in(select rdID from borrow where bkID='bk2023001');#无关子查询
#无关子查询是指子查询可以独立于外部查询进行计算，其执行过程并不依赖外部查询的当前行数据
#无关子查询通常只执行一次，不管外部查询涉及多少行数据，子查询先得出结果后供外部查询使用

select r.rdname from reader r where not exists( select 1 from borrow b where r.rdID=b.rdID and bkID='bk2024001');#相关子查询
#SELECT 1：这里选择常量1只是一种习惯写法，实际上子查询重点不在于返回具体的值，而是通过WHERE条件去判断是否存在符合条件的记录，所以返回任何非空的值都可以起到相同作用，选择1简洁明了。
#关键是not exists语句
#对于reader表中的每一行（每个读者），都会执行一次这个子查询来判断是否存在相应的借阅记录，如果不存在（即NOT EXISTS条件成立），那么这个读者就满足条件，其姓名就会被查询出来
```

```mysql
#查询价格高于80元且从未被读者借阅过的图书


select b.bkName from book b where b.bkPrice>40 and  bkID not in(select Distinct bkID from borrow bw);#无关子查询


select b.bkName from book b where b.bkPrice>40 and not exists(select 1 from borrow bw where b.bkID=bw.bkID);#有关子查询
```

2.distinct语句可以有去重功能

