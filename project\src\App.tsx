import React, { useState } from 'react';
import { FaSearch, FaCamera, FaKeyboard } from 'react-icons/fa';

function App() {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // 实际应用中这里会处理搜索逻辑
    console.log('Searching for:', searchQuery);
  };

  return (
    <div className="min-h-screen bg-white flex flex-col items-center">
      {/* 顶部导航 */}
      <nav className="w-full p-4 flex justify-end space-x-4 text-sm">
        <a href="#" className="hover:text-blue-600">新闻</a>
        <a href="#" className="hover:text-blue-600">地图</a>
        <a href="#" className="hover:text-blue-600">视频</a>
        <a href="#" className="hover:text-blue-600">图片</a>
        <a href="#" className="hover:text-blue-600">登录</a>
      </nav>

      {/* Logo和搜索区域 */}
      <div className="flex flex-col items-center flex-grow justify-center -mt-20">
        <h1 className="text-6xl font-bold text-blue-600 mb-8">百度</h1>
        
        <div className="w-full max-w-2xl px-4">
          <form onSubmit={handleSearch} className="relative">
            <div className="flex items-center border-2 border-blue-600 rounded-full hover:shadow-lg">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-6 py-3 rounded-full outline-none"
                placeholder="请输入搜索内容"
              />
              
              <div className="flex items-center px-4 space-x-3">
                <button type="button" className="text-gray-400 hover:text-blue-600">
                  <FaKeyboard size={20} />
                </button>
                <button type="button" className="text-gray-400 hover:text-blue-600">
                  <FaCamera size={20} />
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white p-3 rounded-full hover:bg-blue-700"
                >
                  <FaSearch size={20} />
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* 快捷链接 */}
        <div className="mt-8 text-sm space-x-4">
          <a href="#" className="text-blue-600 hover:underline">百度一下</a>
          <a href="#" className="text-blue-600 hover:underline">手机百度</a>
        </div>
      </div>

      {/* 底部信息 */}
      <footer className="w-full py-4 text-center text-sm text-gray-500">
        <p>©2023 Baidu Clone - 仅用于演示</p>
      </footer>
    </div>
  );
}

export default App;