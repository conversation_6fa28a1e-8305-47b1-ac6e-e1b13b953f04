const express = require('express');
const router = express.Router();
const weatherController = require('../controllers/weatherController');
const { validateCityQuery, validateCoordinatesQuery, validateForecastQuery } = require('../middleware/validation');

// 获取当前天气
router.get('/current', validateCityQuery, weatherController.getCurrentWeather);

// 获取天气预报
router.get('/forecast', validateForecastQuery, weatherController.getForecast);

// 通过坐标获取天气
router.get('/coordinates', validateCoordinatesQuery, weatherController.getWeatherByCoordinates);

// 搜索城市
router.get('/search', weatherController.searchCities);

module.exports = router;
