数据结构队列写法：

1.手写

```c++
1.队q[N],f=0,t=-1;
2.empty(){
if(t<f)空
else 不空
}
3.push(){
q[++t]=x;在尾部加一个x
}
4.pop(){
    判断空不空
     不空->f++;
}
5.查询队头
    query_f(){
    判断空不空
        不空->输出q[f]
}
6.队内元素个数
    size(){
    cout<<t-f+1;
}
```



```
#include<iostream>
using namespace std;
const int N=10005;
int q[N];
int f=0,t=-1,n,op,x;
bool empty()
{
    if(t<f)
    return true;
    else return false;
}
int main()
{
    cin>>n;
    while(n--)
    {
        cin>>op;
        if(op==1)
        {
            cin>>x;
            q[++t]=x;
        }
        else if(op==2)
        {
            if(empty())cout<<"ERR_CANNOT_POP"<<endl;
            else 
            {
                f++;
            }
        }
        else if(op==3)
        {
            if(empty())cout<<"ERR_CANNOT_QUERY"<<endl;
            else{
                cout<<q[f]<<endl;
            }
        }
        else{
            cout<<t-f+1<<endl;
        }
    }
    return 0;
}
```

2.sql

![Snipaste_2024-10-22_02-28-22](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_02-28-22.png)