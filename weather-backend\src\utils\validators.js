// 验证城市名称
const validateCity = (city) => {
  if (!city || typeof city !== 'string') {
    return false;
  }
  
  // 城市名称应该是1-100个字符，只包含字母、数字、空格、连字符和逗号
  const cityRegex = /^[a-zA-Z\u4e00-\u9fa5\s\-,]{1,100}$/;
  return cityRegex.test(city.trim());
};

// 验证坐标
const validateCoordinates = (lat, lon) => {
  const latitude = parseFloat(lat);
  const longitude = parseFloat(lon);
  
  // 检查是否为有效数字
  if (isNaN(latitude) || isNaN(longitude)) {
    return false;
  }
  
  // 检查纬度范围 (-90 到 90)
  if (latitude < -90 || latitude > 90) {
    return false;
  }
  
  // 检查经度范围 (-180 到 180)
  if (longitude < -180 || longitude > 180) {
    return false;
  }
  
  return true;
};

// 验证语言代码
const validateLanguage = (lang) => {
  const supportedLanguages = [
    'zh_cn', 'zh_tw', 'en', 'es', 'fr', 'de', 'it', 'ja', 'ko', 'ru'
  ];
  return supportedLanguages.includes(lang);
};

// 验证单位
const validateUnits = (units) => {
  const supportedUnits = ['metric', 'imperial', 'kelvin'];
  return supportedUnits.includes(units);
};

// 验证天数
const validateDays = (days) => {
  const numDays = parseInt(days);
  return !isNaN(numDays) && numDays >= 1 && numDays <= 16;
};

module.exports = {
  validateCity,
  validateCoordinates,
  validateLanguage,
  validateUnits,
  validateDays
};
