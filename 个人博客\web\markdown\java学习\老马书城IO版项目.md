# 对集合版更改思路：

1.我们将集合list元素写入到文件当中，因此需要用到FileOutputstream类,由于我们写入的是一个对象，故需要嵌套一个ObjectOutputstream类

2.展示时我们打开对应的文件,因此用到FileInputstream类,由于我们读取的是一个对象，故需要嵌套一个ObjectInputstream类

3.我们需要给对应的book类实现一个接口，这样这个类才可以输出,在book类声明的地方添加implements Serializable

```java
if (choice == 1) {
    System.out.println("执行功能上架书籍:");
    //上新数据操作,定义书籍编号，书籍名称，书籍作者,每上新一个书籍都要创建一个对象
    book bk = new book();
    System.out.println("请输入书籍编号:");
    int bNo = sc.nextInt();
    bk.setbNo(bNo);
    System.out.println("请输入书籍名称:");
    String bName = sc.next();
    bk.setbName(bName);
    System.out.println("请输入书籍作者:");
    String bAuthor = sc.next();
    bk.setbAuthor(bAuthor);
    ArrayList ls = new ArrayList();
    //创建一个集合用来存放书籍
    ls.add(bk);
    //创建一个f对象来存放文件
    File f=new File("f:\\test.txt");
    //将I/O流接入到文件上去
    FileOutputStream fos=new FileOutputStream(f);
    //由于要输出的是一个对象，还要嵌套一个
    ObjectOutputStream oos=new ObjectOutputStream(fos);
    oos.writeObject(ls);//此处写入出现了乱码
    oos.close();
} else if (choice == 2) {
    System.out.println("执行功能展示书籍:");
    //从文件中读取ls
    File f=new File("f:\\test.txt");
    FileInputStream fis=new FileInputStream(f);
    ObjectInputStream  ois=new ObjectInputStream(fis);
    ArrayList ls=(ArrayList)ois.readObject();
    for (int i = 0; i <= ls.size() - 1; i++) {
        book b = (book) (ls.get(i));
        System.out.println(b.getBno()+"---"+b.getBname()+"---"+b.getBauthor());
    }
```

```java
package 小涵书城IO;

import java.io.Serializable;

public class book implements Serializable {
    //属性，定义书籍编号，书籍名称，书籍作者
     private int bNo;
     private String bName;
     private String bAuthor;


    //方法


    public void setbNo(int bNo) {
        this.bNo = bNo;
    }

    public void setbName(String bName) {
        this.bName = bName;
    }

    public void setbAuthor(String bAuthor) {
        this.bAuthor = bAuthor;
    }

    public int getBno() {
        return bNo;
    }

    public String getBname() {
        return bName;
    }

    public String getBauthor() {
        return bAuthor;
    }
}

```

4.我们需要给功能完善一下，包括，没有书籍上架时，要展示就会报错

故我们展示前先读取文件是否存在，用exists()来判断

5.我们目前操作的集合不是同一个集合，展示时只会展示当前上新的书籍，不是所有书籍

6.下架书籍修改

![image-20241203103546000](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241203103546000.png)
