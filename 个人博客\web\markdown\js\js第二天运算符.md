# js第二天运算符：

## 1.赋值运算符

```
=
+=：a+=1 === a=a+1
-=:a-=1 === a=a-1
*=:a*=2 === a=a*2
/=:a/=3 === a=a/3
%=:a%=3 === a=a%3
同c，c++
```

## 2.一元运算符：自有一个操作符的

## ![image-20241104143526428](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241104143526428.png)

```
1.前置自增
++num
2.后置自增
num++
区别:
1.前:先自增再使用
2.后:先使用再自增
同c，c++，自减运算符也一样
经常用于计数
```

## 3.比较运算符

```
=:赋值
==:左右两边值是否相等
===：左右两边类型和值是否都相等 
同python，判断变量相等得用全等===合适
字符串的比较同python从第一个字符acii码往后第二个大的就大，中文比较对应的字母
NAN不等于任何值，false
小数要运算最好先转换成整数
```

![image-20241104145050143](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241104145050143.png)

![image-20241104145018696](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241104145018696.png)

## 4.逻辑运算符

```
num>5&&num<10 与
num>0||num<0xfff
```

![image-20241104145520852](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241104145520852.png)

![image-20241104150449063](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241104150449063.png)

![image-20241104150620287](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241104150620287.png)

## 5.js语句

