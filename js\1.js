
//js语句可以打分号也可以不打分号
//+可以连接字符串，===可与严格判断数据是否相同
//if语句同c写法
// if () {

// }
// if () {

// } else (){

// }

//if(){} else if(){}... else(){}

//控制台输出是console.log(),括号里面是输出内容

//for(var i=1;i<10;i++){}

//定义函数
//function 名1(参数1,参数2，函数2){;;return 某个变量;},return 后面的值可以被我们接收
//对应前面这个function 声明函数2
//名1(对应参数,function(函数2参数值)){定义语句+return}
//调用和c一致

//数组
//var arr=[1,23,424,2,5]
//console.log(arr.length),console.log(arr[下标从0开始递增])
//arr.push(值)在数组末尾加值，arr.unshift在数组末尾加值
//for(var i=0;i<arr.length;i++){sum+=arr[i];console.log(i,arr[i])}console.log(sum)
//arr.foreach(function(item,index){console.log(item,index)}

//对象,类似于字典
//var obj={
// name='吴悠',
//   age=10
// }
//console.log(obj.name)
// let a = prompt("请输入您的年龄:")
// document.write(`我今年${a}岁了`)

// let t = true
// console.log(true)

// // let num;
// // console.log(num);

// console.log(undefined + 1)
// console.log(null + '我爱你')

// let num = 10
// console.log(typeof num)
// let str = 'pink'
// console.log(typeof str)
// let tt = true
// console.log(typeof tt)
// console.log(typeof null)


// let N = Number('123')
// console.log(N)

// c = Number(prompt('请输入第一个数'))
// d = Number(prompt('请输入第二个数'))
// document.write(`两数想加的和是${c + d}`)

console.log(1 == '1')
console.log(1 === '1')

a = +prompt("请输入一个数")
alert(a % 4 === 0 && a % 100 !== 0)