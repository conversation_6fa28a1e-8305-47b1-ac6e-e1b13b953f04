<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <!-- label后面的标签会产生粘滞性，扩大范围 ，需要for和input中id用同一个名字-->
    <form action="php.php">
        用户名:<input type="text" name="username"> <input type="radio" name="sex" id="nan"> <label for="nan">男</label> <input type="radio" name="sex" id="nv"> <label for="nv">女</label> 
    </form>
</body>
</html>