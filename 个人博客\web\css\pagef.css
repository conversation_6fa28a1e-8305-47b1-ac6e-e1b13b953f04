/* styles.css */
* {
    margin: 0;
    padding: 0;

    /* styles.css */
    body {
        font-family: Arial, sans-serif;
        background-color: #b31b11;
        background-image: url(../images/1.png);
        background-size: 100%, 100%;
        margin: 0;
        padding: 0;
        backdrop-filter: blur(200px);
    }

    body::before {
        content: "";

    }

    header {
        background: rbga(255, 255, 255, 0);
        color: white;
        padding: 10px 0;
        text-align: center;
    }

    nav ul {
        list-style-type: none;
        padding: 0;
    }

    nav ul li {
        display: inline;
        margin: 0 15px;
        list-style: none;
    }

    nav a {
        color: white;
        text-decoration: none;
    }

    nav a:hover {
        text-decoration: underline;
    }

    main {
        padding: 20px;
        text-align: center;
    }

    footer {
        text-align: center;
        padding: 10px 0;
        background: rgba(255, 255, 255, 0);
        color: white;
        position: fixed;
        bottom: 0;
        width: 100%;
    }

    .videof {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .wrapper {
        margin: 0 auto;
        width: 800px;
        height: 1000px;
    }

    .touxiang {
        position: absolute;
        left: 30%;
        top: 30%;
        width: 300px;
        height: 300px;
        border-radius: 50%;
        background: url(../images/touxiang.jpg);
        background-size: cover;
        background-position: center;

    }



    .wrapper .welcome {
        position: relative;
        left: 300px;
        top: 0;
        font: 70px 'Microsoft YaHei';
        color: white;
        text-align: center;
        line-height: 150px;
        width: 600px;
    }


    .desbox1 {
        float: left;
        width: 500px;
        height: 800px;
        margin: 0 50px;
        background: rgb(255 255 255/24%);
        box-shadow: 0 8px 32px 0 rgb(0, 0, 0/37%);
        border-radius: 100px;
    }

    .desbox2 {
        position: relative;
        left: 0px;
        top: 340px;
        width: 200px;
        height: 600px;
        background: rgb(255 255 255/24%);
        box-shadow: 0 8px 32px 0 rgb(0, 0, 0/37%);
        border-radius: 80px;
    }
}