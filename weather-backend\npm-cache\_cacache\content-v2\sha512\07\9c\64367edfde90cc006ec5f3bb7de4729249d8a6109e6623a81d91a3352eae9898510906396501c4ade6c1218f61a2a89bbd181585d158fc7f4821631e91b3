{"_id": "content-disposition", "_rev": "45-849c09d832e4a8534ec78b0f31f36368", "name": "content-disposition", "dist-tags": {"latest": "0.5.4", "next": "1.0.0"}, "versions": {"0.0.0": {"name": "content-disposition", "version": "0.0.0", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "836890c43058eb815cf16b9e8c190ec6221330a2", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.0.0.tgz", "integrity": "sha512-zqVGYor6xP2cKYwj5H72VC9ULsb10mBroy3r01KF/wVUnG4AczVyksZAjwYV3+RowrkS6JqRnby0SO2faVmlUg==", "signatures": [{"sig": "MEQCIDx5UG0J6S2axauwisKSmcEKax4MGLtQjPZezJzbEibDAiAwIpDyjz920RYRqWe+5Jz4gwig19SmuAFd/bDqP99nHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "836890c43058eb815cf16b9e8c190ec6221330a2", "engines": {"node": ">= 0.6"}, "gitHead": "5ab443630137b8adfa5b030d9375d00ae5fcf286", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create an attachment Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.1.0": {"name": "content-disposition", "version": "0.1.0", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "66658a81614f35b209fab5562feeeb4acf25105c", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.1.0.tgz", "integrity": "sha512-oMVzxRlFHQsfQYY0QhjeuZ3BBDeaVLcie5FhLcqCOdiMAO8FAeVkZiOvOshIAy8L6L14tg7g+DojGHNJEGJkaQ==", "signatures": [{"sig": "MEUCIACFZN0trOsnNd5HoS9ndqt+M0WakAsQCfFiU9Ug5vljAiEAtEmaB0GBr7cUvZwmQ5cVqjxRffEEkevNaUxVF/FI1AQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "66658a81614f35b209fab5562feeeb4acf25105c", "engines": {"node": ">= 0.6"}, "gitHead": "66b641f4a42ae66d762ec4995ebad6d75db86e7d", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create an attachment Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.1.1": {"name": "content-disposition", "version": "0.1.1", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "73affd67d9f2795cbc1a28869c6c5b5c469b6e45", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.1.1.tgz", "integrity": "sha512-FUADYPhFwURnwiU+g29P1I/ix/QJzYJTD8kfDHX+5Ozf+l4jrlBHIOgUSDtjd5roL/Ivqkj4peLHyfJ/KG8trQ==", "signatures": [{"sig": "MEUCIQCku030P4C9zmndvwztpbEVYKY7agds4j/iQnM9f2KzowIgZ1FOjOzclHUuGwiTVaLFo+kHkNOfX6RqA6Mm+bVsVVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "73affd67d9f2795cbc1a28869c6c5b5c469b6e45", "engines": {"node": ">= 0.6"}, "gitHead": "ef54df731e18d33ba33562479c8e51e618817b81", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create an attachment Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.2.0": {"name": "content-disposition", "version": "0.2.0", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "74cd6d87997f23fa4d72e3dab5cc0bf2be1fe0ce", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.2.0.tgz", "integrity": "sha512-ggzM2ainTbGv6OZkFtiBPT9GD7LraVirs8tvhGixlrrJ0rEkMzBlM+WgWnBYqTjNnyu8XxrrcRVbRkGF2Ak4zg==", "signatures": [{"sig": "MEUCIF6c3/q+5Bv0wglnO84yMgkIQGgMlcXMxbP5RsuqAFkoAiEA+rd4LA2L5Qvv0tAUvrEZ1ijF1Qa4w5tFo4xSzO7gDNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "74cd6d87997f23fa4d72e3dab5cc0bf2be1fe0ce", "engines": {"node": ">= 0.6"}, "gitHead": "5769394c412b559cc3c11d50e4ee52939cb06248", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create an attachment Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.1.2": {"name": "content-disposition", "version": "0.1.2", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "df995b34be2a2c0c205ea5887d25eb598403e7bf", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.1.2.tgz", "integrity": "sha512-+V3y9LwG776n11LIrkhD2kv0xbRRIYczVqHeCh5IW1hjILD9EK40TeOv/ocXTM2xn1HjTChncwN2njzfys1t4w==", "signatures": [{"sig": "MEUCIE0Hv+lEoAsVsBhsjeOrDA0vwdXUfjrasSXPFZK/EvfvAiEA3vD2W38tQe/HXFCxT923Qi1LruJKwbUfuxp05y894Ig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "df995b34be2a2c0c205ea5887d25eb598403e7bf", "engines": {"node": ">= 0.6"}, "gitHead": "775f752ec49d564eacd0ea6b508e3658f7c0d9b6", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create an attachment Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.3.0": {"name": "content-disposition", "version": "0.3.0", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "c416865f3637ccfdfe954604b8dce158ea6ec075", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.3.0.tgz", "integrity": "sha512-8aWuDEupfhfkRe3QgD+lds7nIV+OwZYmwukz2Tc+ObXNs8/myV9e5a2hhrMLLcqoUkuLkjnlCAYhHggUpXj4LQ==", "signatures": [{"sig": "MEUCIFFf95fdyisfAjZM3Q9nM6oHGATvt6qXXU+0LAhFhMAeAiEAkTS7ReROh+Rl0GraUTXjs3W4hSFJPdCfQYGcvUNa9eM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "c416865f3637ccfdfe954604b8dce158ea6ec075", "engines": {"node": ">= 0.6"}, "gitHead": "f347389f873d79ea7b529bf05857f1ef8ddf98d6", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create an attachment Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.4.0": {"name": "content-disposition", "version": "0.4.0", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "e91e43f22fd1d58b44dfe0cf089502fa9ffec2bb", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.4.0.tgz", "integrity": "sha512-2hP2utn2KZGgeO3FPqGAvIqlgz6a1DPYDQRwriCZ2ZxXWsCA0fdfdi0W5LlBtYtOCTO3a6pHn2Jum4l366YxuA==", "signatures": [{"sig": "MEUCIHHsWcHpLgKlOLw18OfDJpdR5Udyic6Ro5JMaf9FbXlLAiEAr7HIMvQdJQ9h2RCj8FNvJJIVwT5Ytjv5Q880jPQDdO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "e91e43f22fd1d58b44dfe0cf089502fa9ffec2bb", "engines": {"node": ">= 0.6"}, "gitHead": "f09cb955eb1ed0db2d0ed2708a5c95e6f119ad50", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create an attachment Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.5.0": {"name": "content-disposition", "version": "0.5.0", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "4284fe6ae0630874639e44e80a418c2934135e9e", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.0.tgz", "integrity": "sha512-PWzG8GssMHTPSLBoOeK5MvPPJeWU5ZVX8omvJC16BUH/nUX6J/jM/hgm/mrPWzTXVV3B3OoBhFdHXyGLU4TgUw==", "signatures": [{"sig": "MEUCIBoXZOyFIPLccPQhvNDanO8vVcAtFGJ7n6ZrAKAfFtkxAiEA3xsKSozBUKwaOLWcgUKyQwkelKpEUVT5IHIZvkAz820=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "4284fe6ae0630874639e44e80a418c2934135e9e", "engines": {"node": ">= 0.6"}, "gitHead": "f3c915f0c9d9f5ec79713dba24c8c6181b73305d", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create and parse Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.5.1": {"name": "content-disposition", "version": "0.5.1", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "87476c6a67c8daa87e32e87616df883ba7fb071b", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.1.tgz", "integrity": "sha512-LXP3Ekizrynh01Muic+1XMkR46z/d2wAO/TBnwCgdTmpFJrtwkzrCxQCsC7QnNqlShJgrQyygcX2I8oJ0wnzkw==", "signatures": [{"sig": "MEQCIBAj/bSbGz9f1c2tZoRP88z8o3I+8IT5jCO11cZGdNBwAiBKMFavfJVd+2W2c9F3XR8bYzllnTKOjIvPtCeaEa3RMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "87476c6a67c8daa87e32e87616df883ba7fb071b", "engines": {"node": ">= 0.6"}, "gitHead": "7b391db3af5629d4c698f1de21802940bb9f22a5", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/content-disposition", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create and parse Content-Disposition header", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.2"}}, "0.5.2": {"name": "content-disposition", "version": "0.5.2", "keywords": ["content-disposition", "http", "rfc6266", "res"], "license": "MIT", "_id": "content-disposition@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition#readme", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "0cf68bb9ddf5f2be7961c3a85178cb85dba78cb4", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.2.tgz", "integrity": "sha512-kRGRZw3bLlFISDBgwTSA1TMBFN6J6GWDeubmDE3AF+3+yXL8hTWv8r5rkLbqYXY4RjPk/EzHnClI3zQf1cFmHA==", "signatures": [{"sig": "MEQCIAYFZ+DJzv/G+1iOfixQ1sDAfd/wu99qaoDZNfbAbxYVAiBO3Jxl9YYR/ZrHju/lfxHSKwFZRJT5PPpX4pzUTHQ2sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "0cf68bb9ddf5f2be7961c3a85178cb85dba78cb4", "engines": {"node": ">= 0.6"}, "gitHead": "2a08417377cf55678c9f870b305f3c6c088920f3", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/content-disposition.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Create and parse Content-Disposition header", "directories": {}, "_nodeVersion": "4.6.0", "devDependencies": {"mocha": "1.21.5", "eslint": "3.11.1", "istanbul": "0.4.5", "eslint-plugin-promise": "3.3.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/content-disposition-0.5.2.tgz_1481246224565_0.35659545403905213", "host": "packages-18-east.internal.npmjs.com"}}, "0.5.3": {"name": "content-disposition", "version": "0.5.3", "keywords": ["content-disposition", "http", "rfc6266", "res"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-disposition@0.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition#readme", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "e130caf7e7279087c5616c2007d0485698984fbd", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.3.tgz", "fileCount": 5, "integrity": "sha512-ExO0774ikEObIAEV9kDo50o+79VCUdEB6n6lzKgGwupcVeRlhrj3qGAfwq8G6uBJjkqLrhT0qEYFcWng8z1z0g==", "signatures": [{"sig": "MEUCIQD7oZVAiVhZqPg8z4CVyVbe6WyOdWgUJAAfgD1fWus3HAIgCHRf3b4FTcVk2gpyITE/oz7ZwcdgaICm6ZMhKvbQZAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19115, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGAStCRA9TVsSAnZWagAAvYUP/1EacAGPVQ+v81km3r0i\n9KorME5iKSDmfrmTmwPqebr9nzY/KupheRlT5dHnxcrSaLVbWRPdxWIG1YbP\nMel73EUEea8hE+CW5X1ThiEAG/UwaNGH5LI0J/K9WG+AHlPRd7soSrPtZ2gV\nnWWKx9g5tjE4j3qH8fhMb+cmrZOAR5xq19st5w5YC1gchmxJftw+VjuyWneW\nOMylWGW3aBAD1lL3uRgdG+FddffUydUsjshi1U0Dq4Pd4JP/skJBJpnF2DmM\nFtbKJd2X+Ff1632wakl2htvnhpDoRwnY60Xkzuz8GeQqi31j8Ll5rvneEMcA\nl5ZJSW3VHJJYJQ0xCay/snWqCo4M40fwFheunTpsXcvjwNxH8qktRTR/8MxU\nHFWQHOUZrK3iNNOOLV4lo8BaQc+8vvqjkBShxOEs0U9ZVNU1lHD3ieBKjGVB\nM29v2L4RTA0URwNP+5a9GCwFz1BJoOLiZcdMu7VsA7cG4cE4eFfbHW3dl3Tx\nekcfYc0dVI2XQZA02RzHNHrgy+gUrpZtp+takxq5buU7fSMX327eJLyOhGzM\n63bThVu5bH7CpOzRvbZNkupDbWgHfue16RKouL0Hr4+lrYdc4TuU8HLCYs+K\n5SP+gtxAvbs6FDp5IOf8OptfYYQsi26GOnW5AzcyeHUyPWFLkT5sLb4Ihi7s\nwfu6\r\n=iuNE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "f6d7cba7ea09dfea1492d5ffe438fe2f2e3cc3bb", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/content-disposition.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Create and parse Content-Disposition header", "directories": {}, "_nodeVersion": "8.14.0", "dependencies": {"safe-buffer": "5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "5.2.0", "eslint": "5.10.0", "istanbul": "0.4.5", "deep-equal": "1.0.1", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-rc.1", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/content-disposition_0.5.3_1545077932478_0.35856888210069715", "host": "s3://npm-registry-packages"}}, "0.5.4": {"name": "content-disposition", "version": "0.5.4", "keywords": ["content-disposition", "http", "rfc6266", "res"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-disposition@0.5.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition#readme", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "fileCount": 5, "integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==", "signatures": [{"sig": "MEUCIDiitW/ByYcU41mYWcwFVlQpvcejAKSYfifN6C2bbZ2IAiEAklgssaMLDQH3csOT2znadlI9It2o7NwlKXKk0fg/5pQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhs9rmCRA9TVsSAnZWagAAzvoP/3wEAAQ/S0JlzpNMVNWA\nHdTPHp2BUSp0iKvqD9mYsdzPjPJqunig2rHGPuMd/EmkTx03MAJmVf5mo9PC\nXJRyE8cduk+AC1KnyhAgf+3YNHLeHr8phIo3LmPndqJS5LHdUF0nC9dCftzs\nTlzJGvPRndD437eB5VZIbIt4ROBxAG72j0KfO60YTUNSwJIbHAfif40baJdK\nTiY8qsmXqJEhObRzSjiNycEdmaZmJyUTeDuQa25MUntrjML+AEM6tsnru7RD\nO9J+3ZnnAsj4wG3xQ8BhTAnj3wXDZB3B3nNVxyiXCpZweSqtNG92Sef83DV6\nRxmE0dEJK7+khs0ym5o1mncRkaZFwxm+KUg5FFL/6s6rNt/RduAXmFVIBP/V\nzvrzfmKxzL23xb0ZOkQO4p6kOMMzAXOh/xE7c0yKiHJDU24rotdgXsi3Zm5a\n0Cy33HZSjeplEv045aE+yfZtfDPmsSCGL9ldBADphzjswDNgn4TDFPIMGjPC\ndtsQ38GXWWbfj+HyqtkrWGNn3TaA1xnqgGlMVo49fypHVuEAD/Wu+xwqKstK\nJC0fQyGXnFs71vWfJLoo9uQtIhyneKIkvBYHakHZfhUByPNFXFZ5qjETp9wY\nGpyo+EPR9Wii4AZUwJoUaBK55MZ5y6slkNEHBFYHmQm2PUVBcZSgNMumjSuq\nY/fV\r\n=VIGF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "73bf21e7c3f55f754932844584061027767289f4", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/content-disposition.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Create and parse Content-Disposition header", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"safe-buffer": "5.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "9.1.3", "eslint": "7.32.0", "istanbul": "0.4.5", "deep-equal": "1.0.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "13.0.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/content-disposition_0.5.4_1639176934492_0.4671385138984676", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "content-disposition", "version": "1.0.0", "keywords": ["content-disposition", "http", "rfc6266", "res"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "content-disposition@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/content-disposition#readme", "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "dist": {"shasum": "844426cb398f934caefcbb172200126bc7ceace2", "tarball": "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==", "signatures": [{"sig": "MEYCIQCQExXj2aT48dQOU8M74Gp1sk6RJkZy59VIMrwFp96EXAIhALbExGqQWb1KE8+7PhurDUVpqJY/txj/U7ezQqaCplgA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19111}, "engines": {"node": ">= 0.6"}, "gitHead": "7a6b472197c3a4336d6ba2ce3c6b570d62d7868a", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/content-disposition.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Create and parse Content-Disposition header", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"safe-buffer": "5.2.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "^9.2.2", "eslint": "7.32.0", "deep-equal": "1.0.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "13.0.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/content-disposition_1.0.0_1725127577656_0.6112743851553943", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-09-19T05:02:12.936Z", "modified": "2025-05-14T14:56:14.397Z", "0.0.0": "2014-09-19T05:02:12.936Z", "0.1.0": "2014-09-19T05:39:30.525Z", "0.1.1": "2014-09-19T15:11:29.395Z", "0.2.0": "2014-09-20T07:26:06.901Z", "0.1.2": "2014-09-20T07:27:06.106Z", "0.3.0": "2014-09-21T00:36:24.194Z", "0.4.0": "2014-10-11T22:03:04.039Z", "0.5.0": "2014-10-11T22:06:03.045Z", "0.5.1": "2016-01-17T21:02:33.711Z", "0.5.2": "2016-12-09T01:17:05.156Z", "0.5.3": "2018-12-17T20:18:52.643Z", "0.5.4": "2021-12-10T22:55:34.663Z", "1.0.0": "2024-08-31T18:06:17.899Z"}, "bugs": {"url": "https://github.com/jshttp/content-disposition/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/content-disposition#readme", "keywords": ["content-disposition", "http", "rfc6266", "res"], "repository": {"url": "git+https://github.com/jshttp/content-disposition.git", "type": "git"}, "description": "Create and parse Content-Disposition header", "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>dd"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "", "readmeFilename": "", "users": {"vwal": true, "chaoliu": true, "jovaage": true, "kistoryg": true, "superjoe": true, "zuojiang": true, "goodseller": true, "simplyianm": true, "xieranmaya": true, "wangnan0610": true}}