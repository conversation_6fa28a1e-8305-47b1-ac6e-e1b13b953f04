{"_id": "on-headers", "_rev": "26-f3831c3e6cad6777537a4123ac14f5f6", "name": "on-headers", "dist-tags": {"latest": "1.1.0"}, "versions": {"0.0.0": {"name": "on-headers", "version": "0.0.0", "keywords": ["event", "headers", "http", "onheaders"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "on-headers@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/on-headers", "bugs": {"url": "https://github.com/expressjs/on-headers/issues"}, "dist": {"shasum": "ee2817f8344325785cd9c2df2b242bbc17caf4c4", "tarball": "https://registry.npmjs.org/on-headers/-/on-headers-0.0.0.tgz", "integrity": "sha512-sd6W+EIQTNDbMndkGZqf1q6x3PlMxAIoufoNhcfpvzrXhtN+IWVyM2sjdsZ3p+TVddtTG5u0lujTglZ+R1VGvQ==", "signatures": [{"sig": "MEUCIEmpEBkrfa55armlt3iEGapT5WlKZbceUMpyDS3uth//AiEArFBEMQvppKjXV+E9HDh/fpZsA4kP7bdsX1xmdI2+KEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/on-headers.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Execute a listener when a response is about to write headers", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "~1.18.2", "supertest": "~0.12.1"}}, "1.0.0": {"name": "on-headers", "version": "1.0.0", "keywords": ["event", "headers", "http", "onheaders"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "on-headers@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-headers", "bugs": {"url": "https://github.com/jshttp/on-headers/issues"}, "dist": {"shasum": "2c75b5da4375513d0161c6052e7fcbe4953fca5d", "tarball": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.0.tgz", "integrity": "sha512-urnSdStWpm1wYfy2UrY26qO3HaBwuw55gNu9Xs17cPrfAxw3dhcedis1nWMPFKEyS3JfWasbUIVVl6q3y7fGVQ==", "signatures": [{"sig": "MEQCICNnjh2xt6E3C9KM6v7Mwd9ffdkY5DPRPmyIHp7sXgMKAiADNkYBZ0oI8E7cG4I9fBQUiXl7xi7OdKPBZxWZfopsGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "2c75b5da4375513d0161c6052e7fcbe4953fca5d", "engines": {"node": ">= 0.8.0"}, "gitHead": "434950a0748cd38bf9a04f3fd4f3ff89cf565fda", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/on-headers", "type": "git"}, "_npmVersion": "1.4.21", "description": "Execute a listener when a response is about to write headers", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.0.1": {"name": "on-headers", "version": "1.0.1", "keywords": ["event", "headers", "http", "onheaders"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "on-headers@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/on-headers", "bugs": {"url": "https://github.com/jshttp/on-headers/issues"}, "dist": {"shasum": "928f5d0f470d49342651ea6794b0857c100693f7", "tarball": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.1.tgz", "integrity": "sha512-Hmfug855QMIrXA8SCoblfPRTzkGwAOMaSygo5hN2fC5Se2YJLJGPaC0wytTWMAplYipqVY9FZQLKGQjwqoYyqA==", "signatures": [{"sig": "MEUCIDX2042N/kd+P0OYJb5NYiZoubb0uwYGchX8zyNVEfa3AiEA11AAqmj+a1joHSRGSWsNk2dnWXFtUcZk4t6hsorD0vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "928f5d0f470d49342651ea6794b0857c100693f7", "engines": {"node": ">= 0.8"}, "gitHead": "ab0156a979d72353cfe666cccb3639e016b00280", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/on-headers", "type": "git"}, "_npmVersion": "1.4.28", "description": "Execute a listener when a response is about to write headers", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "2.3.3", "istanbul": "0.3.21", "supertest": "1.1.0"}}, "1.0.2": {"name": "on-headers", "version": "1.0.2", "keywords": ["event", "headers", "http", "onheaders"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "on-headers@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-headers#readme", "bugs": {"url": "https://github.com/jshttp/on-headers/issues"}, "dist": {"shasum": "772b0ae6aaa525c399e489adfad90c403eb3c28f", "tarball": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-pZA<PERSON>+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==", "signatures": [{"sig": "MEUCIHNdro6AHu4OmFAiBhGZcziP8s0H0unZqjhbbXMzxH7vAiEAz8owPzIxW+nY/1NwAHn6bxecN7ErYVravcd0DOoC+0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcb3EnCRA9TVsSAnZWagAAJ74P/1Wi5XP0E+17U7xu01zR\nnaF1VUbte8DFEouoT4ZgQH9ope94hmbAOW6QrMq2S0FxuTTzZFuDSyKBeQ2g\n2JrwURxitZGU36AX+WuPlQ5+Zw5pmDXk6UjdsA8DpNTOCE6T0N7mKS1KQFFH\nantgfK6JJSRYN1Pv0PIsLFrANaWo2j+tgu+c0Eir0fBk7dEe3Q82B9oaDpP8\nKuJ6jDLC7pz/qcJIdUErE6OaNknALNF0GaqW9ikJgthgZvNsMcA5jP7V3CVg\nmnO20iDAGuDRmTjPdDOThrLPX4oYF5p+xWO6LqxHr2oO/f+NrIRrlJu9JQeP\nFHhqCbh9LEBy6gcg7U+XgIuaFHO0Ujy0hu/rs39TowjuQOuQW9QR58UphGtY\ncDe4lRvwDf86XW+vT65ps/gSABNm8hO/ShPi2A5rVlxIPv+RSWrC3Ril+8CA\n6znvDPyrbB5MuAQB5CZ1Jq9Z0AJ+Js/iNrKjxEwaht1JWmH9LN2PzCe2Upk9\n/4s1u4FsPvExwRWyCDgUnvM3bcNg/ksz9E+OMs6B63I+ywW2iFnpXDzFVKLA\np2TB/bhfRYlgf0lK6KNy8B3Z+iTaPX9ie5aRimU7wZaml0jlh0kwzPxwJyPz\nSEI4ssy0yxHXw+d9DDBl4OOTycrM+fSIrL/qKkWaS/6foiGc/cMsgt/YYMww\nBpKq\r\n=27fC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "c05140cde9bbce2127926752433271c6f3fe8787", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/on-headers.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Execute a listener when a response is about to write headers", "directories": {}, "_nodeVersion": "8.15.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.0.1", "eslint": "5.14.1", "istanbul": "0.4.5", "supertest": "3.4.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/on-headers_1.0.2_1550807334461_0.2296705941712791", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "on-headers", "description": "Execute a listener when a response is about to write headers", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["event", "headers", "http", "onheaders"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/on-headers.git"}, "devDependencies": {"eslint": "6.8.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.21.2", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "10.2.0", "nyc": "15.1.0", "supertest": "4.0.2"}, "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks test/test.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-upstream-hashes": "node scripts/update-upstream-hashes.js", "upstream": "mocha --reporter spec --check-leaks test/upstream.js", "version": "node scripts/version-history.js && git add HISTORY.md"}, "_id": "on-headers@1.1.0", "gitHead": "4b017af88f5375bbdf3ad2ee732d2c122e4f52b0", "bugs": {"url": "https://github.com/jshttp/on-headers/issues"}, "homepage": "https://github.com/jshttp/on-headers#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-737ZY3yNnXy37FHkQxPzt4UZ2UWPWiCZWLvFZ4fu5cueciegX0zGPnrlY6bwRg4FdQOe9YU8MkmJwGhoMybl8A==", "shasum": "59da4f91c45f5f989c6e4bcedc5a3b0aed70ff65", "tarball": "https://registry.npmjs.org/on-headers/-/on-headers-1.1.0.tgz", "fileCount": 5, "unpackedSize": 8850, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCUVy2is5P4m0N3XB6teM1voZQw32BHfeSecrmPI3g9nwIhAOy9BKrr9XRi3X2nCi6bTF9emn8xiSsXYR0lCrXKJhMK"}]}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/on-headers_1.1.0_1752766891478_0.8198063790950396"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-05-14T01:43:08.147Z", "modified": "2025-07-17T15:41:31.899Z", "0.0.0": "2014-05-14T01:43:08.147Z", "1.0.0": "2014-08-10T23:14:54.117Z", "1.0.1": "2015-09-30T03:47:06.558Z", "1.0.2": "2019-02-22T03:48:54.600Z", "1.1.0": "2025-07-17T15:41:31.684Z"}, "bugs": {"url": "https://github.com/jshttp/on-headers/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/on-headers#readme", "keywords": ["event", "headers", "http", "onheaders"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/on-headers.git"}, "description": "Execute a listener when a response is about to write headers", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# on-headers\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][ci-image]][ci-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nExecute a listener when a response is about to write headers.\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install on-headers\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar onHeaders = require('on-headers')\n```\n\n### onHeaders(res, listener)\n\nThis will add the listener `listener` to fire when headers are emitted for `res`.\nThe listener is passed the `response` object as it's context (`this`). Headers are\nconsidered to be emitted only once, right before they are sent to the client.\n\nWhen this is called multiple times on the same `res`, the `listener`s are fired\nin the reverse order they were added.\n\n## Examples\n\n```js\nvar http = require('http')\nvar onHeaders = require('on-headers')\n\nhttp\n  .createServer(onRequest)\n  .listen(3000)\n\nfunction addPoweredBy () {\n  // set if not set by end of request\n  if (!this.getHeader('X-Powered-By')) {\n    this.setHeader('X-Powered-By', 'Node.js')\n  }\n}\n\nfunction onRequest (req, res) {\n  onHeaders(res, addPoweredBy)\n\n  res.setHeader('Content-Type', 'text/plain')\n  res.end('hello!')\n}\n```\n\n## Testing\n\n```sh\n$ npm test\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/on-headers/master?label=ci\n[ci-url]: https://github.com/jshttp/on-headers/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/on-headers/master\n[coveralls-url]: https://coveralls.io/r/jshttp/on-headers?branch=master\n[node-image]: https://badgen.net/npm/node/on-headers\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/on-headers\n[npm-url]: https://npmjs.org/package/on-headers\n[npm-version-image]: https://badgen.net/npm/v/on-headers\n", "readmeFilename": "README.md", "users": {"jimjin": true, "tarunbk": true, "mojaray2k": true, "henrytseng": true, "rocket0191": true, "shanewholloway": true, "carlosvillademor": true}}