{"_id": "body-parser", "_rev": "841-6f49f0db48b39adbb8eb9fb6bdc1a38b", "name": "body-parser", "dist-tags": {"latest": "2.2.0", "next": "2.1.0"}, "versions": {"1.0.0": {"name": "body-parser", "version": "1.0.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.0.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "95c8a2861cd150dc195d50840ea4614149455e80", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.0.0.tgz", "integrity": "sha512-nBv25q4Mmz/ObizPi2xoi5elHpvaQheNWYMfAziwEbjnY6taSY50AAtSt+ZUdFJxU7PI6vTZLsuchQRE4epcuQ==", "signatures": [{"sig": "MEUCIQCjm1sSxADhYNoeGaLSseRF6CnHSB1YcD18aeQrn3oAdgIgQDKOzne2UkSpeUpDA61iZnV7/FHjeXfJmXrQLl5Qga4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Connect's body parsing middleware", "directories": {}, "dependencies": {"qs": "~0.6.6", "raw-body": "~1.1.2"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "supertest": "*"}}, "1.0.1": {"name": "body-parser", "version": "1.0.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.0.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "08a2d025ea286f982d5107ea8a2ba953708620e3", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.0.1.tgz", "integrity": "sha512-JNr7xm9WN153w4il8wiWv4w86F0VYhlkOdAYKLrfJvviT7IIJgL9U7JpGn173TaFB4sWy+/6Q/4LP3+bK6Ki1g==", "signatures": [{"sig": "MEQCIAwzqftmrTrwL8paGNt3Q6OtNDhciVddjWMZ2Xwv6XBOAiA1vBIxP9/e02PbEcMXjHBoUKwytynuquwtIiS1zkSCaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "Connect's body parsing middleware", "directories": {}, "dependencies": {"qs": "~0.6.6", "raw-body": "~1.1.2"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "supertest": "*"}}, "1.0.2": {"name": "body-parser", "version": "1.0.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.0.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "3461479a3278fe00fcaebec3314bb54fc4f7b47c", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.0.2.tgz", "integrity": "sha512-/VlRWmVVwfh8rYG/MHGIwesdJYdxBgeddggFG5ssLfX/Qhwt4NKEQhvzwdzVRcy2RhLxni1MKe0uuPfP/unjyw==", "signatures": [{"sig": "MEUCIAHoDhLSb14ld9+5KQLZFkyEGN3QeK8rCDg4IJQ4KBpZAiEAuxCRXr5Lh7qUY729uacI8/moS1eLQ/9+jKWCmMpCbuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.6", "description": "Connect's body parsing middleware", "directories": {}, "dependencies": {"qs": "~0.6.6", "type-is": "~1.1.0", "raw-body": "~1.1.2"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "supertest": "*"}}, "1.1.0": {"name": "body-parser", "version": "1.1.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.1.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "e6a3c46063b329dab0eb7a31bdc1dca3b3185ab9", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.1.0.tgz", "integrity": "sha512-9yrm354GLsytztMuoUf8uRI0wZ7TJQnLBAfSEVxy6KYe7Kp7A6917RLn80EwLKH8qb99pK2azVOMwu73hKHFZA==", "signatures": [{"sig": "MEQCICS/vg8M35iv6k15KCfTf5htfzgPTtmyNExG6x0Q8FpgAiA+d2XvIbdcUuZhvrJ+P4QHYWUBmK+S1AGIOqv7HJSDEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Connect's body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "type-is": "1.1.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}}, "1.1.1": {"name": "body-parser", "version": "1.1.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.1.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "cf3cc10d885e91fc0ffa35a47ecad858238fb880", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.1.1.tgz", "integrity": "sha512-7C9Yj+601JYEnumC32wfIqIqkrUsXkxE2gssCioDb7NCLgXVyVMRIZ1RM0fajzRNRYW08hOxP05JGXdVFhIieg==", "signatures": [{"sig": "MEUCIE55l1ceYO8CKnKnFMfmIpOo8hOwlSPpOcrA148svmufAiEAq3uOYyhi7Y+/tpeg5cnA7NLctC7QXM467XJX7/C3638=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.1.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}}, "1.1.2": {"name": "body-parser", "version": "1.1.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.1.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "c943b64c4cd3c44dc96a4681b02cd54ff29e8cd7", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.1.2.tgz", "integrity": "sha512-A7tD5EdJ/tZtZkeIUYEWKiXZInTNWyh+2lBL5s0ItyO3OVosTGTeoKI1d84qsP84CpZoNdeZ5bpPPNtb/Nx0Sg==", "signatures": [{"sig": "MEYCIQCd4T+SFiw8gqHR/Bxgz2klFHq8HMJN1HhAnRfgqKYIHwIhANoKYLEhvExYqxl99YV2NdsiXxPOJByGdz1BW6qddhK2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.1.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}}, "1.2.0": {"name": "body-parser", "version": "1.2.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.2.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "f6247cc88d4c673c30a926d74fe36c177b9846e0", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.2.0.tgz", "integrity": "sha512-p5oYikf6X7c5NtXzYQd4T3m7a0r4x6baDEHOvPIaRQGI76S4mpvvdPvQDulXjwAULXZ2s/pLNwAal7TC4gEcwA==", "signatures": [{"sig": "MEYCIQCDQn3pHZfAe0m8l1imNI3/m8pyNwkonaou3+msviiaXgIhAOttW6h5KG2IcX8m0xnic4EOjjCjdqlC1HHXVg8i4t+v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}}, "1.2.1": {"name": "body-parser", "version": "1.2.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.2.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "917beee35a88e9f6893728bf1a542111d7d1eb28", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.2.1.tgz", "integrity": "sha512-0BrQaF9eVPjhT+4rg0hjVePQVtemcT7/Anc7DANq3KW99Yxp7J73DN3AqowV70XQx6hz+cOVZhulNmLC6KZ6uQ==", "signatures": [{"sig": "MEYCIQDWbMpJGK2g0c2g+Ly5JvCahEDq1NhyenPYtRKppoGCLAIhAJSHCnmzj6sUmXOXN/YEz5iqzIsFOwKFdlRQB8hZS1Te", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "917beee35a88e9f6893728bf1a542111d7d1eb28", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.9", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}}, "1.2.2": {"name": "body-parser", "version": "1.2.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.2.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "6106373cc1d34d559ebcfdb582e4e37d4312acfb", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.2.2.tgz", "integrity": "sha512-msu39zjr4/jhj9am7DUYXy0frYS6LfdJ7VFvefqJgM0x+1sBwLrIPpRf0anLr+wjdS6AHk5SQyCaZMQ1ZDvTmA==", "signatures": [{"sig": "MEUCIQD/C/5mkM0OrWTNlrLe/HHf/Xzb0QuSs8It1RH1dOta1AIgEEZkC+T5gYOwN2hNkLnzYFBZtwhM6Mo+sZSYlOpjCnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.0", "raw-body": "1.1.6"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}}, "1.3.0": {"name": "body-parser", "version": "1.3.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.3.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "1a651cb9993a01a65531ae38395ceb0199dd7e3c", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.3.0.tgz", "integrity": "sha512-is7gFt5CcTO6f1z1tAYztWNtqBLCBIqYUVjEcwRdOIi7LipX65mAglF4ChMAYBUjrF06v39d9PnPGnwjv3Yg8w==", "signatures": [{"sig": "MEYCIQDkUcraw4M+R9ncRwo7+FRz81Lapl2nFDd/k/TacPIToAIhANfbm1FMjMFdHOn7exA35wotmTMyYSn9l/av4tD0kJpy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "1a651cb9993a01a65531ae38395ceb0199dd7e3c", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter dot", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.9", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.0", "raw-body": "1.1.6"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "istanbul": "0.2.10", "supertest": "~0.12.1"}}, "1.3.1": {"name": "body-parser", "version": "1.3.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.3.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "1a74513fc7897d70db56589e0d03f0a13f1bfa94", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.3.1.tgz", "integrity": "sha512-89/x3K5hk+ZTWDeuT2K0omjSaA8uzT15raeGH65ZktRs+odBKtyjkB2Gw+eIYSqOW9YFDk8wA47y78kVJ/Prlg==", "signatures": [{"sig": "MEUCIQDuDqUPThFhmTZrNAIC9bO+Y6/5beiE0IER39v5jxXw9gIgLOnnkSZLQVvFKM52KuM3sjQsU6z27+Ht6hhGcFxqz78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "1a74513fc7897d70db56589e0d03f0a13f1bfa94", "engines": {"node": ">= 0.8"}, "gitHead": "6c0a1dc628d98bfa586a424f93a45f431e3c6641", "scripts": {"test": "mocha --require should --reporter dot", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.14", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.1", "raw-body": "1.1.6"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "1.4.0": {"name": "body-parser", "version": "1.4.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.4.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "31274668441c2b00bab6ca50a173442d8bac1382", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.4.0.tgz", "integrity": "sha512-K6o4TywK+OOelRFOdTc98PFMj8Y5L0iMQEaoQZpk7GMnbf9yffraTO99efrCJ8FMJO1Y7XDfqgZFWIsCodYDKA==", "signatures": [{"sig": "MEQCIGx4EjxbpVPx7OBYynUOlJu9x45OMY+6YCwE8579rGH0AiBf0S5jDDel3B8qCpNbyHAOUnR+HRNtrEGTEKoRr73dUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "bytes": "1.0.0", "type-is": "1.2.1", "raw-body": "1.2.2", "iconv-lite": "0.4.3", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "1.4.1": {"name": "body-parser", "version": "1.4.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.4.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "29146acc104a353e8cb07b7b3666d2d829bed6b0", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.4.1.tgz", "integrity": "sha512-mOVgsLYLS37pa2Ya6XKjr/i8An2kbxG9SOT3yCZCQJv5P9FAwrdQkyRRlYGwAqPawgwuifSNAo/71NqrEBADzw==", "signatures": [{"sig": "MEUCIQCT4VnNFrLXu3ajXYrsukTiBmOlgOl8/8FPv/2waDyeWQIgWnZ7hXACGqUp5uxDWiIn73dyJQwX3ejoGn0a5hYQDw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "bytes": "1.0.0", "type-is": "1.2.1", "raw-body": "1.2.2", "iconv-lite": "0.4.3", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "1.4.2": {"name": "body-parser", "version": "1.4.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.4.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "e748603c5f79eb06bd75434e219258986328aae7", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.4.2.tgz", "integrity": "sha512-nBUUDXD1nofz/J4ZBdY/QklJqPE/ooXJZqShnCqf/lsgz6DuRFjXGuWCT+z9pr3RsvDHUypIyL4TMsSIfn0cNg==", "signatures": [{"sig": "MEUCIA+novnODgOry3Ah09z1zI36ExYmIpIodAM8nbBBP4mGAiEAoEVhS3V5zq/mn5gKxep80erdM2F4//q+db0en2jUVlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "bytes": "1.0.0", "type-is": "1.3.0", "raw-body": "1.2.2", "iconv-lite": "0.4.3", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "1.4.3": {"name": "body-parser", "version": "1.4.3", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.4.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "4727952cff4af0773eefa4b226c2f4122f5e234d", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.4.3.tgz", "integrity": "sha512-+/wGpsrfMR0d7nPNnmpKAPQVXg37cU3YVvR/hThORfbiJYvzmGHf+A/x0QWtE/s2XMdj2/UTQUweVqNPlkZlEw==", "signatures": [{"sig": "MEUCIQC8D9QpR0WxXM/CrwhMHU5QiuQF7Thi11K9OzvoBqTlvwIgLjZh52OMLNxb4YLy6u163ZjkJT2BvJfYcu/F9UMKirQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "bytes": "1.0.0", "type-is": "1.3.1", "raw-body": "1.2.2", "iconv-lite": "0.4.3", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "1.5.0": {"name": "body-parser", "version": "1.5.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.5.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "c6fce2483c9eeb49ab349ff25a92d336d91055b9", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.5.0.tgz", "integrity": "sha512-UJfZike68QN1mdo0mA+Z0y+0qi10oxOrCPw2CZpP73O/LIfEWHDy9SHhwsME1mdk1WmnltBLddUkfBpuKPH4Ng==", "signatures": [{"sig": "MEUCIQCgrwSUCfpV8Vnagxv19C2hFslppJQiEhuDryHeLpfeTAIgCmwN8JjbFzjK3KOpVG3sHsNz+TaI9G3EJ1WZUWXoc5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "depd": "0.4.2", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.5.1": {"name": "body-parser", "version": "1.5.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.5.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "8d2eb95e987d274ef02fcf56567b3f3a31749c51", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.5.1.tgz", "integrity": "sha512-d2wC2Wt8e4l+K9DRDEgDExujLogpcZHh5tzt3UTl5+4bLQs8na+IZ1ECsQJVcWPDIkyZMWm2YjJDQFGzDDGoeA==", "signatures": [{"sig": "MEUCIQC54SsvTrmOYIVrg90WiN7/a0iUYI5uAzpgBxfslJmm6gIgCtVHhsV0XZIi/Q57hKycMJALpCFKubEluUdaUEwKHhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "depd": "0.4.3", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.5.2": {"name": "body-parser", "version": "1.5.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.5.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "beebacac741b83f62c9137d5685196e1a44304ab", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.5.2.tgz", "integrity": "sha512-e4V5PN/KJTx381nRvKBSFaoRxS/6IImUMy6kouZPHPKan8RZoDPFE+GeT8RdtbUFNIyhNUW2C4vj4lLbVZiodA==", "signatures": [{"sig": "MEQCIAsKHXrsGEMe6WwYbAPH7xA4E24Oa5IhgV5rRrB1ZK6/AiAElVYt0Z+KRji3P5R/BCyhVN8pdO2m3Rnhv7EnNRPB/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.3", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "0.6.6", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.6.0": {"name": "body-parser", "version": "1.6.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.6.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "d02a9d373c7349c281a8b76b41d6bbf60ef2d3f6", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.0.tgz", "integrity": "sha512-2IP/Pq5mKUFyMEEB40i4LOTrqH+CPwUEUC4uacrhYQ2PeoNk+KEuKhyv+eGJeCn9XL5J6MplG0TWfviYsAiBbA==", "signatures": [{"sig": "MEQCIDe3l/mH6Z7LtHCDiX1YKtcTpzZ4CmRGqjCLwKtuGOtZAiAlEQyDKG0NpSivAcxCAX45sLm1ZyH98B1Fav5yivjK/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "d02a9d373c7349c281a8b76b41d6bbf60ef2d3f6", "engines": {"node": ">= 0.8"}, "gitHead": "0a96b14ae61fd579b23c8abd2e88f265dcd48098", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "1.0.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.6.1": {"name": "body-parser", "version": "1.6.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.6.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "3894580ab743e2c2611fec695bae60a883ea6f3b", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.1.tgz", "integrity": "sha512-r9w2z83VWMDUP0Frhyfc53lfUJOyXW1SF3SANuW3OZV5g/D/NGVBsLHO0bkeLtxhaD6Ayud8VhkaKbiHME4uSQ==", "signatures": [{"sig": "MEQCIB4jQ3ssMUBXwO1Icr41HjTCen11SVUf9VDUQnerB5j4AiBdDPjnUnj3WcE+2gOufZDalfgfv6Ee6l1YpKv1kYVbXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "3894580ab743e2c2611fec695bae60a883ea6f3b", "engines": {"node": ">= 0.8"}, "gitHead": "ac01f78038549e16588ee24eec9e47891e9c5a09", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "1.1.0", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.6.2": {"name": "body-parser", "version": "1.6.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.6.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "38952b4fd534395ab3034e9bb40bbdf3dd99c4ce", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.2.tgz", "integrity": "sha512-Qg/WrLDE5NBX95rzpnGjTwy/CYBTDHG+nuuFEIoQ/ZWIpDxfG1XDB0gER6xQVC8jowbyas9wzbPwSZrEFLfS0w==", "signatures": [{"sig": "MEUCIGbUqpMwd/8KD6/oKZSDUIP5h2+3t4RR8zpPXW7Tra+tAiEAwX8+Y8u8u/DpGBZbVE9qBaOLfTqBvwzGKr0FviSUGIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "38952b4fd534395ab3034e9bb40bbdf3dd99c4ce", "engines": {"node": ">= 0.8"}, "gitHead": "2be2282144cf5c6aa7698186a764eedfe3a71ee9", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "1.2.0", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.6.3": {"name": "body-parser", "version": "1.6.3", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.6.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "db3b270bd3ebce5da4d2d2021653454b24861a79", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.3.tgz", "integrity": "sha512-4JfbwJryDlXYLwwBbrFWy/gMbxagpjoB8+EbfJ6bKXnohtLVilFkA/sv/2aRUbGid/n704Vp9+wrwq/SKBie5A==", "signatures": [{"sig": "MEUCIBJj1E0BuCgPgBR7mhnqhCHDxrqmvVYA6YqF9iTKGYowAiEAwgW6gcz9CIMSSfWVUv1/OC+PpaYz3aQbFXMjswn78PE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "db3b270bd3ebce5da4d2d2021653454b24861a79", "engines": {"node": ">= 0.8"}, "gitHead": "bb7c924c6d700da0218188f9a3358f98804a9752", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "1.2.1", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.6.4": {"name": "body-parser", "version": "1.6.4", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.6.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "befd799cc361a46d34e181f5f881f421a1f3b4c1", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.4.tgz", "integrity": "sha512-RluKYJFDExCfZT4MeW9vtKUiCtWgmUYbb7jeKcTUn/9DrsosDNylt8WdQFQgerxBXv9eAz3XDzV/pls2+6DcxQ==", "signatures": [{"sig": "MEQCIHk8VwFHuqPF3xe1SDYzHXs9AFEj1D5wrlkWlKQdFrzwAiBP5GUUv0cqngwMMI5Hsl3xC7GyHdqTz+SqRfo6ZhrQxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "befd799cc361a46d34e181f5f881f421a1f3b4c1", "engines": {"node": ">= 0.8"}, "gitHead": "44abd9a37b89700469b6ecf550d81f34e5cdde99", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "1.2.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.6.5": {"name": "body-parser", "version": "1.6.5", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "body-parser@1.6.5", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "536f01e08ee2b6df6a941d6c8c9647ee99ee4de7", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.5.tgz", "integrity": "sha512-hNuwcxA9J9d5Go6QEg8Go8eiJd3r6SeN5+TogxvmI5xWg5GrSWmLUGWq41WhPYNeF7HzzVd+C9IL29wpRXSrRg==", "signatures": [{"sig": "MEUCIEtJNwQGDEL2FbPWEg1HcsgOYYM6Bn7ohfL9QE1kWLQYAiEAhjNDrs/BlEV7AQcXRNvnsyR5bKy4V+LDdBOu+QgZi8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "536f01e08ee2b6df6a941d6c8c9647ee99ee4de7", "engines": {"node": ">= 0.8"}, "gitHead": "0d42013b30a6784a7e86dd387a2aa5d17b5b01cb", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "1.2.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.6.6": {"name": "body-parser", "version": "1.6.6", "license": "MIT", "_id": "body-parser@1.6.6", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "abfead725f1983631ce94b8e3e9a297d1ab703fb", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.6.tgz", "integrity": "sha512-jeON7/5++VNhI6GMPOtEuJwlXoP5aLS8XNRj3YURQqDXRCq2Pq7p8hUyFFR3sWq2AhpELOKaB042XRxXgAYglw==", "signatures": [{"sig": "MEYCIQCsyKQ9GtBVK1RVr6R5oSd6IY7GtRk3GlwGnskBKBR4zAIhAPYXc9esZsokuGsn/5HwoeuSSWMrHA7qj4YC9/oVmU3u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "abfead725f1983631ce94b8e3e9a297d1ab703fb", "engines": {"node": ">= 0.8"}, "gitHead": "a9cea305c30ca08e45492a9627ae9849ff28e6f2", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.0", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.6.7": {"name": "body-parser", "version": "1.6.7", "license": "MIT", "_id": "body-parser@1.6.7", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "82306becadf44543e826b3907eae93f0237c4e5c", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.7.tgz", "integrity": "sha512-J6HSnbgUPZMhDuqsTyGdpEy6PavO/05c7bKpqXVbYnLUBdH9oM1DvMTC27I3x9F0/tjfvbpuyedP/uUinj+Veg==", "signatures": [{"sig": "MEUCIBfi/eylNrltKrRb6oT7ODRIMMuWse1NGo2s14Fs6hJ1AiEA8XgGfwcg4qBcsBWgKvLRQbkIrZNqY3FKWx0mU6BKZvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "82306becadf44543e826b3907eae93f0237c4e5c", "engines": {"node": ">= 0.8"}, "gitHead": "30a08ab015555171985e7a047ddfc21178f02e30", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.7.0": {"name": "body-parser", "version": "1.7.0", "license": "MIT", "_id": "body-parser@1.7.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "6a245ea5b32d8e1e0d43bec8344b264ba4b36541", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.7.0.tgz", "integrity": "sha512-SLLySe3OQZk4BVED3cPCTrr9n1xlCpOo6u79RumwySzprOtQ1Y1V6tAoBg/PJOsUYKedRPK8gdAAXq1hsctqMA==", "signatures": [{"sig": "MEUCIQCulx9tNEbmjk2StW0OUjliFPF7LydMs8VaBjxaXmWAGwIgC6J4hEwt/14286PLNfPbIdsJw3PeK0XJ1/3Y3PYM4Xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "6a245ea5b32d8e1e0d43bec8344b264ba4b36541", "engines": {"node": ">= 0.8"}, "gitHead": "9e3906d7fd3ac0d0d01d828774051ae28a64f17a", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.8.0": {"name": "body-parser", "version": "1.8.0", "license": "MIT", "_id": "body-parser@1.8.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "20b3a3d3553a6835d7373456dd9da8720759b306", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.0.tgz", "integrity": "sha512-h2luv+SU8Xm34vKqxoJ7QEDaZStDrzQ7eizLOJbc91IF2SRcOAhPB63FRLbF9J06H7lzPQIZ+cqBOpA81Rn4uA==", "signatures": [{"sig": "MEUCIQCbOmOrj2WAyMQuzERn4OMrak9jwI9DJZcAi/xRC+XJFAIgY/0OAlipqoLBsAjZ82iPTzlK4Z6GtTw6oGeOYeDYOH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "20b3a3d3553a6835d7373456dd9da8720759b306", "engines": {"node": ">= 0.8"}, "gitHead": "17d73ae0ec6fc1f21f932849fa7103f37c67c718", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.3", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.5.0", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.8.1": {"name": "body-parser", "version": "1.8.1", "license": "MIT", "_id": "body-parser@1.8.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "f9f96d221c435c95d18aeaad2bcdea1371902aad", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.1.tgz", "integrity": "sha512-A6jnnZafw0xLopdb8B9VXEoOZsD9hTf5TPHmE5HLWQs5HKPPHLu8aLsVN0KovaO7jbCAlC3D5+5HWVDp2rwLvQ==", "signatures": [{"sig": "MEUCIQDb3H3HW1pwLc8mGmti1o4YITYVJuk08GYwfrIotiK71gIgFOKLen337ATO2lwst3yjsR+ewLdfbM5cceosI7MFezY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "f9f96d221c435c95d18aeaad2bcdea1371902aad", "engines": {"node": ">= 0.8"}, "gitHead": "df508da4f4c37ae6553638f95333b0ac1e8365cf", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.3", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.8.2": {"name": "body-parser", "version": "1.8.2", "license": "MIT", "_id": "body-parser@1.8.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "cb55519e748f2ac89bd3c8e34cb759d391c4d67d", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.2.tgz", "integrity": "sha512-AcQvhb15p8TtusgucXzASXBAdWfB/eqZxZm5aytKhu8C93xHBfH4J/2nQNEElPnMFu8+lxzOIaxCYI+beFCfZw==", "signatures": [{"sig": "MEQCIHboNc/kehfCD6nGXzuy/+0Kt6EHik/ER+n6H7qRpqRFAiBzejwiZfITYIcWxiyDE+uJ8UYDfdANFOdI8d452Tm+Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "cb55519e748f2ac89bd3c8e34cb759d391c4d67d", "engines": {"node": ">= 0.8"}, "gitHead": "caf6e06cf7b4e3d31717e75e31dc2efc873a1047", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.3", "depd": "0.4.5", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.8.3": {"name": "body-parser", "version": "1.8.3", "license": "MIT", "_id": "body-parser@1.8.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "922b82e6448d654f2f5197574ceacefc04a6a8af", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.3.tgz", "integrity": "sha512-uYU8Yg23GXkwrZ8im2ZmnwooBtqm1iyO8H0eRW/0Mp+2GyG7PJFIdlhFrxaNPfbMQEKwaQPIz53HIU4iCnmGeg==", "signatures": [{"sig": "MEQCIGkBf1AJIAj2aTy6ijiGN/5iOmCagRdAr+YvRFQfBoONAiATJt+8rW/4Q6nO2bPw+n18d1PdB+y0zC/n3ZEKosxuNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "922b82e6448d654f2f5197574ceacefc04a6a8af", "engines": {"node": ">= 0.8"}, "gitHead": "b4131a69a898ec4238679bc8bad7aa5359a7ecc7", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.8.4": {"name": "body-parser", "version": "1.8.4", "license": "MIT", "_id": "body-parser@1.8.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "d497e04bc13b3f9a8bd8c70bb0cdc16f2e028898", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.4.tgz", "integrity": "sha512-jTeWaZdC6r5o7FUSWNTPtxeudzg3cybUEgT56clWiW3FOpZ0fbQAUoD2k/BqmQlyEI2sK3TBqs9Zp6p6Fsv/sQ==", "signatures": [{"sig": "MEYCIQCvsA/vsScaVMHX/dCvGehP2ZRmmgTsBSbcWiJVQnrTMAIhAIXPTihs+6IsOkzxJzhpgetDRcvlYlwGmdMfiw1gQDfu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "d497e04bc13b3f9a8bd8c70bb0cdc16f2e028898", "engines": {"node": ">= 0.8"}, "gitHead": "e12078f4b7cf2cf3925304b16c6fd66522f72c40", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.9.0": {"name": "body-parser", "version": "1.9.0", "license": "MIT", "_id": "body-parser@1.9.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "95d72943b1a4f67f56bbac9e0dcc837b68703605", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.9.0.tgz", "integrity": "sha512-fCQmijfF8stcsUxUU0r8mDo6yXPggOBDFVR0WAF85DxFwpdmtFA36oEqz6XOHKhfUmf4dIvfHXkyR7azyN/fVA==", "signatures": [{"sig": "MEUCIApjEYnJOkEyJMZQpA4sHzGknSeCo6vXOsEjnJj3/4ZUAiEA4NH16wNr+uhu9CDacOkFfAhg6ymPxgLKDqu2Tzj5Xs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "95d72943b1a4f67f56bbac9e0dcc837b68703605", "engines": {"node": ">= 0.8"}, "gitHead": "263f602e6ae34add6332c1eb4caa808893b0b711", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.2.4", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.9.1": {"name": "body-parser", "version": "1.9.1", "license": "MIT", "_id": "body-parser@1.9.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "650a3047591fa9bb3cec191cb53933a468aa57aa", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.9.1.tgz", "integrity": "sha512-0gKh9KCeBRvtDczZnlQmivCO4+xh0Ji+jVN26/aODd5nMI3OZjpdbwK+LNWh9fS66RrQAkTrky+SB6vLRM3caw==", "signatures": [{"sig": "MEUCIQCocis9QTiBuFwWnTncmWpNdfRI97I4QDrDp36fBL38OwIgTmjXOayt5q2ftRAijb27XTqK0SB3O5aoL4hPuDRPNFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "650a3047591fa9bb3cec191cb53933a468aa57aa", "engines": {"node": ">= 0.8"}, "gitHead": "ebabe092af34b6995d43662654d9de1f2bf2ab86", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.0", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.9.2": {"name": "body-parser", "version": "1.9.2", "license": "MIT", "_id": "body-parser@1.9.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "07f52cf104939118bedcba689002017271ef3c0e", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.9.2.tgz", "integrity": "sha512-TuLvgBQBqGryJoo7Wuun5Oqg0ytLn+IcgXJOMiLvTNPsCaSzTDgYIB6oVxYXV6Qq3JweltuIh3C1WAW2uxgBiA==", "signatures": [{"sig": "MEQCICrnYF6ay9/O9DSbPlMZeAODQfKiJ4b/LS8ePMsH3SyNAiB/7ErZJR6HOTZqzkq3Qn+XGQDo9PBJ5bZY8yUiS8Fqzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "07f52cf104939118bedcba689002017271ef3c0e", "engines": {"node": ">= 0.8"}, "gitHead": "efb1e7d1749a743515ca0f191ee214e8c2902bac", "scripts": {"test": "mocha --require should --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.2", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.9.3": {"name": "body-parser", "version": "1.9.3", "license": "MIT", "_id": "body-parser@1.9.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "edfacd4fcfad87dfe74f861a5cc712900aef2623", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.9.3.tgz", "integrity": "sha512-nVSZlzCeMgePYRfXhLbmkzP9NDTbLwNnMtSD82hx97swlLWZeD7Aw30ffhyET2sEGn6+mn0uWcUHiBOcVF1VOQ==", "signatures": [{"sig": "MEYCIQCw1rl9KBpd7BT0aw9Jxk/+0LdMXYC9Ve1QfdIUWVmJAwIhAOeSbQbk7Zb9w7IrH9Lhc4dh5p02P7XVsKufOBEGQJAE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "edfacd4fcfad87dfe74f861a5cc712900aef2623", "engines": {"node": ">= 0.8"}, "gitHead": "810c089057c004eeb1f54d638bdb8a15acc09d06", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.3", "raw-body": "1.3.1", "iconv-lite": "0.4.5", "media-typer": "0.3.0", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "istanbul": "0.3.2", "supertest": "~0.15.0"}}, "1.10.0": {"name": "body-parser", "version": "1.10.0", "license": "MIT", "_id": "body-parser@1.10.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "f884d11839af09e3c61e5011059e29cbfe452085", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.10.0.tgz", "integrity": "sha512-VEoXZduApGvhp4tSaYbLTb8BpFM/uhAS2aTclS5U8O2Usu0LiCMwq7z1axhc47KsvCcWGxijAfbKfAXjJZRxPw==", "signatures": [{"sig": "MEUCIFWDpgsVr2kL/gkprOkny+mLIwAcpcZVI3Y9Fl6Dz5NFAiEAr1H7MT5FH5/MwBH6bBAzLq2Moii7PknCErcPPyApBz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "f884d11839af09e3c61e5011059e29cbfe452085", "engines": {"node": ">= 0.8"}, "gitHead": "bdee22aed4f516580c791b1fb1112f6cbc6bcffb", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.3", "raw-body": "1.3.1", "iconv-lite": "0.4.5", "media-typer": "0.3.0", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "methods": "~1.1.0", "istanbul": "0.3.2", "supertest": "~0.15.0"}}, "1.10.1": {"name": "body-parser", "version": "1.10.1", "license": "MIT", "_id": "body-parser@1.10.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "af0c7156b128d946f3c43f5fe0364da00cfa7391", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.10.1.tgz", "integrity": "sha512-0HS6HIsf/8hYcKGj0WWrDs8lmZqRKpACercXflJ6iRHiwLB0S/i3DfqXCnqTeFJlBfRUOkq5CN9/+EnGuuHZjQ==", "signatures": [{"sig": "MEUCIEsBQOe/Bl/rgdH3IRw6vh/u5TtPDt6GCDquS8E8GGbRAiEA92/nWVgqG7rYZpDhDgXdpSDYLN/SNqHYeFAAWLibWpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "af0c7156b128d946f3c43f5fe0364da00cfa7391", "engines": {"node": ">= 0.8"}, "gitHead": "2dae9e45447108c7280538878c3f59c656f30bd9", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.5", "raw-body": "1.3.1", "iconv-lite": "0.4.5", "media-typer": "0.3.0", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "methods": "~1.1.1", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.10.2": {"name": "body-parser", "version": "1.10.2", "license": "MIT", "_id": "body-parser@1.10.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "405d465fcd3ccf0ea8a35adbf1055f6e98316bd1", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.10.2.tgz", "integrity": "sha512-jvUDKORaxvvm3A4QlCxnVVACN/xamCrif3T6Bk6f+41sWmYokg003LMCLJXgeDH63sl0WjHAfGeTEOBbzy4vqA==", "signatures": [{"sig": "MEUCIQC9oJ8m3CNqwVrstr6z8CHVwCtRJN4JRwqWvBdUoZQPHAIgVhhpkZnh3YOvXMSTioJTbymw4toqDebtkG3j7VHemac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "405d465fcd3ccf0ea8a35adbf1055f6e98316bd1", "engines": {"node": ">= 0.8"}, "gitHead": "1fbb94d61e3435865db6092e7f6685436aecb858", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.5", "raw-body": "1.3.2", "iconv-lite": "0.4.6", "media-typer": "0.3.0", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "methods": "~1.1.1", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.11.0": {"name": "body-parser", "version": "1.11.0", "license": "MIT", "_id": "body-parser@1.11.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "29f876cb608efa54e9b2185fe8105efc9219a7f3", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.11.0.tgz", "integrity": "sha512-TD5oybtZnb48JSGTSiBRc3shYVdUJ6hsALEDLp37UMJEXOpJwOhPJ2zgkcti2fPqyzmj3UDZN7TMrQb3wJe59A==", "signatures": [{"sig": "MEYCIQCR/oOQUPGv9sb0S7PE/x1V//wZklEuVRmZK1wL3CCmkAIhAKMGPHkDEqr7pThb/yLo5KC2m2TCwT84YGm1LqWZN2WH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "29f876cb608efa54e9b2185fe8105efc9219a7f3", "engines": {"node": ">= 0.8"}, "gitHead": "bc783dd7aade9a40ba3cd1ec4c65439b8e99d66e", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.6", "raw-body": "1.3.2", "iconv-lite": "0.4.6", "media-typer": "0.3.0", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "methods": "~1.1.1", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.12.0": {"name": "body-parser", "version": "1.12.0", "license": "MIT", "_id": "body-parser@1.12.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "9750fc3cc1080b34a13d18c79840cd559979fce5", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.0.tgz", "integrity": "sha512-tbWYo1REQtmLKUxXm19eia6YxNW3paXWUzV8dAjfEnOiQjCD3Tnkpm1g7yFk/WFioHNCYEp+BWPAeP1iUFC4EQ==", "signatures": [{"sig": "MEQCIFRzjTO/MueIFs0s6hZs8h5o0aI/1IFvv7/kECK4NeP3AiBCfhDyKyZ5QzgJkbSTdbceEcX/kcp9lI6QZZf/9ChDfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "9750fc3cc1080b34a13d18c79840cd559979fce5", "engines": {"node": ">= 0.8"}, "gitHead": "9ec4d920fc0fbfc8351ff528d19b24d80612e3e0", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "debug": "~2.1.1", "type-is": "~1.6.0", "raw-body": "1.3.3", "iconv-lite": "0.4.7", "on-finished": "~2.2.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.1.0", "methods": "~1.1.1", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.12.1": {"name": "body-parser", "version": "1.12.1", "license": "MIT", "_id": "body-parser@1.12.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "4b9b4c67e8eb5ccac7c9eef3fbd6694e721ae002", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.1.tgz", "integrity": "sha512-T7YcXR7Dm3JkVl+/vQpZK3c/cMew02KIo0dhW7j8LH+E7hE5VQoNfb5aby4KhpOhKGsso90oCHMJ7Vl0vmQWuA==", "signatures": [{"sig": "MEQCIAfRHoug1VwrasHtKiAW6CD/o6vs4DYPB0dVz5QiNEY0AiAxqM1QuxSsVo2azzv6uH0bNG5RDaJAVH4xnSCXdAJFZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "4b9b4c67e8eb5ccac7c9eef3fbd6694e721ae002", "engines": {"node": ">= 0.8"}, "gitHead": "b500848a82da89c0810859c2b86a4bd33a7a9983", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "debug": "~2.1.3", "type-is": "~1.6.1", "raw-body": "1.3.3", "iconv-lite": "0.4.7", "on-finished": "~2.2.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.2.1", "methods": "~1.1.1", "istanbul": "0.3.8", "supertest": "~0.15.0"}}, "1.12.2": {"name": "body-parser", "version": "1.12.2", "license": "MIT", "_id": "body-parser@1.12.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "698368fb4dfc57a05bff1ddb1bebeba3bd2c0e87", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.2.tgz", "integrity": "sha512-uLyYuZM0WqAk43P09+i8Zh5IUB8PCUBlEWMMXekWMXUJs6TYuRDy5/mA95L/AAt3/6jRejU5mOTRnlOT/n9k6g==", "signatures": [{"sig": "MEQCIEUOKKcBt7BHvT/zWVm2JVCDe4/kE/zB6FgTnj3AaRitAiAiF+Dv9uJef9mkqh+nc8mxQdrXOEkVp5qShoVQaVF30g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "698368fb4dfc57a05bff1ddb1bebeba3bd2c0e87", "engines": {"node": ">= 0.8"}, "gitHead": "ec92683bbb469f63da8b584c37e7708ed76b09e2", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.4.1", "depd": "~1.0.0", "bytes": "1.0.0", "debug": "~2.1.3", "type-is": "~1.6.1", "raw-body": "1.3.3", "iconv-lite": "0.4.7", "on-finished": "~2.2.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.2.1", "methods": "~1.1.1", "istanbul": "0.3.8", "supertest": "~0.15.0"}}, "1.12.3": {"name": "body-parser", "version": "1.12.3", "license": "MIT", "_id": "body-parser@1.12.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "5f40bf17e7823be6895d4d35582752e36cf97f71", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.3.tgz", "integrity": "sha512-OsEhm5Qi9bLox2dRfYiKZsqwOTzVQYxFlkA2f139F670i6Nv23DV3yHiG+8K+RRqQqDuJfPHv2yH12Lv2Yh7EA==", "signatures": [{"sig": "MEUCIQDEmaGfsk3DiM35rM/GcD2cjprHi/g+OebeUC0MnX3w7QIgIW+oKmKGxjSPMK65CEWgxnx+N/zqbVn389FkqOj1xc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "5f40bf17e7823be6895d4d35582752e36cf97f71", "engines": {"node": ">= 0.8"}, "gitHead": "5addd8e18e0a72795f9ab93e867d3e50f3429910", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.4.1", "depd": "~1.0.1", "bytes": "1.0.0", "debug": "~2.1.3", "type-is": "~1.6.1", "raw-body": "1.3.4", "iconv-lite": "0.4.8", "on-finished": "~2.2.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.2.4", "methods": "~1.1.1", "istanbul": "0.3.9", "supertest": "~0.15.0"}}, "1.12.4": {"name": "body-parser", "version": "1.12.4", "license": "MIT", "_id": "body-parser@1.12.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "090700c4ba28862a8520ef378395fdee5f61c229", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.4.tgz", "integrity": "sha512-fueabp0EDZKvebbSI94mGzVlJr3vViXA7q+W+52MFZCrcJjRlnTkPQjpua8+6M6WOh1swnw+DJiUrETWRIQn9g==", "signatures": [{"sig": "MEYCIQCB0oh/G4thwZ/0DISn0SKNLO/EGCre+lx6GdtHFkXo3gIhAP3hX1/s2dBMdtmjLiqzb49DltBthAIzIBste1PgA9St", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "090700c4ba28862a8520ef378395fdee5f61c229", "engines": {"node": ">= 0.8"}, "gitHead": "faba6ae19686d82133e188707b9b77649f45d3b0", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.4.2", "depd": "~1.0.1", "bytes": "1.0.0", "debug": "~2.2.0", "type-is": "~1.6.2", "raw-body": "~2.0.1", "iconv-lite": "0.4.8", "on-finished": "~2.2.1", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.2.4", "methods": "~1.1.1", "istanbul": "0.3.9", "supertest": "~0.15.0"}}, "1.13.0": {"name": "body-parser", "version": "1.13.0", "license": "MIT", "_id": "body-parser@1.13.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "b6dca73da8c4a9f68b0e64d29acac39dd3ad9a9e", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.0.tgz", "integrity": "sha512-siOxX30eNn7A1g85gsWNj08LAwrElDxlVtMOidHqFi+JCgjrKCJC+SJsLd8n/vDWWg6DUve0eF454gBHs/1p7w==", "signatures": [{"sig": "MEUCIQCwU9EelJvRtTKl2jqZwNBD7/9d7xXx6+MSzpTivknxAgIgVya9+Og0hW3sBLIGnZcEodC+RQRF6ubrogtsFwk7eY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "b6dca73da8c4a9f68b0e64d29acac39dd3ad9a9e", "engines": {"node": ">= 0.8"}, "gitHead": "aa8617b3893300ad52cb19d279ef62ccc99c1394", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "3.1.0", "depd": "~1.0.1", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.3", "raw-body": "~2.1.1", "iconv-lite": "0.4.10", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.9", "supertest": "1.0.1"}}, "1.13.1": {"name": "body-parser", "version": "1.13.1", "license": "MIT", "_id": "body-parser@1.13.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "f07218bc2c4b5e36ca261557c9465481b29ecdcd", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.1.tgz", "integrity": "sha512-Fi98hAzHXZuEzK3zI3dJ87Huk1QCA6ynPFSAzEQ1YYK2N1mEtpOEW+PITWd+gOz5ycLgyBVYSMEOOrZy2YFpaQ==", "signatures": [{"sig": "MEUCIQCHe4SqtMb2ZB5TYB9oiEBOSIEc8m+iqwuzCWkjbsZrdQIgZKAtvGASisykorBQWIncwivP/sGUd4uXKqKuuOJdZZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "f07218bc2c4b5e36ca261557c9465481b29ecdcd", "engines": {"node": ">= 0.8"}, "gitHead": "bf6c1465fe9e36e04668d8129c0fbb8a9b375060", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "2.4.2", "depd": "~1.0.1", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.3", "raw-body": "~2.1.1", "iconv-lite": "0.4.10", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.9", "supertest": "1.0.1"}}, "1.13.2": {"name": "body-parser", "version": "1.13.2", "license": "MIT", "_id": "body-parser@1.13.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "229262a4fd2e402dfb88d99bc27d8be31307e7e9", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.2.tgz", "integrity": "sha512-Jt0zENT15Ba22HOvi8oxBV5eAXgit7pQQxy133Bz+RlWy9ZeKXrY+HOpdHebP1B3aOgxRd4W4LklrihWdgnLig==", "signatures": [{"sig": "MEUCIHygN7KjivEaOMts2XmRmbFC/fAEMkEHJQLbj3wvS2EnAiEAw1EDQ/do8V34cWG01ZJ6PQcRlOQ5Be5t6YaJbWJPfdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "229262a4fd2e402dfb88d99bc27d8be31307e7e9", "engines": {"node": ">= 0.8"}, "gitHead": "b31df3e7550c6fadef6823a020f527ab73bfec33", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "4.0.0", "depd": "~1.0.1", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.4", "raw-body": "~2.1.2", "iconv-lite": "0.4.11", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.17", "supertest": "1.0.1"}}, "1.13.3": {"name": "body-parser", "version": "1.13.3", "license": "MIT", "_id": "body-parser@1.13.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "c08cf330c3358e151016a05746f13f029c97fa97", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.3.tgz", "integrity": "sha512-ypX8/9uws2W+CjPp3QMmz1qklzlhRBknQve22Y+WFecHql+qDFfG+VVNX7sooA4Q3+2fdq4ZZj6Xr07gA90RZg==", "signatures": [{"sig": "MEUCIGbUUwkeTZufW1aAocpCofR5g/pEOiocyPiYjuO18MODAiEAuzcphsP8GLSxz2KLORYQDEOc15CVheqr1y3dvQPjAFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "c08cf330c3358e151016a05746f13f029c97fa97", "engines": {"node": ">= 0.8"}, "gitHead": "79d0972bd18247071326105bfb36539830b61b76", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "4.0.0", "depd": "~1.0.1", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.6", "raw-body": "~2.1.2", "iconv-lite": "0.4.11", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.17", "supertest": "1.0.1"}}, "1.14.0": {"name": "body-parser", "version": "1.14.0", "license": "MIT", "_id": "body-parser@1.14.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "a7a10138547a75bfcacc20472404630c2fa6b0ff", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.14.0.tgz", "integrity": "sha512-gduSv8yKw/TWfNE0npoXyFU0J0Lk8oYQAEW9gDI8z6AfbrWsu8kIvvZJz/uwPhsXggiwrggjqObbKSIhmfXc3g==", "signatures": [{"sig": "MEUCIQCNlgMs588ZNajgORubxb12OYwAjj6D/znQIfzmA8ay9AIgSbmUqoHa6zfdfQoKS8YPJlFNE7PD9d8QKiM3dAwbcKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "a7a10138547a75bfcacc20472404630c2fa6b0ff", "engines": {"node": ">= 0.8"}, "gitHead": "a438bed510877e36724b1716bd6f55a15a1155d2", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "5.1.0", "depd": "~1.1.0", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.8", "raw-body": "~2.1.3", "iconv-lite": "0.4.11", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.20", "supertest": "1.1.0"}}, "1.14.1": {"name": "body-parser", "version": "1.14.1", "license": "MIT", "_id": "body-parser@1.14.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "ffe921eba3ce8f191e2a8a8803844bd025f3c6dc", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.14.1.tgz", "integrity": "sha512-5zDL2GGbhBN1zyJZVMp/tysRNk+tT2xnsKJMhzHWwn3EWA8ASdG5+eNR7nJw3aicgD4rdY3Q9er0j9c7is8Z+w==", "signatures": [{"sig": "MEYCIQCnZiU/woqZsgsW5AFJWXA3bT98xf+OxqT4LrjL2rBHVwIhANEMNN8OXs4sA+fvVTr4qCH21Lyj5tG7op3d3IHs9ez9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "ffe921eba3ce8f191e2a8a8803844bd025f3c6dc", "engines": {"node": ">= 0.8"}, "gitHead": "7847af6e5a36129eea0e0becfbcc521b839313ae", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "5.1.0", "depd": "~1.1.0", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.9", "raw-body": "~2.1.4", "iconv-lite": "0.4.12", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.21", "supertest": "1.1.0"}}, "1.14.2": {"name": "body-parser", "version": "1.14.2", "license": "MIT", "_id": "body-parser@1.14.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "1015cb1fe2c443858259581db53332f8d0cf50f9", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.14.2.tgz", "integrity": "sha512-6D9uiWn7dbnDAhlDikccybuqKCmsoest0es3VSQO8Doz/fzx6Ls7kJNxKBYTjbzu4/RzNsf9zuACnS3UYjVH8Q==", "signatures": [{"sig": "MEUCIEZkm3WRXTuhH3xWw/j4d3jzBraiwI7Uf6QFihW0HMwNAiEAkTSgaSaqTO6yG2404Lda6JBQSX4cNe/Ycc+dZMndwpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "1015cb1fe2c443858259581db53332f8d0cf50f9", "engines": {"node": ">= 0.8"}, "gitHead": "ef5d85d8344f08b21f70a7d90082e7eea3ccdf99", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "5.2.0", "depd": "~1.1.0", "bytes": "2.2.0", "debug": "~2.2.0", "type-is": "~1.6.10", "raw-body": "~2.1.5", "iconv-lite": "0.4.13", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.3.4", "methods": "~1.1.1", "istanbul": "0.4.1", "supertest": "1.1.0"}}, "1.15.0": {"name": "body-parser", "version": "1.15.0", "license": "MIT", "_id": "body-parser@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "8168abaeaf9e77e300f7b3aef4df4b46e9b21b35", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.15.0.tgz", "integrity": "sha512-kUw7+wBWR57kY1buA9lHZFKSjGX+6FQ2kZm/jl3vSMBsUUlBewvexq9nSu5sVb+QOM19bVPW10Lt0mfR1GgstQ==", "signatures": [{"sig": "MEUCIBqmCnmH2y8t6FG4pqlmorRiNbAhBxhZkuVS3BlT7kbEAiEA1/LDjyv6zWjoRhfL4MRmijLMia7n/4VTebIGdCCEAeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "8168abaeaf9e77e300f7b3aef4df4b46e9b21b35", "engines": {"node": ">= 0.8"}, "gitHead": "5b4fabe344e5b3df9e9157c7e9b9e6f5706b1cec", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/body-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Node.js body parsing middleware", "directories": {}, "dependencies": {"qs": "6.1.0", "depd": "~1.1.0", "bytes": "2.2.0", "debug": "~2.2.0", "type-is": "~1.6.11", "raw-body": "~2.1.5", "iconv-lite": "0.4.13", "http-errors": "~1.4.0", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.4.5", "methods": "1.1.2", "istanbul": "0.4.2", "supertest": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.15.0.tgz_1455156407766_0.14806043729186058", "host": "packages-9-west.internal.npmjs.com"}}, "1.15.1": {"name": "body-parser", "version": "1.15.1", "license": "MIT", "_id": "body-parser@1.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "9bceef0669b8f8b943f0ad8ce5d95716bd740fd2", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.15.1.tgz", "integrity": "sha512-pufcv4+P49d+t01Slfbkhm9td5DWqh3VV+hooMhcu3k4t38R7UFW6HIW3WTOPHiM16CDTXulfcPZ1GWSgTw/Ig==", "signatures": [{"sig": "MEUCIH3QjEzk77JD8DzMp0zK6AOtDVwOOnXyjdObEpbe7uU6AiEAj/Ci/1uqnoDj45et878gmhkFW8wyfWhoaA++hDJFyXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "9bceef0669b8f8b943f0ad8ce5d95716bd740fd2", "engines": {"node": ">= 0.8"}, "gitHead": "e701380ab9b862bbf2223e4df4835a15e4e1ff66", "scripts": {"test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"qs": "6.1.0", "depd": "~1.1.0", "bytes": "2.3.0", "debug": "~2.2.0", "type-is": "~1.6.12", "raw-body": "~2.1.6", "iconv-lite": "0.4.13", "http-errors": "~1.4.0", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.4.5", "methods": "1.1.2", "istanbul": "0.4.3", "supertest": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.15.1.tgz_1462512908287_0.2557021768298", "host": "packages-16-east.internal.npmjs.com"}}, "1.15.2": {"name": "body-parser", "version": "1.15.2", "license": "MIT", "_id": "body-parser@1.15.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "d7578cf4f1d11d5f6ea804cef35dc7a7ff6dae67", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.15.2.tgz", "integrity": "sha512-w+PHzLDPwR2csqaHDt/oKTD5XVieOWaa1OV0VcX+agasefEZI3jK6iBabpOcM3ovd3YvIg+sK9sX437wvYjUSw==", "signatures": [{"sig": "MEYCIQDtXKE52XxUz3N4qOQvfA4vBf/Vo0CkqPzNEWVLR2lowQIhAJqGJVxfnhoWf6Lf4wL4YfTcU//1bOr4wcHG3ubQRmHt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "d7578cf4f1d11d5f6ea804cef35dc7a7ff6dae67", "engines": {"node": ">= 0.8"}, "gitHead": "3c8218446d919a5e87fa696971fb7f69b10afc1c", "scripts": {"lint": "eslint **/*.js", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"qs": "6.2.0", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "~2.2.0", "type-is": "~1.6.13", "raw-body": "~2.1.7", "iconv-lite": "0.4.13", "http-errors": "~1.5.0", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.13.0", "methods": "1.1.2", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.15.2.tgz_1466393694089_0.7908455491997302", "host": "packages-12-west.internal.npmjs.com"}}, "1.16.0": {"name": "body-parser", "version": "1.16.0", "license": "MIT", "_id": "body-parser@1.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "924a5e472c6229fb9d69b85a20d5f2532dec788b", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.16.0.tgz", "integrity": "sha512-zFgl6vVxlI5afDTqrzEbxKTVX5lOL0/M0VeNHK4Nka7cYwmJfwjAW1GqoDqhlXyVE+aZEG5aGfEZDWhW/v+6Ow==", "signatures": [{"sig": "MEUCIQCbX08OISdwLzIy90zvz4TBQSiJnUO3JQaHx2NA67OFMgIgbV6DyFeR9tvF5PF8pj0UmU6RXXoVu8pxujOQdV2DZ8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "924a5e472c6229fb9d69b85a20d5f2532dec788b", "engines": {"node": ">= 0.8"}, "gitHead": "c5a73d51483310f8443043d3927c2557993f3416", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"qs": "6.2.1", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.0", "type-is": "~1.6.14", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.5.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.13.1", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.0.tgz_1484710891328_0.08588228072039783", "host": "packages-12-west.internal.npmjs.com"}}, "1.16.1": {"name": "body-parser", "version": "1.16.1", "license": "MIT", "_id": "body-parser@1.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "51540d045adfa7a0c6995a014bb6b1ed9b802329", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.16.1.tgz", "integrity": "sha512-7MTv2fVPg107awTG4ww4GDF5RrU/Z+546C8jsVJ0N8+KJu0SqABmjfRsh4e3nWryxZGF+yOZhGMMYDa/OAoGWg==", "signatures": [{"sig": "MEQCIH1eHUdHN6VlZkNvE7DD5i4I7eOR1oNB55Jo6NE9X5JsAiBLovLFkoaRRe9hUrVXCse2LRsmtk8iY2dzzpl1DOFXfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "51540d045adfa7a0c6995a014bb6b1ed9b802329", "engines": {"node": ">= 0.8"}, "gitHead": "7b630f701d084267a8b9883b27f627014e003d47", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"qs": "6.2.1", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.1", "type-is": "~1.6.14", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.5.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.15.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.1.tgz_1486777002177_0.4995518890209496", "host": "packages-18-east.internal.npmjs.com"}}, "1.17.0": {"name": "body-parser", "version": "1.17.0", "license": "MIT", "_id": "body-parser@1.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "d956ae2d756ae10bb784187725ea5a249430febd", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.17.0.tgz", "integrity": "sha512-64UNLtNOgTZ7ybVuGo7B3msRPTkXiGqn2t2k9KrqisBVhnexT9UarrgcerIo0qeKWHU7sZWflRubuCS2YXdmGA==", "signatures": [{"sig": "MEYCIQC9jImYdpZnYDBX2z4bBjD8EmdU+ZwXqqyPkql6Mt0YUAIhAOwbKC7r9e3QkOmFJrmc7hBmtT/JaZy73zWxN1nTgP8y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "d956ae2d756ae10bb784187725ea5a249430febd", "engines": {"node": ">= 0.8"}, "gitHead": "79bc93911501b0d048dea39a13ab7384b2cb43f1", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"qs": "6.3.1", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.1", "type-is": "~1.6.14", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.16.1", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.17.0.tgz_1488406215099_0.9978320009540766", "host": "packages-12-west.internal.npmjs.com"}}, "1.17.1": {"name": "body-parser", "version": "1.17.1", "license": "MIT", "_id": "body-parser@1.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "75b3bc98ddd6e7e0d8ffe750dfaca5c66993fa47", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.17.1.tgz", "integrity": "sha512-yMi6cDvaQLniK4bQATEZWYVn52TqcFt5fr8mP5xMI+1S5Kk6ouSF90ncoorYXCjsPLN9ilJ/rAbmpNKfj93gkQ==", "signatures": [{"sig": "MEUCIELIex1+IqOrCdnPKBVzjZLKMi41n7C3iLYz0k5+ZVtjAiEAqlLnYN349hfqQ6ZdS0f+9kSX29AW6dBjX2CdggxXcg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "75b3bc98ddd6e7e0d8ffe750dfaca5c66993fa47", "engines": {"node": ">= 0.8"}, "gitHead": "0f1bed0543d34c8de07385157b8183509d1100aa", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"qs": "6.4.0", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.1", "type-is": "~1.6.14", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.17.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.0.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.17.1.tgz_1488807088817_0.47385501372627914", "host": "packages-18-east.internal.npmjs.com"}}, "1.17.2": {"name": "body-parser", "version": "1.17.2", "license": "MIT", "_id": "body-parser@1.17.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "f8892abc8f9e627d42aedafbca66bf5ab99104ee", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.17.2.tgz", "integrity": "sha512-pLoAUdkGfkp6VGbhhNWLtvF5dkWYVY7oSwZa45ZJeTxAipUB9L7n1cx0a/leni10VflrjorZM7Jb5jKxpIYniw==", "signatures": [{"sig": "MEUCIGP5JRxvhvVpnm8IyL6MFmabEBckAY3eEubp6zUL3RdRAiEAoBzpaL3rhymRrRwndzrLKhxqlfs/e326XDzds/1TU5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "f8892abc8f9e627d42aedafbca66bf5ab99104ee", "engines": {"node": ">= 0.8"}, "gitHead": "77b74312edb46b2e8d8df0c8436aaba396a721e9", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"qs": "6.4.0", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.7", "type-is": "~1.6.15", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.0.1", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.17.2.tgz_1495083464528_0.912320519099012", "host": "packages-18-east.internal.npmjs.com"}}, "1.18.0": {"name": "body-parser", "version": "1.18.0", "license": "MIT", "_id": "body-parser@1.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "d3b224d467fa2ce8d43589c0245043267c093634", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.0.tgz", "integrity": "sha512-iKF4dMoQmzJDfjIUx/uqWjUs6wUqpD/IiKR+kfTng2zDhBBXLa7gji72a50pa9EGtdTKzjNWUw3PphxtJk6ejA==", "signatures": [{"sig": "MEQCIFmBTDWJoSDrnwNucduioI8q7jFI+PxlgDW3T/RQSrlAAiBPmyc1Toa980nO+YO6EDUG6gauuueSGzus2hB50xnw+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "d3b224d467fa2ce8d43589c0245043267c093634", "engines": {"node": ">= 0.8"}, "gitHead": "adfa01c1c58102292e353fe4ee7558a4581fb539", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"qs": "6.5.0", "depd": "~1.1.1", "bytes": "3.0.0", "debug": "2.6.8", "type-is": "~1.6.15", "raw-body": "2.3.1", "iconv-lite": "0.4.18", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.18.0.tgz_1504930645505_0.6018156714271754", "host": "s3://npm-registry-packages"}}, "1.18.1": {"name": "body-parser", "version": "1.18.1", "license": "MIT", "_id": "body-parser@1.18.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "9c1629370bcfd42917f30641a2dcbe2ec50d4c26", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.1.tgz", "integrity": "sha512-KL2pZpGvy6xuZHgYUznB1Zfw4AoGMApfRanT5NafeLvglbaSM+4CCtmlyYOv66oYXqvKL1xpaFb94V/AZVUnYg==", "signatures": [{"sig": "MEYCIQC8s9lNNafjwMPSKrGcVFHtFB5yS4gusrPS3Ajt99a8RgIhALIobQtYlX12OEgEoGGtQ/EUFxmkJvJyo+ccvpma6FIF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8"}, "gitHead": "d041563376670707cc693968995ff731adefe4cf", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "6.11.3", "dependencies": {"qs": "6.5.1", "depd": "~1.1.1", "bytes": "3.0.0", "debug": "2.6.8", "type-is": "~1.6.15", "raw-body": "2.3.2", "iconv-lite": "0.4.19", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.18.1.tgz_1505230250261_0.44409058685414493", "host": "s3://npm-registry-packages"}}, "1.18.2": {"name": "body-parser", "version": "1.18.2", "license": "MIT", "_id": "body-parser@1.18.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "87678a19d84b47d859b83199bd59bce222b10454", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.2.tgz", "integrity": "sha512-XIXhPptoLGNcvFyyOzjNXCjDYIbYj4iuXO0VU9lM0f3kYdG0ar5yg7C+pIc3OyoTlZXDu5ObpLTmS2Cgp89oDg==", "signatures": [{"sig": "MEUCIF86iMTOB2wffH3rYvvRoaVpaDhE4TFkMEF2Rj9i251UAiEAuDjo6cy7FDPm7ihWa+ARsEXelPvAkeSGojtpt7rTYRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "87678a19d84b47d859b83199bd59bce222b10454", "engines": {"node": ">= 0.8"}, "gitHead": "b2659a7af3b413a2d1df274bef409fe6cdcf6b8f", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"qs": "6.5.1", "depd": "~1.1.1", "bytes": "3.0.0", "debug": "2.6.9", "type-is": "~1.6.15", "raw-body": "2.3.2", "iconv-lite": "0.4.19", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.18.2.tgz_1506099009907_0.5088193896226585", "host": "s3://npm-registry-packages"}}, "1.18.3": {"name": "body-parser", "version": "1.18.3", "license": "MIT", "_id": "body-parser@1.18.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "5b292198ffdd553b3a0f20ded0592b956955c8b4", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.3.tgz", "fileCount": 10, "integrity": "sha512-YQyoqQG3sO8iCmf8+hyVpgHHOv0/hCEFiS4zTGUwTA1HjAFX66wRcNQrVCeJq9pgESMRvUAOvSil5MJlmccuKQ==", "signatures": [{"sig": "MEUCIAiAWWr4AuCCgPB9fNk9hyO6ipAuWJWDsgvzRrr7o22JAiEArJYScAkCJ4N6Wf5d9+ennuxeFHhIaES5nGMOhBM9Dkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+cRxCRA9TVsSAnZWagAAr+wP/jRF3kZaSUPcjTUOxoqN\nlSEgHs4ISF5j2kqTPeArKhG5cgmvygc9gRtNdjtfLGHbQAjyZtA8tzBdsXZG\n3KuehVC9hf/eUIeQbVTjaxrgX0jevrF4igcmM4tRI17JzFvq1+oCz9aAk99e\n3SljKbwvFPopbM5F5BraVbOpIYCO140dVVBrV9gHOBb+65hme0PzfBdru/Bg\nZ1UnDi9l/lEGhCy+HFlSGw+T9ev0KcgzcslA/9vJBAFyYMXVfFFI+jY2+O4P\nJ35s40gbQ1V/idGM4IfmCg5IhwmvpJ902bpXJEokVgGKcw/mMv3TQpBKov2I\nOxAIIRLr2w/1Kl2d8+jMLcbkIoSD7wutdV5i+rsUR5XJURbsDuitYJbcRvJw\n5MS0bQ5aVI83TOr/35z8671ciWqpM+Ru+7eiMjQzgUOWt2qKuOB0Bi/xhr8J\nXGWbqLwl1PWxJXgGJqAJ7rvXNOmAexqJmyk1mPHDJnpjaFRKnCpDrUbuX4Cm\nw52bjMIW+wYC/zKnUMCJ+5n4gBw0jWzFUsoTCses91YZHMDVNb1tOo2Sjbwp\nxLKIWDuNDQC3lxfuCSxC7Qe3PayQGnOCwoP3o6GM89YFAhFOWgPvgBpvjfWm\niJlgG/dUsGIAY3Gz0i7abBGyeJJAuA+CwFpfERMcEE4HwcJhzOrG7g0prSD9\n3nNl\r\n=47oB\r\n-----END PGP SIGNATURE-----\r\n"}, "_from": ".", "files": ["lib/", "LICENSE", "HISTORY.md", "index.js"], "_shasum": "5b292198ffdd553b3a0f20ded0592b956955c8b4", "engines": {"node": ">= 0.8"}, "gitHead": "e6ccf98015fece0851c0c673fc2776c30ad79e5d", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "6.14.2", "dependencies": {"qs": "6.5.2", "depd": "~1.1.2", "bytes": "3.0.0", "debug": "2.6.9", "type-is": "~1.6.16", "raw-body": "2.3.3", "iconv-lite": "0.4.23", "http-errors": "~1.6.3", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "2.5.3", "eslint": "4.19.1", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.2", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.11.0", "eslint-plugin-promise": "3.7.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.18.3_1526318192390_0.5591283803389704", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "body-parser", "version": "1.19.0", "license": "MIT", "_id": "body-parser@1.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "96b2709e57c9c4e09a6fd66a8fd979844f69f08a", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.19.0.tgz", "fileCount": 10, "integrity": "sha512-dhEPs72UPbDnAQJ9ZKMNTP6ptJaionhP5cBb541nXPlW60Jepo9RV/a4fX4XWW9CuFNK22krhrj1+rgzifNCsw==", "signatures": [{"sig": "MEQCIE6oHUR+zCoLXnukUI+I14kmNMZ2yQCSpnqxynyl7ClSAiAeARDI3r97Fapg8nv1+y0KxR/GIN4c95AQFKS0ka/otA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwnuMCRA9TVsSAnZWagAA2zMP/3i2Q8pQBJx4azFOeuub\n/s3F445wJrDoAKA+6zSOLFMYYasZ0iF60NoE4taDupDF1hzpC4gCYgy9ZezQ\n75kKKBC48jCQP6Urx1tj6VUPzWqG6xdQMjhZpXrkK+EF5XYtAspb8+YSxaw4\nzf4atEm+7Q3N1qwvyfi8T/KQaK7WV6wC513pXTZv8SCtetX/4jBJwA4uUqLh\nXbuO5GcsjNEDmfX91YFKbb2+TvL2kuJkxVVdjeVv+UDLAs8AL+6afVJTe2vB\nmY+9CmSN2egWYDEXgpIowRTXzvasLJ8kQQH0dhseRrnF/k8cxX61VsT0MYEB\nd7mVyXFJE2WrN/HgiVCa9XSzLNn2bp/tyoz3W8TTSCqWOaY2cgbpFBUcBqWY\nmZSkqGqBj0lAJ3qMJw9tfIKiGtLEqsBwRoHTt6yQRsPTTD0wY3WzQTzedpS7\nPKEPDqrqMhDJpjv7vHZyP0E85lSYoDAMYPQ33fYvNbiuIMU4eDxoNJWUImXJ\nTN3uRKDn9QeE8mLTeglLVIu5+4FrDQNNjK6HHcetM89H8F4FGxGl090/H07x\nqc9A2Fe2yCeM6BICsO3BIRt0eClHS6jD15tMDbx9hx4Z4Qt+IgTn0NS4Ebj7\nW1V7qu/d6ajepEVd2kCXQkvJvslxzIGDxXo6OvTN757kROAWoYZNyGefqnou\nHXEH\r\n=IvFh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "998b265db57a80ae75ea51c55f6a191e2d168a60", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require test/support/env --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require test/support/env --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"qs": "6.7.0", "depd": "~1.1.2", "bytes": "3.1.0", "debug": "2.6.9", "type-is": "~1.6.17", "raw-body": "2.4.0", "iconv-lite": "0.4.24", "http-errors": "1.7.2", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.1.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.0_1556249483843_0.8465662994525756", "host": "s3://npm-registry-packages"}}, "1.19.1": {"name": "body-parser", "version": "1.19.1", "license": "MIT", "_id": "body-parser@1.19.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "1499abbaa9274af3ecc9f6f10396c995943e31d4", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.19.1.tgz", "fileCount": 10, "integrity": "sha512-8ljfQi5eBk8EJfECMrgqNGWPEY5jWP+1IzkzkGdFFEwFQZZyaZ21UqdaHktgiMlH0xLHqIFtE/u2OYE5dOtViA==", "signatures": [{"sig": "MEYCIQDX0pv+zrP54j0jwPRztnMBkxHadKdw0GPHw1PeFkxjwwIhAJGo0yU4nrpwGKmmKol3RbYqHQGE0orPKAeKEi0jYylz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhs6/iCRA9TVsSAnZWagAAXOYP/3fnrwpITlNiTT9so6bV\nbwbT8eAGY7ugyp71iqgswH4ZyHPRMdcruxTfBeVWc6XOoRp0LIWv4H+pmlrf\n4RsR/KcPlUbdnvwQ0bANpd4jdEOhIIG1RmuZ1VsVWxiI6lxuyhQJ0aGc8aJJ\nTBEPBL89v5jQr8jVFZHqguUWY+9iJ7a8A5oGL/6vYAxWr3ayAvAg7b2CN3VO\n7w1XjtCim+W6f4yPgJdOf6T+FeZ6C4Dsvvz8ouYpXLdcVe2wMIx4eSsW1BUo\nZ2cfIir2CxnjV5dkxSUDQEZfpoB+LD2ylgOLFpCGG5qI+LFBRMVALmbnBYDo\nGUM35eQ7q9V26lVYBImWIom9N2vfjyGk7ijLYHhx78W+7nRhZi8CO54PjGIA\nqj0bEkMTqwdvHx0jJ8owJlNYpteDAys7qfdBPRkq29y0T6QJRoLj8pnu5sfq\nvqaM1OirWxcLJcwMD0Qj5B9MCWTsrY7ZHIYYmGfOge63XmZh7gHglDCP0Col\nREl+cq/E+usBoqwHeq2TJl3BCkc1U1QP3JbAWdj8ahMWpJpBiygNWWARxGvh\n7qTisdsrx3YNz5oemPv9yL9a3gpRDSbPpPbPvZte0F0pyArpWibuNXmxf9bz\nW1DjWMVpvBX54qkBPqrD4ZTjPEEns90cq0leUIjs8cBo7bG3EKWE/cbIu4wW\nanUs\r\n=nYJJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "d0a214b3beded8a9cd2dcb51d355f92c9ead81d4", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"qs": "6.9.6", "depd": "~1.1.2", "bytes": "3.1.1", "debug": "2.6.9", "type-is": "~1.6.18", "raw-body": "2.4.2", "iconv-lite": "0.4.24", "http-errors": "1.8.1", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "methods": "1.1.2", "supertest": "6.1.6", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.1_1639165922347_0.6576872442213721", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "body-parser", "version": "2.0.0-beta.1", "license": "MIT", "_id": "body-parser@2.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "d4ed97d6ed51f6040b967db0db2252a0b235a661", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.0-beta.1.tgz", "fileCount": 10, "integrity": "sha512-I1v2bt2OdYqtmk8nEFZuEf+9Opb30DphYwTPDbgg/OorSAoJOuTpWyDrZaSWQw7FdoevbBRCP2+9z/halXSWcA==", "signatures": [{"sig": "MEUCIBzjqd8yWc7WTxePUyegrQY86SNSCw/2BbgjMMsB78ZjAiEA0LXwESY4mKMNtCxk5UdwSDNaeEIRxQjyl0ay3+Ju428=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvVruCRA9TVsSAnZWagAAOc4P/jrQSQ5PDt9wMSVzN8b3\nrEXm9qu/E026If+672WnIFLjUPcFv0zc9Bk06JNEd9BPeVK9Tt+nEeCHdwrR\n+lGo2WoG3f/rEpkF0fZsux0SuRFn+mZv+v9SyLDdBlLOpvPAF4AC4o8ghsvG\nem5PWgDpQfKiRX+CN9e7rdzXnZ/7MOR0j3Rwu5n7W98huhVctmFHH0w235pe\njUQ3OJLvNgQ/+PDfdObbHDoDWEs7JSRYvUIez1wKlRIDwaT3Wju6OPKCbWuC\n2l6abH7cwFCxmt7NJQNu/uk6Mus+X60hrx8YZygvHo8fs2Bn5wR5X94T+7oN\n771Pf72pYC/cunpsc43yKiU1UFUDQeEWKQXSLX1J9hSe34WB/ABka1gFLs7v\n81ekg9+lasQXh3iBPzSTW1thwNdolQu8i/i34IpAaJwuAK2cXqJjRSBCeWZk\nzdpKnCXse1NNgr2xuxlLz0kd5In+Xj0WRAMu/svBt1V4vFLWQY67GqhfmTJw\n/FD6DN3dfNfNf7npggoLYbQiv7w1Z1BLCpyj6AWosWxqUDhrIEd1hsTMhyBF\ngPLZnnCsEvZxgUlKCEGV47KwZoPzmAJkkyMXYOVpfVN7eC56aNeTkCBDWVZP\nv6UNdkDinBnqZIN0gYnlACr9zk8lUNssjJ2jxFSA1cbxtuWP9gdXs9x3lNnd\nnU7R\r\n=b7Qq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}, "gitHead": "fbd9664f3d6f41f02a05177215b1b2263ffd94d2", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"qs": "6.9.6", "depd": "~1.1.2", "bytes": "3.1.1", "debug": "2.6.9", "type-is": "~1.6.18", "raw-body": "2.4.2", "iconv-lite": "0.4.24", "http-errors": "1.8.1", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "methods": "1.1.2", "supertest": "6.1.6", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_2.0.0-beta.1_1639799534299_0.9288060234967992", "host": "s3://npm-registry-packages"}}, "1.19.2": {"name": "body-parser", "version": "1.19.2", "license": "MIT", "_id": "body-parser@1.19.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "4714ccd9c157d44797b8b5607d72c0b89952f26e", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.19.2.tgz", "fileCount": 10, "integrity": "sha512-SAAwOxgoCKMGs9uUAUFHygfLAyaniaoun6I8mFY9pRAJL9+Kec34aU+oIjDhTycub1jozEfEwx1W1IuOYxVSFw==", "signatures": [{"sig": "MEYCIQDB/gljkDFGXD26LAQjdpTBri3yNbufb8cEQHRX7uuJSQIhAMhLAWXtuWSnnTV6INHf3qgXmPNlsmTggU+qYx039B8f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDHMdCRA9TVsSAnZWagAAiwIQAIaiKW4cSAAxYl21hS3P\nfIOZykpSmJ4VvM+XKLxQZR3t8uaLA0yga9XK/zAeHEOjJbHQpw9uQWgPnOJj\ns5+x1NPYGwZzwPcmP0/LnO1ArEXD1LdaK9u3IZKEyCmGPFvnGvqJnxandiwH\neSaj6SA20/AlwG9XBO+iStgnV8ubU0UgOgUK1wfcJNITp7KvQY+WFhyFIqzH\n3xyrQAysqI53cyUvxzpvj/QrA2kDkgt3D9UgXZS3w3phSTRMiCRtOks+rP+w\nYxJ/4KgEHy+s+Ut4MBt4Lit8VJvzyPA7fFWKS+2Jzvwq1r4hk3j9+j+m+Nla\nw6AXD41WwjF2PvyB3+PlsGvpueaIJOREI84vHTNUuwlOFhcg0yvCUAzwcMx3\nKdWTrft36l4CXO7AX1OiXLfSq0jti1ifx88dJ+LsmjnjZzOiRRod3j8wFaGN\nv7GZmxsbQybwI1Vwxv/ogZKws8NEtMxRDPwvaLJxW+PJpI5ULsmkcI2ppqQj\nHS5fcWn9+VFYr8IZzDnXKNEbwKAdRsX9HrPD7EftIOKLdX8ekeV0iNTIh8aJ\n1dDsP/wpeLuZtDPjWw5SMYYwkB22/LZ3ZzJt81qR11sdxhjEPWNxts0m1bru\n1aCZNSEBO9uR3CXfDgBXMFfm8UdTU5MrgpLEv8AgfbqwU1CX+xnK9tL7qexJ\n4OzG\r\n=d7sF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "424dadd312f519bdb320c6ee9463d6672782420c", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"qs": "6.9.7", "depd": "~1.1.2", "bytes": "3.1.2", "debug": "2.6.9", "type-is": "~1.6.18", "raw-body": "2.4.3", "iconv-lite": "0.4.24", "http-errors": "1.8.1", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "7.32.0", "methods": "1.1.2", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.2_1644983069606_0.9492122708441992", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "body-parser", "version": "1.20.0", "license": "MIT", "_id": "body-parser@1.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "3de69bd89011c11573d7bfee6a64f11b6bd27cc5", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.0.tgz", "fileCount": 11, "integrity": "sha512-DfJ+q6EPcGKZD1QWUjSpqp+Q7bDQTsQIF4zfUAtZ6qk+H/3/QRhg9CEp39ss+/T2vw0+HaidC0ecJj/DRLIaKg==", "signatures": [{"sig": "MEYCIQC1OSkOnv+96zl/Q+rmfrOAZUHqzW95lhcVtlbTkzWYvQIhAISA01nS8WBaE3hIQgonGwA9xu+0xtp1wU9mBFqRXrBP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSPKQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSrg/+NDcjgvgB1yS8/N+wfk4Ph7ddxpY2Lc8Ww5RM7VU1Kk8FmUiM\r\nZYKpAGXVhvOWZwANhF4xqTwjC3rychMkmVeUJuxFbgWIy3ittJ4ntb9wx+3x\r\n6k6H5bDyIce23M+a6wPcBnwPeHIjPHIgLJfU6AS/HDF6qXpNdzV10xy3BNYv\r\ncoaisnMketbEmBJ60LE6zAjxBLvRr54hA4wOXDqvhJ1ROUWCwEVGU4z7AczJ\r\ne4YWn3rj/elSDKbESvw3XYzAcr6Wk6pP7lCMY0xprj+OhHwiCOe9TEQQaxPm\r\nq9Se7jvpNz9T0ikWA20cnRGtRFOzshVGHAsdhqhUPRe470hfteP03GmCt0Rz\r\naK9nkj6EnkxGotTXIa+Fx/G8XT2i/rYfCF5p1Ewrw4X/X+OHlqKjTB3/JwbS\r\nnTfHlhAl8ixj5Zn8PMXHyUDswyQPFo6PNNK6+7UByAGp3Uez1Zo5hV5aPXyF\r\nYeSMPo7POCoxx4v6u79AwQYuQUmwHQWkJhM1KIumSf/l/aJU8PFVA0OL7t6p\r\nwk2/mWT9r1gc8/YPIKoYlAq44jVBaVJIEdNd7BwxKUxMqjJg1+GiR+Pwg1K6\r\nVaZpxAYDr+pSEGaxG/uu6tUzAkVuAwEW2Sg/pdJ4OSEZF4ZMuDlecEhDlfuF\r\nIGZzkDPPHS81u71NqIRPdAB5FxSGrJkZ1t4=\r\n=08QC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": "1.2.8000 || >= 1.4.16", "node": ">= 0.8"}, "gitHead": "1f6f58e1f8dc222f2b6cfc7eb3a3bf5145ff2b56", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"qs": "6.10.3", "depd": "2.0.0", "bytes": "3.1.2", "debug": "2.6.9", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "2.5.1", "iconv-lite": "0.4.24", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "methods": "1.1.2", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.20.0_1648947856464_0.31704443104628144", "host": "s3://npm-registry-packages"}}, "1.20.1": {"name": "body-parser", "version": "1.20.1", "license": "MIT", "_id": "body-parser@1.20.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "b1812a8912c195cd371a3ee5e66faa2338a5c668", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.1.tgz", "fileCount": 11, "integrity": "sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==", "signatures": [{"sig": "MEUCIG0jCuvvVGjV+GGAmGEZeQeFMhZWZnJ9qP6Ch4Tc90CZAiEAqfZBKAMguRwJF5saPpkusWoe72ZcQdMGy1er8viK27I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPt96ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpmpg//ZbJnafsaEqlDgyIUiLP0VteSrs+e3HvkXuCewU6EfD4HkNKm\r\natWspIWQ0UE8WU3jyjy8dwPwvyUZVsLvOYrEndaNNxmQjHauwk2iKWhbgS5m\r\npC1yK1UUmogVgo9wUTinUjuF4r2AFSPKMxq9o3Ms6vnu8i+Svb60TqYpDL66\r\nxorj0ivXlpUnPcfh6iecA3x02rdEM57O1eG51KYwSd4+mCfjM3UVSiqycivV\r\n9ZIZhsEKchgwH13544CV5ZbssNZqqajIepCTTPQGhWCxpA8tVTKoGTjQPaNd\r\nie+ZLnw5Hm7ddGZj5ywDMi9in9FpycAYospPJK0+pJuMEWp+Az8ROyLku92q\r\nqP/rzc9lJjSQI4g8iUDKgA9T6jU6YGPmCSccoaotBfw7RS/3BJ8vjAEsl2jP\r\n2mN/RrNcLPKBjULS4tTd6Azh6jHL1ymUhoU4dVqMuPAe6DljSQXjHYTr+KyQ\r\n+Soby9jdKSGvMKkvA33MrI0WHej06ZIz1AmMGUAnkP+GD+ozlLXDAa7Y4CsN\r\nG4a5L7pxtaMfnwOZer0mfhJgxf4i/Nqly0NMWnJoXk91guehHemPh7qPXYby\r\n/ouAila3muEJ9LkGwRZDq6uTnWG0bBAFrHA8fh6la6WJcTnLBRY9VZ9RwnuP\r\nO2k558FDW97pjISrvgM/0qyc9867O38QlD8=\r\n=ZP3f\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": "1.2.8000 || >= 1.4.16", "node": ">= 0.8"}, "gitHead": "830bdfbee021d540a742de857dcbd43f40563a02", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "18.5.0", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "bytes": "3.1.2", "debug": "2.6.9", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "2.5.1", "iconv-lite": "0.4.24", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "8.24.0", "methods": "1.1.2", "supertest": "6.3.0", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "6.0.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.20.1_1665064826520_0.2729305626398033", "host": "s3://npm-registry-packages"}}, "1.20.2": {"name": "body-parser", "version": "1.20.2", "license": "MIT", "_id": "body-parser@1.20.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "6feb0e21c4724d06de7ff38da36dad4f57a747fd", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz", "fileCount": 11, "integrity": "sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==", "signatures": [{"sig": "MEQCIHFWkBQ9SNwui362JRxszbvNRiYLO62bIMqSXg0xctAIAiB07wAQALxzWBax4N2eg8AUI8ltUmCbogDn8TBIzCpyPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9XG7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTLA/6A7GxZJclDS15GRJB0Sqaorjhc2xTnVhoHSwa7rzKzN6XU5Iw\r\nPEMoBc5AvFoF1OLT0CTOkPmAdaTxPPBV7Y1QNvYow6yY3P6hDvwvFgXsecK/\r\nX5UAYBiQFEjxJ9oWo1j1iIufBvUoM6QL4IPq1SSChqpm6KzUtUCCdRPL3GGS\r\n5cw7BLNWR4V8bnQmncs5PqxeYAZ7vJ3wICmJ1ZVnNC3rqxURZXlODblKYZaE\r\nLmwuhfYeeQwe1+v1JXfSyRg4b/n07/bu9WKXU9bQK51rCBrwMCQFCEO4nvHB\r\nB1VAJK8rZIkt3Pi7C2R5Rs/orM1wrJODcGzzGE5nGctJDw51lcCe0qNbI8MR\r\nqkYjQgKpnUUtxN3xsH4jSJJ5q8KLZvwIIxlAAffWXieBRn1zqOF4mQ7Hypvz\r\n3snLSti3XA3V6rIs27kKd8YY6rY7P5S7OXxKc4fz9LdCfpWn2Z1HSTdJyCqo\r\nYebfL2T9E9XjfVAWMEt+mbqMJypAIVTKRzeTib9PjddpBWq59bURPjVp9PKU\r\nK5vb6qwWmH3h8VFngw5elsZSCGU1HtyNiEXqhpTqRqNzvTrCHHVArg8JUaXh\r\n5ys0b44lof0W9aEH5wHwRMdM7s3+2SrC/4bxz9pnV5FVDfkeZpYG3qPLDcUR\r\n7RY9pWpZzmrAhhoNdPvQlnrmT06ZN7v3p/k=\r\n=TVwV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": "1.2.8000 || >= 1.4.16", "node": ">= 0.8"}, "gitHead": "ee91374eae1555af679550b1d2fb5697d9924109", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "bytes": "3.1.2", "debug": "2.6.9", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "2.5.2", "iconv-lite": "0.4.24", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.20.2_1677029818883_0.08641331398042396", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "body-parser", "version": "2.0.0-beta.2", "license": "MIT", "_id": "body-parser@2.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "46081eb83ab83e436c2bce8fa71464b2385d61fb", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.0-beta.2.tgz", "fileCount": 11, "integrity": "sha512-oxdqeGYQcO5ovwwkC1A89R0Mf0v3+7smTVh0chGfzDeiK37bg5bYNtXDy3Nmzn6CShoIYk5+nHTyBoSZIWwnCA==", "signatures": [{"sig": "MEUCIA2W6LBbuT0mTqzMuaiX1bmDOVgyjEruDQWrkol3MlYEAiEAzKM70h4aVJzbNlFGFSeH1gvQ3YrTsaGHuMS5348Cs7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9+IsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2ThAAn2ih/nB07tqtAJEls2qlcCG//AVt7FyYOVMHTPJIqL4CPaS7\r\n1o9F/XeRDt0VaJp4K4QwsLpnnvu7qndGI+ZavvxOuzx8Y/pz5eq4shWiMy5j\r\nUkckturosd5EwuuqN8VeGDZf4QL3YmwVn1S5x8vbqh6pNeWr5/e6I+k7yg4e\r\nU/4eXW/SW3qpdoXEMor6KyHvyjhn1xyi/bdRmEMSGdRfdJp2V4r4HzXsUnNM\r\nNgBHUJMqLZsERi/7KgU75cSBavB9gQ82pOBxUmIAORc0RiSeVdXACJgR2EIe\r\nzpTxgMi4CLI2OuLMut68zN/BPVyEY8hVasENxID13nlJzGiGqRHoDo7q3Egp\r\nd1hPbpeyBrUu9E4Oa2e2sd4ucbYsEkGKSsEINbENdOCxfjw7o+9uJCotoPmn\r\nvbfnvsmkjABZh4no8irfHsRkwZCnuq8QgfjjKfQoRs38VAjFGFsOGE76xtGy\r\nOik3mM85hblIMr0a6idIqtUrdNVgutg+in5USQD6nWwcIOAsF/rY2HicvqkE\r\nakJtTpWEgPsRLiG2NINRODFTGYswhO6ucmEWXeBMMHW02MPLoTIoZpYBmZob\r\nCEiiNzmb3fUrKbTe/O6mEA9kb7MdqQAu3ajOCJIfFlnwpDRrHK/egevo7K9/\r\nmMU5iHZfPnVDUx8e+azAtseVnH1HhGejBCw=\r\n=Ktnd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}, "gitHead": "fccaf4879e960d5e8b105759d39d4fc5ecf2f4e4", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"qs": "6.11.0", "bytes": "3.1.2", "debug": "3.1.0", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "3.0.0-beta.1", "iconv-lite": "0.5.2", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_2.0.0-beta.2_1677189675892_0.06835011315207273", "host": "s3://npm-registry-packages"}}, "1.20.3": {"name": "body-parser", "version": "1.20.3", "license": "MIT", "_id": "body-parser@1.20.3", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "1953431221c6fb5cd63c4b36d53fab0928e548c6", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz", "fileCount": 11, "integrity": "sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==", "signatures": [{"sig": "MEUCIB0vgxyEUJdgVza3GVKw7TdT/emSwJAP5yMHGliV5V3rAiEA8gku++m3YMFhAQlz5DO6MA+ATWJVJZ0qxXmdFxrr+fA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62624}, "engines": {"npm": "1.2.8000 || >= 1.4.16", "node": ">= 0.8"}, "gitHead": "17529513673e39ba79886a7ce3363320cf1c0c50", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"qs": "6.13.0", "depd": "2.0.0", "bytes": "3.1.2", "debug": "2.6.9", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "2.5.2", "iconv-lite": "0.4.24", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.20.3_1725923719279_0.44252795426637537", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "body-parser", "version": "2.0.0", "license": "MIT", "_id": "body-parser@2.0.0", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "c402c354d64e2a387b683f89d574b03cbef004d8", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.0.tgz", "fileCount": 11, "integrity": "sha512-Dp+ktxd2u4GkMLJoVvSKLiEDsRRKvbTorW0k66C1Z2aI9X1xKoOIueI4Au7zmMS2yi3ECwdXRHclk/VjvrczDA==", "signatures": [{"sig": "MEQCIDDS8q4T9Xnmdymk4gbIk6FPhnwtAXYBuqV1f6gA8A6/AiAjuVUWSK5S/iSuPCGMCrxOgqrYiTdVT4tzDwaf82Q8gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63035}, "engines": {"node": ">= 0.10"}, "gitHead": "9e06a79ca94da7c603a03d7deec56a54f8951330", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"qs": "6.13.0", "bytes": "3.1.2", "debug": "3.1.0", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "^3.0.0", "iconv-lite": "0.5.2", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_2.0.0_1725939072374_0.5649279280820332", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "body-parser", "version": "2.0.1", "license": "MIT", "_id": "body-parser@2.0.1", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "979de4a43468c5624403457fd6d45f797faffbaf", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.1.tgz", "fileCount": 11, "integrity": "sha512-PagxbjvuPH6tv0f/kdVbFGcb79D236SLcDTs6DrQ7GizJ88S1UWP4nMXFEo/I4fdhGRGabvFfFjVGm3M7U8JwA==", "signatures": [{"sig": "MEYCIQDfc0emvKR2IPQCLyWFuhOBpYrd/B8jQ53xopnL+um7GAIhALcgXeWsW4hnZHox9+jffh702VZVcFbG0lNdauYNuK/D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62995}, "engines": {"node": ">= 0.10"}, "gitHead": "966bc9dd141cae791d5634a46af58435327b3170", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"qs": "6.13.0", "bytes": "3.1.2", "debug": "3.1.0", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "^3.0.0", "iconv-lite": "0.5.2", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_2.0.1_1725941735907_0.7169506071938663", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "body-parser", "version": "2.0.2", "license": "MIT", "_id": "body-parser@2.0.2", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "52a90ca70bfafae03210b5b998e4ffcc3ecaecae", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.2.tgz", "fileCount": 11, "integrity": "sha512-SNMk0OONlQ01uk8EPeiBvTW7W4ovpL5b1O3t1sjpPgfxOQ6BqQJ6XjxinDPR79Z6HdcD5zBBwr5ssiTlgdNztQ==", "signatures": [{"sig": "MEUCICX2XmnQRes7X1LDTAHHurqVVgXB/LlVDVbd2/TIjuHnAiEA92TEJvMF7VeuUTkIDLFi1hNQaCOxBleOiK0vyKpJ8Cw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62851}, "engines": {"node": ">=18"}, "gitHead": "9c0d5ec2053f5fac99734582d4638468d7fc7333", "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "22.10.0", "dependencies": {"qs": "6.13.0", "bytes": "3.1.2", "debug": "3.1.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "^3.0.0", "iconv-lite": "0.5.2", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_2.0.2_1730387407106_0.34674206508347427", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "body-parser", "version": "2.1.0", "license": "MIT", "_id": "body-parser@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/body-parser#readme", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "dist": {"shasum": "2fd84396259e00fa75648835e2d95703bce8e890", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.1.0.tgz", "fileCount": 11, "integrity": "sha512-/hPxh61E+ll0Ujp24Ilm64cykicul1ypfwjVttduAiEdtnJFvLePSrIPk+HMImtNv5270wOGCb1Tns2rybMkoQ==", "signatures": [{"sig": "MEUCIQDY+pmjRJNArKCMKG9ErFaoYYjWFPnk/3beIN53ff+A+gIgSwOmQ0A6rY7Adkbi9FOYj134WYem/7H5t/+LvcCQBLg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 62256}, "engines": {"node": ">=18"}, "gitHead": "eedea54b75cbea80eee2a00fdcd6a58d61b0713e", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/body-parser.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Node.js body parsing middleware", "directories": {}, "_nodeVersion": "23.5.0", "dependencies": {"qs": "^6.14.0", "bytes": "^3.1.2", "debug": "^4.4.0", "type-is": "^2.0.0", "raw-body": "^3.0.0", "iconv-lite": "^0.5.2", "http-errors": "^2.0.0", "on-finished": "^2.4.1", "content-type": "^1.0.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "supertest": "6.3.3", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser_2.1.0_1739218358851_0.8096363610937325", "host": "s3://npm-registry-packages-npm-production"}}, "2.2.0": {"name": "body-parser", "description": "Node.js body parsing middleware", "version": "2.2.0", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "devDependencies": {"eslint": "8.34.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "^11.1.0", "nyc": "^17.1.0", "supertest": "^7.0.0"}, "engines": {"node": ">=18"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_id": "body-parser@2.2.0", "gitHead": "0aa4e1128ef88c7f68a851a44c6adbbed35dbfd4", "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "homepage": "https://github.com/expressjs/body-parser#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==", "shasum": "f7a9656de305249a715b549b7b8fd1ab9dfddcfa", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz", "fileCount": 11, "unpackedSize": 59258, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDrp7iY/NXFz1uz4Xx0/tyVLizCBMG5t9vrCaSGPwkeIwIhAP+fZcWBCYKWnE8KpDFWUbkiGyQ1Im+jK9eFxGuqLFND"}]}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/body-parser_2.2.0_1743038601192_0.2954358225156375"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-01-06T08:25:15.060Z", "modified": "2025-03-27T01:23:21.695Z", "1.0.0": "2014-01-06T08:25:15.060Z", "1.0.1": "2014-03-20T22:25:27.731Z", "1.0.2": "2014-04-14T23:26:30.844Z", "1.1.0": "2014-05-11T01:46:04.793Z", "1.1.1": "2014-05-11T04:24:04.602Z", "1.1.2": "2014-05-11T17:45:44.773Z", "1.2.0": "2014-05-12T03:54:56.041Z", "1.2.1": "2014-05-27T04:07:41.457Z", "1.2.2": "2014-05-27T16:25:38.834Z", "1.3.0": "2014-05-31T22:56:36.899Z", "1.3.1": "2014-06-12T03:08:52.518Z", "1.4.0": "2014-06-19T21:52:05.878Z", "1.4.1": "2014-06-19T22:35:08.600Z", "1.4.2": "2014-06-20T02:10:50.530Z", "1.4.3": "2014-06-20T03:13:32.310Z", "1.5.0": "2014-07-21T02:01:17.715Z", "1.5.1": "2014-07-26T20:42:25.306Z", "1.5.2": "2014-07-27T19:20:54.802Z", "1.6.0": "2014-08-06T03:32:28.565Z", "1.6.1": "2014-08-06T21:57:16.941Z", "1.6.2": "2014-08-07T14:33:30.042Z", "1.6.3": "2014-08-11T01:27:18.704Z", "1.6.4": "2014-08-15T02:58:30.772Z", "1.6.5": "2014-08-17T03:44:13.585Z", "1.6.6": "2014-08-27T18:18:13.747Z", "1.6.7": "2014-08-30T04:58:59.287Z", "1.7.0": "2014-09-02T02:43:05.238Z", "1.8.0": "2014-09-06T02:33:55.121Z", "1.8.1": "2014-09-08T06:41:37.882Z", "1.8.2": "2014-09-16T06:23:04.092Z", "1.8.3": "2014-09-20T05:30:50.329Z", "1.8.4": "2014-09-24T05:16:03.920Z", "1.9.0": "2014-09-24T17:35:07.468Z", "1.9.1": "2014-10-23T03:51:35.787Z", "1.9.2": "2014-10-28T04:05:47.581Z", "1.9.3": "2014-11-22T04:24:38.976Z", "1.10.0": "2014-12-03T05:39:28.947Z", "1.10.1": "2015-01-02T02:44:06.199Z", "1.10.2": "2015-01-21T06:37:52.188Z", "1.11.0": "2015-01-31T05:36:16.137Z", "1.12.0": "2015-02-14T04:51:30.494Z", "1.12.1": "2015-03-16T05:41:57.037Z", "1.12.2": "2015-03-17T03:36:57.043Z", "1.12.3": "2015-04-16T03:56:11.396Z", "1.12.4": "2015-05-11T06:05:37.622Z", "1.13.0": "2015-06-15T00:49:47.887Z", "1.13.1": "2015-06-16T19:00:56.953Z", "1.13.2": "2015-07-06T03:19:18.019Z", "1.13.3": "2015-07-31T19:04:44.557Z", "1.14.0": "2015-09-16T16:40:54.186Z", "1.14.1": "2015-09-28T04:49:56.763Z", "1.14.2": "2015-12-16T23:43:48.529Z", "1.15.0": "2016-02-11T02:06:51.428Z", "1.15.1": "2016-05-06T05:35:09.934Z", "1.15.2": "2016-06-20T03:34:56.363Z", "1.16.0": "2017-01-18T03:41:33.243Z", "1.16.1": "2017-02-11T01:36:42.896Z", "1.17.0": "2017-03-01T22:10:17.219Z", "1.17.1": "2017-03-06T13:31:29.554Z", "1.17.2": "2017-05-18T04:57:45.982Z", "1.18.0": "2017-09-09T04:17:26.738Z", "1.18.1": "2017-09-12T15:30:51.470Z", "1.18.2": "2017-09-22T16:50:10.944Z", "1.18.3": "2018-05-14T17:16:32.445Z", "1.19.0": "2019-04-26T03:31:23.981Z", "1.19.1": "2021-12-10T19:52:02.512Z", "2.0.0-beta.1": "2021-12-18T03:52:14.463Z", "1.19.2": "2022-02-16T03:44:29.831Z", "1.20.0": "2022-04-03T01:04:16.807Z", "1.20.1": "2022-10-06T14:00:26.699Z", "1.20.2": "2023-02-22T01:36:59.027Z", "2.0.0-beta.2": "2023-02-23T22:01:16.032Z", "1.20.3": "2024-09-09T23:15:19.538Z", "2.0.0": "2024-09-10T03:31:12.521Z", "2.0.1": "2024-09-10T04:15:36.065Z", "2.0.2": "2024-10-31T15:10:07.326Z", "2.1.0": "2025-02-10T20:12:39.065Z", "2.2.0": "2025-03-27T01:23:21.463Z"}, "bugs": {"url": "https://github.com/expressjs/body-parser/issues"}, "license": "MIT", "homepage": "https://github.com/expressjs/body-parser#readme", "repository": {"type": "git", "url": "git+https://github.com/expressjs/body-parser.git"}, "description": "Node.js body parsing middleware", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}], "readme": "# body-parser\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n[![OpenSSF Scorecard Badge][ossf-scorecard-badge]][ossf-scorecard-visualizer]\n\nNode.js body parsing middleware.\n\nParse incoming request bodies in a middleware before your handlers, available\nunder the `req.body` property.\n\n**Note** As `req.body`'s shape is based on user-controlled input, all\nproperties and values in this object are untrusted and should be validated\nbefore trusting. For example, `req.body.foo.toString()` may fail in multiple\nways, for example the `foo` property may not be there or may not be a string,\nand `toString` may not be a function and instead a string or other user input.\n\n[Learn about the anatomy of an HTTP transaction in Node.js](https://nodejs.org/en/docs/guides/anatomy-of-an-http-transaction/).\n\n_This does not handle multipart bodies_, due to their complex and typically\nlarge nature. For multipart bodies, you may be interested in the following\nmodules:\n\n  * [busboy](https://www.npmjs.org/package/busboy#readme) and\n    [connect-busboy](https://www.npmjs.org/package/connect-busboy#readme)\n  * [multiparty](https://www.npmjs.org/package/multiparty#readme) and\n    [connect-multiparty](https://www.npmjs.org/package/connect-multiparty#readme)\n  * [formidable](https://www.npmjs.org/package/formidable#readme)\n  * [multer](https://www.npmjs.org/package/multer#readme)\n\nThis module provides the following parsers:\n\n  * [JSON body parser](#bodyparserjsonoptions)\n  * [Raw body parser](#bodyparserrawoptions)\n  * [Text body parser](#bodyparsertextoptions)\n  * [URL-encoded form body parser](#bodyparserurlencodedoptions)\n\nOther body parsers you might be interested in:\n\n- [body](https://www.npmjs.org/package/body#readme)\n- [co-body](https://www.npmjs.org/package/co-body#readme)\n\n## Installation\n\n```sh\n$ npm install body-parser\n```\n\n## API\n\n```js\nconst bodyParser = require('body-parser')\n```\n\nThe `bodyParser` object exposes various factories to create middlewares. All\nmiddlewares will populate the `req.body` property with the parsed body when\nthe `Content-Type` request header matches the `type` option.\n\nThe various errors returned by this module are described in the\n[errors section](#errors).\n\n### bodyParser.json([options])\n\nReturns middleware that only parses `json` and only looks at requests where\nthe `Content-Type` header matches the `type` option. This parser accepts any\nUnicode encoding of the body and supports automatic inflation of `gzip`,\n`br` (brotli) and `deflate` encodings.\n\nA new `body` object containing the parsed data is populated on the `request`\nobject after the middleware (i.e. `req.body`).\n\n#### Options\n\nThe `json` function takes an optional `options` object that may contain any of\nthe following keys:\n\n##### inflate\n\nWhen set to `true`, then deflated (compressed) bodies will be inflated; when\n`false`, deflated bodies are rejected. Defaults to `true`.\n\n##### limit\n\nControls the maximum request body size. If this is a number, then the value\nspecifies the number of bytes; if it is a string, the value is passed to the\n[bytes](https://www.npmjs.com/package/bytes) library for parsing. Defaults\nto `'100kb'`.\n\n##### reviver\n\nThe `reviver` option is passed directly to `JSON.parse` as the second\nargument. You can find more information on this argument\n[in the MDN documentation about JSON.parse](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse#Example.3A_Using_the_reviver_parameter).\n\n##### strict\n\nWhen set to `true`, will only accept arrays and objects; when `false` will\naccept anything `JSON.parse` accepts. Defaults to `true`.\n\n##### type\n\nThe `type` option is used to determine what media type the middleware will\nparse. This option can be a string, array of strings, or a function. If not a\nfunction, `type` option is passed directly to the\n[type-is](https://www.npmjs.org/package/type-is#readme) library and this can\nbe an extension name (like `json`), a mime type (like `application/json`), or\na mime type with a wildcard (like `*/*` or `*/json`). If a function, the `type`\noption is called as `fn(req)` and the request is parsed if it returns a truthy\nvalue. Defaults to `application/json`.\n\n##### verify\n\nThe `verify` option, if supplied, is called as `verify(req, res, buf, encoding)`,\nwhere `buf` is a `Buffer` of the raw request body and `encoding` is the\nencoding of the request. The parsing can be aborted by throwing an error.\n\n### bodyParser.raw([options])\n\nReturns middleware that parses all bodies as a `Buffer` and only looks at\nrequests where the `Content-Type` header matches the `type` option. This\nparser supports automatic inflation of `gzip`, `br` (brotli) and `deflate`\nencodings.\n\nA new `body` object containing the parsed data is populated on the `request`\nobject after the middleware (i.e. `req.body`). This will be a `Buffer` object\nof the body.\n\n#### Options\n\nThe `raw` function takes an optional `options` object that may contain any of\nthe following keys:\n\n##### inflate\n\nWhen set to `true`, then deflated (compressed) bodies will be inflated; when\n`false`, deflated bodies are rejected. Defaults to `true`.\n\n##### limit\n\nControls the maximum request body size. If this is a number, then the value\nspecifies the number of bytes; if it is a string, the value is passed to the\n[bytes](https://www.npmjs.com/package/bytes) library for parsing. Defaults\nto `'100kb'`.\n\n##### type\n\nThe `type` option is used to determine what media type the middleware will\nparse. This option can be a string, array of strings, or a function.\nIf not a function, `type` option is passed directly to the\n[type-is](https://www.npmjs.org/package/type-is#readme) library and this\ncan be an extension name (like `bin`), a mime type (like\n`application/octet-stream`), or a mime type with a wildcard (like `*/*` or\n`application/*`). If a function, the `type` option is called as `fn(req)`\nand the request is parsed if it returns a truthy value. Defaults to\n`application/octet-stream`.\n\n##### verify\n\nThe `verify` option, if supplied, is called as `verify(req, res, buf, encoding)`,\nwhere `buf` is a `Buffer` of the raw request body and `encoding` is the\nencoding of the request. The parsing can be aborted by throwing an error.\n\n### bodyParser.text([options])\n\nReturns middleware that parses all bodies as a string and only looks at\nrequests where the `Content-Type` header matches the `type` option. This\nparser supports automatic inflation of `gzip`, `br` (brotli) and `deflate`\nencodings.\n\nA new `body` string containing the parsed data is populated on the `request`\nobject after the middleware (i.e. `req.body`). This will be a string of the\nbody.\n\n#### Options\n\nThe `text` function takes an optional `options` object that may contain any of\nthe following keys:\n\n##### defaultCharset\n\nSpecify the default character set for the text content if the charset is not\nspecified in the `Content-Type` header of the request. Defaults to `utf-8`.\n\n##### inflate\n\nWhen set to `true`, then deflated (compressed) bodies will be inflated; when\n`false`, deflated bodies are rejected. Defaults to `true`.\n\n##### limit\n\nControls the maximum request body size. If this is a number, then the value\nspecifies the number of bytes; if it is a string, the value is passed to the\n[bytes](https://www.npmjs.com/package/bytes) library for parsing. Defaults\nto `'100kb'`.\n\n##### type\n\nThe `type` option is used to determine what media type the middleware will\nparse. This option can be a string, array of strings, or a function. If not\na function, `type` option is passed directly to the\n[type-is](https://www.npmjs.org/package/type-is#readme) library and this can\nbe an extension name (like `txt`), a mime type (like `text/plain`), or a mime\ntype with a wildcard (like `*/*` or `text/*`). If a function, the `type`\noption is called as `fn(req)` and the request is parsed if it returns a\ntruthy value. Defaults to `text/plain`.\n\n##### verify\n\nThe `verify` option, if supplied, is called as `verify(req, res, buf, encoding)`,\nwhere `buf` is a `Buffer` of the raw request body and `encoding` is the\nencoding of the request. The parsing can be aborted by throwing an error.\n\n### bodyParser.urlencoded([options])\n\nReturns middleware that only parses `urlencoded` bodies and only looks at\nrequests where the `Content-Type` header matches the `type` option. This\nparser accepts only UTF-8 encoding of the body and supports automatic\ninflation of `gzip`, `br` (brotli) and `deflate` encodings.\n\nA new `body` object containing the parsed data is populated on the `request`\nobject after the middleware (i.e. `req.body`). This object will contain\nkey-value pairs, where the value can be a string or array (when `extended` is\n`false`), or any type (when `extended` is `true`).\n\n#### Options\n\nThe `urlencoded` function takes an optional `options` object that may contain\nany of the following keys:\n\n##### extended\n\nThe \"extended\" syntax allows for rich objects and arrays to be encoded into the\nURL-encoded format, allowing for a JSON-like experience with URL-encoded. For\nmore information, please [see the qs\nlibrary](https://www.npmjs.org/package/qs#readme).\n\nDefaults to `false`.\n\n##### inflate\n\nWhen set to `true`, then deflated (compressed) bodies will be inflated; when\n`false`, deflated bodies are rejected. Defaults to `true`.\n\n##### limit\n\nControls the maximum request body size. If this is a number, then the value\nspecifies the number of bytes; if it is a string, the value is passed to the\n[bytes](https://www.npmjs.com/package/bytes) library for parsing. Defaults\nto `'100kb'`.\n\n##### parameterLimit\n\nThe `parameterLimit` option controls the maximum number of parameters that\nare allowed in the URL-encoded data. If a request contains more parameters\nthan this value, a 413 will be returned to the client. Defaults to `1000`.\n\n##### type\n\nThe `type` option is used to determine what media type the middleware will\nparse. This option can be a string, array of strings, or a function. If not\na function, `type` option is passed directly to the\n[type-is](https://www.npmjs.org/package/type-is#readme) library and this can\nbe an extension name (like `urlencoded`), a mime type (like\n`application/x-www-form-urlencoded`), or a mime type with a wildcard (like\n`*/x-www-form-urlencoded`). If a function, the `type` option is called as\n`fn(req)` and the request is parsed if it returns a truthy value. Defaults\nto `application/x-www-form-urlencoded`.\n\n##### verify\n\nThe `verify` option, if supplied, is called as `verify(req, res, buf, encoding)`,\nwhere `buf` is a `Buffer` of the raw request body and `encoding` is the\nencoding of the request. The parsing can be aborted by throwing an error.\n\n##### defaultCharset\n\nThe default charset to parse as, if not specified in content-type. Must be\neither `utf-8` or `iso-8859-1`. Defaults to `utf-8`.\n\n##### charsetSentinel\n\nWhether to let the value of the `utf8` parameter take precedence as the charset\nselector. It requires the form to contain a parameter named `utf8` with a value\nof `✓`. Defaults to `false`.\n\n##### interpretNumericEntities\n\nWhether to decode numeric entities such as `&#9786;` when parsing an iso-8859-1\nform. Defaults to `false`.\n\n\n#### depth\n\nThe `depth` option is used to configure the maximum depth of the `qs` library when `extended` is `true`. This allows you to limit the amount of keys that are parsed and can be useful to prevent certain types of abuse. Defaults to `32`. It is recommended to keep this value as low as possible.\n\n## Errors\n\nThe middlewares provided by this module create errors using the\n[`http-errors` module](https://www.npmjs.com/package/http-errors). The errors\nwill typically have a `status`/`statusCode` property that contains the suggested\nHTTP response code, an `expose` property to determine if the `message` property\nshould be displayed to the client, a `type` property to determine the type of\nerror without matching against the `message`, and a `body` property containing\nthe read body, if available.\n\nThe following are the common errors created, though any error can come through\nfor various reasons.\n\n### content encoding unsupported\n\nThis error will occur when the request had a `Content-Encoding` header that\ncontained an encoding but the \"inflation\" option was set to `false`. The\n`status` property is set to `415`, the `type` property is set to\n`'encoding.unsupported'`, and the `charset` property will be set to the\nencoding that is unsupported.\n\n### entity parse failed\n\nThis error will occur when the request contained an entity that could not be\nparsed by the middleware. The `status` property is set to `400`, the `type`\nproperty is set to `'entity.parse.failed'`, and the `body` property is set to\nthe entity value that failed parsing.\n\n### entity verify failed\n\nThis error will occur when the request contained an entity that could not be\nfailed verification by the defined `verify` option. The `status` property is\nset to `403`, the `type` property is set to `'entity.verify.failed'`, and the\n`body` property is set to the entity value that failed verification.\n\n### request aborted\n\nThis error will occur when the request is aborted by the client before reading\nthe body has finished. The `received` property will be set to the number of\nbytes received before the request was aborted and the `expected` property is\nset to the number of expected bytes. The `status` property is set to `400`\nand `type` property is set to `'request.aborted'`.\n\n### request entity too large\n\nThis error will occur when the request body's size is larger than the \"limit\"\noption. The `limit` property will be set to the byte limit and the `length`\nproperty will be set to the request body's length. The `status` property is\nset to `413` and the `type` property is set to `'entity.too.large'`.\n\n### request size did not match content length\n\nThis error will occur when the request's length did not match the length from\nthe `Content-Length` header. This typically occurs when the request is malformed,\ntypically when the `Content-Length` header was calculated based on characters\ninstead of bytes. The `status` property is set to `400` and the `type` property\nis set to `'request.size.invalid'`.\n\n### stream encoding should not be set\n\nThis error will occur when something called the `req.setEncoding` method prior\nto this middleware. This module operates directly on bytes only and you cannot\ncall `req.setEncoding` when using this module. The `status` property is set to\n`500` and the `type` property is set to `'stream.encoding.set'`.\n\n### stream is not readable\n\nThis error will occur when the request is no longer readable when this middleware\nattempts to read it. This typically means something other than a middleware from\nthis module read the request body already and the middleware was also configured to\nread the same request. The `status` property is set to `500` and the `type`\nproperty is set to `'stream.not.readable'`.\n\n### too many parameters\n\nThis error will occur when the content of the request exceeds the configured\n`parameterLimit` for the `urlencoded` parser. The `status` property is set to\n`413` and the `type` property is set to `'parameters.too.many'`.\n\n### unsupported charset \"BOGUS\"\n\nThis error will occur when the request had a charset parameter in the\n`Content-Type` header, but the `iconv-lite` module does not support it OR the\nparser does not support it. The charset is contained in the message as well\nas in the `charset` property. The `status` property is set to `415`, the\n`type` property is set to `'charset.unsupported'`, and the `charset` property\nis set to the charset that is unsupported.\n\n### unsupported content encoding \"bogus\"\n\nThis error will occur when the request had a `Content-Encoding` header that\ncontained an unsupported encoding. The encoding is contained in the message\nas well as in the `encoding` property. The `status` property is set to `415`,\nthe `type` property is set to `'encoding.unsupported'`, and the `encoding`\nproperty is set to the encoding that is unsupported.\n\n### The input exceeded the depth\n\nThis error occurs when using `bodyParser.urlencoded` with the `extended` property set to `true` and the input exceeds the configured `depth` option. The `status` property is set to `400`. It is recommended to review the `depth` option and evaluate if it requires a higher value. When the `depth` option is set to `32` (default value), the error will not be thrown.\n\n## Examples\n\n### Express/Connect top-level generic\n\nThis example demonstrates adding a generic JSON and URL-encoded parser as a\ntop-level middleware, which will parse the bodies of all incoming requests.\nThis is the simplest setup.\n\n```js\nconst express = require('express')\nconst bodyParser = require('body-parser')\n\nconst app = express()\n\n// parse application/x-www-form-urlencoded\napp.use(bodyParser.urlencoded())\n\n// parse application/json\napp.use(bodyParser.json())\n\napp.use(function (req, res) {\n  res.setHeader('Content-Type', 'text/plain')\n  res.write('you posted:\\n')\n  res.end(String(JSON.stringify(req.body, null, 2)))\n})\n```\n\n### Express route-specific\n\nThis example demonstrates adding body parsers specifically to the routes that\nneed them. In general, this is the most recommended way to use body-parser with\nExpress.\n\n```js\nconst express = require('express')\nconst bodyParser = require('body-parser')\n\nconst app = express()\n\n// create application/json parser\nconst jsonParser = bodyParser.json()\n\n// create application/x-www-form-urlencoded parser\nconst urlencodedParser = bodyParser.urlencoded()\n\n// POST /login gets urlencoded bodies\napp.post('/login', urlencodedParser, function (req, res) {\n  if (!req.body || !req.body.username) res.sendStatus(400)\n  res.send('welcome, ' + req.body.username)\n})\n\n// POST /api/users gets JSON bodies\napp.post('/api/users', jsonParser, function (req, res) {\n  if (!req.body) res.sendStatus(400)\n  // create user in req.body\n})\n```\n\n### Change accepted type for parsers\n\nAll the parsers accept a `type` option which allows you to change the\n`Content-Type` that the middleware will parse.\n\n```js\nconst express = require('express')\nconst bodyParser = require('body-parser')\n\nconst app = express()\n\n// parse various different custom JSON types as JSON\napp.use(bodyParser.json({ type: 'application/*+json' }))\n\n// parse some custom thing into a Buffer\napp.use(bodyParser.raw({ type: 'application/vnd.custom-type' }))\n\n// parse an HTML body into a string\napp.use(bodyParser.text({ type: 'text/html' }))\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/expressjs/body-parser/master?label=ci\n[ci-url]: https://github.com/expressjs/body-parser/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/expressjs/body-parser/master\n[coveralls-url]: https://coveralls.io/r/expressjs/body-parser?branch=master\n[node-version-image]: https://badgen.net/npm/node/body-parser\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/body-parser\n[npm-url]: https://npmjs.org/package/body-parser\n[npm-version-image]: https://badgen.net/npm/v/body-parser\n[ossf-scorecard-badge]: https://api.scorecard.dev/projects/github.com/expressjs/body-parser/badge\n[ossf-scorecard-visualizer]: https://ossf.github.io/scorecard-visualizer/#/projects/github.com/expressjs/body-parser", "readmeFilename": "README.md", "users": {"_~": true, "dvk": true, "lwk": true, "nex": true, "vbv": true, "ymk": true, "apwn": true, "arvi": true, "bret": true, "dofy": true, "foto": true, "hema": true, "hodd": true, "isik": true, "kai_": true, "leor": true, "meme": true, "mife": true, "pl0x": true, "tpkn": true, "vwal": true, "wayn": true, "wzbg": true, "xufz": true, "zaks": true, "akiva": true, "apedz": true, "ashco": true, "bengi": true, "brdjx": true, "bsara": true, "chatm": true, "clunt": true, "cygik": true, "dabin": true, "dimps": true, "djeck": true, "dnero": true, "dralc": true, "ehrig": true, "flozz": true, "h4des": true, "haeck": true, "hanhq": true, "imd92": true, "imzhi": true, "jonva": true, "junos": true, "kikna": true, "kremr": true, "lgh06": true, "lhard": true, "lqweb": true, "ma-ha": true, "madeo": true, "miloc": true, "muroc": true, "panlw": true, "pmasa": true, "qjawe": true, "renz0": true, "roxnz": true, "scalz": true, "seanr": true, "sidkb": true, "sopov": true, "tdevm": true, "ukuli": true, "yatsu": true, "zainy": true, "71emj1": true, "adeelp": true, "alvajc": true, "amovah": true, "apopek": true, "arbest": true, "atulmy": true, "bourne": true, "bpatel": true, "bumsuk": true, "chrisx": true, "devnka": true, "felegz": true, "figroc": true, "ggomma": true, "glebec": true, "godion": true, "guogai": true, "hduhdc": true, "helcat": true, "hyteer": true, "iamwiz": true, "ibambo": true, "iliyat": true, "isa424": true, "isayme": true, "itcorp": true, "kepler": true, "kmaric": true, "knoja4": true, "kungkk": true, "luoyjx": true, "mkiser": true, "monjer": true, "moueza": true, "mrbgit": true, "mryeol": true, "nayuki": true, "nestor": true, "nicohe": true, "novalu": true, "nusmql": true, "olonam": true, "omar84": true, "ongmin": true, "paragi": true, "phajej": true, "quafoo": true, "rugare": true, "ryaned": true, "sako73": true, "satoru": true, "sdove1": true, "sergoh": true, "summer": true, "tcrowe": true, "tedyhy": true, "tevins": true, "tiendq": true, "trinup": true, "vjudge": true, "x_soth": true, "xu_q90": true, "yb1997": true, "yong_a": true, "ab.moon": true, "adriasb": true, "alanson": true, "ansuman": true, "antanst": true, "asj1992": true, "asm2hex": true, "ayoungh": true, "benigro": true, "boyw165": true, "cantuga": true, "chinjon": true, "cmangos": true, "deivbid": true, "devossa": true, "dnp1204": true, "drewigg": true, "encloud": true, "endsoul": true, "ezeikel": true, "fasdgoc": true, "flyslow": true, "ftornik": true, "ghe1219": true, "gpuente": true, "gui0704": true, "habiiev": true, "hexcola": true, "honpery": true, "jaguarj": true, "janez89": true, "jcarlos": true, "jirwong": true, "juanf03": true, "jyounce": true, "keybouh": true, "kjarisk": true, "kkho595": true, "kparkov": true, "laoshaw": true, "largaah": true, "lijq123": true, "mkiramu": true, "moamaoa": true, "nadimix": true, "nichoth": true, "nielsgl": true, "pengliu": true, "rapomon": true, "raskawa": true, "ray0214": true, "rparris": true, "ryanlee": true, "samersm": true, "sammffl": true, "sgvinci": true, "shedule": true, "shivayl": true, "shoonia": true, "siirial": true, "sopepos": true, "studi11": true, "subchen": true, "svoss24": true, "swookie": true, "tomchao": true, "toszter": true, "touskar": true, "vboctor": true, "vivekrp": true, "volving": true, "werdyin": true, "x0000ff": true, "xeoneux": true, "xngiser": true, "~arnold": true, "alexkval": true, "alicebox": true, "arifulhb": true, "asfrom30": true, "baiej214": true, "bapinney": true, "bart1208": true, "behumble": true, "cetincem": true, "chaseshu": true, "dadoumda": true, "damianof": true, "damocles": true, "danielye": true, "davincho": true, "dburdese": true, "dgavilez": true, "djamseed": true, "djviolin": true, "dzhou777": true, "esundahl": true, "evegreen": true, "fgarrido": true, "frankl83": true, "freeface": true, "gejiawen": true, "geooogle": true, "gracheff": true, "hektve87": true, "iamninad": true, "ifeature": true, "javadtyb": true, "jmsherry": true, "johniexu": true, "kagerjay": true, "kingcron": true, "kistoryg": true, "kogakure": true, "koobitor": true, "koskokos": true, "koulmomo": true, "krabello": true, "kulyk404": true, "lifecube": true, "livarion": true, "manxisuo": true, "markstos": true, "maxblock": true, "mccarter": true, "milan322": true, "mluberry": true, "mohokh67": true, "moxiaohe": true, "nanikore": true, "nketchum": true, "panos277": true, "pddivine": true, "psmorrow": true, "qbylucky": true, "ralphkay": true, "rayjshin": true, "rbartoli": true, "renjie18": true, "robba.jt": true, "robermac": true, "rochejul": true, "schacker": true, "sibawite": true, "sixertoy": true, "softwind": true, "staraple": true, "stephn_r": true, "t0ngt0n9": true, "tamer1an": true, "tdmalone": true, "techyone": true, "tfentonz": true, "tmurngon": true, "vishwasc": true, "vzg03566": true, "waldrupm": true, "wandyezj": true, "wfcookie": true, "wkaifang": true, "wozhizui": true, "a.sanchez": true, "abpeinado": true, "abuelwafa": true, "allendale": true, "andrewlam": true, "anhurtado": true, "ansing100": true, "asadm2706": true, "asawq2006": true, "awhmandan": true, "bian17888": true, "bigbird92": true, "cascadejs": true, "chunxchun": true, "claudio76": true, "cspotcode": true, "dgray0229": true, "dlpowless": true, "drdanryan": true, "dylanh724": true, "edmondnow": true, "elevenlui": true, "elviopita": true, "faryangsh": true, "fleischer": true, "fmfsaisai": true, "genbuhase": true, "gpmetheny": true, "grabantot": true, "guiyuzhao": true, "huthaifah": true, "igorissen": true, "isenricho": true, "jabbalaci": true, "jakedalus": true, "jerkovicl": true, "jerrywu12": true, "jetbug123": true, "jirqoadai": true, "keanodejs": true, "lakipatel": true, "landy2014": true, "largepuma": true, "ldq-first": true, "liulei224": true, "lvpeng101": true, "madalozzo": true, "mahamdani": true, "markymark": true, "maxwelldu": true, "mightymia": true, "mikemimik": true, "mjurincic": true, "nbuchanan": true, "nikitka_m": true, "nmccready": true, "npmmurali": true, "obouchari": true, "orangeclk": true, "ramzesucr": true, "renchiliu": true, "rlafferty": true, "rubiadias": true, "ruyadorno": true, "sansgumen": true, "sayansaha": true, "sergiodxa": true, "shawn_ljw": true, "sironfoot": true, "slmcassio": true, "starknode": true, "sternelee": true, "swift2728": true, "swmoon203": true, "udaygowda": true, "viperchin": true, "xudaolong": true, "zeroth007": true, "zerouikit": true, "zhongyuan": true, "alahmadiq8": true, "alin.alexa": true, "alshamiri2": true, "andreaspag": true, "avivharuzi": true, "brandonccx": true, "bruinebeer": true, "cfleschhut": true, "chiaychang": true, "coolhector": true, "creativ073": true, "cschmitz81": true, "davidbraun": true, "dccunni171": true, "desmondddd": true, "dwayneford": true, "evdokimovm": true, "f124275809": true, "freshlogic": true, "fsepulveda": true, "garrickajo": true, "greganswer": true, "isaacdagel": true, "joelwallis": true, "jonabasque": true, "juangotama": true, "junjiansyu": true, "justincann": true, "justinliao": true, "khurshedyu": true, "kuzmicheff": true, "langri-sha": true, "leonardorb": true, "lightway82": true, "lotspecter": true, "luffy84217": true, "manneken28": true, "midascreed": true, "monkeymonk": true, "msjcaetano": true, "nate-river": true, "nerdybeast": true, "nickleefly": true, "nicomf1982": true, "nolanthorn": true, "panzhiyong": true, "pengzhisun": true, "princetoad": true, "richard534": true, "richfoxton": true, "rocket0191": true, "salvationz": true, "sammok2003": true, "seasons521": true, "shadyshrif": true, "sigkill(9)": true, "simplyianm": true, "stephenhuh": true, "thetimmaeh": true, "thomashzhu": true, "tiggem1993": true, "tonyljl526": true, "vicsandoli": true, "vleesbrood": true, "winfredzhu": true, "wuyangwang": true, "xenohunter": true, "yaphtes.ks": true, "zhanghaili": true, "13lank.null": true, "adamdreszer": true, "albertofdzm": true, "cbetancourt": true, "chenphoenix": true, "codevelopit": true, "crusaderltd": true, "dawn_scroll": true, "devdebonair": true, "diogocapela": true, "elessarkrin": true, "enzoaliatis": true, "ergunozyurt": true, "faelcorreia": true, "fahadjadoon": true, "fearnbuster": true, "fengmiaosen": true, "flumpus-dev": true, "grahamjpark": true, "he313572052": true, "highgravity": true, "jamesbedont": true, "jonatasnona": true, "karnavpargi": true, "kevinhassan": true, "kodekracker": true, "leondacosta": true, "linuxwizard": true, "lorenazohar": true, "luuhoangnam": true, "malloryerik": true, "manavsaxena": true, "mano.rajesh": true, "marinear212": true, "marlongrape": true, "masterofweb": true, "mknparreira": true, "mr.raindrop": true, "nisimjoseph": true, "ookangzheng": true, "paulhanna33": true, "phoenixsoul": true, "richard_san": true, "rubenjose75": true, "sammyteahan": true, "scotchulous": true, "soulevans07": true, "starlord40k": true, "technolojay": true, "thangakumar": true, "wangnan0610": true, "yeahoffline": true, "zbreakstone": true, "zhenzhuquan": true, "abhijitkalta": true, "adrian110288": true, "alexandermac": true, "bauhuynh2020": true, "brianhanifin": true, "calvinmuthig": true, "dustinphipps": true, "gabriel.fojo": true, "goodnighthsu": true, "grantcarthew": true, "henriesteves": true, "jakedemonaco": true, "jamescostian": true, "joey.dossche": true, "johnny.young": true, "josejaguirre": true, "kwabenaberko": true, "lukaswilkeer": true, "matiasmarani": true, "mobeicaoyuan": true, "mohsinnadeem": true, "mrhuangyuhui": true, "mswanson1524": true, "paulkolesnyk": true, "peter__orosz": true, "philiiiiiipp": true, "processbrain": true, "ristostevcev": true, "runningtalus": true, "ryansalvador": true, "salvatorelab": true, "suhaib.affan": true, "uniquerockrz": true, "vasiltehanov": true, "viktorivanov": true, "wallenberg12": true, "wesleylhandy": true, "wfalkwallace": true, "windhamdavid": true, "augiethornton": true, "chinawolf_wyp": true, "crazyjingling": true, "danielwthomas": true, "duskalbatross": true, "edwin_estrada": true, "franck.lahaye": true, "gamersdelight": true, "htc2ubusiness": true, "ironheartbj18": true, "janani_seetha": true, "jasonwang1888": true, "julienverkest": true, "kunalgaurav18": true, "leelandmiller": true, "lucasmciruzzi": true, "manojkhannakm": true, "markthethomas": true, "matthiasgrune": true, "piyushmakhija": true, "rabahtahraoui": true, "ral.amgstromg": true, "richardcfelix": true, "robinblomberg": true, "scottfreecode": true, "serge-nikitin": true, "tangshingkwan": true, "tranceyos2419": true, "arnold-almeida": true, "beatwinthewave": true, "bigglesatlarge": true, "bradleybossard": true, "danielbankhead": true, "elitelegendary": true, "emreparlayan42": true, "geduardcatalin": true, "imaginegenesis": true, "javimaravillas": true, "karzanosman984": true, "kaveh.ghaboosi": true, "luismoramedina": true, "matteospampani": true, "mistertakaashi": true, "shahabkhalvati": true, "shanewholloway": true, "suryasaripalli": true, "anatolie_sernii": true, "docksteaderluke": true, "gabriel_hansson": true, "hyokosdeveloper": true, "jeffb_incontact": true, "marcobiedermann": true, "mateussampsouza": true, "mauriciolauffer": true, "urbantumbleweed": true, "animustechnology": true, "carlosvillademor": true, "lherediawoodward": true, "ys_sidson_aidson": true, "christopherritter": true, "destemidosistemas": true, "nguyenmanhdat2903": true, "ognjen.jevremovic": true, "christopher.urquidi": true, "eng-gabrielscardoso": true, "felipeferreirasilva": true, "obsessiveprogrammer": true, "nguyenvanhoang26041994": true}}