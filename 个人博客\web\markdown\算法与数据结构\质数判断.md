# 根据数论判断是否是质数:O(sqrt(n))

```c++
//x是质数是指,x>2且x只有除了1和它本身两个因数
//数论：i如果是n的约数,那么n/i也一定是n的约数
#include<iostream>
#include<algorithm>
using namespace std;
const int N=105;
long long n,a[N];

bool is_prime(long long x)
{
    if(x<2)return false;
    for(long long i=2;i<=x/i;i++)
    {
        if(x%i==0)return false;
    }
    return true;
}

int main()
{
    cin>>n;
    for(long long i=1;i<=n;i++)
    {
        scanf("%lld",&a[i]);
        if(is_prime(a[i])==true)printf("Yes\n");
        else printf("No\n");
    }
    return 0;
}
```

[ACWING-试除法判断是否为质数](https://www.acwing.com/activity/content/problem/content/935/)

# 根据数论求解质因数不稳定为O(sqrt(n))



```c++
//从2开始枚举到n，如果n%i则i一定是质因数
#include<iostream>
#include<algorithm>
using namespace std;
const int N=105;
long long n,a[N];
void divide_prime(long long x)
{
    for(long long i=2;i<=x/i;i++)//由于n分解后的质因子中最多只包含一个大于根号n的质因子，所以我们可以只枚举得到i对应的<根号x的那部分
    {
        
        if(x%i==0)//i一定是一个质数，能被整除，则一定也是一个质因数
        {
           long long s=0;//记录指数
            while(x%i==0)
            {
                x/=i;
                s++;
            } 
            printf("%lld %lld\n",i,s);
        }
    }
    if(x>1)//每一个大于一的数都可以被分解成1和它本身这个质数
    {
        cout<<x<<' '<<1<<endl;
    }
    printf("\n");
}
int main()
{
    cin>>n;
    for(int i=1;i<=n;i++)
    {
        scanf("%lld",&a[i]);
        divide_prime(a[i]);
    }
    return 0;
}
```

[ACWING试除法优化求解质因数](https://www.acwing.com/problem/content/869/)