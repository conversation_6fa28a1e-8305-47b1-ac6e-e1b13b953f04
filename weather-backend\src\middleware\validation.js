const { validateCity, validateCoordinates, validateLanguage, validateUnits, validateDays } = require('../utils/validators');

// 验证城市查询参数
const validateCityQuery = (req, res, next) => {
  const { city, lang, units } = req.query;
  
  if (!city) {
    return res.status(400).json({
      success: false,
      error: {
        message: '城市名称是必需的',
        code: 'MISSING_CITY'
      }
    });
  }

  if (!validateCity(city)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '无效的城市名称',
        code: 'INVALID_CITY'
      }
    });
  }

  if (lang && !validateLanguage(lang)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '不支持的语言代码',
        code: 'INVALID_LANGUAGE'
      }
    });
  }

  if (units && !validateUnits(units)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '不支持的单位类型',
        code: 'INVALID_UNITS'
      }
    });
  }

  next();
};

// 验证坐标查询参数
const validateCoordinatesQuery = (req, res, next) => {
  const { lat, lon, lang, units } = req.query;
  
  if (!lat || !lon) {
    return res.status(400).json({
      success: false,
      error: {
        message: '纬度和经度是必需的',
        code: 'MISSING_COORDINATES'
      }
    });
  }

  if (!validateCoordinates(lat, lon)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '无效的坐标',
        code: 'INVALID_COORDINATES'
      }
    });
  }

  if (lang && !validateLanguage(lang)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '不支持的语言代码',
        code: 'INVALID_LANGUAGE'
      }
    });
  }

  if (units && !validateUnits(units)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '不支持的单位类型',
        code: 'INVALID_UNITS'
      }
    });
  }

  next();
};

// 验证预报查询参数
const validateForecastQuery = (req, res, next) => {
  const { city, days, lang, units } = req.query;
  
  if (!city) {
    return res.status(400).json({
      success: false,
      error: {
        message: '城市名称是必需的',
        code: 'MISSING_CITY'
      }
    });
  }

  if (!validateCity(city)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '无效的城市名称',
        code: 'INVALID_CITY'
      }
    });
  }

  if (days && !validateDays(days)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '天数必须在1-16之间',
        code: 'INVALID_DAYS'
      }
    });
  }

  if (lang && !validateLanguage(lang)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '不支持的语言代码',
        code: 'INVALID_LANGUAGE'
      }
    });
  }

  if (units && !validateUnits(units)) {
    return res.status(400).json({
      success: false,
      error: {
        message: '不支持的单位类型',
        code: 'INVALID_UNITS'
      }
    });
  }

  next();
};

module.exports = {
  validateCityQuery,
  validateCoordinatesQuery,
  validateForecastQuery
};
