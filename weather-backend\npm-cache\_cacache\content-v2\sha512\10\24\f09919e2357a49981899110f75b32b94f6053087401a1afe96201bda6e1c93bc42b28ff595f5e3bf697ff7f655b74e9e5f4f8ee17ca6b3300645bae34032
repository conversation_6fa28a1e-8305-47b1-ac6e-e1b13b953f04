{"_id": "supertest", "_rev": "436-9ee84b9b3a294daea0eb65b3315a7a57", "name": "supertest", "dist-tags": {"next": "5.0.0-0", "latest": "7.1.4"}, "versions": {"0.0.1": {"name": "supertest", "version": "0.0.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dfb2f55ca67e75947bddc75f24d874cf02c88559", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.0.1.tgz", "integrity": "sha512-Y/QZSARtYQqrbGyEpiP8OGOQA2h7gqJMD2qU97y7IkDlNZPoTC0SWOWxIfLmrmxnSjKz+FIgMPSBmtZeKfJGag==", "signatures": [{"sig": "MEUCIQCEVM0NR0nt6/Fe1Jc0hH5jdd4+11wkLZkBAq6BBTa2qwIgIE0PLfsljJi3huOIsQ90WRcHhCzPQUs2WrOMTq/G5HA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.5.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.1.0": {"name": "supertest", "version": "0.1.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "082e5534399261d710799fb699063ea6cd501f36", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.1.0.tgz", "integrity": "sha512-6OydLhtkOBH8mi3vwBwVoKaNXveP0wa36dnDnS3AUjmzkU20g4d9/4IY62hvDh/nhfLqM2gdo8TlF0EQMu96RQ==", "signatures": [{"sig": "MEYCIQDTXusvXHRDLKg60IxMZsGwwz+mppSuekQXWKRwkeY17QIhAJ8tZT/ja2I+rK4Jkq4D+W16RMjUvFwCCymkwk00F54w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.5.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.1.1": {"name": "supertest", "version": "0.1.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.1.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "346ad1b6c350075541755f8f4b6a5f2437d7da3d", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.1.1.tgz", "integrity": "sha512-3DsmuC+nlNGr63ZmqqqwJdgVcVt8uQ9ysTXZ1+vXJiKdm90S3oqbMJvo2ivnfhdgeAECrQYlH2p2jN69+avlTw==", "signatures": [{"sig": "MEQCIEakhBDxPpYa+rh/51nE0Zqrqz2WW6slRsaymreLG2M7AiA6K0PuoaP1jTd+ZO9PbscKhYgFyk2eG76QqIhK1Af2JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.5.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.1.2": {"name": "supertest", "version": "0.1.2", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.1.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "868a854457d9900f30b0179c621da84deb3ce693", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.1.2.tgz", "integrity": "sha512-si8QRBz5Bb8QafUb9JIauLnOKl69AmNu6nC94MAKqUY30sjfLsbPAJBSA5HoZRUi3e/xLaPrdn6pjxoQqYWacw==", "signatures": [{"sig": "MEYCIQDHWZm7XcvbhmR1wSsV9bZR+UGAfmyR1+KM5Ytrzqs/agIhAJqfgpwlbGbtlTS9x06kkDNj4oHERnBxve63fo3cFPYw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.5.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.2.0": {"name": "supertest", "version": "0.2.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "42ba3809457781e171896e5d1de1de14c39afb0e", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.2.0.tgz", "integrity": "sha512-w/x2qSDOvkBMgdqjNDngHQroI+OYX+p1+xtXSyhuPvanvZOnUriFSYr3URLkxOlbZU+HLDwwCJFnletsKl91Ag==", "signatures": [{"sig": "MEYCIQDJBwyj9hBiCaDyK7RlnYRowMeR+xEeewZXmQAVQFREcQIhAPwbLGqlq4dRcnENP0rP6qEciyxFv0Ws/VlSR6Orrznc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.9.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.3.0": {"name": "supertest", "version": "0.3.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.3.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f8c5fe9e1a7a85a366d65db2a71a537fcf8d6c71", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.3.0.tgz", "integrity": "sha512-DyhWV/VU4FhztjrY3lGRVLpDWR+mwuUI1W3g1LhCZL+SFARCXbvbWkgAEy991ZQ2XHRZPBSZstdKCj94BySApA==", "signatures": [{"sig": "MEUCIQDqqntNEUrdA1jixQo/enP1P3Sc5eR3maOVx74Cl7ldCAIgWEOYoMKDtXatac3rTCj6kEFRcIq96diF2PPD2ZEX97Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.1.61", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.9.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.3.1": {"name": "supertest", "version": "0.3.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.3.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7ab695845892800cf6cc8736fdeb91da23d82039", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.3.1.tgz", "integrity": "sha512-vM0FH8zsGt/D+qF3tjsof1xhaLnFAkslf9kSBHEvcnXhwrt6vdgNVT2lHb4Fht+9u3s5QXqX5Vh47c7a7I6JFg==", "signatures": [{"sig": "MEQCICAii9nZm7v2zUV4FpHcbjtMmp4MqL/PiXTcODNR5AmqAiAap8PrNOKGw8X1Ez6OW5lJuifcD2wwAAHYmPQEnWjkJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.1.61", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.9.5"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.4.0": {"name": "supertest", "version": "0.4.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.4.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1acf634ac3219fa35b6c10ec8773b097394736e6", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.4.0.tgz", "integrity": "sha512-WDfr2413um+MNwts9Mv740mj8vh+gC/x6LbRRegqLiBgcRVsUzYJnouHHAUg1kXqk43AZPZkiRNeDZOLGMwgkQ==", "signatures": [{"sig": "MEQCIA2PfghfPHWMSlXmad3g/Pc5FZQoRjovTlpsvXgUqDRjAiBGtSQJWYAs93g0lEbQyZDrtse7fnZD/jpms08+DI1KUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.1.63", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.9.5"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.4.1": {"name": "supertest", "version": "0.4.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.4.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c6916238e93c6d5b38426c12cf1a524c27c1a04d", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.4.1.tgz", "integrity": "sha512-tOv4F7URWEgGy6nhL1gAMPpwRvwsyLobw6+co6+SwPyoFP+TQnB6wlL1RTOoxzfmZixtjsuBtF78ALKo3XkD2w==", "signatures": [{"sig": "MEUCIDDyEIC/lF36Tn9hp9ekvnuwBDis5JgXMvSZ5AO51U/lAiEAzrt2fge38b017Q0ZXOXdFCGVOCbe8YI7hly0BQuhZ6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.1.65", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.9.10"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.4.2": {"name": "supertest", "version": "0.4.2", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.4.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "53a74343f639c315c916e1f60c1e288e62f6d26e", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.4.2.tgz", "integrity": "sha512-EQnu7xpdx9YZnD0fbmmUiwyj1gwtCI+LaU8ilQ4LZXIezNOc37mc1Ly5VW2XZoi5Mq9TYjx5YoUBGQuMlLCB6Q==", "signatures": [{"sig": "MEUCIQClhOtlAxMqhSB8iYftEBiolq4G/XbpNqDjKLKT6DL3kwIgEtpA1V6BmoHe0uJ7nTjtUtf73H+1NDeqo8guyvPYNMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.1.65", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.10.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.5.0": {"name": "supertest", "version": "0.5.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.5.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d417056fdf7b9f3d13c5131ada0d1ea902ddd4b0", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.5.0.tgz", "integrity": "sha512-D/vIum5n3N6J+nfa9rYwmKvlgd9RV6oGwm4wyZY2m6curAyu6O5/oiZ9Zirv7qZmZEdKKIL9i1O+Eic0u6DgAw==", "signatures": [{"sig": "MEYCIQCATkD8Bst6gfBLK+Bf7tYBNgtcMqdmVgd4+thsak38BgIhALmamZA7Gf0OthXz3WHYt2K/RMacW2vi+o84rPP9V2mV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.1.65", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.10.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.5.1": {"name": "supertest", "version": "0.5.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.5.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2098797cb685a2578e9b142bf0240fa22a1b86c1", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.5.1.tgz", "integrity": "sha512-ZRAMeTTuN+7HIhzDJDOgRXsuuGKir+G3T9CnRDdCZDIDAW4c+BzmOvJPYGa6ZUUMqEJ2yU0AtQbgphMXfHfBMw==", "signatures": [{"sig": "MEQCIF1Qo07QfZtNz+8sPVFXElUg+4xMiu5lUDAl/Jw5fQVbAiAQmY0rdXIbI4d/sgkq/cveydENXwpd0oLJzKpWgi5YQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.1.66", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.10.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.0.0beta4"}}, "0.6.0": {"name": "supertest", "version": "0.6.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.6.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b94ce2839943e912e9aa6b482465c672762c7ae6", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.6.0.tgz", "integrity": "sha512-sE0M2SJRpZRWIAxIH4Y4/qsKMPduw5cRmaTrTv8hkUq+Q1F3qHfAtLIoVx/rImFwidCGxgTIVCn7t0k9Dir5EA==", "signatures": [{"sig": "MEUCIQDZgVc6r4yxBxOW3PgdoDdkZ26xPfWkAG0UaEEJGgUU4QIgOD0DR2bVTCFL9ONz94pjG8r1M4YIqHpmDJrDoUbUaU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.2.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.10.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.6.1": {"name": "supertest", "version": "0.6.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.6.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8e7c82343644c30fecb8f4b23b1aa3f1e9f7ae8e", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.6.1.tgz", "integrity": "sha512-cVPaQZVnISSnLkXdemOqRdQ/snUCJrvZs0dDl+C+5S/vnrg27el0CxTYcOdnbh7O+b1LfIplENpIuLFjMlY93A==", "signatures": [{"sig": "MEUCIQDPPOym51dXsKyd5GBTyhzyMex9091+vUTBux8Bs298IQIgL9pmulWdVy4zVjztEAfnGbESm05zNfcFzMW8Vt86J2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.2.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.10.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.7.0": {"name": "supertest", "version": "0.7.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.7.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8a1241e594fc7144f42599166b2311770068c694", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.7.0.tgz", "integrity": "sha512-hCwIHyRYumiQKsg1bgy7FQPhb0ihl7fRlPAcaYf/5crSFBAuYz59+9Ntv4v4Nzw+FsSRYDqiw0tCTDzJeS2qpw==", "signatures": [{"sig": "MEUCIHnYJXsdmbSTf/VZwghhAfkRIxSVuvAS8LUoe/0PaqlKAiEAkRyfcJzgqulRlLw6/vqjWuq5eYxUiw/tpnpstjOesfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.2.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.10.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.7.1": {"name": "supertest", "version": "0.7.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.7.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "349a65a8bfb5207250658f71761279ad3a671d88", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.7.1.tgz", "integrity": "sha512-YjCEmPFwMgTHpx3gCbBgxOBSV1o9K6w3csB4dBq+udE+3UuDkK8nxo5FiIE6itxvVVrGfe1yzDAyTO+aowrWyw==", "signatures": [{"sig": "MEUCIQDhYezgw13T4s2vvJayPp63WUOi/sXNXJBkgiyPci1X4QIgP0vyliAbiTj0H7joAe81UXdsinp3/uuOvNpaPpUqTuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "_npmVersion": "1.2.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.15.1"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.8.0": {"name": "supertest", "version": "0.8.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.8.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "c8dd008358ed60175cfd4dfab0ab1af81d0dc55b", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.8.0.tgz", "integrity": "sha512-NTr<PERSON>rewXZWLcQKg6XkevDZcQ8ujLakP9eamiJLqKC1OkkdQWGgpkpX7b3+GZRNZOmlz3L+EHD4dWE5E0vDGwIQ==", "signatures": [{"sig": "MEYCIQCiun8AYQhCuxOJF1G8nj2T0acMw8JqINdPKmRo+0uXQgIhAO0jHO5IrIUbbRL9eQX5HcACeKIFtzxgMvI6Nl08ZB8w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.0.1", "superagent": "0.15.1"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.8.1": {"name": "supertest", "version": "0.8.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.8.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "be792b92481d8e33a4ebe8907495c5192387d101", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.8.1.tgz", "integrity": "sha512-eQ7GeYerNss9u4MUdm4PkOG28r1E5w8BNHwT8hAx5sPiqRCVB7dQYRMGMlcWdhEVgb4qwSEJGVEq9Lr3WshU9g==", "signatures": [{"sig": "MEYCIQC1Lw/R9RjySKn1WO139vROcOZalS9Fr/QEeLujfMCLxQIhAPjM3WQ8Be7BHsGeYEvPzD3Q/Xo/JY51GT4cjxS5rKhv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.1.0", "superagent": "0.15.1"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.8.2": {"name": "supertest", "version": "0.8.2", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.8.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "2da3519f7c9dcfa8759e2de9c794f1d4acc0333b", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.8.2.tgz", "integrity": "sha512-oaPr5hPSp+Hq++cogE4418PSy8rcdnci1NwhbZluil18nh0OpJRxH+2lUnVHopBP5qfi7ACth14uUGy0KwX+Mg==", "signatures": [{"sig": "MEYCIQCGFUYMM5W32zPVvQi3cwSYyb9fMF3rAU3UfS8TLcJhPQIhAMataMfNZYsonOzOXrWnnFsUp165ZoR1Oc6nScS4IXIR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.1.0", "superagent": "0.15.7"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.8.3": {"name": "supertest", "version": "0.8.3", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.8.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "7af32af5bd74514f3fabc444facc9858ae438593", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.8.3.tgz", "integrity": "sha512-GGh66sx16/BCe0XnItQOcoYMwg9sPvcaJa4D4rKafBIHYTZ//Yt3aWjh2cbW6CYd7iS6uzuoy/woa+mkLrfQYQ==", "signatures": [{"sig": "MEUCIBsaLJJ0ruCtafBQElZucIGVqRqwKQbaKylF4j1deieGAiEAy8tOi2IoETYUqTxcSr2UI7l4qL8Ta0HUejbmIoE9WVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON>@recurly.com"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.1.0", "superagent": "0.16.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.9.0": {"name": "supertest", "version": "0.9.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.9.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "955efa32ad01fb9eecfc6f7c3122699a5843bba4", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.9.0.tgz", "integrity": "sha512-yW7DiKh3ijuD8VFeJnuW1mAhBSA1TuOx+om5PQgjYMf9JwNyuypcOKSQeND2G04OXJ4sKO3EUeq43ON/5TjYKg==", "signatures": [{"sig": "MEUCID/dVDGXOLI84h4PAfH0dO7eCTWpMygH81h9ZNp05X2+AiEAt/E8FF1WtQyyCV3/9pAtVse+tEXJpiowjglWb2AN3ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.3.23", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.1.0", "superagent": "0.16.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.9.1": {"name": "supertest", "version": "0.9.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.9.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "f6c2b36d026fdf5a643cd0ba6934004fb8d8a76b", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.9.1.tgz", "integrity": "sha512-Fwi39x8cH31BJl9jSVHb/GfVJXH8DR2DmD8vBiFRW+n5n1Y3yKR2lxFYDfewOiUhnFmmp4SxzN/xdN4hWUS5CA==", "signatures": [{"sig": "MEQCIBVYLjUVYLoyJhWMO0e7hcwLYuJrmdDJmyBPHLFB0/ubAiAsteYOxgOJoN5QX3MVrjvIqmL/Q33B70UcfHG6fXUF5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON>@recurly.com"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "^0.1.0", "superagent": "^0.17.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.9.2": {"name": "supertest", "version": "0.9.2", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.9.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "0226502732d262e11d3db8ec66e12a24a927e8f5", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.9.2.tgz", "integrity": "sha512-9kl8s2G68RxoQEzXrCZ+l3ud0tmxqgElCunx0jPtwu0Q5SMVpE8/ckLLWzL8XnO68/7cmKyQebhkpESOZ0hLhg==", "signatures": [{"sig": "MEYCIQCul8Wi/wOVUmZ++r4KloLE2PXzZi/v2/j1vAMme4i2/gIhAIgN3iWYK1fOKYkI8AnJiRnXXgyi1YCYyAHWhCxYyp00", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON>@recurly.com"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.1.0", "superagent": "0.17.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.10.0": {"name": "supertest", "version": "0.10.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.10.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "5ba821b5f4e9e643292fcf87268dfd2688bdbb58", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.10.0.tgz", "integrity": "sha512-MDy+U1Xd+FCozysxqavW+/F59Oo7BAYe70FHEx7uxrvm9QxmXjf81j7hckTEVuqU7rppmdpNAEAHjdy6qwK96w==", "signatures": [{"sig": "MEQCIBgJCrh3LKrI5+5xqRWgEI+fymUjlgbO8PQ8xz6fGUyEAiBS8R/5r0WYqwctlcAh/zsV0zJ1syGATn3MUyF/K3K/Pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON>@recurly.com"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.1.0", "superagent": "0.17.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.11.0": {"name": "supertest", "version": "0.11.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.11.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "f8496b9f05cac1ea2fcec1d21adeca7167f42460", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.11.0.tgz", "integrity": "sha512-42t7zdXYgELgud3imxY1eYZvfyL0BYaJrgmEjC16w16nuZwWrTm5f8IkdUzCtu3wQXewijxCeQxIfGGSbRvyRQ==", "signatures": [{"sig": "MEUCIA4bgnfiZUZZLiJRsESKslDa3cvkw8aXpRFEttxsBwefAiEA0emMqRpUjMv1hiXQ8BNPyOzf5QYtnnffZv8Ei6HSVC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON>@recurly.com"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.1.0", "superagent": "0.17.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.12.0": {"name": "supertest", "version": "0.12.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.12.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "bb3bd1fb661f4b7cb545d957bc841d166fc2c709", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.12.0.tgz", "integrity": "sha512-Uo8tSvXW5ouhYISSrn+g4ma+v2r9kXhUY+D+QDiD17VZozX/O8k+pw2uE+ZrDbdxU1A6hOknv1zP4E0B0CkofA==", "signatures": [{"sig": "MEUCIGjWa0q6eiqP9hRARSsLP1ns7srxt2Y2wbKAhhUnl0iVAiEAhlJ1gifgeSZj3UsN1iTWra9lGy6j0yu/WBZ+3MrZz34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON>@recurly.com"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "0.1.0", "superagent": "0.18.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.12.1": {"name": "supertest", "version": "0.12.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.12.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "02abbcbecf148838674971318be4021b25fa1881", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.12.1.tgz", "integrity": "sha512-lNpXfu/XsU7mDDwaXy2Q0qp71+9PlBk1kpx2wbebJgOXwuwZxINc40MAfK/PlVh9isWUXMhjAE8n1bxtaRYL7A==", "signatures": [{"sig": "MEUCIQCMeN1QWkA1ESXMU8sJbN3QAcQm/p1eKP/uPoS5lJEtJQIgTFlZJ/oMvfbRrbfUYrThPj0SdG0UJ6r24ud8IKQlKkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON>@recurly.com"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "1.0.0", "superagent": "0.18.0"}, "devDependencies": {"mocha": "*", "should": "*", "express": "3.1.0"}}, "0.13.0": {"name": "supertest", "version": "0.13.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.13.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "4892bafd9beaa9bbcc95fd5a9f04949aef1ce06f", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.13.0.tgz", "integrity": "sha512-93A2mDSb+6nK6OfGd+g+KajO+Xf7uHiMu92lYQGzs6y56Q4D+72xRGyb6fL0qp4/zvQHHt4kxQHJ5HtFsrRGZA==", "signatures": [{"sig": "MEQCIHu2C2oNisTQzg/kX3hKRFY3YRz1L8V5RkadmNYoQxvUAiBgp3PgnrLGOKNF2vA5OR/1k8R9FSQzdy0FDodk0I6oZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4892bafd9beaa9bbcc95fd5a9f04949aef1ce06f", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON>@recurly.com"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "1.0.0", "superagent": "0.18.0"}, "devDependencies": {"mocha": "1.19.0", "should": "3.3.1", "express": "3.1.0"}}, "0.14.0": {"name": "supertest", "version": "0.14.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.14.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "d385a8ebced95350de8bde26460d848917dee305", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.14.0.tgz", "integrity": "sha512-ldXzY4a46u1lAQbxQo3ux/rXC3s4NA86k/4U9qQnF8DMpuHxUIGEXmxbdCpgPae4RQOoUmnlMT1Su308DBcNow==", "signatures": [{"sig": "MEQCIBYAnrzhtY3A38m0ebYoD1CjmvMnX/RewiziW1+9K9LVAiBk8VwXVGDu8pejisgddjYRIghN8Em+mbpPe+ENgKdEuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d385a8ebced95350de8bde26460d848917dee305", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "1.1.0", "superagent": "0.19.0"}, "devDependencies": {"mocha": "1.19.0", "should": "3.3.1", "express": "3.1.0"}}, "0.15.0": {"name": "supertest", "version": "0.15.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@0.15.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "86118695de4be58869b3ee94c45e1d084ca7fac5", "tarball": "https://registry.npmjs.org/supertest/-/supertest-0.15.0.tgz", "integrity": "sha512-vsdfVq3TzNt1sYWonvAO8ChG3azYeL2eKV6hY7tJrhHIVie7RSohbL4Nf2DCZ1/colXbF4Jsi1kVfa0lBv0Cpw==", "signatures": [{"sig": "MEUCIDQzMGgZPM0wjVMXqtVmonIh2NYzQyDi4L8AnV/rjMlXAiEAtNUUmJR4fnSJW4OqS9SoejdKisCAmyK5MrEQhV5u470=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "86118695de4be58869b3ee94c45e1d084ca7fac5", "gitHead": "eb039686059c842d4f9de271146408f893cc54a8", "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "dependencies": {"methods": "1.x", "superagent": "~0.21.0"}, "devDependencies": {"mocha": "1.19.0", "should": "3.3.1", "express": "3.1.0"}}, "1.0.0": {"name": "supertest", "version": "1.0.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "d10c59bf4b9a6f8df1ea300e02221f1d6ae091ed", "tarball": "https://registry.npmjs.org/supertest/-/supertest-1.0.0.tgz", "integrity": "sha512-sKKpVDUNcVjoRbFugkUBgfKsw5HaCKYnEJXCGDs58J2MqWPo/XrBw+B56c7AKNU+WZBmbZY43kXe0MTu09aBBQ==", "signatures": [{"sig": "MEUCIHwS9UHfwrQ78JahTyBG42jH2z+3SEBn3XgNo7r3KXd9AiEAx7OiazjyzohZla5bKFMSdzlGUoCVctokT2mN4KVNu+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d10c59bf4b9a6f8df1ea300e02221f1d6ae091ed", "engines": {"node": ">=0.8.0"}, "gitHead": "a79b7b7db52878f5291e92aa61075661906bef05", "scripts": {"test": "make test"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"methods": "1.x", "superagent": "~1.2.0"}, "devDependencies": {"mocha": "2.2.4", "should": "6.0.1", "express": "3.20.2", "body-parser": "~1.12.3"}}, "1.0.1": {"name": "supertest", "version": "1.0.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "876dd8eed6e9c7f5d7ae438abbd58f44efda1863", "tarball": "https://registry.npmjs.org/supertest/-/supertest-1.0.1.tgz", "integrity": "sha512-YImbM+Vv6atG5ROjk4kLWJsU5ZrlyNqLANqirdJRGtl4rk2jK44viKGGWQE+DrzNC+fE9/Szox3W71u4VpJEYw==", "signatures": [{"sig": "MEUCICRAT1NQGhxmdwoDpVs2V/pxkKy2NckdTi6SsTfetl3UAiEAqjSbI6D6/XRvOnHfHp1s9YFcxWZOt0xZJHL1H7LgW+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "876dd8eed6e9c7f5d7ae438abbd58f44efda1863", "engines": {"node": ">=0.8.0"}, "gitHead": "4f73760055b736025eeb098c49864269b52ea75f", "scripts": {"test": "make test"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"methods": "1.x", "superagent": "~1.2.0"}, "devDependencies": {"mocha": "2.2.4", "should": "6.0.1", "express": "3.20.2", "body-parser": "~1.12.3"}}, "1.1.0": {"name": "supertest", "version": "1.1.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@1.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "3c441b2d9ec0ed42524eb31495611ca3d434ab1c", "tarball": "https://registry.npmjs.org/supertest/-/supertest-1.1.0.tgz", "integrity": "sha512-h9+RXVfNYI3gm1K/sAldjc5UvnqFzFfjOeuZuWj6KkvOOMIpvrwfARJquDoSKpJ02Jh/6Z54MTO/f1Sk0iPQEQ==", "signatures": [{"sig": "MEQCIFSTq3kmijn6Jb/6MbSKckTn5hnElwIq27abSyWRe9QWAiBIqQz7hZCO0VJzfdh6JBGSlCEmeGANdbu6yOmbWy4XGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3c441b2d9ec0ed42524eb31495611ca3d434ab1c", "engines": {"node": ">=0.8.0"}, "gitHead": "67381281851f4069cd2478d25963bc205571d4cf", "scripts": {"test": "make test", "pretest": "npm install"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"methods": "1.x", "superagent": "~1.3.0"}, "devDependencies": {"mocha": "~2.2.5", "should": "~7.0.2", "express": "~4.12.4", "body-parser": "~1.13.2", "cookie-parser": "~1.3.5"}}, "1.2.0": {"name": "supertest", "version": "1.2.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@1.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "850a795f9068d2faf19e01799ff09962e0ce43be", "tarball": "https://registry.npmjs.org/supertest/-/supertest-1.2.0.tgz", "integrity": "sha512-JZnQKOTbPejOqKpqPiFU5WcL8YHKel3xCrL0AmQxLIQnM5WLGCSJ/wI9gdD2redLHsnISeXkQBcAyKgqD0Ybag==", "signatures": [{"sig": "MEUCIAnf8OiaKQ/J533r3D1IujvMAMofOkzW0UMpZQPwH6R3AiEAtipS/7t0Ep/SAT/i0qbpGFpH3wo88/QORE7gpy9gdoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "850a795f9068d2faf19e01799ff09962e0ce43be", "engines": {"node": ">=0.10.0"}, "gitHead": "f4efb75950d3081a06dc1e9e88b0372a0512f08e", "scripts": {"test": "mocha --require should --reporter spec --check-leaks", "pretest": "npm install"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "Super-agent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"methods": "1.x", "superagent": "^1.7.2"}, "devDependencies": {"mocha": "~2.2.5", "should": "~7.0.2", "express": "~4.12.4", "body-parser": "~1.13.2", "cookie-parser": "~1.3.5"}, "_npmOperationalInternal": {"tmp": "tmp/supertest-1.2.0.tgz_1455204548964_0.9615733381360769", "host": "packages-6-west.internal.npmjs.com"}}, "2.0.0": {"name": "supertest", "version": "2.0.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@2.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "060e18edae56e3d62b95c6e9c4e2a5e4e50979ff", "tarball": "https://registry.npmjs.org/supertest/-/supertest-2.0.0.tgz", "integrity": "sha512-OUwfZ9iyL+HqSlM+xVcw9GvU0dNtRiDFSbVZfXj9+r1ThmIPUe7flXMFSwPnrWfPKVEJ3QWGn598aEkx9aD1SA==", "signatures": [{"sig": "MEQCIDbQmT4lwDfPDUMJtc57fpjEvrQYEdRTB89yc255J9lLAiAa3LgHZpTqNfp+lkfd8KwI0ePm/+dyyFFzOiND028kag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "060e18edae56e3d62b95c6e9c4e2a5e4e50979ff", "engines": {"node": ">=0.10.0"}, "gitHead": "054ad5737941bfba140b4bb20e7d44a5b8640f03", "scripts": {"test": "eslint lib/**/*.js test/**/*.js && mocha --require should --reporter spec --check-leaks", "pretest": "npm install"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "4.3.2", "dependencies": {"methods": "1.x", "superagent": "^2.0.0"}, "devDependencies": {"mocha": "~2.5.3", "eslint": "^2.8.0", "should": "~10.0.0", "express": "~4.14.0", "body-parser": "~1.15.0", "cookie-parser": "~1.4.1", "eslint-config-airbnb": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest-2.0.0.tgz_1469802716322_0.21347057516686618", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.1": {"name": "supertest", "version": "2.0.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@2.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "a058081d788f1515d4700d7502881e6b759e44cd", "tarball": "https://registry.npmjs.org/supertest/-/supertest-2.0.1.tgz", "integrity": "sha512-ay8SLm8FsxVk1MjBhSkwPaJimEyyiPS/i97NGP/OEDD5xXZykV8hleIaxomhCXQgJEjLohuZ7g0dZqk92/3bKA==", "signatures": [{"sig": "MEYCIQC1UCkfMOdwboNhvtKdRN/kQA4Wh4m2AGiSpNyx3HFDEQIhANq39z35m3jMJEd8MjrGaon1+ZYKXD3vH1u8RoUxC2Ny", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a058081d788f1515d4700d7502881e6b759e44cd", "engines": {"node": ">=4.0.0"}, "gitHead": "5930d2cee1191de782d4e0b50629c5debe1b0aab", "scripts": {"test": "eslint lib/**/*.js test/**/*.js && mocha --require should --reporter spec --check-leaks", "pretest": "npm install"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "4.4.7", "dependencies": {"methods": "1.x", "superagent": "^2.0.0"}, "devDependencies": {"mocha": "~3.1.2", "eslint": "^3.8.1", "should": "~11.1.1", "express": "~4.14.0", "body-parser": "~1.15.0", "cookie-parser": "~1.4.1", "eslint-plugin-react": "6.4.1", "eslint-config-airbnb": "^12.0.0", "eslint-plugin-import": "1.16.0", "eslint-plugin-jsx-a11y": "2.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/supertest-2.0.1.tgz_1476891321450_0.4725050034467131", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "supertest", "version": "3.0.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@3.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "8d4bb68fd1830ee07033b1c5a5a9a4021c965296", "tarball": "https://registry.npmjs.org/supertest/-/supertest-3.0.0.tgz", "integrity": "sha512-qpiarzDVqh36Q5SfYoCr57Z1HukIUI0WUaXRef9vNj8FrgB/diGLILtxOTh/B8ye2bDCHexMtSwxugL9fI7O4A==", "signatures": [{"sig": "MEYCIQCBtHe2NTrOVGzcr5yzgUc2vL+yAFuJDF1hjlskvJwcywIhAN8Oy7j00SWV7UpIDa8HKHpRzQjwjlNDelQ1HuOH+hwj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8d4bb68fd1830ee07033b1c5a5a9a4021c965296", "engines": {"node": ">=4.0.0"}, "gitHead": "199506d8dbfe0bb1434fc07c38cdcd1ab4c7c926", "scripts": {"test": "eslint lib/**/*.js test/**/*.js && mocha --require should --reporter spec --check-leaks", "pretest": "npm install"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "4.4.7", "dependencies": {"methods": "~1.1.2", "superagent": "^3.0.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "^3.14.1", "should": "~11.2.0", "express": "~4.14.0", "body-parser": "~1.16.0", "cookie-parser": "~1.4.1", "eslint-plugin-react": "6.4.1", "eslint-config-airbnb": "^12.0.0", "eslint-plugin-import": "1.16.0", "eslint-plugin-jsx-a11y": "2.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/supertest-3.0.0.tgz_1485735764561_0.7902017892338336", "host": "packages-18-east.internal.npmjs.com"}}, "3.1.0": {"name": "supertest", "version": "3.1.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@3.1.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "f9ebaf488e60f2176021ec580bdd23ad269e7bc6", "tarball": "https://registry.npmjs.org/supertest/-/supertest-3.1.0.tgz", "fileCount": 13, "integrity": "sha512-O44AMnmJqx294uJQjfUmEyYOg7d9mylNFsMw/Wkz4evKd1njyPrtCN+U6ZIC7sKtfEVQhfTqFFijlXx8KP/Czw==", "signatures": [{"sig": "MEUCIQDJrOslcqXo0ZeROVb9l3y9ohqkwYoszYj8rr9UbNfxGAIgVg8MEyzuguAI0vB3CFPO4Xf9BIGsyvjbTaTePK2yGpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+Dr1CRA9TVsSAnZWagAACyUQAIrN/xYTWKYrfwB1qs8H\nVGullx1SAGWcnGJCDxDKcGaX0c85nqEpx+RheMcYkH6KqdxPZvkiQzBgrDJg\nQlNaJ+sw+OD6VsVvCzT6/JRMfmQhY/h16ShcmhLoBLue6ZB8Yql5ySdRMWOr\nLY+b3FeVGni/ryyeDX7wcaioMuMCGMHso9DH+eXET81DmpxR9nL0klyD8yf3\nOtD9XXyQ5/esyfB6GBinTUKsAO2uXCEMpQmxGOBRHSziLOP3hwhJYgh6XeER\ntTd508KMT5cy0TlGE8KCTrKHNpcCkDAvfFaa16wE9t7skW84pXX/kfi3sa94\nL6QzYBrN4HvDTUhoytqmbuUiSB0kHbgyB3tgaU3uZAaiSMEeG6/CY2GtwX50\nbgLb/5cREYZKRJwlUhR/EDc6gzFn5QMsG4lhhLp4/hjiZBsQnlk4MmYzNjHX\nc7vCcCqy+3pu9NA3cH4By1TOos/9ONddW0Kk0phUBnQTJ155+tvD9L7QRos6\ntaZp6xgyEVW+xSBvNcS2MQQYjTGaS0jRsjFy63I6zqxnI5U695nSJgSyZ8gv\n/TKT7wCvY7rdZHxrlmMFh/3W0M0+gccCeL24111rAVj/fimlQW5hgganECwE\n6J/5ryC97le7QIWf+ASmSO1rsPe3OCXaByphl1v3RbUnE4OyNzgmGjBD2d2g\n2HrU\r\n=sD3G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=4.0.0"}, "gitHead": "d4a63af138d23da0e2f3deae6be9356893a8190f", "scripts": {"test": "eslint lib/**/*.js test/**/*.js && mocha --require should --reporter spec --check-leaks", "pretest": "npm install"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"methods": "~1.1.2", "superagent": "3.8.2"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^9.1.0", "mocha": "~3.2.0", "eslint": "^3.14.1", "should": "~11.2.0", "express": "~4.14.0", "body-parser": "~1.16.0", "cookie-parser": "~1.4.1", "eslint-plugin-react": "6.4.1", "eslint-config-airbnb": "^12.0.0", "eslint-plugin-import": "1.16.0", "eslint-plugin-jsx-a11y": "2.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_3.1.0_1526217460409_0.6140213614586143", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "supertest", "version": "3.2.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@3.2.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "9addd40096135f4fb8f032dd7c3fe3789604aa7a", "tarball": "https://registry.npmjs.org/supertest/-/supertest-3.2.0.tgz", "fileCount": 13, "integrity": "sha512-f+ANFG17GuXbMAQRveLLtqmodbKBxqfMwAktPRS0w4R7m+OluM0Nl+Ygr/tZ/0u9IbdwqtBoHspam3A17ThTig==", "signatures": [{"sig": "MEYCIQDzJkdb1Jvl2bBD7dw0RliAPQANqGSO3vsP9YGRjzidowIhAKkZRAr/OGy5+tTunSbKAP/G8aTiE/+ncvU+28m5L5pf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbj8A0CRA9TVsSAnZWagAAXg8P/2PJkTecwPk+QtVrZoBA\notzk/1N839xkszpz5uREJqIZsxDul5MYBbF9a69wSw+gGYpjBX1GGU6LiBPB\neHegsY5kgl3DYvSYkv3PYwdXVqVENYKDfXROmjmA5q2A2BVPBkIxAL6FJDmj\nvj8Lh+hmO/Ikl8Ogn1hP7Q/mv7HXmU+w2y9rMGdqvp4CTCiI2LgZv2agjVGh\nXD+bA3HXwDtaob1PtBhFZ1u5lASrE1NYIn7R+8tKAD7fQjbnntdcW53B5TI0\n9zXcQH9W5vp+dN1H6LQU7VvvN5eF8/0Jg7WgT3W1qtaGMeFzeDHlbIWVhuoa\ndvbtR0QzlIOP0O64M+OiHkZ7OdAUx7lI/IfE8rox21GYQgIbPibzt/A96ysC\nDeoN0ddhZvuAUz7iesKO4AjBW/1t+Tzids8SDJIRe6vZFH86IuJy9CCeWK1u\nTQnKyz8gP7vU0U1fEbMhN7oMGgn6dW3i7agL9VkBxNoP60r0niXiGK0YLz8v\nUD+lvYexlSPps4BdTx7lq0cQslCRyUtctHZV3NAJ9E2fG7xopPHeuSoijQ1u\nGeQVE5WJPeHc/U59T2UDF1/PgE8wlL2/d0BAHS+oGzUKenntQm49QgpaxIZ5\nzgW5HDb9LT3/HE08HMlGimUvP/KzhBwTLP/DckqNwApD0EIQcIexFAVKlzjn\ngJwp\r\n=FvKT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "c52338b0f19a5faf0db0e88bce18d847aaa55624", "scripts": {"test": "eslint lib/**/*.js test/**/*.js index.js && nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm install", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"methods": "^1.1.2", "superagent": "^3.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "nock": "^9.6.1", "mocha": "^5.2.0", "eslint": "^5.5.0", "should": "^13.2.3", "express": "^4.16.3", "coveralls": "^3.0.2", "body-parser": "^1.18.3", "cookie-parser": "^1.4.3", "eslint-plugin-import": "^2.14.0", "eslint-config-airbnb-base": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_3.2.0_1536147508025_0.6964650727438442", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "supertest", "version": "3.3.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@3.3.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "79b27bd7d34392974ab33a31fa51a3e23385987e", "tarball": "https://registry.npmjs.org/supertest/-/supertest-3.3.0.tgz", "fileCount": 14, "integrity": "sha512-dMQSzYdaZRSANH5LL8kX3UpgK9G1LRh/jnggs/TI0W2Sz7rkMx9Y48uia3K9NgcaWEV28tYkBnXE4tiFC77ygQ==", "signatures": [{"sig": "MEUCIQDtmwFDSG51mknvsUSGIBL5jYv/I523S76p61hAYzCtzgIgP0rGuhpa4B3yBgRUy1NzXyHez6m3BX8VOG6XEpprtBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkY1ECRA9TVsSAnZWagAA474QAISwM1aqI+nEk60VYwL1\nEJEon6PcSWgtLQ7atLVrdOwy9i7IbqwXRhrLGuZhdRbPSm4rNWHCuqOrL3eJ\n0jbhfXTfyzTZOvUtAV1My2Evby8Mfmv7NnToz5tZygXYI188Al0dMLXmM4JF\nqr+aXBUd0G4DltQo/MZ5FA7utEqXeP6bbVK/9pF1tjotrZ0xEyfIr/6Rq8nx\nTwM2uNtQn4MzWXfSaxEuFH2zwcv8K0kXnQR1+uMwHsJYuO3pz34X4eFw4Non\np4F7Gm7Ch+PSNHT7lNr0+pY2KE24Na5gaAiyi0kNcN1KSzDaCQ24yVJdnE1i\nUWAS+Kbc70tjkktHRuNAySAnKmQJKNjI7+0AD3JuPTGS/txQ517kGD5ytQRs\n1PteJJIwHhFwFJpTkhpwuTIuR+LLd2ulRazMzmiFHlCGATn+AmPSYjp0fOqP\nnGNa+THBeXXMgD+EAgdy10z9n/M+jn5gDuG0J67YWMja5if0uPIhbHCXas9a\np3EApPZfl2JM6B0f2Sa+fdHwtG6eDuxYmClXeE6g/p7spTElx1E88ZNtcHr1\nOjykdcCqpv44t671DXigU3/DgmmIFYTlx4UV8exj3G3Inuo9Y9Su6N5ZSIth\n62K+y8/9qC4wIhSeE8PvwsPXe+W3JLOdGRoBC7MB3aV4Au/zSWtLJh71nGMk\nTb7n\r\n=/Yzh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "e910e851fdd54e3e382dd3aae21623cacad69dd9", "scripts": {"test": "eslint lib/**/*.js test/**/*.js index.js && nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm install", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "mikel<PERSON>", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"methods": "^1.1.2", "superagent": "^3.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "nock": "^9.6.1", "mocha": "^5.2.0", "eslint": "^5.5.0", "should": "^13.2.3", "express": "^4.16.3", "coveralls": "^3.0.2", "body-parser": "^1.18.3", "cookie-parser": "^1.4.3", "eslint-plugin-import": "^2.14.0", "eslint-config-airbnb-base": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_3.3.0_1536265540077_0.77574297467526", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "supertest", "version": "3.4.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@3.4.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "4f1d9db50ccdda23382fde5376f3f34ea8eb90f9", "tarball": "https://registry.npmjs.org/supertest/-/supertest-3.4.0.tgz", "fileCount": 14, "integrity": "sha512-X3RKdIE4asSnJ+xi81tFne4BlYTOEq6W+6vKwl8zCMWQ15mQh+e/BGNF2duEjLXSsKP+VChyh6WSp+444Do6Gg==", "signatures": [{"sig": "MEQCIGb5NRkW9DRUS3K3wssjxVk5O3qk+mRWPrYQudOook99AiB4jo47clXODz9GptaDSKm86ftpmz+d7VLyF610ue5sxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPuzjCRA9TVsSAnZWagAANx0P/3Iw9jd4pinHkIjou5RC\nJgMJeYxiL9TOcRhFgCVcyfAg2xuMoSnbjNaU+aYAR8JvLo5RT5mO970rRKIM\ne2wBJ4le1zy09/KQyImcWEFlYlg39z22w/uNgrovuhTdJsaGSQuQpf7ytLxo\n+PiJCmitun+pPDePUImX56tovr9VV8NyYyj37EzhFLTXLReRHXpso/RMuXZd\nuxx/x7g0z2D2hA1b1NO8HBhRnNrw3DJLohtt5uP7DMHkl2gUDleRCGsjyOoA\nKs1HYvT27S75k8EPbCSHct3okeaJnaQyAuLdYH7TgwfsEnOMxmV8y1wOgEnM\nYstcl0vhvkIvOjZCgvq+5dpxevUfLrXspPqtZkVJRnPjKPNAdo3kjCHS3Jbi\n8UAxGyZVMV65OaYumy4suGZAXXLvkcO3rSfxq39VXzGgojNn17/oA+RVYip6\nN3DODFSd+a8s1Nue/I1YFuvbgWzEEc/sWGo2TMRjn5ezywgsaQoRT/+iLQq/\nxADkn88qtO1pyevz3viQgmYuWvnIifJr2MJpIu+2hTnh9ztf1Q/J76l1Njw2\nNBxBr80dUgsiLeVrtN7An+rceMzsOM3RmXldMtfThY1wbIPE6wW8FqGjJ/vS\nVFk2rjINpqC/5N97ercuIiOdcuIfV225AlgLZJyX5w6h7r4+LOEHCkPEJkrB\nXesC\r\n=UzgL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "5640ac914f17805d9614d3445d97419fe3c2b5b1", "scripts": {"test": "eslint lib/**/*.js test/**/*.js index.js && nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm install", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"methods": "^1.1.2", "superagent": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "express": "^4.16.4", "coveralls": "^3.0.2", "body-parser": "^1.18.3", "cookie-parser": "^1.4.3", "eslint-plugin-import": "^2.14.0", "eslint-config-airbnb-base": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_3.4.0_1547627746409_0.01655663715418454", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "supertest", "version": "3.4.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@3.4.1", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "91b19ea34804c205769d0ff5ad947aff3eb2731d", "tarball": "https://registry.npmjs.org/supertest/-/supertest-3.4.1.tgz", "fileCount": 28, "integrity": "sha512-r4AmsjjKxC50LxGACe/E4xKjau2amiFlj3aCT2sZCRig2o3l4XFN6Acw7crDu4d8Af1f5chafIyLkQ1mac/boA==", "signatures": [{"sig": "MEUCIQD59v86u+H62VwFG4GN+8DNLqWqWQF/yrHVGUOCBpOrgQIgYxMbdbaFNAICLEyPz6nkdXx8IrN3lhvFeFjBGU3U1ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPyMnCRA9TVsSAnZWagAA6boP+gIxnaQWkg5XLlwVMXs8\n6Q2DcXTy4rto1bGdMss48o/bWqoXZWhWms6Wbr6+4aWCzoj/teNWin520FYp\nkrLEV5IOBb21t8uUp9cXvxxg6GFrzWwkgp/qSGLuezl8WsAQiVqpE2HXqoMN\nbMqUmdoQaGByZqzYthuhLK+UscgUOXSd1TPfMbsW8PoNtPDVFzI+muteoeyY\ngCdcKASBMXAbY00xHM9CdzFRAuR5D7gxRuIodZuk0j+ZnyNZhdzCF12bJkkd\nAFnFle4DTJxwXGeWvrNjZJk18omNZiAquP4JUN6VcQaPBcJ6melsQGkX5ZFf\n4NcvSgQqYjy9H2FERIIMRyrk9S2W1jGI0+zkTN8sj6VDvkNHeQ8S8M9q1xdV\nSgDg0sEYadNo5eSDaFf07f9mINT2MpT3n6okCX+gKGmpig4U0Fdq+m7QmZfJ\nNfhBrdwLv+9rRH/JB3v/w1qfr5odA6F23K5Ydti8eGvlDDkgghsdpMKRXwqS\nmOUWz5EF3JkUIFUq4je4qbuG5iDoBmzYEnTVIidKg69tUnVuXDEZO1nbxd4L\nbqgtQ48c+MMWHNWU3yNfxb4fMU66ngO2MdV2dgtdzTeNrOwx6JqXFO0l1cLX\nTBvA4tzc/IjuYUYt2UBVWNl1ORIP1XOOFpelGw/kOTFpmHb7wBpzgcS4RQ/j\nNT7h\r\n=cxkQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "8ef384015c0509bcf6e39ea7407a474a3ea84b72", "scripts": {"test": "eslint lib/**/*.js test/**/*.js index.js && nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm install", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"methods": "^1.1.2", "superagent": "^3.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "nock": "^10.0.6", "mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "express": "^4.16.4", "coveralls": "^3.0.2", "body-parser": "^1.18.3", "cookie-parser": "^1.4.3", "eslint-plugin-import": "^2.14.0", "eslint-config-airbnb-base": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_3.4.1_1547641638857_0.9735961344470148", "host": "s3://npm-registry-packages"}}, "3.4.2": {"name": "supertest", "version": "3.4.2", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@3.4.2", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "bad7de2e43d60d27c8caeb8ab34a67c8a5f71aad", "tarball": "https://registry.npmjs.org/supertest/-/supertest-3.4.2.tgz", "fileCount": 7, "integrity": "sha512-WZWbwceHUo2P36RoEIdXvmqfs47idNNZjCuJOqDz6rvtkk8ym56aU5oglORCpPeXGxT7l9rkJ41+O1lffQXYSA==", "signatures": [{"sig": "MEUCIQCklWupuVB/YkB0R+XnxK8sX1D5ks2xmY0u18oIqbuL5QIgbx5n65JOH4jzyMbKVgsV4YsM8y0euTjDVwJ3JoMmo0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRs8DCRA9TVsSAnZWagAAYjkP/iM2SL0Bctr7wWcnI+l+\nhKluVD5cBS8o7MD121PZldPamOOt3tSulA76g7g+dZ/rF3d0bxeeboBuEp43\njb9zgUCOIak8Eo51YgwI3GlmIUXk4ZK5TOKN6Dy1VbODo6JZyB6c4M97AdtZ\ne7R7tteSvURNMB8A1408bM5OBpYrpydOd1fzJmeAT/td722XuLH4lClBvt4b\niVKsqkeHd7Y9xQITMhqPc/hAzl2N7R4VE/K0HHpNP0PRa5983Rih6pHjzTXb\nO9XpYWPeMa3eewVFrQuDuo6uY4KXYWcai0n2BIWK53rTXJLENOuRuRBPQH+6\ndhX/fGC28ghQU1vzur1VF20UKOvmM+2lTVreieL29sXZ9ErYxrvIOkh8EFZT\nHEuERxJxvo5NEB2tsEvRqhvf7IpQuDhzTpIoYECNfPCzER80ALGn/3NTTQcg\nT7OB5T+JIcT8UuLI8JMnZo8LhmVZ1xGlQOl4b61tkvCU12z0l1RER8D7ymf+\n+oLLJzfdqx4t6xcb4dY19WaExVmypcrnxva4OHzS9jYh5pGtICOKqEuAYcM1\nEqSBoFBwYXSKHbN+BaL+1WL8MohDgCANo1cvZUw4VIXnQI3kVihVRmw3VydS\n6YtdFEZpPmZO9YAOpN+DH0RtxmPw0vWn0kaaV05ZeSJ9nLp9qQr51mXkp2r2\nbmUy\r\n=XZTG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "1792d7d91e276362c48c09fee1781f246a21605b", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"methods": "^1.1.2", "superagent": "^3.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "nock": "^10.0.6", "mocha": "^5.2.0", "eslint": "^5.12.1", "should": "^13.2.3", "express": "^4.16.4", "coveralls": "^3.0.2", "body-parser": "^1.18.3", "cookie-parser": "^1.4.3", "eslint-plugin-import": "^2.14.0", "eslint-config-airbnb-base": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_3.4.2_1548144386858_0.8678949886949878", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "supertest", "version": "4.0.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@4.0.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "1fda3a5faf331b20ad5fb836ddf789e80b365db6", "tarball": "https://registry.npmjs.org/supertest/-/supertest-4.0.0.tgz", "fileCount": 6, "integrity": "sha512-e3UaSMkmVaPQ10MHqb5T9G1mb3L6MVUtLTxkVhKWndHfXyZylwtkjWXD9FzyHqlZ1/EPfeTJ+QDtmax5fb02cw==", "signatures": [{"sig": "MEUCIGdgwz1SffBopabHsN8G/JB8NSp4D/jju9f3gAtH+5YfAiEA8oaAjtShLFziO8jkEakI815J7/bWrV7RBL5S8P1liHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcg6ADCRA9TVsSAnZWagAA86IP/izSzKlfGs6eAyPMVFTf\nbezbizc3Yhua3d5b9aJIH6TN3WIhYvaiwXhAbavxw1oF4wTDKg5SLGirbjQw\nOosk7rXQpay7fmjyVD+8C5dG/2ZH3flIlQK4oZdQeQii0i+T8SbIH8G+abHu\nBqkDrmumlVXZZtKU/pfLBX5BE4EBJJXEoNI+fBxKrpXGlxkQeJOCkaCkvRZ8\nJrIRa762RzvCO6cN3EFvJ0RupsY1X6kjqdUbQYwWfyu6FID1yxZgnBW/u6/N\nRosmPX8eFi4NcrWiWuxNBPBZz7U49TvnUn+IT0sb56l5fuAey0udbM5pujP0\nxqCm/W/ts6FtvN3IR7FT3BCfC+vpyv2nTlIrmOpuw7o1IP7QYeF4KeSDf824\nyZ5a/EK8WR5wBB5VOGrd8aa+fzmZcu+5X8Ez94R0OdN8YrYJrrSTFANf11NW\ncDD6wYIKBxNFrXz6dyIVl9uMd3GLvq4y/xwGhXCnmpHUIFo/4YtMSzpKXCl6\n7+Ehv3tNQt9RH2u0ZLhEsH0R4m2lylsu3/HB/PazrtXy0HhYbCkEcW0uAYyH\nIuFsNR8f8KigzEGiTy0HgoCerA9LMAFuW13j3DKILoVpu3+5lukKhyVtD+MG\nmM2RPdLJ6/oGEJ06q+497r6D38Ud8l6FNvp906cxqYL15ZMfcQzm4uCRWavL\nHROl\r\n=KUX4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "0cfc1983e3ffcbaf5360d0c29e9b6c4e0942a26b", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"methods": "^1.1.2", "superagent": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "nock": "^10.0.6", "mocha": "^6.0.2", "eslint": "^5.15.1", "should": "^13.2.3", "express": "^4.16.4", "coveralls": "^3.0.3", "body-parser": "^1.18.3", "cookie-parser": "^1.4.4", "eslint-plugin-import": "^2.16.0", "eslint-config-airbnb-base": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_4.0.0_1552130051005_0.5413790336632205", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "supertest", "version": "4.0.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@4.0.1", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "3d8a245559a9fcd60e5ec91092e42adec4d9ccc1", "tarball": "https://registry.npmjs.org/supertest/-/supertest-4.0.1.tgz", "fileCount": 6, "integrity": "sha512-IHZIfJeRuSDKsB2Rz/alsaP4leAO+ZJJGS7/PLE8EXDo6voU9vzosF4vzlD+orEXscHJ4xCyRNNzoRjap2BvGA==", "signatures": [{"sig": "MEYCIQDLn1NdtBvMiCqDcI9lO/2SCsNduQsozyVz3XvB/L+vkQIhANdRCOBY2TTmAXpQs7gliUl67cbqF9HTJ70aNFT4ULUx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJci56OCRA9TVsSAnZWagAA7TAP/jXIwT40mx1ocG7UMSXE\nDmPnrK960QPvANu4HCmOWRRwe9ePIP+2txDNKan1ov9Ir3f1L1S9fPmnjvNS\nIKF7e5CW3EVto2h5B6go6Y5IA9lsDI+ODtcmkUIN6yCJvxZ+SvhILWLGLZ2m\nwV19UlTrX6QkqF5dWiXc8xMyx6bct+7BMyR6ntIFzLBQcVNmllkooTdkmzX4\nOCWe6mXYsZsVwMKh4yXLyr2LWo0mpGHvxXka1hhmxcD7Lvil04xqlQj/d3OI\nUx4WJQTOWD06qmKtIdOFnXX8L66cpjymbndhGUjfuvmbV51nTPGh4zYh+FKi\nPFevPjagyw8sXLwE2axnQku1cDLI8VdWp9e7UR/UNKVyresGRvjamdJv8oVH\neqWn9+9tyPipH520u/WHt1Kw0CHsAFprmprJEPBXnxmS96s3JW/YGMIE2hB2\nA1+vA8boI6VI00KcJTuENq7mLEbALraKhFH+q/8SMH0kCFvQ+0qKUwa4EaTb\n8MfcHxHf1t74IWKfJEcS6i2Qr85TQ4ND+9q9iVCRlPiKZ14Lbx5ZEVut3N4e\nde26WCTaV0OidHHNuu7UaTvggzkD8iQOGD+zr+xmzj+0toBumMtvR25kTxTC\n9e5EqNeuHqMJ/LiPWH+p9uW4BfQA2dLRm/qet5hUixBeKeFN7wkL3nftgDoY\nyrQx\r\n=l9C4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "715d9a9128ff1c1e5d674637341b49d5f2da2e20", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"methods": "^1.1.2", "superagent": "^3.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "nock": "^10.0.6", "mocha": "^6.0.2", "eslint": "^5.15.1", "should": "^13.2.3", "express": "^4.16.4", "coveralls": "^3.0.3", "body-parser": "^1.18.3", "cookie-parser": "^1.4.4", "eslint-plugin-import": "^2.16.0", "eslint-config-airbnb-base": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_4.0.1_1552653965309_0.22477872348035777", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "supertest", "version": "4.0.2", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@4.0.2", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "c2234dbdd6dc79b6f15b99c8d6577b90e4ce3f36", "tarball": "https://registry.npmjs.org/supertest/-/supertest-4.0.2.tgz", "fileCount": 6, "integrity": "sha512-1BAbvrOZsGA3YTCWqbmh14L0YEq0EGICX/nBnfkfVJn7SrxQV1I3pMYjSzG9y/7ZU2V9dWqyqk2POwxlb09duQ==", "signatures": [{"sig": "MEQCIDKw7ky7TbG+jtXDC8KFMoqvWHT/PBxYmgfQR0McG1M+AiBAP/6kTiGcpZZMzYbFP5TWDKIYrfl+jrQAmxqNE6IIog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJci6LVCRA9TVsSAnZWagAAbccP/R6uTWibT22RkFY92KP2\nuJouUEcNmPCXGIbKdmH/l4WnzGUDf1iaRIUpv5aBisUFawRU37PDKdNKcPle\n7Ko/F+aDVA5CN+YfjmVivgXh9FFRmrhZsBsUhRCkcumPbNr9gA8k9SvW0VL7\nehm2ugtHhts7+kmD/kHyXdaL5kQKCRdBrSzPBXXKBMYrThozO0tahl4dOs0m\nliK3D1lnNNgH5YDJ8m892M6G1QdI9/yXaH/XZ3xIJp3ubNShwURqZVqdyX4E\nEkm16ZFwgR3MVD3o9JjUVvGTryFbT32ohHb/FE/5I24CisMgtFPBAJoM88rH\nFJPZh7dz2OCsZ4PutLyfcMjvyuUYAI0qq0Pjr856dU9fQM6ygdNjW3IQMMX8\nhXy8TdUp/eMzLXgvRwCETbFK0v1+viZE2Zj4uSC4RBgBeTFgmRTl+wiss5yj\nSnWIGWjH1Jj5Txa/IPlYRlp7moZ4+jV1C1JvneJFgHnXDPGbpdvbkuvr6dzZ\nm5QDPz4W8k/baYbzG3uzF5Q0UvMl/21jJExeYSbVGxMmeMZkqAbji+T9fjYC\nBdI9irshZAiw08Kr9GByPKDpnBnHOOofuOFMH/SoRb0bTgfQPEXnWtw0dqxP\nYI09eLIA95KPjw6UTpcbKZHTjAfdpAUqtmaa3h/R4Prlos94nYZU+yUZEw7h\ntoyg\r\n=PQUM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "775911f25d78eee03b792077eb6f278ad98fce97", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"methods": "^1.1.2", "superagent": "^3.8.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.3.0", "nock": "^10.0.6", "mocha": "^6.0.2", "eslint": "^5.15.1", "should": "^13.2.3", "express": "^4.16.4", "coveralls": "^3.0.3", "body-parser": "^1.18.3", "cookie-parser": "^1.4.4", "eslint-plugin-import": "^2.16.0", "eslint-config-airbnb-base": "^13.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_4.0.2_1552655060802_0.12136249730579518", "host": "s3://npm-registry-packages"}}, "5.0.0-0": {"name": "supertest", "version": "5.0.0-0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@5.0.0-0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "7e6febb93d7b7c3eafd94dafc2b4ff2ee4cb9c99", "tarball": "https://registry.npmjs.org/supertest/-/supertest-5.0.0-0.tgz", "fileCount": 6, "integrity": "sha512-+XblQKVMblt7kf4BRtK1vezM+Xxq+CWlksy4kmLyqDsN1y89YrDIJ0j/H2CGLMMNk+8k1/bCcGUw8XLs1NYxpg==", "signatures": [{"sig": "MEYCIQChfmkrRPM+10EqdwB46+6gjj9XenDAYedOi/ii0S9RkQIhAPU3+78i4Kq18KUq2D+g0fGwRtAP5n3OW3TPvHpUxoQN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlIEQCRA9TVsSAnZWagAAWH8P/RB9XGChk6jTpQEBW7un\nShCrNJRvq+o8aJSEaHOiqH1X94Ys67qz6MhrvrL39BzykTbMkt8tfi6HvJ6/\nhGHzX3DrytXjvlIuGlFYqZAJGg0Frl3WNNwN2lzAUpQMZ2oSJYnZqyt74RUF\nEkDw9BFWPLPNZnKfXSy3fuh/mkPUxds1JCa4TjljEdVyKLN0a1clgwveYzY7\n+flHqUhAARgeBr0jQ22kal9gqMCYEMCsiwcIXXt4fojyuOqShfTIIjc+5pZq\nZvIdQ2yaMMDd+0g6xq3kumyj5ZU2QNVYgYZP+L09W/sJ4L8dJfhwd9EaSmoe\n/xexQ1a1q3gsARuXOf64mCi/Yin5an9/w1eNIsG4dUsm7CGhIjQRI95kP99c\nUUbuBHJYsrWvjZ2GT1c2j+eZoqBIDob1qMadVsyMNWX9K2WWB8XIckzuuMRN\nRx1vwZu2rKJbZ2iSlvQ/TrCYJcVz86+XZ3IMKO9QPE5ADIp8IwcOC4ep58gI\nooQPUUJqknrMViQwLoiu75JWlBa02EgTXgVMQA3+nOPdBjSyabM3O0pHre1C\nCLpskQtokPifBXzFr5hy8W1XXTqkyn6W3ADG7uuJ1UOoYFe/QrW+PVOJuo3n\nM6a7635MquXqYNa7S8N2wNVhOpSsX9ZRVVcN6cAXGZJab1XD0/WDMQx+j+h3\n1Hoi\r\n=/fkx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "910a11133b94d44f8ac380a8a46bfdab2bce9f19", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"methods": "1.1.2", "superagent": "5.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "14.1.1", "nock": "11.3.5", "mocha": "6.2.1", "eslint": "6.5.1", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.0.6", "body-parser": "1.19.0", "cookie-parser": "1.4.4", "eslint-plugin-import": "2.18.2", "eslint-config-airbnb-base": "14.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_5.0.0-0_1570013455672_0.19893684647827703", "host": "s3://npm-registry-packages"}}, "5.0.0-1": {"name": "supertest", "version": "5.0.0-1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@5.0.0-1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "3ccbabecbf53d91bb01707376e8929e11aa291c3", "tarball": "https://registry.npmjs.org/supertest/-/supertest-5.0.0-1.tgz", "fileCount": 6, "integrity": "sha512-AoLNf/RsBnGn/rghQbrTyKXOSdm0HWlSxR4d9Cg2ip5K0Bn6W9gPC/yfQjDcWU5motYCNPoJBmYYCzVhHOWFGA==", "signatures": [{"sig": "MEUCIQCy8zs7tiNprJPG7QZEFhBjDCkqQ9f12ZlrrZhQSNW72gIgZm/23aCu0aY6BrfNxeeuBgTEDTSvlriSNUvfc5W4sLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbeYdCRA9TVsSAnZWagAAfTwP/3H/Mz4r/D7XD9wVrmji\n1KifFxSECMUPUJ9ExZzCPgzzuUz5VFLp2DMQv61ZWZ2ZmLG8dWeNvHUuDXKL\nr4Jb4AK6SRaBCGEZBfqBy+hDC1Xsv8T2XjqzbYuTBZu+uNKbG3y4tFJpC26h\nE0PpyfqDikWYkvnHrCqEiPAsjS42KJmvr085RlP+Gxxj9XHDOqkjEbJjp1SQ\n5Ke5+2ooCfzyfXzk7dF6FCGaQZ+ZCgNaVu2Sh6Eqq8VUYZ9DWyQEHYOqavGZ\nXLQuJL8a3BoKTf1XbBEEkZwAyX5wuryT785U09mlLiuZXanb/GsTSgi5xzO8\nTxFzYf7vqOdebmmGmPgolC9HdmfRO/HwoLo5fq6i55SpxLMwWkaAn2gtPAcO\nMHJZLxW166HAplmM21y5B6twOO2NSiDzPYTCw6r+eJ8HCT67kihqZqzOPe8/\nXUOw380i1bvFMBDpUaA1ZLsTfL9jOZ5NljCs/rbcYTTxto7aX9gyM4TZmvAe\nGdrR2MvnUm4KGG4hySUIOYKukfDKKEWFXR1Mmcbokw+03zQ16ZKD+SJq/Uqs\nUgjLFFY+ahYrAH3XsWINTCffSOK107dkoqE0WqxCcz+zzuakqXFdTCnyB56f\nZc4MhM7z1gkmHwnnbERQPuUFlzhfPBsLQckyjK67jnermbkON2WN3kwv8HKp\n03fb\r\n=zXtZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "44eb96ab53f5b5bdfcdafc400d77ca29fd738c95", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"methods": "1.1.2", "superagent": "6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.4", "mocha": "8.1.3", "eslint": "7.9.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.0", "eslint-config-airbnb-base": "14.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_5.0.0-1_1601037852817_0.00018540075142103873", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "supertest", "version": "5.0.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "771aedfeb0a95466cc5d100d5d11288736fd25da", "tarball": "https://registry.npmjs.org/supertest/-/supertest-5.0.0.tgz", "fileCount": 6, "integrity": "sha512-2JAWpPrUOZF4hHH5ZTCN2xjKXvJS3AEwPNXl0HUseHsfcXFvMy9kcsufIHCNAmQ5hlGCvgeAqaR5PBEouN3hlQ==", "signatures": [{"sig": "MEYCIQC7cD5vj+8fNJq4t4cebkrb6Ti23smBPWP9dBjOTsheQwIhAKnYENJTtY3sJ6O5hrisx2MILk7M5Q46fqlQ/poyosgV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbfCxCRA9TVsSAnZWagAAca8P/0DLlorgIPTggnSelOQs\n3KDVJWYt4uiUA2hthDG4dqncl1OfoNwD1hdQWXx04tx+sJgQr4sxQrqqS0JZ\nOXPDRpe+Ha2SZeMNSOVLDPlueViy6005C8zgf0G2NCg4GUJ3hlWFuMG9eARf\nElaQhyvY6tE/p95L5tRdF1C2VeIyG/GxlKAxOX1iUx4eo+oQ4t2BE9rxBKTo\ne4pV9GsXk6LYC08t+9wQHFesZcADTRsoLpqGr5gd9c5HLVsPf8aPTRcEh8Uy\nNEOKS5q0CX/4rhVKpf1XM49LL/vUXUOPOWlwkPLzR+pMPWJQwFchYqeKIwTK\nL45almYSbXYBxgqyCDF66eP+7odewm6JKJmphdRQHTEd7ZWwpqqyrA5rzqk0\n1G28TESlZ9rr9k3QQy16Bco/d7h/mYKeMF62fnl2oL4Kv+WSuT0U0XTRnjx4\nQleWlYJkNW2yYTzMxQ607HJz+YWhZR6xaYn8HSoXGbCht2O0PnbuUW7CbEaa\n248Dch3zfU1hIggmuLuYjHeyHZS4+dyHEBxECspKLqQ3CKkeGLi08RO9hoTB\nM3Dyz7PDPzPsj+NnMsO7wlp3iz7c/z0UA2/MtYPyB0ouP6TxuAORZry70Ala\nWIRTbFcpQyV3N6QNAWv1vYGOodvQvQ/kT1OHB1D2mNnz/4KIgA5YFyAIMda3\n+fED\r\n=sLFz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "8b1a1d878ba460f1ef9b433e377f1623e90915b6", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "rimiti", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"methods": "1.1.2", "superagent": "6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.4", "mocha": "8.1.3", "eslint": "7.9.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.0", "eslint-config-airbnb-base": "14.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_5.0.0_1601040560666_0.3289541323738612", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "supertest", "version": "6.0.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.0.0", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "8371509fadd616217b42ff3d3be7cc0267baea1b", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.0.0.tgz", "fileCount": 6, "integrity": "sha512-7+Skilm7kvUZIaKfALPgjS3i8zYs11zvEudAeYdqJZL3f+SGGFV4qQkkTVkYcs+zbE6de47HP8o0a0hy1BFlMA==", "signatures": [{"sig": "MEYCIQC5hRi84Sy2vutJ6DiJlV5Q/QMZbh/bXz3ixFhaM2VE6gIhAMY2G7Bo4HsgLIMvqeB+gvU5c0wZhrp6hYmaHRAQFDwM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmbtbCRA9TVsSAnZWagAA/hgP/ixnzfBZvFNdZhDVhuWg\nT/vSH7Pd2eVZSjoMjKZoQHMHalmvN0xgL7zMw9skzz1eDl98PkPVcBLq/489\noGsUKB0cf45n3plytA32rnn7uRb5/eabw9epWBlUi5+8CWaYia7nlazPuLRE\nAv1Vc2lqyR5W69YwMWmY/JQRdUdBp1mNnSY+fEqclDcX1TDnzD+ftzPmf+wP\n1SdiWFOAXG18eX5srFLsO34cGccYQvOZHcpRVKqiarZ5ZZEym/ojAEg2PeD7\nw+htf8Nns7ctHESekkvmGlsNP09RrahkZDFz0vDvRhWj3lUnIO69Kkcf3UB7\nwFEKrZ4cx4GJyu+gC/gOCqnoY8Qck6aNDQvWsuPiREbJxtfvvRjZDmqekqn2\nwlnek0pTJddR86CkCjPk3oIpSVa8ajmip8Y/89ppv4ozbZECW4+Mdj+y6cls\nn0zirWDzwfzRcTqKrJRLL0rWJjp8yiRRWRDXkOAatOyBkyaRrY9lTdgTRj2F\nt79PGngo2FJxDuy0zUWODkMIyALFfTliO4oI0v08vRF0abnvflidBkPhfWKK\n8oHvaf5Ga0teq0KsfhAHDkmTLpfybUkOcg8QHqBd6X3ue8TO+3pppaP414Ip\n0olqNOxJN9KO5lGR+BB0u8/NtPU3kwztIIXaNtd+LB7eDVA4jM8Zm+EJlEES\nSEvo\r\n=EmN/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "fced0be6bff92fc68475c0df3e5c9eddb18b784d", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"methods": "1.1.2", "superagent": "6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.4", "mocha": "8.1.3", "eslint": "7.9.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.0", "eslint-config-airbnb-base": "14.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.0.0_1603910490623_0.3374103579224963", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "supertest", "version": "6.0.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.0.1", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "f6b54370de85c45d6557192c8d7df604ca2c9e18", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.0.1.tgz", "fileCount": 6, "integrity": "sha512-8yDNdm+bbAN/jeDdXsRipbq9qMpVF7wRsbwLgsANHqdjPsCoecmlTuqEcLQMGpmojFBhxayZ0ckXmLXYq7e+0g==", "signatures": [{"sig": "MEQCIAxwPQFpQMo8IKwMCvFYyodYH40rluX1oMMsg2SeAxV/AiAh9Z9zWncSou8XQU6/R5Dnl/zgINn9uSL2DLSBhJnk1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJforsMCRA9TVsSAnZWagAAyKIQAIyesvjr/Uvnwgg5zI4L\noZNdB5DTpoKZig2CJH5knDn1OfM7cXbXRoFDcwYMn6QE7JC2pqp3a1/4VycA\nsHm3AzIGcPajEHZ1GTj8sySHH9uCAOxXhBZVoTRP0lHsYaJfypPi/G0FO9cv\nJ40C+RpdGXlRX2oNbjjbP9+Z7LccjaJO5Y5eW4m2o0pV4MdWAMc31yJPc+Qh\nrOUaiEMTraJpOO35AmYdbKlSenGFNpGNhWN6laBa0LIJd0BeSfkFcdF4P0oE\n/fNASofJzcGIsUYoAuJ9bpk9lkW6/2ZeQILZyFc8eh4DnDJBhTtAx8Xz6sJU\nZkrHOJMxhu1EBAKMBo6MtqSuAsZAwoSH6m97o26rbLRWD86LtgKKFLjDeCQI\nYnmEPMHv5vim0a8PMUXVmauWA5pL5Gb51gZpWhtZBjDrUb9mDrv81NcUP47A\nFisZt4fh6qhGUCWLo+/GEeoY4vz8UsDZU50pBbotgtpnZZWD+gGXMjEdboTp\nuAoL96CMRhTkAvj5W4x1i2YG9tJgQJOTjpsN4EeqnMtppVfBYa6Sq1uSo0E/\nGSnac9SxrrwoM8WMksqercDAcUN3s6xGAf3dvHlXi0qXjvMXYw/cdFHSnaMd\ng3T3tNq+G/J59Q43MhoQRqGcN0YGuneWT2O3yYDMYDTo+RYGsjdlMHvUexoX\n0jj+\r\n=tps5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "30b721056e59f7ed8b580d6275804662b5fc201d", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"methods": "1.1.2", "superagent": "6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.4", "mocha": "8.1.3", "eslint": "7.9.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.0", "eslint-config-airbnb-base": "14.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.0.1_1604500228155_0.17826707889092552", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "supertest", "version": "6.1.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.1.0", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "680a91ac5d58984965c014eccbdc9176fabaa47f", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.1.0.tgz", "fileCount": 6, "integrity": "sha512-3l1F78crTryOfgEipLeBljKcIf1JTgs/Rzn9TmxDLCXiFyBq1rELCTyDMOJLc4od3Y1Wpre4TXiSO4p1LpS6lA==", "signatures": [{"sig": "MEYCIQDYnEV//GF0t6rr6hcjphcIMYaEtQZa6viXDl/+PlBBBwIhAMXtA1GsLVF4yqCBzkyGXma7TRRuPeU6F8kVJIvfgBui", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAa6WCRA9TVsSAnZWagAATBwQAJSbcA62gOwGLhw0NdGQ\nU6x/Cim2pqCe+exi9FhqZvxwUj77nsQM6HMz87h0xaNA0t+P6ZgHPq7kXneH\n9cl1uHlz+3gSz/kglK/2xz67n/oV8zdw8nqW4Nz2RCUkszq7X0R5XcqkgNxS\nZvl+T5i20Qo/Qx6opyB1ZzOM54lg+OQnNzYYXyF7wIbzjWH8iiOWXm2VaBeF\nhiGfjyfH+CrVi1p3/I5ddBW9X9xfy9C06AGyzwvNvsytpOAyodeNF7DGtroK\nCtP5FyJ5AvRC2UPJCILQUX+lgc1yKMR2gCvLzJ4ZAzypsLYmqAxsiOz4ssNJ\noOoWc1AoR00PNcTB12dewprWruT4sbEViPdMctpltqDOS4wm5sdHmegF3QxD\nvQXLUw7/kMmMu49pbAu6UFiaenrXSo5Bil7kbo9DhxoX7cdDODMCHBI3lB6X\nUi/RX1GOfoyl4aJHLkR+UHT4TL1qHwP2jiJVHnPU6Fyy090nHadeznoJ5gjM\nHYbOazwdkxyNpWAKG6mpXhykyLb+3dqapyrvkfKfENpYYInYxjKrKffkLQkB\nIq2l5raPPc6ewFKKOO/Zo+JTYLSQSUqNye3RXEqQkX9rsSeyaESPR4uTcjrO\nRH2IFYi63ef5fJ25Z1OUd3bj4GPSsWMaDthFQ/BZVw0vt2vMhEH51m0iAXDW\nJztK\r\n=EPdv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "fe2368b3f19f93dd16737fa35f6ab10a602c46a5", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"methods": "^1.1.2", "superagent": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.5", "mocha": "8.2.1", "eslint": "7.17.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.1", "eslint-config-airbnb-base": "14.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.1.0_1610722965475_0.9403705635520585", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "supertest", "version": "6.1.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.1.1", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "4fe6ddfdad4ef3eb72926046c6c625217771d9ae", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.1.1.tgz", "fileCount": 6, "integrity": "sha512-3WDAWfqdNifCURjGUHZFv3u5nMg5tFtFRCTJOcSZXdlYZ0gqVF3UMhA7IJDP8nDXnR3gocbQ6s0bpiPnsoFeQw==", "signatures": [{"sig": "MEQCIC4z9rkC/G/nlaftqNxiggyhA0HFCj/9gPCsfO5I+DksAiBXcC/vo3oiXrEvXxma99fecLldZXfIG0ryMyJncjIuIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAdcYCRA9TVsSAnZWagAAX68P/1ksowDaC+2uhAf3JexY\nKHVtjTM0cDIOS0CNX/fuHuGaDY+1wMraw0g7u6iP627PVHfkXTWebPQ+dRQV\n4IsgsH6j3+GPKNWmC9tmOYVD1M9ZD0YUxiF2rcONlqhvfUVKqvNifisnSr2f\nXSRwJVkCYONzwT74NHzfopXn25tpuukZJjCJtXaR8+Y9waEJ4kVs1aNQhIy6\niBPGmRBFUpCWkHICmouCGHaInDZJBRdzFx+ryTj5qZbURSGY1ZJ8ApTq7JNc\nPAvRy95w5N5smwXkjPbaAumH5BUS/udw3ED2p0YBNoInppmMHNQ4NIr5szuh\n4HgX5KkZmFY9vpqcwczW6eZvP3BEVT8mktepAeW7XEWejsTpuFvYQvO43zFR\n+DCQclccGu3sM7vfIxQLDRqbppnS+xk2rZV0OiPB5V6fsNZEVzDcJez+REOE\nEVndabFN2SGxW/2Qq4BWj0JeRx3+aoRf/dgDItAzy4voFjSWNVh9BPlolQIO\nVC3aZYMfmTWR9RPRiCJknHiyp0GH6QyQU7NUzmgkU7XK1Q6RTsZzDexz5/tL\nsw8J6d9NNs8needAt7A6ddCttXEi1Uc74KC6LcQ9gDpPT5Dfbi/lvL/ti1xB\nBe2CdEgUQUg+ng060Y9gZBeW68/VIkhVqsQjGYGfJtOWLuBDWXbTrWtR7pjT\nkicn\r\n=vMEX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "abd2f456056adb6141b0906172e361e4eafa3930", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"methods": "^1.1.2", "superagent": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.5", "mocha": "8.2.1", "eslint": "7.17.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.1", "eslint-config-airbnb-base": "14.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.1.1_1610733336524_0.387663786099941", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "supertest", "version": "6.1.2", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.1.2", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "1679c234b139eed93911c185b67f689348fc2453", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.1.2.tgz", "fileCount": 6, "integrity": "sha512-hZ8bu3TebxCYQ40mF6/2ou58EEG5jxo1AbsE1vprqXo3emkmqbQMcQrF7acsQteOjYlkExSvYOAQ/feTE9n7uA==", "signatures": [{"sig": "MEUCIQCYWLiYAx6fU/fxqqIpmjf3n3+UsffDAehs9VBNqCqyVQIgB170IYJHcafMjZJ8TTB5/sKZX4GMniQfwktuXx22JXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDlwlCRA9TVsSAnZWagAAoqYP/36ur39Kmdg11J7ZhtLB\n0x6rbSA2S8j/coOAhnuaBgsG6YWMZrBW5UPSBC/xd8hxY0ZZcv9T58wnmkjW\nLvQenKIVcqJgHW44V9uhvUs+0OXcbra8rdJGBUrTZgh/JpK+wXeTw8FhpVuQ\naLEDelljzN7pX+6Pr8aUS688Ifr1XpkmOoiBL5a6njwbP5H21oM35MorrDOk\nl+jsjycurlC6CgtFjGnJBniKbPVsJC1h8NtttAJgACYLX8zylDVB80aBj+Fr\niNFv900QXUf0rHKlR7EDoQXNnhIl16PBrzRYTASi+aW7wdx9V6G6jBPESNfo\nCfeT8pnHUrRiAjlSz7IN/yscMVc4lhTGU5PpMHuOKE16FMObq7/edTu4+xAX\nVl4RG/RSMwZbC5oEOQH8Jy5GY8R/nBRtmG3GxnGH5/ycy0QLoc770saNPfTg\nKsV4/rkBClZM5gILLQWHPsv5nAUeAu9QONGc/I9Eu5cT6nUOj94EzZEX7H8Z\nCtEgc+RRJhaNKRdTtsogzCo2Xp+AnPkpowmcpGVlGx64FxKYNM7vLi49QJjE\nOmgdwJKOf+CELfqCCcFeNnQaNcjkl9A/MtczyDkNIAvKAR0WE1+yXqgPQFjz\n3hZCsiEQbj4H98ekRillmZMpbayEDnlzfakpKXWT6E7lUtNUpAsqd316EU+X\n6PwB\r\n=PanX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "c21ba9a694d51018df477fce90dbbbea04d6f8c1", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"methods": "^1.1.2", "superagent": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.5", "mocha": "8.2.1", "eslint": "7.17.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.1", "eslint-config-airbnb-base": "14.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.1.2_1611553829007_0.9257815517298009", "host": "s3://npm-registry-packages"}}, "6.1.3": {"name": "supertest", "version": "6.1.3", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.1.3", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "3f49ea964514c206c334073e8dc4e70519c7403f", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.1.3.tgz", "fileCount": 6, "integrity": "sha512-v2NVRyP73XDewKb65adz+yug1XMtmvij63qIWHZzSX8tp6wiq6xBLUy4SUAd2NII6wIipOmHT/FD9eicpJwdgQ==", "signatures": [{"sig": "MEUCIQCiWCksrCbKoKAY6QOXR9JK1VrV++ndCnYrheWRwDkMfAIgCbQeGZ9sGcWERfcZI5I5RDWN6aCNSgjmM2MI7YYRjzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDzESCRA9TVsSAnZWagAASMwP/iIB6NYgZDLDf1SHUtb4\nEk1iVQ7huhowjCDjGYVr9IrnUB2/4JAywo1sowOB6Wemv0p+MqkF1F39HccY\niZ5z7wr5PaKWXYnmFgmeru7RB467fqas0t2uf15guCsIMOzEpoMIWcn5IiH6\nY5lpri2/TnQzlptQzhemuSPCAo1sX+xpbwvTP+2GPMItYtIgQ7qXXb8jSBjS\nfuAqt6qHZZLafRLkwQ5OSCu4m0VKz6S0V6ikHAkJ+MvG1RM5n0ePcY+IoDWa\nC0VdHLBCGej3P0AgPHu2e9LPcRUsQP0jVwVNo81EmxNv8/VqkA6BkgPO9Pq/\nlJXxAu043LLVzuX4ER6Elrxk3Yz4WTnzW6DniS285jpf0yyMtrOels3ppnQ7\nlqN7IJaWshto8gTGXSFtJmKeIW+gZ8ei9uz+l/Jh5QYoXR71yvsygc80Cf3h\nG4rjcZhGHeI2lTuEr8KbSoGP8LXMGsmPD6inWsv4bx9iTZJtQhtzFXbJ7GmR\nsDyPgmIuUMfl2QmYhUSBNazQhGkRmxOvP2BxPvvIAZYisl/Rf6BVBw9Gbfjm\nKonrhp7nz1RhTOy5cQFUHT/qERRAGopD7Mu56Nmucy6indiKCPOcycMAsKkC\nMf+Erf+5DP+0Sd7x88C60/cBdhjL+UloKmkQMexPzfSfeC41KbUoSYo6Go/z\n6IpB\r\n=Ucke\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "1bb8c665f8188c312cfc89bf5898f92dfd282882", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"methods": "^1.1.2", "superagent": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.5", "mocha": "8.2.1", "eslint": "7.17.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.1", "eslint-config-airbnb-base": "14.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.1.3_1611608338144_0.39870729555104933", "host": "s3://npm-registry-packages"}}, "6.1.4": {"name": "supertest", "version": "6.1.4", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.1.4", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "ea8953343e0ca316e80e975b39340934f754eb06", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.1.4.tgz", "fileCount": 6, "integrity": "sha512-giC9Zm+Bf1CZP09ciPdUyl+XlMAu6rbch79KYiYKOGcbK2R1wH8h+APul1i/3wN6RF1XfWOIF+8X1ga+7SBrug==", "signatures": [{"sig": "MEUCIQCuwxmmOjsepuVzTZwH4wPiBWeWYci39xwtgsPvoWOMVQIgEEZ97MbWfK8mJdci0C1mM2bNRX7bBFNUCyKTgdeyT08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg95+gCRA9TVsSAnZWagAAeAYQAJpAurPO3PYawZyhOnHf\nZsFNNL1OJc3Sb+OqmG7vja5IMfYK+KgNuQmZEfCgzcKDpO67LyzLbZ5LjQev\nOsCcRBc1mJ9I/LnvBJ8gKYKbF3633Qfew6mL0jeJmqwvt/l40nTpEd9jutX9\nQcOhe3WS+e/gcqHGDfdAzRCbElfNZdc7pcoF68iFrqQf2pRavWLISHjlSqos\nv8IBl37E5uGqcRFmAw+pxQpPooNECwHsWEYvNlOQWoJAPm8oxbRi2yrLXIGz\n+NFnIYCrSscu1wbLkzFbog2MbzMmlEsrJwGae/de5gCombWSt9SvQvFxKKs3\neRrPnptJXGWWzfYIqUS7WFBw/O5fg/Rk4yJdBGUnQnKsQwYtKzBYh3/tunHR\nhRy7obWTgOLi348zl9Q3n8EFcIhv2aWAzCb274JTFe2MEHjz+pbKGi+uh6yP\nDp0h7bbmh8570X6jmG68RO1LFbKpHqDF0v6yQilIAK6Cvh0kK1eOp7Cj2eGp\n2C+B7PGMAgmlDRwQtA+ou2BTyLmAtpTXLcuvkfOZvFhxPMs44w1D8jBvYGju\nEhkvpCEo5vA1dXn8Mld+mjKm77ks2Mame65cgiNx9c/RkbM8TT+LjE1qXEoH\nJF8JLKGrVMrew3YPyphyrDmCFcR6m0e9kNJI5Gmf4qTuURxrG6WaSPMO9yZs\n4Q1j\r\n=TwDd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "cfeae1afa0ff51234c157464694f6fba874f4869", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"methods": "^1.1.2", "superagent": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.5", "mocha": "8.2.1", "eslint": "7.17.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.1", "eslint-config-airbnb-base": "14.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.1.4_1626840992734_0.2862106380711964", "host": "s3://npm-registry-packages"}}, "6.1.5": {"name": "supertest", "version": "6.1.5", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.1.5", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "b011c8465281b30c64e9d4be4cb3808b91cd1ec0", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.1.5.tgz", "fileCount": 6, "integrity": "sha512-Is3pFB2TxSFPohDS2tIM8h2JOMvUQwbJ9TvTfsWAm89ZZv1CF4VTLAsQz+5+5S1wOgaMqFqSpFriU15L3e2AXQ==", "signatures": [{"sig": "MEUCIQDrUE71CzxCgUUIvMumvzCwWoe84C/4vVHn55dJ4F7OOwIgRGuseZejLon3bg1CMmVLDvZDWEbe0HIDbg/0UOpe7R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFAOlCRA9TVsSAnZWagAAGEsP/0/bC+v50ccPwy3rCt/q\nEmsa6fEyaThCD3KBt4kbmgrIqLBIsyn4JpiwquWzdEtqHqhUG28trdWCHeW7\nY0s6hkD1kzJD06BjYB34B7IspTpWk0DGLNwI6PLIcJ62U3fqH+qaMlyZa5Lu\n347kZOabyzNu6NvNvg4X8ty+hGoEAWeOMDF56dWh/4zrFWe0q8iqa35K1VsH\nGtbf8cHsVbt3Tjufz3zTParIsql4Ms4pWIf/pD4reghscvVuTFplsU+To114\n0gh8HXf+NLsUGgl9vlEOZ+IiAVMgBcLoBghpyJRHT7pGpK9dVMYElvXNNBff\nRNExFgiNgNa6vn3iYBIQKJWPTsWhBOZhZOoDZAH1hSreaqQ+EjwlWDsQ5JDr\nuQQptYDKa1NMfpCn1L1gsyl4I6dzzvbYv2isZeLRQWM1TO8wfXvHrV9QluRx\nQp52VOpbYeJVuLj2A2oKs4gcrjZ3sm9HZNZVzVkVBTYk89T1+Sz84FhbpnFK\n5hSSjE814EwmRfsLxQdxISC2sto2cjAqXM0AGUgQv4jymFLOfhIOVJEOlif7\no80nLyU0zitQfuF3oNrAvDBWA2xRLh5oKFVKF24UbUYAIxrsXA9y7Z2itpDF\nO7eWBOJ8eOHZZRw1QtqtZl9o78qeXvIoutKvlSlpAt5rxKUNjYVuY3cjxLcg\nokrk\r\n=0/pw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "b735b90694178cdeece7562d9e452110ab4728b3", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"methods": "^1.1.2", "superagent": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.5", "mocha": "8.2.1", "eslint": "7.17.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.1", "eslint-config-airbnb-base": "14.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.1.5_1628701605578_0.8337493445570656", "host": "s3://npm-registry-packages"}}, "6.1.6": {"name": "supertest", "version": "6.1.6", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.1.6", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "6151c518f4c5ced2ac2aadb9f96f1bf8198174c8", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.1.6.tgz", "fileCount": 6, "integrity": "sha512-0hACYGNJ8OHRg8CRITeZOdbjur7NLuNs0mBjVhdpxi7hP6t3QIbOzLON5RTUmZcy2I9riuII3+Pr2C7yztrIIg==", "signatures": [{"sig": "MEYCIQCPaqzlxhdgUyvcSWUxEtIT8sAD9bQqcCt8xHUjM1b/lgIhAPtC182Fp7VQya92pN/xG1f0EDhrqWvxA9ueYh+LURA3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh28bRCRA9TVsSAnZWagAAA3kP/0BBR5y7zPs6WUZiL5kM\nxnlyYoS1itfcqIxerz4f2Pv5RpAeA/31hqRJ4SToH/Mf3Cl/CCZ3klAqCYMl\nsGM7u/IoRA8yXFqUFAGxO7/ZHvfdCkPzWWK8YGcv13jbUZrGFuZxxa6Mkoar\n+3s96fwpfwqNAqTLc/SRPIsDx5DQBlcPNZ3YAZg944RcDyhAVNRc6Y7n8F6u\n2d3RIr6j1ui5pBWAbRYMb56VTE/WkpuwHnDPFNvXeWHUxoKZ2W4wQutrcQkc\nt2GUobuc6IzYDzfzy6F284rUwBl+2jpKxkP9vLSn8+pIDnsshFxCk+YnUuV7\nOaYhdOjdACbRWeVRz4ylqf4N0zS3AmK3vgAO3uRfb0vF2UnDkTIn2JDyQdpi\nJzeb5/6VFyBAfKUS/KIDkRHlGn1P4w77PNaHAaeSp1Zb65SaJ/1IGOfVIJoO\no4QW/wu+z8jgxqeayKTSEeQaXo/28MTrp1HoHtLCYwi7/4DrXsEVcwDUbHJm\nOcgS7XWaEjs/8dya8y2EQvHLafvIQayPWEDZsWpJzcNRVZgyRrsZqxc8LWef\nOHJ0IbDenfS4yq6EO+L4J52Fsl92fRkPhIhYGi27ZYUFV+IHTogleigh/PG9\nhEc8NWS5Pnao3EKqal9UbgH6VkQYFUeJ6p6mvPz9+ADkZUGNQzZAQn5NH7Ag\nxQAx\r\n=bTug\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "gitHead": "f1b92fb2c68ae2eac9291176a82b635f5fc43720", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"methods": "^1.1.2", "superagent": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "nock": "13.0.5", "mocha": "8.2.1", "eslint": "7.17.0", "should": "13.2.3", "express": "4.17.1", "coveralls": "3.1.0", "body-parser": "1.19.0", "cookie-parser": "1.4.5", "eslint-plugin-import": "2.22.1", "eslint-config-airbnb-base": "14.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.1.6_1629231390535_0.8682461989223147", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "supertest", "version": "6.2.0", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.2.0", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "dist": {"shasum": "5ce9e1603744d79b6620e65d2e16e9dc635244ae", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.2.0.tgz", "fileCount": 8, "integrity": "sha512-JU30iTy0Utm5GbVJvrtt6RHNQECiqVl6KckEVhnR0AoGzFGq0IFMmBXnoTxYy9pM/vgufiPevjxlF39XaKn77Q==", "signatures": [{"sig": "MEUCIG2A9PEJyFzDtqOrE5m0aJIt/6n6e+14ngxXQAxixdlOAiEAgs/jEGqRfntXvsTQ9euYigHj8LG0FUaVSo42Vdyx/VE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3LZ7CRA9TVsSAnZWagAAD88QAICTkVDRHge6BKGqWNIX\nzSNek9lOel2cm7CuIWwa89I4y7cEh48qT/kFRBvzpzJ8adYPO2C4wUMUe2Ke\nrUGJbSZRR/DYwt22r5PZ6fjWWHb310D/I/GTXho41Nd8Z/gO60+/TIM09y9W\nxnl0fpnHsRs8brWC2ETSkIRAnEuov7ZV/U/+kUy+UvcQWodYZJIj4bhkgj3O\n1xJ1UJiXiEdvQWssp1AWjskDDTGekAiEduMXbKtceXqD6ki0GoNxrBrwXn08\nJV8ULC8lze/v1mrKddASpSpy8mz6bAUwJpVKUcEJVad7FuszZm3iYlFrL/o8\npwGvjiF4oDoFQI7BR/TcB5odYFyfLGRFv06UtJnr75YpMBdmA7hSSR2q0l4B\n8ZqfJoI66vyIsh/RDoLdtYsTVvKSEK/aG9nwOgqNoGMnHIdd8PYOIKDBKPB5\n1rpCmBz/zackUdYayLzSLQj6Dny+3gN3e6UJmjwnjIVmWmUK47p0jhAEp+lv\nnF218b1xhmY++zIFoN9klKY7P28k0T7dnLu6dVMFe+jzMsQA++w+MTg2g/C4\n8OzYD7+JZxYaoeVWKaKzCV0UnDe6irrJv/+WPYVM4w6Odp5QGROxLHUNVwzQ\nxe9Wz4FisZ4FKU+rTQ7CLNYTlXM3HRRpSxkceJyvxmvOXnI0/VkKFIy/VPEq\nMBSo\r\n=WvbI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON><PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"methods": "^1.1.2", "superagent": "^7.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.1", "mocha": "^9.1.3", "eslint": "^8.6.0", "should": "^13.2.3", "express": "^4.17.2", "coveralls": "^3.1.1", "body-parser": "^1.19.1", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.25.4", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.2.0_1641854587192_0.7380997200109596", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "supertest", "version": "6.2.1", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.2.1", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "dist": {"shasum": "add42f1afa661fa5b3083da05c6c964069768e0f", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.2.1.tgz", "fileCount": 8, "integrity": "sha512-2kBKhfZgnPLmjpzB0n7A2ZnEAWTaLXq4bn3EEVY9w8rUpLyIlSusqKKvWA1Cav7hxXBnXGpxBsSeOHj5wQGe1Q==", "signatures": [{"sig": "MEUCIQCTa/LkxuTBX766qiklg60mlwaDqLDfpnwhVGNlsAcBQgIgfe0WXhFAlhkR0GJuEweOsL7hei2sxnMWreQOHtmnTG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3gQSCRA9TVsSAnZWagAAzUoQAIqWjSEriTemy8fISPuv\ne4vyg03BFHWuuytQVp4Yy+EZqYHi5sX+50sNGKROwJc9Oom73tZCt9FDCC2N\nbzE2uQ4TRaFMBXc9hGk82I2SdkJoCmRdZ5rUvKZl4k2XZXhqj6Qv7PMY2+Zt\nv8GfXApQsu9cg0kIVdrIZfqQbns1amqKOZshALnKcgIiU1LmNUNqDKCWNq04\nrHVMjEKrLGrYbS7Fg+Dn4+jgNN0k6QgRjI89tNcIAFusnPlGmeW9SjH9e68s\nzTY4ylxYmWV62GollKuUoSr/0mDJHIENEUcW38Y/0E0n3i+i1Cwl4NJb92OB\n5HUkBhvmSyKkGWHvXbE7ido/lpxX4Q7LJCGdvcOwjuBBq52DBMY/fgElRsCH\nBQouq24++h5EMuWY2j64qJviOCSEUT/0VtRL0J3US5R8O+5M0UAioHx1kZBO\n+JM57skDkQoroJ8iRpFShC7Rwg6zNqNmeqOw7C8HZIo13ZOTgm6jVUckTSDg\nvFwcRrtxMEKXEJXwLrbuy+ndqcYDONhOygaQ0015OqaW7+cCrLRd0l3UnTL+\nmKPQB6Dxf2RSvdApEDxTz+/DYIZNNncpmSDutwYTVRuD4z0oziQLRf/SN+x+\ngSVwhQwLOXqgmrJK8WlhivAQ4L6ST1fyz/ct99X+20roEhYWUp5EFv/A7fpz\ntYLB\r\n=fWOB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON><PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"methods": "^1.1.2", "superagent": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.2", "mocha": "^9.1.3", "eslint": "^8.6.0", "should": "^13.2.3", "express": "^4.17.2", "coveralls": "^3.1.1", "body-parser": "^1.19.1", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.25.4", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.2.1_1641939986717_0.9733632725869084", "host": "s3://npm-registry-packages"}}, "6.2.2": {"name": "supertest", "version": "6.2.2", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.2.2", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "dist": {"shasum": "04a5998fd3efaff187cb69f07a169755d655b001", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.2.2.tgz", "fileCount": 8, "integrity": "sha512-wCw9WhAtKJsBvh07RaS+/By91NNE0Wh0DN19/hWPlBOU8tAfOtbZoVSV4xXeoKoxgPx0rx2y+y+8660XtE7jzg==", "signatures": [{"sig": "MEUCIEEH1Ar0JtD7Ptjc/OODicFKqzZ2Gdrmep3SeMKmN8XTAiEAp+q5mp6QKE5REVsT5e2wkzsCmEepLe1uC/q/I3smI6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zh8CRA9TVsSAnZWagAALYsP/Rrfw/SQ4ZNmDtPqXHJh\n5Ow1pzLRt/fC57ViY6H/6OG6o/lI0xJVG94NBy0quaO7JcHAI/JE6RGkhAC3\n4zTDhv0VZCrrc9HaxZKOUEkGe+2zZLnXdMI+Qwm6dwQmZzWGyTfmeAerVBRF\n/J65ngKaI8RQzAPMatJXN/uCAbq4kLDp1YYhaXsO0bCCh51u7wbkmKSx8XRN\nO+uwWd2RQFMIjvp5litk/GMGcWZ45159SKlrdfBSdtorHHfpqF7fBqH0AY5I\nnRJsTufoQMWQ6rZaYGwiDTeeEE1k+Yxz7zUzQ6yZEKn09yh+bbpXG1VZcZEb\nQbftvCAKSHw1eAFeVUh9kiQHy8BfL89fnXpNW9hUqCunOJpbG5Eu42JO6xE6\nmoQNXagAFveS60YN6PYewG6OlTG+ipThga5yjxD//9yUN2I3NgUg/9SroYTu\nZOMvIefkvjL5Ku5hLCcKoLdf5Hjc6yejGYFHZcVWtBE+HhQwU+vcGMHftbYg\nOLmgpA08YuLw69OjDe5T22w4B/uRjXIrxZlQ7xcfmN/mu2CdWpR1y/265O7Q\nGyhVE+sbkB31zgfkz+xVd3utcFGEqs13Bv9ULA/kKz5wAF2Y6HiqJzArp4sS\n0PCZK3zoPPodiAW9fGQQneW2iGLNlyjDPhzZ79HwhfLkcI30wR+3+tXHAcJY\nzz4T\r\n=N/9C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "niftylettuce", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON><PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"methods": "^1.1.2", "superagent": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.2", "mocha": "^9.1.4", "eslint": "^8.7.0", "should": "^13.2.3", "express": "^4.17.2", "coveralls": "^3.1.1", "body-parser": "^1.19.1", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.25.4", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.2.2_1642543228287_0.7065865489981569", "host": "s3://npm-registry-packages"}}, "6.2.3": {"name": "supertest", "version": "6.2.3", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.2.3", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "dist": {"shasum": "291b220126e5faa654d12abe1ada3658757c8c67", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.2.3.tgz", "fileCount": 8, "integrity": "sha512-3GSdMYTMItzsSYjnIcljxMVZKPW1J9kYHZY+7yLfD0wpPwww97GeImZC1oOk0S5+wYl2niJwuFusBJqwLqYM3g==", "signatures": [{"sig": "MEQCIBNAvNVahT3bea0alyDddLRfZhDLYGt6oO7C5c89HNlTAiBWt0nC9e6USlD5Lbei1NAntJbKx+6L89x8SbO7HCGpww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaIW/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp73A//TFlE62rgLp7sSUVddm+Y/ewWUcg1yWdH3pOHtlq/2ju2cE1n\r\n4yn07hxZpg3PjtVpkswPf2HIVPj3MNK2z1zAOfJdQg2RMLNwFIzhtI6YBNDV\r\n2I1pLLllRTRDPESYuT6GuE76Da0ZVUhpwZqvkKibRqTp8T63HehY8ztK80Hg\r\nuDw3CEwJnaHkrQyz0jMtYakuVyri07p5x40gwMojJrEn+fUmPZj4OPgo6Z7R\r\n5bDDoNMY+D13RGRYeWobR9tvQt25m78WUUSsi3/0+8ppJIKQWuaTLgIHI5Iu\r\nuiG73bstyakzya3NZ+VifpNj2Yv9lN88ZKtg2KRF6OJbmHy7qZfktBSxrwLt\r\nzrgEPgnSCnwVGrijIOz7guIIYHL+e3n7IQ7RfJ3mvg0MjgVfPBufujqMGF8R\r\nMjRgLXicE1Kqo3Q6nvj4BTfVsqjpwxjIQuLyLtKPJLHK2VIE4Kl+U28+eICN\r\nKpkiMC0sLclb741VKQtYDi9pVkTq8dSJn0hiPlOpWj+jTkj9L4QNopovvWUo\r\nCN71aNbTp0mP6kG1nW1h7qupZ8em1syEpmPfdbiJp/8YCAw2RcKQQbGMidYW\r\ns00a2v7o+tl5BrLFMKkqs62ZecNhZycwraZNowlaVYbCSJ+tOP8QvF04prta\r\nL5GbvxzxAP1uf9QG/mev52AMk32va+MJ67s=\r\n=zuwF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.0.0"}, "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "https://github.com/visionmedia/supertest.git", "type": "git"}, "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "licenseText": "(The MIT License)\n\nCopyright (c) 2014 <PERSON><PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"methods": "^1.1.2", "superagent": "^7.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.4", "mocha": "^9.2.2", "eslint": "^8.14.0", "should": "^13.2.3", "express": "^4.18.0", "coveralls": "^3.1.1", "body-parser": "^1.20.0", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.2.3_1651017150992_0.9656549135094332", "host": "s3://npm-registry-packages"}}, "6.2.4": {"name": "supertest", "version": "6.2.4", "keywords": ["superagent", "request", "tdd", "bdd", "http", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.2.4", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [{"name": "Dimitri DO BAIRRO", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "3dcebe42f7fd6f28dd7ac74c6cba881f7101b2f0", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.2.4.tgz", "fileCount": 6, "integrity": "sha512-M8xVnCNv+q2T2WXVzxDECvL2695Uv2uUj2O0utxsld/HRyJvOU8W9f1gvsYxSNU4wmIe0/L/ItnpU4iKq0emDA==", "signatures": [{"sig": "MEUCIEhtJZhFVLGZSCah7fTU+QiMCMIS+8YwHlnV6WyYRCPdAiEAp9JZ4Bh0qWMHhpOwr5L+PBxXy08DwoSINIk0DUPe14c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiv4EPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8gA//aM6L6sjQZDf29dxqqZFx6l3N6jOlxppcY/Vta4Oz0uOdA1ho\r\nK7ThqF8KihzVxnaucRuyFPcE8sGSN5h8OYiDvdw6qliUtkbgTdSTKyTZl7Fj\r\n7waEbG8Wmd2dmbRX7nYhHmp2JilejnSakB2cEEO0tgy6rSXTl1a1/D5UOhv2\r\nlMlRpQ3tCGoeE+EdUYfzbM9VQaRWtnjh2t7rcG48Q9qmQWPJzfmR5VdNIS3f\r\nPG+hoHSNCtZJqBDuwms1FoNVpqOpSLevZ84z2kiW3y+mLKpKDlvTjQt+5gvE\r\nZEm3m4siITSnEHFWc77GOzhSINuwNOFGE1q5LSDqIYNXLvBYrabKuddQgnaS\r\nlPnQfNKldpzTABWoye3MrnaqS/1QY5H5gVRObmqN7QecsydLCtDTiv+FGBSC\r\ny/D4wHEr0kc8ESGr7sOwwbAlaseC3TYMxDBdVzvawoP9IiYfw1z6IOZAVTq8\r\nvAQfbVZiReZq2CJ9vircvBkgWpHoXAPA7c8NpcP0MRvqmb5O5oUQ/r111jK5\r\nSV14tJUr3UXWkuNTvkzgBbUjcSm4wpn/xR27qX/ro1QqAVdd21uaYe7MqFh3\r\nfGK8fRO9x7OOLHoAQckc/0MzUXyTz6N/5TE0pXIpN1al6lGIbVDquwwJpU72\r\nvES7MDjo+Sh6BVEhDMZzAclx162WxOwvyR0=\r\n=KeT4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.4.0"}, "gitHead": "5543d674cf9aa4547927ba6010d31d9474950dec", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"methods": "^1.1.2", "superagent": "^8.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.8", "mocha": "^10.0.0", "eslint": "^8.18.0", "should": "^13.2.3", "express": "^4.18.1", "coveralls": "^3.1.1", "body-parser": "^1.20.0", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.2.4_1656717583433_0.07641467075523134", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "supertest", "version": "6.3.0", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.3.0", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "06c21234ff0be9e398ba19f7a75f237930d57442", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.3.0.tgz", "fileCount": 6, "integrity": "sha512-QgWju1cNoacP81Rv88NKkQ4oXTzGg0eNZtOoxp1ROpbS4OHY/eK5b8meShuFtdni161o5X0VQvgo7ErVyKK+Ow==", "signatures": [{"sig": "MEQCIFnyx0f+rqNjGEo/Yvp1C8WJwcxr6MBmoqkzrWxtmS6QAiBNn7BXy4RQnjR9UPk6bYIbk4a+2EpDqxfdyt6FRJDozg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjO6CLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZ+BAAnu5Y04HTU0qWnx9QBUJWoEzSRYsKRxT/0PQ70eNJpXlR/BiM\r\nUzq+XHWL3bfLDA00XPqPjv+Xj+1RHQcdmn1sIuBXmiFeL5d2HQwihpPe2CWX\r\ngk3ZTwamfoGjZwUCr2XbO0GmseGHHSLazk639ZIo0+/faoAEeiosDr8QSX2w\r\ngmKoy6MjKma26vacvn/epzGkpLJvoBzBfC+65goKEwWYzhRtAQ0zEeLE4ReB\r\nu4JraytzFz02dGuJVH3U7JxXKe3GYQntOK3QT6EAcjK2s3rj+yd2Om6RADqE\r\nwhM8bNGOm/Mr8+qDo5WWe564EN1mO1zHqAejQE0br/rvhvEM9H6wWQBXMYjO\r\nD3eRCnm7T5L8vyYHguw06b51jseNWgtQgr4O0vy2nCy6CtOzkTFfSVGXyAr0\r\n8+ypYtaw7AGtPCTKGtmEo07bYupa9RozmhaZnyxTVrZqdqNKIhA35vrpXj32\r\nfgKVuoztim4AdDtRFUX3KymhHxQew5TvCpng96D8now8wKN7DVS/gk+yUGQV\r\ncqNZJg0Ctj2v8/1n4j5+t+9o27fB+B5p4o64hqYtRWugOSn1HEwTM7FV2kpw\r\npgdK+cQnmybsqTBHQy/CvGnEDPEJp1xwLBNsxgq3L5MFjPXFuYq5rAQQrLwE\r\nf8QX3AaZC6wfcRi5JFpURoI2f/tkCI3GHQc=\r\n=sAJE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.4.0"}, "gitHead": "6a950434e26f2df25f1c6d08682dbae14a198aeb", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"methods": "^1.1.2", "superagent": "^8.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.8", "mocha": "^10.0.0", "eslint": "^8.18.0", "should": "^13.2.3", "express": "^4.18.1", "proxyquire": "^2.1.3", "body-parser": "^1.20.0", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.3.0_1664852107415_0.40141799798626554", "host": "s3://npm-registry-packages"}}, "6.3.1": {"name": "supertest", "version": "6.3.1", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.3.1", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "rimiti", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "a8ad362fc6f323c88730ac191ce30427dc869088", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.3.1.tgz", "fileCount": 6, "integrity": "sha512-hRohNeIfk/cA48Cxpa/w48hktP6ZaRqXb0QV5rLvW0C7paRsBU3Q5zydzYrslOJtj/gd48qx540jKtcs6vG1fQ==", "signatures": [{"sig": "MEUCIG6GL7NHVuVqsUrrYE7VUUSnEYjXNclUD/4qZquINABVAiEAzOkNA7qB40LbcbUrxlOt6J89ry7fAoAcDs3DrJcT/vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvYIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqm3BAAirbbRwU0vEONibbYWZq2xw/LPaAAX7UP4WxJ9d/+nT9axqC6\r\n8DhR2jBXhPpUITi15t3Su6OaOZCeODkGXUOPpghDpL3xpkBLzOwpmio8VOYc\r\n3YywcSRvgx3EgZ5YEv9q/8iVVQqsbMk8Avw6rxhaNzR2C9QCIVuHQJOQhKdD\r\nJzxwR1BPevF1CN2OpQHEFf7DF8yo5atD6hBrybLLh1jOwnQJKhTPHSRTmfA8\r\nIx7Y/5c7a5lCHuVeqrQx8vrCFtiWSkwv3Qdw0Poe6snaxcp/3NJjk4aOjGQM\r\n/aTZBS4V4yQQe7vsqQhKOrZ6tuEptGPJo4iWjonxRsAv+Qhz/rBzPXzbsDFF\r\n6SuE8NNSPyH5l1iVdt2LV7hXFhfbLT3EIGpaPubBkcUnIncORkI0TiLkDJ+6\r\nLP3jBTgLkdapAf4wjYwESQrBagK3y+sCdXeoBO2c+i5UeRSI46TNwRXZoRTr\r\nWp1DdJQ7NvChvqrvURJ8WFb8ybNjksMHoXUI1P51BMT1O8CtHwSsRxO2brYo\r\nkvsJ+Jgc9XqlZDrQcqj8jaG34kReAaI6YBeMC0JU6eMJhMiFr9xUKvVp/OLy\r\nC6bBP8lFcLAzo9nJ5XkyGXQr6kJ5eFbF/QX/YWwSN7EFn8WL6fapLgQnVzgF\r\nYdXkZOUvWEtwdHfsEC2zw694Wjbc4Lc9K9Q=\r\n=B9lY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.4.0"}, "gitHead": "ca353ddc6370426655cf7eddda386a7cb9571728", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"methods": "^1.1.2", "superagent": "^8.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.9", "mocha": "^10.1.0", "eslint": "^8.26.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.1", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.3.1_1666643464158_0.979590015188454", "host": "s3://npm-registry-packages"}}, "6.3.2": {"name": "supertest", "version": "6.3.2", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.3.2", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "7780b4b85bb2ea675c05b5cb80fa52f4dbe5a52b", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.3.2.tgz", "fileCount": 6, "integrity": "sha512-mSmbW/sPpBU6K8w8189ZiHdc62zMe7dCHpC2ktS9tc0/d2DN0FaxNbDJJNFknZD4jCrGJpxkiFoVyemvKgOdwA==", "signatures": [{"sig": "MEYCIQCPzXgb7F075NLyLlG3EPUSUjF02SxJjXsQdhbLwO5i8AIhANAPMT1S6tJI5vqkUllZfiprNYGfpRTJ7yGFWBoKZsWv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjim+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqobw/+JUDPIABohIkAnVowG3edoFUtifliiwtPwogIwpZppB44bR/x\r\n+FN5MGn6XGJpJ/qcDke3fWhhATWNSWSh3OuScbDN/Z1UG9ZKW+NQbKYC/Ly7\r\nUdXKy/sphIlWEjm4ahf4p+uNRBdZMdtbKRxAQsjLQFZCF5hhR5bG8DdogWN7\r\nYywXUzFVLr7gsZKu5KrQqKbmYh5yeCDkxV0bK5U3k3vBLkIO8HsYglgBjf6Y\r\n0tkkz2r/MiFT74ivuoDgVZYv+gbtp9PRcQr9ha/SmR1ZSPqeb9zUuamZ9M+5\r\nj+zDHCUquGZORbU7z5hamfGzTfrQqcHq63akZGebcWdPBsUhRFomZjE1SuyR\r\nOaSZ4Q7k0TM8g3V1k5CD/QSo/B7jVokR5G13NKcVvQlNzHz/a0e0cpWAA/hA\r\ntM0+ZK545WXrbcj1sIIgcfum+0DYktjcvopYkkHku5swItrOvPJhVdZoTlxC\r\njZfqCoLIiqZCGeU4oq5aSvwat/9Nx0wz9zMipdF+oXNGDsRX29yopVmwryiM\r\n0jqU3nPaxyOC0W9lrVVNFCqddfRBaWzS1ACyLarwazUOLIJ9tp15VFrIakRo\r\nqLGRVr3+R6gn/yNIiEVbnQK7GWQ9DBZL1k/U4U2ikbbj52QyZQc2dk1iLNrm\r\nvcEajA07+hnbVPY7+jaRL0DdDVYGFHwmN6w=\r\n=xMr/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.4.0"}, "gitHead": "fb4f327b3647a7ae6aa4f0d4500ea8d465d7dd6c", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "16.18.1", "dependencies": {"methods": "^1.1.2", "superagent": "^8.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.9", "mocha": "^10.1.0", "eslint": "^8.26.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.1", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.3.2_1670016914074_0.5718451436309788", "host": "s3://npm-registry-packages"}}, "6.3.3": {"name": "supertest", "version": "6.3.3", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.3.3", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/visionmedia/supertest#readme", "bugs": {"url": "https://github.com/visionmedia/supertest/issues"}, "dist": {"shasum": "42f4da199fee656106fd422c094cf6c9578141db", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.3.3.tgz", "fileCount": 6, "integrity": "sha512-EMCG6G8gDu5qEqRQ3JjjPs6+FYT1a7Hv5ApHvtSghmOFJYtsU5S+pSb6Y2EUeCEY3CmEL3mmQ8YWlPOzQomabA==", "signatures": [{"sig": "MEYCIQDvfvvWGNpyIDDHW1azbtDNaoL/ACUR2ik24L6ETwKq3AIhAKFnSWXEc8kf3jRAX4PUndPnQbTp4FCvtkxVo58YAEZi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkSE8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3kA/8C2bhQpoWRApS7GjZWsrYg23J6IhgueMV6B1dAsfQc5YjlpkC\r\nl3sQNB6H37sFQkct6nlWwyC8TMgCtK8IVeTjR4xi6DbGwwgZtOE8cVANQDKn\r\nvgeZpBACGB7thCWTx8doBYQ4s4thj+vZ3uBU8OOOQgC/aNPGQAwfvurZy/TG\r\ngvTLF4I17GDo9/2saHMYKXK3tvIzw5u/3Y1E+3UOx5xRQiw3jCe2D2rxyiD7\r\ni9Z4Y0VuLu/jvIZ/tP+XENXR9NFIx8FeyW3AgL2gZaM+rEr4EwPweiW1N+uA\r\nhMQMNHWXWUc92nuMe793U/Xhb1vBSrAdANR0PPQyL79muTT6qfb1DLcRZEMe\r\n+G4EOqtW+zMu3E13blXfQLZonXH3ZFvVIdGJOpSpuofhUSfTaqXqQH6LDdt8\r\nqRFh07IHVpzdkPDPQN8iaV77X7WKxDqAm+aetTE4S2nAocL1hwX18Sc+x6v+\r\nDNpXtA+UOIFHYWF0x4Yr3FHvlytQikwdUqd31qCkupSxu41YJoHQsHEWGs6Z\r\noaN6bZGUe1AAqkHznh62WxBjupK9MaJLgjyvzG7VK5AzFRRHFVO/pXa6DBex\r\n/RBny3CnKlYwV2np+1UYSFpNXzqUeywXluqgLFNW5qfrZc2IU6FA+GplDGmm\r\nOrxenxflrEqQil82j3U5POWK/ASZ7PQEvng=\r\n=n8Ag\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.4.0"}, "gitHead": "ffb96df43b1d5c2bad7f6b9534ecf948e87b9452", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/visionmedia/supertest.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "16.18.1", "dependencies": {"methods": "^1.1.2", "superagent": "^8.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.2.9", "mocha": "^10.1.0", "eslint": "^8.29.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.1", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.3.3_1670455612489_0.1767663781355613", "host": "s3://npm-registry-packages"}}, "6.3.4": {"name": "supertest", "version": "6.3.4", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@6.3.4", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/ladjs/supertest#readme", "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "dist": {"shasum": "2145c250570c2ea5d337db3552dbfb78a2286218", "tarball": "https://registry.npmjs.org/supertest/-/supertest-6.3.4.tgz", "fileCount": 6, "integrity": "sha512-erY3HFDG0dPnhw4U+udPfrzXa4xhSG+n4rxfRuZWCUvjFWwKl+OxWf/7zk50s84/fAAs7vf5QAb9uRa0cCykxw==", "signatures": [{"sig": "MEQCIEV/JzOLJx/oLBbYjcczy3UEl6TekEPDxXv2dKSHEu4EAiB5hlSsWk+rdjmmKcqb+MQ0rIz64HNh1KVQIXqSaFTlHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23946}, "main": "index.js", "engines": {"node": ">=6.4.0"}, "gitHead": "fd571c82cdce1a7ba93f9a576277dbe535fd381f", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/ladjs/supertest.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"methods": "^1.1.2", "superagent": "^8.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.3.0", "mocha": "^10.2.0", "eslint": "^8.32.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.27.5", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_6.3.4_1705250510189_0.4954015668897285", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "supertest", "version": "7.0.0", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@7.0.0", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/ladjs/supertest#readme", "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "dist": {"shasum": "cac53b3d6872a0b317980b2b0cfa820f09cd7634", "tarball": "https://registry.npmjs.org/supertest/-/supertest-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-qlsr7fIC0lSddmA3tzojvzubYxvlGtzumcdHgPwbFWMISQwL22MhM2Y3LNt+6w9Yyx7559VW5ab70dgphm8qQA==", "signatures": [{"sig": "MEQCIFMIcwsQZhMEkC2Rl12UJ6JRTttH2j4QuGtydcfhvV//AiBjFeZJZ3jfCffd2zzt1IBOdN8MXUEUKnocJfvM7hz/vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23986}, "main": "index.js", "engines": {"node": ">=14.18.0"}, "gitHead": "2ae1c36f48fdc5a60e8239c2d69c47e169042829", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/ladjs/supertest.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"methods": "^1.1.2", "superagent": "^9.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.3.0", "mocha": "^10.2.0", "eslint": "^8.32.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "eslint-plugin-import": "^2.27.5", "eslint-config-airbnb-base": "^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_7.0.0_1713971976134_0.9794778855659003", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "supertest", "version": "7.1.0", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@7.1.0", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/ladjs/supertest#readme", "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "dist": {"shasum": "09b273174a8820e57ccdb03d9ca0d96c08c96b52", "tarball": "https://registry.npmjs.org/supertest/-/supertest-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-5QeSO8hSrKghtcWEoPiO036fxH0Ii2wVQfFZSP0oqQhmjk8bOLhDFXr4JrvaFmPuEWUoq4znY3uSi8UzLKxGqw==", "signatures": [{"sig": "MEQCIEangC6wStTNaFtpl4zYp5QHb4gNGyTZ7pxEZ4WjfZ/zAiBL3+ljGK7OOYh1DQb+8m8Z8y779QT/++B48fQP7cYNxQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24298}, "main": "index.js", "engines": {"node": ">=14.18.0"}, "gitHead": "359bc52e3a4075d63ef036b11ba4e5e4ecce2e42", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/ladjs/supertest.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"methods": "^1.1.2", "superagent": "^9.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.3.0", "mocha": "^10.2.0", "eslint": "^8.32.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "@commitlint/cli": "17", "eslint-plugin-import": "^2.27.5", "eslint-config-airbnb-base": "^15.0.0", "@commitlint/config-conventional": "17"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_7.1.0_1742485687351_0.88675690330751", "host": "s3://npm-registry-packages-npm-production"}}, "7.1.1": {"name": "supertest", "version": "7.1.1", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@7.1.1", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/ladjs/supertest#readme", "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "dist": {"shasum": "c5e7e2d047fbbe4403b17b2622dc5323adc39f11", "tarball": "https://registry.npmjs.org/supertest/-/supertest-7.1.1.tgz", "fileCount": 6, "integrity": "sha512-aI59HBTlG9e2wTjxGJV+DygfNLgnWbGdZxiA/sgrnNNikIW8lbDvCtF6RnhZoJ82nU7qv7ZLjrvWqCEm52fAmw==", "signatures": [{"sig": "MEYCIQDMLqHr7cILtjLwrd9TpLzhRjodGDSiqooUmjRvEHwrfQIhAJG7Q1NiDkgZyCcVf8/xDH1wj3rtdk4rW8tzOmvFxaRF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24299}, "main": "index.js", "engines": {"node": ">=14.18.0"}, "gitHead": "200031e21905b25591ece2111ab2eb56cbf48fa2", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/ladjs/supertest.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"methods": "^1.1.2", "superagent": "^10.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.3.0", "mocha": "^10.2.0", "eslint": "^8.32.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "@commitlint/cli": "17", "eslint-plugin-import": "^2.27.5", "eslint-config-airbnb-base": "^15.0.0", "@commitlint/config-conventional": "17"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_7.1.1_1747049225656_0.2694900255914079", "host": "s3://npm-registry-packages-npm-production"}}, "7.1.2": {"name": "supertest", "version": "7.1.2", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@7.1.2", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/ladjs/supertest#readme", "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "dist": {"shasum": "e8173825985a24714ca3e4613e5d86243f1c3ba8", "tarball": "https://registry.npmjs.org/supertest/-/supertest-7.1.2.tgz", "fileCount": 6, "integrity": "sha512-hM44JNZTw2IS55zVQB6O5HmDxn0qpH6X2owVcNTNdBzlbYhZPPIwVyTieFMt3FLM84As17vjFyrXDf88H7aiPA==", "signatures": [{"sig": "MEUCIE78smq00fd1eAbjwUy6792wVMzCyzEGyzp4aBSdID8XAiEA7O3gCG5UhaE58Rsm++OTQg+lQbMYJc9U94xETwSxYZo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26130}, "main": "index.js", "engines": {"node": ">=14.18.0"}, "gitHead": "29c3267d31740bee7af17144703045f836e59405", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "actor": {"name": "titanism", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "repository": {"url": "git+https://github.com/ladjs/supertest.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"methods": "^1.1.2", "superagent": "^10.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.3.0", "mocha": "^10.2.0", "eslint": "^8.32.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "@commitlint/cli": "17", "eslint-plugin-import": "^2.27.5", "eslint-config-airbnb-base": "^15.0.0", "@commitlint/config-conventional": "17"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_7.1.2_1751914133264_0.012629883105480522", "host": "s3://npm-registry-packages-npm-production"}}, "7.1.3": {"name": "supertest", "version": "7.1.3", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "supertest@7.1.3", "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/ladjs/supertest#readme", "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "dist": {"shasum": "3d57ef0edcfbb131929d8b2806129294abe90648", "tarball": "https://registry.npmjs.org/supertest/-/supertest-7.1.3.tgz", "fileCount": 6, "integrity": "sha512-ORY0gPa6ojmg/C74P/bDoS21WL6FMXq5I8mawkEz30/zkwdu0gOeqstFy316vHG6OKxqQ+IbGneRemHI8WraEw==", "signatures": [{"sig": "MEYCIQC9Rq9w7SQLBq/Ej1Bsih4czt6+IrjPVXrjHzlXwpwu3QIhAK7Chy5UzkmWbgl9ogXZ5yyjXwTEahW1xRzFvyJ5K+dF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26131}, "main": "index.js", "engines": {"node": ">=14.18.0"}, "gitHead": "0cf2ec375f1aeed4eac257e51b1d81263452337c", "scripts": {"lint": "eslint lib/**/*.js test/**/*.js index.js", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks", "pretest": "npm run lint --if-present", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js"}, "_npmUser": {"name": "titanism", "actor": {"name": "titanism", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ladjs/supertest.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "SuperAgent driven library for testing HTTP servers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"methods": "^1.1.2", "superagent": "^10.2.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "nock": "^13.3.0", "mocha": "^10.2.0", "eslint": "^8.32.0", "should": "^13.2.3", "express": "^4.18.2", "proxyquire": "^2.1.3", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "@commitlint/cli": "17", "eslint-plugin-import": "^2.27.5", "eslint-config-airbnb-base": "^15.0.0", "@commitlint/config-conventional": "17"}, "_npmOperationalInternal": {"tmp": "tmp/supertest_7.1.3_1751930851401_0.4834737759722003", "host": "s3://npm-registry-packages-npm-production"}}, "7.1.4": {"name": "supertest", "description": "SuperAgent driven library for testing HTTP servers", "version": "7.1.4", "author": {"name": "<PERSON><PERSON>"}, "contributors": [], "dependencies": {"methods": "^1.1.2", "superagent": "^10.2.3"}, "devDependencies": {"@commitlint/cli": "17", "@commitlint/config-conventional": "17", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "eslint": "^8.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "express": "^4.18.3", "mocha": "^10.2.0", "nock": "^13.3.8", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "should": "^13.2.3"}, "engines": {"node": ">=14.18.0"}, "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ladjs/supertest.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint": "eslint lib/**/*.js test/**/*.js index.js", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js", "pretest": "npm run lint --if-present", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks"}, "_id": "supertest@7.1.4", "gitHead": "076228a654236d65e593cd94b8c8950722d78c5a", "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "homepage": "https://github.com/ladjs/supertest#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-tjLPs7dVyqgItVFirHYqe2T+MfWc2VOBQ8QFKKbWTA3PU7liZR8zoSpAi/C1k1ilm9RsXIKYf197oap9wXGVYg==", "shasum": "3175e2539f517ca72fdc7992ffff35b94aca7d34", "tarball": "https://registry.npmjs.org/supertest/-/supertest-7.1.4.tgz", "fileCount": 6, "unpackedSize": 26131, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICGHFfAI9W9Kp9OlCQ4oGkRZZl6Jpi/vgNi/REzLz3E8AiEAl8F3Bx1lKYI39UaF6SVCk/s9ivg2s838MMTaDSNaBZk="}]}, "_npmUser": {"name": "titanism", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/supertest_7.1.4_1753226524364_0.020101522853462228"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-06-26T22:08:49.934Z", "modified": "2025-07-22T23:22:04.730Z", "0.0.1": "2012-06-26T22:11:12.995Z", "0.1.0": "2012-07-02T18:31:20.850Z", "0.1.1": "2012-07-03T23:42:11.764Z", "0.1.2": "2012-07-15T18:43:39.451Z", "0.2.0": "2012-08-29T21:31:33.996Z", "0.3.0": "2012-09-24T15:55:06.097Z", "0.3.1": "2012-10-01T20:48:23.428Z", "0.4.0": "2012-10-18T22:12:22.542Z", "0.4.1": "2012-11-14T22:49:47.801Z", "0.4.2": "2012-11-17T19:40:44.443Z", "0.5.0": "2012-11-28T22:19:33.112Z", "0.5.1": "2012-12-07T15:46:27.554Z", "0.6.0": "2013-04-15T16:05:04.383Z", "0.6.1": "2013-06-02T23:17:12.436Z", "0.7.0": "2013-06-04T19:29:20.647Z", "0.7.1": "2013-07-02T22:43:38.351Z", "0.8.0": "2013-09-16T00:41:41.048Z", "0.8.1": "2013-10-28T22:19:40.508Z", "0.8.2": "2013-11-27T05:07:20.080Z", "0.8.3": "2014-01-08T05:10:15.204Z", "0.9.0": "2014-01-17T22:08:23.411Z", "0.9.1": "2014-03-17T18:07:48.348Z", "0.9.2": "2014-03-17T21:34:13.862Z", "0.10.0": "2014-03-21T05:10:05.169Z", "0.11.0": "2014-04-14T13:02:22.610Z", "0.12.0": "2014-04-30T14:35:01.601Z", "0.12.1": "2014-05-09T12:12:02.364Z", "0.13.0": "2014-05-23T07:08:00.362Z", "0.14.0": "2014-09-29T15:07:05.864Z", "0.15.0": "2014-11-11T16:58:46.712Z", "1.0.0": "2015-05-12T21:23:05.144Z", "1.0.1": "2015-05-13T15:56:09.503Z", "1.1.0": "2015-08-26T15:14:39.624Z", "1.2.0": "2016-02-11T15:29:12.092Z", "2.0.0": "2016-07-29T14:31:58.182Z", "2.0.1": "2016-10-19T15:35:23.431Z", "3.0.0": "2017-01-30T00:22:45.168Z", "3.1.0": "2018-05-13T13:17:40.500Z", "3.2.0": "2018-09-05T11:38:28.192Z", "3.3.0": "2018-09-06T20:25:40.266Z", "3.4.0": "2019-01-16T08:35:46.551Z", "3.4.1": "2019-01-16T12:27:19.011Z", "3.4.2": "2019-01-22T08:06:26.981Z", "4.0.0": "2019-03-09T11:14:11.111Z", "4.0.1": "2019-03-15T12:46:05.557Z", "4.0.2": "2019-03-15T13:04:21.125Z", "5.0.0-0": "2019-10-02T10:50:55.944Z", "5.0.0-1": "2020-09-25T12:44:12.946Z", "5.0.0": "2020-09-25T13:29:20.869Z", "6.0.0": "2020-10-28T18:41:30.731Z", "6.0.1": "2020-11-04T14:30:35.889Z", "6.1.0": "2021-01-15T15:02:45.604Z", "6.1.1": "2021-01-15T17:55:36.654Z", "6.1.2": "2021-01-25T05:50:29.158Z", "6.1.3": "2021-01-25T20:58:58.276Z", "6.1.4": "2021-07-21T04:16:32.908Z", "6.1.5": "2021-08-11T17:06:45.698Z", "6.1.6": "2021-08-17T20:16:30.670Z", "6.2.0": "2022-01-10T22:43:07.370Z", "6.2.1": "2022-01-11T22:26:26.903Z", "6.2.2": "2022-01-18T22:00:28.514Z", "6.2.3": "2022-04-26T23:52:31.160Z", "6.2.4": "2022-07-01T23:19:43.633Z", "6.3.0": "2022-10-04T02:55:07.554Z", "6.3.1": "2022-10-24T20:31:04.538Z", "6.3.2": "2022-12-02T21:35:14.223Z", "6.3.3": "2022-12-07T23:26:52.652Z", "6.3.4": "2024-01-14T16:41:50.357Z", "7.0.0": "2024-04-24T15:19:36.296Z", "7.1.0": "2025-03-20T15:48:07.565Z", "7.1.1": "2025-05-12T11:27:05.859Z", "7.1.2": "2025-07-07T18:48:53.472Z", "7.1.3": "2025-07-07T23:27:31.611Z", "7.1.4": "2025-07-22T23:22:04.554Z"}, "bugs": {"url": "https://github.com/ladjs/supertest/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "homepage": "https://github.com/ladjs/supertest#readme", "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "repository": {"type": "git", "url": "git+https://github.com/ladjs/supertest.git"}, "description": "SuperAgent driven library for testing HTTP servers", "contributors": [], "maintainers": [{"name": "niftylettuce", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kof", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "mikel<PERSON>", "email": "<EMAIL>"}, {"name": "titanism", "email": "<EMAIL>"}], "readme": "# [supertest](https://ladjs.github.io/superagent/)\n\n[![build status](https://github.com/forwardemail/supertest/actions/workflows/ci.yml/badge.svg)](https://github.com/forwardemail/supertest/actions/workflows/ci.yml)\n[![code coverage](https://img.shields.io/codecov/c/github/ladjs/supertest.svg)](https://codecov.io/gh/ladjs/supertest)\n[![code style](https://img.shields.io/badge/code_style-XO-5ed9c7.svg)](https://github.com/sindresorhus/xo)\n[![styled with prettier](https://img.shields.io/badge/styled_with-prettier-ff69b4.svg)](https://github.com/prettier/prettier)\n[![made with lass](https://img.shields.io/badge/made_with-lass-95CC28.svg)](https://lass.js.org)\n[![license](https://img.shields.io/github/license/ladjs/supertest.svg)](LICENSE)\n\n> HTTP assertions made easy via [superagent](http://github.com/ladjs/superagent).  Maintained for [Forward Email](https://github.com/forwardemail) and [Lad](https://github.com/ladjs).\n\n## About\n\nThe motivation with this module is to provide a high-level abstraction for testing\nHTTP, while still allowing you to drop down to the [lower-level API](https://ladjs.github.io/superagent/) provided by superagent.\n\n## Getting Started\n\nInstall supertest as an npm module and save it to your package.json file as a development dependency:\n\n```bash\nnpm install supertest --save-dev\n```\n\n  Once installed it can now be referenced by simply calling ```require('supertest');```\n\n## Example\n\nYou may pass an `http.Server`, or a `Function` to `request()` - if the server is not\nalready listening for connections then it is bound to an ephemeral port for you so\nthere is no need to keep track of ports.\n\nsupertest works with any test framework, here is an example without using any\ntest framework at all:\n\n```js\nconst request = require('supertest');\nconst express = require('express');\n\nconst app = express();\n\napp.get('/user', function(req, res) {\n  res.status(200).json({ name: 'john' });\n});\n\nrequest(app)\n  .get('/user')\n  .expect('Content-Type', /json/)\n  .expect('Content-Length', '15')\n  .expect(200)\n  .end(function(err, res) {\n    if (err) throw err;\n  });\n```\n\nTo enable http2 protocol, simply append an options to `request` or `request.agent`:\n\n```js\nconst request = require('supertest');\nconst express = require('express');\n\nconst app = express();\n\napp.get('/user', function(req, res) {\n  res.status(200).json({ name: 'john' });\n});\n\nrequest(app, { http2: true })\n  .get('/user')\n  .expect('Content-Type', /json/)\n  .expect('Content-Length', '15')\n  .expect(200)\n  .end(function(err, res) {\n    if (err) throw err;\n  });\n\nrequest.agent(app, { http2: true })\n  .get('/user')\n  .expect('Content-Type', /json/)\n  .expect('Content-Length', '15')\n  .expect(200)\n  .end(function(err, res) {\n    if (err) throw err;\n  });\n```\n\nHere's an example with mocha, note how you can pass `done` straight to any of the `.expect()` calls:\n\n```js\ndescribe('GET /user', function() {\n  it('responds with json', function(done) {\n    request(app)\n      .get('/user')\n      .set('Accept', 'application/json')\n      .expect('Content-Type', /json/)\n      .expect(200, done);\n  });\n});\n```\n\nYou can use `auth` method to pass HTTP username and password in the same way as in the [superagent](http://ladjs.github.io/superagent/#authentication):\n\n```js\ndescribe('GET /user', function() {\n  it('responds with json', function(done) {\n    request(app)\n      .get('/user')\n      .auth('username', 'password')\n      .set('Accept', 'application/json')\n      .expect('Content-Type', /json/)\n      .expect(200, done);\n  });\n});\n```\n\nOne thing to note with the above statement is that superagent now sends any HTTP\nerror (anything other than a 2XX response code) to the callback as the first argument if\nyou do not add a status code expect (i.e. `.expect(302)`).\n\nIf you are using the `.end()` method `.expect()` assertions that fail will\nnot throw - they will return the assertion as an error to the `.end()` callback. In\norder to fail the test case, you will need to rethrow or pass `err` to `done()`, as follows:\n\n```js\ndescribe('POST /users', function() {\n  it('responds with json', function(done) {\n    request(app)\n      .post('/users')\n      .send({name: 'john'})\n      .set('Accept', 'application/json')\n      .expect('Content-Type', /json/)\n      .expect(200)\n      .end(function(err, res) {\n        if (err) return done(err);\n        return done();\n      });\n  });\n});\n```\n\nYou can also use promises:\n\n```js\ndescribe('GET /users', function() {\n  it('responds with json', function() {\n    return request(app)\n      .get('/users')\n      .set('Accept', 'application/json')\n      .expect('Content-Type', /json/)\n      .expect(200)\n      .then(response => {\n         expect(response.body.email).toEqual('<EMAIL>');\n      })\n  });\n});\n```\n\nOr async/await syntax:\n\n```js\ndescribe('GET /users', function() {\n  it('responds with json', async function() {\n    const response = await request(app)\n      .get('/users')\n      .set('Accept', 'application/json')\n    expect(response.headers[\"Content-Type\"]).toMatch(/json/);\n    expect(response.status).toEqual(200);\n    expect(response.body.email).toEqual('<EMAIL>');\n  });\n});\n```\n\nExpectations are run in the order of definition. This characteristic can be used\nto modify the response body or headers before executing an assertion.\n\n```js\ndescribe('POST /user', function() {\n  it('user.name should be an case-insensitive match for \"john\"', function(done) {\n    request(app)\n      .post('/user')\n      .send('name=john') // x-www-form-urlencoded upload\n      .set('Accept', 'application/json')\n      .expect(function(res) {\n        res.body.id = 'some fixed id';\n        res.body.name = res.body.name.toLowerCase();\n      })\n      .expect(200, {\n        id: 'some fixed id',\n        name: 'john'\n      }, done);\n  });\n});\n```\n\nAnything you can do with superagent, you can do with supertest - for example multipart file uploads!\n\n```js\nrequest(app)\n  .post('/')\n  .field('name', 'my awesome avatar')\n  .field('complex_object', '{\"attribute\": \"value\"}', {contentType: 'application/json'})\n  .attach('avatar', 'test/fixtures/avatar.jpg')\n  ...\n```\n\nPassing the app or url each time is not necessary, if you're testing\nthe same host you may simply re-assign the request variable with the\ninitialization app or url, a new `Test` is created per `request.VERB()` call.\n\n```js\nrequest = request('http://localhost:5555');\n\nrequest.get('/').expect(200, function(err){\n  console.log(err);\n});\n\nrequest.get('/').expect('heya', function(err){\n  console.log(err);\n});\n```\n\nHere's an example with mocha that shows how to persist a request and its cookies:\n\n```js\nconst request = require('supertest');\nconst should = require('should');\nconst express = require('express');\nconst cookieParser = require('cookie-parser');\n\ndescribe('request.agent(app)', function() {\n  const app = express();\n  app.use(cookieParser());\n\n  app.get('/', function(req, res) {\n    res.cookie('cookie', 'hey');\n    res.send();\n  });\n\n  app.get('/return', function(req, res) {\n    if (req.cookies.cookie) res.send(req.cookies.cookie);\n    else res.send(':(')\n  });\n\n  const agent = request.agent(app);\n\n  it('should save cookies', function(done) {\n    agent\n    .get('/')\n    .expect('set-cookie', 'cookie=hey; Path=/', done);\n  });\n\n  it('should send cookies', function(done) {\n    agent\n    .get('/return')\n    .expect('hey', done);\n  });\n});\n```\n\nThere is another example that is introduced by the file [agency.js](https://github.com/ladjs/superagent/blob/master/test/node/agency.js)\n\nHere is an example where 2 cookies are set on the request.\n\n```js\nagent(app)\n  .get('/api/content')\n  .set('Cookie', ['nameOne=valueOne;nameTwo=valueTwo'])\n  .send()\n  .expect(200)\n  .end((err, res) => {\n    if (err) {\n      return done(err);\n    }\n    expect(res.text).to.be.equal('hey');\n    return done();\n  });\n```\n\n## API\n\nYou may use any [superagent](http://github.com/ladjs/superagent) methods,\nincluding `.write()`, `.pipe()` etc and perform assertions in the `.end()` callback\nfor lower-level needs.\n\n### .expect(status[, fn])\n\nAssert response `status` code.\n\n### .expect(status, body[, fn])\n\nAssert response `status` code and `body`.\n\n### .expect(body[, fn])\n\nAssert response `body` text with a string, regular expression, or\nparsed body object.\n\n### .expect(field, value[, fn])\n\nAssert header `field` `value` with a string or regular expression.\n\n### .expect(function(res) {})\n\nPass a custom assertion function. It'll be given the response object to check. If the check fails, throw an error.\n\n```js\nrequest(app)\n  .get('/')\n  .expect(hasPreviousAndNextKeys)\n  .end(done);\n\nfunction hasPreviousAndNextKeys(res) {\n  if (!('next' in res.body)) throw new Error(\"missing next key\");\n  if (!('prev' in res.body)) throw new Error(\"missing prev key\");\n}\n```\n\n### .end(fn)\n\nPerform the request and invoke `fn(err, res)`.\n\n## Notes\n\nInspired by [api-easy](https://github.com/flatiron/api-easy) minus vows coupling.\n\n## License\n\nMIT\n\n[coverage-badge]: https://img.shields.io/codecov/c/github/ladjs/supertest.svg\n[coverage]: https://codecov.io/gh/ladjs/supertest\n[travis-badge]: https://travis-ci.org/ladjs/supertest.svg?branch=master\n[travis]: https://travis-ci.org/ladjs/supertest\n[dependencies-badge]: https://david-dm.org/ladjs/supertest/status.svg\n[dependencies]: https://david-dm.org/ladjs/supertest\n[prs-badge]: https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square\n[prs]: http://makeapullrequest.com\n[license-badge]: https://img.shields.io/badge/license-MIT-blue.svg?style=flat-square\n[license]: https://github.com/ladjs/supertest/blob/master/LICENSE\n", "readmeFilename": "README.md", "users": {"f3r": true, "jwv": true, "pid": true, "swx": true, "vio": true, "viz": true, "5ika": true, "fibo": true, "j3kz": true, "japh": true, "kael": true, "kwan": true, "leor": true, "tztz": true, "vaju": true, "vwal": true, "wayn": true, "akiva": true, "ashco": true, "dnero": true, "ehrig": true, "fourq": true, "iseif": true, "jream": true, "jxson": true, "lqweb": true, "makay": true, "miloc": true, "ouq77": true, "panlw": true, "travm": true, "ugarz": true, "zorak": true, "71emj1": true, "ajduke": true, "alek-s": true, "augbog": true, "ctkdev": true, "daizch": true, "dankle": true, "dkblay": true, "edin-m": true, "figroc": true, "ga1989": true, "hualei": true, "kaycee": true, "kiknag": true, "knoja4": true, "legacy": true, "mestar": true, "mfjv88": true, "monjer": true, "mrbgit": true, "nauhil": true, "ngpvnk": true, "nicohe": true, "phajej": true, "potnox": true, "quafoo": true, "romajs": true, "royliu": true, "rwoody": true, "ryaned": true, "sgiant": true, "tarcio": true, "temasm": true, "toogle": true, "xlarsx": true, "yeming": true, "yinfxs": true, "zhoutk": true, "abhutch": true, "alanson": true, "asm2hex": true, "astesio": true, "bmearns": true, "cbeulke": true, "cooboor": true, "da5atar": true, "dnp1204": true, "feihide": true, "floppee": true, "fredtma": true, "fullrec": true, "hckhanh": true, "hitalos": true, "jerrywu": true, "json-ma": true, "juanf03": true, "karaoak": true, "klipsil": true, "korzhev": true, "liunian": true, "mars009": true, "nadimix": true, "nichoth": true, "pobrien": true, "runjinz": true, "sachacr": true, "seriewe": true, "subchen": true, "toszter": true, "yanghcc": true, "zaxnode": true, "abnerlin": true, "austinwo": true, "cyberx11": true, "dschnare": true, "dzhou777": true, "elrolito": true, "erikvold": true, "ghyghoo8": true, "hektve87": true, "hugovila": true, "iamninad": true, "ipodppod": true, "jaychase": true, "jfmercer": true, "jmsherry": true, "jon_shen": true, "jonathas": true, "joshberg": true, "leodutra": true, "leonzhao": true, "losymear": true, "madarche": true, "mhaidarh": true, "mluberry": true, "musikele": true, "ordinary": true, "petecemi": true, "pnevares": true, "pr-anoop": true, "sixertoy": true, "softwind": true, "ssljivic": true, "tregusti": true, "troy0820": true, "ttian226": true, "usedf295": true, "vchouhan": true, "voxpelli": true, "ycjcl868": true, "yournian": true, "abuelwafa": true, "alcovegan": true, "bian17888": true, "bredikhin": true, "chrisyipw": true, "cilindrox": true, "codeofzen": true, "dmckirnan": true, "elevenlui": true, "elviopita": true, "goliatone": true, "hagendorn": true, "hellboy81": true, "iceriver2": true, "icirellik": true, "jaycrypto": true, "jetbug123": true, "johndietz": true, "larrychen": true, "lvpeng101": true, "magemagic": true, "max_devjs": true, "ninozhang": true, "npmmurali": true, "redstrike": true, "rlafferty": true, "sasquatch": true, "sqrtthree": true, "swift2728": true, "tampham47": true, "tangiblej": true, "thedayman": true, "tmaximini": true, "vijkris99": true, "zeroth007": true, "ajohnstone": true, "angrykoala": true, "antoniordo": true, "cfleschhut": true, "cschmitz81": true, "earthling0": true, "fmoliveira": true, "greganswer": true, "johntbales": true, "jussipekka": true, "langri-sha": true, "leonardorb": true, "marco.jahn": true, "mark24code": true, "mattbodman": true, "monkeymonk": true, "nicomf1982": true, "oddjobsman": true, "princetoad": true, "qqqppp9998": true, "salvationz": true, "samclayton": true, "scotthorn0": true, "shentengtu": true, "stephenhuh": true, "zhangguixu": true, "zhanghaili": true, "a3.ivanenko": true, "antonnguyen": true, "cedrickchee": true, "chown_chmod": true, "codeinfront": true, "craigmorton": true, "easimonenko": true, "garenyondem": true, "icerainnuaa": true, "jamesbedont": true, "jbdoumenjou": true, "karlbateman": true, "rescommunes": true, "stonecypher": true, "tobyforever": true, "vparaskevas": true, "wangnan0610": true, "yeahoffline": true, "abhijitkalta": true, "ahmedfarooki": true, "bplabombarda": true, "darrentorpey": true, "dpjayasekara": true, "generalhenry": true, "ghostcode521": true, "ivan.marquez": true, "jamescostian": true, "kerimdzhanov": true, "lsxlsxxslxsl": true, "markscripter": true, "michaelfdias": true, "mpinteractiv": true, "mswanson1524": true, "nickeltobias": true, "processbrain": true, "samhwang1990": true, "shanemileham": true, "tobiasnickel": true, "toby_reynold": true, "walexstevens": true, "yonigoldberg": true, "zhangyaochun": true, "augiethornton": true, "crazyjingling": true, "domharrington": true, "geekforbrains": true, "markthethomas": true, "serge-nikitin": true, "superpaintman": true, "joshua.marquez": true, "karzanosman984": true, "santosharakere": true, "shanewholloway": true, "alexbaumgertner": true, "humbertoc_silva": true, "joaquin.briceno": true, "mauriciolauffer": true, "miguelprovencio": true, "carlosvillademor": true, "maciej.litwiniec": true, "rahulraghavankklm": true, "scott.m.sarsfield": true, "davidjsalazarmoreno": true, "obsessiveprogrammer": true, "programmer.severson": true}}