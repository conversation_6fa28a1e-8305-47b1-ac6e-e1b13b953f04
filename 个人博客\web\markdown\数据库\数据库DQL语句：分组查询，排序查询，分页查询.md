数据库DQL语句：分组查询，排序查询，分页查询

1分组查询

![1](C:\Users\<USER>\Pictures\Camera Roll\数据库\1.png)

![Snipaste_2024-10-21_08-31-17](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-31-17.png)

![Snipaste_2024-10-21_08-33-47](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-33-47.png)

![Snipaste_2024-10-21_08-34-37](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-34-37.png)

![Snipaste_2024-10-21_08-37-38](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-37-38.png)



2排序查询

![Snipaste_2024-10-21_08-44-40](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-44-40.png)

![Snipaste_2024-10-21_08-45-16](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-45-16.png)

![Snipaste_2024-10-21_08-47-37](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-47-37.png)

![Snipaste_2024-10-21_08-49-33](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-49-33.png)

3分页查询

![Snipaste_2024-10-21_08-52-00](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-52-00.png)

![Snipaste_2024-10-21_08-53-13](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_08-53-13.png)

![Snipaste_2024-10-21_09-03-53](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_09-03-53.png)

训练案例:

![Snipaste_2024-10-21_09-16-20](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_09-16-20.png)

模糊语句用like 或者 regexp 跟上字符与占位符

![Snipaste_2024-10-21_09-30-32](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_09-30-32.png)

![Snipaste_2024-10-21_09-32-30](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_09-32-30.png)

![Snipaste_2024-10-21_09-36-58](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_09-36-58.png)

![Snipaste_2024-10-21_09-39-53](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_09-39-53.png)

DQL执行顺序

![Snipaste_2024-10-21_09-42-59](C:\Users\<USER>\Pictures\Camera Roll\数据库\Snipaste_2024-10-21_09-42-59.png)

也即条件(where条件，分组条件)-筛选-排序-分页
