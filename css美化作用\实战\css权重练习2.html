<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .nav li{
            color: red;
        }

        .nav .y1 {
            color: pink;
        }
    </style>
</head>

<body>
    <ul class="nav">
        <!-- nav后面li标签权重是11,.nav .y1标签是20 ，用.nav #y1也可以，！important也可以，只要第一个li标签的权重超过其他的即可-->
        <li class="y1">人生四大悲</li>
        <li>家里没宽带</li>
        <li>网速不够快</li>
        <li>手机没流量</li>
        <li>学校没wifi</li>
    </ul>
</body>

</html>