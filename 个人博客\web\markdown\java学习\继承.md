# 继承:

## 继承是对类的抽象：根据类中共同拥有的属性，比如下面的共有属性是姓名，年龄，身高，方法：吃饭睡觉说话，将下面的类抽象出来叫基类或者是父类，这里是人类,这里是继承的关系

![image-20241127153057672](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241127153057672.png)

![image-20241127153840646](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241127153840646.png)

![image-20241127154741133](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241127154741133.png)

```java
//父类
package 继承;

public class person {
    private String name;
    private int age;
    private double height;

    public String getName() {
        return name;
    }

    public int getAge() {
        return age;
    }

    public double getHeight() {
        return height;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setHeight(double height) {
        this.height = height;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public void eat()
    {
        System.out.println("去码头整点薯条");
    }
    public void talk()
    {
        System.out.println("我爱说实话");
    }
    public void sleep()
    {
        System.out.println("一天到晚没事就是睡");
    }


    public person() {
    }
}

```



```java
//子类的写法
package 继承;

public class student extends person {
    //定义学生额外的属性以及方法
    private int sno;//学生编号

    public int getSno() {
        return sno;
    }

    public void setSno(int sno) {
        this.sno = sno;
    }

    public void study()
    {
        System.out.println("直接开学");
    }

}



```

```java
package 继承;

public class test
{
    public static void main(String[] args) {
        student s=new student();
        s.setSno(21);
        s.setAge(20);//继承的方法
        s.setHeight(185.00);
        s.setName("颜小涵");
        int sno=s.getSno();
        System.out.println(sno);
        s.study();
        s.eat();
        s.sleep();
        s.talk();
    }
}

```



# 继承:

1.我们先写一个基类，定义共同的属性以及方法，然后再定义子类

2.子类中继承是这么写的，public class student extends person{}

3.继承就是指继承父类中所有的属性及方法，但是私有的还是没法直接调用