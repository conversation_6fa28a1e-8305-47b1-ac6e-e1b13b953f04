<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fibonacci Debugging</title>
</head>

<body>
    <script>
        function* fibonacci() {
            let [prev, curr] = [0, 1];
            for (; ;) {
                yield curr
                [prev, curr] = [curr, prev + curr];
            }
        }

        for (let n of fibonacci()) {
            if (n > 1000) break;
            console.log(n);
        }
    </script>
</body>

</html>