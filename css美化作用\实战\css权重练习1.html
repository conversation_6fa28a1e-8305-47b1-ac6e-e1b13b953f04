<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>css权重练习</title>
    <style>
        .nav {
            color: red;
        }

        li {
            color: pink;
        }
    </style>
</head>

<body>
    <ul class="nav">
        <!-- .nav后代li是继承权重为0,不如类选择器权重为1 ，所以成粉色-->
        <li>人生四大悲</li>
        <li>家里没宽带</li>
        <li>网速不够快</li>
        <li>手机没流量</li>
        <li>学校没wifi</li>
    </ul>
</body>

</html>