#include <stdio.h>
#include <stdlib.h>

#define MAX_N 100 // 假设数塔的最大层数为100

void findMaxPath(int n, int data[MAX_N][MAX_N]) {
    int dp[MAX_N][MAX_N]; // 动态规划表
    int path[MAX_N];      // 存储路径
    int i, j;

    // 初始化动态规划表的最后一层
    for (j = 0; j < n; j++) {
        dp[n - 1][j] = data[n - 1][j];
    }

    // 从倒数第二层开始向上计算
    for (i = n - 2; i >= 0; i--) {
        for (j = 0; j <= i; j++) {
            if (dp[i + 1][j] > dp[i + 1][j + 1]) {
                dp[i][j] = dp[i + 1][j] + data[i][j];
            } else {
                dp[i][j] = dp[i + 1][j + 1] + data[i][j];
            }
        }
    }

    // 最大路径和
    printf("max=%d\n", dp[0][0]);

    // 回溯路径
    int col = 0;
    path[0] = data[0][0];
    for (i = 1; i < n; i++) {
        if (dp[i][col] < dp[i][col + 1]) {
            col++;
        }
        path[i] = data[i][col];
    }

    // 输出路径
    printf("数值和最大的路径是：");
    for (i = 0; i < n; i++) {
        if (i > 0) {
            printf("->");
        }
        printf("%d", path[i]);
    }
    printf("\n");
}

int main() {
    int n;
    printf("请输入数塔的层数: ");
    if (scanf("%d", &n) != 1 || n <= 0 || n > MAX_N) { // 修复输入验证逻辑
        printf("输入无效或层数超过最大限制%d\n", MAX_N);
        return 1;
    }

    int data[MAX_N][MAX_N];
    printf("请输入数塔中的数据:\n");
    for (int i = 0; i < n; i++) {
        for (int j = 0; j <= i; j++) {
            if (scanf("%d", &data[i][j]) != 1) { // 验证输入是否有效
                printf("输入无效，请重新运行程序。\n");
                return 1;
            }
        }
    }

    findMaxPath(n, data);

    return 0;
}
