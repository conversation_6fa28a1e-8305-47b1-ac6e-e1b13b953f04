package com.weather.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class City {
    private int id;
    @JsonProperty("cityName")
    private String cityName;
    @JsonProperty("province")
    private String province;
    @JsonProperty("country")
    private String country;
    @JsonProperty("latitude")
    private double latitude;
    @JsonProperty("longitude")
    private double longitude;
    @JsonProperty("isHot")
    private boolean isHot;
    
    // Constructors
    public City() {}
    
    public City(String cityName, String province, boolean isHot) {
        this.cityName = cityName;
        this.province = province;
        this.isHot = isHot;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getCityName() { return cityName; }
    public void setCityName(String cityName) { this.cityName = cityName; }
    
    public String getProvince() { return province; }
    public void setProvince(String province) { this.province = province; }
    
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    
    public double getLatitude() { return latitude; }
    public void setLatitude(double latitude) { this.latitude = latitude; }
    
    public double getLongitude() { return longitude; }
    public void setLongitude(double longitude) { this.longitude = longitude; }
    
    public boolean isHot() { return isHot; }
    public void setHot(boolean hot) { isHot = hot; }
}