{"_id": "destroy", "_rev": "21-7eca78d9307dd918a13888cac66169b1", "name": "destroy", "time": {"modified": "2022-06-15T01:49:38.799Z", "created": "2013-10-03T17:12:42.209Z", "0.0.0": "2013-10-03T17:12:45.167Z", "1.0.3": "2014-08-15T06:30:06.962Z", "1.0.4": "2016-01-16T03:14:05.899Z", "1.1.0": "2022-01-25T18:57:27.526Z", "1.1.1": "2022-02-28T20:11:00.367Z", "1.2.0": "2022-03-20T19:03:12.452Z"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist-tags": {"latest": "1.2.0"}, "description": "destroy a stream if possible", "readme": "# destroy\n\n[![NPM version][npm-image]][npm-url]\n[![Build Status][github-actions-ci-image]][github-actions-ci-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\nDestroy a stream.\n\nThis module is meant to ensure a stream gets destroyed, handling different APIs\nand Node.js bugs.\n\n## API\n\n```js\nvar destroy = require('destroy')\n```\n\n### destroy(stream [, suppress])\n\nDestroy the given stream, and optionally suppress any future `error` events.\n\nIn most cases, this is identical to a simple `stream.destroy()` call. The rules\nare as follows for a given stream:\n\n  1. If the `stream` is an instance of `ReadStream`, then call `stream.destroy()`\n     and add a listener to the `open` event to call `stream.close()` if it is\n     fired. This is for a Node.js bug that will leak a file descriptor if\n     `.destroy()` is called before `open`.\n  2. If the `stream` is an instance of a zlib stream, then call `stream.destroy()`\n     and close the underlying zlib handle if open, otherwise call `stream.close()`.\n     This is for consistency across Node.js versions and a Node.js bug that will\n     leak a native zlib handle.\n  3. If the `stream` is not an instance of `Stream`, then nothing happens.\n  4. If the `stream` has a `.destroy()` method, then call it.\n\nThe function returns the `stream` passed in as the argument.\n\n## Example\n\n```js\nvar destroy = require('destroy')\n\nvar fs = require('fs')\nvar stream = fs.createReadStream('package.json')\n\n// ... and later\ndestroy(stream)\n```\n\n[npm-image]: https://img.shields.io/npm/v/destroy.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/destroy\n[github-tag]: http://img.shields.io/github/tag/stream-utils/destroy.svg?style=flat-square\n[github-url]: https://github.com/stream-utils/destroy/tags\n[coveralls-image]: https://img.shields.io/coveralls/stream-utils/destroy.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/stream-utils/destroy?branch=master\n[license-image]: http://img.shields.io/npm/l/destroy.svg?style=flat-square\n[license-url]: LICENSE.md\n[downloads-image]: http://img.shields.io/npm/dm/destroy.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/destroy\n[github-actions-ci-image]: https://img.shields.io/github/workflow/status/stream-utils/destroy/ci/master?label=ci&style=flat-square\n[github-actions-ci-url]: https://github.com/stream-utils/destroy/actions/workflows/ci.yml\n", "versions": {"1.0.3": {"name": "destroy", "description": "destroy a stream if possible", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/stream-utils/destroy"}, "devDependencies": {"istanbul": "0", "mocha": "1"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js"], "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"], "gitHead": "50af95ece4a70202f9301bc3edc8f9fdbbad0f26", "bugs": {"url": "https://github.com/stream-utils/destroy/issues"}, "homepage": "https://github.com/stream-utils/destroy", "_id": "destroy@1.0.3", "_shasum": "b433b4724e71fd8551d9885174851c5fc377e2c9", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "b433b4724e71fd8551d9885174851c5fc377e2c9", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.0.3.tgz", "integrity": "sha512-KB/AVLKRwZPOEo6/lxkDJ+Bv3jFRRrhmnRMPvpWwmIfUggpzGkQBqolyo8FRf833b/F5rzmy1uVN3fHBkjTxgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFeit2JREMnGo+lyI6yoifcGwz03uckJGwbMOYfvRnHzAiALOmJCbvzQggmzmPybp1dsmmBlKgK+mVGpSEQMu2Wojw=="}]}, "directories": {}}, "1.0.4": {"name": "destroy", "description": "destroy a stream if possible", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/stream-utils/destroy"}, "devDependencies": {"istanbul": "0.4.2", "mocha": "2.3.4"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "files": ["index.js", "LICENSE"], "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"], "gitHead": "86edea01456f5fa1027f6a47250c34c713cbcc3b", "bugs": {"url": "https://github.com/stream-utils/destroy/issues"}, "homepage": "https://github.com/stream-utils/destroy", "_id": "destroy@1.0.4", "_shasum": "978857442c44749e4206613e37946205826abd80", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "978857442c44749e4206613e37946205826abd80", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz", "integrity": "sha512-3NdhDuEXnfun/z7x9GOElY49LoqVHoGScmOKwmxhsS8N5Y+Z8KyPPDnaSzqWgYt/ji4mqwfTS34Htrk0zPIXVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBpKO/Fv1wYVlXGPq1EyWJQ2ypd+HpOairW5TRyVAi+2AiBf1Jz1H3kLevLMb51WDLm7K7F/gFlOd3LA00/uEgn/Sg=="}]}, "directories": {}}, "1.1.0": {"name": "destroy", "description": "destroy a stream if possible", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/destroy.git"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"], "gitHead": "40e874982939d8d0b04afc8d1576307842b000d2", "bugs": {"url": "https://github.com/stream-utils/destroy/issues"}, "homepage": "https://github.com/stream-utils/destroy#readme", "_id": "destroy@1.1.0", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-R5QZrOXxSs0JDUIU/VANvRJlQVMts9C0L76HToQdPdlftfZCE7W6dyH0G4GZ5UW9fRqUOhAoCE2aGekuu+3HjQ==", "shasum": "b77ae22e472d85437141319d32ae40b344dff38a", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.1.0.tgz", "fileCount": 4, "unpackedSize": 7604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8EgXCRA9TVsSAnZWagAAN0AP/099LQLye0ezRKzDBFO1\n9sTqui6s8gk3Bve4NE4hRLHiASPiR4sQ9ny2PvHkilR8q0ybTFNODcj/3BPC\nLcvB8XpT1aTs04tIOw2WUNHA738PhG/TQDy8nDZanA2ZKOAESBZRAJyx8iHw\n2pTJC0brDshttzulZWfQG13lpAMH3hQsId+yKHShTu6gpbSfE2FiJicxpzHP\nFkBb30yLGA8TOhd4wKnsso32ppgMa3otipkySg2W64E8KvGFPWtFmTpmJEyE\nyKX1vg0A5Q2+6Jhq82UaTiwrjSrEVLgHrIUp/00Wr5lfzfLOIe2Xqng8oE1z\naVa0aXAzxKSIuyL6bE8LEwLgz7zbVil9bI8xUFo1XEgrwc6/YQds/5xHbIkU\n7Ecyxeb7LBtpIVdOdXd04Ahs+SvOnRaO4sjKLAgmfmHD9Vf/+RFBpwHT1Ces\nhlGnHQMXvBoaExc2PZw22JKZ0ZKsHHycE3C8lefRxa2zA6ZoqGoYFX6unwij\n1zaXqFkOEOEW9Jw/hyEyFTsDMbhye84XdA/eQs0PV+de3kIevsglZYSkmUbw\njS71+tRDVePB41i4I/ovYseC+7wmC5InmebK0ROGJ3G0Ba1+fc9S9haANbxq\nMQjZHBiEODSfR1jMFnSZbxhcGk0NWYwfkzkq9l+SjH1iLM3BleNmYZ5veUzC\nOG+F\r\n=VXVf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFqMP66wjv4rJ89sHGF7xzFRWFZFvzwiAYPjhb5KKCutAiEAkiTx/rOxl3cJSoAYiW8zIcYgHMatmeMy1Os+wJI4/ZI="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/destroy_1.1.0_1643137047375_0.3416969550105049"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "destroy", "description": "destroy a stream if possible", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/destroy.git"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"], "gitHead": "b7fee52bfa3f7590a1fc444ffc5356035e82b6ca", "bugs": {"url": "https://github.com/stream-utils/destroy/issues"}, "homepage": "https://github.com/stream-utils/destroy#readme", "_id": "destroy@1.1.1", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-jxwFW+yrVOLdwqIWvowFOM8UPdhZnvOF6mhXQQLXMxBDLtv2JVJlVJPEwkDv9prqscEtGtmnxuuI6pQKStK1vA==", "shasum": "38a65ed2f2615ad12bf59c6b5e885512c0cf13dd", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.1.1.tgz", "fileCount": 4, "unpackedSize": 8176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHSxUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeOw/+MojKAAoQ23Kz7o5dWOfB68l3dZvX+3m82sDgV30x6K4kAiMW\r\nDBQEA44Fl1+Vs8jhoNklFwz2RgkOZ3DQuMgN1I8sPc5ShzJ3Jvybjmhb0vXu\r\nODquOv8TchRuvxFAz6GmUziWuEFSYex0jkX2qm9eylhNxVxA+LAAmOSNJI56\r\nVnihLzGTEabvj09cja4F1JmgzoAKTf7Me3z0ac+lUNVRMLZ9rgKC03670xJr\r\n+YzbLKUwYuiNU9yrUVK4S/8qxcHorWMfzPe6Y1itMksq/Tfno2lFSoyBAZjq\r\nMbeXPVRTUVUpXkMIgPEkpink/WWGtDBASiiWslej7O8qvGTD5IccItvLl8hH\r\ndAOvXIWZuA1GTNhI5iQKFc9T5FZTl8qwKuyJgRJRZtq7dcZ7f8OG3OyCtvzg\r\nm+WS1myI7L2srKhN0l0Ujki9YJZjNiKaYuszooKPetCqjyHi8XPhnf2gawGJ\r\n2JeiFC9lYGGNDX0V8vUSaoSua7dC5Wrn1zBaKodbhSro9O5dPFa8zxS7NT0R\r\nyM/afWmetaL2eFYjHC+BhYsAXabyCIrvpjsHM/XNEBWaUaND73uwQ57m4w5V\r\n2vvjUhV/GfepntH3jrS8VKHy4knuxwS8P6ygXm+GJEVfZd9o2T4qUBnyR7qb\r\nTASRLNiaZEbBLL8weXJogOE8SiHZ5WjQXZ0=\r\n=tpo+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFeiIZ6BCzTB2Ibcc35m9sYhye0gIs5Gpctt8p2bi53SAiEAg7GmCElyD1czRCdNZcGUkqxsZkl/bjRHcxl3FEApvmw="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/destroy_1.1.1_1646079060228_0.06464213033636246"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "destroy", "description": "destroy a stream if possible", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/stream-utils/destroy.git"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"], "gitHead": "f387a486a42869ab8ef1c37a90922925063aeb57", "bugs": {"url": "https://github.com/stream-utils/destroy/issues"}, "homepage": "https://github.com/stream-utils/destroy#readme", "_id": "destroy@1.2.0", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "shasum": "4803735509ad8be552934c67df614f94e66fa015", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "fileCount": 4, "unpackedSize": 9018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiN3pwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2ZRAAjcRX9pSm0kfICnOCjFla9HKKvzVVXwMNJQ0cPWAN2Q4/sCr/\r\nsnQY0aTN7TxYd2FdsTUS6V1WVfVMQZCCJbSuIAEVuSZaIQx+GPVnPpOTQyG5\r\n+gksV1GS2TC5gg/CqHeXJCZ+I9SVpLK+PiUiGojODYqdLWOrtGSIJepYAkLL\r\nZPJ/YBFeVisOFETdmjOYj43l6+cru0WR21AUJEjBFhiiXxi2ex4B71l4f0rm\r\nNATSmlPKX7m/Wq/1A85wR1V8RHUU8dFS9SQ9ZpqaHUvTzfQ89KdEzweEsQg3\r\nLbUwuf3tVUPv1IWKT3rZLeh+2XF4ycDRwIA/u8FtiQ2fNTeIjEj8iAbi+Gwq\r\nAx+tQuLh28XH0maLrWYbgTo6Jph+s32AdHLfgdAYnz9xlNOiyKmvfI9QCBs+\r\no3SGVOW20u5YvGNL7+kh2UgACexrOG7aZmAJQ6SOegxlKK7ZzbHEFVqLAjTz\r\n8sDm+WAgbbm5jvDKoM+dW232t/zlvhyuilGbfNgNIpCe+JQ8voXaNEK3L8Ek\r\no8SYW+iaD54tNMSivIChGSpH+L8LWk5Q5zzZ3XIRiEt3tqhNUfWzFoXvlroU\r\ndhPVXDfKMjMCkFzpNfB0NYV7RpRTGa47XAMG7t8J2Ag6wAjd1kWdehZlxH5y\r\nZ0v+ieCqjGkKESD1U51GvxC7WYlGzxk3lMc=\r\n=uElj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAvgBzNFFtjdCvxlY7mn7G0Iibc5t2EE7fAlK+zlw/jfAiAmhIErvReywAyY5drbpZj4xVSVfDeVfAcU8T1j2KJRzA=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/destroy_1.2.0_1647802992075_0.9023270978475559"}, "_hasShrinkwrap": false}}, "homepage": "https://github.com/stream-utils/destroy#readme", "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"], "repository": {"type": "git", "url": "git+https://github.com/stream-utils/destroy.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/stream-utils/destroy/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"goodseller": true, "kankungyip": true, "mojaray2k": true, "chaoliu": true}}