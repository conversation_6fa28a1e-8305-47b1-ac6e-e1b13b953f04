/* styles.css */
* {
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    background-color: #b31b11;
    background-image: url(../images/page4.png);
    background-size: 100%, 100%;
    margin: 0;
    padding: 0;
}

header {
    background: rbga(255, 255, 255, 0);
    color: white;
    padding: 10px 0;
    text-align: center;
}

nav ul {
    list-style-type: none;
    padding: 0;
}

nav ul li {
    display: inline;
    margin: 0 15px;
    list-style: none;
}

nav a {
    color: white;
    text-decoration: none;
}

nav a:hover {
    text-decoration: underline;
}

main {
    padding: 20px;
    text-align: center;
}

footer {
    text-align: center;
    padding: 10px 0;
    background: rgba(255, 255, 255, 0);
    color: white;
    position: fixed;
    bottom: 0;
    width: 100%;
}

/* 整体页面布局相关的样式设置，这里假设你已经有了一些基础的样式设置，以下是新增的部分 */

/* 设置盒子容器的样式，使其子元素（盒子）能够排列成一行四个 */
.box-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
}

/* 单独设置每个box的背景属性及其他样式 */
#box1 {
    width: 200px;
    height: 200px;
    margin: 10px;
    border: 1px solid #ccc;
    text-align: center;
    background-color: #f4f4f4;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('../images/常用网站.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#box1:hover {
    background-color: #e6e6e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#box1.inner-box {
    width: 80%;
    height: 80%;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

#box2 {
    width: 200px;
    height: 200px;
    margin: 10px;
    border: 1px solid #ccc;
    text-align: center;
    background-color: #f4f4f4;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    background-image: url('../images/douyin.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#box2:hover {
    background-color: #e6e6e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#box2.inner-box {
    width: 80%;
    height: 80%;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

#box3 {
    width: 200px;
    height: 200px;
    margin: 10px;
    border: 1px solid #ccc;
    text-align: center;
    background-color: #f4f4f4;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('../images/js.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#box3:hover {
    background-color: #e6e6e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#box3.inner-box {
    width: 80%;
    height: 80%;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

#box4 {
    width: 200px;
    height: 200px;
    margin: 10px;
    border: 1px solid #ccc;
    text-align: center;
    background-color: #f4f4f4;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('../images/算法.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#box4:hover {
    background-color: #e6e6e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#box4.inner-box {
    width: 80%;
    height: 80%;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

#box5 {
    width: 200px;
    height: 200px;
    margin: 10px;
    border: 1px solid #ccc;
    text-align: center;
    background-color: #f4f4f4;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('../images/bilibili.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#box5:hover {
    background-color: #e6e6e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#box5.inner-box {
    width: 80%;
    height: 80%;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

#box6 {
    width: 200px;
    height: 200px;
    margin: 10px;
    border: 1px solid #ccc;
    text-align: center;
    background-color: #f4f4f4;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('../images/雨课堂.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#box6:hover {
    background-color: #e6e6e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#box6.inner-box {
    width: 80%;
    height: 80%;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

#box7 {
    width: 200px;
    height: 200px;
    margin: 10px;
    border: 1px solid #ccc;
    text-align: center;
    background-color: #f4f4f4;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('bg_image7.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#box7:hover {
    background-color: #e6e6e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#box7.inner-box {
    width: 80%;
    height: 80%;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

#box8 {
    width: 200px;
    height: 200px;
    margin: 10px;
    border: 1px solid #ccc;
    text-align: center;
    background-color: #f4f4f4;
    cursor: pointer;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-image: url('bg_image8.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#box8:hover {
    background-color: #e6e6e6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#box8.inner-box {
    width: 80%;
    height: 80%;
    margin: auto;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}