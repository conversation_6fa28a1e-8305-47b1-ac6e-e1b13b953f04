{"_id": "es-object-atoms", "_rev": "3-c974d2127b54dfc4a2fae7614342fcfc", "name": "es-object-atoms", "dist-tags": {"latest": "1.1.1"}, "versions": {"1.0.0": {"name": "es-object-atoms", "version": "1.0.0", "keywords": ["javascript", "ecmascript", "object", "toobject", "coercible"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-object-atoms@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/es-object-atoms#readme", "bugs": {"url": "https://github.com/ljharb/es-object-atoms/issues"}, "dist": {"shasum": "ddb55cd47ac2e240701260bc2a8e31ecb643d941", "tarball": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.0.0.tgz", "fileCount": 14, "integrity": "sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==", "signatures": [{"sig": "MEYCIQDMdYhKH0LKyi7qy0uW6r2RvFCpCa4P3JbD/VVNXxPXSQIhALsh9F93vAXqFWBqrSSs0qDg1qmJ+fnaxJaVJwxaHI7K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9166}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./ToObject": "./ToObject.js", "./package.json": "./package.json", "./RequireObjectCoercible": "./RequireObjectCoercible.js"}, "gitHead": "34cb6da2da31818d011df9837188bd653ebd62bb", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/es-object-atoms.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "ES Object-related atoms: Object, ToObject, RequireObjectCoercible", "directories": {}, "sideEffects": false, "_nodeVersion": "21.7.0", "dependencies": {"es-errors": "^1.3.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"aud": "^2.0.4", "nyc": "^10.3.2", "tape": "^5.7.5", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.4", "auto-changelog": "^2.4.0", "@ljharb/tsconfig": "^0.2.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/es-object-atoms_1.0.0_1710656343176_0.9493742507070562", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "es-object-atoms", "version": "1.0.1", "keywords": ["javascript", "ecmascript", "object", "toobject", "coercible"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-object-atoms@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/es-object-atoms#readme", "bugs": {"url": "https://github.com/ljharb/es-object-atoms/issues"}, "dist": {"shasum": "ecdf38b6784b194d38065df324300bbf515c73ed", "tarball": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.0.1.tgz", "fileCount": 14, "integrity": "sha512-BPOBuyUF9QIVhuNLhbToCLHP6+0MHwZ7xLBkPPCZqK4JmpJgGnv10035STzzQwFpqdzNFMB3irvDI63IagvDwA==", "signatures": [{"sig": "MEQCIDYA9zFREDE3sdLtfNBxcOm2dmLUhdkg6LVryyK3fKcUAiB2grjYYA5wA1R/1JMGibZIJH1QeLi82MxpHCF9j1xQSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9877}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./ToObject": "./ToObject.js", "./package.json": "./package.json", "./RequireObjectCoercible": "./RequireObjectCoercible.js"}, "gitHead": "1626a072fc23d52a8c8f0add14fd013789c39601", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "npx npm@\">= 10.2\" audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/es-object-atoms.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "ES Object-related atoms: Object, ToObject, RequireObjectCoercible", "directories": {}, "sideEffects": false, "_nodeVersion": "23.6.0", "dependencies": {"es-errors": "^1.3.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.3", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/es-object-atoms_1.0.1_1736827931880_0.1438068059308797", "host": "s3://npm-registry-packages-npm-production"}}, "1.1.0": {"name": "es-object-atoms", "version": "1.1.0", "keywords": ["javascript", "ecmascript", "object", "toobject", "coercible"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "es-object-atoms@1.1.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/es-object-atoms#readme", "bugs": {"url": "https://github.com/ljharb/es-object-atoms/issues"}, "dist": {"shasum": "095de9ecceeb2ca79668212b60ead450ffd323bf", "tarball": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.0.tgz", "fileCount": 16, "integrity": "sha512-Ujz8Al/KfOVR7fkaghAB1WvnLsdYxHDWmfoi2vlA2jZWRg31XhIC1a4B+/I24muD8iSbHxJ1JkrfqmWb65P/Mw==", "signatures": [{"sig": "MEYCIQCljQoRbvzAdn/1yL6yNJwsIJDY1qh1YO3L9CfJd1A+fAIhANo7lDlBe3xWl/P936kWi+fxMrlwQxgACLzPBM08q+Fv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10836}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./ToObject": "./ToObject.js", "./isObject": "./isObject.js", "./package.json": "./package.json", "./RequireObjectCoercible": "./RequireObjectCoercible.js"}, "gitHead": "a59cb4db53204065e7c7eaa96550e180112cb340", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "npx npm@\">= 10.2\" audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ljharb/es-object-atoms.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "ES Object-related atoms: Object, ToObject, RequireObjectCoercible", "directories": {}, "sideEffects": false, "_nodeVersion": "23.6.0", "dependencies": {"es-errors": "^1.3.0"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "^8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.3", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/es-object-atoms_1.1.0_1736869242300_0.****************", "host": "s3://npm-registry-packages-npm-production"}}, "1.1.1": {"name": "es-object-atoms", "version": "1.1.1", "description": "ES Object-related atoms: Object, ToObject, RequireObjectCoercible", "main": "index.js", "exports": {".": "./index.js", "./RequireObjectCoercible": "./RequireObjectCoercible.js", "./isObject": "./isObject.js", "./ToObject": "./ToObject.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "npx npm@\">= 10.2\" audit --production", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-object-atoms.git"}, "keywords": ["javascript", "ecmascript", "object", "toobject", "coercible"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/es-object-atoms/issues"}, "homepage": "https://github.com/ljharb/es-object-atoms#readme", "dependencies": {"es-errors": "^1.3.0"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "es-object-atoms@1.1.1", "gitHead": "fe8db15106acf15abec2e76818d1aa4713293109", "types": "./index.d.ts", "_nodeVersion": "23.6.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "shasum": "1c4f2c4837327597ce69d2ca190a7fdd172338c1", "tarball": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "fileCount": 16, "unpackedSize": 11442, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC85u8dWki0BxJdBEGY33ZvpWVFw17eA3vYZFHA5Y1qIgIhAM0sqBvSeAPuAZuTdLt2t6SB7PKjDPDnfQtZrxRGIpyR"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/es-object-atoms_1.1.1_1736901763173_0.5406383546222293"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-03-17T06:19:03.175Z", "modified": "2025-01-15T00:42:43.521Z", "1.0.0": "2024-03-17T06:19:03.345Z", "1.0.1": "2025-01-14T04:12:12.122Z", "1.1.0": "2025-01-14T15:40:42.459Z", "1.1.1": "2025-01-15T00:42:43.342Z"}, "bugs": {"url": "https://github.com/ljharb/es-object-atoms/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/es-object-atoms#readme", "keywords": ["javascript", "ecmascript", "object", "toobject", "coercible"], "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-object-atoms.git"}, "description": "ES Object-related atoms: Object, ToObject, RequireObjectCoercible", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# es-object-atoms <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nES Object-related atoms: Object, ToObject, RequireObjectCoercible.\n\n## Example\n\n```js\nconst assert = require('assert');\n\nconst $Object = require('es-object-atoms');\nconst isObject = require('es-object-atoms/isObject');\nconst ToObject = require('es-object-atoms/ToObject');\nconst RequireObjectCoercible = require('es-object-atoms/RequireObjectCoercible');\n\nassert.equal($Object, Object);\nassert.throws(() => ToObject(null), TypeError);\nassert.throws(() => ToObject(undefined), TypeError);\nassert.throws(() => RequireObjectCoercible(null), TypeError);\nassert.throws(() => RequireObjectCoercible(undefined), TypeError);\n\nassert.equal(isObject(undefined), false);\nassert.equal(isObject(null), false);\nassert.equal(isObject({}), true);\nassert.equal(isObject([]), true);\nassert.equal(isObject(function () {}), true);\n\nassert.deepEqual(RequireObjectCoercible(true), true);\nassert.deepEqual(ToObject(true), Object(true));\n\nconst obj = {};\nassert.equal(RequireObjectCoercible(obj), obj);\nassert.equal(ToObject(obj), obj);\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n## Security\n\nPlease email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.\n\n[package-url]: https://npmjs.org/package/es-object-atoms\n[npm-version-svg]: https://versionbadg.es/ljharb/es-object-atoms.svg\n[deps-svg]: https://david-dm.org/ljharb/es-object-atoms.svg\n[deps-url]: https://david-dm.org/ljharb/es-object-atoms\n[dev-deps-svg]: https://david-dm.org/ljharb/es-object-atoms/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/es-object-atoms#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/es-object-atoms.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/es-object-atoms.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/es-object.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=es-object-atoms\n[codecov-image]: https://codecov.io/gh/ljharb/es-object-atoms/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/es-object-atoms/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/es-object-atoms\n[actions-url]: https://github.com/ljharb/es-object-atoms/actions\n", "readmeFilename": "README.md"}