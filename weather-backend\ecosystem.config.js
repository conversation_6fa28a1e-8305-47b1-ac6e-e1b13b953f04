// PM2 配置文件
module.exports = {
  apps: [
    {
      name: 'weather-backend',
      script: 'src/app.js',
      instances: 'max', // 使用所有可用的 CPU 核心
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 监控配置
      monitoring: false,
      
      // 重启配置
      max_restarts: 10,
      min_uptime: '10s',
      
      // 内存限制
      max_memory_restart: '500M',
      
      // 自动重启配置
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      
      // 优雅关闭
      kill_timeout: 5000
    }
  ]
};
