{"_id": "@types/istanbul-lib-coverage", "_rev": "512-a69c331649beaaac02ef02a0b98e397c", "name": "@types/istanbul-lib-coverage", "dist-tags": {"ts2.4": "2.0.1", "ts2.5": "2.0.1", "ts2.6": "2.0.1", "ts2.7": "2.0.1", "ts2.8": "2.0.1", "ts2.9": "2.0.1", "ts3.0": "2.0.3", "ts3.1": "2.0.3", "ts3.2": "2.0.3", "ts3.3": "2.0.3", "ts3.4": "2.0.3", "ts3.5": "2.0.3", "ts3.6": "2.0.3", "ts3.7": "2.0.3", "ts3.8": "2.0.4", "ts3.9": "2.0.4", "ts4.0": "2.0.4", "ts4.1": "2.0.4", "ts4.2": "2.0.4", "ts4.3": "2.0.4", "ts4.4": "2.0.4", "ts5.8": "2.0.6", "ts5.7": "2.0.6", "latest": "2.0.6", "ts4.5": "2.0.6", "ts4.6": "2.0.6", "ts4.7": "2.0.6", "ts4.8": "2.0.6", "ts4.9": "2.0.6", "ts5.0": "2.0.6", "ts5.1": "2.0.6", "ts5.2": "2.0.6", "ts5.3": "2.0.6", "ts5.4": "2.0.6", "ts5.5": "2.0.6", "ts5.6": "2.0.6", "ts5.9": "2.0.6"}, "versions": {"1.1.0": {"name": "@types/istanbul-lib-coverage", "version": "1.1.0", "license": "MIT", "_id": "@types/istanbul-lib-coverage@1.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "dist": {"shasum": "2cc2ca41051498382b43157c8227fea60363f94a", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-1.1.0.tgz", "integrity": "sha512-ohkhb9LehJy+PA40rDtGAji61NCgdtKLAlFoYp4cnuuQEswwdK3vz9SOIkkyc3wrk8dzjphQApNs56yyXLStaQ==", "signatures": [{"sig": "MEUCIQCo+JqE9+t08k1ArpciVSaUOy9khc5zGxsnVbLb5pXYWAIgZ1zGIkX7PhTD5aiYI+iO7zX8+A4KfnGfO0Rn1mfJoP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for istanbul-lib-coverage", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.4", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-coverage-1.1.0.tgz_1504188367728_0.7808519673999399", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "18e17ffdef5603196bad38f89279245fb5eb05516e3fc2d00e79b3b0863e2a0f"}, "2.0.0": {"name": "@types/istanbul-lib-coverage", "version": "2.0.0", "license": "MIT", "_id": "@types/istanbul-lib-coverage@2.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/loryman", "name": "<PERSON>", "githubUsername": "loryman"}], "dist": {"shasum": "1eb8c033e98cf4e1a4cedcaf8bcafe8cb7591e85", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-eAtOAFZefEnfJiRFQBGw1eYqa5GTLCZ1y86N0XSI/D6EB+E8z6VPV/UL7Gi5UEclFqoQk+6NRqEDsfmDLXn8sg==", "signatures": [{"sig": "MEYCIQDCqM8rW0be/aSTn6PGHhGZpreONNMcplfMi/bYAeijHgIhALQL59I/wCMbbQGwKKCqYrVsCa0JUSdxuzucEPNe9z8f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchsLYCRA9TVsSAnZWagAArfIQAJQlQyZAcM1mwaVHrnIJ\ngPv7luS1p5qy0QsTs7v/I9cd31jfpgOxIVOjrQzo4ykE/FvsqXl4uXcfHB5m\nLeIA4EriUjlS1q+tkUGSH23pCheJ2y2yMEdCLaswYKG9NDZrdA2lcKx1HzHc\nuNeb5J8lIUO36ITy8KPQt5yy3Gk08RV13qjxu8LUbp04YeJ4CUoWQzFJfGV5\n5Bli01g0XyQiJTrDuXoQB+WU1MYA9wHFKJF9Tf8oCP7ZXW9z2aRIaEWuSFhh\neLYjgpUEUN9J6pA8zH/5wAAAleHZHHz/KlZzTT5X0ezimaJT/S42vNrF/lFJ\nj9gLGyzh/oznG3f0/QbljGTdwa9Tr4ULGXLlDy1SZeRVbl2erHT2NJWhXnjw\nLfVdFoaYvYIoHe+a0ZSkG/J8DYEhsgaXYMgbhk9hPddLmlYKlpOHODygB2Os\noA9eYR5x3fK0qaNmMzWnNnMpKiHbz/z/SNACBDN93DDVl/0O1QWwUA8mzLnH\nBaAjG2CDr8NRiw5zz+l3r3IhKY76ifAteiL6GVFuZvjh+ZQ/HqnDhfFLs7HD\ndb5YGLtuYJKJsTv24U6DrlkmxPt3nUGLRzSR8BUhY4CXxyJNlBYO3ZxX1+/A\ngbvRbZD/xd30ZOgTz6B5jm3dA0SM32XOqs+qx1izS9OzrNLE8tyu7MigTJ0p\npU3y\r\n=vIpO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-coverage"}, "description": "TypeScript definitions for istanbul-lib-coverage", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.4", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-coverage_2.0.0_1552335575366_0.69184007543582", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "08a570d6de8b28ae4979fc831b2d4f0e3754a8514f7e261b7f7071ca3677c2db"}, "2.0.1": {"name": "@types/istanbul-lib-coverage", "version": "2.0.1", "license": "MIT", "_id": "@types/istanbul-lib-coverage@2.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/loryman", "name": "<PERSON>", "githubUsername": "loryman"}], "dist": {"shasum": "42995b446db9a48a11a07ec083499a860e9138ff", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.1.tgz", "fileCount": 4, "integrity": "sha512-hRJD2ahnnpLgsj6KWMYSrmXkM3rm2Dl1qkx6IOFD5FnuNPXJIG5L0dhgKXCYTRMGzU4n0wImQ/xfmRc4POUFlg==", "signatures": [{"sig": "MEQCIFOjEnpvGdHrsCjG+aPf1quncVjBXMIrVmbtZcd1+9CSAiBxcqWgX8tBi5NVB6Qa7jLzj/fUODr4KKrH4GNoKcHDRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwj3bCRA9TVsSAnZWagAAzWAP/1dDPb6st0zskt5CpZ/E\n56muV3YHhM30/1IOdJk86omPBA39D3YrbCHMRTFM++yQjZr5LojZ7nsiZDvD\nrTz5lVvTTVdcHX5FvCN/QJIDyJt1SFyXmtMwFK9wFN8dKXerslBGn1z7hD8T\nuXVQ0l7knZKGQ6xFjHbAnHGuUQ3t3ARQEdblm21NQOpestUK56/69JBW1zYl\net9B6ds+2IkKoT8ySqrNevWTyx4xHaWShJsO5k+fCe49Bw4I/IFhzHBXKmd9\nb7yWI/WWcAT14pt7dmhchz++w6EgDdZQBRVKbE51C0RSz+76nmYHQbfEbb5i\nBM+jgRwrOnldQO5MluEJ70C4qDCg6zTfhjtpB/n6XqZd+mheAEq+eGV8zZji\nJHd2O7EjAwcpVLyg+//VQW3lh+qt6H03hodtd0YiTl4Dy9ZtilJ+7O0wDtrB\nQD/kFiEdo2cWjxB+bE8SDEB4JTHlHsUJst1kIG0u1zHhBjqoDAjXfu8DAsl8\nDW33HnqpJBaDk2i3WmC5hI3mHorQ0I50ssFRkJd/Fd7Sz/pjSxJPKbWYMElf\ngTIEboxSPtUPWeUhYlQzpp/nXkjPSwwaBWY9qzqIVSjsIdZgPxup/XUKLyEX\nuzjOWfC2u608h8aR0RBphAq2qErzH621ErrLbrfj8WHaA9BPZ8/b86tbDPAi\n1Ird\r\n=acMQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-coverage"}, "description": "TypeScript definitions for istanbul-lib-coverage", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.4", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-coverage_2.0.1_1556233690261_0.41770746267224257", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fb2cf9603945473dc60dede8472e884daa070938a01b09aa816ca0cc979213ba"}, "2.0.2": {"name": "@types/istanbul-lib-coverage", "version": "2.0.2", "license": "MIT", "_id": "@types/istanbul-lib-coverage@2.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/loryman", "name": "<PERSON>", "githubUsername": "loryman"}], "dist": {"shasum": "79d7a78bad4219f4c03d6557a1c72d9ca6ba62d5", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.2.tgz", "fileCount": 4, "integrity": "sha512-rsZg7eL+Xcxsxk2XlBt9KcG8nOp9iYdKCOikY9x2RFJCyOdNj4MKPQty0e8oZr29vVAzKXr1BmR+kZauti3o1w==", "signatures": [{"sig": "MEYCIQCfSBswcy1BTb+e3SW2H78+UagrNwhX9WNg7flCBhKoegIhAI2FjnogOwApvXfGXcxK8QzKqeHd480H1RZoOOZn987B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeviu/CRA9TVsSAnZWagAAJgAQAJkMlY83kFYjlco82FTA\nJK387oCRsU3eLxETbiCkHU5zB7rX5f/f5ZFcn4ODRp6e4laRHagJZUNpoV7K\nmslnkO62z5xqkGWMfC0/nnlQRhJjxc/4P8+EAwfW/R5U89OJcwK3plVE4YtD\niz69kBSVofm2j428vBNf7oj82/j502RYvLgcDbj4k8Nr86auc1HCxD9er39j\nxfvXoXFRA7uFa10iphKJCsEghp8wbJ6Gjb5v+DimF5mAzNnY+BBRUr+y+SCu\n47xGFgU1pxUf+RGJMPf+MEnfTNJB20xWyLA5rjb2kfxamDqwOPNOIibkilE3\nbXPRYR0aOjnHyNbWrHpUh0dX2MqiLJLg2G3avGCyNzIOqZRJasj03yDgn59H\nR7TBU6xCJLFfnq1bftPzgtRJ7wEYCp7WpsX2i5V3M7e9GifqJraMVqpAMc9p\nSxBLXsGJDY+h8aKd/qBgjA5dgW8+WfosO5R2VlZapt5GE5lGBbX5gSD4xInJ\nAU5ecteGKw+XvUe3xNsQaAdrEpZ1jw7jhcyGQ+2g1I64N4M3Ijw/P4qk2+xz\nx2ipjYLbmzgkF/7TVGuKpAPMg1AODR6dihOVXmHiR4BFXwT05jBoXlRURLqS\nZGye6p6kfQTS3twZVBLjZMg2h/Wc60w8t+ttJED5RpTrm8ZX/Jvk6FpB/2Wv\nN8yK\r\n=EfWN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-coverage"}, "description": "TypeScript definitions for istanbul-lib-coverage", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-coverage_2.0.2_1589521342923_0.5041644327818589", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "82429516abfcf471c75c0718848290621361fc0339eb7644032bb753a9322eee"}, "2.0.3": {"name": "@types/istanbul-lib-coverage", "version": "2.0.3", "license": "MIT", "_id": "@types/istanbul-lib-coverage@2.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/loryman", "name": "<PERSON>", "githubUsername": "loryman"}], "dist": {"shasum": "4ba8ddb720221f432e443bd5f9117fd22cfd4762", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.3.tgz", "fileCount": 4, "integrity": "sha512-sz7iLqvVUg1gIedBOvlkxPlc8/uVzyS5OwGz1cKjXzkl3FpL3al0crU8YGU1WoHkxn0Wxbw5tyi6hvzJKNzFsw==", "signatures": [{"sig": "MEQCIFHdi3/x1SyF6uHixLpa+68mUA1KjRSN/cNLePXlKxk6AiBhhOoU9z+e0pIeJAm2sNrTGgUYbrqN3aSn1QiHi1egWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe37gWCRA9TVsSAnZWagAA/jIQAJgho0+e4/mUo2orXS6x\nX2SKsWghl55mAi94Maz9PrN6OLZZKzDattvJ68qwpHf2CY1GKuVq1nDvrd8q\nb5ifF6CHtqdalUOLYnTV5C3aIyROzZnxhBC/qMyJOvDQVxqyt+j5UP+EHrDc\np+APlGhyPwZne/yg2lOTgtaiypmmxskr6M/0xDsUTOYiV6iC9NwSnoTqqtD5\nlQ3d5gwKh+c6s4VzoDoNNKCqsEfS1j8fF+lmbLssQhLUVGGCeE4erfW2mzqk\niafOQ10gevMGtVEgw4KSGRu3FPtUeJBxL8YiCKldsZYC8jPs3SG5hpOkoja6\niacC51/sQGQLpGmKJ07EnxZJ4JzWyvmykNBJE4dm6XAmWhexD3MXQQbj7Mc7\n05q/AdlQ4/5w8L7+dHxRG+wpb/E4IAHxG07bGbcMMm3Az54YRw1HUXcDMLXD\nf5kdbPPjRxhgwMbY6gdrW5jwnuLlX7n223IY/QrpuvUk2npt3cANobQ/a+fv\nC6k2wa+ElAVE8I0oDFoC9jvNUoc9yEpTpZwjQChrGkfCw/Esuj0zXjxprjyL\n4qSRcLYUi98JyBw8ZbjEJoHT9KioetrR+Lhi1sF8wxK8Ma7006tVbFiz+olK\n3WBCPxlRNUhvq58Oc+bzunoBvD+FTYpZzJ3wiy+yudLF3JCJkICUZ/cizTMk\nKyVV\r\n=qsOk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-coverage"}, "description": "TypeScript definitions for istanbul-lib-coverage", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-coverage_2.0.3_1591719958381_0.36526670187578336", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a951ff253666ffd402e5ddf6b7d5a359e22c9a6574f6a799a39e1e793107b647"}, "2.0.4": {"name": "@types/istanbul-lib-coverage", "version": "2.0.4", "license": "MIT", "_id": "@types/istanbul-lib-coverage@2.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-lib-coverage", "dist": {"shasum": "8467d4b3c087805d63580480890791277ce35c44", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.4.tgz", "fileCount": 4, "integrity": "sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==", "signatures": [{"sig": "MEYCIQDg+OS+NS1kQ1UVxUmSwGu6pe7R/k+4LTyAF7ke1FMsYwIhAOVaGRxz3CijV+vVlULMvg10tYoCeli9RDGUjMo78cwM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5757, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxQ25CRA9TVsSAnZWagAAcwsP/1iitD0+kXVVLqK0OC1l\nBiw55wbJ9ocFxFVPgVtGi+RRvG3IBLwrI4G0w1hfysxGBEWTw4zoVP2sHQOS\nM/aX7Ky2Y9CloEnhB9biQnNlljZ6nAEepERV35vlLccKNfYaj5La+3/lzthg\nm0xvWKUYMb5ebl9hG6HcyOiVUVhj9IwOoYMRbCRENZsSiWScP0H5wf3FI0/x\ni1D2XkoCtCuwg0c7K752229y/vx4E3ZDigRz2edrBkZ65LMBpGDPCi8rrcf2\nWI8rAJhafEcUmzoxGIL2ebIZq/81+5ya4EtkMkNdOlwQhhMr+U4ipxj0p1En\ngXWP4ScjhAArzcCAdQ5cuvE1t8tuEDVgNAV8Yfu3jJUtMPcE1ovpWJAnfr0S\n8eZtAtxnKlUP7esOGAZhOcixagJBgChLIZkjTOi4kXMHj5X8htih56F2Zpd5\nk1mVo9tPNJ9Hm4XGUHIYwdOgewSbFKv6RsZ/BAVTltSmc5/bQKtHDnGTpWYX\nWpMSSQpl8btoROnmtiqfN25SDfavROvw0dUS03VTSIKPIc/5OWa4naL6DaSr\nawrujdgXX2YioU1TYsYXw3TmChtSvdXKBGkcLyDNfMFTqx/0Mk0rn+iSW/Fk\ngr13VyGDOlwkT1/GicG/ZZ/34nAqdF7YnrnlayBDdi1GvOHPWbXSBBzBqKIZ\nIRdl\r\n=xyuW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-coverage"}, "description": "TypeScript definitions for istanbul-lib-coverage", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-coverage_2.0.4_1640304057132_0.608821627809151", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4df627cf114fc2a3196e0bde6df85bc30eb84e778319bcc5dcfdf962ee9fe093"}, "2.0.5": {"name": "@types/istanbul-lib-coverage", "version": "2.0.5", "license": "MIT", "_id": "@types/istanbul-lib-coverage@2.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-lib-coverage", "dist": {"shasum": "fdfdd69fa16d530047d9963635bd77c71a08c068", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.5.tgz", "fileCount": 5, "integrity": "sha512-zONci81DZYCZjiLe0r6equvZut0b+dBRPBN5kBDjsONnutYNtJMoWQ9uR2RkL1gLG9NMTzvf+29e5RFfPbeKhQ==", "signatures": [{"sig": "MEYCIQCF9inIRUpdqAYo9ZmM0eM9pz5/ivIMGNBIvcCWmEsmMgIhAOXAHtmszlffjsiP3369Nm/I01XPcHsTZm+wwqZk5+8s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5445}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-coverage"}, "description": "TypeScript definitions for istanbul-lib-coverage", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-coverage_2.0.5_1697606964537_0.36320504934330433", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0f0a5836412039a71ccd185c989ef86b7eaa389a1f70a01631ad31dc99577275"}, "2.0.6": {"name": "@types/istanbul-lib-coverage", "version": "2.0.6", "license": "MIT", "_id": "@types/istanbul-lib-coverage@2.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-lib-coverage", "dist": {"shasum": "7739c232a1fee9b4d3ce8985f314c0c6d33549d7", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "fileCount": 5, "integrity": "sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==", "signatures": [{"sig": "MEYCIQDPBVtq71eXI0FjBuB1eH2vfBOGCjABi4u8yS17AUdCSAIhAIZeupqQ86Rp1v0JOAbS9Wi6u3CTOYfp6x3dAMeB6qjH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5445}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-coverage"}, "description": "TypeScript definitions for istanbul-lib-coverage", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-coverage_2.0.6_1699344648913_0.21863447761579136", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "36c823c8b3f66dab91254b0f7299de71768ad8836bfbfcaa062409dd86fbbd61"}}, "time": {"created": "2017-08-31T14:06:07.811Z", "modified": "2025-02-23T07:03:24.561Z", "1.1.0": "2017-08-31T14:06:07.811Z", "2.0.0": "2019-03-11T20:19:35.509Z", "2.0.1": "2019-04-25T23:08:10.449Z", "2.0.2": "2020-05-15T05:42:23.035Z", "2.0.3": "2020-06-09T16:25:58.503Z", "2.0.4": "2021-12-24T00:00:57.277Z", "2.0.5": "2023-10-18T05:29:24.738Z", "2.0.6": "2023-11-07T08:10:49.131Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-lib-coverage", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-coverage"}, "description": "TypeScript definitions for istanbul-lib-coverage", "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}