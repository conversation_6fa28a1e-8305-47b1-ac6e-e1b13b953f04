# 最大公约数

## 1.定义和表示

 最大公约数值两个或多个整数共有约数中最大的一个，也叫最大公因数。此处将a,b的最大公约数记作(a,b),a,b,c的最大公约数记作(a,b,c)。约数就是能整除的数

## 2.解法

### 2.1辗转相减法

有结论(a,b)=(a,ka+b),即(4,6)=(4,10)=(4,k*4+6)

以下证明:

如果p是a和ka+b的公约数，那么p能整除a，p能整除ka+b,容易想到p也能整除b，那么p就是a和b的公约数，进一步也就可以得出他们的最大公约数也是相等的。

举一个实例证明：

（78,14）=（64,14）=（50，14）=（36,14）=（22,14）=（8,14）=（8，6）=（2,6）=（2,4）=（2,2）=（0,2）=2,最大公约数始终是2



### 2.2由辗转相减法到辗转相除法

由于(78,14)=(14*5+8,14),所以不难得出可以直接由(78,14)=(8,14),然后接着来,直到一边为零为止:

(8,14)=(8,8+6)=(6,8)=(6,6+2)=(2,6)=(2,4)=(2,2)=(2,0);

这一步用算法实现如下：

```c++
int a,b;
int gcd(int a,int b)
{
	while(b)
	{
	//此处c一定要小于b
		int c=a%b;
		a=b;
		b=c;
	}
	return a;
}
```

```c++
//递归处理,也叫欧几里得算法

int gcd(int a,int b)
{
	if(b==0)
    {
        return a;
    }
    else
        return gcd(b,a%b);
}
```

# 最小公倍数

## 1.最小公倍数和最大公约数之间的关系:

两数的最小公倍数等于两数相乘除以两数的最大公约数

![image-20250706160125118](F:\front\个人博客\web\markdown\算法与数据结构\assets\image-20250706160125118.png)

 