{"_id": "forwarded", "_rev": "21-1a50e3f8b13e3f73dbd500e81e531af4", "name": "forwarded", "dist-tags": {"latest": "0.2.0"}, "versions": {"0.1.0": {"name": "forwarded", "version": "0.1.0", "keywords": ["x-forwarded-for", "http", "req"], "license": "MIT", "_id": "forwarded@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/forwarded", "bugs": {"url": "https://github.com/jshttp/forwarded/issues"}, "dist": {"shasum": "19ef9874c4ae1c297bcf078fde63a09b66a84363", "tarball": "https://registry.npmjs.org/forwarded/-/forwarded-0.1.0.tgz", "integrity": "sha512-h17abE+9l03GtF7H+Tdf/exIbFnOgiOieYrtBfleXuDTU3jGncrv4oLOIuXnFPveDuQPd9kd3MGkhKaMGoQwOA==", "signatures": [{"sig": "MEUCIQCEiBX7mTuL/5JucigWrJBPVRxt11L+fVDApm++ck5XtwIgNUS0IrUZdNlahB+QEHfA1mvL2KbuFJU1ZpyGtJXKMdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "19ef9874c4ae1c297bcf078fde63a09b66a84363", "engines": {"node": ">= 0.6"}, "gitHead": "e9a9faeb3cfaadf40eb57d144fff26bca9b818e8", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/forwarded", "type": "git"}, "_npmVersion": "1.4.21", "description": "Parse HTTP X-Forwarded-For header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2"}}, "0.1.1": {"name": "forwarded", "version": "0.1.1", "keywords": ["x-forwarded-for", "http", "req"], "license": "MIT", "_id": "forwarded@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/forwarded#readme", "bugs": {"url": "https://github.com/jshttp/forwarded/issues"}, "dist": {"shasum": "8a4e30c640b05395399a3549c730257728048961", "tarball": "https://registry.npmjs.org/forwarded/-/forwarded-0.1.1.tgz", "integrity": "sha512-G6EmO0EsSSQzV4GbnJMjkMOhLjyhrQQnNbC4ZW+3PRS6xajSJlk7AdRu0dECQZwSzuXx0whg16ll5ISajv64qw==", "signatures": [{"sig": "MEQCICicZCuAe4DEjCqt6vtMmJeqFLF2ErECfM1gp/UnpE0tAiBTvdrWujSJNhWLzOIlAUZoq72K3FzVOXjeomW51JNg5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "8a4e30c640b05395399a3549c730257728048961", "engines": {"node": ">= 0.6"}, "gitHead": "45e46e2a56348b62ebe679e263550624e6a02d70", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/forwarded.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Parse HTTP X-Forwarded-For header", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/forwarded-0.1.1.tgz_1505085347950_0.7802835658658296", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "forwarded", "version": "0.1.2", "keywords": ["x-forwarded-for", "http", "req"], "license": "MIT", "_id": "forwarded@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/forwarded#readme", "bugs": {"url": "https://github.com/jshttp/forwarded/issues"}, "dist": {"shasum": "98c23dab1175657b8c0573e8ceccd91b0ff18c84", "tarball": "https://registry.npmjs.org/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha512-Ua9xNhH0b8pwE3yRbFfXJvfdWF0UHNCdeyb2sbi9Ul/M+r3PTdrz7Cv4SCfZRMjmzEM9PhraqfZFbGTIg3OMyA==", "signatures": [{"sig": "MEQCIAmPid+M9ttUZF0O0m4yyiffkILNiGdUCn2a70aaBw06AiAsN6V8lrsqi2oM4hIkJ6sYwdxGowox22YKZN3ADrmTRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "98c23dab1175657b8c0573e8ceccd91b0ff18c84", "engines": {"node": ">= 0.6"}, "gitHead": "2fc094b49781b62acb0e2b00f83abd641d604a7c", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/forwarded.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Parse HTTP X-Forwarded-For header", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/forwarded-0.1.2.tgz_1505441873168_0.0936233215034008", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "forwarded", "version": "0.2.0", "keywords": ["x-forwarded-for", "http", "req"], "license": "MIT", "_id": "forwarded@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/forwarded#readme", "bugs": {"url": "https://github.com/jshttp/forwarded/issues"}, "dist": {"shasum": "2269936428aad4c15c7ebe9779a84bf0b2a81811", "tarball": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "fileCount": 5, "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==", "signatures": [{"sig": "MEUCIGStXOvHp1UhPb+oEODqJdGPc4uV03Ad9ONda410dnFNAiEAjMxcrDOEE375P4eyMqQzBGHGklkig8NcDmLGK+CNK+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtW/WCRA9TVsSAnZWagAAyf8P/23NMl1yyxOGfaXAMeyK\nkLZQ+pdMp8yFjRoRVbeTJV3XJVEEIkXABsn7pFMldGmg7NklHY08BzbnOr/u\nY70HiGdzQu3KsrG8qZxC0gvn32C3jJ0kq/MMaOV2oBZzWQDQSpehLoEJmPD5\nJwnsWsMjzvWYhQjXbpZnVBBbnD9qvT1ATyhi6ncnEywMmFJ3bpkRAPIbwHxC\nvdbDmwGstT/AuwjFXn+eiKlE6QSTN72tkz4PlqoVMB0OFFyeREBkd7RApHE9\nQjFmSbkCgytnGYeSQWAaksx5OV0u8rNxtIte3IeAPYldVazswhnnZ7Qmy3Vw\n6GGxVpyqu2+xowCw9NPIqLjrYTwLGCgHwvHDwouh/q2MYqPhxrR8PfBCwQKs\nIoo+xKjE/u/xVT0ETcC8b9BcYE4HhiaPgUP+GLvrxdn/kF7BZNX2MwV77JMV\noXUItT9Ug003RLKHrJkhfcdYtmzHmEMGpUOoUnvQ2bdMSPsesrQkxkhVMNlj\nzn9NITRMaOraEkWKASoca5MJTgSaBJ3DAcug7uBSZBRJXY20KXdD4EhYPiqf\nv6burmtlkzd0NpHZFqLNxJ1g+zoU5LI+9nGwunmSlscCMOcwmQlVxS2mTm+I\nntH7Eqmn0XPoQvFd6uraVdarGR14QWlrPHhx71AwZ+TW3GxjzRIirLmKJBS9\nIDiQ\r\n=FFNt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "93d2f4c185edd14cb023632c0b216a9f08646ff1", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/forwarded.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Parse HTTP X-Forwarded-For header", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "8.4.0", "eslint": "7.27.0", "benchmark": "2.1.4", "deep-equal": "1.0.1", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.23.4", "eslint-plugin-promise": "4.3.1", "eslint-config-standard": "14.1.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/forwarded_0.2.0_1622503382323_0.8063619100472423", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-08-19T08:43:29.230Z", "modified": "2025-05-14T14:56:18.151Z", "0.0.0": "2014-08-19T08:43:29.230Z", "0.1.0": "2014-09-21T19:23:28.870Z", "0.1.1": "2017-09-10T23:15:48.834Z", "0.1.2": "2017-09-15T02:17:54.232Z", "0.2.0": "2021-05-31T23:23:02.495Z"}, "bugs": {"url": "https://github.com/jshttp/forwarded/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/forwarded#readme", "keywords": ["x-forwarded-for", "http", "req"], "repository": {"url": "git+https://github.com/jshttp/forwarded.git", "type": "git"}, "description": "Parse HTTP X-Forwarded-For header", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# forwarded\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nParse HTTP X-Forwarded-For header\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install forwarded\n```\n\n## API\n\n```js\nvar forwarded = require('forwarded')\n```\n\n### forwarded(req)\n\n```js\nvar addresses = forwarded(req)\n```\n\nParse the `X-Forwarded-For` header from the request. Returns an array\nof the addresses, including the socket address for the `req`, in reverse\norder (i.e. index `0` is the socket address and the last index is the\nfurthest address, typically the end-user).\n\n## Testing\n\n```sh\n$ npm test\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/forwarded/master?label=ci\n[ci-url]: https://github.com/jshttp/forwarded/actions?query=workflow%3Aci\n[npm-image]: https://img.shields.io/npm/v/forwarded.svg\n[npm-url]: https://npmjs.org/package/forwarded\n[node-version-image]: https://img.shields.io/node/v/forwarded.svg\n[node-version-url]: https://nodejs.org/en/download/\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/forwarded/master.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/forwarded?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/forwarded.svg\n[downloads-url]: https://npmjs.org/package/forwarded\n", "readmeFilename": "README.md", "users": {"mojaray2k": true, "rocket0191": true}}