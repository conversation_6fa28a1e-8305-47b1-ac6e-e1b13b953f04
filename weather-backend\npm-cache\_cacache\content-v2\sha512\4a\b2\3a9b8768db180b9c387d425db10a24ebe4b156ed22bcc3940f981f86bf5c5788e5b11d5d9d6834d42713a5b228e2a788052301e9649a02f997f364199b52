{"_id": "jest-watcher", "_rev": "167-cb5989c923c9bd8a118b3cfab1103034", "name": "jest-watcher", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.5"}, "versions": {"23.1.0": {"name": "jest-watcher", "version": "23.1.0", "license": "MIT", "_id": "jest-watcher@23.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://facebook.github.io/jest/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a8d5842e38d9fb4afff823df6abb42a58ae6cdbd", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-23.1.0.tgz", "fileCount": 16, "integrity": "sha512-fPd97YIzF6T8rXSm8bdwAEwZeczOOJ2EuB6YOmHl6PR4o8jETum6Eal/ZqKLw/Hac2H0yxSTwvLdy7/o1M451w==", "signatures": [{"sig": "MEUCIQCuUfQFZXMpbZaA2/65Lb/yNjDSsJvBdYMo9F8tEqp/IQIgMJWZggCZM9s2H1MstRyC/MvBexopdeBRQkYFe0hG5Ss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDufvCRA9TVsSAnZWagAA8hUP/20rX1xkHIAWCkAi9fIt\nRCSrYgve2zp6xQj7bSqPzWiAXArttDzZEBUP5lDjHI4h/dh3iA6zIsNMGJqg\n8r2pktvMt5O6/P/eRzNl1wlmmZ8gYcWl7pUcrcxxfauX/MVFCHbYAdOHKq7Q\n2L/dhNLLc9eBx8+gQ3Aw2ZZivKjBUuRaoHErmvNMaro8JzE1lKJkLKb7k8Ct\nMQdC4b6MOLkATBLmMPbPiQLnl6sBG866apvXlh1oqRdus8I4eHANxSfKIMvI\nMiY4DrDDMs68AZ6E+ybCeatoH1H0OYqIZTIWV8AIbRbwUKokuutE+8aRMFfa\nq+qr/fxloYeehHz2Xc0k/6Fd3K1nAfNejDQXe4gxB8oXttS/U9cvOS1VoNeJ\nKSIjrUA5kSzSHjpw5Z2dttBwcqYPidGib9pM4oBUXhpePdi+meG3hkxSm827\n0AP0eOsZ7IsgE+xpWjWJ4GbpyestDbjQkk/HJ0lVdxZS8u8CREt/YXYCqq5U\nQmTDCaHCZCRk9iY9pUPHqI99U9PhnWCmVghlqEsqbwdJL5dpucWZt1aU2OzJ\ntupKmX8hqiFx3F3iNfuaByDgfjuutc1oY0XpFfELm6XHt+zkzP4QeNCgqSpK\neq6421KSrZte006imQN4yGOT+yll7kiAA7+IcpcLoQ9TGx63FxMlhu66dDS+\nGJyc\r\n=KFtI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest", "type": "git"}, "description": "Delightful JavaScript Testing.", "directories": {}, "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_23.1.0_1527703534853_0.36859187631342905", "host": "s3://npm-registry-packages"}}, "23.2.0": {"name": "jest-watcher", "version": "23.2.0", "license": "MIT", "_id": "jest-watcher@23.2.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://facebook.github.io/jest/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "678e852896e919e9d9a0eb4b8baf1ae279620ea9", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-23.2.0.tgz", "fileCount": 16, "integrity": "sha512-UMlQkOLwCx9DFXhg/jIxUbJ4qRk9FKzi6uywSFpSMaFZWDavUuHNYeEQi4heiouJ7gNKQNmRtQf+sz63YuNnJg==", "signatures": [{"sig": "MEQCIGHWURj9dbnO70fyTEnSU/Yi+aQUkKo5EClNvJH19Pb/AiBOnYrbbuVT13tsaMH6cazFuEBcoMRFDs4wEImvlM0L3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16247}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest", "type": "git"}, "description": "Delightful JavaScript Testing.", "directories": {}, "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_23.2.0_1529935513327_0.13912338196484808", "host": "s3://npm-registry-packages"}}, "23.4.0": {"name": "jest-watcher", "version": "23.4.0", "license": "MIT", "_id": "jest-watcher@23.4.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d2e28ce74f8dad6c6afc922b92cabef6ed05c91c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-23.4.0.tgz", "fileCount": 16, "integrity": "sha512-BZGZYXnte/vazfnmkD4ERByi2O2mW+C++W8Sb7dvOnwcSccvCKNQgmcz1L+9hxVD7HWtqymPctIY7v5ZbQGNyg==", "signatures": [{"sig": "MEQCIEvTCMAKjrV5DAuy0CFVsDFmcJfB5Ks71ccTHftjUUkcAiB7q3yyMoWXsAAZpE2I4k+pfiLs4F5SGZHlsGj6tQ8leg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16248}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest", "type": "git"}, "description": "Delightful JavaScript Testing.", "directories": {}, "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_23.4.0_1531237946531_0.7586931795733249", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "jest-watcher", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b75b18d7ba5e568043ae9feb25057cc67a50b61", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.0.tgz", "fileCount": 13, "integrity": "sha512-r<PERSON>lyQkKBLojkvHkLV+1E4d7bMJyyfxFrJ/b+NU0dzRmqyp500yNVmRiRiZqBHcWNuUJ1qxwIw7PjiSc619G8w==", "signatures": [{"sig": "MEUCIQD3X8mL1wKjt5dePErQzSaQLRpgS0cneXJGdK2jg/4AAgIgI4559B/qneKPTdGOb/La6HWi6q51MU+7Ay7VKxX+5M4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyco6CRA9TVsSAnZWagAAWdIP/jvF7cju2CrA+M9tiE/i\nr5rUnoMmClbQEDJ1DfLRy/+vmUsX9NdlPZu5lbSPHvCncwffUgG1UwqxaJoD\nG9BHSCSOTY+dhbMNBC1rt1l5uwf9/ntiYw5/oGIybjfU1MdjdKj0RkkcI2CD\nu2IqmvY177m3HFk9jXnys1p73Doj+CUnpt56M9eC8lskQRPuk+pPh2Ahz/Hb\nk61IusuR4JR3XQ1ToB+M9njDziTgmAyLkZW9833ZbZZW/SI8vog2d/m4muq4\nWyzDpQ5istrzAIjHlmDVxHgpxdT7RxKEoML6qkvvkgw9NDH6yXcdPjVIEAlM\n3Tz2JbNdQSNbEVFcIW2ghqOH/dTSRSvZubQ/bFEGlG3wzUtP3qKqpy3dYRns\nphArUlu78hF/LC5UkkWCfjobYVLoun0CxVU5xGKgqjM7vPO6G/qfJe3NrRO0\nfuhRpc3i2WHh19n5gkXCaJYSQWd6JAljHXaYjIJ48mZT2Fdrd6chMicYVGcg\nSs//fc4rRUmH7QDCBINo0LHMYqamBwUM0pE6gTM6696hlShAlJeYtn2RNmqZ\nu/rVXwBbzNXCokshOpF8DWTwQ57wUZ6kXFmcz7mCyzEU6SNJ+DHjI7z6x2LI\ndXeSftdWK4Y9ibFoJdeJGJBjgCXX4U4lI7i+c1bL/EZ3ivx/ZN4FEmyL9MeZ\nCaUs\r\n=uOdb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.0_1539951161539_0.9304228381622928", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "jest-watcher", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8a94dafb90cf6c9404709085614aec6565a7bca1", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.1.tgz", "fileCount": 13, "integrity": "sha512-19EOefXYqQvKhJAA6NCop15b013od2W70T9dCKM2CTk+XKmtUtY5ZG262Zo3vCOf7wZjFwE7Zydt6tZWjW2vfw==", "signatures": [{"sig": "MEYCIQD+emK2QdyOc8zit6Bvk/PwRx1IWxq92SNvzeuBuccSbQIhAJ6IKw9P6Ro/IKDFTRU3Qzlmn6ciy7CzFh9dqXGE2lG7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5RCRA9TVsSAnZWagAAaiMQAJlpElAOrjluVeJIVfJe\nDhB9ozUWJgIlm0xLF4oBllkpQBSjpv4nuagFSJ3+tsGmttOIfbP/rH9bBeGh\nd9zscGCwIEN3l7UBk1tSe4/ZVQhpN8+MI0b8IlSKJ2enlN7rdPs8XiO22hqZ\njYegwfYLReYiZMqtaZCVfOvaKuQ/ZiwslZ67g7v5AFEqtt/gtvlw8dzhZG3o\nZakSO6xtJG7KARUj837gp4nRinqOcHd5MkUx18p9MG8S0nEoRZAx/qfgFXLu\n6Gv7cR7JEKKeW/cm/nz2ORzUz3/5r2Uu3r92mp6MVg+n7flYAvfLxIIHPiGv\nqh10QfUlu53XBRNxQ3c+tgwVIHobloCnX6a8jSe9HNPHCelTpMgtHztfIMvM\nGJ+jBTTKbBoJX31lGiAWI3q+N+9dkQu14C6LyLWFLU365fiQRYAaUbDsUBMi\nuY481Ay5y1A2HzLZqsQSs6D+qI1xcav14KrbL3UwHb2muMjAOKacCauF6tbo\nbiufIDwhPKvseHyD7vo6/lWLMpqhqQ5VKJL0k8Axrsy6yPXNWHS3uJHvYcd5\nvo/m5dWKFCtxID2gjvv9/QSwJ5BlL/aryLWNnOvoWITKXme0eTXqzk7mZysm\nuHzelAn0o5TNPTuUqWnVNcTIWTaS65b/0iHiD3KrRnRxN5jA+EdeBK5nuCtZ\nznS/\r\n=jeIb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.1_1540222544968_0.288555560304095", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "jest-watcher", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "06db9a092d8755e8ec5c57f3329bd83e38324c08", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.2.tgz", "fileCount": 14, "integrity": "sha512-pxm9ARxD8ZwpKxPGd/I7h42d+BLjKxw/i5ZK6Wae0JVKzZJH11J1Nr21BTSWBJa7GPmOoTB6iaEaFdo9UVy5Ww==", "signatures": [{"sig": "MEQCIEQ1/vK1W8msAgK7SosVyzkATv1oWsqx+OBb838Ig9drAiBP5YIhTs6xOZoxxC1BElavuJ0cpdjjPih2CqXKbdvwxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0aAgCRA9TVsSAnZWagAA8oMP/iyLnxwzCkhO05vqZBBC\naJurned/Sk3Owxa5O8ewNlDEnR9Ib2gpSl0jEDhskddwrOmJlySyR9zXNc+L\n5uMTxRddfmRv5CFGzs6Wqh1DZruXL9snaJKizOv+zP0u3OTd+8+ujzMkZlz6\nx0iqzG9N4fKaAFKYR4PeTMpDL/DXimYC7uS8NFKY6xxCfF72dXjtLjgqV6ud\nrs0iqkwf/tvG3jj9Z4To2dkkVTO79Lti6C8ZqqJqsTuInH0OtAg0mNdHZOVp\nU4ieysKyWq7rDmbLwYuWG+uve7nRVUhGClWeAfggx7WmouEWUv8mbR2hIl+T\nImG3ZleaI4T7OvwgkFvT1QZsSPGkbSwUkpc2we0xHCOoHUE/uDVxV7+eyheU\nMTpvlwTKqes5pae8Rkwg1UG+UYQhE9OGiKPLijtDTqa5ckbQNLABO6pSKmtK\nzYLknQRYQwtanWiZxHTaOWNe7/vtz7sthA2nBVs00mkmIEr7NygQCEGZNL5n\nFyp/oKoBwNG2lh1gdadysjm/UpIqZJmfuiwcrl2WAfWJeIItxV9xqNImWLmC\nUQNh+AlsbXZUZc+nhGTKh2P5mEFcFgtuvLu9IO0/+93C6rlc/3iTg069wlZm\nafEpXo/fydSSNMPsJcZZ4Vz+WN1YAdhGfhnSJQkco8aU7BIxEriCpQoGB+1z\nd5my\r\n=KRSB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.2_1540464657168_0.6765786855226501", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "jest-watcher", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "71ac48ccb510cedaaa1156a637a1bacdc837fdf8", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.4.tgz", "fileCount": 13, "integrity": "sha512-RDL1n5BAbnseRTZQDAlBoLmjla3sDx1LQuadFa7a73ujxPG9nQyDusVAnVf8JU684zlNMNpQ4BdK50GAQZW0TQ==", "signatures": [{"sig": "MEQCIAKa+EUqkFIHLO06uqKCVmResN62bfiDtzQ4J3wY5+FLAiA2zlVfc6wugnj8u1syfYSH3YXnKkkagCIwHBFLuz6BCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00HJCRA9TVsSAnZWagAA5DYQAKGF8TAk4W+nBBvEMJOt\nftWk0/tLQsqPtIUcwAvEK3CuvsV8SsFx1wY0Fl3D+4mJGSW4DgSXiUmiP1Nk\nqqJ7ZzLqREPR2gSNczjdeZKxr7MJbb6nPaA1PRQQjEYFrM39FvsbFrnOPK31\nvfSv4GANFUk7NCu5BVEN6WxYzggu+MHol7t4FLZ3fxIJYRKJwivqywr0P7fp\nl5WNgeSo1I61wjrZXswSMJx5BW22T7B4T3rKCjKt3OVYD3ctlWmkuP3/KvE2\njuDQykMHqvo5Svzlvnq3lTPFVHSiFtYVumlfthwedGc65JOW71qR+n4wkDQY\nPe2p61YWcgBjBQVorKGafHC/NzRw0EL1VjmSRuglEsRK+tL2nejNIgPLdKxh\naJho7EW0rYmur9obmotsJNFjMvMijYLY+yd4Gf/A2EQuLxWKq1yrddnAcQA2\nO4xGghHG1/kqLH1NNmJ98MjeHaBva2jYvs9YjNpjYJowTDIBqnjiD1MC4P/c\nY7SGg2Tsw4zJNEKhnXvgXwhDo5jhjO7c/e+EtinWf8il5ThdZw+MJJfamJrr\nAs4KkSfuNhvO8zj0YmW7utruXRvmu3u/s3jQbW0eoivkDcoNUS01i8/7Oe8X\nRSXRzuUz0hJ+6wwZ5bPPahlzpbHwBMznvFAoxz81lWjsk8eN+/4v/j+ZlJvU\nx38+\r\n=ny2j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.4_1540571592665_0.24662896783661803", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "jest-watcher", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6b0f2618c19c24332b5b76367d3438c35156c243", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.5.tgz", "fileCount": 13, "integrity": "sha512-KdjYfGFeJPeU8R0POAIAX5zT7/EgGISeuXZwkSFbHjVYAARGaKoIvKOZ+q8N5ZHmAU37YfHgu1aXt+S7f7QdLw==", "signatures": [{"sig": "MEQCIE5EpMpAtgPAffYukSgN23yCuLE7Xlx6FoJ+rAm/A7p/AiAvBG/Bx4b9dtGlUvDKgTHyGDGV0OeNG3afkaI3D6vfxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5YfLCRA9TVsSAnZWagAAJQMQAIx2LWc3xYvLlFJPv9m6\npppPteHBOjnXTrnwAoHmJNs3O4G8oFrwOxqqB9gx+pgBr6De8rVT41zeZewf\nK0Qx2bwF9tgmCfNbGFxNSk4h67wdOwr4EYKXJLDMco/3U5jVKH7WxnGMLuth\nZLM471EWVtottUKAAGIYGVMrw4VxAvneKDWQv7GM7dChcXmDy2avJ64NiMWX\nDvrDizuz4kEc7rvyZFFt6MgEmQ4EI8hfwsz8IRDuKa6atyIEiewq61zGunK2\nMO1Ug+qa5AnstHizddVxEFgkL1yGBC+h/ebkt02lzIRDo3ilz4g49p+5uDQW\n1LCROMtea54R64IU6CxXeUTnkFRe19UW+q0Pm/NpFSloGXdGBCKnwLOG6Mn7\nwl1uNMdu1t1cHju9EhZB31rf1Noial3u33WNqcaVPxMHqku4lMgxaAFDGtCU\ngoZwNdZwXvmLu9b4EalTb+dJOE16gPfqqGQFL2IAlUMoIgxM9W+0s47G3xsC\nK9xhpAv6DXIabdeuZWSa7lCMI26tXLRhfjWe3t8bdP/daGBo3tLWlPd6gLaF\nJu2b/zeNXSjiXewHSsbQlZIMNrNJ9Sr2DlFxJDvATemFzLaorIHgPKkBpg8c\naVpcI7vCY/4HY2jghg42fgVk4Axq38dZ8lJirIA6nNhfqWMh7fF7zgDPqejy\nDV//\r\n=Z98L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.5_1541769162471_0.24896063155214443", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "jest-watcher", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e54918ae31bfed17581fa3978afbe5cf20e9c512", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.6.tgz", "fileCount": 13, "integrity": "sha512-CxL49DV+dNa7ET1OAFGoZd0F1Bt8dgdwyycV9znLkB+RJrVm+kh8KPF24LM5p66f5oQyhAfuxO71u6eRPYkSGg==", "signatures": [{"sig": "MEYCIQDBz5LcLTaMh9hNmQxaaH+jBY95QA1ibNlmVXU2XGC4zgIhAMqGuFbvxXqquwxJR/rbYjZ+gak8CrRWn5yaDDIQOLte", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5ciyCRA9TVsSAnZWagAATmsP/2Xi3L0Jg38tfBwki0f5\nARcaQAbnqPB5muVDbq/mok+KpOoibUjf89FR60LMjnBWGAnS4/Lq99xei8Fw\n/dGlplzNzmoUfBWiTvsaOH6qG0LG3hXaAUcDv6+rc5BcvPwH0jNB+PBt2A6y\nz0+snxGy0N0t29PrxvsTw/rVWndJ3lnC4Du7v36n83is8RhpfcDdMzeQ0UeH\n9S82r7n3WUuBJFCDGKq/nJACvAQ/djnHxi7oaYeNSHBfIDO1STSrPysqVaHI\nbW8TdN3gWt9p+gMWgvwpdlNtnMbVt5jSKOzCoNvBOQVWqIBNkZHXL12oJGXj\nBMxGDAOgRKRSuZO6TOItYYhgs/ygJE99hpP+S377LKI6iIPMssj3Qf/L+AH2\nMbCX4ej+GeGnESKOKTeFgoz9sEG/xAipJa+7W7iaWC8pKtHCtcDX1yFKI8/O\nV0husesntk8Ykcdrjmva19mF+H1CJVYWjcd//F3JEUvRTr5JrdoeKUzGhMOJ\nM+jJoYGPORGOJOGncfqWQRy8+xLvmQm+CVgqngnBmZIGOqIsrK0pcUd+bXfM\nFnQro9NTE+FHPcTjgUbQoaoPop+AoVpj+90N/fGRIqiDC2fa1/0pN+gu/UOe\nvAF23IIxe4OCKpIdNd9QvUWfyf4nEgLx7vqQ7bNQFgj9EOMpBGd4ulh+YvW0\nGbEY\r\n=yTBi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.6_1541785778196_0.6933044658060299", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "jest-watcher", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e0615b8da631ae5d5c7ee8d632967e9929c60984", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.7.tgz", "fileCount": 2, "integrity": "sha512-dxNfJEenG3hZDV5DUzqDN4q6aeoUtiwbOR+RBcCZB5xKYgM3qZRU/XjSWUUIfnrv46mAo14wOq2mV4jpBfWBKw==", "signatures": [{"sig": "MEYCIQDpsZnBDiVD3IjT//rBJH0sww9+yqErvFQPAHhM70E9iAIhAP7xz8YXEBSBTbp3fJNtzLYI9Vo+5skW/j9yCFuoz8gj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+DWCRA9TVsSAnZWagAAnFoP/3yQ9DUgrLbBlTVTo7Ht\nJcLEszIRxHwItEQH3rs5IspFBQ74bXqGdiOkFpez/oSSjmhzrvx1OG6HziT3\ni1tEkAHF9XyKtoonT0BhMIrnUMWWJ2kLVqC1ZMM5FGuIHDK/LGIoSuOPoymI\nc0nGptfpgto3uPCvkFekHqP27EN4PUu/rqBrDxkr/6mBWXjbrm4i5jXdCCiW\nzJ6BsT8QltnmLCLCeQ8jwqtfaxmgvG7h19bL++HEtdHD+3V8voRuMsVQQM24\ntCJ+TpyR2brDrZkBVCJCvDZH0nymhSCR+65BJ2kielj2FiLzUb7PT8zi7Wwf\nxI4pgvJkZxNMTKWgfJiv44m0jWS+QvuFd5VVqLRaCPT0JXXFy947/kd7/QFc\nysG2tOyYwP6gBH080WDz4a7kycfoN+9dGba0Mnp6EO1LNaWOX59Y1+Sfnn7L\n3kMgM+B0kPjUKHScMe30H/oW43/WLcx8WeRgM9WPYrvlDtXvEVqzSt4bTg/u\nzyTikePbH8ZFsfBSUsxW8zmFKxP8OTH3Xh64xWeAhXdJgFXWDxIhb4BRsmog\n0sINDAhvKxFrTy8GeYE3Ji7Sm5XqcPkhVGaVDpw12iev+zd7umrG1uu/tkrv\nAY7FitWqwudFHZkx72PYIjLGpVB5AMXsvuqk3A5QnF70k48xRzn8HYh+xwhT\nBLPp\r\n=0k6f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.7_1544544469327_0.7479667120642788", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "jest-watcher", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ef2beffc9e9c01a47d5396df572aff70a1e8ed11", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.9.tgz", "fileCount": 13, "integrity": "sha512-6BsEPvFir8b3gAmZeFm8WOpSxOhj99A++tVTSPh5/C8S7FlQJYbto4gnk5PO0dePhv5yyVuahvI9y0ZhCt8glQ==", "signatures": [{"sig": "MEUCICygxJkCdd47cPP1Up0m5xWhrlgFv1AOw90iKxXAAlhyAiEA4Gsc9SIOMYm2g4PUKEl8FH9vrLSMcpZf89y+j1SE9ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlTqCRA9TVsSAnZWagAAo3oP/0++UOvmhMeuKKv/WTEx\nE/w7jV18tra3A93UVQr4qgrTrdS6jTnxTLH1yV7x6pCOJEsecX4vgO320Ax9\n2EVGV316PLufBWPeTkErZcNLdcY53KA/UZhVphB4gUcPMpXy7s/5dgk80lGt\nAlhNztEzQkv19Z+SPJOW9Zy4ooPyEMDebtMupy0KiOxNJL4Niy7dd7sdmIZn\nkQkU8Xov/5Qo3kk5+kUkVkbLGXFlDvRqn4Q6tFpshSV+xRujeVp1zesIASju\npnZgLHEBeUPjOL1v8nwM6HIhXvbpClpBcs4xULRlRHkQdx3/V7Ppn0dZJgXw\nxzEuSeW19Y1oDDr5xU3VoD59qk2f//v5+aiEs4+yjOc0VaptcV/FNvhM7EBf\nJ7b0X67j6wV0VRbTzj5SIwF/oyht5J5nw/qKufC+imiPVAQHilQV2UdWJh9u\nHYiJ5KgT4PVwTnZBp/Fj4ljqItsYgw7xShBGvai8IcUK+uVcNU0YiJmM5iU4\nqBjsS6xV8fwD+OhnJ+jpINeBu0LY3I8W3mOQCOLKSAb+f38eqimQW8AfJvfK\nrohMZDKkmJFE7KWkecOpZ8QrKxPvFaPXpk4f92EER7obmotEEPegcPrqnM6U\n/hyEh0WJKE06BCzv7mNMr32BF1nkfgGL1P2hMKe4vautviCsO4Gh0f+gcXZO\nx8uH\r\n=kqRr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.9_1545229545805_0.6791077026952421", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "jest-watcher", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3a328242e8c519378507842478c5db23f4f36b49", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.10.tgz", "fileCount": 13, "integrity": "sha512-J7s356xFHp+t+pz4Zz/o8Hz80NVI4XDdqF31x0P/BM5afzJknn4EsxGV+y6XfhkMguNxssLXSjHF4Y6giGvgpQ==", "signatures": [{"sig": "MEUCIQCDP/KWXqY6MFnQYq4PNSn9SIfSc4o0KQvINkG+OWErQwIgZu2WByNx2iWuWnMZnRZ3RuB9iREB7ItWEoUuCMDj61A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNimRCRA9TVsSAnZWagAABvAQAJ5BZXHajHzrtP5n4m46\ndicrwFnfptyyu0wqDALETbTyc/E2RFbTI9xRHkj38vQ8FWguhDqG6u4XrSUn\nK3x4eO8vKwcJxWd12fZYvfQ1V3MwoDiRDFRSMOxknqFB/3koBe7WPPpB+mnv\nYnx2g1MeVatk0H+T33GiV3LXATLORsI9KH/0LnnDG/NvlpnUZUOnJOYGsJcx\nNmCXtuTakptQSAxhE+hHdAvss4I/fCgpaJ5ZNSTHWFGvpHu3DAETopNqchoM\nVtnu+8G2N3hAFt4wxoUVCFSRib7yezDBnpVDab5GskJlrSe9cRRWnzChGSSo\nyEraDWhIq7vQZQS0HUIy2Fl7MQEyWcd3mdBs9WKSe3eLkHP5GxEgAEPaMp7g\n8DweJmLGOyXGphcHCIRHlr2/uOBmuLNE+G3W9SXiplczAoczdHaIBBgJ68lJ\nA/JZKpmL/aiC4xjkLWshkEQj+E/zbxvsZCi53aIRrqmn3p7IiyGGEEuI6z2R\nB3Wayu3Q9rAnlbIJUAcWOc73VdyKtCElGJOqWYLw+n5V/M7HhUc5fGl0yijE\nHOfusgQ3UKEHF+7WXpsWBv86L8jVen5DRhsDxCXLcfQrwngtT/Sdb0FVKgfp\nwur+HVb1bOlBYU01hCwMNDJuOlyEMI7Ay3qhmah9R7n/Pkrzm3G0G9Qkc28H\n7XaJ\r\n=sfmY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.0.0-alpha.10", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.10_1547053456512_0.2357761440175341", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "jest-watcher", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6454cdcbfde614c51ab91ad8f226bf6cbe0a6c02", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.11.tgz", "fileCount": 13, "integrity": "sha512-dAlN1fFW0wa/WF8cKbyphz8/j2gTYNumVW6rkE9C3a6EjNTUgcUgYZImYfXfvXeS7gQr+QUfY+VZ7ZUsp47hFA==", "signatures": [{"sig": "MEYCIQDEBvshSoHKciqZVc+7Bgch4cujmVgCqGxVVwixgW/L9QIhALLqclU2FEdixvhlqmN4nxdWCH10CGDvEFG1pD5vfD9c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN5BNCRA9TVsSAnZWagAACigP/0VYQxWGV+yWHXI3s/MP\nNEEMTWfzMB6OOIC17oHFoDORGM5dEKwa2mkjti2lh/uMftSn0gtr4CgHuYzi\nZ/RoXPpNKqghKPRY9bgkKglRBfedPPV3dXJzVZffsxC3ieNIQYNYU0RhVpl3\n+haDYRxFWQVo+5o662XoKGNurJlxRZzsUHJlAsx6Me5GZEJjHXUWikFwAvc2\nOzipLqza+5HLL1HJTXMXSyN3lFgmSk4f3SbNnt2LM7SiDBce2jhFeMP6VqQd\n+1Y9NcvYfMRQMRhDI8/0Fn4r+9H+7QT0ps1LRU/Qx4Gl9L0OQt1ct6THl7Py\nmxUCnNqcLcKuC+QahfFJbrU/0E/SNhMcImerIzyWHApzCxTaHlAcRJIP/8uN\nFl8rON/AVG981DmJUMXLbVui5AlklSNVNtB9iYnmBKMAoe65ZVVfjMfkYeB7\ng/2ufYPmXq/OrOf7gWZEI4JTw1+NNoxPonWbHTUqZJwdxJkGd1blsMDQqaJl\nUbBDkrbQ4eQtsvbJlkkDaZLRCdwW57fqI6eS3+CmLuz8+oUPuib1YVhuz4M0\noOIb9D7s/d2KOSyAW7LFcZlNfpKMmxFPwqHt8GKODtQOoPH3Q54F5fsQ0gDL\nrcOM/4JXFMpGaPn//h4YWzgcrKoa+WGOIPP5gWG2X6W3paRkWA94zmGMgUcP\nyW+d\r\n=OLj5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.0.0-alpha.11", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.11_1547145292436_0.08120637247986862", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "jest-watcher", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dcfd73f6825f48c1fc7b692b12ec28d6a851869a", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.12.tgz", "fileCount": 13, "integrity": "sha512-imOMCszzzhylWgzHoapQMI4kHEkQj6RAwhG+/FphpB0Rqs5C6lLnq+qEe8tQ47BN+v3tLNdbsfGPEmR9t4u3cw==", "signatures": [{"sig": "MEUCIDOFKfvj8HoNivoOe7tnhftMICHeqCGU/4wTcxB5MHcfAiEAsIRqPGQtxhO5Gcu0MhatMR61AgScBM+rboZddDS8e08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK+uCRA9TVsSAnZWagAA70cP/RAsfw/b8W7GNiIo8PA9\nIxAH+pjgRNHQ/6MhRL1z8ddFx1FLZadh4wW6gM/DG89V5F6gGvI4Bj3AS8Q5\nhbC0zkiaL7smtA0DU9bINmU+aqvjE17FYUMbpP7OmZd44WNGAWv8j1PLk5Kk\nhr628oJNf17bUKvfl8FIdFHXYxsCUiaecLRxens6FIcvgDan7C0vl/ky2yC8\nPrLh46eFj/vZ1GsG3iCj/DS2xVUsTiZiWKKtoHcWVSlpQ+kdlocjjr+5XJ94\nO+gw37HKdtiAF0wx376GZdQtMilsfBQnXyOIU5a0R8iTv49lImP3/j/xXXAk\n+r4kL0YTEOHFXPWUAQ4QmHiVOzhSTmMzhfZdTtyC5wANXPMqed/3fOYO7Bkp\nyauDs9igQGJGUtCy+kmcQFhh4HJBXr1ifC3PCTCynfFZYe2Gv/RvIewaefxh\n2a4wyQj9MczlYGE6B/J5zAZxHOm+a5C7h2z2QfVXYJBrMCT/dmMDkKtjL840\nlXW+/txAkvjvN6Pj7D4HLXtUFR9kJaggR85R+Gnvb+CIDgedRzXAL8QZBDs3\nDiGn4339kXqs81A5zPYJdncFbFeUUB5YFMh5cU14vYAB11pnUfmNTkmKjWsk\nM2yVs+VjnHJQ+DzymWkTyRUrZJJo28436zo3IqoUUVn1UtYWXhhyYB7RnwHU\nBpea\r\n=+zA8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.0.0-alpha.12", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.12_1547218862398_0.2233113973252716", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "jest-watcher", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fcee1232b2b608586e489a49d3dd22c8a555db2d", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.13.tgz", "fileCount": 13, "integrity": "sha512-FdCP3ft3faZ3zNwbFgNJZZE8nNXMyMMOnwGxnTes/x/Y61Bpgb39JeSDm5l/cLe/PJfhmFxie6qzUGfsKLpzfg==", "signatures": [{"sig": "MEUCIDI0QhaY6VZynb8ZnmTdvxLwZR/7LZqfnDt5yE6Ia0IQAiEAomDCuB7nsTXFSwg+1lhMWj+YW+Pjgw/3m2JjI9OjPC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUlCRA9TVsSAnZWagAADCIP/AnBlZ+4yuXVq9W1gUF/\n7HnYne9UJxDhVnEBR822W0Gu9RBX12tdn7vtN4wtjeCZ4pO9fZQrpstMQjqF\nqISHS71rtpK9crdP9uukvlsbQVkGgSDpE3M9jYHsOmiqEbusJoJxWffRC4b3\npQ/MwkXmV5CpKRaxZYdWlnte0MfIv71jxm+yvGc6f7PTuatBh/Y4/GuNNVk8\nsdwEcVLgKZOVegf8Rk2QTtR3NhrioF+WYqVwQN1nQlc3Y1wtVAEZDUj2xC8F\n/t8/FlvuJCu/yBv0hCARYibQIFiW9OxI6ZhIhY8U4EHcda200YnIg8vM+zUT\nNEAO34xaWKCr6F66QTIcmOVuG5cTLdnV94OhMXiwecPbDjC/+Z0qoxlFwwRc\nEpIZkdklTzpmOj4k1eZdoKfkPy8FCHpmSs2uEAhPyIzjctqy2Ujm5Vk6HgF7\npnyWeY3GReE7hFUhnad2D/J/Vdq/nlXWRPbc+v5qxqfrsF8oBKueALqzoY97\nniHpQWG6X/bTyCLtMeah8fSCJqjbG6SAgQ8Ml9Xlg/GlH5LYElD+h1drdu7k\nN5r7SMWtbJOpd9HDCppeH9+ZDe+HG+SaxVOq/iimZieedDnuJzgkuSt0nKhE\nic+Syf3FuZOr9byUbAXIWAIUU2DPllb8IOgicoWMAwB/ziQOQ8kYvBCqB9YZ\nm3HT\r\n=9UkD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.0.0-alpha.13", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.13_1548256548946_0.655084002739823", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "jest-watcher", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b073d14b6ae4bcce0904e2576bef9aa076a539a7", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.15.tgz", "fileCount": 13, "integrity": "sha512-ZXA/xObBguEs/UXgLkqVm+vCPTznYm4jP6/Dx2umVhU7ototgfI6rHnZGgey608GO4/yW/4pPyk3pjCDzrKhTg==", "signatures": [{"sig": "MEYCIQCynfsgPW9byIE9WtO4do/eybXOEjvMX4rN1LHVL0uFCAIhAPeUv4CKRywyQvqv7rB2FRCzbep+ImyaOgHAYujvertc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSft2CRA9TVsSAnZWagAA/xEP/RdagbejfvTIZymSX/cl\nMcU5pcNtHPmDNPG7DND4CUSwVqQv+N9hgsuvwI0BaZgCwmGDWNygReOL1yOx\nzDDuh148oDEoGCiuS0khfYjWodePL8pqkwN/7VCCisVhXHibIatdyhTht6A3\nzv7GukzOXSjpLRCM+/0RMJOqDP80mcrlp3plt7vIF2NHHHrIhQzdIOooa/N1\nNegrsY0c/caP26HtrTeEpeQbbQCRUIucva4PtIcbHcl1KN9+DC34jo9Pgw54\nCKe0ozdUEXwqpiWIBe77R4WKX8syKBgeqHwJTBG+7501iqA+4UqHKTdUIGlF\npFSkwrlI3GxZ+ErnTeVqGjS65It9Htr0UEVnreRJviBBSd/foiSrnUMKj4o6\nsi6Woc/6iXFg5/JWsvOOQ/BzcL7A1JbxbRaFvXGVfQEv8tgljpLwfcniG3+5\nZnheHxp8sYAKGcgv5MGq9Y/wA7qtfna1SFfqOsnabz1H33QaCN1i6+7sctn2\nuRaA73ft/2O6id386cQF77nnXYaun4aAjVTCFP/EQBlN0jufHF6poTomz1tB\nOp1wvWXO0Ti5RrXcTi8jqE/MXebWhenPiX/5FkkB2YUxo+QSaRy+PNiTJLVS\nm0BSDUewmdr0XYyFpnJy2fsuG26gzccF7gF1jez4eBNy44sfr6blqhZrza8H\nptan\r\n=ahAj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.0.0-alpha.15", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.15_1548352373819_0.5659867840846318", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "jest-watcher", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "jest-watcher@24.0.0-alpha.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9eb8d635678e0e017703a7ceec6de5eea961c64c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0-alpha.16.tgz", "fileCount": 13, "integrity": "sha512-o/I5fbVO9DxpZGDXm+kQydXtmy/BdgEFs4STloUVKtrdbLGWa65WWWauALMSEW4/uRyQfYgg8JiPOe9XKGrfig==", "signatures": [{"sig": "MEUCIBrgBm1O2YNTV5yzPlLRRTCkoDm5L3vu4KsHyji2J56kAiEA2yVUOvGxs0tn7k4TQ0w5kcCMAjYcLYFa3ljc1gSejNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxI9CRA9TVsSAnZWagAAB2QQAIvqhIVicKGAwRkfb8sU\nqlI0wk7aCyQiLgRIHQO9gUUDQMOGyz8p1DAc6OrFHSCVwwk5oS8R3Czj00Je\ngyANnm4ZThSXGYyOJ1KqV4nk0g1/JVbVJHf+1hrbAmRmNWE1Fxw2j3weBm+1\nVlmEo/SO5LfdIXa9ezDkcSC8g1NosQAHV+fNU1pAGj36YunCQeaOKR2WNBUW\nR2Q6cAc6gSY/vEGAF39Wi1ddF5zhVXTkd/Y80KbpIs/IYXx2v8AZQbC7AIsb\nocWtfERZBwn4b1XcJm5MncQB7CzwE7nERznCAY6WamPq6ogiYJNdF94XsY3y\nLXiak2oPa2NTDeEasvbVfcwLGdyL46tu/Y1hE7JcT3hQN11RzPwRI6mWg+p0\nijSnymKX8tS/89J2SdeOyrkSHWp2TrGmk3H9tRKYCRgbwREobu9Qqu093cJm\nCIt/TV82HhBewDrAf453eEmnsOt8VYwgdWgobOZpsHrjg1ecmkb19hG+CIX2\nq3RKmGRk5YmT740wyHadCucOPYan09zJAF0CP2pTLgG6EsF/lrPShUiUUxIM\nyOT/k/gsBpUnbqzbDepRRqOEbmMAf92By4TLk27bvABKUFAw+REWZt7BBa5f\nGbBh1u/3A77qEyS3ZFgHYgzg0B+m0touNwWXe4WYghnNnH8yV3tNpMOW+Rm2\nPAXU\r\n=XUy+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.0.0-alpha.16", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0-alpha.16_1548423741363_0.8134124080286895", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "jest-watcher", "version": "24.0.0", "license": "MIT", "_id": "jest-watcher@24.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "20d44244d10b0b7312410aefd256c1c1eef68890", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.0.0.tgz", "fileCount": 13, "integrity": "sha512-GxkW2QrZ4YxmW1GUWER05McjVDunBlKMFfExu+VsGmXJmpej1saTEKvONdx5RJBlVdpPI5x6E3+EDQSIGgl53g==", "signatures": [{"sig": "MEUCIQCb3OyQ3N4FKYRzFRnTwncPpLWXJxGmH/sd/DDMldpQ0QIgIfssgftWG/PT1HLOCKNsQK6BDA4G+hISTlkwwkbDyNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWtCRA9TVsSAnZWagAArjoP/3fISNF5wcOlosgDh8m3\n31Basud1gMhnV3u2R/frTaPpcLjFoDwCsLDpoIRDd7W+4Fn6l47vJ4MWtS3e\nT2J2SaNdQnT3JasjjIPoxMgVDqFQrrETvz+BaUY5O+2zm2daItF/U/1a8nwI\n2Dmbcw+VpxGoGSW52Fc9t+9n5UXdMIm783xhJw8NZzHuxkwYCffLj/xfJ2ou\nDmqRwdhfgvZ5b3Tg7dSiryaT/Rv1yTbQokxXXmUdOQbSpo38191s6BE9mtKC\nGM7z5Rh6uucDmkgVhNGptBRG3Lkz0e5gV3Sv6NWivYx1E2YaKjnmA8YuHMNw\nek8WmEyRa3V42nchvllcEjUZxFdECT6iOnu23fx23o9zLkpk7vWp6fxbiddr\nox6SPyd6u9ZoE78XMEKy371Nt4Kn0lM55BWSSP43VMz8iPiKxZiQZtkKyDOa\n+oUYs5UpARG9oO/2lXraj0mLAPO29dRkzcxyy8hzeW6jytViElQcchmW6Hf1\nx90HdCrfp2qCs4wxB/O1jnQZ3TGrgBJCA5c1TDo94f8I/d+akXOYYRdcAEqG\nISVZHaRvQWwONYwBU+L9sL9KC/CbFl7DMZ9gdNUgvIuQiJCG8hPKkkrjhosM\nPgD72hghLrmL6ob2ygazkjI1H4LpRv2qNfgujEn2wd6XYmBTBA0dIazQB0gs\ndjjB\r\n=rI//\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.0.0", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.0.0_1548428716865_0.8660417968826837", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "jest-watcher", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "jest-watcher@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c9162c93c1e779f8eb0f95648fffb44e5d5d93f6", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.2.0-alpha.0.tgz", "fileCount": 35, "integrity": "sha512-knzllLNuP8w0OqIA4O8H90IiuufgqzKnhcXfGrxBoWDJfeuhA8xyRUpCfbiKf2R6zrzOAc8s2p/KAgvoaY5uYQ==", "signatures": [{"sig": "MEUCICLFKQlJuWB9/dZ0Xb8z6Vz57cWFecd+MiHsm9exiauFAiEAlMq2lkSLdExwdgjvUs8tneinIFhD5HnubeuuY40lcss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfo/8CRA9TVsSAnZWagAAqOIQAJFUX2xZ05sUurrEXEBJ\n4LMYSVQGPQC+bwJqo9Pm67+ah3UEo3l3VLKR5KcysGJtjHkPhpWMy3/sWBoN\nWBjMkiPyr8Gchyes92jGqdugrNc2KmCRqWXLStAVxM+tqdubMM+RpeRv2hhN\nknAp0n5tR4d3Pulcc1juSX6cQLhHznRxDa3LuzxOUpFBtU1UNtyOTc4R+EFx\nz8M/H+n7WGgKVQGigMlWGdv2p4dK2o+8PXMavlI4BxJCHK2/9tjxR1MS0EL4\nuzR+Ajz+0NSZ1AdpHdDJNV+PIH6Oh/FfZovVb2K8UV1Nm6C8RqA9J2hzEhR6\n79BNEhf9+sHzXCkGqBA9gExFJKHl0cp8qMtg7wFQXksPzsY8apU8f0xiWgAa\nOtwoFaJVvczpFkgtcVM5drkb2Bz/Xl4+FgdEppX+WmWzBS5frkE6YwCHm1yS\nWD6k4hcXwkVOxX+HFGzZDP5hqBnCg/hyejC6Gi5d8uEIp5ATEL0ND2V6q3U+\ntzyj4vEiCVAjKD51iWmL7BpX24HtLckuhJbz5wYqbdO1273l6C5oiJKUtPLt\nWGqV/kwEXCNJkVcoVNAOThjbEUEWa/vwRD1Ti+ktFlBk5xPBY6cslFKYppZW\nwgfF1JAkSmbGtsiWv7iUt6+2MjR6OeLZOAvOuBSASuG3UfrBbnxGdpAF7Ppa\n9Tki\r\n=+wQU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "5.6.0", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.2.0-alpha.0", "@jest/types": "^24.2.0-alpha.0", "@types/node": "*", "@types/yargs": "^12.0.9", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0", "@jest/test-result": "^24.2.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ansi-escapes": "^3.0.0", "@types/string-length": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.2.0-alpha.0_1551798267736_0.41718472711886223", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "jest-watcher", "version": "24.3.0", "license": "MIT", "_id": "jest-watcher@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ee51c6afbe4b35a12fcf1107556db6756d7b9290", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.3.0.tgz", "fileCount": 36, "integrity": "sha512-EpJS/aUG8D3DMuy9XNA4fnkKWy3DQdoWhY92ZUdlETIeEn1xya4Np/96MBSh4II5YvxwKe6JKwbu3Bnzfwa7vA==", "signatures": [{"sig": "MEUCIQC5J3kVwlls4G2XqCyVLRUVlkFXPLb2vHGQ0M3tzcbX2wIgbk8N5KxZstE5lT9Z6TKT35u3am3sXtz3hZnwrsPmDjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRXYCRA9TVsSAnZWagAAhKwP/2NFSg8jiyeHXM0516t9\nzmXkhc4COWRPijLoprqMycjyGAufDu0l0RI6iK4ws+s1w5krUkIcdnTXAzPD\nP2jO9Yj+Pa5dX0YR3s1yKC5RUUGSYPC6rE6/b3CuLRaO8oIF7AIwRFrhTJbO\nKoS/l3i5+ZASA4RmMf7gefwmPFJcQANAThbUHm+wcb9sR+GdmB2Sdys30GB2\nk/WMyYFo8mKFPeQSS3TiBijRuzJDl1mxl/f6akEhfZ4EFq9fZKs5kbpGtaIL\n17pg+KMx5OSNHWcIhk+H7Oyc3JT4qh+b0PbKr4yL7RPE0+vU0QCoDdckjg25\na9DMWK1Ux/Roy3XAKzHTIEH7/hXeo0DcHoi58O+ROTKTWtcij7mJnF8vLlyY\n6DhLbXRI7SEIdfuqZGStyzH9g9Ok1aZ3OuDEPMO0drFsIryc/hbNFsVkYjpb\nwbStInSQEeqJXQ2tbiZMJ7z5fmpaf6lF4vPIIbRWJEwDvLbXKUstTRQZ/Z/j\n5hr8PDLfS2iNRhVcSpqdwyMR7gpcx+mK+RxgRpf+pUjRbv6QNLFleINQJN4f\nAFHiZjuJr65lCJR+P6nD9zk2IWipokTXjwqlWbRo66YPzDDIM3YhaaB+6nXO\n+hUrvS6M+JBGIKUVsyc9QMec2wgLberhJjDz06uOZLnjnfUp2IMxNlDc6RFh\nOUJh\r\n=Rpzg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.3.0", "@jest/types": "^24.3.0", "@types/node": "*", "@types/yargs": "^12.0.9", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0", "@jest/test-result": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ansi-escapes": "^3.0.0", "@types/string-length": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.3.0_1551963607947_0.6413169327121162", "host": "s3://npm-registry-packages"}}, "24.5.0": {"name": "jest-watcher", "version": "24.5.0", "license": "MIT", "_id": "jest-watcher@24.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "da7bd9cb5967e274889b42078c8f501ae1c47761", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.5.0.tgz", "fileCount": 36, "integrity": "sha512-/hCpgR6bg0nKvD3nv4KasdTxuhwfViVMHUATJlnGCD0r1QrmIssimPbmc5KfAQblAVxkD8xrzuij9vfPUk1/rA==", "signatures": [{"sig": "MEQCIAyIZT+TWHyqJG8eOkxucA+IHwgeACCirRNpMYUoFTYUAiAvK5hsecslUqfu0BaG+m8WLJZvRWC8zvcEEuD3rElHEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch+ApCRA9TVsSAnZWagAAllgP/3rClW5ZlrqVlla3SBDX\nuDWI4XauYlINKaPozHb+5XEGoREOmr9xhN6DhN9+9RKDVlYQHItXNFdnMwtD\ndZCplahAW9lumRuQDrSsDOPJWBVCuVhHqZn4edCM5WEhBbcWIcDsMqhY78j9\n17a38zcxZbv/8Bz0ftR9ZtIT/kQJLP38tdO1lxzRbe1PVGNAJyqEYjOGzub5\nBdOISpWaLCzA50nnDu1eCk8ataSkZwJ63Ny/klzDQ2BuyxBg6KycWzVdZxje\n4hH6hutVRTOy/5SfPmjlkNG1CuSoh1IzhlkHAFD47CBbhsd1c6wHIsjXBQ1t\nCcQ6DLbF85+26TyYD1kqHUHn1WENXMBQQqzvv+xxDC7I6tCWYnylV0eEo1Yl\naxH1RGjZMpi1iDiFB9H/I5CzC899LcXO6b4tXupAjNiLczU3ABXPCtNMncZO\n+E3tImzVoL1FR5yHsbYgFvlibsVwYitJ0KusPd8prPZ+JMwz0Pr4jzkIJAyg\nAFMC5cBttWe7eOxYpsRFC3iewJR5E8TFHExg/7sOpjaPDPNttP8R5pQ+Xbvt\nwsB00KJjwclZUjTETvTGfV3Qp39I35zAGHtZ5nJAm02MR1/2fHijdwZKmmQ5\n3QI4Ttpw27NFaf3DoBT/Zh8emdpnEhutasD5lN4PkHytRU6rKdiy+Kxi/Blq\nT1UA\r\n=+/0X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "800533020f5b2f153615c821ed7cb12fd868fa6f", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.5.0", "@jest/types": "^24.5.0", "@types/node": "*", "@types/yargs": "^12.0.9", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0", "@jest/test-result": "^24.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ansi-escapes": "^3.0.0", "@types/string-length": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.5.0_1552408616756_0.3053976743785287", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "jest-watcher", "version": "24.6.0", "license": "MIT", "_id": "jest-watcher@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f66a49a4c89f60626730121d74e36dc006c53c2f", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.6.0.tgz", "fileCount": 37, "integrity": "sha512-u9YFF8VjGh8vRwuNpuVUAwZFZno+lZuqayITjXkwEsWumuUNx0s9/6+DvB/AiQx/FxcpbXlMDNAflFa7vs7UHg==", "signatures": [{"sig": "MEUCIQCelHET7O1DACKcWS6zKq2pRtGiqUSeuCd1nbxrqdAKPgIgZ2oBItAM16IaCNEWsIZcTVSW0K7Sr/PZU6gkj2NqIXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAqCRA9TVsSAnZWagAAPVoP/iGQsvZhLe/R4Rc0JUrQ\nkUy34RfIMK5OI93ndMHB0Bdn62tptJ3sZYcSKl3lewBL+PYgPDfGEcaEXoV8\nbWUor2d2+nJ6e4fI01i+SI+f+a7+6F6XwLRit+mqUQOCphKPpJrzfs4tLQL2\nFigV8nocnbkV7d6A/wee6jW3fNdAEMhfeJ+z7UN+Cw2PpgAvNY8BuuPTGpQR\niT2xf2KyeO9A+0JDrup1OU8ho7ptwaqEeBzVqPiyOOLRCIDkquoNZcfJ6RvY\nhWY/Le7mLvPlRQD4XLkMppuzp2m4xWZ4lt7mQh28o35w3PQJZkP/w/aQNJa7\nQ+zzqSyDn4U5SOS8p4H2c38f9LZT20PTulqiyxrBSGEcaL+bFzUMRvtoQYJn\nJsJGy14wTX3Pcn/r+R2xm4f8AzZDdfSHsb47TZ2M52qWgonPhuzfTy02qJpd\nKV/Uv+vmFta7LIywuCBB/Ck9spbbWZgblEhdursfYoTfZwzIvP+Km2fT07vW\nDLtbfqoIkWq1TkQcKD3DOE/Q3yuS0uq22VxCNCo0shmA0Ef6mgbnajRt+H8H\nxu1sNsAF+O+E2MXKBT/viMimtAnEV9Mp/ySnYuZpKn+6ivjlInAyW1fExwU8\na2I3JpZO5zhFGJD69PdoTlXZHKmOn+mtcRrKFLaBk/mNoepMV0YkXFPNz84S\nYJ9Q\r\n=SCrb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.6.0", "@jest/types": "^24.6.0", "@types/yargs": "^12.0.9", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0", "@jest/test-result": "^24.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ansi-escapes": "^3.0.0", "@types/string-length": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.6.0_1554157610097_0.7679089370877916", "host": "s3://npm-registry-packages"}}, "24.7.0": {"name": "jest-watcher", "version": "24.7.0", "license": "MIT", "_id": "jest-watcher@24.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c0229f166bfe1561f939f7b0d83f12fb512f2a2c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.7.0.tgz", "fileCount": 37, "integrity": "sha512-BBDn/6iG1dSM7fR7FBu5o6R+ZwBJBhKmM2tAqpp3yOzZD/1Aerhdx7laLFs2gajWpBzC7OEHr6yMddDX+6n0Mw==", "signatures": [{"sig": "MEQCIEKRNM52nNCtAC9IbxWT08+FrlVlGFVSWQ8j2Hv6YYMCAiAO1WvS8rpjHK+WZZQE+79xqZfnEP+TkOEPK3a38tc4Gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC6/CRA9TVsSAnZWagAANO4P/2ovw6XAx+QQNgo8Xv28\ncZSnaZtf/RxSFVn0vVXGIwOU/Be33GLL8YWNGl2sLoCQVHC9EX/Cbj+GTyJL\n6/uMGExpKnsM6m/yaaHPGyToDCt/8suz1EQ6wMG+ihEXjuX1+Y4Sfa+90xiY\nmRLycdhA4ST0F72nzONxbODE/83/OD7gPeI5zSHTxQeMcBfooaYC6eGq3jAm\n7NwWlt6iIUvl8JUGLxM2dn5lRkynp+VNiMv8kehsD1cbbJo/AeUetcSPCT8U\niSWGKNZQVVXgcYkZ9WdNyL5z9RECJDSittpZmnnZlxESuYNt4WUel5ID9L7o\ngFxCccJlxNHMy032Wzko1UPX8DGrb9gFrfALqL8X8N4ucrMEByk2xvlsy+7F\n8WWiMepMNVKFeEQz9Bh+zWPrcb4wLOw1rHQIY2hQrBqKjwXK4w5GZ2XEpYg1\n7q3dDzXR8SFviqaaXbPQ2p4zXSM4maKjCBqDb3VwlaBhAqq1xe2tpVBij1Ju\n5akh+oB6Rr/NdEjlEXZfzvrcwlNLW3pS8r9lFgoNtDAdCA418Juu0qUCCdX/\nbXGjb+imZzqXv23PRKXqO+gjSpUJWpvidJNm5BFNot/vCE7ccxGP71YbeoqQ\n5H+WOxW79NBsSCmjqJSdT/p9OTm+hWHn3fcqdUEC/C6by46Gz1Bqt9TaHsof\nhFTI\r\n=oWYf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.7.0", "@jest/types": "^24.7.0", "@types/yargs": "^12.0.9", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0", "@jest/test-result": "^24.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ansi-escapes": "^3.0.0", "@types/string-length": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.7.0_1554263742267_0.026995751684679714", "host": "s3://npm-registry-packages"}}, "24.7.1": {"name": "jest-watcher", "version": "24.7.1", "license": "MIT", "_id": "jest-watcher@24.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e161363d7f3f4e1ef3d389b7b3a0aad247b673f5", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.7.1.tgz", "fileCount": 37, "integrity": "sha512-Wd6TepHLRHVKLNPacEsBwlp9raeBIO+01xrN24Dek4ggTS8HHnOzYSFnvp+6MtkkJ3KfMzy220KTi95e2rRkrw==", "signatures": [{"sig": "MEUCIQDvST4UDJ3RO1j1FKMcsTepYKRmGh3MH/vcg6txJvtHBwIgbRdOQnFUDMXZ+usWjJoVVkvTV4BzHdJqSNPm1uk/p3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpVuFCRA9TVsSAnZWagAA7W8P/2zTQp8XMsZPolWZumnm\nX0jl1OL8T3KNKtQlMn+ATkiEAxOHBz/ldIOdf4PvDhX9IPki3DDDHhDFZXLT\nB06MnwesKTHAXHdRLAexp0Yxp2kAHLczkB075BnCvA+T977exGykZ/ULq89L\n38x38x2cdAgD2yJA58pRAgkorfiKppS+ki4rSDCxzIv1DXG0THh4PSFkHr4b\nXokTUDuszOoKjmmJZCev2VaiIP4POB87npXpMLxgInQcBZ0mnXE4f1pkrHBe\nYbe5BV/pPXonavSoZw2BJvC0BAT6++6++090Ol7tnsR6nQnMo652/hMLjs20\n/MjsGccuawZzdYXItimHpQL44GdIjnB+Wwv/sw4v+v7Fdy1OEO9SUCE2eILq\nIGG+FQFsoR4DR8mhjhJuIsxq/1Zbfik6aoIZRFiyt6McJj1N6B9Xl1kf6ZLJ\nlzpNrmDA63I+aBLldSzG7pszykclmzAPApRa492GF5MjHArpSfwOzAaFAHvJ\nHpuxDuePB9cEvIz9ZcoLbpevS6xaSF1Sh7+GJslpCCt0fZBNiZCBuWWFRAn3\nBMLUxg5A7yyfx1mxz6KwHzMH6OJCmTXNM0lhJO5+X7ia1WSzc0UZFvgWIFTb\npcM8GQUl/dHlYDRD1D1rJqLhqI9kwSGA+mu3lpt0xUBCLJroCewad2dZoHzw\nJ9Ja\r\n=47/7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "0efb1d7809cb96ae87a7601e7802f1dab3774280", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.7.1", "@jest/types": "^24.7.0", "@types/yargs": "^12.0.9", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0", "@jest/test-result": "^24.7.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ansi-escapes": "^3.0.0", "@types/string-length": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.7.1_1554340740859_0.20911660068946514", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "jest-watcher", "version": "24.8.0", "license": "MIT", "_id": "jest-watcher@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "58d49915ceddd2de85e238f6213cef1c93715de4", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.8.0.tgz", "fileCount": 37, "integrity": "sha512-SBjwHt5NedQoVu54M5GEx7cl7IGEFFznvd/HNT8ier7cCAx/Qgu9ZMlaTQkvK22G1YOpcWBLQPFSImmxdn3DAw==", "signatures": [{"sig": "MEUCIQDbxtcYrpm2WrfWgsBkB7sGbWy3rvNij0aY2eCnD3FzuAIgJT+DigOQfGw5Nqtcd2bLTy0nmPIkzXk0LE09Iz0vLaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 228065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkRCCRA9TVsSAnZWagAA5HQQAI2+l8Y6Sh4w9+Y6H8sI\nIWOu7UC7tDAYNCiF41YB+ZxabXEhld87BAErYKdhyWfP2s/K7reJ5XdTtLcM\npc7pNtSdxQKsELdn84lzAXfMugjw3+xQ3x+oAXyNOthHQU8y4CR6gcDNf4sJ\nIb8+RPQ6hl+BavKvKb6XUD/UNVuEAivKROvy1bcymV/SRowOrneeFE9pzH87\nIo7c638uuiOGw969Sdaza7nowHp+kWwksb5CSbWKgJGbwGgdEB3bnxwaJPlo\nISWEpQd9b4wB3VZWltI1I6cHMAR6gsXeuLkUuSSg27TAsXXkyWkhOYnzsEk8\nd57X7HMhDZbYODYfazg+2joq2OzHsqOe53HOnRRifFZhYkUyPDaAgrPkuYLT\nxn7IUl7pGmkll3D/E0xztEu+g1xshfOYlXQN9tSKUuR690mPoRfwHM9JuIog\n+9vz21El2BaqVq+Qj78Bvd6iULOArjwO+8p2qzTjiWCAGGZWlaxFkYaBXs2O\nJBgqfKbO5YntYe4GRPYo/c2q3ZQqpJK0GeIOgh+oMQ2wmthHNQpwT0FEsZJJ\nq5NEbtGb5yYZ6cIGSPLbsJMsZcXe8kGLSa4RVxqb3wAj8qnOrNPBz4qIRb+5\nl0vWAnyoM9J+Ozitu1qkfr2hs0xAyxs8PsJIwxsCnc/LCHlszn9TPzE9Lmc5\nrPCS\r\n=uG0G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.8.0", "@jest/types": "^24.8.0", "@types/yargs": "^12.0.9", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0", "@jest/test-result": "^24.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ansi-escapes": "^3.0.0", "@types/string-length": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.8.0_1557021761949_0.839294658630916", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "jest-watcher", "version": "24.9.0", "license": "MIT", "_id": "jest-watcher@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4b56e5d1ceff005f5b88e528dc9afc8dd4ed2b3b", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-24.9.0.tgz", "fileCount": 35, "integrity": "sha512-+/fLOfKPXXYJDYlks62/4R4GoT+GU1tYZed99JSCOsmzkkF7727RqKrjNAxtfO4YpGv11wybgRvCjR73lK2GZw==", "signatures": [{"sig": "MEUCIQC1gU7g3akBNr0QDM7hB3ggDuHmyLNgQIkXNzLABIHIkwIgSZOQdWn73jVw8WHCT3fI+WOSerOibBXQgJUqli0lNus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkWDCRA9TVsSAnZWagAAP6gQAJ1pBrwd+kcn3y6lIsMt\nAJi0Q50vndIPraHUrtPuwf/19PDFYFT91lIDHPkNEk1ddB2Gt17xfcsWKLdm\npWlJTZwRmvVCp56OGLjLE7Xta3G+Kyt6hcovM5/wes97jdA+MPw3CvwCXyjP\ndhLY0QKftnA4iKQgCbEkyWAtVUZpzr4wdUctzTqPWleJlxxM1O2E5Hic2UdR\netr3UzoPDUTVzjLIXKqFFhweqizwBRYOJoJu5oysd8GWRR9cXK8C5/SbNz3U\nMmnsr6plmmdiEfC2Szm/I8O4EbMflRZPzt0PMyCR3HQP8OTg36RUvb4SfGEK\nybJXUz2CPP7xHkb9GE7/aLaOMPd+jEiSTbBIrPekQNAjuElvnb7qm3SiU24n\nzDGKzU6SFp4ehHFhpBzPXuIMax3zdpvEiihX9sE6Rr0mSChZ9grBlASW5dYL\naq7Pgu9QxI7jFIXQZZu5HBbf5DVZFHKS8Mj4QDe3ulyOCn+p4y0N4maMNnYV\nHp04/bhxw+mMDLkXA08W/I/sih9QSlLSJm30Rbfnkk/1y59Y+BikcQdzRwQY\n2xKhs+HOcwNS5wkvxaSCCtZVzW2e3OY1FBnI1lpFVZT6A3ZoFh9RbrwP0WuD\ntTsWYjFlEReBaNczNS2rRlWQUwzZVDnMq1za8dDIdL5RNNeF5FitKEw1GzpU\nMKlj\r\n=oFE0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "jest-util": "^24.9.0", "@jest/types": "^24.9.0", "@types/yargs": "^13.0.0", "ansi-escapes": "^3.0.0", "string-length": "^2.0.0", "@jest/test-result": "^24.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ansi-escapes": "^3.0.0", "@types/string-length": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_24.9.0_1565934978474_0.8863418729408263", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "jest-watcher", "version": "25.0.0", "license": "MIT", "_id": "jest-watcher@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ebbaaa680acc342e0ff50a6ef6097def04a86e70", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.0.0.tgz", "fileCount": 35, "integrity": "sha512-BghKLLn999ZtyAInLvCY9/VJZ5AjyBAHoKlYstpwUkfwbKUYtq0Yh23K2lacSeWiHDOvL5tEx9ydD2xiBl8kkA==", "signatures": [{"sig": "MEUCIGnHQ/CEsxqZF7FxjSN2RkTPBiZ993RFvhX9p751sNV8AiEArfBxxEtzFct3UwwG1MK5uL+zUQxN7CHJuPPi4NV3TXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgriCRA9TVsSAnZWagAA0mUQAIiJoToJtQist9ABfDTQ\nGD8JI5Lg/tLQ1Mi2GFrtorg2jQa++JXjx8PTtBIqlbAI8V/jes1oYvp8Eh5s\nyp7t/nx/xyB0FKQCNpF6BpaX8kh/P5JqV/0dLEJ6oKlDLNYFm+Xh7dVu8EUM\nXhFkcrgRfiFpq6muzkvOleyft5twGIp7k3aMlkJoXmC7UmcWHP2qaTrjZ19o\n1ZAETBKA60XwCeJ5lWrlLc+FAVWUelDoUduHTF6zdlirW5tvQVqCwqdq26o0\nZaQsi9B9DogG0I+gGw1pPtR2RBDdAEsbKGCtI/rl74lTC0Jd6MMiTK+QciKf\n8WvAcFsQ5Ddi65piAWarNmAb6M8o5FTSFW/JK2VGomGCsQAHwJVBIy3mXPVm\nxUGE86LxW2PBiFOzmAVwhAVzJDuFwCfVpiEYtz/2+BZCijQt8hy8HdkGhz/S\n+h/Ok4VrFTBnpWwii5yOBEFmA1zqzJeKdkpC5NEPnlccUDF/RLbrTI3F+uuX\nzsu9YZ6M/sOZl3G3O5X/CMbQTiMZzNBv+CABAPBbuzp9T4JH2xgv4wmsU7jK\ngmUMIoC2vwZ9oBkYtHUb7tZkDGRJ8zdtoP7aSNWUWN7zo4OtqDOK0DctkwQk\nNPCwpC00choOmw2suRnx2mJAQ3NxwuW0y3dHMIMOvXfaoV/sUMIynAnEpz10\nHGLb\r\n=NUXP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"chalk": "^2.0.1", "jest-util": "^25.0.0", "@jest/types": "^25.0.0", "@types/yargs": "^13.0.0", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.0.0_1566444258384_0.06931446067790281", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "jest-watcher", "version": "25.1.0", "license": "MIT", "_id": "jest-watcher@25.1.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "97cb4a937f676f64c9fad2d07b824c56808e9806", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.1.0.tgz", "fileCount": 35, "integrity": "sha512-Q9eZ7pyaIr6xfU24OeTg4z1fUqBF/4MP6J801lyQfg7CsnZ/TCzAPvCfckKdL5dlBBEKBeHV0AdyjFZ5eWj4ig==", "signatures": [{"sig": "MEYCIQCzvph7iOo8PjkZX+flQTFmOfQo0S1qkt/poDPNKINzugIhANGsIAOq5o+nvcOdJB0S5RVUzckgDKO4ZUg3FhT+1uOg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56cCRA9TVsSAnZWagAA4EkP/R1ewX7UugzMLaL1P16Q\nwhDRIIb3tkwK9uSx8O4rH7Xv3M1Bq5Ff+6nCr4NVMfvHAE9JW619fMqwDMXu\n++owugbsiQYBawwl7VOlwxZN1Rgm0j+m034VwucLL4tIF4AMH7BF4URxhM6+\nCWmMtieCcCqQka7FGnrUsROWkGUFrkXBjeOJnRKsqcsYqmMknsLOqf3og+12\n4dd8sCIdKcu1RpsKO+/2aPAMpYSSN1z/UAz77XrahWs4eASZbih0UHkOrMus\nfliUUoZIFz++7xAiy/Mr5VRmO2DQhUNsboYrtkluWRinzhYKmD8EePbF2gTH\nbHfSU2plvVr3Wclw5ir/DXmVOFXy4l1Y9dTyYAvs3/q/iAGpPdNCTM4UMJDZ\nVi/qIFfG1gaG4zGabF8Ko6HSLCa0KOUqfHkgyzgwYB2EtB0Mobo3X0J+Kcq3\ntLoSSJI7lXVC7uYMS0BsNjFEvCsrgGUJTJUyXr5Ox9UnszCCrxqWWIpECvLf\ndf17Zy6TDLEbJoHSsTnE6DbJwyysvU5tdR/ahusydIQvjr0NredBRt1Cu9lb\nOn1UKcPgKgk4/SaGZJ+1ZpbK7cTk6A9qkZJypKPurT/aih9hoRLTdUfK7x9l\n+ewfBKvpEtm2Ji910vqhMTI7H8iFCIaQl6MIFxHQsJ3gn5RjQzbrtB2/3eTr\nsN2k\r\n=lHmq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.1.0", "@jest/types": "^25.1.0", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.1.0_1579654812136_0.13993135988873728", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "jest-watcher", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "jest-watcher@25.2.0-alpha.86", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "15b49667d0f8b6ea2d8331fdde2d8532d4264d88", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.0-alpha.86.tgz", "fileCount": 35, "integrity": "sha512-MVfFynklvKlsO6B4rCXabHZ9L72hPVsq6EFXKWS92fP3TsYc+K3Msi1pv1SGCh/KBjt40HqhnoVLX3ZEuQwKaA==", "signatures": [{"sig": "MEYCIQCrDFP409AwakiYbmPVsFXzWQpiuItXUPj8sJqdCFApmwIhALOf3+h4XTOXKScTYXbw4LXP82oZhZPBTLC9fHjiluly", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5H/CRA9TVsSAnZWagAAelYP/RGvAEb9pNi3xXf6CfQ1\nP1/nDfoEb5laY0AMsucvE9Y7IVfUDcH3Lbdiiy/0OIbh8sz6mIcCqJBKYftt\n1GIOOAKDZj4Zj7K6X1iD8rXv+9Eicye9mdhKuuBcK8QapZSIrALbj3YbmPpE\nZKrt3k9+0KCtK8KJokv1y5Qsv6Bi6Rp1Yft3gBT56rkaUEOVcrX2Y6hxjAvA\nwrgxxfWFpe+23/vED04fQPv6dpZl04ltmGtohFVim1++TBnrIP1aUGiQGDaI\nKJrolTlzc3yb7ZJPfqNHwkObN0yC6eL2t9vJCOcDQWyYzLcB+kpDVWQU89hB\nnvYAB2EgYuzQPQ8XeH5oNLULm9bdJt6DgSWYq0cpTJ8NEAc2CYST+0+DzgXD\niMI7HK1WQ2OoJUxVhHg8rZKSQZH0YjkOpyCuyQkihlEaov+mHa366Hpkg1+R\nuJvl0/5boTkCgA+hwrE4JGIdxQJN7VfQYBO98d8FbxD9ILYbKbh1q2UjXp0z\nmikdhsNktgRSlV4MQaB/Gsfkc0ij5OYSiRKJ3NO4J3S2IzvN/VK1zNbKeVWJ\ng5d5sCyEPgHMcYYTnzIjiogrcQGQD6wSEmWKlhz7xijQYAV9gE4xNZhBwvNh\nLxeBF0Mc3ed7wtfPObsS7fzii2Mqw0SNIcDSyQHMPOuc3xcFbjGMTeQUt6q4\nNKi/\r\n=QfEm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.0-alpha.86+cd98198c9", "@jest/types": "^25.2.0-alpha.86+cd98198c9", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.0-alpha.86+cd98198c9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.0-alpha.86_1585156606859_0.8655100805337206", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "jest-watcher", "version": "25.2.0", "license": "MIT", "_id": "jest-watcher@25.2.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5cb37355602b743eda565227ce2252bdc0ce92ee", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.0.tgz", "fileCount": 35, "integrity": "sha512-jfUrHJfr4OEhJ0oGOqzH5yAXsUrtFWPalV2o6EI72T3Kp/mY3roUj/8RMmi7md/fL2GJ1BbcWzsQuaXuDRhJ0A==", "signatures": [{"sig": "MEUCIQCEcsE+4tmzIouI/0z9MFqxCKAPibkA3AgEECD3XTqnbwIgNRFAUTBeaFE9zqM6bQwMyBnoVD8+9Ty9a6/7byJw8zQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5vLCRA9TVsSAnZWagAAdxcP/j2EKv6Hok/17VS58nDu\nobzlvh4jLzhECOzifgTgw3xkLEln1AJjsArtM8WhX3ADqRZuz2/l7e6f8wLu\ngSwliZg9EnOC1pWSQbpXJykE7U9ygPQhWMeElVsCHwwobGDo2nm87IccuMJq\nCVq6zdjI39StjoPd48WAEV0q1UkTpZR15x5F95S5I3oj/ZfXb03ZuucoO2qb\ndVffIFXhCl2nnszNZ4BA2jiV5bCJimwXGGUm4zlSeg8Wqgj82b8iw+/dsnNC\nECy46V4QD19W8AyUTYCepTlbSxZpFbdTm8wgiwXUFpVS3KMRThP0toK0ktZv\nV1Zar0ntj9yS8nMm2vO5Y6vzqz+KK6CY00ebJBBZxOD6d2oC4YK26MNpNv2R\n815qWGdEWjQrMihL463hVaPuSy8uP1/7mnpTd7Qi6+t5kKlJwvnIwuqG0p0R\niemEEixhsMZVza1k85KESwOZH1RinRSjqXQ1rbJm0bObagK1m/OnMOUjQ4dR\n102RUTNLnapqGPpBCk7xvWjGbycfANP8opbkMsE3VvmSrLOQJ92X1/WeO0oX\nmrIFXzsMB58ZX6ht3UPtGxfFIyM44HxL7WU8jcd+Df8N6BgkT+frqnS415/z\noHRNFBUL2yqpKz6UsPLPd6ZZ6fiI0AApgBU5uAQ336VAVMqgjTv9wC87X71Q\nBm9T\r\n=kKmj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.0", "@jest/types": "^25.2.0", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.0_1585159115261_0.08041774043479166", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "jest-watcher", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "jest-watcher@25.2.1-alpha.1", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9e976243f800c216544d5e3f3c2852808dfca377", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.1-alpha.1.tgz", "fileCount": 46, "integrity": "sha512-X/mfxEKk7KaAMaERouPBIuSHJJqjDoMePh1TZU+M/kDGSH/AEqwqetMtAwD7hiZBAPmQwXHKf0NtrG0VMbj88w==", "signatures": [{"sig": "MEUCIQDNzcLBHDIpWRsgcLEPOHW2WaEld+9yv/v8cilImo70OgIgHqrtLa+sM0CaU+MW2loyYvdTS7k0PPVp2egD5LMgXm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+9CRA9TVsSAnZWagAAc78QAKIfGCtVdoOt9NUF5C1D\nxrFMFklH6UGlJx9pbMfK5ARDOCaCLgmawwfcAWCD1xhPVlk9tyLuVh/kMO6O\n<PERSON>Cke+bWzozyeDVQ5u/SWj4Ux0i9BKkyUxG8uLVvKi4GDNrMUG4B0ZQhS2\nMPGEjW0mB26YIuX27l+QeK4OhWijiNdpynRdxv0b29F+4lQyHEzwogXvudM9\nWzEotWMey0y50SrYA/7UY3ZVytH2ewdVSVX3KOUDkxlKeDRiaUzHiP7C+OcX\ngmaTBjY75N8Fz1VrB51Du7inKYGCOaweTpcSvsOMbFAik5YTR0NNOXfjOWbZ\nxYLClgfm6HF1vNlPTsEjh37E1BvYqkcwqn93EvIKQYxs+3tsosPw1XLFGcsD\nBCrRt/SFzekxyjWJ/4s4SC8M8mw9UNm3mop497FIbKUt3In7xYWc+jmtxTd+\nNBFQSPfKHA4I9WFQbdRhL554bSrFawM5Lz+40VFLufUFSvn9Hvk4rg5WT52R\nvtCAOzmC+bVi84AnfK2cENKaZCowc7kLVOl3ZGtrlH5LUUC1Mq6IySwE8FFP\nnTAdBsMfCDlMCg4nvgofti1tn+fYAWTD4hKUts59M3+LYo/mvTDVZ2RygpEr\nfLVAM9pP7gdka0B8cHPyps4Hkhy5zPNSBWZc86KvkrSv7ZnBarms4rY8fdpD\nxfdj\r\n=ZNT2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.1-alpha.1+5cc2ccdac", "@jest/types": "^25.2.1-alpha.1+5cc2ccdac", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.1-alpha.1+5cc2ccdac"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.1-alpha.1_1585209276923_0.6285677000599881", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "jest-watcher", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "jest-watcher@25.2.1-alpha.2", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0f2702fca0f955f48060e00206f92f2f712d99b7", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.1-alpha.2.tgz", "fileCount": 68, "integrity": "sha512-LNqVbuTAVjj9W4jilYuY2kMWB9Ck0ZU/qejOzIOyog8v1Zv5bdnurZOBZiTBACy8/EGKMMS2QMJWxEd5Jlk+8w==", "signatures": [{"sig": "MEUCIF3Ivd8c6UnC1pcZtmSKAhX9BWXHZOgqkeQ5QZdvJjsnAiEAoqo5EjgNi9INKajo7TDOEsXHFgMGGrpVIGEcK+px0X0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGOECRA9TVsSAnZWagAAeVAP/1PSfsQExLLnXhNZYTg6\nqIQKdZUceUvJ2zj84pab2Vrn5pxBa90ajnDYGHX3yTJm+Wwt+CoB4e2ImTo/\nyw8ToI2d3PuFP8s/l6gP2dLOakmugWjZYO0BJnPPKOO/29Flmj2fsVtporUD\nHKQ9Fvs0WzYa3t9Vc8gl3UFqAsEkhl+eZmCeB1HocJmVN9qNiTRhp1+uqs1l\n+OWfMfoWEQBwDyrUSqYMulWoVAc4E4iMC9YqeeOROrXZEoLojT+tMh6YGV7m\nMI7J5h/rGu3oDlRWassrtTrToNniJXgaNTg3f5MWQLrAyBbmh5U6yAZbuJaf\nKtWihM7y14PNZe3h2G6cnx3eXJCMW6xxK7gGhhJmfgOHp9bX8WhTPk8C2h19\nLgGzWRF2c4/AgDkdlzOnm1wwzgc3wF3L5T9y53GCVlL3t47Mxl3WZUeOcuKa\nt8RCJgNEsgcafB7wQEMipqkkxwic57THeINQP/HKVDT3kybnMsQdfF2c6u9p\nxXNMhdFmYFGZzoaKBCoL64eC4wmUV5JlmpLxPk48udHxvIaKf2Yyzjddjw1n\nLr+3/l7aCD+2zxKYO4k31iw6EsvTRdfIkjIrVx8nwYJqpGcdCLLRrJ8W12kw\newgRlD969C1M/mxXP5z/igaZW0NtAstA1KkK9iDF8wdfS5LwNkAWijbGHPfH\nn+pk\r\n=0bec\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.1-alpha.2+79b7ab67c", "@jest/types": "^25.2.1-alpha.2+79b7ab67c", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.1-alpha.2+79b7ab67c"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.1-alpha.2_1585210244148_0.004358228840322109", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "jest-watcher", "version": "25.2.1", "license": "MIT", "_id": "jest-watcher@25.2.1", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "605aeef37ee4ce867f2f58485fdb9eea0f8cb369", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.1.tgz", "fileCount": 46, "integrity": "sha512-m35rftCYE2EEh01+IIpQMpdB9VXBAjITZvgP4drd/LI3JEJIdd0Pkf/qJZ3oiMQJdqmuwYcTqE+BL40MxVv83Q==", "signatures": [{"sig": "MEUCIHT32lCoipufGSZ3kxrDHRg+PaVFMej9F/x2iqgBz7vUAiEAvItkMQd37gQ+vKVOB3IvduBXcVcNnw42tF42QRG5rtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41787, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9pCRA9TVsSAnZWagAA+dIQAISKStbJlVLnvZbZdnW9\nUEbqKDYtKQcxqKMOSX3q7RpOEArF1R/azSz0bU29DAgPIvWKpJD6LSeMF6ql\nmefss4Ycaj5vnJQi9WnN7H3+XdUIJ8kQL110OMKnEtHlksLDDtbdzd8tL3/X\nn6TER7OEf0HuUKXTJPocYT4JRvkgW5ddTTQR9kH45Ym+ydmbMsid9M+BTgEm\n41UManBbbiPXBNUpjuS8mywmEj6iqtWTIjTEogjrqY24dvQUxDii92Ry28p6\nSsh8QCB1TLoucKm8uBY/byvASVitspmM7zOKyjtXZCxp4ySIqhOXhvgbo+Wg\nzyHNbgfQWyKqar5Ebbj5+pGztPw2grLSzEV68hZissPKWOIRMPwcgZ38wYv2\n6GDV2LKurhY0D4fNqmOChmb+Nj4shDrZIvRRSjJonmyF7QsimnK08z+EM9wf\n17dH+4FPxvr54OWhI5XxykjjA9fJTM4wR2b/cGCR55dslbEpemB8VKJ4Cvcw\n1L2nWxJU9vcvBpHKcl/+lPc+xCrqTvRaQsf2/imnbum4z2GQEzogBnta4BzY\nsb/M29TVATZYMEnUPoTSQk1dULeksNI3aYl+jbxlqCnMNlLk4PH/I+lakYd0\nIj475HSU1Y9g/Ko7O4xQBDNqZ6oKVBQPb2QYpq/imEnFbXyGWP2kgoH6NPWY\ngc0m\r\n=QCq1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.1", "@jest/types": "^25.2.1", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.1_1585213289466_0.7559809020982027", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "jest-watcher", "version": "25.2.3", "license": "MIT", "_id": "jest-watcher@25.2.3", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a494fe3ddb62da62b0e697abfea457de8f388f1f", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.3.tgz", "fileCount": 46, "integrity": "sha512-F6ERbdvJk8nbaRon9lLQVl4kp+vToCCHmy+uWW5QQ8/8/g2jkrZKJQnlQINrYQp0ewg31Bztkhs4nxsZMx6wDg==", "signatures": [{"sig": "MEUCIHyzXvxFHrxHnNdyB3yux+Tkn56lMo0AsbzRWqPgeLzVAiEAqCiwiUjssYjGDpLvGAXF0QJ4qcgDRJ036uDn9nQGnoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+kCRA9TVsSAnZWagAAnwEP/R7+20ZxDCB3I1usmrj9\n+itLFH1A7AlNzo9ytgqDegR6gms62lo8+IhLcdKAxCOr+L6rB4i35IF52bYj\n8S+6wEL0Dw+z5cZJf1JI6dnavB7JToCf16s7Z+5FQG40aEwfkBKbqo1o9GXV\n5/rEwJrhBX68R6WO0HDXVpm4cqwTlWotfzJlGvtbO64noOzGE0o/HQoWhBek\nIw9IeoqmkH/JLGbdqhLPCyZUKxPb7xq+Au6A1u6lW5PoUHnCEDqviuw9xAKh\nM0K1UXDKUum7DR7bearyJ+2ewZ0GERlFcfa6L3LauLvVNuqq+c4RKwo7ksju\nwFRGE61PqOtY/3C0z9xBKyFEXEVvnw9Go7bykqzy+C7r8aQe9svxhjaQ6v0/\nVGS+9EGFvspl3+/z9Z1Ap6tEJvPYhBWy8dCPwV/cxJpA0b9KkjeZOHt7LX5D\nmdsTfu7ztKX2/SrXhunR0ZTZvFe7RBf6hVWjDEKGs3Jgwfkt002/UQv/HWyG\nXEVtbEEUeY1K+StgLeSC6w/T5owjA8YdAA+3TzoF6c/J/wgKd5IjKzM35Kfq\ns/G19EP7c+SIwCPIkLrJOio87gOyWxa+5sXlKCaReezaVlKJF6zdmXTbXoji\nn2cMw+UqyJ9R6Eehio9sbYmLqg/RsIxMfw7DyXC1PLgucacoYLQmscUO+RL6\n7gUf\r\n=VlE2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.3", "@jest/types": "^25.2.3", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.3_1585254308073_0.538866059638389", "host": "s3://npm-registry-packages"}}, "25.2.4": {"name": "jest-watcher", "version": "25.2.4", "license": "MIT", "_id": "jest-watcher@25.2.4", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dda85b914d470fa4145164a8f70bda4f208bafb6", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.4.tgz", "fileCount": 46, "integrity": "sha512-p7g7s3zqcy69slVzQYcphyzkB2FBmJwMbv6k6KjI5mqd6KnUnQPfQVKuVj2l+34EeuxnbXqnrjtUFmxhcL87rg==", "signatures": [{"sig": "MEUCIQCggLPe18O2BYogLx4qMvqTeXkqXM9ChrehHKUPtBDo7gIgYOqhKqVbpGZPoU/Roy4suDolKNQI4seSnazPbjRCv0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegPlACRA9TVsSAnZWagAAF/YP/RLCBMj3l/99sPl1GaMS\n1Wkiu9pn8aikb2tKGbrcBHrWjsxUa/jNZRM919Kj5iG49ZjxqIIH3W6sq6FA\nDLNVPpomLARAyWtP5QiV8vG8hSM1GnGGNJn5qqVgIdqGFcKTqKDrGeJoI62g\nHTlYiYr3sI1W+qpR0gR2dSKuF87n5B9I05Z+he4FCh/HN5CHh4iF6L1/SOYm\nHco8BQt8WYUxlQLGlZVsDosKIqmswC7iPtOWDYog9p8tvdJNP5kVAP7yI9ZZ\nAtQr3QchnL0apiN1E6nVs9rtSMhnlyyL6eQUAOJ1LW4inl2tyMJLJwm52FlF\ngGPq2F0dIbSbq2BxhUxogCIGEvfr+LZtnfnhltYDSXqkPwordInA8RoV8Ele\nkc40WRICzM5PYIfOcJk74CnIitQOgK9Z4/EzNasPblfKJRCX+y4n7nQurp6y\n6im4qcKeFIiTKWV1NiJ4LU1RYZJkdXfhLonnLP8kECyhNI2NfDKM9TkNn2Ir\nlG2sbYFdVlr0bhY2WvJaSeQ62nAHxrWi4NzpoOGioDEIF0ZTb84h9nsJMz2t\n1imfUGeNNdsIyqEONWF+Nj4gVsiRBPLIDVD7wDWss+QEJDArxp834XkImMOD\n+VGoNKc20hIxiu3fU24TfBgbg12b2TQ16YSvUVaryhjpxDBkgkGelXOCihb1\nxC/I\r\n=RxMy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "324938561c608e0e9dddc008e5dde1589d7abc68", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.3", "@jest/types": "^25.2.3", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.4"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.4_1585510720459_0.5607415113549041", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "jest-watcher", "version": "25.2.6", "license": "MIT", "_id": "jest-watcher@25.2.6", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "19fc571d27f89a238ef497b9e037d8d41cf4a204", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.6.tgz", "fileCount": 46, "integrity": "sha512-yzv5DBeo03dQnSsSrn1mdOU1LSDd1tZaCTvSE5JYfcv6Z66PdDNhO9MNDdLKA/oQlJNj0S6TiYgLdOY5wL5cMA==", "signatures": [{"sig": "MEUCIQD2aBHRPhV6GgHahMLy8P7hXelIFs28qW7bdfEVDbjPHAIgQVP1iENuWlyqa+4H/fQGCH/NyF6sMzmt4BOL1hOxjW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb6KCRA9TVsSAnZWagAAfccP/3GRoYmX4ND3QzGxIwEt\n+FDizVZE7WGSAqABixqIGKnPI7gzJGI4n1JpGW+vvb705Nd+wP3G4phUi+SP\ncReCTiCvjOq4+uZHWCf7vU3Zyus8sQQZR5xG1JVOUdY5WAm7dC0ho+Ef3T4V\n6UC7mPa7dT1CduxhlHbYBEEuBU5+f2ZUNVzaFNQi96FqDXhAQGTQEP/pznK8\nNZ1NRlZzlPWFcXqhSuHCHt7FV0Hld1SP0+hzJzM/dSmuSHd1UGtvQJOmGcRJ\nFq+QEsjIO35wp5Yd5A0hJfgXlrTJ3qI4Rr3NvC3yRbIzzfOJ/kTMzmKIomc9\nMqPjyVIY9DiRBh7ohpYTCFBWNwazrws/LlZUG5NeTaXTHstVzyCvNnEBc3s3\nzvw4CrdrbQSlbpX23lLA0VSdUlcYUDlFilInv7hWZezyspI//L5Qof5sQvTU\ne+MZnKD1fOpx9nQEXAtRWpHMzTJ0GWXW99KQS4HvM6xUv7ONmXjIKHamTcc8\nbjugcSXLVMXYuYbiCYNynzGUYhhj1qkvaWImXe/jzAzDjuj7GfdS+okSgPN3\nynKNNYt01NmHkTkJd7oHTHJA+ZumDiG6LGugQtjjfjCPOaUeu+BT96dhDS9Y\n12XKqpPxWqUsJF7E5C/YK4dBxiPToq9N/6wF1UaNL9zDYOonkL1ZjBy9SYed\n6Lpk\r\n=TSzf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.6", "@jest/types": "^25.2.6", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.6_1585823369919_0.3684276582729138", "host": "s3://npm-registry-packages"}}, "25.2.7": {"name": "jest-watcher", "version": "25.2.7", "license": "MIT", "_id": "jest-watcher@25.2.7", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "01db4332d34d14c03c9ef22255125a3b07f997bc", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.2.7.tgz", "fileCount": 46, "integrity": "sha512-RdHuW+f49tahWtluTnUdZ2iPliebleROI2L/J5phYrUS6DPC9RB3SuUtqYyYhGZJsbvRSuLMIlY/cICJ+PIecw==", "signatures": [{"sig": "MEYCIQDtyY1wqQirKrwFRIanhGWhFM38PgtAl8yO7jIiZByCyQIhAPgZMKCRk1ZIbUiplRb9S7RVLoro20/gK5uAcUXqSxT8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehursCRA9TVsSAnZWagAAvEYP/0XZgLMsu3+yl3gTyi/G\nHNkllTNfSQ5MJcBwOJHp/wdHoY/UyOKzmTlfKDTbmJslg36UTET3ZuD6M755\nXidk/vx4VJYBk8XSzw3rR0Y/00Ig9rJOt8Ka2RI2u2Z2ecLQtuPpsOUvu4fd\nTr2IoBGz64F9QxedQfDN5xHFlCkKDiWM+F3ASk29pEYmNwHIzmXA2rp3lvef\nfBwH5ip7xHyeLzi5bsNT00txCyKb/Yl7+HQqzgvc7Jua61tire3O3GvYT/Mq\n5OL/60SpEj043bOU04/PWpn7AHPlquNKN+6wnAUaMn3xS4yZRqFwWcYm08gE\nhrFra4XBuyQ7RxbKYhIwNR1ukOGdmI0Kh05EjKrycr5XFC7xzjkuqZw/BsZ1\nQm8hKhbNJNB9b2/8vXHjYe1MRskqZWRXScFjO8yxcKAtYPm3D4waren98Idw\n1WZMcMuMrya8IkbPweDokV5PBjdBVNWML5tFKAvJFYbCut1rgNnuyg7jPVj2\nfgbQoqW41m9QuJTwvdxEm3cHAIm+useAZNYGors1JGnSRDf4wdKT8NYhES4Y\nds+g5/SKEEqCgRnS3WbWAwDWM/rJhQELUWhKHW4MRdYJOFS22XR/Oc7tCT7C\nlES53N4YhOtvEbd1XI53Jf7j3GgvS6doxPdvDxIBREwE3TWXXo1P5nbvUeRd\nXV9T\r\n=ZqVT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "3c2fa9347b86460b5dfc558f033b8d4eec0ff8e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.2.6", "@jest/types": "^25.2.6", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.2.7_1585900268392_0.021961783549656166", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "jest-watcher", "version": "25.3.0", "license": "MIT", "_id": "jest-watcher@25.3.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd03fd5ca52f02bd3161ab177466bf1bfdd34e5c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.3.0.tgz", "fileCount": 46, "integrity": "sha512-dtFkfidFCS9Ucv8azOg2hkiY3sgJEHeTLtGFHS+jfBEE7eRtrO6+2r1BokyDkaG2FOD7485r/SgpC1MFAENfeA==", "signatures": [{"sig": "MEUCIHN1EMTNh0xVRdzz0cP3ffmDgV7aGqKBwnZic8h8P2T5AiEAgEqaUgOmHDz4QcCB84fNlSAnGSE4Jktk+EmNaXJ2nn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/ZCRA9TVsSAnZWagAANpMP/0I6YYm2y57r9yAn0o3u\nfWeUFJhbGS9EKvjZmXaNl6VKdexgn4wFS1mdDvdPJpbQmbCncIa/2D+HmWBH\nhq8jpKGlAmR/bmBoHe4quZnRzGXs0RbcVB4/TByAyvckCuyblL+zLw+LWPhv\nMWStvp/rjwLawgi6Xw6LM+JnN2DemtXCWADjE/u0mRWgIlMYFc5U+w26AJHM\nwkXgfyhxxGDycQfvlarRHWiKa2YZXfA2xrHNXj8RNa3amO2B1QeQO1Xozh88\nRUkL1nIFQoQs61Evw8giRKMI2mJN0HB9XgDLFiyRXr8SFkqvEnUn4gwgxqNW\nIMqZ0O05g+lnZ/UIVg67aB0hAmuVOG3MMVLSYE/z2uXmq1JN21yBPu7fpBHT\nTDfC2ODZUjvGb9Llu8hzuW4DaVIZh80YTZP0ipteayB7noJpI8PZTZHIV6Po\n5GO1IHHTAZpRFMr9vM+6RD+MezC9m4FFASHlEZlm9cGRx0owfny5iDM1PBgR\nTV2D0y4wMmGRmcXnAwgutBq4kOaieHvY6RjSWDissIL7vZNcwOaBEclPjnCS\nTm0tlaT24MMWFiKOUAlsNUepOPZDEkX8DQiMaPghvgsPLlfIUCMHAzBczvog\nxWYVl3rTiAlY/10SwqIzcBEToDX3JtlxlTpl+d18AJa1rdTlwVVzHqUv5jHz\nJrPo\r\n=ofPx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.3.0", "@jest/types": "^25.3.0", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.3.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.3.0_1586352089057_0.9390505654767127", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "jest-watcher", "version": "25.4.0", "license": "MIT", "_id": "jest-watcher@25.4.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63ec0cd5c83bb9c9d1ac95be7558dd61c995ff05", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.4.0.tgz", "fileCount": 35, "integrity": "sha512-36IUfOSRELsKLB7k25j/wutx0aVuHFN6wO94gPNjQtQqFPa2rkOymmx9rM5EzbF3XBZZ2oqD9xbRVoYa2w86gw==", "signatures": [{"sig": "MEUCIQDuE4XpkY5wokLm/IBxRKKumQv6BdbZkHevo2iEEABI7wIgVrfURJY6g6vFjEZPY5h+QXYoqjxIYR5I6thWoPWpy0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMeuCRA9TVsSAnZWagAAFgIP/iWndPk1EZmJaQR7gSOk\n8C9OW2XflKNg0M9iBSn35mlyupPw7bug+WWLX+ZYmHEQPpzny8gAtiee2mGZ\nRejoNgOK6FsmFva99IWlwm4/ZTy1BX3JK7nb+MqHjOMi+23XpHZQJS+NBgdt\n+8V1jHElsrhYwrBtiwukS7T+v6aANp1yFbd1y/45sCpuzxnUmsjAsVBaGu2m\nN1VhnXPi19aL7h1+jRry/pRSTP5Xyl85p7WahgknkYUY02NBJKKnELuUpC/E\nRM9l2kQQJbBJBqeUhB0foxDFwxewKIHGaVYC463GhtagqpA4/ipRPNSaDqJ6\n+k/j3w5+VgNdjhvxMjWWzLH3F6J18pSWlf6tRdrYNzHmPbskMEB3ALGlA+xg\n8ITEHt0EPr7P34hwEP55fl4YNg5C6eMadFxoZIyAEkhIn9vlu/3hvItwgdon\nZT+IBM6rFhqdhWKl44LuqGO3zOllzV91rKzeVlL8O/R6oQy5+mCi4V0dlc3n\nlI96px4wXp8Y8/lQDWYpzeZ+pypTdEb7xkQlatVg6Gzk5y9yM3PdxIMCz8ha\nZtAiE/0PlKKt53ai2exwdrCrAob0/2zoHW7DidcTGhGS75bbOu4TzsAQ7kgw\nVmYO8KQTvqGlo1HLJYkujfudMA4nHYm00UZE2G4+EEXSJqw4yNzbrge4FQ4G\nrD4P\r\n=3dyV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.4.0", "@jest/types": "^25.4.0", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.4.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.4.0_1587333038222_0.8095253865917638", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "jest-watcher", "version": "25.5.0", "license": "MIT", "_id": "jest-watcher@25.5.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d6110d101df98badebe435003956fd4a465e8456", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-25.5.0.tgz", "fileCount": 35, "integrity": "sha512-XrSfJnVASEl+5+bb51V0Q7WQx65dTSk7NL4yDdVjPnRNpM0hG+ncFmDYJo9O8jaSRcAitVbuVawyXCRoxGrT5Q==", "signatures": [{"sig": "MEUCIH+uuW4YcOqYeEFHzKdkx6uvdOwHZ09Bs+AIpKEK48FGAiEAgf3G0Rz2fPjQ2OmSUM1cwqoavhT4UAcCbgWhi71X5GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfgCRA9TVsSAnZWagAAX/kP/j+PPzo8L2QAerg3xvUP\ngrfEatvzbit2N6vuC2UU5XrXtm3bOshglVmA2uKCYskWtyipMlLgsdVZN6Cr\n90snAV/UP6VDwRR7YK379dLLaYbugq2BWaBU3qTS51/dG1UJH9kPyZ7iLHyI\nek6fMrUeFghVUgrlC9eqer3ni6YCGQHjrSBTSQvkM/n10Ud2MWVgI/U5OVPq\n8+tSwDqQApXhk3wQqufYZF77bnjZEwscbjFAedREHcaM/fns7yBm/0IQOoOa\nAsoUSUlzsyX2CgaB1nI/cSiOUelBRQX2BzD03dOyVD99AmCmZUh7ZX4zSS9G\nJ91a818lu2lUpcd5eJnuCcob9wcI7R7qYchiyC8jI5th3Xc83joKFbMLnT6r\nWO565ewZoX9O42q05OucUluVaV9GSaNQsUzoa8z3YA6TP4fxcuX5p488ckRL\nE9YoMJgnbJlzenDjFtwwLnMc7AHnLWvDyY/6U6VBqsU+9g13kUVAqAwMSnyE\n+U/ERzJTsaTzCPgV/nEjYwFm6CPksDyzRSxGZc6vty4WD5xypcuzObA8Grvf\ny8hEaK2uIz8VkKMP6G3xXbxhGmNp28YQ5cDTwJYUWRGT5+YuCAy+DkHy78qI\nv6h9IR4MhIjkZz+Of6lq4dNhz2QV6BSaLvlb9ipUgR3dKvqhQf6IvUYWo70M\nQU1V\r\n=c+qJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "jest-util": "^25.5.0", "@jest/types": "^25.5.0", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_25.5.0_1588103136299_0.1118073754283011", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "jest-watcher", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "jest-watcher@26.0.0-alpha.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5be51529b979310fb937ca17b7c3a19547dcc3ec", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.0.0-alpha.0.tgz", "fileCount": 24, "integrity": "sha512-LL5ESnX5V4cHT2sax5/sjkZHLbi6w2/aD45K9QgjAj45X0JKw1l/Yiy7TjFsNs9c15p2jsa4O6zzVzQjuGPHSQ==", "signatures": [{"sig": "MEUCIACka1M3b++MpKINO8gugo2U2RKsu7HI7P1K4G0/OvfyAiEAhggXvykqcxwtaOOX6s9mg5LRzaS4fUFPhptJcaom6vA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPeCRA9TVsSAnZWagAAFasP/2B2Zi/RpIhmyHSxVybi\nv1O/WoW3cs3UzrATeyP/2rBNVUKH/0+jg2SIQYjjjh/8kHDYdMqneBFycuCp\nwyRQV1wmkO1U+ndn4/cu0KEdGDqeukj9KyYylahAvJoZf8V9ei/AOrT5U3+8\nIXk6TmJPcQHOWq8c2Eb1b4RoBTWdOifK86YMLdatHyd10XhybugY1+OCWC/w\nfOUT5Z6lxwyVYlFfaglEe2QjEJMycuzF0ftnHPtMecN1e5cQCa6TP4S3sv0j\njQ38MXc6P2h3h008+UKct4fkgKlcKx7bnjzyKGkHIPUX77JOj4XIHrXVWDSm\nAtO/7Szw3yF9uDflJNhjVO8FqPvzBSczSqma2nfhNP7IIlBwOdNblqql+VvT\n68ICfqu6yQ85UUuPlgQd5DkFEmCyvEMBFWUJ+BQF8oG5ihoNprQ3o9prGsSC\nkFUhJLZZYC189DRCiHkasWrxokeMIIXYFPTuwferQE30+qdG3bmWuLJnFA+A\ngGjSqz20jNaZ9n/8ZgfSojK6CZ8JK46HDmd8ydV7fGMTxkAUGfj8N57eUtxI\ndttRtABLTHCWYpoaBItHsurswfCclaUhiXRLnoYfvIWhUs30TmXD+ovm6qvZ\nx9ui20P70M+yRLkC1g7y4LvaKNVfXpuILRhsGpI4O6l/5jLEYB/7MsVAaGjh\n//h8\r\n=275o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.0.0-alpha.0", "@jest/types": "^26.0.0-alpha.0", "ansi-escapes": "^4.2.1", "string-length": "^3.1.0", "@jest/test-result": "^26.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.0.0-alpha.0_1588421597944_0.28277858428724256", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "jest-watcher", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "jest-watcher@26.0.0-alpha.1", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4940720240d0d3ef4d4eeb97b62d53756f669119", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.0.0-alpha.1.tgz", "fileCount": 24, "integrity": "sha512-ywOkBtBmmlWqFaIReHZY+kmTs6YdiHY4bwn/tLXxs2CILb7vmGN6xEkpsp67Zrf1+2qzF4qYJUlJwhVvghQQ1A==", "signatures": [{"sig": "MEYCIQDWTd4gfULJbme9Vpvaoqt7SjpH0L4jGvyyoIcHHbn33AIhAKZuOm44zJUsVEGxR8qpgJWTK4aRqmiV7+ghpSngchYk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHyCRA9TVsSAnZWagAALpkQAJd8PpcpPfG4Ad/dMUw0\nCF5NKVJAV8RqTTHJXoHMfqHE8GxOIlnOdvdSZKESeajggdHBt5FPy/bzf+2p\n8YpJvFhG2BhDMRoe2vObi/XZC//uw9l5TcDh2a3rMb8TaiewfksR6FNVMkjb\nfUQYSUvrxKwX0MEdtKrgET/YEuhtzvUENhtktzwCvvdeUNMUUkEvPGJld7vp\ntMhKf7BPi+4dvLNpqTq7K9etgE0ITLh89GplPomVrORL6Y9N+nYdRCl3/yE9\nsAD+OErg3uQfkdy+oJh+gDNqrV6f4FG1Y+czfvJE+Mv6R9d0SXuwiZeOCKTt\nu5B6zCRIy+Tq2seVGpR3iDFMHObjZrRo908PMyPAcT0hi/bvM3FCbLfZ8UH7\nyGiEH1Pm0Ufiy9bO/wrc1RX9CV4oDobi9THIh1M/2X9YalghHDDSbczA1eO9\nKr0DsPUHIpPKLe65SrcxBWuIeQY+qazc9bOiF4R7SBC99gan/or+o2M8RwO1\n73zvh+i+sAVXBhHO513qYHmg8lmbrRTXlYWrB1CPXfqpY27mbzW+rUuoxxkC\npaAY58am/fmWuGoMvQEI9UksLLR8PUkEcpVsHvEGELyl+vzuDLAdFDgwIj9G\nG7jHqN8/wXube69YHCsGcbHYpE7vf7n1U2raliTkTCL2z6RUbMx1qPIPN6p4\nF/Ea\r\n=YMHj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.0.0-alpha.1", "@jest/types": "^26.0.0-alpha.1", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.0.0-alpha.1_1588531698588_0.6083577784942218", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "jest-watcher", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "jest-watcher@26.0.0-alpha.2", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "196fa56a3fb30805156b78a304206d629d5cf56e", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.0.0-alpha.2.tgz", "fileCount": 24, "integrity": "sha512-gAAFXypy+FOZPeVOyW8527LYFCpmFrotmyocXjpzIsv3osT0wJ6sr6C/mMc7FtvITQYrAANX7jKnHw4Al3bOvg==", "signatures": [{"sig": "MEQCIDRWHmBh0WkESrjiClKr3RNtlM8s/Edqa8W4uCeBlOjXAiAeFXknrtLoMfzCg3Yi3LbNNjm6XS2C9W2Qnka+9pcJeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1gCRA9TVsSAnZWagAA4+kP/34WAGFHNFdj+Xv5SrlM\nlQhsqU/RpayY1ON3UVf78tQvhVrIDzpF1XKgb0jinwTIjHH/cRyigvCVi2So\n9j3vEbLxCof2QmJ8LZknZLyxr8osElMFI82C0cl746syQnOqvk6/umoDRHXk\nqfuiP6TbCxFmWkkNMHezFZ3p6GSVvChS8pkzBFRwCg5+/2X1ujPdZnZ7fIBp\nAdF8gt2UGnyQcjdS3PIAcFogxalWv312hmIYZaLUp8GS1OQn6W7+bevn6PSM\nYRGoHfrZdwjKkKN6+i2XQhgZDCmqFOb+r8wlJME1f0DqEKOsjiESWdpnIXF3\nkP1ZA+uMtn5Id0atASkl9HQZ66pRlcnCm4vejT29qRZN4mr3gp9pfBHBxH17\nXvXPjegCsWrtEVrkY2fEXYpgK1TuZfN7b9cCR+laeYTm7L4UNCJcsZeGgG9D\ngmXIEXrVquEe3EGE839+lJlIIAeTkpNKzAo+eyZbhBDeDfy8Y481O7Rgm3Yw\n8gev68XM6aOJd2xbOtkkKxcfs50EdicTdMoEaxk//JBUO2x8XjuurWxKrdPX\n2ufdJPt0wknHx2yBrlWUKfig3fsT1/8WnEiEreBXAUK+5x650RsuywIJOD4Z\nyJM7IIwTBmwYuAjKn8JU1a0i7DHlL6LaBqx0oKzkVTdCxy7Ml4ina+S79W5o\nfMg3\r\n=CG3N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.0.0-alpha.2", "@jest/types": "^26.0.0-alpha.2", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.0.0-alpha.2_1588608351941_0.8807987039999114", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "jest-watcher", "version": "26.0.0", "license": "MIT", "_id": "jest-watcher@26.0.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4fa3e0ae5f9ffb37a59b962a5415701cdfa9c238", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.0.0.tgz", "fileCount": 24, "integrity": "sha512-FLAhP6suKZJHoorNSdT4Nzeur+Fo2HS0jB0HLnQFTmVjtvRCqmXPC2o48oOtRY0xiQ6zaMF8kSfiaRWy/TRTwA==", "signatures": [{"sig": "MEUCIQDz6SfNHiGVUhvfISzeVyt22bmCfeqWarqwpFpWhr97SwIgBDAEfu4YrhEJ4QZPW1VcBVUwHQ9uIIv4DBZ+Dp6qq1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaZCRA9TVsSAnZWagAA+64P/2iVZ3j7KBXAlbxShY18\nsAMO9JvgX73Nkg5+Oi6Re8iY8AnzgOlKf7cJ9Iz2kJUp6iZLrXKliZviwPGv\nCj742AkoDwGiapnpiW5m3FUdXDiX5bEkEQH2WzjQKgYDXGHCFC1/C+r9Ya2L\nhZaXrX2A5fj2a6rDlrPoB5O/PG0RWxGDG1DDunchNMe6kdznHtyqnwOwJmzW\nHD8YCO4iOL5Cy1ycMLPWg+yVFJrzFu+a7s0spXI9Ff2I2ODLngMNVfGtXnFl\nbv4gz/CKVVUYA7u8kWgY5zrBtweycPw0Zyyu33xw7vSxBEQ0GuCMewpSlEq9\nIpDplSCN/1WEgQoSQSlxy9AKwIVS1hg3TmruVazdufFlZXkEiE9cPA0DcgR0\nMOzyCq9CUDwuuXUZP13kjMXI10MLOVf2siM16oxD1WFD0LGJPO4m+JhDZeQ6\nh5E3Mahf6akp2hF1tQu+rihJWSMMgTSwcWeMK+L1754XIJtZipctAUsKOfUB\n25YxB+wQBKC1z+5F4Bc601gXY3fiYl0frGG/FAaM8d3V8OAKIO3rbZzhVF+u\nKdaxHuakHCtsbIRJsBiBSlLf92do8R8ctOSUkW1l0uDEM+GVmhzbc3zYrQDH\nf4z4N2eo/a8U3lzOcXKFe5mm/Ri4nbm/MoHYLe1d7pVgPt9nap1NrPZvT0IZ\nsz5l\r\n=Vaz0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.0.0", "@jest/types": "^26.0.0", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.0.0_1588614809034_0.6195270040024357", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "jest-watcher", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "jest-watcher@26.0.1-alpha.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "01acf79a5b903a7f180c5dabf5f0b9b8bed6e20c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.0.1-alpha.0.tgz", "fileCount": 24, "integrity": "sha512-7NJIbDcsT1wMmERGxFWTAv33uHgz/MfD9TjUAIb57d/RikSozE2mMIuDVCF/nGzAxdjhy+cZdcWDOYWe09hl8A==", "signatures": [{"sig": "MEUCIQDyUuUMLf+qIAMLRDvfNkR/8NXLhBGZzj6Xj7k7UWLQpgIgWayzbCFITnz6cTIeXt6dDZ6z/waKKfKj33ze8nIORHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQxCRA9TVsSAnZWagAADAwQAICT8ib7OH/kh5cUIvPY\nfa8+TW6D6C/cq6GM3rbQDIMpMV9mmKUJLz2nhuvtrG9dPT+PVSxh38NpxzP5\nGm937vsXuiZ9BxYOjeVYiRdC7CgkkyCdjM8RHj0iIFik6wnTOUx79/90FlCL\nWF1W7XBxr69b4zWg7Y4LyLA8yHt+QNzJaAYeRxu66vDgSQwnGHuUB0thm5IA\nExtpr4SVi+QIbafW/rIz8EGfm94t93gd9xKz4X4kjetnuTSwqae5qYFm3Lk/\niCOx3MZOJd2o40v2npmZweuU197gwHj4AHryuPKdu4Ubai09HPuXmeuRCyYD\n3Y+a703I+kPhXYKp0gniYE4fW1+an4glQIfc1gUwlOfQjuyT1tQmwidTCU5g\nD/OVCooOm4lkuvyMVS6z/qhmLL9e6PN3N8TEpx7ApDYa5igYs1rjTb9urCp7\nnl8c1NHfOC8si3fGY+Srw617GTACruNNl3qNidLi4hPNOxTlchCul+E9MOGW\n7CRNqTcgyfJAY4ywdNRM2Oo35V1uX767RjnNHCc7D8rF2S4nvCP5UC9OzTGS\nZ5DB0cd4KIKVKWV3lD6hef2dCaGIk6gxZwBAcys73bR8lMGm/RpTOTt81Kv8\nipZNxSgQi/mfZnaeDoIgm0n79pG/5P/o0YeDR8fngsntevIEx0GocZoOcyQp\ngR2i\r\n=5pyO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.0.1-alpha.0", "@jest/types": "^26.0.1-alpha.0", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.0.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.0.1-alpha.0_1588630576541_0.737187483726069", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "jest-watcher", "version": "26.0.1", "license": "MIT", "_id": "jest-watcher@26.0.1", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b5e3ebbdf10c240e22a98af66d645631afda770", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.0.1.tgz", "fileCount": 24, "integrity": "sha512-pdZPydsS8475f89kGswaNsN3rhP6lnC3/QDCppP7bg1L9JQz7oU9Mb/5xPETk1RHDCWeqmVC47M4K5RR7ejxFw==", "signatures": [{"sig": "MEQCIECsPJgX1SNAR4ChFhmrAxGHJsbO6PJCt1KrY86Jxz9JAiBedwXebw+LkHhzoxYHzXjJc54woqGQVv5r99S97gRXug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesULJCRA9TVsSAnZWagAAdPoP/j8LSZiD+kORnSbgjEoZ\nYKECxjoYRhVb0N/NZL/uyBHezCgrP4XFSF6/uffbZPmJgg5VUusPAwxb/PH0\nlxo33h/U1TRrxrXvYK7uuG77PLgdTuxCJIjRwLMpsSkVqCHQBDG28jYqjXxh\nJCK47R6Obi9YXPtIULsu0w0UvVEgwmHq+oRUyJ2Q63i98FjEgVj+bSZJx19s\n3XGFabq+5eLnpQE/ENsOPmHGh3uPMs4iQIWqJA0O6w62sBiE7LroKK5CQAtp\ncYr7YjuIE19bxrbq21VCOkZz/3TiDT2eUtdPHljxP+cIIlkauT8XpFvVcqmR\nXkHQmzkN/mEiIIDFJP4xFOQQLcCZpkNMTdhwwfC85KP7ocak3bCwG0HIRHhz\nrt7lMFArKRkH8EDEOGuk8Ugvjl51vx1jZtPFX5DnFUPOzpLmvf5b38SbL7vD\nqySPCtKLAUv3GiLSm55lXNsEW9algnimXa9NGupTs05qreCXnhNBvXZD5Fzu\n9cavEZsJ244QTX+8gPE6Ju8r7WssQcVPnWIGkJnpDhsY/zC2H5CzPA8EI1ZF\n6o2FKNkr7zEGHQrf31pl+1OMPbf8a1LROhCZxdAgcoy+Rkph1mc6XSs9x6bU\ntN2corAVnoiQ+30/GzuaP5g4sJ3JCuxZD3c9DudGnQhDeuz3Puu+y29OWJU0\nmjp2\r\n=TBKL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.0.1", "@jest/types": "^26.0.1", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.0.1_1588675273437_0.5460987025190407", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "jest-watcher", "version": "26.1.0", "license": "MIT", "_id": "jest-watcher@26.1.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "99812a0cd931f0cb3d153180426135ab83e4d8f2", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.1.0.tgz", "fileCount": 24, "integrity": "sha512-ffEOhJl2EvAIki613oPsSG11usqnGUzIiK7MMX6hE4422aXOcVEG3ySCTDFLn1+LZNXGPE8tuJxhp8OBJ1pgzQ==", "signatures": [{"sig": "MEUCIA7pUpaqr6a75k1xpdimin93mBhSu6GGetWgRn4iXyCgAiEA6ExKApz2lj892NfXpaA0IqhSHxbufVcGvLOPKRaSoBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hyUCRA9TVsSAnZWagAAjpcP/iZi5Rb8ixoHEzDUjxpL\nZEK2Rf7/3W2KUtP3jcW2AYBIMsWCEnZT0Pgty7Ftt5r+9lblZa2lDJw+4BD0\nOSgNc1p7zk1hc1xcgfgazSnIuWVLqfKQNFeRQz7jvPPEeOydNWbGOXNHTlr3\n0yrcMbVLs3LR6Z0mmuOWxApf9IoQmgMokOQ6E2SDX0v3Y6MQK403CEs7ondq\ncP5zNT8vRkdpU4m2KWjEOVnHi4f9pu6PQ5FibaDT4QWNkU+tgmUtxq52itaJ\ncQndw6HzTdlKIQc98ARlnJzmev9OkDtXym7vtKFb0WfgOfryYKTJSI2Hwt0T\nwCbWxIo+mrfyMH/c0v2woxIIzOIhpCEP2ihC2zq0WImrF3PwEdzLiJmpf9G7\n3vvnurWU7xid3bnZaggfm240xjBBnKWEHj17aK5Nsq6tx7k8F3fBmd+Bjpwn\nslDj8MF10ktLYA7261mfSA5jhb7IKoCJA83/0RsOs1XkXKLqbbjrdsvnapea\n4wlzRbNrVjET8A6d7G1ThF7F+0c/b8YD9Bb3EL3wWYbWW4K85zlyjyjfjL0j\nxyNHjGhRu5ozdPyu5gw2RI6uJR5qbeg7CBb+Oh2wqb2HMzOTevT2Djs4zsF3\nAADxun5x/vkuLBqWgbsfbl9LVfUhkPd7+AfYw1bO485FzLUi2UOGFVCyTTUD\ndDyR\r\n=IA1x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.1.0", "@jest/types": "^26.1.0", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.1.0_1592925332187_0.5909462442577278", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "jest-watcher", "version": "26.2.0", "license": "MIT", "_id": "jest-watcher@26.2.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "45bdf2fecadd19c0a501f3b071a474dca636825b", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.2.0.tgz", "fileCount": 24, "integrity": "sha512-674Boco4Joe0CzgKPL6K4Z9LgyLx+ZvW2GilbpYb8rFEUkmDGgsZdv1Hv5rxsRpb1HLgKUOL/JfbttRCuFdZXQ==", "signatures": [{"sig": "MEQCIH/j08ii1xvSUxhR/8IVnxOf/dbjmfwy7nVp1FukEebAAiBL428O+mpG4E3LTbv/Y+jfuYh9W6s5uewBWWglhTHK+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzvCRA9TVsSAnZWagAA8S4P/RZCyrs9nyyeWouBNF7m\nLQ/J79f2yDvXC2GHXgeGUdoXktDzXPPF3rHaBV3lAxl8GKacknxOpgxvqgaR\nz7/ZyRidHezlT/OBP/XooVApt1fqtszVPuvxogDdOGRewW9XKF9+MlEwtH+T\nYIlZP+DSB50t1tMF/wDmndLsL+ZL8/17k1ZIwX6q34q4OVA2RZY7PKmLOUdu\nWXBvAvc6LwRvQBCj3WyZUp4A9FfyTyGhoGxC7gQZIrR3YHIbABnSrc5xrYQW\n34r8TsSIFqoY7Gbt7sVQeAt5NpnyDa1avW9sSV31k5wA/OIL1EMlt5PWdB9G\n2QyEIYsfnJc18exl+4o0lHwIAdpg4SjcPoyWYOmO/2DmW3v20CpwWnL5XCXF\nBhz3r5rRBnaLTYasXn7EwNdpDNa4Lt/K6J+TcAf4Zapea4A/Eqjv+y+RQ/Z4\nDMkOgJ07PoRClvhVUWghZkwvMqrdR06ymK8EEf2lSIs5mYNMisNtM3X0BkuU\nKhqD4V1CSVWNaa9DWPlKKgyAIAJSObTnhzWx+plkSRxAJ18upPLGLT068amU\nNlcEMVZvSHJWjjl2VyQdRuw4apLmPSC53jQnZ2pkz2jaWWS+sTWevpWvy6nj\nT4aOpi4xEAxMeaAPVidu/IW5Xtc3X4BgW2rwA300TKU0pIHbVW7uIed6OiT3\nd31/\r\n=Q6xY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.2.0", "@jest/types": "^26.2.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.2.0_1596103918950_0.4897033105444937", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "jest-watcher", "version": "26.3.0", "license": "MIT", "_id": "jest-watcher@26.3.0", "maintainers": [{"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f8ef3068ddb8af160ef868400318dc4a898eed08", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.3.0.tgz", "fileCount": 24, "integrity": "sha512-XnLdKmyCGJ3VoF6G/p5ohbJ04q/vv5aH9ENI+i6BL0uu9WWB6Z7Z2lhQQk0d2AVZcRGp1yW+/TsoToMhBFPRdQ==", "signatures": [{"sig": "MEQCIC+eCyExnexmwXojR8jP4EEN3s83836g75xf9Xx6r2ZcAiBW3twizaSznpBmKN/+wJBvrO1lkqTwmE1Vc4W2TkJkVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTA2CRA9TVsSAnZWagAARN8P/3zleqGk4OBxuC/wvjwj\n5RQEkQTXMbmdzpDCN7vZF80SNL6X23rmv8e1czmkzB3rSX1Rxy/ix7uhJCES\n9PIWlHow241mpnUu/4gGnl46y6HHXaWNcKN8w1NP99BrF6B/pABKfFgk/UOu\npQQhsVdEEzHPfKRPXkp99gqiuOGhVAubEjrkBRTRywKxOMondTk+FBpTN67Y\nNGevULlDMma5eYiU80EXdZnm8mSdxnjtBjWJ2SM0nkte4aMrXsxNkTzWBZDy\nsAXNTakN79GeKyrfDa4SwOZBa0/a++4Xkgmj8YkAzxxmGIaHSAp3LzYsGyHl\nYBly/TAs2OYtV3BsfDuXD2t2zwqhNsuHXWD07yeaA9fizkwn32gSNFkjzXay\nRbtCYP9b+FDpVM9AnBosqs90AFx1KnKQCq8+OKk/K7zD3e/XPVinuqCPBNl2\nrjBdbLxs1jpd9CkEeHe6R9rBoQ45OhTh+Ewp9jlp4Cbxf0NgyDTQ0spD2Xg0\nxHfrZaTmGTvUk9GQAO9t0wOaO99oTTGwv9LfLZGAwGrbHa/klNkC0sA9OTBF\nJGvzxDow5mUukgXe6bCN3o19f9xDlcfUzAsKLa/DzQdUKrNRgfmCYV8ULuLx\nn4tPLFhfiK2oa0Jrfplm6lG3H4fiorj8/hTFdVpEw+HRQiQuGi/FCH0VjsA2\nJbAc\r\n=KFLQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.3.0", "@jest/types": "^26.3.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.3.0_1597059126129_0.6072285861424105", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "jest-watcher", "version": "26.5.0", "license": "MIT", "_id": "jest-watcher@26.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3aedd339ee3dfb5801e71ae9a00da08369679317", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.5.0.tgz", "fileCount": 24, "integrity": "sha512-INLKhpc9QbO5zy2HkS1CJUncByrCLFDZQOY30d9ojiuGO02ofL1BygDRDRtFvT/oWSZ8Y0fbkrr1oXU2ay/MqA==", "signatures": [{"sig": "MEYCIQDllWNJ3fzYQEtrIIejopQNbTmlIM7ohbV6/HwzvN0EAQIhAM1yXA+Fzsy/r8w2iDrm2LyQoDRTL6/EFcu+z88VVgpq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeudDCRA9TVsSAnZWagAAGrgP/AgzOt7Z0g72zKXrx5vw\nPvIpnBRnuUzwS0iHKb0n4C3L3VorGds2K08DklMejhrhqDnAESQ4Ilf/uOjR\n8WyQMNoYHxP4WLpkUPEp9FH5oXC7LhszbRrJXzsoVw8zqluS9Urj/6n+Wgxy\nUYj46ixRXkE/4H68NNCwi/oC4rY4mFhpYPF3AK4ze7tTz8p75nmdjSxtDsPW\n1ZuyehwWXUYE0J9fctemzyVYhidCTE+0on4HIyLDVOl53qvML8rpBQOojD3c\nfGzCrDBN4Nl2N7nspg3tmcaU5Hk5jmLNO+j+8kL3bv2C/rS4GJoaVpj0LIm5\n5glO9ts15AH1SKw1fYPaTrrjQLTRnSSBhFEMIOv+okcQA8jUgSj7u0VsZKgT\n4tx1x2SIBhL3mScJxYvuJ9MRuC1cuUcqcLntVfyHL3xt0WSqlYRaXPdp4Ki/\nSqtN4Kk87HfI0jjTWAhM75F0PR9dYxpzH7JKD/Dkk6u21YPplyWyKHeY4Odg\nUpAktogRztDOrSiFyVbvcD6ANOxmfN76dmm6EIH3WSZLY4t2ybMkly9AFJza\n3iw7DTjpzWzh5TqLmQ4y3sy2LOExh4pzBH1PEw+BacScumrQ7xG/JDCKoS0S\nMPqeQ5K/78X1irbr4EDTc3VbwCOb7U9USRLjh0a3IDGdSMha1vAUSQIRFQVt\nXIKx\r\n=sz+y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.5.0", "@jest/types": "^26.5.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.5.0_1601890115527_0.9014723925391297", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "jest-watcher", "version": "26.5.2", "license": "MIT", "_id": "jest-watcher@26.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2957f4461007e0769d74b537379ecf6b7c696916", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.5.2.tgz", "fileCount": 24, "integrity": "sha512-i3m1NtWzF+FXfJ3ljLBB/WQEp4uaNhX7QcQUWMokcifFTUQBDFyUMEwk0JkJ1kopHbx7Een3KX0Q7+9koGM/Pw==", "signatures": [{"sig": "MEUCIC9FbchAREBbL6Or8m7sIGi+nugsLAWkuTA8xSQlwMekAiEAzZkXHy585ulwN/uDtNYH/VZzr8Pcm1wFglnUSF8A/Xs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEyMCRA9TVsSAnZWagAAS8gQAKAzIHpcqVs+6ot5oW5g\nU7O9lSZJQvq1Zb3jJ0pqBeKI/HGEE/EPLZEA98dXx2p+9cZ6clUZPjPCBiS0\n+MnKA4bQg3+CnOOK4oNevQmnwQUiP8aNiWiU8CiKze3n37YBa0WjkXglxKVe\nXplacBT93Z0cwN/dLpr0Rs/9S3sQuhfEW1L94SJXgyf4m3rr232lBVtfLX0u\nJvC1hBw4Iw26ehssmcEttC6nanFk/bJZ6UZ4S36WXzM23xs0Jkx4yZAaFitu\nMTj0rc8Z5BIGGVdovIq+WApVVsnkr5UicY0htUR1/pgU/25mDqlaJeU7sQl3\nZVBTIw/NYPoPD0Ujzi296rxT+fvIx6acGXIankGjzDQeZzqYkFfQUA1cBGFu\nrZKKaBDIDFpUpoKBf/k0G1bBJR+uIIrETRVBVoPYy/62TIBDkJgYm+dLMEDv\nPzihRasZSMzYPR4Xy4XWqf6/n3HoiofI/4aQ+Xnaa2FHDQbcfvH/Y9hqFN+Y\nXUENuaikHLynyyn7npHRGuErw5hbvj2+wubHewLKUmd8JZP/Pyn0gjmbJp//\nv/HZ36MIu5l2rqgZcvR1R9Lpqlr/fOv800PTYF5ktDkTWP80SBS2Xhwh7wUt\ndF6vdw+yyYrCepN8knNgISSOtHyEyAPmB3mdD8fOCd+6TjPv4bj3GcXFWEZv\nnYY7\r\n=1zoV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.5.2", "@jest/types": "^26.5.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.5.2_1601981579935_0.8043438559690652", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "jest-watcher", "version": "26.6.0", "license": "MIT", "_id": "jest-watcher@26.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "06001c22831583a16f9ccb388ee33316a7f4200f", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.6.0.tgz", "fileCount": 24, "integrity": "sha512-gw5BvcgPi0PKpMlNWQjUet5C5A4JOYrT7gexdP6+DR/f7mRm7wE0o1GqwPwcTsTwo0/FNf9c/kIDXTRaSAYwlw==", "signatures": [{"sig": "MEYCIQCrWgi3hMvRdjVZbwDXgXbkyzL22+8lyZXZ5Q/FYdXNewIhAMV9+H9C34tsyEQtsB/MhwbRazKbXrdfdAzWzynxu3JR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX+ACRA9TVsSAnZWagAA3YcP/2sbkq0xWGOQqLUVRn3s\nizB6NTB2H0+N62VeOACoYOlsjEWPFyPcfhVYUtoR3Z2fKOD2bbgtj7k6GB7C\nvlSzv6yhUVMmYejGFsypXUxGlzJRVNId8+rEGFwPq8Y9O2MImk0ozVy2Fj2Y\noclLYxK3hOh1fb6mVfsU2HXVZbOxeYlzr9SBWHrt5qHa4GK10WGgU0RpMsui\nHFjN/6p0RnklLnoeuZDHULfipKKNpdCg1uLzOGG+yERV6ENCTgjY1zdBe/ag\nrPvLQtgwbM6X1jCotPxuRjke7fRdzwunyfP7QryAcZWbAXEcVGJhMuWQIcV8\nmIKTy3ch/JFXcwdH4odUTgxj9BWFtmCm7VvAUtRHqfMTunua4CY5U33qsgKl\n6IwImIHEYRmPn+ThsK8faRgorn8PPRZogyS2Z7vZHBt0Hs6947+DZQLRvzdf\nlGWaVjbhap3RHVNgkBukDUUh5AVbpJxnkOpbjRfGA19bQHelmhHakDcETEAH\ntfJ3ZJevCkaEwjsq2fL/nmJKzBgqDYmrogv5qcjWGgXvnvf0G0SCwuGnigd8\nXpeGJG8IdCCKCPzyQMkAxFxmeegLv3sTjlbJJSuxZP5CfB7C3A7wSNhxqqK2\n22UX6oy6N/ADXCfmCw/GV6S7ULGmnNmK3nkhoN4G2BtRvcvKCP/Lt66ZtG4s\nWsCp\r\n=srXU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.6.0", "@jest/types": "^26.6.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.6.0_1603108736317_0.9701001091596475", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "jest-watcher", "version": "26.6.1", "license": "MIT", "_id": "jest-watcher@26.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "debfa34e9c5c3e735593403794fe53d2955bfabc", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.6.1.tgz", "fileCount": 24, "integrity": "sha512-0LBIPPncNi9CaLKK15bnxyd2E8OMl4kJg0PTiNOI+MXztXw1zVdtX/x9Pr6pXaQYps+eS/ts43O4+HByZ7yJSw==", "signatures": [{"sig": "MEUCIFrF3CpO1Pj2Kb+hOD2Tv+tyWiT1LPhCBbCLvm4josk/AiEA1HR8Fowh06v1mly0VT4aU7TuzOI1lgR+Fep4qlPUCZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp0cCRA9TVsSAnZWagAAufUQAJyf9uU4TlZzxRqyolR4\n3m9M/F9ld1KS822TSB//d3Rg1NrzCoqXaHRiQk7+RlIQokrcMqKezbyWRQkz\nqQvUGptTOU7+jClSes+1KSWgfubnWn4imARv9cZ5pqmTWW5gHwyx/yA/rDBQ\nPeXB9hUqegilPHMCBaOCd9dPZOq/O2xOMfWP2vbSUlWR5XV0saehgw/F/oZx\nizTjFYOm/uA62t8M4u1db+Nn4roQWSS2dIg28GNk7OpiFfI/B4joQpB3sSxi\nlQ751wSSGAQaTkImtwXPXKUGRROVDv4ixo6FF06UGkoPDCAL0fmykUhCuiHd\ny7ylXIhkDhN+VEbfLSlXUcw4VecdiIPi+ZVAGvfS820WM49S2oQHFbfMXtJA\ncUeUVUP92cQx1EbZthwEAfiOApTT4KQPRYAWtKi+tiYYTssZFtv6iq3iQV50\nvjKkGqBR++IKD+JlbNjRvDqSplIEyM+WvsYAh+tqC0P1XHFdjMfJaTlztJnJ\n7daRAQ9+RDmLFnZgQN4MmHT8E/oqOwEawg9L5W13ebb2bJtwgBgICFX/ZSBw\nFROq4MicXgkco6qEATOoBfX4mz0arGHZ7PfJaA9jywUa8iF7uaiXGzUgEmay\n2IcZir7VtqgAqvOFZT/j7RP1qHPZd1fxCHoB/Hv7uH0f3285hsKaIiaKouVA\n4zcB\r\n=rpP3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.6.1", "@jest/types": "^26.6.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.6.1_1603443996092_0.048052208613987846", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "jest-watcher", "version": "26.6.2", "license": "MIT", "_id": "jest-watcher@26.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a5b683b8f9d68dbcb1d7dae32172d2cca0592975", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-26.6.2.tgz", "fileCount": 24, "integrity": "sha512-WKJob0P/Em2csiVthsI68p6aGKTIcsfjH9Gsx1f0A3Italz43e3ho0geSAVsmj09RWOELP1AZ/DXyJgOgDKxXQ==", "signatures": [{"sig": "MEQCIHxDqEALdn8e+pgCCVj0Idf5EsLj72oYPHCluWoI82I+AiAXeVdEHA07w5DwtcZCTL/TGRH32BXvoYyJdv1Zm0lhng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADrCRA9TVsSAnZWagAAa98P/RSzOLJH26kFFS9uwd09\nac4Z+UPgyWzT3GfFoD8j17t5bjfXMQJV+JQB+ZdGRLTYH8MCRZUZxjUwSRsi\nEozNmK2F63CIdV17rmCWbkaeGmxRWIrsA4u27jI+zaseT3RF0Keex614Fn7F\nG51nR0zUZlzpN7wgflhF2L7PjZseaL8XFEIef/Q1ue+yzIOjVOB98ZQvnXUQ\nKa88K93fT/2zCDZR+91JpctzM9xyHo2Ufl1ewg4/aE7wSovgKyUsWjQ8mvDP\nWDDqk9VeRPF5nWx14mSzMW1H+rSnuq4H3xexBP+x9ZxGW5rlJaBNdKR/W9Bt\nPopQoeeLm3pPxmtEsUefVtIwSqIo0B7y3BM51iomkaPoCQUw7S0G36sjaMhS\nQrM4/ZjvSrsJSFtt0js79o0E+pqs/OsE+0rljlE3mq984PSd4vkeB+Xme5kW\nSY8uDbKzNW3/ccURDJRJ2FnVsOs+XasFdTu3+eaf45QmUQEeKTxcccRo1bEj\nVkNd+YyU+Z+Ju2XAwGKACOFvTkX3Z/g1YWgSE6FXYY1IgJegCw8tVl6nyZvH\nCCAkkqvkO8ZsuFvmFjcU02qSry4GJIgA3BqU9xNWfyjDA6sqx2CwWJpkSnED\nRj8l4kFOKvOwowXsBR4WJIi88SPLf4SNi2Nzsp5wXfiYiTTkG4dxOselGJio\nq7Tk\r\n=ky0Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^26.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_26.6.2_1604321514650_0.8486736977321603", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "jest-watcher", "version": "27.0.0-next.0", "license": "MIT", "_id": "jest-watcher@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e0bd7e0c6436565f843f4bdb8646fd1d8438b1b3", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.0.tgz", "fileCount": 24, "integrity": "sha512-7SC8ohjAsKHhyCm7KPjCeC7lnAGvzkZ79IUImmOFEeCGzXbMbTzNbtRke3lxyDJxTld0w4e5XFAPLNMnQ7bKcA==", "signatures": [{"sig": "MEUCIQCSI09zdb+gjwtd4VzCb80kzDIptbHAmyyrxSwmMAid/wIgTejv561whOLp5dbsh6+dx1mraHI6O3FXf51hRLJTB5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KWCRA9TVsSAnZWagAArlEP/j/NpmXcQ6ci6nyK547M\nPk+AJm+qrXwz5Myr88YtlRl47Qz02iLmcueNKSVCFZPCtQgF7RK/WNKrcWwK\nKEkTFrnk+6abWz64bmD/5VPw57rzy7qChuXhVVeEQxkM1TTOO6VvfoZ7P+WL\nFSWVPteCBvOv2xE1g52lD5l1oKpFRNf3cvkfeuYYbY6xvj+rkxsizyK+I2Rz\nhewt9UNWh7CZK8rc73oFREduvZI2k2GdMPgpyOWtH9rFVyPry6GMKcXdJE2l\nRzbnX2tGjGIiwWRhQXcuy8zt6vq7x0VdW8WcLMTp/FSmXw/kipsJRy3o6pGW\nZi1wHvFmML5c6/UifUvBAGfQ/aFPPC1iFib8fWx3a64pqun6eWx3vEsVz7/q\nnmjSQPkSntG47XHT7+HzKDF//tbfz1Ih89ZORUgPC2pj19X1+k+VwaU+CZnB\nguskjM4zKMbCBMNfO/mEE/TjYDDrD2kaQ9ddGL9thGjtTdVqeNVENJ8GAhDI\nwulveCBW24e4gHqiyfmPwV0XyLIh8DKCR+5GwK02ugK05pIeMHCm/mvyRcNG\nnj3tJGQ4cZkvNSMTQHnzRSYrXUN1D4xuU5Ni1igocCT9Ql1QIHsYCiHU1Pfh\nQOqQ9yvCgdlQzqYDryxx/B+MFMUOsyaPqGrBcYRED0GJYootvTCvOH9SrNDs\nYAkf\r\n=oxWe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.0", "@jest/types": "^27.0.0-next.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.0_1607189141839_0.15142247964453626", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "jest-watcher", "version": "27.0.0-next.1", "license": "MIT", "_id": "jest-watcher@27.0.0-next.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "26eed07001e250eb21af92d9e84f2c91afec1b9e", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.1.tgz", "fileCount": 24, "integrity": "sha512-MRcCEIr4BckZ+Wl8w2knrGwPGDrLOxBMtsWMuEs4cORzoExQK3LYwmUgXlEdfmoCqi9GOmqhu9MGR+QibUXvuw==", "signatures": [{"sig": "MEUCIAZSwr6B9jBHyUnyqfQ/ZQc+SgqNWTX4VQNd9+xL23/eAiEAnmpJep6PqiNWRB763OdGZt0Ja4/WiN7gqHV4ICv3fvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziN+CRA9TVsSAnZWagAAVuEP/0SZNE//IGjZtMmI6Kiv\nD3hVGoKIl8I+IC9RrXpDGBFMhZgPE7gKzcFPBqZe1cjPlY0Z1qykuWC08bq6\nVUaiFhx3A8+Dn1Cw92TwcLZSRRTrId4gq38sHR4yNa+VaieuGtoPCsOtdRbF\ntFJoP7J6zptpyu+x1PX7UMXQuPGDDA5CwEZriOY1lhGOfNdnEsP8Eii4WOZK\nY9Cc+gX91hSP0zNE/TGgCRPQFV/VRbaVVE0gzkFNRy5L76BLaTsS1w0UsoS6\nQyoHjcDUWeuN7e+THbu/D2tHAZkubjjoMbxxh8siUmgYTsj1xAFx465SGCRI\nW0y3tfJRyyoKaWtsRlI8K76bk41F4ASNzI5WvecQN5l/hTzLemwLd5rHT5yg\nKpJmwMymqbX4IyR1/awI5b4Xrw1/hd5UtkRTUHLtrEsqdlrsuIBAgbzq66Dh\ns+lHElw1YmPH7X3hYDFb2Ws3FQDGgAsYJWk6JAi7gFfnfkSIe2Lj6V4Wzpf7\nTBOh1GO+pVGFh8jTg6bOO/50NlQvgCSkMWjFcdeEJbM6xrR4PLmMiJOmG/ck\nWa8G+SXEtB9tlizVDAqj477m34LXQVhcpWGpfwxy0Z/51JJaYjfvHUmLCuiH\nNonuzIfkq5F225Fc3N1wdie6uomX+djWUWH1/3IaeKSg8367sr5S8ikRQ7Pw\nivAH\r\n=kbHA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.1", "@jest/types": "^27.0.0-next.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.1_1607345022277_0.9705843211725562", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "jest-watcher", "version": "27.0.0-next.3", "license": "MIT", "_id": "jest-watcher@27.0.0-next.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c36c241a7ed8f848def1431b53cdf14317785d95", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.3.tgz", "fileCount": 24, "integrity": "sha512-xUOP1XG1pY+Qoj3MRjQe7UKM2YLM2LD1PTp0cDb8G5idITFgFebCyDnMgp2+L/dN1yvlRqan0DRMppBWGZizhg==", "signatures": [{"sig": "MEQCICgkuuNyUz6rLbqUbuBd3ezuWAA/4ZMM0mazS0XhJBFuAiAYVcnnhyqsnC5McWI7T26xGJwuJlreNt/h5dC4Qewasg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuXHCRA9TVsSAnZWagAAljIP/jt7Co3c8O19fNrPw7es\nTWmibZy0/CWNXt7dTw4EbczzyJncdD3qCmHrCSc17Sbl+18wn6OtZSPLwPrQ\nxKxWth0kh7P6yLmHAdB4SzSY2ew2Sg5pHyUUZ5XEUZF2CXDZkPZXYBAUToVI\njqmGcw6A294p/zs+7Hi17mL6RYPlUrgfMdbNLZ2u1/TKHO2p5wOaXnaMds60\nQsWuLa1zkFOvIh9heuUwm6vMxR77xm9uxd/Vew+zWiAjv5Iz3up3mj6I4yN9\n/bmP/ExFlF8grS53G4P73reOUvMi7HLF978VXN8CsC9XiRc/4+mJ92ZZm5Yx\n65S0iNZkndjOdR9n3QvocswO8bagoJAhgrFudO9xkXzCDg+ScS8javwOwl8T\nq8tzM8sOcijZ1jUNCRuvJg/UWTLcIv+RACXOqR93QHg0V24bfXt99xpBmaMZ\n3U2bakWCCSrqJ57wN7uiwSKKtJ6TZF5cRwZlHJNzH14WUi+oxOOxWqA8HJT/\nlJgh2nAqOWoHHwa9dCyIjj3WnMHQeKf8YBwDVND8vkJbELb6VSEHBDuXcVqC\nCnOZ9XPGwTokT1VyC7t396V6edOyVGvrR9m0Qvf2KHI+DdAMOjyawFARDaIz\n2ULOjhgEM2scQ+j1DhQ7bPe77b/SVThGV7yU2/c1cz/lrt8W3smZPKl0UW9/\nqP7o\r\n=8AqI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.3_1613686214681_0.14085270924262527", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "jest-watcher", "version": "27.0.0-next.5", "license": "MIT", "_id": "jest-watcher@27.0.0-next.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dd9c51044d3ba0eb971b57ce4fdf72f10783475c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.5.tgz", "fileCount": 24, "integrity": "sha512-V6H/CbdIxdJp79UGEOcEiJRqiYIrG+Izos7RIFnHum+GyhZTGMPPwxlsAa82eN08UQaBwFk3Za4VldUxmYPMhg==", "signatures": [{"sig": "MEQCIDfzFfVxaR82TVOkssy9c/12N/6+5FqLe+UDlH0y4jMcAiBO0Ug6tJtU7xiCz9L7sJ2DZInci5ezRB7uU31AWW4Rxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1ssCRA9TVsSAnZWagAAMCgP/jIFvY4uDGfODDQWIDCi\n447rGoJ5jxW9WBG/q+WCHU1TOOtxvooRpv4djq93XXJGswFzSblEUQKjQAW5\nIQ6emDhE0JbEfK1AsGMWCBdKtI4s45H9b8bFT/YhJ2R9BJ7KJ3SN+rRvfBq/\n7Pb+bYoo8/74eIdBrycpssEAfgkmnQH6gh8tWwLHHaxWOVGIrmOxJipeOi18\naCKhDDpPsNQXsEOjhXIbc4hNL25LCBS8M9+2zPmzdTCOUfTs3IT71YgLSPHz\n0VzlCVWqHqxw+WGEHtqzXsCCvFrvMTQpNcOecr7J37fS7BkAnm0jL461vdp0\nDFD8pPYJ6+cWigIWKvaon0dj1BC5TGfbqrwXmdwmD0PT5JXXYh7tT3KKgPnQ\ndN/hh1b/86Z1L9PBKoD0OmfJvPFVqGjLVuhCkQ7PPRblL63eQJqnzKCIl3Hn\n2GhxJ7aJRQQB+rSEHOsIXEVrf8aNo8t2Exc+FJBlRE/Nlw1EP1sP5VT+43Lk\nMisisWafV0yMcc65Awt3feaertBvpWbboEluP2Z2XH35mpe5vHQXqlE4SHe7\nGtNuhzsxg2mWioAcQPoSHB3VwolNjrynFKDdp3SY85bGsgaiW5QPm12t2CdV\nU9+v1ecajDDzII4++lue4I4GY9QDa8iq++pYhkKBTNs2UHNYiQE3P1UoVn6/\nTqIO\r\n=5wmJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.5_1615813419911_0.7216625473589704", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "jest-watcher", "version": "27.0.0-next.6", "license": "MIT", "_id": "jest-watcher@27.0.0-next.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6abd9e5b6cb8c6f101b70f0274b7a814ab191980", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.6.tgz", "fileCount": 24, "integrity": "sha512-mljt2DsTWHR9MajSp0ToBF/+gctNEOeUpfuc5wEiWsEOc2aEby+fOaFSK4SIs+VUu+ljPTV8mWvQ42q1Q6HCtQ==", "signatures": [{"sig": "MEQCIAZ0DP63CJrY/6scCLYFy/cja40+W8VOhTvhL948R642AiAYcXEkQk6pJuusbHHEZWZT1xku9sS2/xXm1/XLuYkWtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOchCRA9TVsSAnZWagAAtUQQAJdLUUJ2nLuYoDy6ETXj\n4tq/VPGHnaGuRzGlrVyvuwEXn0LooOr8zzM3PlStA/5b04HtdW2iBbSNvNx4\nzTDrwPQjMgF2rfDOQvL7vpcxmrkKYawbHsMaLDshN2dc1G5MIb9ltdcEg7Nn\n4TTOqzhEamZi1ObqD2ipfus1XqNHGQVBMV7a0DVWBOxGB4PdAd8GHUwaiuO+\nm2yRlpT/7ft41rfhaKSVbRStN8OVFq03+GPVx0zW3I1dtpBFfeP/scs/9RKa\nWZYa+DthE7JlhF8pz1vqs5h0K4UM0HkXR6AX1ME+qI8bSI+hvNisiFKhwq6F\npeM3FLEleyDTX8YnHrKmaheht1uu4FPAWK4042iDhidimSUJA6ItfOlhmnDT\nbi4M22+/6blWR6Q66DHcnkG+11rX4s4BzEbvJHdeiPCOLoik/ofAPQgYqrqZ\nAi1L88WTuY/v1WCFiTrEccrjZtx5FzbLipFUUMYd7NVVP1ZXMTXhddR9d9J/\n17iOwkPF1V9dqEIKuxmKyO6jx8SnWotovfs78O43CE8vYU3pndCgTbMHEkOx\nTc9ls0NqdyAH2lf2eOTY3IG1ZHeaRbT4nZ299z1yiIrffXDm29KDUYrZkfi1\ntICYrEg/SFfNrfoNx/E/cPZ+wQHbDw0/bN6VCk6x+SBopo3FY1fxrBPqtDyR\ndvFC\r\n=6jkf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.6", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.6_1616701216849_0.6188038590210772", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "jest-watcher", "version": "27.0.0-next.7", "license": "MIT", "_id": "jest-watcher@27.0.0-next.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd76a1604d633cfd6579b9b4041548795226a231", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.7.tgz", "fileCount": 24, "integrity": "sha512-gDEL7/lfSw5/VgsAX2i9xPU3s/e7ooCDTXY9T5cAvPN/4o6YSlGkYYVa2EfgzfsX4AwaSaEOOZ/mTuu++hKxBg==", "signatures": [{"sig": "MEYCIQD+7lXIhOqRNIMpu6wX6oCoGQRXfi5NYck+KAkVdcS+fgIhAMvlYNI3jiJoqkm7PSK1AnZbcmFzqufWT4eLgbtFkxxa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCcCRA9TVsSAnZWagAAmFgQAIl3BiSQAuFRHZmdU/Pn\naWZng7iKdIzta24JqSLHoWVg7J7T3xjPYwjg3/qgi5zQvQTKKS+jQ1/5F843\nW1MFXNJgb5E6bxTH+7jc8hL81BQRtV7qjfl+e0LxcNETqr5old4TWErsk07P\ngh9wkYQRb3C8iavCXQM5rlhoSpxh/C/9t+Fbc3BdoI0glmNinviYiNc6Mh++\nITUG08ZhymFzXavnhTy8IL+0/EFyTElZK+veOOxud5oM/vaVneBCg7ZQ9t6e\nsD3i6E3ltYxGURzYbuy4KvUiJmdkQR57SbFqsta0jJYn9+K/ojtc6bC3l+it\n0ZFVb7VD/1U41tfgXOf2yg98AZKYcfJhLJLCPZeoS+VIaR2XSl4l+RXbqvz8\nfaK12NQRQc89DUM8C+FW9TilQZSh11vrL/wKuGQ2tn++XriKwpPgJQp9OCKW\nlh+7zlFWhkXf8vqEsNp94nHtZLfGgjOJb4N0hPKP6Xt0fgO8DEsrbAiVbhFr\n9jfiQAlchZ6duJEMQVPZI6L4p7NnAZaZBQSdDCORr4otzcA+YqJy5JH82Yv8\nb4FvtT8Q9FaztQWfqlDia7KctuIfXE/EceV49cg812kUCRS1rrECb0YL6C/y\n9+vEbTdgAJ+LMjNtJsjE6e5TSNIlm7SJZ/0ygdOhXsGRAEikPJj6CVufx2cY\n1vuG\r\n=dbJP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.7", "@jest/types": "^27.0.0-next.7", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.7_1617371291895_0.1032521533596702", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "jest-watcher", "version": "27.0.0-next.8", "license": "MIT", "_id": "jest-watcher@27.0.0-next.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c81370870cee792e696cbd340cccb6e41b06eaf9", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.8.tgz", "fileCount": 24, "integrity": "sha512-maxzxh3kMDbjBfy4tnLQKVXL9INkP6em8EBjUph+UaigxLznawDfoYbVeP2wYACoetAX2UGIoWXsyU4yTGRO4Q==", "signatures": [{"sig": "MEUCIQC2YJHgLRCpajgww0ZAtyhUfwbiNw2asvGqbKaBs18YsAIgD52QuoCH29RlnXtVZfu7PzdeAyMgdfQXqfla05nEYPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzjCRA9TVsSAnZWagAAj+0P/ju0TmYb7zSLMhPGctV9\nGd2GPLEkq1CdtA4uHAm4uP/f0WAYKNxAtDSbSaFieOXVYArtAxvNWVRXJp/2\nTIlKRLUhDyMTvOuQh1ILphWih2WGH/juDPbQtNidQXLiNQHcILDbn2z0IBS3\ncgzXxKO3AtRodM0S1UkDNKRu80UDwCruWPEByke4dFxyXo8ICAMdVoARMe1V\n6hDzakcrY65ofofV8y+bj7t8ExgrKc4qUES9Wxan/OcSc2Y1hDQ6jEuX20PG\nf6qfAy9lNZYMsYiLsiPKsaH56inusvHoHaSHvw9j0sTQ5Ox6vGXx9PVzKF/X\n5X1ZvuESlp56m10u+JS6ACeLUxAeiKBo7ivuwe/NcOr1ie71DGgd2jvx4FCc\nbrGL3UWWhu1kirR9Tq+F/0isy6os3Dk22gNrHa9z0QKnMkUSlbh7ENHAdJNX\ny486mpXUr3zMD8/pC6QwO+djrKJqJ/8XvA6pW3G4y0j+OgyNJj0U7zmSgUTF\n2D1g3er/HkxQGUSnqckB+GqvMurwqjuMQJXIItRpEoN9uuCFmJx7DdgW9nPp\n3E38zv45BHWMy/rcnztQzumkNLQuBdoAeXPcnpo77lBaPPeoE8lCtzRNUY/H\nue/sageYFl8b1QxJuUO+UR6FKASWLL6kCKK5Dp2MZ+Dx4/Fx25mN+ZsGdxHv\nB1rW\r\n=dzB9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.8", "@jest/types": "^27.0.0-next.8", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.8_1618267363087_0.2973712333734202", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "jest-watcher", "version": "27.0.0-next.9", "license": "MIT", "_id": "jest-watcher@27.0.0-next.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "712085fb44aeb5aa7b699797b10ee1b0e91e3949", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.9.tgz", "fileCount": 24, "integrity": "sha512-ka4E2N3GKFKtenWCnTChIiwqA5taLdBduEHKrHPQglS/PrxFV0XqWJ8tu289I8rwCdzb3dAoMQ/No9PY8RRJpA==", "signatures": [{"sig": "MEQCIB1xLmDA8zIko0Vg7PTGwUM0geftzmigS3JkfQESk2hDAiBsjv0DXREZU4VTIOq25RxHWOTfJzvQubsJ7Un/+NOJMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjTCRA9TVsSAnZWagAAOQwP/1pDRqHlc3lnQBZ1zwkC\n/sRRnTnj5+HrT4LKdjS2pcz8ok+lX2G6JBaT6sGG7rBj45eljn2dmXvXhtsY\noCntKVP/uYQQ7DGXX7N8Igxx3s3kROw+iMmGsTaRE6VlNk8aNDPXOCMLbXxW\nReSPkQaa/m0bFYPfTtipFdY/qXapTjKKFXhOz7uD0/oJw4j+My2q7AhQxbzF\nZcvBs9Pnl3/9NWSI77N2s/Ccet9g0xEGsW1ywaQPEsf9Nkx8ogxly5GYUhqZ\nDyJMeyAYl9sj1D/pbrmmWQr3/oLtjRyUIcBHKE+zvmVKq+omWcbIo/6w0DDE\ntN8k/4fE6YDkya4Hy4DYZ+qPs0momZwAD812a49Ivg/6v70SkIfxqrwJ7bzT\nw+rBxKseJsVId75H/gtrCnT/DR1wq4LV3mc+N7Ms6kl12gPWMavS8adm1vXH\nj9/lSsIK4Awnl2xvXQASTNptqxk0RVjZ7U0TyAMpXyzEtnBk91nxnouAwYR7\neY4dWzva+yPgm0cWYVd7s8tAn8uOsECQCen/CC9Bs4AQuXX1+dYsKWAGBCdb\nDj1dAchh4qAHMi5UXf41PQXycB7KUIOgTuAVrOYlufRtlvxjegba10G1StVI\nDrsb5RyaDyA4TgbpdlK8oBoUtZ0EcFcAXDItOYM+dUbu9j5en6Uuke6zKHMT\nin6V\r\n=SBoo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.9", "@jest/types": "^27.0.0-next.8", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.9_1620109523295_0.30721554868681", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "jest-watcher", "version": "27.0.0-next.10", "license": "MIT", "_id": "jest-watcher@27.0.0-next.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec2b476242fcfa849c941a348109d0c8667766ae", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.10.tgz", "fileCount": 24, "integrity": "sha512-JqlPgEzQRpAnzbYTenJ4sW2Zv9n3xyqu3yvxaL7UXqXTZyw91Qb6nMQTVSe8rUp9VE4+75vwdYkU24dQAJ2KVg==", "signatures": [{"sig": "MEQCIA7q/1SPwch3405Mwv07wCGbbl+Xmf3ouGDJZojk9+v5AiBLr2rHCFrBqI5ZfaFm6CKVcSb2KOPyo/YD+jNIQB0vww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4XCRA9TVsSAnZWagAAM5UP/RsyR6KhVTZGHIUpCmMm\n5IBq/HeZtHiMhr+2k6XLzwilGXwZ1CQSrL54NxVre5OYrK9Wp4Mw87G5k202\nU3ielE2WI5jUsZid15WzGiSD78au9aHvSqvD/59CD7yz1fiskQ01rLSeQkXi\njc14wcIkolzo0hABg+WZ6bDAwDmeU9LVZLXvp1eU3cOxWg6f2tLLRvau0aYd\n6NSdv0iyZp7DChgFtc3kl3j860b2HTX7rluMUPO8QQ+CB7NUA7ToIkZ5E7Rz\nKxWz9xou81DgJVRBiC9jFahf+sm7MaupC/zb8HNRQKCimPvRa4AR0WKbV9nn\nPSzpxrRRuzGlyIG81prcITxhyyP7NuoUC4t/4jW0Q74nXA56iCh+QfQpHBmT\nFgyN9mCPwl0yKOliYoqBxSqEFbFtBfMG61PwjKnMA5ihXLZF6W1/ljWA33RK\nlCLnOtKzf10yq/oHXPBz29N78tDRf7NLS4wA/HxTllE2qQRGW6QLVREvpV7e\n41WHI1CrAaIj17Ltg/WnNLVxId48Bw6wy6Zz134bwz7oO7Mby2219h1rzd4n\nVCF8jQzMdIb5XOC/iz7x4Wq7J2Fv+KtVds3oC09/7X6LXpXBAiD1etIfAiW1\nNAgi7PQVvtl70pqyrS2zMlP1sAcjco+5pvb9nGRGEb8qOjpNIB/ZnH8mG79r\n7zcT\r\n=zrgf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.10", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.10_1621519894625_0.9686208191816306", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "jest-watcher", "version": "27.0.0-next.11", "license": "MIT", "_id": "jest-watcher@27.0.0-next.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d5d880288a3da7a98bbc8645092e13e915dc2b77", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0-next.11.tgz", "fileCount": 24, "integrity": "sha512-g2XXqSwqeM1JeHF9gGuEmz0okKogXUQwVXf3TdfUXN8ExHE3x4xsWGeiiq6BFac1X/PjKqZaBtrHuhA7tJh4rg==", "signatures": [{"sig": "MEQCIGuq8Y57wAYntvLWwOzSLsPzNUJwaY7ysL6WVtejFI07AiBma/7PsSPgjuqw+2YWzdVaXJghTuZPKa8nJIkYDFRNZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKoCRA9TVsSAnZWagAA/bsP/iu5NKBvvz2NwNjOSjgG\nPa+VZ411FSRkxgyMHfRG6EE2q1HQwMxpn+tVWOq59KHSDuMytNYxKM+tJoa4\nDOXNWC6WMQQ5SPxbU2n5xFY/ZYB7hVVllqM+BIduu08+CojGzjjfJlfD1x1j\nxd9UR6Zx7is4dXhYedzVptXwo4tGNrs6wdXJ1NYQX0qYv9cakrS2Qre43oKg\nhZgxA3lxO4im52vgrAmRObNgg3Lwa4eHwcVc+45zQ6Afzjh+YIJ6IZZP8AsW\nCS9Hz9abL0/t2q6ve4vq6qx8l5IHWBk3xpL+285qUHF7dil8A3vlYnzaC+/y\nKembcB2LwAMf+qQhECbK7Nnh3n4SN554IU3b2E6NeIi0Wozmx+HovkG4aqm0\ngbtAfZamQBJoivbXmD6IkWAzVsm4DYDDMrvsQfR8cIP7ax2mLiAwgzcZdES5\nWX5koyKhZcXiegAcQcqFmspoN+ER8K14JVe2TUxkgpdY4cXRGQ1msvWkStXI\njAsQtpZyKhTx+9L7BXILRunTBJTB1Q12ktfQusDmnr1CF2+UQYGEndJAWR1J\nEkjThQCMfvRv79n/Ez7IC3o4CAPRb8M2h5KZyCGPQX3oTCKq7DV6vQBsNJXW\niHNeBGt6nCkmqb7c8fNpiMcnG18383PF1m+i7EZzQspyORR4wBTBKG9ZtnFr\nDQ3c\r\n=gwjS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.11", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0-next.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0-next.11_1621549736455_0.37088520354886945", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "jest-watcher", "version": "27.0.0", "license": "MIT", "_id": "jest-watcher@27.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "29298b65f92f30d1cd591539428fe612340cf4c0", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.0.tgz", "fileCount": 24, "integrity": "sha512-rgOf6LwPB/vOJ7SFurtjvAZo0x81PGg2ghduzfpG+9/KLU0P9faDEyCfYInaQVJutkE3cLUjZef1xXTuL27mhQ==", "signatures": [{"sig": "MEYCIQCGYZ6SOF4CzYsZ/N5oQmhLZyov6zeDsJuaRxENj1i3ygIhAIAfBNPExyNOBlRQF06MV4iucSMCJvH//MuliwpktfIJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLIkCRA9TVsSAnZWagAAVHYP/RnZE9EbLWgqoOGNNO9I\n/Qk2WmqbLyZf1whF9ADGiLw9o7LsYivYlv9gx1r48VMUTvFuzNtiKv0l7do0\nR59BqjNs8laqyjT6hMceO6qkGDSuzWGcK8nhUX39qJOcOenARDkpGcHHD3BG\nHVoqgGpi9XUH3g8rb4dE+2Y6Dc+7zXoiu2npppx+tgbB+5LRwk1F9dzN8y3D\n0RS3q0WF57dne6IipOu+DrT86GlpKio0dVEG5gqXB7wIdtphmSOAwAQXik2Z\nRRzpUkDUNVJs18aZ8Od1MQfljMJ7rRqvg5zLY/QzJqyiPp9OKQgB+J4cLeNF\nCOQqSBsvpI28a9rzKxS6g6keKveEre4VJpj4tvoBxMl3ln3JPQyqDSnMYgkC\n5jSA4Ta4f2Lzl5d3zP3EkxxFMzxE7kDMd3Q03vxC0CQgKWmb2EDHK2XeBrZJ\nTB/OS2dhUFdDdtJ6SYs97maxXPahkq0OqRwS6NXHheKN8BEMGShzC101ZNgE\nqeV5cj5fBX8q5m0Gl6pbRDdbuC3gVOE9pJw7cdb9UkcTdxlCJKgXdx6b8OAh\nYOneBBl5ELvgWV760iPmv/kAQgmFL+3vAKvnPihRYB7pCR1+f2hXPb15HzEI\nn9Yfks1Ra1nxTit7Qw/du1QKjK/+7Ud58AfX0LhKAf29YqC3NKuZK4P6U6Ma\n4gq7\r\n=5j4a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.0-next.11", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.0_1621930532063_0.9514766537328809", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "jest-watcher", "version": "27.0.1", "license": "MIT", "_id": "jest-watcher@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "61b9403d7b498161f6aa6124602363525ac3efc2", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.1.tgz", "fileCount": 24, "integrity": "sha512-Chp9c02BN0IgEbtGreyAhGqIsOrn9a0XnzbuXOxdW1+cW0Tjh12hMzHDIdLFHpYP/TqaMTmPHaJ5KWvpCCrNFw==", "signatures": [{"sig": "MEYCIQDDuHui67W5MbiZn45E5RYoZhc85sRdaRwFHHJERVtU9gIhAN4GUhqcyXJQBlzehzjnkEMCnmPqyN9rCasILdXdYlGT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMw2CRA9TVsSAnZWagAAmcMP/3T3RTHigN8MefF5Dy39\nh1ZxMeSr/c5dKotesD79IbUyDCiwPFv1LcvPvDxPikV/cAlndpvYQZQdEksk\nLYecoz+4E6htIoepCSdyA7Gde05Nwnb+p33qu0bhc49fjy3jlT1c39QFv0x1\ntMP/1+yzEKaxPpgQABGVVciROFTjfeuEA58wiffgl9ILYSP68g1Ou/vvwdOO\njShyx7IWv5Z80MII42mVqvjLzTs95BfwbBn4YTbwYoTQVeY9BPolq+ewuRzr\nID6wixc+N9pUmue5qkf5+r1/Wg4UidMu+ot+zBPVAlzzhGizcYJGk3rveW2q\nRXgV6/ZVPbFOTWA90savisCuxe99hcQd1c2WWfvw0NCBvJpIZHrbms2SYIpC\nGuo9FTFU3eTRHOo0sTzHdpyhVCFspaD3gLZQBg5b/Zw5dK1kDV8yL/3/1Ysv\ng6XFzkpYlqVks8SXiegW6UTaZjeVtyEX5fv8zlNV4DYzoArmq3tp8Tg1qG/y\nbPe0hTQpV52QdjcqGqE56jdXbV6shfO3P+X8MMkAKjTzlDhPg58ZPoQgiMrx\nKpXQxDCI7t7TT1PLGuL+zG0gkVTBnHYPXcqL/p9fE24XD+ICJG0QX3pcmuHR\npsnI3Zj+Gslk/VQ6OxkWYjaTnQmrZGJZFUOQ7hlAm4jVyyIbyYiCXNwzxZIF\nWfA8\r\n=nYKh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.1", "@jest/types": "^27.0.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.1_1621937206561_0.6208756214965874", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "jest-watcher", "version": "27.0.2", "license": "MIT", "_id": "jest-watcher@27.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dab5f9443e2d7f52597186480731a8c6335c5deb", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.2.tgz", "fileCount": 24, "integrity": "sha512-8nuf0PGuTxWj/Ytfw5fyvNn/R80iXY8QhIT0ofyImUvdnoaBdT6kob0GmhXR+wO+ALYVnh8bQxN4Tjfez0JgkA==", "signatures": [{"sig": "MEUCIQCGdpSPG6SonGkxrSx4bxn62pYpbUyreWFwZ/lu4zutDwIgFPPJCyR3D7nhpsQA+omDwFv7wwxiAQJFBq9rzogYTD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi6LCRA9TVsSAnZWagAAjwsQAJ1s1DT38YLBXU8bBvvi\n85su6mE22B0yKG5IimmPiSVQP0WkI0WjsfQxhVYA4I+RKRBrUX5r2whHJXVi\neB/0BQz4M5jLmfHqNsl6k7A1Ypi3D6UeYXBBUNDz2b9+FJerqt/XWhNLDiCI\nwhq0zj4zeNA4HgZxuwYqf7oTDQ1SSRKMH+Eu1/KjWPjWLgF+jMv+clD9jvl/\nho0dPFa7tTWdMgqft1yrA2kDBakYb7CEV+PHDy6DBlKhNqWpvwCf+IIy72cu\ncGmg83/LfVzrEKbXSmn1KDtq8xWSxwYv5v0mka7EDgpRHx3LV+j3p9cxQto4\nBXNg6/Hbl7w46A0P2y7eO126QeaZw3rr3o5YUeOQr0FzGP2fzccKq930l6qs\naVvE+cyv9MCDqtt/4F7AOoc7p5lFuOSlFgqsGT0EXkscrWA/G7kQ9TMM8nIU\nTreqXt4RuEUhkyOiMfDplSM7yCzQL7Az7leL83gyy8+pTeUfH1VSncZjPzKo\n8vaoBoxRvjIoa/N63gLh8qCnNcqefLuiwS83qLwnfHqsMWV22DjUKR/ySwFt\nd/zZOjKRnsA0VAnXHEtPR3VRZE5A8mfBFw4cVSjKsY8JwvazATCZRwU6R/kj\nIgN/xM/TMWYLskieQO4JfuNSi2nK5tGlWYt+3bZMsBo+W6mkpAboXBnM+TQy\n44Ia\r\n=Ibtf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.2", "@jest/types": "^27.0.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.2_1622290058784_0.949330237420825", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "jest-watcher", "version": "27.0.6", "license": "MIT", "_id": "jest-watcher@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "89526f7f9edf1eac4e4be989bcb6dec6b8878d9c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.0.6.tgz", "fileCount": 24, "integrity": "sha512-/jIoKBhAP00/iMGnTwUBLgvxkn7vsOweDrOTSPzc7X9uOyUtJIDthQBTI1EXz90bdkrxorUZVhJwiB69gcHtYQ==", "signatures": [{"sig": "MEQCIFopfRg4u2mrPrRk6uAqXek9LdOKzW+U3gcQSji9gx5jAiBfsxBLJgeBSsgZYo6wOQ24NuSyTttB7PIuXvQmDpBOCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gF0CRA9TVsSAnZWagAAFXIQAI39OwHpo4J+YnX9iaZg\nh9Oz30HQmQ51EAXnukum0sbcvf6GyFMEDi5VLet8oPmwzMiIHe51Unv/g/IF\nfGXyRoQQnGaXPDufYZXrYijTMcSsgsScuval8kqVBr2D8QItACAfLNxSwLg2\n3wK1b8N92hMRJe/Oz0Ulq0Bkub2RUBM6e5IWRWcb7VJ3v333QPTeSf8HS+6j\nGR0zv2uzG8PxJ7OweHYWyCaDIlZStT0fQrbV38JI6ayRzIOQnbkvpt3eLdIJ\naSIUKV4rTLg+P3wieJxqpOfvIusr9Pu96lD1kxoIN5CHaqjeYWXa2+yx2u2K\nuDPCwssJoKwQQaP+3EtyXl4f6R0TOcKW4eZwko0ixPnc5yxSt5LhEOn7qDR6\nFYryMKM5GCo2KKURRC5NqYPqToL9h7qa5jA0dE50os+BdtxrIJ5/jpMv0nHF\n3StHmkFKsyiEBEE0wumYb0Jdffz9RhzBGOeE9srn6WCED5zKLxUA7oZ8YxZ9\nKGHlayL5tU6r/L2FiGvo2IC1n8cG3vagTKks1cq8mr3oo69NKvT9oPORbZxy\nVk36Qut5bIpBTZ62HM/K9+4f/54nCiIt6z+iJB6NlI4YUP+igCgqQXqa9aNm\nJDRQTRWs1lPN+E+SkyIEtdhsAiByAuNfc9uiswOSO+jD5oXxfjvWifZUbLQm\ntAMt\r\n=RtXJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.0.6", "@jest/types": "^27.0.6", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.0.6_1624899955784_0.21157975049208733", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "jest-watcher", "version": "27.1.0", "license": "MIT", "_id": "jest-watcher@27.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2511fcddb0e969a400f3d1daa74265f93f13ce93", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.1.0.tgz", "fileCount": 24, "integrity": "sha512-ivaWTrA46aHWdgPDgPypSHiNQjyKnLBpUIHeBaGg11U+pDzZpkffGlcB1l1a014phmG0mHgkOHtOgiqJQM6yKQ==", "signatures": [{"sig": "MEUCIQDaRFBl1rE52zZv5G33JATV5cdR99+yaLdrsLoQsGoHOgIgQXOREE/9AYMTyzZmQIFWzU3tQa3Cyqe+1KByhGGEryE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeWCRA9TVsSAnZWagAA3CwP/22Qg+5CTQWEukc+/2eM\np2hKDqZadchrmjiOJsiciZyraYhst3k6JLGTEVhA7MNmCk0Ab60FDP/J1ddN\naWvTEhv5UnPh/Z6Zgmwz3Wlc90KmrXcA/mUH+s4NqEEFFRUQans6ztiixKZq\n361yTgMNL1Wz1v4ljBVxb7lPZwy9LcEAQC1qr7Ybc3fVzaU7huooSZ7FwyH4\neeosmoBemtARynSL+hqIUbHvtS4ixw2/ipJ+rOfidSbWMXVxGrHn8GAB4EeS\nUH0qq1ObcecQ7XBmkJyKa8NfLsdihXoVcEIkYaqtsi9imdPOUrkFZ1Dzf97S\nvmQinEuO+qKtAImxIGQT8e1DmoR34zZDTRqwspY3xNvhh5kPTxUBYoVKxvjQ\nXDLH/2dnpImQKdoSdfr9WCcUmcLCdFWeIPQRQN4pW4AhrVzzjjKXNkgfW+oR\n7Hc92UMUar9RPKpUTlbldDE0g3yDYeSEbl3dP0sGRtBHB5Rm8ZdTbiXosHbD\nLEjUutvdLMXVdy4CuYT7micnhEqp9hvzHr6YbCq5uNRvV2e5Vi9sLuGEMnmt\n7ihy6n6EHOyoGXtCzQIS7f1m/kd4aYHPO7BuLolxB1KLb27tGQsXXg1jk+7P\n//rtTVivkASEdBJ70AKApQu/zn7cl3/WONfayhALkO+OzdebICFqWeLXKrZb\n8w0e\r\n=H3Op\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.1.0", "@jest/types": "^27.1.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.1.0_1630058390066_0.7382435114117525", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "jest-watcher", "version": "27.1.1", "license": "MIT", "_id": "jest-watcher@27.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a8147e18703b5d753ada4b287451f2daf40f4118", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.1.1.tgz", "fileCount": 24, "integrity": "sha512-XQzyHbxziDe+lZM6Dzs40fEt4q9akOGwitJnxQasJ9WG0bv3JGiRlsBgjw13znGapeMtFaEsyhL0Cl04IbaoWQ==", "signatures": [{"sig": "MEYCIQCFzhNQffzP9ZUdh/Ew7aIkiJg3e4umpR9jYqegsx86xwIhAM+RyNyEPERqJfbE+WCWCUKKi92nBAIzBm+ZcinoTI8Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIyICRA9TVsSAnZWagAAi1MP/AuldEb94rNdRQ3Ki6Dx\nMLCXdKE5Z8pl1tkvaLtMcg7k/Xsva3mYn9M6KkFTAy56blemuuXDcp2ZaS8l\nWaodCPrbHheetfyV7uy0Pmll9NNZrMnYmoXRExx0At1ef2JHwTTVPYDWRzIK\nqBNwv3zA5/XtBwHLS9MLcAkVbAKt6xWlmcdZkVnOysCuljwUqIqn4Sw9gqp0\nVEwfYH1U3ytkrwXHn4hEAEmXfUhLb0P3Cq85uiir7hPHH5jTAT19woqQPpZc\nKrI7Dk33+YCum/gd3v1+yEW15xNGGliuXp2a2zNtpUeHVhbLSyyaxgIUKAuA\nmFB0Z++BbjhjneP5vPe35v4yrDJsvcBPX4PYQPJKDuMb2QXgXdgKulr6Vpk+\nEkPP614KSOsDX6fe31RRubPdUMdvlx2UWyKisnpSRyUdL+OGZSdgGdyhC6yz\n4l8YWajrOCOSHwF3I3L4Ce4lVYA90AxAqLSFK0DHlma9ETFxZGQrLwaYmi8K\naQS6eNOnfHfmPYpN9W7+ncBadw/EkYa3CQAX1rQRu0T13jDRHW8cDz882nZZ\nWiGi1fV7RRbfliINkangUb28FFY1TPokyU8Q+/vLrat+Tpqdy6JV3BrHxnT4\nNWYgDMY4A/XOY/3l8SvoGNoW5MUOvKxGt0iahMsqbqmos5HGInGlFraydS9Z\nepLE\r\n=xuPm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.1.1", "@jest/types": "^27.1.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.1.1_1631095944522_0.11497565578878799", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "jest-watcher", "version": "27.2.0", "license": "MIT", "_id": "jest-watcher@27.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dc2eef4c13c6d41cebf3f1fc5f900a54b51c2ea0", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.2.0.tgz", "fileCount": 24, "integrity": "sha512-SjRWhnr+qO8aBsrcnYIyF+qRxNZk6MZH8TIDgvi+VlsyrvOyqg0d+Rm/v9KHiTtC9mGGeFi9BFqgavyWib6xLg==", "signatures": [{"sig": "MEUCIQD0e6HVzipJs7OpyxIR6924k2VRwOCtrb1q4LEH1QQCFAIgK46BoYVy83AwBoSPI6vvDjDUO3uz70X/nqNcBX0VcZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwahCRA9TVsSAnZWagAA4xcP/1VrFtihqAaPVMxeLxmF\nSNkz1GhB77NM4k70WCIBDflfzc62tIn+FWvgQrN2S1sk4+LRg7HITEuzVLQ+\n6Jz6WAiSLfVqOOdL6vMyQwbkBVEpkdkvlWRyCHGNwYLbZP5UNnvekIPH/Edj\niMgzTgEj1cFONfilm3565HRRKX9PUpVPnvZeC+tEpiYbAaCSwfFBlIxQ1jmV\noKgpWRHaa9934CbcLnZSQm1DBE81y/yWeaW2C0+xRRoptCfWnF0F7kDy2/02\nCmmRLFW/AISCjS2CivnXGJSzpdpe3YIjYbluJ5Ap9m8HOZws2JSmELzxun2i\nHxI61bsQ+0aAvUTlz+AwZLz7RbPv9gd+oqe54g7K5J2BaORGj0KuVGaKtIpn\nWE51E3OjQdqCcs2yUbBa0eV6uxAoSMh1xEY+As6LnPCyo7NOwqYuB9cy+tWW\n+8L+SSGJZneq1yE9O/k70M2MDKvG8XLg7IzuX6PvEbWnBtqMgmu/fZAVLaKI\nTfaMsKpGsaiHqcd5mzrU+tu6kkO4cDkAXy6SINwmWTjbaRRr9kJC06kWfQg+\nmy5IbkflMXROjdaYtZiLIcl7kPfGKAymgFCEWSBXER3X0TtShfOqwp6riGLl\nZ0GCSwRBcfT8N7hOfJl6HrACr1wGaQhXvzxYNI1cl/MxcThF1bTL1XyJFq2N\n/9o0\r\n=8W9y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.2.0", "@jest/types": "^27.1.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.2.0_1631520417082_0.57330341789502", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "jest-watcher", "version": "27.2.2", "license": "MIT", "_id": "jest-watcher@27.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8b00253d7e880c6637b402228a76f2fe5ea08132", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.2.2.tgz", "fileCount": 24, "integrity": "sha512-7HJwZq06BCfM99RacCVzXO90B20/dNJvq+Ouiu/VrFdFRCpbnnqlQUEk4KAhBSllgDrTPgKu422SCF5KKBHDRA==", "signatures": [{"sig": "MEYCIQCdi7R0Hx78A4DXyrXQC42nmeHSXSTuBfwba++9/L7aRAIhAJLnnlxov+r80w5oqmy8fUCDVYwO4GdUuZo8qz+0phF/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.2.0", "@jest/types": "^27.1.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.2.2_1632576913114_0.9096902065188102", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "jest-watcher", "version": "27.2.3", "license": "MIT", "_id": "jest-watcher@27.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2989228bdd05138094f7ec19a23cbb2665f2efb7", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.2.3.tgz", "fileCount": 24, "integrity": "sha512-SvUmnL/QMb55B6iWJ3Jpq6bG2fSRcrMaGakY60i6j8p9+Ct42mpkq90qaYB+rnSLaiW/QQN+lTJZmK+lA6vksA==", "signatures": [{"sig": "MEUCIFRDGMNgDmcCgXLryYSXnC/oISsqd+VbsBPvD0E9xcQuAiEA6DESoHiKNwlDZ7hBm5oxEk3+15XzT9qNvHHRAlvfPOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.2.3", "@jest/types": "^27.2.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.2.3_1632823889080_0.15528479895560965", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "jest-watcher", "version": "27.2.4", "license": "MIT", "_id": "jest-watcher@27.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b1d5c39ab94f59f4f35f66cc96f7761a10e0cfc4", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.2.4.tgz", "fileCount": 24, "integrity": "sha512-LXC/0+dKxhK7cfF7reflRYlzDIaQE+fL4ynhKhzg8IMILNMuI4xcjXXfUJady7OR4/TZeMg7X8eHx8uan9vqaQ==", "signatures": [{"sig": "MEQCIB2TiWeMmoPM/e4VLsL/JfipDNZ2vhFy0rlbcBXw6WFfAiBtXpmwBbY5NvvNZA5TtrQ9Pu8eurV+0Wei2rcMAk+yFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26805}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.2.4", "@jest/types": "^27.2.4", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.2.4_1632924296167_0.9937566044845956", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "jest-watcher", "version": "27.2.5", "license": "MIT", "_id": "jest-watcher@27.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "41cd3e64dc5bea8a4327083d71ba7667be400567", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.2.5.tgz", "fileCount": 24, "integrity": "sha512-umV4qGozg2Dn6DTTtqAh9puPw+DGLK9AQas7+mWjiK8t0fWMpxKg8ZXReZw7L4C88DqorsGUiDgwHNZ+jkVrkQ==", "signatures": [{"sig": "MEYCIQDU84PzP00QDhO8C/IX8y63sm/CzM4QuEk9i0SGmShOiQIhANl47sJin7GfMg41z39VIJM26C8sOkQgCShawiFAbxC1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26697}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.2.5", "@jest/types": "^27.2.5", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.2.5_1633700369181_0.6489969968066958", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "jest-watcher", "version": "27.3.0", "license": "MIT", "_id": "jest-watcher@27.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "13730b347e2ae8ba3c9435055bdad2ad73e5c348", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.3.0.tgz", "fileCount": 24, "integrity": "sha512-xpTFRhqzUnNwTGaSBoHcyXROGbAfj2u4LS7Xosb+hzgrFgWgiHtCy3PWyN1DQk31Na98bBjXKxAbfSBACrvEiQ==", "signatures": [{"sig": "MEUCIBJgomNL8oTd+6FOW19Yyu3RKCBU+1O7HRg0l/6z1yuhAiEA0iylpOyDO2IemWzF/15lceB9neq6EVfMIxWJXvs7zB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26697}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.3.0", "@jest/types": "^27.2.5", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.3.0_1634495690740_0.1375709589670442", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "jest-watcher", "version": "27.3.1", "license": "MIT", "_id": "jest-watcher@27.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ba5e0bc6aa843612b54ddb7f009d1cbff7e05f3e", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.3.1.tgz", "fileCount": 24, "integrity": "sha512-9/xbV6chABsGHWh9yPaAGYVVKurWoP3ZMCv6h+O1v9/+pkOroigs6WzZ0e9gLP/njokUwM7yQhr01LKJVMkaZA==", "signatures": [{"sig": "MEUCIQCMOOwJ3GgSwJ44ozxikrkYXlAALveceB2a9gbFfXnWJQIgbBjif5xTpFQGgCaqBEZX6m02Zp/ds/LmCyn7f4sEALk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26697}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.3.1", "@jest/types": "^27.2.5", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.3.1_1634626656456_0.3503592717843491", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "jest-watcher", "version": "27.4.0", "license": "MIT", "_id": "jest-watcher@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1d38eb7a7cd3f488363e0757fa8a4934f5887817", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.4.0.tgz", "fileCount": 24, "integrity": "sha512-0ZXzsp/NArW6IXxo4g7DP/nCJqS/OLCZyl08qzd8ANGSEoTsliivBumjUK5/0gvx/K4Oc60APNyTMfJJ6WENcg==", "signatures": [{"sig": "MEUCIAQPA3Cmo8jMn9YEerxdqlhBToA8QIPtWFbVZbNae9loAiEAm5EOrtbXPbK9CpxOms1CV8EZXsMlBpxE9A70ddOGRLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNeoCRA9TVsSAnZWagAAvXkP/jqGvdYhRFTFa5IXlRlp\nx7xuLCXNHiz/YeWI06yFxYhOIPdcycmLdvBtt4O+ypDcJWflbthMQshDGTl0\n2g+oXCampW37czWu1BO6o7B587sdh2PaSxtld3CztQQ2umay7wjABFa5I21a\n+Qkm1JOZsKsrEOwfZvnVlvIPfnzXMDL7YKAyuOdVsp5jMMR7Dp/wf9tgj+dM\nrCHx1ARDPzWhIJi9eRif/fRjO0uy7wwbISKROeTb616ga+mVgLFwkihUBChG\njAGMSzdZgKjaiQnfftiD1UNWp2y6N1IY8I/yzHDVvXjDT0j17YIVKPQ8DKdc\nXzOm/6B2D2oPmZ9lpqPrveOlFjSNT7u5gDdIjC1ZkngdS2L9F3EjpxyVXskY\n0skkpGjtamwpYsvRm+7Uls6TA0bKuXt7yD9E1LLC7mEcDrF4Xjs1rqPPFa2X\nXyyTiUzHz8KlGKlflKC73b+Byyz61KcAtInN7Dzu12EaPd2cI6/0GlTFjJge\nR7r6AKfs39U5iLed0eGk0lRWo5erFh6PuSOPtYyIvCSWMDIK3Uw2VK0f4x/l\nuD+x3GlvQvo3uMOYj3QlN0nVc8aoVikChpIWgpjbijwCXj+aN5nIrVJ0uXlg\nLXJXC9GmEM9vyYd9v9QPuqMhI5+PPI9VeFxlQ32rzH7hakT/ou/jPGaQdWjN\nakJX\r\n=ytKD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.4.0", "@jest/types": "^27.4.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.4.0_1638193064020_0.8918669389740697", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "jest-watcher", "version": "27.4.1", "license": "MIT", "_id": "jest-watcher@27.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "73ccd12c7de312814920c3fc4c11d51ef16d3360", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.4.1.tgz", "fileCount": 24, "integrity": "sha512-tm7C/Mf69mL6pvRsXmH4GNaZbXZaJuO4Qz8/Gfl+oaO2o+vjipgR3o/cyDYfnZitkqwu43O6cCRPXRLIlpWqtw==", "signatures": [{"sig": "MEQCIEsNqEJ99JtI1tBeWNJHoemfkUeHio8N1D6IaVGDm44CAiBvtQ3XHT716KpowcQIrOEkSJWEfUEIH0JiqLS5HuvHew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeLBCRA9TVsSAnZWagAAzmMP/2CSrwxbDrdODguP0efR\n1Orrz3t5XIgvsok+JGFe3QfEIJrx7D5YHm4GzKhU90A2PSyAGwd0gyjfp80R\nJAZc55vhr+FcZZ+HbCaq87Q9OZRIY4tRXjb1wsDwPeDe27U78JcdYyoaCsPR\n2RVdzCLdxhlptRvSqBDDEjNqfmXTF7crltuKjS1yjyFekaSlbOYg4rwsU0MI\nXZ8G4W6ILpVRwhYAISw8ZXmKxuOTN3d0X1U+34PKEqdy9ItX4k4Mx/P8tZ/5\nzZa4YLsHgWO9EsKsgIPU2xFH7rZ8e6DLI0MPwdz6a7K10NJXX4JvLZYbVFW7\nAU9z1sWfrhxGAJID3H3pNwCk7HLzycKOibXSY1MDRkfH3oUglVr0YcBOKLxm\nmVJoaDat6riAMuE5ZsXrD6B5IApSzwOKXmA6bZuH3nqsFZJctQ9+4JZJqUjM\nu/QtuUN9Eo/gYi57PKWdrMsvgZBWsux0YuFif061GRiKCR7H23ox+UCJRCty\n5B/Po2y3pvuAMmGXBm90RXKBK39YysDyLuRsYQx3r+bqbWkv69ZTWr3+oIDC\nwKS8/SwmpqqtqWijj3T14n2NuLIYaoUIPkKG9Kbucg7Dr1DzUi0FBXQv8Nw5\nLQKuJMMgPVrjffpRvbul07nZChWXvQbSajJqgfIzNROeV6CKHPX7UZ4K+ZJ4\n/mQu\r\n=0kmB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.4.1", "@jest/types": "^27.4.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.4.1_1638261441389_0.7693801790191026", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "jest-watcher", "version": "27.4.2", "license": "MIT", "_id": "jest-watcher@27.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c9037edfd80354c9fe90de4b6f8b6e2b8e736744", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.4.2.tgz", "fileCount": 24, "integrity": "sha512-NJvMVyyBeXfDezhWzUOCOYZrUmkSCiatpjpm+nFUid74OZEHk6aMLrZAukIiFDwdbqp6mTM6Ui1w4oc+8EobQg==", "signatures": [{"sig": "MEQCIDxo+muao/SrwA8aOvbCnrvcgeXFqq9AyZxvPDA7ZFeuAiAwoV/1j32FeGwR/bHUb/KHsNfAaIJj3qEofa82bV8djw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDVCRA9TVsSAnZWagAAU80P/A9ODMZTwFbDrLq6q05P\nne8+/dNMDMhO76N1nzR9+xC9cVzgtqDnedBZlO8EDSfuzOpSUxwTp+iAKZKg\nzP2wtBBXn2L0yJdZRKHLAK4ZtBZWHmnDhl0q9JMC15DI74xYX7WiW7Htt1GB\nI5GAuPAIMkSV/0o2hoH6SldkFWDlSUHaF2dBwamjrDw87nk5vbl58nHDcilU\nDa9b+pl7PAJ7zlzaWMFpNFCYOvH8LAaFvvEQU9KL+Y1Tio0VnqPAx3AWbayi\npL//ZAV1uPeuRg2fwe3kjdEboGcMFWY++r6UY7X8AlfAitn3S9LIjTq3s/Oh\nbild/GS3XCmbuGp4jy2NgMKKETjt7ZPWcJ7UEqUHWWY1SPIIj16R9sTwuYg4\n1oJPkqMu8Qduitl1k8H2BjQ7vUtxRv388kIOFWBR3oc7c1bCWG0kEIqhiKGz\nngpI62+W2tL/K1LFrZZYptE7jB1uWe0LloKKN92nuQpqekxcC5LqTeyoCRXK\nUfTwY9MwVsWRjzlNBzHXA6aj75PkKvnhdBd5JOiafFkteLBEoPLpVD2WNE13\nZJfDpVfyrzxsHDrjNd7Skk9Ze5IJ1Dfgfwx4uO6FBV4CTfTz/77L5Hyaf+xt\nVGI78PzrRjZYFXj4DPyvvSkbTYBk5ZAnEQZOLukGqNipWueg8MwNjQ6K5htu\nwLUL\r\n=rENB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.4.2", "@jest/types": "^27.4.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.4.2_1638273237579_0.8768903765451441", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "jest-watcher", "version": "27.4.6", "license": "MIT", "_id": "jest-watcher@27.4.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "673679ebeffdd3f94338c24f399b85efc932272d", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.4.6.tgz", "fileCount": 24, "integrity": "sha512-yKQ20OMBiCDigbD0quhQKLkBO+ObGN79MO4nT7YaCuQ5SM+dkBNWE8cZX0FjU6czwMvWw6StWbe+Wv4jJPJ+fw==", "signatures": [{"sig": "MEUCIQCxv74q1/JRV1W+7beh/bkrE3HW5jdBYN1wn1+xbpT31AIgNTXvad7OVbcLMguzP9VSmlW17+zBSAv6mn9gs3zzCDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJUCRA9TVsSAnZWagAAzcEQAKS1XILd9Ri0Ro2sw+Bm\njDVExi65G2O253Rf8GqGdzZZVs5S6Sgqf43NBI8ew0pmehLEzfdMfozE8SY/\n95KcNtJOjp5Q0MizQibsQwWfXRVinIMptnoIgDwg5XCtSwF8yu1YeLpSGYc6\n+BDDOTnBihqVkZbG8VbObY9GSNvBei1GGw8J2ZyhRIPubZLTEHfFhWYEV3ts\np6GyEyGQex2hSSMsBbs9YeCVP8XXVc9chIjUhH5kVZf34FtXnRbMSRkPZg4P\nm8+LlwAQNEyqMPXaB2B/4sAK8DSc0MGjAwtcj2KfNbjxVRGs5Wxr1w/FwemG\nKkfFgcfG1lmSVOZGEAxHlW7svp+Unm6g/U1a/0Dem/VdLDa9sbUbe7U7wbiu\njY/60UFVNpEXXDZue3cwRfVgzVkjVEAiOUaKqXYZRko/dMd8RLN6Xx3/UFc3\nyusWOgiL15dnbf1RvRDofmUul9tTtIXFRmAFBQlG2X9lHOVMX269QPtBgeg8\n7AoCYMUTP+luGXEW4bind0Gd00sJgq38AMlNs3czmww+WqqgArbdF7BM3onH\n/XyG485oCNfgMkVyqMAA2wiD9R2E0eDZBSb71J7X8qTLMOweEOPjtKX1ABGS\nRMWr9mmCddXPO+Rmj+P7DMVPV0kCowtM1qFqMPj6Wp91VMgnUhv3fVuDpsjQ\nDXqa\r\n=f04J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.4.2", "@jest/types": "^27.4.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.4.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.4.6_1641337428631_0.23451738864952176", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "jest-watcher", "version": "27.5.0", "license": "MIT", "_id": "jest-watcher@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ca11c3b9115c92a8fd2fd9e2def296d45206f1ca", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.5.0.tgz", "fileCount": 24, "integrity": "sha512-MhIeIvEd6dnnspE0OfYrqHOAfZZdyFqx/k8U2nvVFSkLYf22qAFfyNWPVQYcwqKVNobcOhJoT0kV/nRHGbqK8A==", "signatures": [{"sig": "MEUCIQCopdY/HkxYuF2EYsyiLQ8W9Vx5kX6AfkBI787TYx+S8gIgcxx1Ux5hrBH3c8zHeD1jU0QDllhP5uTJHglx8xeD1ME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kqCCRA9TVsSAnZWagAAT1kP/2T2TmhDbRAUn4mK7WD9\nFGpLUrDmJh6cu3aBZzugt5QA9+1LTxvhhw+EHEQkW1JT7OCfsn4Fxu8tP6UI\namcn05UlVteCwLXLp4ZluvcQ7VrZmaO7OPNNThesnMPiIOnC+H08P69S/kN5\nTyTXQFAsMOW0DzaxK9QD2gWfSUqOr80gZiWNZF2LaRpcd0e0aZrNbOVjFS6c\nRaZqr/ZEniSrdzJU49h7Fj3gMa5qdNmyZXbyOPgUlqN6Js5C/dAWH1GbpxcI\n0hdud3pUn7rrJ4quAJSfbvEZeOVQ3tu5mi/2q2cF7G1BMHAnKKTIUDHjpTZ0\nekWHTclHDdf9T5Y1NegZpcvStjHjdG3YkGZ/wV+iIrJS5XpsxF7BVJO7Z2vx\n7zdGSj4AK97EyxbH51VV727tdT3GNczgupYIHxIt2B2CY7fmQUNV5sLdkSej\nqxzIdYjVZKjgs46H4PodKHQaTcrp/AxFcNB37a4CUnValp5iy2k4e3X4Jc02\nieY+CxqtVMa7FtbOy7Fyw8AGWIq0ObrGmgwg4yyhGZHVK8qKwbfpUzgsqzaN\n6Bn/L6LJSqlbMSkNQCg8tYaNNHmh/sQdgZWHR1quGMobT0LYzHlPvihnMvE8\nvaqsnVLGlt1n3aGJLnoWRR3WPjMNCc3QumEtV5UvS12hv2pwnZDfP8/af55Y\n0r6N\r\n=2EP5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.5.0", "@jest/types": "^27.5.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.5.0_1644055170530_0.6482815920931826", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "jest-watcher", "version": "27.5.1", "license": "MIT", "_id": "jest-watcher@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "71bd85fb9bde3a2c2ec4dc353437971c43c642a2", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.5.1.tgz", "fileCount": 24, "integrity": "sha512-z676SuD6Z8o8qbmEGhoEUFOM1+jfEiL3DXHK/xgEiG2EyNYfFG60jluWcupY6dATjfEsKQuibReS1djInQnoVw==", "signatures": [{"sig": "MEQCIH6h4K+V2G9X2tb/quaZ4p4gwAJICaqdFGAYbByNrDJbAiBqSHrJgUlqf5pTmcoXwvYEzDN8MeMA8agXRdVZkO46Ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAkttCRA9TVsSAnZWagAAyg4P/0qd7xk1uwh57/fIjA7V\ncQfTbcC3T+ggY022jLTjoQZiDtwRMwcYFJO5Us1mx9KVWjj02Sugb8rRXwg4\n8YNr36daIbtVF6E6+DwEN1KtWxHc7DFOORigMOfo/r7FrM7HOqRIQi4o90ii\nCUxRk6JaekootBu39RCoF9a8NLS+wTriq8HK/1DBE1F4GuU0e1oeWrdBM+zG\ny2v85WSwhg+lU9MISMP6/7W07RhXnnBcxROb8BcsqKEL3cSFaGonvR7vj5l6\nf7FyfWJ/IcPJf+PlEFmMyfl5lEFVdQuovSZYYzbI9FkgZD6oFIAPIDDVdM89\nljqiciG/rLSnSNfMXDQ2csWmnbIMQzFqGroMm4i4mvSFuy/1uqmN37B21Fjj\naLLM5P/l1NtA+lH02gZTnBfrm91tYkHF9OIfbhQAay01aO9g/ynziz0jsCNF\n1x5SBoFCZ3Wuz/Jd2uiHNnc2KXR1znfqTn6bH2bNNb0A9oyNShFERUg2ld3C\nsvpqGDCZ4qlVVJuNFOm57uGK6PxHWKTxBJHx8imG8YXQfUXut5EbmZKdcYZ8\nTJx9d0xsPAhx1rXCycYh4fezT7VnOKdwf6sdxJkSylFufCX8NRemyGyDhIcQ\nJBhvyzPiZie4bHypQUg/QGoFdm4odaHnTL7YEgq+DSlPRo4Z/17NQfJ3/NwD\nM6kn\r\n=JkWi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"chalk": "^4.0.0", "jest-util": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^27.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_27.5.1_1644317549790_0.6760643266145061", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "jest-watcher", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "05a00aa9c4bb6a6dfba376d3e61565420f426c21", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.0.tgz", "fileCount": 14, "integrity": "sha512-o/VIRU9A9FCsRRtsJwgg5hkKFbx4YuqAebQNZxleHb9NZjBh1O/b+H5B8G5ipov59mj/OpdHyx8AR4ivGzFDNw==", "signatures": [{"sig": "MEYCIQDlfETlkanlSDC5bXxTBrfbRR8G/lV9FAgem61IeD7UqwIhAPGGQDHzEgzJ37Li4D/f1Lbrm8o4NIgMDOREue7jXZaZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22184, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVbHCRA9TVsSAnZWagAATV4P/jg6zagRD5o/FIPlWFI+\nX6M7zeyxoYQkJJZAvSygPhX+SXGz3j6U1FJFLvrmdZteXovqa/ncIj1IlrZL\nsf83FsPf0y8PAzA17LoAEzxoYuXIKGEZgp36538ijEhh8TnPkmBN0ZbpLYHg\nkmcYgCM440eCPFBNPL1QURqSx2iZ/NcfuVvU5f2cn+EJpmwrr6WCj0/L2s2z\nxf7nKQEoelMdOUf+hpVxXbcgWHRLdDarptY/xMwx+RMQpQVy6RqmHRYFcHjG\n7sZQEMtXuWz89cBdXGV9iUbxF75BoHoQzmWpQt/7617vy6eRBZXzCSqeakuU\nAbZXswdgIx8akw6i44ohOThpzWE4Y2RgmOOKQwxquI6bVNm/vkzHOdEAnw3t\nXX9whePvSJSlViBhETp9mOIekpYBZ1W18iif4oKGdCFk4uv2kEMdc9+GP6ZT\nPjDRsDQryjX6hQ8ZqZFzksFEz1au8EOJtCr2AGII3AChnSZuFn2p/QHAKvBw\ngwTExIU8NkSOrf28DMyazPAc0x9T08R2k8lpPlp2cnvfZkSiftl7o5vbEs2C\nkM8vddwRKmQ3fqTxx35+Iqj5p9XVZRu8UZGO1du6xBAUZMYa+nkVMEpHIvGI\nTxWnFnykFxU2fMq8tnr9k1Cq+9+IcpgNbeNLVGy6SVOO+znUnQWjtqhaxlK4\nOUTz\r\n=dNC4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.0", "@jest/types": "^28.0.0-alpha.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.0_1644517063276_0.4165450685286103", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "jest-watcher", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3dcbe4b10dd1a6f113223474ec377c2dc1426142", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.1.tgz", "fileCount": 14, "integrity": "sha512-LVtWvbDJsJqs+PCTbh71Y//Al2WpPZCsft1UmPXzd70eizk3FwzV6U0zNg+xJVa4cNoqRk5kkx2jg9yyV6nakg==", "signatures": [{"sig": "MEYCIQDETZnGigfhCGk2LHP5/eUQ3iU007z3EFu8uaJErQiYqQIhAPZ+XX10CXDVpIRUvvJ37v9CAcp0vFeJATQYuSfeWWUD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqsCRA9TVsSAnZWagAAlbQP/2HdOd1I3Hqiewn4UKPB\nq5dPzPQAvfMTgTJuq2owUo5OE++XBIT85uhykKg22f6mROlvEeLgJJUpqF/W\nah1hacxlgRj/6pGi9Yx7Q2X5vYtTUxWA4CxOVeC5vRgVRxgwV28y/GDA7kTt\ndKo2FJlSWvu8ER5vKBWe6Tpx5CE82VwBsPCLl0ASsW1eUT/sXcjDD8hNDJzU\nofan8jPPEqbd45rmPyzdffWpRsM48ITFVrQzf63AoiQtCRhfN5j66Cgk8rdX\nGr2I1TfpJbugf2429bLaP8pHPLG70at4t7P4+1L59pdgXlclN719YfKitq23\nYFWLC/eGQVb7n+nvvY+dFPCKyTTm/hzehFZAx+ATmmdV13bpy0AYwiH/qed9\nLqH+3gWVwvMHhy+bJL7BrpCTRi15Jh7zaHztemj5YPuyqlV4ssCOIoAf/JSK\nr0WER4ghA8V7zOVDh/uMXumuqnKwe93pXSJhvfSdmnOWOCQEYAAtUWOl80qL\nkfT/Z2LGzQ0Dl/WQCybJyFtnQg7Rut/E0FleMlruogVULnNZNOoV9Iyavn28\nbtK67Rdj0gpQCHu3EgiHFR3eYvgILHjGH3f1KSWtUWNhRBF55eO6Ceu1kpTX\nr8NggWJn0kRa/hA01LKYByxZAwHlmUg6PT4IY6abomzPgdhOuIF48w2jJ8Q1\n0soS\r\n=pL4/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.1", "@jest/types": "^28.0.0-alpha.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.1_1644960428789_0.6436480609765782", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "jest-watcher", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a6611fab0e24bc13a13babec10bf890a96b25010", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.2.tgz", "fileCount": 14, "integrity": "sha512-H08GppTqP/gHrdtV+WDRLHkfsqNIucK/yH2tFPgvGKtyu7vF7xx/rN5lCu0TJiHWL3k8VYUg8n5Sk1yOtMwXXg==", "signatures": [{"sig": "MEQCIGoS+TmRdKNYovgLVSLIQUScSk8J8AG2G1aiPtWGqh2oAiAZZsM6S1UqZvswZzDqm/2YxsOI6iGc+ndMHGR/aMa9MA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT6ECRA9TVsSAnZWagAAH5YQAIR+IoMcMMST41G7Dt9l\nC/TyyQ+LJeOhsp1usRPkslIM2sceCtMKOVunGY6rh7eBYgcXbTHX9QdtiVhV\n4CzT0ySgo/TCoda4bf/pSd4D/wHpNTj+yL6yQxK/AQdcYuB2ktImnZEKMFab\nBIkYqtbDBy4YJnoWT97+ZgFxyWlVxkiyigasY3yKUDCcmBEl+zUvyCvZtS7c\nXURXRi9egOuxD7AXIrmsPma1rnrbvams5/M0kT8Aj/+s3rXGNkDL1Aximi5T\nSnmYceOqJN0arQTJbBJnCt+QUV/kgTI0LoogWRMjZvjxwEM8ibQz3boTkOQe\nsChL6pyT5QHYJS9Hpv+PR3SGUYsBdTS5d51njcKCrtDjaJrN1MB9HopNYBZA\n4iQE6nUhsVIP9H+d+ioSguqoGPjuEgJ3gn3sN/HMnjpCGsPK/lgY+UhbAZLA\nUMjosfnhoQV3utQkwe9zo4zEvU4oSu32FuX13tocNVYzQfO2+Wm3XSXklZh4\niO0G2I0CHosr0f5RAV8oqdL2p7Ee4RVT847HzUHseUBAaoZfCqz6hoZ/iRWe\nzeJUurha/ygWh3/uv9kydnT2HqhdN0Gu8Rr6rTKWe1klGxTNkRTcs4jDvvLr\nB7klLpJB5OducEP0NnbHFhSU0GaD3sIKTunbBK4xTgFR/kU6si6AtSeIvd9e\nuFJ9\r\n=Naef\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.2", "@jest/types": "^28.0.0-alpha.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.2_1645035140328_0.18272871008652158", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "jest-watcher", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "49458bf2d991e83aca95b858d95bfdaeb15cf34f", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.3.tgz", "fileCount": 14, "integrity": "sha512-h5mtwfGE5p+Zzfc1hyaauQToATWiOGhAJOrh/oPo/+5Zl+GNLBjq5szxEPeBMYFkSoskv5YTTckI9R+e9roRpg==", "signatures": [{"sig": "MEYCIQDzWDQTIkidQ1NRdkYwohciUzvtE60knnVCKRXb8SOK6wIhAOWOijMA/eQp7ytaQhrL5qmCgTBGdV+4S9k6b+TPMdxg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosdhAApGYxQGYPxJ5gmx0Q1HivWpk7sHr+kuzXUcsetZ/ggu7JozdZ\r\nAhGDKHo75FM+IEq+avKJEDkq0I/ibhFAjlOoGRyo9TgYY07+EmOQo4vB4Gnp\r\npygvAdm9kqtXLo1e8T1sUeaBrsT9f77SA8R1SLTj03FIZDucTPSVibcHAvBW\r\nQ1Z15nHbgOPFQTrid8+gnt0RNXHnJhaf93R5vjDf3ecWjshlgjqnYezOC7TI\r\nYMtttCJSd4qSxRDIEiwJ50Rbhqbp17XMysa/ZvxyKMVHqRaFlfz1/GEwnKfy\r\njQH1JmBrDxQyQdFS0eBikXDFrS56NinR90RjEQmf4DzpSP/FFzdnBS/dNlyx\r\n3RCIZzjPKNZQJCpboifCDxGBlm9EZw57TvMDoFQziv0ruyKZKs+76nirPqYa\r\nK92kT/dpamliRMKsH/nA2SiVBKhX5VMAKAz7lH4jZOGKZdi2t4H/evO1Zbow\r\nz8Lb2x+JRd91jDAnptaonQh62Im1iTjlTbH43Zgil2XeagnGU1zz5hvdpbfz\r\nhyv6Bu7HZseOT4sRsS/52/LXed2CAyl0g+De6UvLS3ZaxUK72DPtZiMN1z2R\r\nZ7t+BdGQlESeQ4qbzpNX6/F3M4h4Q9KOpagZBq3n6iuo2G6BSkVduFh+kM7y\r\nOaiqbAJmc4gzUH/A9awU0nEgfTtT0YMWGAM=\r\n=9cRd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.3", "@jest/types": "^28.0.0-alpha.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.3_1645112547141_0.6734108072380003", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "jest-watcher", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "091f36328e28a1e8344563eea0ef7550fbea15e3", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.4.tgz", "fileCount": 14, "integrity": "sha512-VAuaGWyS+b6emMvudh3VkJJ+u2SY4PwNoJdQGTXzEkWJ6PeDVOnAB2Dx/qbSHxgzmu1IryhqqSpaDaL/r2u6XQ==", "signatures": [{"sig": "MEUCIQCfP2Nxmu7iQMRRomhozsbkosQoqetefEDvo8/5xLpC9QIgUmOaAGeU3wH3LuHrMlWij5oS/7xn8t58WA2XJRQ3/z0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSRg//WE0L9DzbMoLCojvV43UIiAu/UyOeoFDJcoDqqkAkzI5IVYBz\r\nGlDcsKK0tq2YcDTBK7Lla48fRGN/h2H3arxG3wkrrWM83kSgumfGcJ99eGxd\r\nL8d79I+xIWr9DTgoMvDDW+mECm3GDh/VYD7G4lLnUt9XBcJUYMJMmAvWaILa\r\n3E44SO3e/fJb7ctTkYltVV6o/L/baPaDU8Rr9QpRnDX6dyL4C9BzGxlmG86r\r\nMsvYm1tA/LjUKsdQAXq3YKkVsbwhmKL7BgQLa1HBoyPAzxlIn9ngY/K4OcKd\r\n+Y5UMOCqSCj8wiXzayZxZhMJrOUOpjVlpBMm7i9k0z1yhbT7ZKrmNtM4afZW\r\n1uohC0pTgw+Mi+j/FkdQLFLMgpqyS2r0JRev4+mBRJ7XuJuyecLznbuy6zFY\r\nQv3td98XjgZMMZPoeHqiEkQJEVCgd5MsXqASn1nc29CkOEC0pab4PmjUAG5X\r\nmv7DiK6K19vVHrnN+Q7M2XjAhP7Gm8yrijHQFFvjlAbE4xJcgdHrKSdYftnm\r\n/+CelBHamkp21p0po8ZFzxBXNwrdbQ4jeAcBVQFw3SUFt3YaxJRCub63qbOW\r\nel2nRIVQvUOFaa0POSKditz06QOWt/vUXqrbZH0A5A66GxUurT0ksNQt7OrB\r\nYfgVakw84lu4gMIljlBWzodApYjbPJx6psk=\r\n=E9AY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.4", "@jest/types": "^28.0.0-alpha.4", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.4_1645532038673_0.5756413850004702", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "jest-watcher", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "49d0648dd868af19ac65458843ee6d0166751580", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.5.tgz", "fileCount": 14, "integrity": "sha512-l7fbgln+EpvSOQK5I41TuQAhXfctWhQenX5IWvj08doauDPF69yhxSJan5YZhMvqUQQcH0e4MA20uFF0iCcing==", "signatures": [{"sig": "MEUCIQDqC1AlstxWYKnw0EisHMyBmQUqCr+qLciWQQZpQnvgvQIgZtkos4Xu2YFvW4jQQSJvCw+Zepd+E6mXaQdaDxXpMiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22179, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLOQ/9GAxiEj65jPFiL+afiRNQfnp/Xgu2dMfHW0pxiXE42nY4sIJD\r\n5D4UNxgA5LQkV0pwMKZyQXFtpbKfU1/dRYGLaCL4Kz9brNNnaZDifSafPC+D\r\nfao8TRBfRA+6g3VyobWfpwYImhKySYHGgmhskOaDKCU8D7owin1QJCCIbcxn\r\nt44KwSjvzVoeCzvNhdUFjbORYgzTOsQ/nS9S6AHVWNDDZ/Wx2rS8vPYW29qF\r\nsy7bmN/GbKEmNPpQcwlWehiYikoQJGiRVrYIGe+zAP2eE6o2meUYxPcXLBwE\r\nDgT+trNsktIlkD/5BT77UO82xpDZ2jA2bgi1EvgTmDuyGohOKgn+I/NTSuc4\r\nF4TpJqjYync7EmlbEPPHr5pk0rVC7jELEwrvKT2UyfF5lujWYUgA1z1SHqBG\r\n/we1hnl7m3vI/NGGKsBXeG7aiaWLN4kd2d40+Cv8YJoCWc6IS+zHaEW4qqJT\r\nBmQ2+RU0NVKDNQyk0qTFtUbxbO9UAj92fwa+pfs/+SkCZk30f4wWoEfNECDB\r\nX0VvlHXPEehWovqbynA6KikrTsXL/mGGQpZUDFmPu59Ck8lZ2yiNVFzPMmL6\r\neRyrv7EZIMJ0nABTCNlcH3DQ7u6iCgYGcVIpIL+Yf4uCSNVeMsNT2x0KeTOs\r\nmxcF0pexjah33t8wgsGiQ5XucQUQcSFqTcc=\r\n=Lu7b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.5", "@jest/types": "^28.0.0-alpha.5", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.5_1645736243822_0.6058824385668196", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "jest-watcher", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e3788bab3f60f40c99e32912b77f7da15bc583ce", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.6.tgz", "fileCount": 14, "integrity": "sha512-ZXCHizlEQWdVrq9u6rT+dU6QXh+9g93x5ftRM24kOb/d9L7OJun7IqkX8mfVfjojYw+U8d3gG0vSCLu8ClLYwQ==", "signatures": [{"sig": "MEUCIQCmkeUNGdD5m3RsWcsY3mzeL8vWjNg4xgWLT7d0jhnF2wIgQmCPqwcWIbdnXjsm6/HWJLNHi3W0Avj4ODnQdD+5ZFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdodACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpbyw//f30i0XV4zqSMVbwlQWQezO0wCcbin5YdPzzXfPMGlA4kOVPL\r\nfcbEPl41g8aVdM+czzXtf0YUbVjGhmveABsEEtzzr+XyrunryepBeFHcUjWu\r\nWHkqcQKDiU/7duCIPrAFKXuWD7Y8zhke3QijfXHd7Bhfawt+B/EcTy+FRlpl\r\nacD22Lfhibj2/CqRS8L8beNIveyYKM4hBqR97FW/VeOIACnnAeI/Zy2I2yS1\r\nIs2gu3n8dh+BYWYEgBDYpnmgYv104kais9WchEttpkhGmUbEYIH/pKuUGS0X\r\nrk1DaZ6GdQ1VR3aRdrFj6izTAV6wX0Xydyqk8HPBMy8CwsRFzbu+UXLeyBgR\r\nRRUqK1WBF6SPVCZXuz+j+kJB9SZwQpTlFx93BVL8NQb5/5yqgMvUPVCWVwa1\r\n+yAwDoezO77sJsk7t/dwiTBUOy1rI7pd8L8GOjO4wk3NygVtARaK2dUQa1tK\r\njnkaU8kAAhnlhUpIirnJ86BtGgrrlOvJ1oo3/BCgGX9KodDg64234VOVQvSs\r\npbah1BctnPH6BGVRe8eFJTREFpIwJN9rIOF208Wr74bNabsQFImqsfiSaRAm\r\n6gbzdDED9dsHoagpC59GEplyBTEjtLUCOq4/Y0MMB4KqleTw11B2ZjsLde9n\r\n1YpWpXoMAziHuCNuVWgei+eGd/wnUeruEhg=\r\n=2eJf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.6", "@jest/types": "^28.0.0-alpha.6", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.6_1646123549648_0.49406921130853654", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "jest-watcher", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aed9d02448e89517cd70fd1911b637e3255944ab", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.7.tgz", "fileCount": 14, "integrity": "sha512-JM02CcwVftglBHMHrZ/vauWCZ+AvP5pNq6RgCetoqxhHXBrcO4odvjRP1mW5fmq7WnOEW3lh19QQnFNkvJVIyw==", "signatures": [{"sig": "MEUCIQCSWC/yOHgnK0/1W7nTXtjBB4OMnowGf1jARttU/tvbhQIgBa9nBMSJZw6ZSSopbT6yF05pIHidVDVQZhBTbdLxVIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIzw//RWyDVEanHs4IqXwO35yKb6A9sUfd/3QpMHaPWY0zPSRmx0pB\r\nJVjzQazOYTEk3YfEV42VCjX5TIeMkPAbIl7TdHC7eGnn0tqLmmQ2PxZlz8d+\r\nhEzKfJCyL1ozoMIe9Lh5DK7Cf8G3y63TTS0549/tkezNcGSTpesOx4pB7Lnd\r\nXjNTfCn7S+h6gklBgEwOkF+yMK8bb3hI6a/NSIlO57X0/iYUVjoh2f0Sjdnx\r\nfsSyoxMzWyoD+dLy5pkh/PtXxT9cjH+oI+NxpQ1rdX8GJcF1hPZZo33iKWJQ\r\n2YqRLDSpELu+mYbq60yr/yUT4ttwcGrpoFmBerzQvlAoXknzHRku5DfkGBcI\r\nlGA6Q4nzBc5IZNg/A0PfuclznJzrCnLlabwwRbvaS2vcTLUhUxvrsXMid09m\r\nw7ezpmQSgMovb8I/JZrw+5U/UhsJJREbH6aBYnZcSDBF/66wudhl3PLKOKwL\r\ngNad9A7GMx1AmK+ACtdCvdmD7BZnAFyDpXt6iW9L81DJ2YdODeXurhRQ6goq\r\nh6zbi3t+kLd+K/rlRpDorqF65SztplYYKknIzoUT795JokSdfFP+BHiIk02X\r\nTtI7UhDLl1BZIrE6EysX6tihxRG8qoEruq5jyjgiqvRmVYsqpi9Yq3lHY+jm\r\nZnF2BKH7/u43DHtNiaLVgv0uzC58SPU5EjU=\r\n=vAuI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.7", "@jest/types": "^28.0.0-alpha.7", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.7_1646560964652_0.07741985767950421", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "jest-watcher", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ef60542d4bce7491df5f953493c70b0dd3dd3d84", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.8.tgz", "fileCount": 14, "integrity": "sha512-mXZdJ9TlWOfuF+/95CHkodEyoILqD2Ni5BcFn0k3re1NbKrQYkbDkfAPJmTidJiBVR97sBLjnvPBHgGEvgzohQ==", "signatures": [{"sig": "MEUCIFXz5GKdIg7A1ioG2CjH2shwid1wACPSGcjwztH+vytnAiEA16o0DeuTrlZasCgpka1Tc0pG2R3Rx9K6wR3MPrg2law=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFl7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphrhAAmMOjUjC22uWoHyOt7x/tOIlrkssDwrfKoAqvVtzDdMHp1u9a\r\nxmIJYIBf0DWO278+QCGEHzevw/YVh3Ona9CoCTAglkFvzL74MXFDWDLkJojv\r\nVC3cFDn5H0Tj5bLOAI7+G6kkAh7hxmnlUoPDYI53v84mt/1zdH/wQSfQvQ6h\r\nLh9s+dVm227vYGegCdioGkazS5LA4ft6ueNPrNMt/BxMIldin3SmWpZ84onu\r\nA33+DusJfOntTOsryrJ1kMp4yRP+0dA5yDJ0VTexb+4cbjjN9gKKxnNd/+G7\r\nYo9ZPVCHCWVAwvbd02micCCP4XlUW5YZgrUrRKNANi3ZyaboZfpTNmzK/YFk\r\ngWKjKFEBn2+MmwsXLO1rJ/h/6ZNfR5mI58+0U0l+bBdj7beS0JuUjgGuQroR\r\nWRdmDpqsYkOHLn4VguX54iEKM5JJ/OS8G3ViRxswfidRnIOEhrs3av1nN5fK\r\nCKmewJwF2pRuQdlifZuDryM8XSsVr+bFEUrRRxJ/Ry/VXofpcQNtQYxJ7Ggb\r\n3v3vdwjsYWBN7UuOM8yoGSoreN6ICyRDpmbNOt2bhtySHTmpnFQ8NAnCTeJQ\r\nr9+/p6gcayxXUs6vp9dNAXx2+B3TQHKi9NNIYJYCImLUDoUo7EMsEGh5B2cd\r\njfYOPKl096ovDfdOL27iHYFxLB+1EBvv2BA=\r\n=TxQk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "jest-util": "^28.0.0-alpha.8", "@jest/types": "^28.0.0-alpha.8", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.8_1649170811474_0.3071071244406083", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "jest-watcher", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "jest-watcher@28.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c1531c8b4d4378fcea7e53e33deddf4b43ad233b", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0-alpha.9.tgz", "fileCount": 15, "integrity": "sha512-kJ8+RzaFgeX5n5hwj3FTu1zuD9NLCwXrKjsO81atlQXjYfhORSzYmc6cJ8+qrrsNpDieedIYHX2lBPTGoVkAKA==", "signatures": [{"sig": "MEQCIBG4R1sTsr+A5gZYPPibhUPfr1+OSDkXfLh+UfHab4bcAiA3wYq9wa8Edwpynyw+wAGJ9QCHch7tOFaF7Oo0nhWl+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRyg//dYbIX5AvRSB2bvmZDxv1CU3JOjiCVfOKe7nBmWgL5uAMirJn\r\nhj5BUkZCqx85RTRL6yZhQ1AeYQw/DvcscnaxyKdVrrCQZZDI+6gqIKM+q1Tg\r\nq5h9fQxGKOnVX16RBce7YVhiaQE/BTLNjpwy/UjOIOHK/fcZALSkiDs0Dllf\r\nEWfpR/UR+Qke349URT8jx8r7a8hnsSd5wF1NAz+iaHWooCj1rJkpssqzhW9R\r\naqNz4zcbKZC7RLIx9jvvrI4kppcK6dOggLU9uJzssEfzrtH3EwFAHe6eN/F3\r\nrpz46Oy7C7ut+/ezm3VUAWAd6nuuW/YH/dUXYgtnFOv20TmBu2vBWgq370VC\r\n0Qm5C6ykPd8e7CUcbvrhuxs26BNz1Ju2YZoE6EtsrkSkJ5Dv7RyjrzBUIuDl\r\n1HC02OEMGumx+jBuI0a8ZOki68lLxTbpfihOBfFIvA8ch2hiBMJlYm07yBdO\r\nTuX3XJKcFpSjV1wKMzOlTQScXHXeg1upBc7JlfEk+VYX5PAFkZf60Wq6LksR\r\nXwa1m2OMUfDyh3mTa6ZYAlSWnhR3p13Sbvub2eJrH+oPKMqd5xij0x8r3Zwf\r\nPzEBknVXJP+8RiD8wgixmGYKuGKUqXAIUM8qjfq9sAAu8ZkWp8TTtZkPmHec\r\nYo/ReA2J5f+VnE6kY37wI2gcDD3LuNn3Dak=\r\n=TyNt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.0.0-alpha.9", "@jest/types": "^28.0.0-alpha.9", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0-alpha.9_1650365959031_0.8885600472137047", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "jest-watcher", "version": "28.0.0", "license": "MIT", "_id": "jest-watcher@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e57ff3ca7ac9256eb58664dfd2c93840259f94b9", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.0.tgz", "fileCount": 15, "integrity": "sha512-SOeze65Bvb6biK+gXqb2fa1T3F626AuM/z3fvISF7wPgKkCzqxPG6obkNJIzcISpWfSP4G+Pf5eNVScj1KNsYQ==", "signatures": [{"sig": "MEUCIQDTu2qm0fGtQ7QqACyvzW4ONL6+Vvgna8VaKd9MS1i1/QIgUP5oPH4HVGJyEANHGcRaD8FtbbwrKvtBC//3c2T2l8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrQhAAmB/uc72h1MbZH11BAcnV8npQlUIGGHIW+14aNv3S9iESV14n\r\nqhk8p4GnZMWEWirwMeRgW5OOpuGma0I0XvAxbN8twb1N/RHC1G9GaW5Yn7Oj\r\nwyJ/9IZNFACXVwzHBQIbvBJTW6dJ/ZXVFFbLAgqlwb1OswjbspglRRt2pG8Q\r\nlZqHzvPmmSzJKYUC5abYsglG7wkE8RYh2WmNNk+i0f0LzbvF5/MVkN75+htc\r\nAiLK+GDgEMYBBqnWUXdsQdsDveV23C/69TbthD5ljiJ5uZ46fuidK+YOZMHP\r\ncDqawNLtCShZqo+vCaL6sSyVSbdZseFVL2gPRCif19tnCnhBDz0ZA9ofbDmn\r\nN853raJY2ffPqIjU13J+Ggg8RCMczCHo7i4bpidPevzV94peEU07d7y3uXVD\r\n4jV8g51bNUpyiQjhYpixTXojOAphCFv9jsOEQx0d+A00CG9pYUITq0j1aMgA\r\nJDLuyzJOwR2AogSVric8RWxJPut3/Kmb0EaJCS/R/enWvSegFZ8QQLbKjZte\r\ndOKtimZul3K3Is9lBOGGNLH9byhZoSL+MEIpZ0zqvA6wZ1SV9c5z2/N0tWvU\r\n/SRSxeYLUC+hXJLu8T2qNJy8y22ggriTw7tqr3fKG9IvtUXGMQNsen6FQbXt\r\naGxz0MGYlq2Hi6RX7gWxlMaKMTJvUvXiA2A=\r\n=5PBv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.0.0", "@jest/types": "^28.0.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.0_1650888494258_0.8049308243072351", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "jest-watcher", "version": "28.0.1", "license": "MIT", "_id": "jest-watcher@28.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1460553913120ddfe3045fda7e80144819cc576e", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.1.tgz", "fileCount": 15, "integrity": "sha512-tKyjsQal10vBomcyn79ZTutv0N0/dSfYJ+WRFJ3nlaMejiDlLKjMGQ/QrcwcXIXMXQyt0tJG1ycmqLbJg5AK6A==", "signatures": [{"sig": "MEQCIDka/O/QaXOpzUj+rhjlBVP9wrOFbG7sjwTqkiQ1svK8AiAkx1Jtj7hChMZmmN6hZWR549zbn9wLCZydkjumIbGzhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR7A/8DJnP7fruwSVqP2JluozFvrmyx0MEGHI/MJuAbcrE7nRAzMJ+\r\nfg20ra91s0zetgAWu9XU1aN6XtfuOxhB++VvCqHUD/0LOXbkeQcSKlGFMLkJ\r\nybmAssqjrfRcfNvXNQlKY7IpOlyDwaqOTkU9Y+oC9hnrUIG2NadMgetm+que\r\n1zddDwCqxhFpVqhSFH5UdBA5Zo4LQbdASVYaNva9O2eAZEDINYsy0Gh9jOX5\r\n7ZJm16kNesrcyYNMk1f1w4fH1vs5C+ne7Uoz3I5hZj1FKWJPrS1i5DrZImZr\r\nwD/+rvYVse7lDScaDUPUwjnLDT8F/gRqkrBC1bVvnZc8tVeIEbeg4382YRTe\r\nRcGwq9QlUKVbM/7Eq9uAVFWRwFOlmC5NSgjCReR+a46QJaWSkIWqzHof7fma\r\nAxaLouVA/FgtB1GoCQq0dsRjdK8GEDaLLQEJ8JytoaQiT3tSe6myH1Vk/f4d\r\nu7D++2k005Jgwi6NfmaK1b6H9dyCO4JchkHxNcPbb7kBgqd32uOy2s15WjbB\r\nhjEh74JdD9Yuw290Obmhb4tg8yzT+squWc86tJLnZJqWDO8biTUlKnzI9RhX\r\nfOfWYof1K/Ys2YnwgAgXO4eWKMmVoIaTFHuTpCi717CGmzb19J/18SJ6GEMr\r\nJ/uu6ldiTRFRQd54DPAL9DSxnx+Kjdy8AF4=\r\n=vL/M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.0.1", "@jest/types": "^28.0.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.1_1650967363881_0.7460040759913893", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "jest-watcher", "version": "28.0.2", "license": "MIT", "_id": "jest-watcher@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "649fa24df531d4071be5784b6274d494d788c88b", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.0.2.tgz", "fileCount": 15, "integrity": "sha512-uIVJLpQ/5VTGQWBiBatHsi7jrCqHjHl0e0dFHMWzwuIfUbdW/muk0DtSr0fteY2T7QTFylv+7a5Rm8sBKrE12Q==", "signatures": [{"sig": "MEQCIBxGPM10B5NYUptU0h7OIxWmRzWSfAVw57tafDZrunZ3AiB0iMQ0rQV5QSeC9fYXXy13eVSawMVDpLsYi0+nejZ5/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHrxAAg90E4OCKym6UTfI2Io7Ot2iqoGBo7cBUcl3LqxwhAwhhmPDr\r\nMZ8d1kHoBE1gZI+U53JjgvA4MelUIhpXzuaJonogj/poJ+92PyTw18HBogki\r\nODiHYSk487eeugQWBBqXJLWj6l8fVO+pZAmia0R7ze17wYKCMGx3PyZvwBQ9\r\nO3AetbStLoGRJIgIr+QK8KT6R1ghMgWqOZcfLK5/5ODpNPxm5HEk44YFgA3X\r\nJxNUifhVIq5gIVZTkf1iQBDFpaxp352BAOLoUcbplq1GGa76WtH0+PugvBRU\r\nwPhBPBfn3o7Fu8C8uixXWDwFsEQT+/YSFpDaMGwyb1GtNu7rkizFPJ6K0rAQ\r\nO/aqjuwatM0W1RTGi+Js6OuIiwky2mcQgGMMIy9yA6HADGLMBTdwT+aEp5Hu\r\nZSEgVD9DyMgmRxkz7g017B8o38x4HQK7eOiiXxn82rBHyVmdsPp7YaEeBXCi\r\nZQkYntWxq3c1eG0GvU8jQINOz13W+dCuOUmvTJG6DYCu8EojKmS9ERC2wi+7\r\nB5eE7WXVRelulJ38dV2k9i3Y4Kla/w6jGNOWTUwcRr62KDKulWI5/7s5nFvz\r\nBLXQWmoOn/PXGcyVP1neKQrLJGwouEZOChAMVwFOohcuL/6c12ChfXWaaxWF\r\nIcG1yVXRt7dHj6fGNErAGHdmoX/uJ2SCLdY=\r\n=KRF7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.0.2", "@jest/types": "^28.0.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.0.2_1651045448510_0.755116849731263", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "jest-watcher", "version": "28.1.0", "license": "MIT", "_id": "jest-watcher@28.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aaa7b4164a4e77eeb5f7d7b25ede5e7b4e9c9aaf", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.1.0.tgz", "fileCount": 15, "integrity": "sha512-tNHMtfLE8Njcr2IRS+5rXYA4BhU90gAOwI9frTGOqd+jX0P/Au/JfRSNqsf5nUTcWdbVYuLxS1KjnzILSoR5hA==", "signatures": [{"sig": "MEYCIQC9ADypnmhpyeqiRdXl/tMK0xexFRVJY3uAmiOLFyJDqwIhAPleBWIPhzdWmkX1h1zJvbHHCDZdgKjIt94WXz3ELYwm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK1A//VSN5a2CQPAezLzrCjGLSlOrQJwwG4u3Fh0IH4AZB8K20zeAD\r\nRw2Pt3eciNc8nHIgLVcnd85B164pa7lt4EddY6faWNoJu3S9At2Jb7p2nNMR\r\nNJEod+Tpa/djTkdi/x2zOD7iTSX1RogonwXOGtNulAW0G4pso0n7PakC9L+R\r\nuo6jyOBBsC4pAeiJU5/VKxi4IyMY2GHPSaElHz8/d57AmRbx/YOMokclOKg6\r\nenAyks+IOcfpEG7srsYLZ6COhkBg2lSFFWyD6gDx0Y2l9gMfqv9/t8Nsu8+U\r\ngLYXStClHdcCT0Mz6RQZwU6q1dKgOsb1KuKEVRgXlOcwYtCrhJgo3hnfO/+e\r\nPx3SKJTyy708o3eFPLo8kJeWt8RRzmrJcDwlZr/QoXdTOLUe5nLhyB7Ig6f4\r\ncoHOBKZFZOTUqW8Qzx+1jOutDiqzcfUErXXET0VJasBE03981GTSFuEERV4x\r\npc5i81fi2OIlCNxQgF8efHDEzMDnUmRU8s16NRAFRXEwNtpgQ+6vaY/oBjCD\r\ntU8Q5/RbjeCkyeWbThCsA45fdjT1iI+kpT6/A46xJ1NVTvHTdr6WHd9QJVmE\r\nk84bl9ngvBc0V5lV2l4udqJDXXOWllsiZUhjGuT53tLU/85IQxbUQJr5KUIO\r\nA7TBkI55moxq/8xPPG+D8VHWZoZMWM578V0=\r\n=ntzL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.1.0", "@jest/types": "^28.1.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.1.0_1651834138217_0.7820542563421433", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "jest-watcher", "version": "28.1.1", "license": "MIT", "_id": "jest-watcher@28.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "533597fb3bfefd52b5cd115cd916cffd237fb60c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.1.1.tgz", "fileCount": 15, "integrity": "sha512-RQIpeZ8EIJMxbQrXpJQYIIlubBnB9imEHsxxE41f54ZwcqWLysL/A0ZcdMirf+XsMn3xfphVQVV4EW0/p7i7Ug==", "signatures": [{"sig": "MEQCICCWt+Avagu5pzX1DinV0cx8pDBK0bgPaEQMYiBbpS7GAiBL7gR0R4cHTjM8Ejh6WmjOR4IHSgsYxbURe+DqN1PzEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuukACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJMA//Wu6GJF+87fKAy6270Hz+UksQ6W8L4Iv+94das63LPgBQ0qV1\r\n+5NpN+rKZbYaUNuhtUhhj11o/ppzAsmZZ1bBOI1zxkA7yD1x/t1qCduEpFdq\r\ncQEyDZdtk/hY2B4P/ubvSEzExW9pNsb6SjlgJU7sniYLAvkBE3zu8EZ7cZHo\r\n3E2QwrWnIL43oleIwM0oBTHnT8TM07JOj5K5T7kTwbQ/vxe/IDFyfgsI/Rm6\r\nJpLOUGBHO84l0kSqmRl8cqx/RxsXcakUpUNiwv/kBCCkgjzPksiXmfLXkBzT\r\ni+oIcgj0mRdj0Ao07d9LblpfENrFBU9XafZOtex+tjepllVf2vMrk+gEkTd0\r\nzcprSWLXkIq8I+WYi4dWTerlEWfPQFfhpSA7KDv521fPJnHtfQUnjLzQlP0V\r\n97F9r/G49WPQWs+G+3s6XK1/b3tzJ7QU0RNGht89hZ7WUCCzWtRTHTTcAJFb\r\nOSa6gqCSFYffL7lkuhNTKVdDIORejo0UDjKAoDDM5tTLrwmSxAC/gxu8W//p\r\n6gOs+6YXPZII/EVC44FqgCv/xnkKVGzbKj4LvjrtrOBE9KrcNrWfPz8a6FE0\r\n9uC0de6CQOsWifEnT+O17yq/FjczkIiwNzQ4flTrsiGkKL1IeasnXCE/68zV\r\naaubDMHGBduU5v32m7RkYHXN4zeDLjaqLe0=\r\n=OD6K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.1.1", "@jest/types": "^28.1.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.1.1_1654582180267_0.7621716066593744", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "jest-watcher", "version": "28.1.3", "license": "MIT", "_id": "jest-watcher@28.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c6023a59ba2255e3b4c57179fc94164b3e73abd4", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.1.3.tgz", "fileCount": 15, "integrity": "sha512-t4qcqj9hze+jviFPUN3YAtAEeFnr/azITXQEMARf5cMwKY2SMBRnCQTXLixTl20OR6mLh9KLMrgVJgJISym+1g==", "signatures": [{"sig": "MEUCIQDDLdLs1gMopq8df5wJ4FY4SQpg4C0cblOzx2TFtimW6gIgLN6PCbhJn5gLQyrpGbWBhm0D7KjxkrzpTYYKanwveOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWCxAAodCyjLgmki4nXLCQpFkgUx0VJt6Tb8xBp59TAGpCR8md0phk\r\nGFUWTJ6eb/H71xjDAdIP/R+Pf336hZrhblU2R5vJvD/hBezE0ZVJGh4QGnBX\r\nSq5Bl9GsrVGAOY9rnXfySQgFVyWinSpzYSxzuBIedyahWLZ4ra7zaVRhSDQo\r\nuaXyl+rFgfnsHE2Q6qOFedc13M4d9W9J4rBOibce0O3sJooPUuSdPCKLuHVj\r\njEymDwo+tKvGR1SXTKxklbxYldvwE+St4aYahPb6fE0E44ywxhg13KcXRkzP\r\nWwRx4K25H2EPi8JIg17msQthIGhAKgSpkCuPvL5rbq8lET9Izsqn8lGAGRRg\r\np8BeTHRIlZC+Ul6q4biTwF9mUYKLrHUtVpFZJKB/M7H18kr3BPmXnTWgEBp1\r\nQiPwWY9iLtvB9PEG25dxjODnBjEGI0QEKR0eOiKzNVi+QdYIk4CW1wh56Yo0\r\nkVUTmNcJudorVbjhCVbKQikkaKzzGCPKujsk24uBGWWOacbnsTg4SOTVfdc1\r\n7lqhzjyh6TIJKI0vj3I/LJE4dJ/xH9ngcIpSAAvXqyPP2JDk0bBbVh/2Zeus\r\nxkhBBt31nuHW/2YeTNnSiFReC/sKQpsh4d38TXmOOUwsZR0q71x8u5/P3Ojf\r\nv+4w2hI1t4slHsS8iUAdZSzjEq657v/mtyI=\r\n=NIv6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^28.1.3", "@jest/types": "^28.1.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^28.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_28.1.3_1657721553112_0.5012414498132844", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "jest-watcher", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "jest-watcher@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b788f7c64cc4a1a2d2dbf1dba423d555c384872e", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.0-alpha.0.tgz", "fileCount": 15, "integrity": "sha512-gmZW7XtofMzekMkF0sH3W2EXmnDV06FRkiH2NrwZKBZY/dbXT7KfBeGkU8FIr0wB7x1Qvad4ot1th5bONBTmZg==", "signatures": [{"sig": "MEUCIQCm95EWXck0Pn0ke+ww1HG+d5QIIdsAQ72ibLTxFdWkwQIgJs7DOtBxBXU0iJ23v8f7XGlxpGA3QQBBZbsuG25GzMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoABAAjzVhV8wA2+/1eyroUobqOdMWvBgQ21ore5yvm///1gctFYPm\r\nRCtviNzZ1/OLn8zhdszAd2GLWuHkYhTwdUrRyJrumNJxyfHK//NPj0zF0x52\r\ndvSMm5ND8UUABl6WbgbhX5G6DOnJqWKLdx7Bb5uBdjVmLtoOOoz7HGcFTCFc\r\nKB+hLoBxqasU2x7FltBmw1l1HqFcAXoZvsy18QILCiVreZxgYX47EGx3d3jx\r\n7yb7OlncffVsS2v25+hmopV2sQLqCBkA1S2d+v+UKtJlmIjC7GkadvpER3MK\r\n77aiFaNQ0JndJ+tqFuIYUcenm1jQ3pL93aEFqdUKS44be1baGJokbYmuOsk9\r\nghcq7fj9VUDZZAmrzKvE4/HKa8HO0GFtU7WqaT9zaQZsMU9vfmMBM0cYCnEe\r\n8ORx7U0F0zlxGVLji8AWX4KXnMIr/snmD+uSBxjqxYC12IcSI5+FmaEm0Hs/\r\nGutRlgsSf1p4MueiOPDB3bAdOOQsJPw8cx/T+IiKk5GWqnSXPjN/wTfmS9sE\r\nW/Iek1tae7ah5P8rDBcRuj631jmpFrfUJ9hY8UXuBBeXEgknAMzSO02dEDCc\r\ndj4ovQGiMcPiqqNWn8aAlX4t9tZ0L//CSBZ5pCpnCHpenwIrI/XrbcJQoasE\r\nvbh69Q7LhVbWwzbmrTXbVUjHZZKsMTof5C8=\r\n=mog7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.0-alpha.0", "@jest/types": "^29.0.0-alpha.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.0-alpha.0_1658095631426_0.9636327466858037", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "jest-watcher", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "jest-watcher@29.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d4c1d5b6c21dedc2e7e5ee2ac7d31b87cd1eab49", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.0-alpha.1.tgz", "fileCount": 15, "integrity": "sha512-91mAhcQBxiXIYYQgZcfHwKoW0ghqnqFmrRlmQJBnVPPa/If+bnSoI7XNsdEdyVzofWKh66t62AvrVSG8c1xJTA==", "signatures": [{"sig": "MEQCIH+mEhEAgUQTmqMgGtTfbREpXauVIkjaVVVrXGS7QnICAiAdwW/KH0jOBWwfhOu8nQKXSwqVMlXa/dlOOf7XKxmc8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPyA/9F8V0Vn/lXoJqX+l5YbDjVEqwXq7v8XFetyvtwZfXcdXxloez\r\nuthZY7NOI14Kkyq4PenSlWgprtXizcq+io2KKnl7Q4x7/Orws2nPNRqCwyUC\r\n1fVMz9RxQOHdIdsU/7uw5iQ7ezs7P15Oh8uVLCNVemxW9DgvRebjleNyS7FV\r\ncpf1FnMbdNoOkRCFFbsWF/DL90ABGjBEEdUhLeHMcXEU3iZB7SwHHEHuEyWL\r\nMQFf33MpMSHELPT9wN7cqnDtPKeeGmUya7P3xKfbQ+xoASCnzHgIUhGAEQGs\r\niV2baMVDjDUmDYKNbqRVb1wi7AyHiw7VlTKdmLLZM+S5xVdrayCJaBB1uMc0\r\nG/C4s7CjJVPYY6KnRdVyduYlSxeAhc2kE0HwmkE2NIo3xTePh4PZFVvkdivE\r\naMQ5LyDa2ou8nPW6wC8HFMpe+LBICoHssz2bZZ5Sq79qq+bfI9KiIqC9K5FR\r\nRqXOTrDzSvAYSGAiTNTHfCYmxvvzWJLqI99t15DamwPQo8fEj1pV0zmpJp58\r\nCueq6PbQfEjIlP9xlOfhub/pHOZc+zAYv9qCTNnqK9dgJkK1F39ZA/0OcqPB\r\n3AOqc/021YNb9pTfMe1ed4iaQGLLMjX8HslPVS/rfWVZ3TKfnS2Psm7s9Ssz\r\n//l6MARQ9lWwQkLpjW9RVR2Te+rWy8pKmJ4=\r\n=pjSR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.0-alpha.0", "@jest/types": "^29.0.0-alpha.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.0-alpha.1_1659601413616_0.3266295134469568", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "jest-watcher", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "jest-watcher@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e8231c6c6ed4c01d9be8966a3f38c6d1546322fe", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.0-alpha.3.tgz", "fileCount": 15, "integrity": "sha512-LJqiIGHbC00qz8RlEkRfO4EEFeSSLNwIdXZPjoJJce8Pl5d15ZHdOKSY9OX5Ed/5ydWDHCYPN/w07ZBKEAOy/A==", "signatures": [{"sig": "MEYCIQDzqOn0+qkYUiMFCEm8Sf1nV4VhaOSj0WorZVT6FEQMXAIhAIHX2Q5CYfh/MQl7B12s+ZfVmey5+QK3BB5hUvh4TMLV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ETACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzPw/8DVsCjj6bKYat+QTyT3+My5nOQc7fZN+/8Ck8wBGrQF04LoW5\r\nA8aGlphW2cWhxVnSPmLCUSdUMob7DkhtofCOfE6+wtDkLItGs+EeE4gxre/2\r\npYGuGoxXNdz+DqdlAGIEicukDzY1DQFNY8fMcmlTtGwOPqob90diWAcoNyaa\r\nrOx3CYBSV6+a6DaMrmXtQIEXKKZw9ilZg5QccoayHwKlVohGi8ZV7VnWIKFw\r\nsfmkgzmLnvI1WIqG63wEomKtX3zUL2SWH/KPaOofj/njnpG2aSO2pbCe2XkR\r\nC7BH+G9kzp8HqXLmeXIf5YpCfcGNbi7LBWFWRS82PcSeNLfIH3eq0XZ9/Elq\r\n55VHrZMdlnnsjqeU8P3ubISfd7GaGXz7K4k4nkjT/eHt48c5yVL5ZNP72UrG\r\nw6Iqp4eE9o4j/6wx0kuYSanqfhkXXrHes1riLM7d1G1V/pXaRW2F6/nJwNdC\r\nJsRpcnNY85Pl8oXwPVFJsPyffQn3gUmctgSF3tihLkhJTFk2JilsXuG4vYqf\r\nISo+/CYIHI6BBTYCHllxMjIYG3SuuREGBxoh3BJDJY2RsOKqdy2+YiviQCtx\r\nm5+7jB8d8fsRQ1APDWst1STzu4iSXagaZEKJU9k7hVlYpnFPoeiaWYyu3mSb\r\n/6UO81MFUhCELnglhah+K0+sDi4J3Cr9ClU=\r\n=XcGm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.0-alpha.3", "@jest/types": "^29.0.0-alpha.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.0-alpha.3_1659879699680_0.1070238251379918", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "jest-watcher", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "jest-watcher@29.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "72c9f72c53eb09cab1eb3a64cafc60563ba01858", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.0-alpha.4.tgz", "fileCount": 15, "integrity": "sha512-s9szd+N6l/kqb+lMaSG3FcLKeg0S7vrUXsfU62LuRexdl4daGbqMpjaRVZ4fCN6owuDuuQLWXjtvLYImN6ZbMg==", "signatures": [{"sig": "MEYCIQDTEVA7v9kp7JKZCggtQJn9U8BHoCQnql+mWZZFgshusAIhALCUsEtzuQfdqH7tm47r+usYyc2dQEI0VFH8elhghUXF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QoiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfmQ/+NsF184kucUIGruWkHAkXsmYFKSfYEG9/8Us3r22kozfw3JLN\r\nEexEOThQBCNoLZHiGAcr5FRDEPLl60XluNbFr+eA3btez81lonBP8jb/RnDm\r\nDSLEvvZJqsouVtkgwBgtBjqmGwOfEWN7JPA34iib6MEn2mpwCPCHgzlH3caQ\r\nsDukM6T3EsQMztKCjG3q/PGFv0IljPEe5kKSqRiKEtYGchk+mXecwwWLutot\r\nZwrB8xFHKR7BvjYIsNRqWcBq6EouDgysO2vPpvx3ZKIjciNISnXcl0/ktsr2\r\nn3uRru2B0N4Q/R8/nJ0PX/V57ahmgRgZxPkg/eelweiUt/YNucE3vxAbTrvt\r\nQiAEihSbaJk3nJaN2RPRBtbOMS5wVOr15v7koYYT0fjte/xqD3CeiTx4rQyO\r\nHKgcDDjxktV3uHDTHFlStQPAQrNFPw0YrDpVOoSLpWXMl/2entGuVB6p99PY\r\nAtsEKohR3ZEhvvganWlbTEyT/kn/SBe4gMDZ9+eevZPR6SUKhZz7OzKFmzUp\r\nbaDMNUNd1lG2q5TX0zjTpGguY1PHNmiJAmKHUa19OC/vI6/bIGl4uddUvlZq\r\nEPCJYLm3O0K8F510M26Q1B6DiAL+RNkJMfU3G/7bVPxfw71paJeN7/WgP9wa\r\n66itYFC81XBEwSDjoHBG+HPg79X87Iec0Vc=\r\n=qZMa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.0-alpha.4", "@jest/types": "^29.0.0-alpha.4", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.0-alpha.4_1659963938541_0.7034371080423454", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "jest-watcher", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "jest-watcher@29.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "244c1577655fbf389f390653b81f373cc32076ae", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.0-alpha.6.tgz", "fileCount": 26, "integrity": "sha512-5HzTCBIXnINXaL5XsILwT1R1+EBjPPNUuKNaUfHoOO+9QE/fEiSEVQl/IrIkHB5maVgklKw1O9BRNtbaBB3NCQ==", "signatures": [{"sig": "MEUCIQDsRzftsfl0/7Od+Sf3RIogwtcQ9epbIANr14ZQtcIXXAIgOfcfjljBkt+jt2e6AWxsqt7tKALcEnEbaWFvUeRmAxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmovjw//U0I3XPnGyv+1iVMdM94MZH06yac3hLxiI/Rt12NgcQvRoxOg\r\nAaRXgRc/j97xwnQLU4wGYf5I2b6p42RAvTe5JdP9dW5id3xsohIl8I0Em4ko\r\nG6H8iojtsA/43uZYJb1jINEXa5jX9CDM1LEqkvLoyuG4aXBZEBHEMnFlS+JT\r\nlJzweawjalN6IGBz7LzK/hnOcT152QnLUZVjWwJGrrlHG6HNQs+JOv1uUM5r\r\nZQSaQqQiNphBw6mBvzRIjEW3q7c6pwoJTxfPLqKcxR5rrcoAqH1xFr+XBqt8\r\n1UH7kpI+q0M1vdWtBA9vyjXBvN99bnjGkVKdLgipruHAdrrXN1bktB2r3j6n\r\nt29WnZGIiw6UVrOY+bACL9A/mPdOBYEV78we8c7rtj5w8QFxDsBkAFlC4xjD\r\n74Y1uRwpHEAQvxREujHcKUIu4etuCEurCfO+yEaFCXtqSJFSd1NF8YPYzYOn\r\np/XoKAm1Plz4Aikq068uqDVcOZRnKPbgCCxR2E/3d9VCIWIl75an2anzK5wa\r\n0TqZI+gZBuHsZ1Dq6Wx31WotslbnBwpKFcTFooTOdW4HkGNKxLqKvgiqkfbP\r\nleGy7xNZOnXQ6aQu0uoSnfAK39ueEBAl6C3YlnzxlqapApeEWvqSnSRVMX7c\r\nqF4V8p8HIzhJG9nxNsNq+h7rjORAPUluaqE=\r\n=B0Bc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.0-alpha.6", "@jest/types": "^29.0.0-alpha.6", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.0-alpha.6_1660917472910_0.1325431821622347", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "jest-watcher", "version": "29.0.0", "license": "MIT", "_id": "jest-watcher@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7ecdf96acaafa0d1afe4be9695f3c81978afd20d", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.0.tgz", "fileCount": 26, "integrity": "sha512-GoRq5QJt5/dv3keK7rIzg9R0e/HpTnjyMNYtCTTDZgGIj6QUDMpiJqt7Mwfyyaxwg5PS8gVyQvRQn6Lril4cuQ==", "signatures": [{"sig": "MEUCIF0afG6622NW2TZLcT4iDE3CMA5kcwOutXkJop3VlF92AiEA/ZLGuIZNR5VQM0gvr6mMWY8zKRmg4y1qjEYpZbD97XE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpdpw//W3V9q3MCa0ULXnxR4LvQnP1yYm9G/Ga0sA9PmXnqXYz4B/fq\r\nncEvfgRsN1lmqSW4lKKjGW+i1+NjzS33AVJcZKtM85PrbRcr6DHJqeGMww6y\r\neYZn1BVtgFLaTsAdlzT7i6Fex2ZiSawsYUwIwUhHuJg9Gc1E/6skYEv6Q/WP\r\nZ8W2i/HOU8SLAZ2KRtfNPMfPgUvMLPahej/TSEotQ88C6xaiW9QVWcLj2u9a\r\nIT+bNCw8jHyEAwsD2evFlA8jO9HjS06vsekBEDLtGYnCzsW+OG3MEdoNxcJZ\r\nzhufcpSfNASZJiuET6Jhq1gAwjtSEeTrc19YpAdIiHGEa4ia/7U06mkBfCYM\r\nH7fPOlTUw8H7Nu/Nf5eAfdPKQaOWhqRvbAva4klcmFdSwTnjBRmi1oDOvutF\r\nt+BNlpTT0Qqt+r18uZ1P6UifvpHeeQ/hWRuGnYmzqzPsREtqCE500zCO6kaf\r\niPOj88XHn14BQ5Meda1Nr6+ZLxuzuPAoPBkIJ7+hNjhPGIHCeecmS/2haJf5\r\nMcmHRWsYpiGulf0j3W+v/AquoVS2MWCV9mZouhkbImAWKM00V1OUY4bGPcBc\r\nfh8P25/cW1FljNQrH3hRxUtx4mr9+ZCw9/3RAOpLYRMVJq4fx4TThBmsBFWc\r\nzcHmeDE4Gk7uBqDeF4TTX36deLnnGERNziY=\r\n=0s7u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.0", "@jest/types": "^29.0.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.0_1661430811843_0.1811974269383545", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "jest-watcher", "version": "29.0.1", "license": "MIT", "_id": "jest-watcher@29.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63adeb8887a0562ed8f990f413b830ef48a8db94", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.1.tgz", "fileCount": 15, "integrity": "sha512-0LBWDL3sZ+vyHRYxjqm2irhfwhUXHonjLSbd0oDeGq44U1e1uUh3icWNXYF8HO/UEnOoa6+OJDncLUXP2Hdg9A==", "signatures": [{"sig": "MEUCIH5ZSCrI7OJzIpENcpaTZ1IQgMPIB750uft8nOHDwnfXAiEAr1vo5CoSTSnwVaN5gS2L6I97XHPxcwM2elQpP2zrp0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMv1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowZBAAirwGRCzSGygFa6XJe8K1EutWca2xv8UtuYbDVFCj7xgII7Te\r\nBoaEwro1IcxGXW+zn9yAo36Bnyf8e8j10iUgBoP9Ik2lgme4892ttZkPQU+c\r\nSAtULF1N88b5Di5qlsLaV7EOdtC1XP6VONz8a+fjaauOE6tV+U8gjUgZBH6V\r\nd2KYthWwUBoAj8p+EL8o6FKqtC7UpMTCg9rSEEYfqMLZQUVplyKUQAdFp4V5\r\nukkB/Qt6y6W0AkkFlJpbnTzUTC+FtHwL7uXc8EZJPGtjqmRrKB6i+khYLRr2\r\n85/ePY59AeQRA1qygvXWqh1WvTd6inLMvqjvQSTMkUJ6KstkJX6pYrD5ufy7\r\n6lWpzuMgEdcddRWemB85bHuITsxg4kMC7UaUSj01lKvpeiBZIWuGBZ5cn0ie\r\neRwjDoyZ04kH5Osb6c7HPSSPAwnsOdLcMCPIefJD4eJuI40gFViakTySjQLz\r\npwXPZQ/3b+Js6adHZaSyFXinu43QUuHhiOcIs3Xzw6WouVi/BZ842Szu/OoV\r\ndbsdOFuVoJiGZRBsfT89aJynDXpc+jwWm27dggexoCbHlrzx99uUg+FA36zh\r\nDxj/dbUEw6ObqjuRWtQsP1Ph2wBwXnnvgyQ5hI0Td1xcw8uhyLs8A1sjJQtC\r\nePlnTT7Ke2zgon9aPPm6LVGafd48XJhOtfU=\r\n=SseB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.1", "@jest/types": "^29.0.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.1_1661520885753_0.6831358261019578", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "jest-watcher", "version": "29.0.2", "license": "MIT", "_id": "jest-watcher@29.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "093c044e0d7462e691ec64ca6d977014272c9bca", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.2.tgz", "fileCount": 15, "integrity": "sha512-ds2bV0oyUdYoyrUTv4Ga5uptz4cEvmmP/JzqDyzZZanvrIn8ipxg5l3SDOAIiyuAx1VdHd2FBzeXPFO5KPH8vQ==", "signatures": [{"sig": "MEUCIDxPdis3Wu/MkAeLJvLzfv0E/Xtc29DXbll4d3hwYzd/AiEAkNM67OnOmQo0gMbYB4twu/HjE3uqVYxjKf3e9kZMc+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNSA/+OSxoeiOAOWGPF0wivfXOqH2439kV/YbjxgslQoHj7V+jrxRc\r\nveOXfzdu7sfyPtwGiPnhAQRhX0+ObXNpDheZaVjsNpQ7fYNtnognufIrMZBr\r\nZaruUF0w/RIB/YvCiy7OQRqefFjmsb9H1dSh/mN9Vp7cEs434ShzC5qPUAoh\r\nEzqtgvEvDKaAYA4JIlPRsdVakDmvpyQ3C7YW39RRhhSMJjwDHlVcOoaH4k59\r\ntH6f7UFGFnu7mzB/cU6g7xLk0DiKUlNhotZ5vUeFszmrn9h4qyIv03nQY6Og\r\nUs+t+pZuNBuV5YhkCE0xv4NlWwAv3eevzBNwWHtWMYhhBtlGJ0Nmew2BrFeK\r\n3JJU+YXCXMvpc6CHAZu83QsS9eAyzwAp7Q/g4zxOf/Zib/Td4L4mYKgNkv4G\r\neu7wt8OJM4gWsBywVgHNYXeVO+Zj+FHM8td+Uo/LIeUPrpJnECAgxBi5s9r2\r\ngoJbS9FN4hG8PImPy5dHOaRpR6tMrlgMxbIKyz+FcmfmM0/wt46Ywk+6OSJW\r\ne4Q3Ir63R/qhIAmIIqqD+JxUHkJfnpPKI+4d0J/eKg9jGPE+pu+XtLgQSqga\r\nklgUWRC9n9HkXLkfopZLZekzh+Lh/0r5/jX+bAZ2WCACnFZyQNVzu1XgpkgI\r\naNMq8Nz3Hlbe+bYdqbYoxGAwVO34reNf938=\r\n=hYMc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.2", "@jest/types": "^29.0.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.2_1662202103208_0.27593399711520417", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "jest-watcher", "version": "29.0.3", "license": "MIT", "_id": "jest-watcher@29.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8e220d1cc4f8029875e82015d084cab20f33d57f", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.0.3.tgz", "fileCount": 15, "integrity": "sha512-tQX9lU91A+9tyUQKUMp0Ns8xAcdhC9fo73eqA3LFxP2bSgiF49TNcc+vf3qgGYYK9qRjFpXW9+4RgF/mbxyOOw==", "signatures": [{"sig": "MEQCICFb9ltrhstyJVs63USx9NsqpZmWObDEzRLL/wrYjotIAiAnob0T3f1Ce48XFz/wrWeIDnzQMVE/rzpQIwss6yIMeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpaWg//WXAF+FVK7lL22QkyhQyshKuGzIlb0ttI9j1FCtlNTQTEw95K\r\nHEo6KbyzchY238WMCxYICDEH/KZAmocsdI3ybY6oseg81PYqXqrmV81Xmjpe\r\nFlXovHTQB+NC3OUfe4dq+39/g77CsWe/zQjfl9a6vEWxm6UmTx6ylD4s1VJW\r\naJ+/+0TI7jaykE57Xs3e+iNll6N1NkLzBg6ZqKKcYfU1yGhE7dBjYf+y4Up4\r\nCUbFf41VeNIEKCqiJ3Nt0sTEiL29gNyEkca7RVAD+pOO3CcPNUyKuiGOKxWG\r\nbsDepz8ghASs2D84rr7hrfjEPFhKW7v6+kDcYeRlawDTgfWR8Kj6Jb4VhTCS\r\nCwZWiV1bF0GOVIn06CZed81CoX2yljKdz22eaSiYFjGYrWYi9W0PUqbxOYX+\r\nDxMzfbqZMZJBaVaOJs+ussw4TXfwRqUBNDvRYakazKxE1IylDDbCcLLvmUBL\r\nqcLix2bhqc27j3zdTdm0yM7P4RbiOj0LrDuKjj2YlvmoYtw2wBizGou4coY1\r\nlWWhWEcOBASLtFKee0U09sSq4ZBzTMZVn7M5Ibc80juttWXeXcSs5efIFPK9\r\ng8+J92teTuDPJRNsbMAKgu2NtfiJDwcc8q19XvgYIPl2DG/YGONArt31+tk/\r\n82qy2CcH7VmQ/xQndO6qanK8qm5sutg7dUI=\r\n=Rt3d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.0.3", "@jest/types": "^29.0.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.0.3_1662820906891_0.1842493120466877", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "jest-watcher", "version": "29.1.0", "license": "MIT", "_id": "jest-watcher@29.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4a9f9d2c9bb9115f7b543c26f468d2c0c0202506", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.1.0.tgz", "fileCount": 15, "integrity": "sha512-JXw7+VpLSf+2yfXlux1/xR65fMn//0pmiXd6EtQWySS9233aA+eGS+8Y5o2imiJ25JBKdG8T45+s78CNQ71Fbg==", "signatures": [{"sig": "MEUCIFX9h25KxjKmBn/A1eQPGUkzqc1dRUJGHkx49GgwJdX6AiEA3WTfKP/n53dshiZTzc6IxSVAE1cwfqv4pYkldencaaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvmBAAkc7AddYWqELEj/MGcLFgJIwC2hgjODbtwFcvL8j27q1E8l9q\r\nrydmlonAJDklLdUuY5sroUQpil2i9WVTyqhgJfetb8j7jvq6lUDQqUi8P8Gk\r\nJka9j1DR+64Nj2DpNp0JNZHMqid+Zytk1fpe+SefaWu3sJ8bwDNXTh54g1B/\r\nx6SZaGjmsZYXVsOnEPA/R+k5UDNMSNAnJs0qTgKE2UW8RL6jjhbbHroznlFe\r\n89bdxvCWpSOUqMqFfAqT5pBEXEnITfHAmE/UVvQwaUjgp8nLbgP/iM3hPpUH\r\nXsIUNoqdboYhkXjiHDW3nxV2xIPzXK7nRXsIUH+5J0idjIuqLCA9/qzhU66p\r\nUStq7RIJdtSLdnTOopYzWgRKLJZGnyvAvEu08+ad+WFbHYB2KFZnLHP6vcxa\r\nGK7F7lX8cpkkOBk2JWVf0oIQxYEwQyEJAY2/AjwTZH/gY6BMBAsuS5g0xO5B\r\nlOJGi4cb/VfBmGGQJC0WVohrkkxO968QESaYAwHYSF1odSji6bQD1IoSdDmZ\r\n4T04f1My7eD7jxK+74KvyaOO8lJhdz2hEFt16+/aFDVokKcRPaEav71oY04z\r\nC42PIFnDYB7CXFTiE5QSL0tELkibf7/hpwzmd8wp1/KQQuKxS0cl8gC6kXHF\r\n2f5Za0ty2pME9jyv345U2cNe3QlzXXTvSKU=\r\n=PL64\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.1.0", "@jest/types": "^29.1.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.1.0_1664350665785_0.05020962681924779", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "jest-watcher", "version": "29.1.2", "license": "MIT", "_id": "jest-watcher@29.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "de21439b7d889e2fcf62cc2a4779ef1a3f1f3c62", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.1.2.tgz", "fileCount": 15, "integrity": "sha512-6JUIUKVdAvcxC6bM8/dMgqY2N4lbT+jZVsxh0hCJRbwkIEnbr/aPjMQ28fNDI5lB51Klh00MWZZeVf27KBUj5w==", "signatures": [{"sig": "MEUCIQDiqJ1oRmoV5gAsZvAUrDUQxcM2rq3WJyPGXtFhJ0grVgIgNbFum+bj+DHAikadliq0WiFxbA8juMokr1T7lvc0X7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaThAAl06MW5qjZeujk7aYlstGR+zM6iV9u79dWh94eio+ltiGNvkV\r\nVxOvYHS86dd67WMq79MOzfuiafBv//0yJQ/W6yvPIP9LIRMmSizEnLv0HWyG\r\n8vCTi7pvgbcXcRKcV+ClBz6dyOz85kkjRJc9phe2kicXSg0CB6v6MBmOB6S4\r\nPY/m12eJck230o+wawxFaWd7MSyndz5A3zSMbZdEDcj+qjpeTPuoLFgGGt2Z\r\n0lydn++Sdk6Y5njuL/l1sDSwzFjXff5s2hlm7c1TdTh+3HrMUsZDwGNtf4sE\r\nboqoyDnbjpROeCNJHIZPW8zB2ruoAchBTWetvOJFE4PcZPQp0ItTgENbCJuk\r\nCLmpCXM3N4LoM89VFH7KXufc2wT0AxPhP+e890Va/w+JLV5YCGxC88IyJZ6x\r\nJaORa7V5QvOWFtWE8xFFLzah9cMRPouCSXAR1toPP6ZxTcUfhT0rXhmojvv1\r\n1METcVq4D2nt9P+qeKqWQ/RY8F8DjqakmkdjEL1Wp6ll0ESOnHOGEMe7wdgF\r\nTI/sOwu8xRLv09IfC5TL+7Lw+d6LsH4EZxIRIIkyOCkfWApHfFMoEUeTWFR/\r\nK9WZ1U2FIepbIOOMz0rcqxLeMXI+KmMJaDfotL7y3cCUh45dh829Mb3aMcqZ\r\n6ppXFdil7CaUEy/7bPArALzhXhLBZyrvRIU=\r\n=jVqM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.1.2", "@jest/types": "^29.1.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.1.2_1664522572969_0.03108370552570383", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "jest-watcher", "version": "29.2.0", "license": "MIT", "_id": "jest-watcher@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d0c58ff76d3dd22fff79f3f9cbeadaa749d2ca6e", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.2.0.tgz", "fileCount": 15, "integrity": "sha512-bRh0JdUeN+cl9XfK7tMnXLm4Mv70hG2SZlqbkFe5CTs7oeCkbwlGBk/mEfEJ63mrxZ8LPbnfaMpfSmkhEQBEGA==", "signatures": [{"sig": "MEYCIQCe11IJIIWXolUjMuF1Mn0fVXKnPyJ3t/LyIzyshHDuqwIhAO3r9j5dirMcHgqTqsRRM2ALEvKtQ1yODDr0c7xOOjDb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT1hAAoP3/WlJPvto4mgwQcE+lLbFOpBCGxVy8mqsaPFuLx3Z+DUMg\r\nt1NJjjduUomrbitiq6tze/q+rc0ZYneNi6Ugmyg5NKOujtQXJ/NVwL/kEVcP\r\nPVMcWUy/T5+9RQyG0l7zmsqlcWV3wQHq6vSPZ2BSdu4WoRHUVrH8UWi8QIYf\r\nbNnsaKo5N+ahBZwQDd7bRRkqtwZxHFg1I+go1DhsPleyXa4twUsnrQb0hRmH\r\n+v608GUX6S/x0xrboS1SztonLeWJW1j/Jwvatf9MzB+6n8LABBupPYPnaF0a\r\nwSMGzaLAGf4dB3B1UoUOxnexq72WkGqvQzLNKv2fxJnRlUsyrqiqJj6xW4r3\r\ndg4Fw9wWBGvxxYaUPq3e2OWyBI0VRSWgK3gXMNGIDYJ9iQ+8DfTMcfKgFpmV\r\n+EeGQ92l0NqbnzkQZ3n6NCchNUyGgNvp6S9k62PXjN7PAebuwcogwOEttjA+\r\nKBxwxNtmBMkonxBIkVLJNMw3VK0ZA3miSphUEcJo65l+G3BVRMob8ZwlNiDl\r\nOYKC/Up7fKkcpoIgY5kbn/k0w3Fm6M5rgdKeGibA08etGYowMp9g1fRXP3sA\r\nfg3XxKIT33VPocJQJltqKX8bmN5XaXcn+RM6sqylrX1uo8sTNaeRzH8BCsuD\r\nIcrDKUns5ABwp9RoS36kzvoaC2yqGttRUA8=\r\n=nQdk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.2.0", "@jest/types": "^29.2.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.2.0_1665738836553_0.7183008249132496", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "jest-watcher", "version": "29.2.1", "license": "MIT", "_id": "jest-watcher@29.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1cb91f8aa9e77b1332af139944ad65e51430d7c3", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.2.1.tgz", "fileCount": 15, "integrity": "sha512-7jFaHUaRq50l4w/f6RuY713bvI5XskMmjWCE54NGYcY74fLkShS8LucXJke1QfGnwDSCoIqGnGGGKPwdaBYz2Q==", "signatures": [{"sig": "MEQCIGLkLxpORz948DA07qWhWiJozCsOtWLUC9tCa268/L7YAiBESgy/thV9w4aak3hOPIPexhj+2Cj6khkp4G7mrRuSsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxFw/+PFBwOAY1Zidbile7rEbKkwJoK9Tx1/0zGVAEJEoSAZa+0XJY\r\nvX2lpNVfZ6LXmabGb1vRlqdVqlLqRNvdfDbrluP/fEqpMorwtMb899zLsDf2\r\nWzN8BPVhghb5Ai7pFFRxULF3zr/9IBsSjLi+UGZu3KClJ/EyOnmp4yAZQ5bx\r\nu7/iZULMiOCfLRuDZPGJbMBmhX4XXVEWo+93Gs0wyvyymomKFT+305BK4QYJ\r\nnnxV24t+ZjL+esUZ987nSE9M3LSeDDmhiOm0LqoktMkSgT1Sqz9Ek+yK0kkB\r\ncCXZHBvLoCEHphbPqzLA/4MA1V5XYKBDo6YKRQ9DzRo6GQkT2sjYTFQszFYv\r\nj93nxLoPpQi+pbzacdyOTYcnby77gqBmKiNlGq1b3wW/z3YwUAR6MjJTlsRJ\r\nEhpDsexe4SwMhkJj1UlP5r4BklIwvgP7V1Qrl94MzaYmJNAuRxfyh1f90Zzw\r\n6jtjTisN+ll0+dsHIXxRq/hq0qsUSoW4T6oFpVa7zJhxElvHR6+kPPDFdfiU\r\n0ysxs7ULdzsYEv+Jj51rJO92eh9D93hWVtlK2iE840WV98eXleK+IBLVBzL4\r\nkqPKwTJTpRkpmuGiPfyVd28CJwaaZggKPN5RmXS8DT5Y5f2eNHBi/QzUSGta\r\nHUJWOMJnk+fKgApfRfMNNMSulPf/9KI0y1w=\r\n=cRZZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.10.2", "jest-util": "^29.2.1", "@jest/types": "^29.2.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.2.1_1666108817530_0.09235256170318262", "host": "s3://npm-registry-packages"}}, "29.2.2": {"name": "jest-watcher", "version": "29.2.2", "license": "MIT", "_id": "jest-watcher@29.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7093d4ea8177e0a0da87681a9e7b09a258b9daf7", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.2.2.tgz", "fileCount": 15, "integrity": "sha512-j2otfqh7mOvMgN2WlJ0n7gIx9XCMWntheYGlBK7+5g3b1Su13/UAK7pdKGyd4kDlrLwtH2QPvRv5oNIxWvsJ1w==", "signatures": [{"sig": "MEQCIH7CFRnTfDXsA27/0jTeyju7kQ49HDLF67OmMxi9fAsvAiB14YbtGrYgIf2PLNm8myzaKZoEtDuAw/WYCKuDRXBzjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvRmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWUg/+IxhmuXmoJj55yfrtXefQwKyeU+B52cy7vAwIpk6dalrhWZxo\r\nvi9hsheC6SgLR7+vWgEU2ffNSaa0sIShUzYfNsrCYQlXDXtVoMdrLX6bGcvG\r\n1BVWnEVG4BtseY809DDoM7oxJ+cJgaX/WrvAaYYaPX2YryAdmuNzGuR8Ldxz\r\nTLFWqj/QjuN3UvZ/Pj2JYZ/fnUR1i7aAFeKkZV8IRQtEfMHC3jpbUB6J/hBz\r\nV+sXpflqTbZfl+hUlTcO0ttYz59RRZwCyUPgeKHlQH03Hkei8QuuLG17jA9/\r\nXtPLCTNWU/1PGig0X0DJAxDui9+xMcP47bcyVBQBjekuoF3eeT/FWq0cqgg9\r\n99FfpFsmbgpmeYA0oNfpBHwe9ehaGc2wYqe9mRArKgK6oIemmJeV7AuzI545\r\nR3cCa9GANZS6l24e5yhxDcyNOqpH9M4PbC6GDahLPryGpOqgC1nYkEauOSRo\r\nieA/RLWRWqfAZpqKFgQ2x/oHH+aRVHjjSO6K4Z31Q/FXNmra/UNQjoPYpQn4\r\n7WCjTy+LJp8DQOXij7b14jGyIHT/y2XX2UwibD0fUd+cWSmopZiTYLKCXLRg\r\nbJ7gAL5BxNM9v+eEch67p43qxeLKYVg4dWc+kgC9078WfzkovVNSxw1MRBR2\r\ngwOZfsKVcqKrppeQUB44Ml0W/J5X7+86uNE=\r\n=syVz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.2.1", "@jest/types": "^29.2.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.2.2_1666643046085_0.6111164346008338", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "jest-watcher", "version": "29.3.1", "license": "MIT", "_id": "jest-watcher@29.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3341547e14fe3c0f79f9c3a4c62dbc3fc977fd4a", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.3.1.tgz", "fileCount": 15, "integrity": "sha512-RspXG2BQFDsZSRKGCT/NiNa8RkQ1iKAjrO0//soTMWx/QUt+OcxMqMSBxz23PYGqUuWm2+m2mNNsmj0eIoOaFg==", "signatures": [{"sig": "MEQCICCUUevqAfieJ38F0um4JERU/MWWUMqLQ81yqwaSLjA8AiAB4w45semM3074uTQOYliJS5Pi9OpaVkJR9o6diSzvgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptCQ/8C6z2N6aHumJwqLhNhQV32YCBgW6+GYb2Z72MYC2Se5TfYGUr\r\n5nGaiu+c5h+DDROjwB22yTQG+ktoQH+QHFHpIvJWUFzRg7rzld8Vg4nKbuV9\r\n2Ed5vMvahynyKWFnoz6F44jB80kCD2vAMhnvcgP/AtxwUjbPv3OBM+A5dBeA\r\ncMDCnsq9lHKXazXU6UadT4sj4QG3nRBcVXxTRpyORy/+xcrxsxT8cZ3GmJqP\r\nqvZc4UOO4NN1HKrK5mofk8TNI8uAchIVbgWsyS4aPAWMJUvpHEPFt/QlsqRs\r\ncWIeSYcmpCKJ+UMCyhWOGwaJhkO5CNZEmWDa5UuMHlIyWtok2IxhBgK7ebHb\r\nmcGl1usF9kiPugOCga7yOKIh/HT4fet4UP56LtS0aZHnHuRmby/dHsvKQp8Q\r\nXaDi+j/Xk7QhSyvii5+JxUlUVWba4VQn8b4Cgva/WI1WxwRIMA+GGKW22ZA+\r\nJjuKE8gHsM7XrEwF3JmxCAF8DUkRQQxAFKanbtJloJrPGDn0WL/AHsSb7JYL\r\nSg5Ab6SDtvWFur1j9kyHzGnK1Q7LKbWAyDXj26DBWRDOjbo5Wxi2rbFC+esa\r\nw4gihgjKQCce87sCvvqs6yhQj+yMrsSimhOVr0Acn7etvZnlAtVl6xy6MTzq\r\njTPkonGaStVia/pLP008kWYP98MYkaJsbpQ=\r\n=YJHp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.3.1", "@jest/types": "^29.3.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.3.1_1667948186818_0.46180488979991674", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "jest-watcher", "version": "29.4.0", "license": "MIT", "_id": "jest-watcher@29.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4f51e6fba4341d965279a5a646adde5104d414f0", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.4.0.tgz", "fileCount": 16, "integrity": "sha512-PnnfLygNKelWOJwpAYlcsQjB+OxRRdckD0qiGmYng4Hkz1ZwK3jvCaJJYiywz2msQn4rBNLdriasJtv7YpWHpA==", "signatures": [{"sig": "MEYCIQCo31pR4GESvjYLGgkE/Y4tKkJ2/75wofzZE5UX5aZkjAIhAPsuPZhBtLtqvuk3AQcEM90kTMwoWbwP4s2lUNxaYSny", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7lDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4tw/+NqtpC/kuMJnWgldOOzka8bwCzvHbZyGQudNjRrm2ZPBn49ch\r\nc6O3/30zGk7sT53MOOce/wTFkf+2jKXEH6BjbCIiOIh5Zv2OGskec3mzDEZm\r\n0QraRG6Qjl+FTy3g4/QzjPqkoZqZxz4v47JM60RbsoyS5DWtO0kKfGAcMFDm\r\nxm+R+aqT2XBZ9KLzw6MG7bGbix6JRYuMsrW9aCJH7iuFQVq+cNCG1YgaL1xt\r\nGEhwbYITmp/qYx1nX53iHT0psusqrm7L3KZKoFY8/nBqXoiWS/YnoymIrcaw\r\njTsTzUfdjyn8RTleic2FGsCN7iqm7hr/epUCSuDcyszyPchaRakotxphmL3L\r\nB7+GpjkaiAwmptol+aDQ/DQMiN7g6s5u2w7L2ifBvZ+DSNIij9aZLn/9dHn0\r\nCNqcwyXqTP3zIDhbsLlPh/ABZjaCG3LhLxFWfZXQNidc8/gS6Jpv6ndd9mgX\r\njOPz9Qpm0K81xNrqrJv80hWXvBWm1nZjdwKkqLgatcfRfG/VvzzhztnnFYrO\r\neMPT2t3Ofwyu/WPpdk2y5mOgJHBl3fFPRsLX0UIU17mXnG7wuXoZ3HTKwmKo\r\nkG8YWwmlV+/WuY+pDK3O88ECVZmV46W7VeRrs8uq3kvWCWRhSm0hiti+e1J+\r\nP/TqUP/HUUXlfhP+W22GyXV18soW0FbXbiU=\r\n=CHl8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.4.0", "@jest/types": "^29.4.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.4.0_1674557763418_0.8708558332616743", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "jest-watcher", "version": "29.4.1", "license": "MIT", "_id": "jest-watcher@29.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6e3e2486918bd778849d4d6e67fd77b814f3e6ed", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.4.1.tgz", "fileCount": 16, "integrity": "sha512-vFOzflGFs27nU6h8dpnVRER3O2rFtL+VMEwnG0H3KLHcllLsU8y9DchSh0AL/Rg5nN1/wSiQ+P4ByMGpuybaVw==", "signatures": [{"sig": "MEUCIQCNqaCmpxNIFYxna5bCyEV683DmbMgqIhgnSX3NFSj3iwIgZ+PXIk8vTO2JmDKseHA0vHr3gvXXgtXlP/te0Y3/aDc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWiw//X7x4mb916L05yXnGnekQPtxfvQ4jGjzZ6GdX7iu43zM/FalT\r\n3ZM+dd9lRr38xF2etEt2qWwdaj76m0Xv9W8FCsAkop3yEF5qZvyQPjLxdXZx\r\nwIlItpMGDqUyU6Ifu7Si3smtSesNYdLh3Vzp58G31glorFTQAjf1dZw7gVTD\r\n9c7olFu+T1nDwtrJyYEeZ2lwz2infJKj886Rotd1y5p8hE8DCXhthNl2+zfK\r\n1eohhQh7bUk7yGlLXp8IidRTHcrbTzgky64DGr4/lFOQ1WT3OUsFMxKmRjBG\r\n9+V09JctYfal+8Hw6KDgOHoSdm3BtdEVU85QKGoLAi5QXjNe1efUOPYeBqbT\r\n6CWGDaSC9eTLlSJIb3o7ULsBZ0MWUFdr4Oh4wZIpMMvGbMsp9UArE8/cAwQo\r\nGGOv6BUXk/DbQWacS8nf9Ccen4aN/hUjJSrN3FZOBLrwLML9LhbtPqsVXI1k\r\nGxLVQHwwFcR9U4frqoowVWbBm1zEtL2pJqvJ2x168gfVZFdq5q8de10/Csa3\r\ngmvUkSCy1lP1/uIUleObjPpkz6uvUZSG+M1wk5QNGWCrpq5W/aE2lv2Sm0lE\r\nLEqMqd3oZKmNh3KG02E+7ZVTGmJJtavP4M0bx/26WyVZ+JpbdwoalnVATabQ\r\ns3I0j2RBJiIpaou0QVnaJGsThBBTWcbt6hc=\r\n=dPNu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.4.1", "@jest/types": "^29.4.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.4.1_1674745724104_0.17590110232621958", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "jest-watcher", "version": "29.4.2", "license": "MIT", "_id": "jest-watcher@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "09c0f4c9a9c7c0807fcefb1445b821c6f7953b7c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.4.2.tgz", "fileCount": 15, "integrity": "sha512-onddLujSoGiMJt+tKutehIidABa175i/Ays+QvKxCqBwp7fvxP3ZhKsrIdOodt71dKxqk4sc0LN41mWLGIK44w==", "signatures": [{"sig": "MEQCIG2EzMcnC49Id25f8Ws1KX8dNFmVDG5oeFXCSVPLMf9YAiBu2SYpyMCf+aTD9derP5Vs9EUqOZcKSQfSlQiQfL2leA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lYHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqjcw//f2Gm4mo/I2OYUvVrNhjtNUSUE182bFHX1npeFBDQs5Qb3dnb\r\nuna8AqBJHgDJymd/RmD7aNSE8IEz0Xsy+nZvpB2xCg927jrBUQZm1E6mo+DU\r\njTX0FTvBfKq+g1fLlgJa6Af7sxCQY0ciG0qj0vX/M4VUqEV400by1sNawwus\r\nOT0qKEuC+iAHFWopKiJvC7Y3MWy1avxbPPROguk3OVLVmmAvLDY3NIYTz2MN\r\nfQAuYySGE1g9oxtz8JblZQQitIWfPgI0UaV2DW+1GhUYzTKZ/H34uPAoxLS8\r\nd9DjQ1pusq90Q6NpDDnKxmyN69VfWh/K+8o5Ywg/VM3mWS0lgmb/01HAOu/1\r\n0YFPLBWTYaZIyOvSbFCly9QAF89X5UrsT349917njQxrOcaUcILHmJz9a41r\r\nkizHEi9HNJikRX9UrP+BId0bQ2KHYL4jjO7Ts32eATN6wPAvBZFxAv2gM08B\r\nnVaz4xKe6QTxhTP8jpczSQzL8dFpNBxvXXawk1PKd3JOPlv4jy45FxD7DzBv\r\ntjaTrB8PmRu4mvKxHM74I3UAMrXo7fD3fELo11FVAQns3bwqSWj/VWgXakiR\r\nQvOz0e4rrx7dfbgURwvKq7Hww812xDBjT2GZvQL2EMFzkZEzl6ZdLEdzIFhg\r\nfsYj3mqzW1eBVMTQRwpKyZJK/us+j5jojV4=\r\n=q9OY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.4.2", "@jest/types": "^29.4.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.4.2_1675777543351_0.862960824172222", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "jest-watcher", "version": "29.4.3", "license": "MIT", "_id": "jest-watcher@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e503baa774f0c2f8f3c8db98a22ebf885f19c384", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.4.3.tgz", "fileCount": 15, "integrity": "sha512-zwlXH3DN3iksoIZNk73etl1HzKyi5FuQdYLnkQKm5BW4n8HpoG59xSwpVdFrnh60iRRaRBGw0gcymIxjJENPcA==", "signatures": [{"sig": "MEYCIQCWZD8ihURDk0Bs+nHl+UzkRZMyA0x9KdJCb6rxcCX4HgIhAMwPgVRxGWq3akZUAMm48nx4WVaVWNRO3QlyoGU9wKm4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MisACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5Nw//XIidTZlEtEvye/E5eAPPFKZ2sJNmWq4FjnXWhEw/+4psuLTa\r\nsOn+dy45q8jf24AZJBPHHR/zmRkZhBhvN/c2+0voCuUiEPaO9PZeWBrIrb7k\r\npl1bmYBDVuIkh4A70vN8rldwksnqczaPQczgywj97BbX9BFgIdf67H4TiQG3\r\nch+n/4ZktBPNb7bQd8wPKMF+Z/3wgh3cos/rhFaTxp+POMoi0z04iJG0drVg\r\nNyffGWLYQimpm/ovEGpVpxuQp76Rr0v8qAp8whbR9D/lj7GWO47THK9QSpGm\r\n3R+AJ01i/Gr6q1/pJHkS1TGgXZ/X+2fxb1JW1HZR+aBRXFG1KPaRCS+o/sKv\r\nKrxLbAIYvojrEnjnsSDJ8rrIt3wp7EeGAZXfSERtAFsuMkQqBncAVVIzKWV/\r\n1bEYvNBG8VT7mvPKgihD3DeX9bf9ODuiRFiRBRLj1bsTafUwe4n7Kd/Nr6gC\r\ni0Jq9i4p7jdp5Nj/U5N2v4JvRA6K2QxU8Lbxr6sWl/DEx/zi72HVwR/3XeHR\r\nohZbuex56a1h15eGhi5pmcaQy4XyoLd0RdTef7oxlxdPFNhBResdoMBXj4pX\r\nAWImhoXM+ueDdPN1ZMQ5J4XKofBlyhzpFmpUN8GQgHnKVrzOu31f9rtYO+tk\r\n/rCdJFyjTx+2mS00eUuLCJbh2elE8KPkgEk=\r\n=/VdP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.4.3", "@jest/types": "^29.4.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.4.3_1676462252578_0.8444700084769603", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "jest-watcher", "version": "29.5.0", "license": "MIT", "_id": "jest-watcher@29.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf7f0f949828ba65ddbbb45c743a382a4d911363", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.5.0.tgz", "fileCount": 15, "integrity": "sha512-KmTojKcapuqYrKDpRwfqcQ3zjMlwu27SYext9pt4GlF5FUgB+7XE1mcCnSm6a4uUpFyQIkb6ZhzZvHl+jiBCiA==", "signatures": [{"sig": "MEYCIQCGiXGsu0cQe3TwH3y7V1GhdWh03uFBgv7mvxexGwW+GAIhAOj0ydAWgnO/t74ErtGSe25BNw3Y5dprvh6Uz2DNa4oo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeu1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrP3w/7B5mtgsFaG/pHYgGgARDlP5E64V19orOQAI2va6zaz5mytIXX\r\nkEBxMviczHPqDrZx+oCGrIVZ5m2RfeFxk5R6ouBPyA+2pXH63sdeSb6WfuU5\r\n5PYEAFFwK5mIdGjCX47HUEnKLrkowUfS2R/dVngN2BJyUKJ2KwgaZhgLWbO3\r\n9oncvj1aJOsm6yq6LUyGRXbswDqHp+gkNtzDHSatZH980H3o9N0a6AUOoXea\r\numrNNXwo0n9AugKRAncPwOQTy5bpVfHzEkLlkf2PkCvSdyeIp4rklo8OhLaD\r\nvpV3K06PEJlrHC7vQsY111PcxpHtInjnIxk8AEZ44mtd3JkTWaoUwSVmXmmQ\r\n3e8EPvgcKT22XbXEuamdrdLhcPQqmbgtO4yDUWlgvCZ2N+xq7oFI2ezV1h/e\r\nUjBTmzsG1mJWkUc2BkquWtcB7NAe2QrnGlOoTKO4crFDj1r6Q5ERMByA0pn5\r\nqqJNYWnWNxATMMwf3uua65wJlrexg6Y8q+JuqdtOCW2aVXEuZQQIBUfq0+qU\r\n3PyJHcFuaA20D71SbZviVRvr+23ugqCydmvnqShLPQESKJ5+yhjPIrId1H4o\r\nKiEi5SqQKFaAm3y0wsWangAivx6657nRg2ktdz2NSf+cjUQnuJTGgYvqJDDe\r\nLJAkPwHWIoKThYH74nbBVes6vvUVHmgl2WM=\r\n=985i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.5.0", "@jest/types": "^29.5.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.5.0_1678109620901_0.8455381275664491", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "jest-watcher", "version": "29.6.0", "license": "MIT", "_id": "jest-watcher@29.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "77df9ffcdfc70406fdd577020c1e4d62de5a0299", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.6.0.tgz", "fileCount": 15, "integrity": "sha512-LdsQqFNX60mRdRRe+zsELnYRH1yX6KL+ukbh+u6WSQeTheZZe1TlLJNKRQiZ7e0VbvMkywmMWL/KV35noOJCcw==", "signatures": [{"sig": "MEUCIGwwJVZIALEzpqeAd0HxYInOA0MPIjxUcKVFGT9DwjKyAiEAh8pL2r2Yx5p+0bCf4L0rpkpq7GZFstAHvIjHyVPVxaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23434}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.6.0", "@jest/types": "^29.6.0", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.6.0_1688484354386_0.07880719424459315", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "jest-watcher", "version": "29.6.1", "license": "MIT", "_id": "jest-watcher@29.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7c0c43ddd52418af134c551c92c9ea31e5ec942e", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.6.1.tgz", "fileCount": 15, "integrity": "sha512-d4wpjWTS7HEZPaaj8m36QiaP856JthRZkrgcIY/7ISoUWPIillrXM23WPboZVLbiwZBt4/qn2Jke84Sla6JhFA==", "signatures": [{"sig": "MEQCID7vyegzmF2866S7/IrUmeKkmod5p+OWzlFbCJiZSmLXAiApB593bf3HQwddQ+n5O24kONU1M6L1uGBMTFTi3za0YQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23434}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.6.1_1688653115940_0.1702637735683774", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "jest-watcher", "version": "29.6.2", "license": "MIT", "_id": "jest-watcher@29.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "77c224674f0620d9f6643c4cfca186d8893ca088", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.6.2.tgz", "fileCount": 15, "integrity": "sha512-GZitlqkMkhkefjfN/p3SJjrDaxPflqxEAv3/ik10OirZqJGYH5rPiIsgVcfof0Tdqg3shQGdEIxDBx+B4tuLzA==", "signatures": [{"sig": "MEYCIQD01BPEsbGhs74SPVunpOBeSAky6ILzoKqJ21fjOvsagQIhAKQ2PnPpl/uYzP1K7zT036Qbbs2TlEh1svee0EQmnvxk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23434}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.6.2", "@jest/types": "^29.6.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.6.2_1690449699683_0.1186261017195005", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "jest-watcher", "version": "29.6.3", "license": "MIT", "_id": "jest-watcher@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f5089852fc5f57ba1d956ec02d80cf2f6f34156d", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.6.3.tgz", "fileCount": 15, "integrity": "sha512-NgpFjZ2U2MKusjidbi4Oiu7tfs+nrgdIxIEVROvH1cFmOei9Uj25lwkMsakqLnH/s0nEcvxO1ck77FiRlcnpZg==", "signatures": [{"sig": "MEYCIQDC3V19/PLFsbZfjaemJi8rYII7X52OSfJI3NSejPjN5gIhAJ7H55SBwiqirpxIvhSMZRPKJu8WJ5k6U1go9cZsGD+J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23430}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.6.3", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.6.3_1692621585650_0.00725804367927263", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "jest-watcher", "version": "29.6.4", "license": "MIT", "_id": "jest-watcher@29.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "633eb515ae284aa67fd6831f1c9d1b534cf0e0ba", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.6.4.tgz", "fileCount": 15, "integrity": "sha512-oqUWvx6+On04ShsT00Ir9T4/FvBeEh2M9PTubgITPxDa739p4hoQweWPRGyYeaojgT0xTpZKF0Y/rSY1UgMxvQ==", "signatures": [{"sig": "MEQCIGoNhi8JtZVZPQdx7USF4TdI/WnhM73s9+8h4oPUHFyZAiBOuBfL8E2xsOKJl+khgXcCKtlI+PhvKE9KEno5THuRag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23430}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.6.3", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.6.4_1692875472609_0.4987226786133625", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "jest-watcher", "version": "29.7.0", "license": "MIT", "_id": "jest-watcher@29.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "7810d30d619c3a62093223ce6bb359ca1b28a2f2", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz", "fileCount": 15, "integrity": "sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==", "signatures": [{"sig": "MEYCIQC0YZupRmUN6srL9i1hvYkdt5YKsv3vTAAqlwdzoxAqTQIhAMRgne3VRPE8Ps023xztzrlh1aKBlBfqALcj4HuGDGDc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23430}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_29.7.0_1694501032352_0.5655508551903763", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "jest-watcher", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "jest-watcher@30.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f48b0242e07af9bc66137b2ae020952b2ca6c428", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-KUZ1mJS00UjcJbJ9/2IyIBZr6qnFRfwqUz4qEKUQh1fQTUDiad5YyXQoCcpcDAs4m8o8r5uwamLPLyAGgwkxjQ==", "signatures": [{"sig": "MEYCIQCZDVNu9NawlvEqCh9cVehddO5EookWZplwaxKfuxtGYgIhAIxCGCEaCRsk/CCIu/BPFffeVTYnfcA67VAX0qTywY3m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23076}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-alpha.1", "@jest/types": "30.0.0-alpha.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-alpha.1_1698672801786_0.6045306751743593", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "jest-watcher", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "jest-watcher@30.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "c83730fa1f11d8bf9080f7448599791103326cd8", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-v7XKoUwsFFrG2xoDv5ZUhpFOhKE5t0fHTAFe9xJRrM/xjaIOs44aoR55HDpyvVEeS1j2xmwYynx9E7m4OxZmhA==", "signatures": [{"sig": "MEUCIQCvKrZOV6PBoZeIFWM9L7/agYjlj5ok49KGtymKX56g1wIgGZmJEReeBcCGrT5+vpfwwDmdcVjyEdVVBCDbhf+VnhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23037}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-alpha.2", "@jest/types": "30.0.0-alpha.2", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-alpha.2_1700126919625_0.7497302950420162", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "jest-watcher", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "jest-watcher@30.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fc167628ab200f035db552c3b7f47a6c50ff97c6", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-prny+JoMv+1jtIpLP3CxHPseXPlUGPGOrslFPWCDJ9NoIMqmjxwe5KlTrNwbDnP/zMUPPYfaIbKZD9XiSmq78g==", "signatures": [{"sig": "MEQCIEyerR6vF86UTeOPKPhyi9JH4KEdORADfojS/K46R4SYAiBJnXOodcQc0/8A9sLKfZxP8LkhTdz4q3OezsG0ewvMCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23049}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-alpha.3", "@jest/types": "30.0.0-alpha.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-alpha.3_1708427365250_0.9954797829026074", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "jest-watcher", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "jest-watcher@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f9130613f97ae9590fba1d18aa030572a7f5077e", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-BSIF4lklBvNMRu+VyuoPfgvBmA8XBqmX6Y9pi6GQCHOrKhcUvTjSpiy7eLnhhaawpDkYYUuioaH4szDaYH/hlw==", "signatures": [{"sig": "MEQCIF9sSrwl3uLIBagkXouBd6yuPO5TtSOAYbOmPJ6r4QbAAiBzcCkwb4aerC1FTJRA7ecj8796z1px/Jxk2WAeX7KVHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23093}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-alpha.4", "@jest/types": "30.0.0-alpha.4", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-alpha.4_1715550219862_0.8890899543557398", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "jest-watcher", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "jest-watcher@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "71d5ab5373ec57f296dd17e7962e0734d91e8bc2", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-TFzV/GtI3tA4ab0IP2HZyaZ7skMS1yu2HmLfznAjhFixZpdkA3zsRevH5nX0YZo+2zAO/jgrJoXdWH8OexjMug==", "signatures": [{"sig": "MEUCIDzVZ4gGqZBlEuJw+3BF5R7EIKJJY5+x9nbtwbEm4639AiEAl3nJYv3IbE6/xtCVZ/j+nhXaPkqB8Tv4GlDA9A76gsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23105}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-alpha.5", "@jest/types": "30.0.0-alpha.5", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-alpha.5_1717073058500_0.5248161326593943", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "jest-watcher", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "jest-watcher@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f77ee8b90d165a86f808862cb341a914a24de095", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-+zL1y3GSJG8EOxVSc2p0dndis0rNDcwKTs4b1bpNTI0XneeTiZlCpRBNYI+sqBl/eZtJBrQdiBRSYz7kJqg7NQ==", "signatures": [{"sig": "MEYCIQCgoDJP0f+/0p1CqTjY0L0tHwPzY9QGxuBOrQ2o5tjXfAIhAPjNbRnhMv+hOb8TnpYeubgWsnyFXqDbJUPn5mnPj3NF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23033}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-alpha.6", "@jest/types": "30.0.0-alpha.6", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-alpha.6_1723102994238_0.015169932360711291", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "jest-watcher", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "jest-watcher@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "545f05964c82c60b1d35a7a7cc3467b06481e686", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-lR8YUEcSJ4x1b8Tr6WpQjkCpwwvtU/YlFaBbg0zZ8gePLAeDWs8WFcn1dYOOOe396mCs0YCqBZtBdBCz945kyA==", "signatures": [{"sig": "MEQCIBph+loO0JiWqpNOhakhAzKIGDmI0SLpmvPqU/gJHOeGAiAQ3egimMWjSTsSBiPk+RkHJEZ3ow67h3D63Z02t8P+DA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23033}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-alpha.7", "@jest/types": "30.0.0-alpha.7", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-alpha.7_1738225722812_0.800400261860372", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "jest-watcher", "version": "30.0.0-beta.2", "license": "MIT", "_id": "jest-watcher@30.0.0-beta.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fc646311a84441f55de378abf74ddb19472b12eb", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-kk84UPBjfKmgO01amulZ5oJnKJss1NOQ9qSjliqMCLo/B57E5pU19K6CagcYwgSNaUXhAUEkb3NsushojBzOTg==", "signatures": [{"sig": "MEYCIQC2FaND5Xn40SC9bpVrHnrlgpYfUuEEMFG1RZ44YgS7ngIhAOUvMWRn4P8V/681b8/PZvbrpC7OYK0NN1mkdQMbin3f", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23028}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-beta.1", "@jest/types": "30.0.0-beta.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-beta.2_1748309008057_0.9762323986291597", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "jest-watcher", "version": "30.0.0-beta.3", "license": "MIT", "_id": "jest-watcher@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "0943a51e1a16c34806a9572e33d471a383f6f681", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-9hBtY76IMC9+3I9bXytB/2Y4fdzcdVBy9WWKfZ+LI8E0r97qUg/uGlhd+3jMBxSnJCXAaBAVUzjTadSURDFX2w==", "signatures": [{"sig": "MEUCIATVSjjvesUc5qZh0XFQI0xhGBo54g4RMipXJqlBttcFAiEAqVvQ401N4DIrgFw1guSUCnYG7GjbxUsMVfCQB26ZCtk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23028}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.1", "@jest/test-result": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-beta.3_1748309277779_0.8644974836157278", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "jest-watcher", "version": "30.0.0-beta.4", "license": "MIT", "_id": "jest-watcher@30.0.0-beta.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f53a71fcad379412132e053bd75f7df3ac8407a3", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-beta.4.tgz", "fileCount": 5, "integrity": "sha512-yu1CXtpDIfN2c0/ohtBYUP/dAUGsxQIuzTBVW+4uJIsTx9HhKjjJJctYt14f47Ekd+FDBkkmGrL9Y77KDxMJFg==", "signatures": [{"sig": "MEQCIB/z8vsnFmYTv68GnxUgd94woLzmfVCGJicuVzZ5EqYuAiAUzT2U/0XTYFqW9QE9XECi69fVhLfo9MU7ZJcHslIs6g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23028}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.2", "@jest/test-result": "30.0.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-beta.4_1748329474389_0.7363380543994085", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "jest-watcher", "version": "30.0.0-beta.5", "license": "MIT", "_id": "jest-watcher@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "e065dc12ab4ea1e0a6289a3410a243998d85d61d", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-beta.5.tgz", "fileCount": 5, "integrity": "sha512-xCxo68p7YCnqPcHYRk3pu4OlrF9b4n/6saGSSvwdeQI7J9zfQOUCpTqTwm++ZU0rD+V5gb2ocWxkykWLY3Q9EQ==", "signatures": [{"sig": "MEUCIQDAAXFbhX7/yjWbqJEBjUzsWa4dcHoQRwva8t+AX7/7pQIgQQ5Tr77MmB8ABzNhnwBrIsz2RNpdatkhy6hnRahj09M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23028}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.2", "@jest/test-result": "30.0.0-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-beta.5_1748478617785_0.4458582639993338", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "jest-watcher", "version": "30.0.0-beta.6", "license": "MIT", "_id": "jest-watcher@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "2bdd07a90f56f654c1263d7569173da84fb5797d", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-div75NPs+WBrIQr5bKfIrXQYWYMj//mHpR6kdInb4DbEVUTXFnNPFAtcQUlyMW2NNuhIAN8kmtt3uXT1i/cNvA==", "signatures": [{"sig": "MEYCIQDcoRjPI3YU9D9kN7tYvnPB3qqM9EP4N8HPCzO24Bl5EwIhANCCMdZhCbCHSUxFolDaCHLXDEqP5DaX33Dphh3Jm+x6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23039}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "30.0.0-beta.6", "@jest/types": "30.0.0-beta.6", "@types/node": "*", "ansi-escapes": "^4.2.1", "string-length": "^4.0.2", "@jest/test-result": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-beta.6_1748994659291_0.5926010879508752", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "jest-watcher", "version": "30.0.0-beta.7", "license": "MIT", "_id": "jest-watcher@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "46e84ae6ca2603c31765e88773fa8428431ec09c", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-5GERDFA4alDn27bxZ0yUwoiSrH5kLK7npMupdejF+ddL6XfuR1YYfGDaiKvNZlMpaiBwRv8q4piUzx/o3ecmoQ==", "signatures": [{"sig": "MEQCIDMrFDN0XPv31mSEnQ/bRayUC2tM8kSIRHOlL0/UpvqqAiBwbmMWV7PWcgaX5T0DYESDhiZ2yBUSg3+HGKcDUzYHiA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23039}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.0-beta.7", "@jest/types": "30.0.0-beta.7", "@types/node": "*", "ansi-escapes": "^4.3.2", "string-length": "^4.0.2", "@jest/test-result": "30.0.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-beta.7_1749008151796_0.2771000904035714", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "jest-watcher", "version": "30.0.0-beta.8", "license": "MIT", "_id": "jest-watcher@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "8bc7ac682b873a07502039cd10130d565ea6a0b2", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-msu5YZZ7QId0lsCBvEWTLulMSf+buW7hx0EJms77XpJYZcUDD+yS3q9Ivj4TnHoZZ8Epu91dy7qxDSrdgp+RYg==", "signatures": [{"sig": "MEUCICPG1lB9NLtJFXD+qaBD3MWOLLQuKIVnemVXkZKaCwSoAiEAgdInOMDYOW7YEOEK+MHL5l6FSqagxRm1L2bYJ4BlGBQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23039}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.0-beta.8", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "ansi-escapes": "^4.3.2", "string-length": "^4.0.2", "@jest/test-result": "30.0.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-beta.8_1749023600281_0.26568766427886126", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.9": {"name": "jest-watcher", "version": "30.0.0-beta.9", "license": "MIT", "_id": "jest-watcher@30.0.0-beta.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a48cd80b593de810e862d6f60c494d88be03bb9b", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-beta.9.tgz", "fileCount": 5, "integrity": "sha512-B5LX7E/fPknZSPzRJRDHuHqH3DSO2jlIpiN7h33nvZ12Cdx8iVa6WNOJs1UM2rPwrgIqyKxME2D/JDYU45SwQw==", "signatures": [{"sig": "MEYCIQCMUbCO0f+JnQOOdT9EdwHk8N6L/njaBXUHLp4unal11QIhALzIp8HuyvRQUNkF4rJ3Bc6b4MgEemYluMGEJ5ditdE9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23039}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2f52a9ed429fb8797a99868860430d55db6d5503", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.0-beta.8", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "ansi-escapes": "^4.3.2", "string-length": "^4.0.2", "@jest/test-result": "30.0.0-beta.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-beta.9_1749109236683_0.733446853480896", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "jest-watcher", "version": "30.0.0-rc.1", "license": "MIT", "_id": "jest-watcher@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a38bc484ba81ddac4975c7d2e90178f37cee1d5d", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-QVOV/he6Mr9O0LLBaABWLbsv5IPfre/qKunIzWbXqtyMFJAf4vQ3FU3UIxgDMZIyc0SKnVqZkGUc5X5jaGVrKw==", "signatures": [{"sig": "MEUCIBG4qwVGGaAOlejr5UaSafUxj+BEJrxYLEiOwdb8oqNIAiEAy3Xfq51tzfysxUpLt394bse5V3DVb/G69B8E+SNGTcs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23027}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.0-rc.1", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "ansi-escapes": "^4.3.2", "string-length": "^4.0.2", "@jest/test-result": "30.0.0-rc.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0-rc.1_1749430975802_0.19826382962857703", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "jest-watcher", "version": "30.0.0", "license": "MIT", "_id": "jest-watcher@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d444ad4950e20e1cca60e470c448cc15f3f858ce", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-fbAkojcyS53bOL/B7XYhahORq9cIaPwOgd/p9qW/hybbC8l6CzxfWJJxjlPBAIVN8dRipLR0zdhpGQdam+YBtw==", "signatures": [{"sig": "MEUCIQDjLzltu+0nFSzpTIupqLej2PnORMpJYtv8phe4RkWc/QIgZ5xRtKAz5dN7tvpd12M7H2c3AJe9NiP8SmYuY16EagQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23005}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.0", "@jest/types": "30.0.0", "@types/node": "*", "ansi-escapes": "^4.3.2", "string-length": "^4.0.2", "@jest/test-result": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.0_1749521761574_0.36478017401686014", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "jest-watcher", "version": "30.0.1", "license": "MIT", "_id": "jest-watcher@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "11c8e8d8b16ed88b403bc8dab0446068f3084806", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.1.tgz", "fileCount": 5, "integrity": "sha512-TZUy0f9VypPGse7ObbKyfUo7fhVtzLmmDhX84dv4KMvu2j27Nj49L06hBjAiGwi9m3jZruQuUEtQlctaVLSRZg==", "signatures": [{"sig": "MEYCIQD5R2iQW764Ktb3gd6MrpEfhXZC5Rf6DbrwyEz9t3sdjgIhAOy3bC5fFyJ2xV+VI/emIk6tubQQt5F6HAHV5av6yvhY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23005}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.1", "@jest/types": "30.0.1", "@types/node": "*", "ansi-escapes": "^4.3.2", "string-length": "^4.0.2", "@jest/test-result": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.1_1750285898810_0.02065787173375755", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "jest-watcher", "version": "30.0.2", "license": "MIT", "_id": "jest-watcher@30.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "ec93ed25183679f549a47f6197267d50ec83ea51", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.2.tgz", "fileCount": 5, "integrity": "sha512-vYO5+E7jJuF+XmONr6CrbXdlYrgvZqtkn6pdkgjt/dU64UAdc0v1cAVaAeWtAfUUMScxNmnUjKPUMdCpNVASwg==", "signatures": [{"sig": "MEYCIQCnlpvB2zw5CjLLvw5IMy+SD9z9BRvP04notx/VIrGmdwIhAKkqfjGGNYINr7+vbD62EHSV2ABuEVn/RThfee0EXPxA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23005}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.2", "@jest/types": "30.0.1", "@types/node": "*", "ansi-escapes": "^4.3.2", "string-length": "^4.0.2", "@jest/test-result": "30.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.2_1750329989087_0.08269458665860885", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.4": {"name": "jest-watcher", "version": "30.0.4", "license": "MIT", "_id": "jest-watcher@30.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://jestjs.io/", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f51b9870760d917851bb5b871e95b3c5f021cb86", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.4.tgz", "fileCount": 6, "integrity": "sha512-YESbdHDs7aQOCSSKffG8jXqOKFqw4q4YqR+wHYpR5GWEQioGvL0BfbcjvKIvPEM0XGfsfJrka7jJz3Cc3gI4VQ==", "signatures": [{"sig": "MEQCIHF5Swkqbvhn7uUljm21U7wKb6p6aGfTDezGVhx5wmosAiBZnnv7oT+FVEWDpthsF5WtkXSz4qe3fMVAaO2yKWxOZQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28621}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f4296d2bc85c1405f84ddf613a25d0bc3766b7e5", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-watcher"}, "_npmVersion": "lerna/4.3.0/node@v24.3.0+arm64 (darwin)", "description": "Delightful JavaScript Testing.", "directories": {}, "_nodeVersion": "24.3.0", "dependencies": {"chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.2", "@jest/types": "30.0.1", "@types/node": "*", "ansi-escapes": "^4.3.2", "string-length": "^4.0.2", "@jest/test-result": "30.0.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-watcher_30.0.4_1751499949769_0.036768355431610944", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.5": {"name": "jest-watcher", "description": "Delightful JavaScript Testing.", "version": "30.0.5", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/test-result": "30.0.5", "@jest/types": "30.0.5", "@types/node": "*", "ansi-escapes": "^4.3.2", "chalk": "^4.1.2", "emittery": "^0.13.1", "jest-util": "30.0.5", "string-length": "^4.0.2"}, "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-watcher"}, "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "homepage": "https://jestjs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "gitHead": "22236cf58b66039f81893537c90dee290bab427f", "_nodeVersion": "24.4.1", "_npmVersion": "lerna/4.3.0/node@v24.4.1+arm64 (darwin)", "_id": "jest-watcher@30.0.5", "dist": {"integrity": "sha512-z9slj/0vOwBDBjN3L4z4ZYaA+pG56d6p3kTUhFRYGvXbXMWhXmb/FIxREZCD06DYUwDKKnj2T80+Pb71CQ0KEg==", "shasum": "90db6e3f582b88085bde58f7555cbdd3a1beb10d", "tarball": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-30.0.5.tgz", "fileCount": 6, "unpackedSize": 28621, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCgdppmrAIbn6uzlRdN3Z22a+IJlxPSHY3sAyFnLOUQ1AIhAOh33eUGwakatmYUHSucXbeCLNhVaG/KwTY9lFhoOMs3"}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/jest-watcher_30.0.5_1753151323737_0.35887518589449785"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-05-30T18:05:34.852Z", "modified": "2025-07-22T02:28:44.162Z", "23.1.0": "2018-05-30T18:05:34.937Z", "23.2.0": "2018-06-25T14:05:13.362Z", "23.4.0": "2018-07-10T15:52:26.640Z", "24.0.0-alpha.0": "2018-10-19T12:12:41.677Z", "24.0.0-alpha.1": "2018-10-22T15:35:45.119Z", "24.0.0-alpha.2": "2018-10-25T10:51:12.021Z", "24.0.0-alpha.4": "2018-10-26T16:33:12.788Z", "24.0.0-alpha.5": "2018-11-09T13:12:42.594Z", "24.0.0-alpha.6": "2018-11-09T17:49:38.406Z", "24.0.0-alpha.7": "2018-12-11T16:07:49.469Z", "24.0.0-alpha.9": "2018-12-19T14:25:45.921Z", "24.0.0-alpha.10": "2019-01-09T17:04:16.628Z", "24.0.0-alpha.11": "2019-01-10T18:34:52.651Z", "24.0.0-alpha.12": "2019-01-11T15:01:02.496Z", "24.0.0-alpha.13": "2019-01-23T15:15:49.034Z", "24.0.0-alpha.15": "2019-01-24T17:52:53.936Z", "24.0.0-alpha.16": "2019-01-25T13:42:21.442Z", "24.0.0": "2019-01-25T15:05:16.982Z", "24.2.0-alpha.0": "2019-03-05T15:04:27.856Z", "24.3.0": "2019-03-07T13:00:08.150Z", "24.5.0": "2019-03-12T16:36:57.010Z", "24.6.0": "2019-04-01T22:26:50.230Z", "24.7.0": "2019-04-03T03:55:42.447Z", "24.7.1": "2019-04-04T01:19:01.100Z", "24.8.0": "2019-05-05T02:02:42.115Z", "24.9.0": "2019-08-16T05:56:18.646Z", "25.0.0": "2019-08-22T03:24:18.500Z", "25.1.0": "2020-01-22T01:00:12.289Z", "25.2.0-alpha.86": "2020-03-25T17:16:47.011Z", "25.2.0": "2020-03-25T17:58:35.352Z", "25.2.1-alpha.1": "2020-03-26T07:54:37.032Z", "25.2.1-alpha.2": "2020-03-26T08:10:44.476Z", "25.2.1": "2020-03-26T09:01:29.640Z", "25.2.3": "2020-03-26T20:25:08.262Z", "25.2.4": "2020-03-29T19:38:40.591Z", "25.2.6": "2020-04-02T10:29:30.027Z", "25.2.7": "2020-04-03T07:51:08.517Z", "25.3.0": "2020-04-08T13:21:29.237Z", "25.4.0": "2020-04-19T21:50:38.342Z", "25.5.0": "2020-04-28T19:45:36.390Z", "26.0.0-alpha.0": "2020-05-02T12:13:18.043Z", "26.0.0-alpha.1": "2020-05-03T18:48:18.686Z", "26.0.0-alpha.2": "2020-05-04T16:05:52.064Z", "26.0.0": "2020-05-04T17:53:29.216Z", "26.0.1-alpha.0": "2020-05-04T22:16:16.641Z", "26.0.1": "2020-05-05T10:41:13.543Z", "26.1.0": "2020-06-23T15:15:32.311Z", "26.2.0": "2020-07-30T10:11:59.039Z", "26.3.0": "2020-08-10T11:32:06.236Z", "26.5.0": "2020-10-05T09:28:35.653Z", "26.5.2": "2020-10-06T10:53:00.036Z", "26.6.0": "2020-10-19T11:58:56.443Z", "26.6.1": "2020-10-23T09:06:36.213Z", "26.6.2": "2020-11-02T12:51:54.868Z", "27.0.0-next.0": "2020-12-05T17:25:41.996Z", "27.0.0-next.1": "2020-12-07T12:43:42.397Z", "27.0.0-next.3": "2021-02-18T22:10:14.820Z", "27.0.0-next.5": "2021-03-15T13:03:40.032Z", "27.0.0-next.6": "2021-03-25T19:40:16.996Z", "27.0.0-next.7": "2021-04-02T13:48:12.055Z", "27.0.0-next.8": "2021-04-12T22:42:43.287Z", "27.0.0-next.9": "2021-05-04T06:25:23.414Z", "27.0.0-next.10": "2021-05-20T14:11:34.749Z", "27.0.0-next.11": "2021-05-20T22:28:56.601Z", "27.0.0": "2021-05-25T08:15:32.237Z", "27.0.1": "2021-05-25T10:06:46.681Z", "27.0.2": "2021-05-29T12:07:38.947Z", "27.0.6": "2021-06-28T17:05:56.018Z", "27.1.0": "2021-08-27T09:59:50.192Z", "27.1.1": "2021-09-08T10:12:24.683Z", "27.2.0": "2021-09-13T08:06:57.231Z", "27.2.2": "2021-09-25T13:35:13.250Z", "27.2.3": "2021-09-28T10:11:29.229Z", "27.2.4": "2021-09-29T14:04:56.367Z", "27.2.5": "2021-10-08T13:39:29.324Z", "27.3.0": "2021-10-17T18:34:50.863Z", "27.3.1": "2021-10-19T06:57:37.060Z", "27.4.0": "2021-11-29T13:37:44.155Z", "27.4.1": "2021-11-30T08:37:21.512Z", "27.4.2": "2021-11-30T11:53:57.740Z", "27.4.6": "2022-01-04T23:03:48.782Z", "27.5.0": "2022-02-05T09:59:30.737Z", "27.5.1": "2022-02-08T10:52:29.917Z", "28.0.0-alpha.0": "2022-02-10T18:17:43.412Z", "28.0.0-alpha.1": "2022-02-15T21:27:08.946Z", "28.0.0-alpha.2": "2022-02-16T18:12:20.449Z", "28.0.0-alpha.3": "2022-02-17T15:42:27.300Z", "28.0.0-alpha.4": "2022-02-22T12:13:58.806Z", "28.0.0-alpha.5": "2022-02-24T20:57:23.944Z", "28.0.0-alpha.6": "2022-03-01T08:32:29.749Z", "28.0.0-alpha.7": "2022-03-06T10:02:44.761Z", "28.0.0-alpha.8": "2022-04-05T15:00:11.628Z", "28.0.0-alpha.9": "2022-04-19T10:59:19.154Z", "28.0.0": "2022-04-25T12:08:14.411Z", "28.0.1": "2022-04-26T10:02:44.057Z", "28.0.2": "2022-04-27T07:44:08.638Z", "28.1.0": "2022-05-06T10:48:58.364Z", "28.1.1": "2022-06-07T06:09:40.380Z", "28.1.3": "2022-07-13T14:12:33.318Z", "29.0.0-alpha.0": "2022-07-17T22:07:11.571Z", "29.0.0-alpha.1": "2022-08-04T08:23:33.845Z", "29.0.0-alpha.3": "2022-08-07T13:41:39.815Z", "29.0.0-alpha.4": "2022-08-08T13:05:38.657Z", "29.0.0-alpha.6": "2022-08-19T13:57:53.025Z", "29.0.0": "2022-08-25T12:33:31.980Z", "29.0.1": "2022-08-26T13:34:45.890Z", "29.0.2": "2022-09-03T10:48:23.393Z", "29.0.3": "2022-09-10T14:41:47.081Z", "29.1.0": "2022-09-28T07:37:45.903Z", "29.1.2": "2022-09-30T07:22:53.092Z", "29.2.0": "2022-10-14T09:13:56.748Z", "29.2.1": "2022-10-18T16:00:17.687Z", "29.2.2": "2022-10-24T20:24:06.222Z", "29.3.1": "2022-11-08T22:56:26.963Z", "29.4.0": "2023-01-24T10:56:03.591Z", "29.4.1": "2023-01-26T15:08:44.283Z", "29.4.2": "2023-02-07T13:45:43.509Z", "29.4.3": "2023-02-15T11:57:32.748Z", "29.5.0": "2023-03-06T13:33:41.015Z", "29.6.0": "2023-07-04T15:25:54.560Z", "29.6.1": "2023-07-06T14:18:36.134Z", "29.6.2": "2023-07-27T09:21:39.917Z", "29.6.3": "2023-08-21T12:39:45.856Z", "29.6.4": "2023-08-24T11:11:12.794Z", "29.7.0": "2023-09-12T06:43:52.575Z", "30.0.0-alpha.1": "2023-10-30T13:33:21.935Z", "30.0.0-alpha.2": "2023-11-16T09:28:39.798Z", "30.0.0-alpha.3": "2024-02-20T11:09:25.435Z", "30.0.0-alpha.4": "2024-05-12T21:43:39.996Z", "30.0.0-alpha.5": "2024-05-30T12:44:18.636Z", "30.0.0-alpha.6": "2024-08-08T07:43:14.452Z", "30.0.0-alpha.7": "2025-01-30T08:28:42.984Z", "30.0.0-beta.2": "2025-05-27T01:23:28.266Z", "30.0.0-beta.3": "2025-05-27T01:27:57.956Z", "30.0.0-beta.4": "2025-05-27T07:04:34.552Z", "30.0.0-beta.5": "2025-05-29T00:30:17.949Z", "30.0.0-beta.6": "2025-06-03T23:50:59.535Z", "30.0.0-beta.7": "2025-06-04T03:35:51.970Z", "30.0.0-beta.8": "2025-06-04T07:53:20.506Z", "30.0.0-beta.9": "2025-06-05T07:40:36.905Z", "30.0.0-rc.1": "2025-06-09T01:02:55.991Z", "30.0.0": "2025-06-10T02:16:01.744Z", "30.0.1": "2025-06-18T22:31:38.990Z", "30.0.2": "2025-06-19T10:46:29.292Z", "30.0.4": "2025-07-02T23:45:49.965Z", "30.0.5": "2025-07-22T02:28:43.933Z"}, "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "license": "MIT", "homepage": "https://jestjs.io/", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-watcher"}, "description": "Delightful JavaScript Testing.", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}