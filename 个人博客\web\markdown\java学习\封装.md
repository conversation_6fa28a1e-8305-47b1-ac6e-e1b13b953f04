# 封装的思想：

![image-20241126230618542](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126230618542.png)

![image-20241126232705708](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126232705708.png)

```java
package 类与对象;

public class pp {
    private int age;//定义一个私有属性，age，只有内部方法可以调用

    public pp() {
        this.age=18;
    }

    public void printfage(int age)
    {
        if(age<=18)
        System.out.println(age);
        else
            System.out.println(this.age);
    }




}

```



```
package 类与对象;

public class test1 {
    public static void main(String[] args) {
        pp p=new pp();
        p.printfage(40);
    }
}

```



# 封装

1.就是指内部值或者方法我们封装的类外部无法直接获取或者使用,通常通过private来修饰来达成

```java
private int age
    private void printf_age(){};
```

2.把该暴露的暴露出来，不该暴露的不要暴露即可，用private修饰属性，用public来修饰方法即可

![image-20241126232920993](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126232920993.png)