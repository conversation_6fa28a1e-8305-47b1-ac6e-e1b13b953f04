{"_id": "inherits", "_rev": "97-53ea97dee2e9541a65b649ecb349a05d", "name": "inherits", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "dist-tags": {"latest": "2.0.4"}, "versions": {"1.0.0": {"name": "inherits", "description": "A tiny simple way to do classic inheritance in js", "version": "1.0.0", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented"], "main": "./inherits.js", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "dependencies": {}, "devDependencies": {}, "_id": "inherits@1.0.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.1rc6", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "38e1975285bf1f7ba9c84da102bb12771322ac48", "tarball": "https://registry.npmjs.org/inherits/-/inherits-1.0.0.tgz", "integrity": "sha512-5KfXESjCAfFQel2TLqhr18NEz++UiWVIA0jwHzs2Kbvb3e+r+G/eVhRfoZbaPCL0PnERvK5YeMgh02O4eenufw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCQCRNTdud0D6MQlD2K1U23YahprB/OiRSHk0Avz8VgAIhAIWziz2yt0m0OfeR5l7hSw0+Yq2Q/qR/U9V6pvC/NIUH"}]}, "directories": {}}, "2.0.0": {"name": "inherits", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "version": "2.0.0", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "main": "./inherits.js", "browser": "./inherits_browser.js", "repository": {"type": "git", "url": "https://github.com/isaacs/inherits"}, "license": {"type": "WTFPL2"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "scripts": {"test": "node test"}, "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "_id": "inherits@2.0.0", "dist": {"shasum": "76c81b3b1c10ddee3a60bf2c247162bc369f8ba8", "tarball": "https://registry.npmjs.org/inherits/-/inherits-2.0.0.tgz", "integrity": "sha512-/1BHJ22Ma30b0G9pThu1BPqybizVGTE8kYkZA1jZG/drZtpsaQT6yaOBObN+SY77sfHGFPOr6tfx0pgzdXJYBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFF7cq6vLIiyatOUaK/Y6nDxQunJgJlRdbv6pEvydBmJAiA63vEhPd/T13osTQNRHA+mU2PydSo9ZGFZOAMHfIvwZg=="}]}, "_from": ".", "_npmVersion": "1.2.21", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "2.0.1": {"name": "inherits", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "version": "2.0.1", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "main": "./inherits.js", "browser": "./inherits_browser.js", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits"}, "license": "ISC", "scripts": {"test": "node test"}, "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "_id": "inherits@2.0.1", "dist": {"shasum": "b17d08d326b4423e568eff719f91b0b1cbdf69f1", "tarball": "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz", "integrity": "sha512-8nWq2nLTAwd02jTqJExUYFSD/fKq6VH9Y/oG2accc/kdI0V98Bag8d5a4gi3XHz73rDWa2PvTtvcWYquKqSENA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAcFsX3T+mrxv2RNNEsUhROaf5Ue0VRglcBy/UFm1DDfAiEA//B/Tv6CGmF74+F8d2DsnMxHQl5tKvnd/lEik7g28BM="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "inherits", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "version": "1.0.1", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "main": "./inherits.js", "browser": "./inherits_browser.js", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "license": "ISC", "scripts": {"test": "node test"}, "gitHead": "8cc604cb8bd24a427eb92e96bca4d25a87ce4ea1", "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "homepage": "https://github.com/isaacs/inherits#readme", "_id": "inherits@1.0.1", "_shasum": "1bdf16c6ff8266cb858c6da2baf3637a99fb3d87", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "1bdf16c6ff8266cb858c6da2baf3637a99fb3d87", "tarball": "https://registry.npmjs.org/inherits/-/inherits-1.0.1.tgz", "integrity": "sha512-9QT+Biqw5CrQOHZw/X7cn3JIwJV6hJxiddHQ7WMHL4x6gdCzHHUA+fboy474lvK+8p5KHcADozSIN39aA6ZCag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDu0B6+0VAAf1LLsxWbtLdLslQuoFUKArTj7D8b8BQeYQIgamLe6FYcfUnzK/xIUzPB//4KZFuH21W+54i5/OkDn3o="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "inherits", "description": "A tiny simple way to do classic inheritance in js", "version": "1.0.2", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented"], "main": "./inherits.js", "repository": {"type": "git", "url": "git+https://github.com/isaacs/inherits.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "homepage": "https://github.com/isaacs/inherits#readme", "_id": "inherits@1.0.2", "scripts": {}, "_shasum": "ca4309dadee6b54cc0b8d247e8d7c7a0975bdc9b", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ca4309dadee6b54cc0b8d247e8d7c7a0975bdc9b", "tarball": "https://registry.npmjs.org/inherits/-/inherits-1.0.2.tgz", "integrity": "sha512-Al67oatbRSo3RV5hRqIoln6Y5yMVbJSIn4jEJNL7VCImzq/kLr7vvb6sFRJXqr8rpHc/2kJOM+y0sPKN47VdzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAWqCVtJoBS60UHntyYQrldjYdrPtuDfdtBDvdeq3Q2BAiBDMimJqowFSs0ww21+SUlu7B7I/cOomYLl8cUxVokZ9Q=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "2.0.3": {"name": "inherits", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "version": "2.0.3", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "main": "./inherits.js", "browser": "./inherits_browser.js", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "license": "ISC", "scripts": {"test": "node test"}, "devDependencies": {"tap": "^7.1.0"}, "files": ["inherits.js", "inherits_browser.js"], "gitHead": "e05d0fb27c61a3ec687214f0476386b765364d5f", "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "homepage": "https://github.com/isaacs/inherits#readme", "_id": "inherits@2.0.3", "_shasum": "633c2c83e3da42a502f52466022480f4208261de", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "633c2c83e3da42a502f52466022480f4208261de", "tarball": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsL6y7UY2qKCAifQ8Fnb7QwhOQbvtbI+Kr6VvNxZjHZQIhAL4wnDU7hqX4YDaHqbZRZHmZX5O+HRNZsauKdwk4sB8o"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/inherits-2.0.3.tgz_1473295776489_0.08142363070510328"}, "directories": {}}, "2.0.4": {"name": "inherits", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "version": "2.0.4", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "main": "./inherits.js", "browser": "./inherits_browser.js", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "license": "ISC", "scripts": {"test": "tap"}, "devDependencies": {"tap": "^14.2.4"}, "gitHead": "9a2c29400c6d491e0b7beefe0c32efa3b462545d", "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "homepage": "https://github.com/isaacs/inherits#readme", "_id": "inherits@2.0.4", "_nodeVersion": "12.3.1", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "shasum": "0fa2c64f932917c3433a0ded55363aae37416b7c", "tarball": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "fileCount": 5, "unpackedSize": 3958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCpisCRA9TVsSAnZWagAA34wP/R7/M+OPDguEHhSTBFwm\nyr5qskotwW0egz8MlwqkYJnKmkNMGVwH2ciD0+mzkoomD+iUf9cAI6qjAT+p\n2b+qFTikQpScNZRMKnMF+f5Jf0X6IVS03tojFm2i9BSxD0DL7fRoNLw/3seH\nO/5vYeiQUq0Ojx3AY4hf31AQTfBlZ7pohiE6BNAYWBXWpCq2c3uENGaeiwxk\nnmTL/fUs8RRubjrqW5Bwpi+PZrkmwcL+Te/juGLP1Ef52BDjaeVk1e9YlNtJ\nX++I+HyVR0Kb4pYyBO/iRE1ifanLmGm70PsBFUmwAAq21FGhFH/cfAeoRJo7\n3MuW5+W8n2BmJKwpngGIf/92SWJP9Ww1Vo1Lo11fpwGjPjF4RiFkD81+GDSI\n6LXikSQRmGS+6FIrkXDKZ45ir8K3tGw6peXr/fq1FmzicySGRd8gFpAZIxCw\nLIM7YXBonoGsG8p/LqG6rTfmC3ymOR8R+WH5NGeMXkRw08KJED5nMkSp5b4I\nIRenKnfIZgz+daoFE8p/W6KtTz+Ac1pMou9vnSa3B7YZjr1y6B6B3PLDq3kC\nyZZWvv3u78F8u8WkVS8iA8BvEO6aPcENzQcT61P4h0r8mitpLmvKN2+WViLV\nowBn8STKGeuXImHwPgY6Et6U29r9ec1Y01YNNf8Qfd03eNhroDFKBsC3rPGU\nT9HJ\r\n=18y6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHXj0k+tuAUqkpeooUqc/nleKcodzFQI19krYf3DkGBZAiBW7yh/k9RVcOvSDyyahK0Lr4vyYCvkybyNbx1ELNPSVw=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/inherits_2.0.4_1560975532337_0.07791065184845936"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2023-06-09T21:33:12.982Z", "created": "2011-04-07T00:35:14.848Z", "1.0.0": "2011-04-07T00:35:15.718Z", "2.0.0": "2013-05-16T14:44:54.523Z", "2.0.1": "2013-08-19T22:10:20.463Z", "1.0.1": "2015-08-26T22:12:12.252Z", "1.0.2": "2015-08-27T19:36:46.817Z", "2.0.2": "2016-09-07T20:33:59.118Z", "2.0.3": "2016-09-08T00:49:38.520Z", "2.0.4": "2019-06-19T20:18:52.465Z"}, "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "users": {"fgribreau": true, "pid": true, "dbrockman": true, "fibo": true, "shen-weizhong": true, "joakin": true, "stringparser": true, "kxbrand": true, "magemagic": true, "oceanswave": true, "gdibble": true, "rethinkflash": true, "iamveen": true, "docksteaderluke": true, "simplyianm": true, "alexkval": true, "mrmartineau": true, "minwe": true, "koulmomo": true, "chinaqstar": true, "kontrax": true, "sammyteahan": true, "antixrist": true, "monjer": true, "abdihaikal": true, "tobiasnickel": true, "christopher.urquidi": true, "markoni": true, "bret": true, "spywhere": true, "nickeltobias": true, "princetoad": true, "slurm": true, "mikemimik": true, "hyokosdeveloper": true, "roccomuso": true, "sadr0b0t": true, "mojaray2k": true, "soenkekluth": true, "sprying": true, "chrisdickinson": true, "leix3041": true, "semir2": true, "zhenguo.zhao": true, "j3rrywan9": true, "flumpus-dev": true}, "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "license": "ISC", "readme": "Browser-friendly inheritance fully compatible with standard node.js\n[inherits](http://nodejs.org/api/util.html#util_util_inherits_constructor_superconstructor).\n\nThis package exports standard `inherits` from node.js `util` module in\nnode environment, but also provides alternative browser-friendly\nimplementation through [browser\nfield](https://gist.github.com/shtylman/4339901). Alternative\nimplementation is a literal copy of standard one located in standalone\nmodule to avoid requiring of `util`. It also has a shim for old\nbrowsers with no `Object.create` support.\n\nWhile keeping you sure you are using standard `inherits`\nimplementation in node.js environment, it allows bundlers such as\n[browserify](https://github.com/substack/node-browserify) to not\ninclude full `util` package to your client code if all you need is\njust `inherits` function. It worth, because browser shim for `util`\npackage is large and `inherits` is often the single function you need\nfrom it.\n\nIt's recommended to use this package instead of\n`require('util').inherits` for any code that has chances to be used\nnot only in node.js but in browser too.\n\n## usage\n\n```js\nvar inherits = require('inherits');\n// then use exactly as the standard one\n```\n\n## note on version ~1.0\n\nVersion ~1.0 had completely different motivation and is not compatible\nneither with 2.0 nor with standard node.js `inherits`.\n\nIf you are using version ~1.0 and planning to switch to ~2.0, be\ncareful:\n\n* new version uses `super_` instead of `super` for referencing\n  superclass\n* new version overwrites current prototype while old one preserves any\n  existing fields on it\n", "readmeFilename": "README.md", "homepage": "https://github.com/isaacs/inherits#readme"}