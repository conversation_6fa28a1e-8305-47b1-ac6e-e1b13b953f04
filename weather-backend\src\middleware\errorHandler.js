const config = require('../config');

// 404 错误处理
const notFound = (req, res, next) => {
  const error = new Error(`未找到路径 - ${req.originalUrl}`);
  res.status(404);
  next(error);
};

// 通用错误处理中间件
const errorHandler = (err, req, res, next) => {
  let statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  let message = err.message;

  // Mongoose 错误处理
  if (err.name === 'CastError' && err.kind === 'ObjectId') {
    statusCode = 404;
    message = '资源未找到';
  }

  // 天气 API 错误处理
  if (err.response && err.response.data) {
    statusCode = err.response.status || 500;
    message = err.response.data.message || '天气服务暂时不可用';
  }

  // 网络错误
  if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
    statusCode = 503;
    message = '天气服务暂时不可用，请稍后重试';
  }

  // 超时错误
  if (err.code === 'ECONNABORTED') {
    statusCode = 408;
    message = '请求超时，请稍后重试';
  }

  const errorResponse = {
    success: false,
    error: {
      message,
      code: err.code || 'INTERNAL_ERROR',
      ...(config.nodeEnv === 'development' && { stack: err.stack })
    }
  };

  // 记录错误日志
  console.error(`错误 ${statusCode}: ${message}`);
  if (config.nodeEnv === 'development') {
    console.error(err.stack);
  }

  res.status(statusCode).json(errorResponse);
};

module.exports = {
  notFound,
  errorHandler
};
