# 第三天：

## if和switch语句区别与相同点

```
switch语句可以用与case是确定值的情况,if语句可以用于范围判断
```



![image-20241107143928255](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107143928255.png)

## for循环：

语法同c++，因为可以在任意地方声明变量，注意语句中有分号

![image-20241107144236246](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107144236246.png)

![image-20241107144948491](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107144948491.png)

![image-20241107145005664](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107145005664.png)

## for循环遍历数组:

![image-20241107145711529](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107145711529.png)

## 死循环写法:



```
while(true)

{
}

===
for(;;)
{

}
```



数组索引在js中从零开始，长度-1结束

![image-20241107150342916](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107150342916.png)