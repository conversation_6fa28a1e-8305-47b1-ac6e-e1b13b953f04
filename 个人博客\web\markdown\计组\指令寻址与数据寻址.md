![image-20241106103845838](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241106103845838.png)



# 指令寻址:

## 1.顺序寻址

下一条指令的地址：(pc)+1->pc,1是指当前指令的指令字长，因为可以是变长指令，一操作码多地址码，而且主存的编址方式可以是字节编指

![image-20241106104025325](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241106104025325.png)

![image-20241106104145671](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241106104145671.png)

![image-20241106104537132](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241106104537132.png)

跳跃寻址：

由于读到了jmp转移类指令或者call指令，pc会改变执行流

![image-20241106105512748](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241106105512748.png)

# 数据寻址

![image-20241106110910787](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241106110910787.png)

## 	       JMP指令指向的7是一种真实地址

![image-20241106111239339](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241106111239339.png)

第二张表100是初始指令地址，jmp 7 是初始地址100+7偏移量

第三张表jmp地址是103，是根据pc+3来寻址

![image-20241106111550614](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241106111550614.png)