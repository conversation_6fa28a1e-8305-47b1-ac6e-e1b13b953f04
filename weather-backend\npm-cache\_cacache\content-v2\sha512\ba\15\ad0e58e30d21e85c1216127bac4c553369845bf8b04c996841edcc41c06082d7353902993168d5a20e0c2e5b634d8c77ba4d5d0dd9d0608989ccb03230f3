{"_id": "slash", "_rev": "40-2be9f7c5f8cee44a5d5e025a57c16a56", "name": "slash", "description": "Convert Windows backslash paths to slash paths", "dist-tags": {"latest": "5.1.0"}, "versions": {"0.1.0": {"name": "slash", "version": "0.1.0", "description": "Convert Windows backslash paths to slash paths", "keywords": ["path", "seperator", "sep", "slash", "backslash", "windows", "win"], "homepage": "https://github.com/sindresorhus/slash", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "main": "slash.js", "repository": {"type": "git", "url": "git://github.com/sindresorhus/slash.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.11.0"}, "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "MIT"}], "files": ["slash.js"], "_id": "slash@0.1.0", "dist": {"shasum": "1726ee854d6a95c056dc697ec8d5be9a43926243", "tarball": "https://registry.npmjs.org/slash/-/slash-0.1.0.tgz", "integrity": "sha512-V3ZYwMu/hnFBDs/xGAmUBz1kFfukPhwkgqPDDCsimteRl7R8Q2hZO36yEdhafuAf/p81Th7rVIzLOi9TBlawhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHjf+afq3RyotVzk5t+vk7d9Gc5wMZWorI04WGLRJk5cAiBIt8lBtbLR166ubl3NjHkjn7PJnVxcnCH20hI8K9usMg=="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "slash", "version": "0.1.1", "description": "Convert Windows backslash paths to slash paths", "keywords": ["path", "seperator", "sep", "slash", "backslash", "windows", "win"], "homepage": "https://github.com/sindresorhus/slash", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "main": "slash.js", "repository": {"type": "git", "url": "git://github.com/sindresorhus/slash.git"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.11.0"}, "engines": {"node": ">=0.8.0"}, "licenses": [{"type": "MIT"}], "files": ["slash.js"], "_id": "slash@0.1.1", "dist": {"shasum": "e3b93c40a94a547a246367b71b8c31a793521a65", "tarball": "https://registry.npmjs.org/slash/-/slash-0.1.1.tgz", "integrity": "sha512-ItT3ioIfI071Qdy7fRqW+gI5NQtsx7kN1TC7l56vNlJscU/tZAPul5gUrOSf/MPtUGaXvLk7cm2ljO7Rhm3A3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF1GH3DDqmFn+ytiwb61IkPLTvsI6SzqqAD5ufUecVn8AiB1L6hzh23IlVWeNLjYe/RaVs7mzrQUxZTRE7n1Bf6b2w=="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "slash", "version": "0.1.3", "description": "Convert Windows backslash paths to slash paths", "keywords": ["path", "seperator", "sep", "slash", "backslash", "windows", "win"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "main": "slash.js", "repository": {"type": "git", "url": "git://github.com/sindresorhus/slash"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.8.0"}, "license": "MIT", "files": ["slash.js"], "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "homepage": "https://github.com/sindresorhus/slash", "_id": "slash@0.1.3", "dist": {"shasum": "aa710c8ef50b8e1d187ad6cff46f38c656ba0e57", "tarball": "https://registry.npmjs.org/slash/-/slash-0.1.3.tgz", "integrity": "sha512-9yyU4xFsBoZ6+s7njsnfRxWXmiSg0q85Wws1sQ58t47P2s+LZv2WLzaLvem012jKsikbXKjwJBzZUB2//kYQQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCO26UloWinoGyBg3EJWumIKoWEPsDy/8gHGIifr9gb3AIhANUtlLwvlr8BsABwXH8Y8r75CdQSUtXiClheiGunWqDg"}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "slash", "version": "1.0.0", "description": "Convert Windows backslash paths to slash paths", "keywords": ["path", "seperator", "sep", "slash", "backslash", "windows", "win"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "repository": {"type": "git", "url": "git://github.com/sindresorhus/slash"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "license": "MIT", "files": ["index.js"], "gitHead": "c801dd4568ad9380b534067eabe88942394f82ff", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "homepage": "https://github.com/sindresorhus/slash", "_id": "slash@1.0.0", "_shasum": "c41f2f6c39fc16d1cd17ad4b5d896114ae470d55", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c41f2f6c39fc16d1cd17ad4b5d896114ae470d55", "tarball": "https://registry.npmjs.org/slash/-/slash-1.0.0.tgz", "integrity": "sha512-3TYDR7xWt4dIqV2JauJr+EJeW356RXijHeUlO+8djJ+uBXPn8/2dpzBc8yQhh583sVvc9CvFAeQVgijsH+PNNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwMWV5iiiSKuDbCdMm47vmxF+3ETaNQm3Ei2ax/ywfFAIgIgj3K0BKzYfY3lLAZLcmUnLs/qhyaxjhxoql/38PeFc="}]}, "directories": {}}, "2.0.0": {"name": "slash", "version": "2.0.0", "description": "Convert Windows backslash paths to slash paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["path", "seperator", "sep", "slash", "backslash", "windows", "win"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "0d8c8d1f50b6634b86248705a1ee9c3ad6707ebc", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "homepage": "https://github.com/sindresorhus/slash#readme", "_id": "slash@2.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A==", "shasum": "de552851a1759df3a8f206535442f5ec4ddeab44", "tarball": "https://registry.npmjs.org/slash/-/slash-2.0.0.tgz", "fileCount": 4, "unpackedSize": 2819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCH4AB0R8c+cDKXTPFb/nDBkCJiWKFuQzASLJJBHir+qAIgJ/u6O3mBwO1oPBGxytZIc+XmFXIW2Qu8Rqodv1n5nJ0="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/slash_2.0.0_1521792101357_0.30105296160091255"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "slash", "version": "3.0.0", "description": "Convert Windows backslash paths to slash paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["path", "seperator", "slash", "backslash", "windows", "convert"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "262981c1fca19193a20657ac99690214d9483d0e", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "homepage": "https://github.com/sindresorhus/slash#readme", "_id": "slash@3.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "shasum": "6539be870c165adbd5240220dbe361f1bc4d4634", "tarball": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxStYCRA9TVsSAnZWagAAsvIP/iXpZHVuBU3NCxi5hUA8\nUsX+z9EThoOOnVY4bdiUBQSZExZcRt6/n4qkEPgcYtPsQAFnx4fZsA6rkhgk\n0Yh75/aup1Hizu4n3kX9lVLOqhXs8V9hU2BaJY0McxyzX45Fpl5+cP99Okfv\naEDj4S6rtudKIzkDHNvkJE6/cjhFplL/CnvsrPjadd8GnRzBx25n3NCQ1MP1\n9Yz4nHA1wYSVDnRYjAmsoU46vo02aRclzLvSZiQ+F0gENpBR75TnIBg6YNf+\naKXBAhwpq6j+s9BQQluMTbtv8KWDyHqHoGrC8XTB6dLf+ANYaXGDbOXkszS9\nOkziCwr/+3YpPnU/O1DmOcVp0lbcE2wxN4pfCvrrOh5RLLE6bfH8ikVEzzQ1\nbsgEQ7dW1ungOyGnpylfAL357jRZ6bb10U5TYbPZAoDi4zR47ar4NXRkeW6f\nW5GDRdTk8n3bm3QoFJR7XMvDXAPIg8YwCadhmFurbzACDYhOB4sbrc57wywt\npQRCWbf3ik57clBB9KggkWEmq2ymoSj8iy4tvrP+aR0WbtJO9lHrKEZeiucE\nClWWq84aFOvEtKxRe1dbJrRXbFcTAbeVn+tavJ/aKQQnmSpyn6G38hxJ2N5/\nGTnvH6HWNMSK5qaKSESV1D7mz0sY24ZQFWWwEhlRD02HTvDqVSdH6rJkGCNP\npGd7\r\n=FmeP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDa2XM52bozfvT19S8BCCh46iPqrPDeCFGNbgCcOdvgBAIhAO+zw0YfwwpbdEIPHWEWaix0XaROpZCgUDzNxa0Jjw+J"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/slash_3.0.0_1556425560214_0.8479364180448674"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "slash", "version": "4.0.0", "description": "Convert Windows backslash paths to slash paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["path", "seperator", "slash", "backslash", "windows", "convert"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "b5cdd12272f94cfc37c01ac9c2b4e22973e258e5", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "homepage": "https://github.com/sindresorhus/slash#readme", "_id": "slash@4.0.0", "_nodeVersion": "15.12.0", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew==", "shasum": "2422372176c4c6c5addb5e2ada885af984b396a7", "tarball": "https://registry.npmjs.org/slash/-/slash-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdpTDCRA9TVsSAnZWagAAaCoP/jrDh9mNkD4+sjieUyxV\nxpk1g6Hbmo1WHhi7coApCFTydpVvqp+kmtjh1/4iFmfT02Z7IysnPiHt53pM\nKB60rgD91mEHcDek147+/RXxd3rCHtkk5AD2IrjwK0fjxQjyso6nk5c8fjju\n80kNukSm15R+56tQZYdl7dfB8mVb2nFWTvRvLCVe34RTKa83DOaxed7VcXCi\nZ+PwNARxg1iqQYJPvBmvq+6m43xk/GYgFEcn8WUdG+ME8jQuhdT73CV405Uj\nGvoY2W4InqRzj44CGDerTqkMcVz6QJXHrgkSUimd/fHEaLHjlfbftD1bv+ly\n9vMNsLvXrd/3Kq8CilhqpKCoqWq+Nl8CGmnGmo2hW3i2XH9L0TQ+KLTi0cDr\nUliGoLypdFmh33COEc/H58FNMnepPGILOUhDE3viso7fSXWrUlhdPXUC9KFI\nGUbKYlqGvyErcr74yuaCrfs1vuLiPh2Smr4bOTWOZiGs1GDHzABh2c0ei3M6\nCv4C/2riun4n/tLI4fKYK2pBpgZJDtijVJ7KR+1VMBjLlz23MNC8diuMNpUA\nrBf3rs5obd/ZcpJjpeE4SwCGw7f75j5R842QHCoFCyUEb0cBryBGsrURubLG\nMxKjYg4LCKfiODcMSjKogERc6ax2De3DoV1TXHaYlHv2k6kf+VlreoiKR8gv\n6I23\r\n=Ij5h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJGlPIa5UHHIZj1S7b1AZRtglAloUHVpzi+T1QNBtoSwIhAKzm2uBTErRgqC0N2+SEWM+ULSRrlau8qfo0ceqHbx9+"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/slash_4.0.0_1618384066606_0.19573108233420888"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "slash", "version": "5.0.0", "description": "Convert Windows backslash paths to slash paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["path", "seperator", "slash", "backslash", "windows", "convert"], "devDependencies": {"ava": "^4.3.3", "tsd": "^0.24.1", "xo": "^0.52.3"}, "gitHead": "205c404ecf35b7a7a8a687d8d3b79101aaaa9fba", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "homepage": "https://github.com/sindresorhus/slash#readme", "_id": "slash@5.0.0", "_nodeVersion": "14.19.3", "_npmVersion": "8.3.2", "dist": {"integrity": "sha512-n6KkmvKS0623igEVj3FF0OZs1gYYJ0o0Hj939yc1fyxl2xt+xYpLnzJB6xBSqOfV9ZFLEWodBBN/heZJahuIJQ==", "shasum": "8c18a871096b71ee0e002976a4fe3374991c3074", "tarball": "https://registry.npmjs.org/slash/-/slash-5.0.0.tgz", "fileCount": 5, "unpackedSize": 3293, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAtnjJR94ezBS0GqOlu6NUYCZk9mTxuuOi0qLtTO4+guAiEA6wZJR1lVMJmfbQQk3HQTlfC3WPIo0kWBOxGONQJUfJY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMqqiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAIQ//akTU3jO+79x8Q6nRHf4qa8ZH87XZiWUCGU7pI+wNj7ldaxHm\r\ncSV9I/eGz85yKEqROYmGfgZC6OHSCIJI8b161VkHZEyJm0DMgIsrDcDdXKJa\r\nXhVQ0Z+OQ+snRqoUspPP6qpAuBTMTYQy43uDzdoFcimkQVgkxf/3TkpqAvrn\r\nQdfuqTKIzxe/Hnzt6qaffI34lWQNiDvq/lCjBe7jl2FOIvX20KJQ/DiQeokM\r\n9LijquWqS/as8yvSkcq0YwdJj9h8aK7uj++3T85QQ5GDpjV/2tvc5nmgQBGB\r\nlh27iJcfsrovqZ5I8zTT5PLHvhPe+skXR55thKubxh094kkP0AGcf20Or0Hr\r\n2sOKOOWrYGsqulbb3moBaFWRrtoIuO7trXhrv9wcCfJGKSWY8CY1CtBPsXO6\r\nsMe+rJWLsMAIcodM++hhBFmPnM6ijBBbOfSUJ2+OURGt4KKNTiPLrZnamudP\r\nak+4NokMtZBlxFJ9WHRDL70VlWUYfD2t9MZjCgD8Bc51z43G/PuDQuWa49s6\r\n2KjYHkoRjmfcn+SN0lKs+fP+7hBb09+vs0wJ1FYTm0nFqffiFUriKxQWSGA3\r\nL8pagQPP0inKC6VSFz9N1BKo+lsYWPTGGXE0PtcG98zYInlfz6Pgj6h6eV44\r\nuxEoTTLJT5q9pDIVFyNYsFR2FeMQEveYL8A=\r\n=slNE\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/slash_5.0.0_1664264865966_0.7130270807368944"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "slash", "version": "5.0.1", "description": "Convert Windows backslash paths to slash paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["path", "seperator", "slash", "backslash", "windows", "convert"], "devDependencies": {"ava": "^4.3.3", "tsd": "^0.24.1", "xo": "^0.52.3"}, "gitHead": "8659627938cde71310dc25871eea776ff59485b8", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "homepage": "https://github.com/sindresorhus/slash#readme", "_id": "slash@5.0.1", "_nodeVersion": "16.16.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-ywNzUOiXwetmLvTUiCBZpLi+vxqN3i+zDqjs2HHfUSV3wN4UJxVVKWrS1JZDeiJIeBFNgB5pmioC2g0IUTL+rQ==", "shasum": "c354c3a49c0d3b4da1cb0bbeb15a85c2a6defa71", "tarball": "https://registry.npmjs.org/slash/-/slash-5.0.1.tgz", "fileCount": 5, "unpackedSize": 3250, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDMTQ3Ave3blgp+9KbpdhHv0ARevtltePgayJ/d7dkI0AiEAswRXTJOkpkCnSzoKiaKXQHmCpSQ/s2Tp194+HIuFnt4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSDEMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9dw//eBXrps4+O0duAdmIb0YuvLjwaoolZsXp6OigEywi5JBvZ0b3\r\nxMo9TfuoIg9aPatyc/jsO+S+JLxZJgm2wE+CUhlC5hADRrw4J+zntSsiHY09\r\nYBrg0P8rMyg6Oz6D5IJQcrCUB9fnIl04uWKmRpUQ0+SZaI/ncVkfiRZKk5As\r\nS6L9g11si35yUKae41GyJDBPZNFhw2CWnvS8iwiKcoTlOlYKBjP0C/FK5Q65\r\nF0ISWEGYQE1/AtjMLceEaZs606HA7+2YN35CRJbBpsM5MfB9p6CQ/oeZSsui\r\niJJ1+DHEW+a5RgYMOOYDTSort0Mh78ihnqAEdaq9+9rPGsk6OrM9ZvUVJNkU\r\n2+WYfuAHXrif/1gaLRgQ2jv2NvmnwOiZlcH5cuSgmk7wDfbGAj4llxFZ40yo\r\n7/eMQQIdg8DbxoY3HGInW5MfALGtiOp1RUGwCg3vIBMHHmMAj0lY+/XY5uCz\r\ngqQCdjSboZ7ZB6lcvKtK/1VGYt1JEznKmYYyvO3uS8MMadofluEpN69WKBMJ\r\nqtTTO9TV48ziSHqewZFS+mzMC0witOg41PMthBY2qnsDRX/dyzbNR6Amq3yP\r\n0t+tB2cXwqtb3brHgB2gSln3dR4osqx7rYa2jKW4r1Xu99sjbWCaOobG/VC6\r\ndYuvgO3EToNPbY2wzZi+zlTubhgpa/GrNyY=\r\n=6Xw+\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/slash_5.0.1_1682452747936_0.13160998782923716"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "slash", "version": "5.1.0", "description": "Convert Windows backslash paths to slash paths", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["path", "seperator", "slash", "backslash", "windows", "convert"], "devDependencies": {"ava": "^5.2.0", "tsd": "^0.28.1", "xo": "^0.54.2"}, "gitHead": "98b618f5a3bfcb5dd374b204868818845b87bb2f", "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "homepage": "https://github.com/sindresorhus/slash#readme", "_id": "slash@5.1.0", "_nodeVersion": "16.16.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-ZA6oR3T/pEyuqwMgAKT0/hAv8oAXckzbkmR0UkUosQ+Mc4RxGoJkRmwHgHufaenlyAgE1Mxgpdcrf75y6XcnDg==", "shasum": "be3adddcdf09ac38eebe8dcdc7b1a57a75b095ce", "tarball": "https://registry.npmjs.org/slash/-/slash-5.1.0.tgz", "fileCount": 5, "unpackedSize": 3254, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3HGWLS8jAsOpp0YfrSvOCQkAsTHZFz6q7Rpv235ci7QIgW9LfHcOITTHcWFuYee/GQnoS0wdcAcc0lCzd0PTBlfE="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/slash_5.1.0_1683652023209_0.5292883174098482"}, "_hasShrinkwrap": false}}, "readme": "# slash\n\n> Convert Windows backslash paths to slash paths: `foo\\\\bar` ➔ `foo/bar`\n\n[Forward-slash paths can be used in Windows](http://superuser.com/a/176395/6877) as long as they're not extended-length paths.\n\nThis was created since the `path` methods in Node.js outputs `\\\\` paths on Windows.\n\n## Install\n\n```sh\nnpm install slash\n```\n\n## Usage\n\n```js\nimport path from 'node:path';\nimport slash from 'slash';\n\nconst string = path.join('foo', 'bar');\n// Unix    => foo/bar\n// Windows => foo\\\\bar\n\nslash(string);\n// Unix    => foo/bar\n// Windows => foo/bar\n```\n\n## API\n\n### slash(path)\n\nType: `string`\n\nAccepts a Windows backslash path and returns a path with forward slashes.\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-09T21:55:09.388Z", "created": "2013-07-18T23:34:04.300Z", "0.1.0": "2013-07-18T23:34:07.348Z", "0.1.1": "2013-10-08T19:09:41.668Z", "0.1.3": "2014-04-30T22:47:43.354Z", "1.0.0": "2014-08-13T10:15:02.780Z", "2.0.0": "2018-03-23T08:01:41.518Z", "3.0.0": "2019-04-28T04:26:00.409Z", "4.0.0": "2021-04-14T07:07:46.753Z", "5.0.0": "2022-09-27T07:47:46.126Z", "5.0.1": "2023-04-25T19:59:08.127Z", "5.1.0": "2023-05-09T17:07:03.367Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "users": {"passy": true, "tommyzzm": true, "itonyyo": true, "lshearer": true, "iamstarkov": true, "huhgawz": true, "wujr5": true, "cblumer": true, "balazserdos": true, "marcrobinson": true, "vboctor": true, "hsiang": true, "jochemstoel": true, "flumpus-dev": true}, "homepage": "https://github.com/sindresorhus/slash#readme", "keywords": ["path", "seperator", "slash", "backslash", "windows", "convert"], "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "license": "MIT", "readmeFilename": "readme.md"}