#include <vector>
#include <iostream>
using namespace std;

vector<int> v;
v.push_back(1); // 添加元素 1 到 vector 的末尾
v.pop_back(); // 删除 vector 的最后一个元素
int size = v.size(); // 获取 vector 的大小
bool isEmpty = v.empty(); // 判断 vector 是否为空
int firstElement = v.front(); // 获取第一个元素
int lastElement = v.back(); // 获取最后一个元素



//对于静态数组，可以使用sizeof运算符来计算数组的长度。sizeof运算符返回数组所占的内存字节数，将其除以单个元素的大小即可得到数组的长度。例如：

#include <iostream>
using namespace std;

int main() {
int arr[] = {1, 2, 3, 4, 5};
int length = sizeof(arr) / sizeof(arr[0]);
cout << "数组的长度为：" << length << endl;
return 0;
}

//对于字符串操作
#include <iostream>
#include <string>
using namespace std;

int main() {
string str1 = "Hello";
string str2 = " World";
string str3;
// 复制
str3 = str1;
// 连接
str3 += str2;
// 长度
int len = str3.size();
int len1=str3.sizeof();
int len2=str3.strlen();
int len3=str3.length();
// 查找
size_t found = str3.find("World");
// 替换
str3.replace(found, 5, "C++");
// 插入
str3.insert(6, "to ");

cout << str3 << endl; // 输出 Hello to C++
}

//引入stl之后，加入了size()函数来计算容器长度