# 类与对象

![image-20241126214426999](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126214426999.png)

![image-20241126214648260](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126214648260.png)

```java
package 类与对象;

import java.sql.SQLOutput;

public class person {
    //特性-属性-名词
    String name;//姓名
    int age;//年龄
    double height;//身高
    //特性-方法-动词
    void study()
    {
        System.out.println("学语文");
        System.out.println("学数学");
        System.out.println("学英语");
        System.out.println("学化学");
        System.out.println("学物理");
    }

}
```

```java
package 类与对象;

public class test {
    public static void main(String[] args) {
        //对person类的对象创建实例
        person p1=new person();
        p1.name="颜一涵";//对person类对象的属性进行定义
        p1.age=30;
        p1.height=160.6;
        //读取属性,
        System.out.println(p1.age);
        System.out.println(p1.name);
        p1.study();//直接用对象来调用其中的方法
    }
}

```





