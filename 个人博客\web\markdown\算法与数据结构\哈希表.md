# 哈希表：

## 拉链法：x mod 一个质数，冲突会达到最小

![image-20241119121413705](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241119121413705.png)

```c++
//链表的插入操作
#include<iostream>
using namespace std;
const int N=1e5+10;
int e[N],ne[N],idx,head;
int m;

//idx表示还未被分配值的节点,使用是idx是几不重要,我们可以通过head或者ne[]找到idx对应的节点即可
//head表示头指针指向的下标
//e[i]存储i下标对应的值
//ne[i]表示i指向值的对应指针
void init()
{
    head=-1;
    idx=1;//表示下标从一开始,y总此处用的是idx=0
}
void add_to_head(int x)
{
  e[idx]=x,ne[idx]=head,head=idx++;
}

void delete_the_next(int k)
{
    ne[k]=ne[ne[k]];//删节点idx不变
}

void add_to_k(int k,int x)
{
  e[idx]=x,ne[idx]=ne[k],ne[k]=idx++; 
}

int main()
{
    init();
    cin>>m;
    while(m--)
    {
        char s;
        int x;
        int k;
        cin>>s;
        if(s=='H')
        {
          cin>>x;
          add_to_head(x);
        }
        else if(s=='I')
        {
            cin>>k>>x;
            add_to_k(k,x);//从0开始就是k-1，插入从idx=0开始对应的k为1
        }
        else
        {
            cin>>k;
            if(k==0)head=ne[head];//!k表示k==0,k表示k!=0
            else delete_the_next(k);//idx从0开始就是k-1
        }
    }
   for (int i = head; i != -1; i = ne[i]) cout << e[i] << ' ';
}

```

