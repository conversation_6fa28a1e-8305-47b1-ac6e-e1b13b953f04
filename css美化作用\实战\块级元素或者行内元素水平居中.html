<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        div {
            width: 200px;
            height: 300px;
            background-color: aqua;
            margin: 0 auto;
        }

        ul li {
            text-align: center;

        }
    </style>
</head>

<body>
    <!-- 块级元素水平居中方法首先，它需要有宽度，其次它margin左右外边距设置0 auto; -->
    <div></div>
    <!-- 行内元素或者行内块元素水平居中方法是找到他的父元素，使用text-align：center，相当于把子元素当成文本使用 -->
    <ul>
        <li><img src="3.jpg" alt=""></li>
    </ul>
</body>

</html>