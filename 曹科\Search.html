<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MY百度
    </title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
      }
  
   .container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
      }
  
      form {
        display: flex;
      }
  
      input[type="text"] {
        padding: 10px;
        font-size: 16px;
        border: 1px solid #ccc;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        width: 400px;
      }
  
      button {
        padding: 10px 20px;
        font-size: 16px;
        background-color: #4285F4;
        color: white;
        border: none;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        cursor: pointer;
      }

      /* 新增样式，用于使图标并列显示 */
   .icon-container {
        display: flex;
        justify-content: center;
        margin-top: 10px;
      }
    </style>
  </head>
  
  <body>
    <div class="container">
      <img src="image/百度.jpeg" alt="百度图标" width="200">
      <form action="#" method="get">
        <input type="text" placeholder="输入关键词进行搜索">
        <button type="submit">搜索</button>
      </form>
      <div class="icon-container">
        <a href="zhihu.html" target="_blank"><img src="image/知乎.jpeg" alt="知乎图标" width="100"></a>
        <a href="WeiBo.html" target="_blank"><img src="image/微博.jpeg" alt="微博图标" width="100"></a>
        <a href="CSDN.html" target="_blank"><img src="image/CSDN.jpeg" alt="CSDN图标" width="100"></a>
      </div>
    </div>
  </body>
  
  </html>