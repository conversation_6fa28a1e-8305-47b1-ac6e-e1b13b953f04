# 高等数学：第一章函数极限连续1-1

## 第一节函数

![Snipaste_2024-10-22_04-50-33](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_04-50-33.png)

![Snipaste_2024-10-22_04-52-34](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_04-52-34.png)

​                  当定义域和对应法则一致时，两个函数相同

​		![Snipaste_2024-10-22_04-56-29](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_04-56-29.png)

![Snipaste_2024-10-22_05-03-17](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_05-03-17.png)

只有在内函数值域和外函数定义域有交集时这两个函数可以复合，可以认为是有意义或者更多

# <u>和之前学的有点出入，得有交集才能复合</u>

![Snipaste_2024-10-22_05-12-16](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_05-12-16.png)

![Snipaste_2024-10-22_05-20-45](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_05-20-45.png)

根据习惯我们会把x=f^-1(y)写成y=f^-1(x)的形式,因为二者是相同函数.



举个求反函数的例子

![Snipaste_2024-10-22_05-22-46](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_05-22-46.png)

# <u>这里求反函数可以多学习一下</u>

![Snipaste_2024-10-22_05-24-56](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_05-24-56.png)