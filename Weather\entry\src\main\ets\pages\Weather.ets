import { router } from '@kit.ArkUI';

@Entry
@Component
struct Weather {
  @State currentCity: string = '北京市';
  @State currentTemp: string = '24°';
  @State weatherDesc: string = '中雨';
  @State bodyTemp: string = '26°';

  build() {
    Scroll() {
      Column() {
        // 顶部状态栏
        Row() {
          Row() {
            Image($r("app.media.location_icon"))
              .width(16)
              .height(16)
              .fillColor("#4A90E2")
            
            Text(this.currentCity)
              .fontSize(16)
              .fontColor("#333333")
              .margin({ left: 4 })
            
            Text("切换城市")
              .fontSize(12)
              .fontColor("#4A90E2")
              .margin({ left: 8 })
              .onClick(() => {
                router.pushUrl({ url: 'pages/CityManage' });
              })
          }

          Row() {
            Image('')
              .width(30)
              .height(30)
              .borderRadius(15)
            
            Text("admin")
              .fontSize(14)
              .fontColor("#333333")
              .margin({ left: 8 })
          }
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .padding({ left: 20, right: 20, top: 20 })

        // 主要天气信息区域
        Row() {
          // 左侧温度和天气描述
          Column() {
            Text("26°")
              .fontSize(72)
              .fontWeight(300)
              .fontColor("#333333")
            
            Text("晴")
              .fontSize(16)
              .fontColor("#666666")
              .margin({ top: 5 })
            
            Row() {
              Text("体感温度")
                .fontSize(12)
                .fontColor("#999999")
              Text("28°")
                .fontSize(12)
                .fontColor("#4A90E2")
                .margin({ left: 5 })
            }
            .margin({ top: 10 })
          }
          .alignItems(HorizontalAlign.Start)

          Blank()

          // 右侧天气图标和风力信息
          Column() {
            Image($r("app.media.sun"))
              .width(60)
              .height(60)
              .margin({ bottom: 10 })
            
            Text("风向风力")
              .fontSize(12)
              .fontColor("#999999")
              .margin({ bottom: 5 })
            
            Text("东南风 1级")
              .fontSize(14)
              .fontColor("#333333")
          }
          .alignItems(HorizontalAlign.Center)
        }
        .width('90%')
        .padding({ left: 20, right: 20 })
        .margin({ top: 30, bottom: 40 })

        // 三小时预报
        Column() {
          Text("三小时预报")
            .fontSize(16)
            .fontWeight(500)
            .width('100%')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 15 })

          List({ space: 20 }) {
            ListItem() { this.hourlyWeatherItem("14:00", "27°", $r("app.media.sun")) }
            ListItem() { this.hourlyWeatherItem("17:00", "28°", $r("app.media.cloud")) }
            ListItem() { this.hourlyWeatherItem("20:00", "25°", $r("app.media.cloud_rain")) }
            ListItem() { this.hourlyWeatherItem("23:00", "24°", $r("app.media.cloud")) }
            ListItem() { this.hourlyWeatherItem("02:00", "22°", $r("app.media.cloud")) }
            ListItem() { this.hourlyWeatherItem("05:00", "21°", $r("app.media.sun")) }
            ListItem() { this.hourlyWeatherItem("08:00", "23°", $r("app.media.sun")) }
            ListItem() { this.hourlyWeatherItem("11:00", "26°", $r("app.media.sun")) }
          }
          .listDirection(Axis.Horizontal)
          .scrollBar(BarState.Off)
          .width('100%')
          .height(100)
          .padding({ left: 20, right: 20 })
        }
        .margin({ bottom: 30 })

        // 生活指数
        Column() {
          Text("生活指数")
            .fontSize(16)
            .fontWeight(500)
            .width('100%')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 15 })

          Column({ space: 10 }) {
            Row({ space: 10 }) {
              this.lifeIndexItem("感冒指数", "较易发", "#FF9500")
              this.lifeIndexItem("洗车指数", "适宜", "#34C759")
            }
            .width('90%')
            .justifyContent(FlexAlign.SpaceBetween)

            Row({ space: 10 }) {
              this.lifeIndexItem("穿衣指数", "短袖", "#34C759")
              this.lifeIndexItem("紫外线", "强", "#FF9500")
            }
            .width('90%')
            .justifyContent(FlexAlign.SpaceBetween)

            Row({ space: 10 }) {
              this.lifeIndexItem("运动指数", "适宜", "#34C759")
              this.lifeIndexItem("化妆指数", "易脱", "#FF9500")
            }
            .width('90%')
            .justifyContent(FlexAlign.SpaceBetween)
          }
          .width('100%')
          .alignItems(HorizontalAlign.Center)
        }
        .margin({ bottom: 30 })

        // 未来七天预报
        Column() {
          Text("未来七天预报")
            .fontSize(16)
            .fontWeight(500)
            .width('100%')
            .textAlign(TextAlign.Center)
            .margin({ bottom: 15 })

          Column({ space: 12 }) {
            this.dailyWeatherItem("星期一", "晴雨转多云", "22°", "29°", $r("app.media.cloud_rain"))
            this.dailyWeatherItem("星期二", "多云", "21°", "28°", $r("app.media.cloud"))
            this.dailyWeatherItem("星期三", "晴", "29°", "32°", $r("app.media.sun"))
            this.dailyWeatherItem("星期四", "阴", "22°", "29°", $r("app.media.cloud"))
            this.dailyWeatherItem("星期五", "晴转多云", "23°", "29°", $r("app.media.cloud_rain"))
            this.dailyWeatherItem("星期六", "小雨", "21°", "26°", $r("app.media.cloud_rain"))
            this.dailyWeatherItem("星期日", "晴", "20°", "27°", $r("app.media.sun"))
          }
          .width('90%')
          .alignSelf(ItemAlign.Center)
        }
        .margin({ bottom: 30 })

        Blank()
      }
      .width('100%')
    }
    .width('100%')
    .height('100%')
    .backgroundColor("#F8F9FA")
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Auto)
  }

  @Builder
  hourlyWeatherItem(time: string, temp: string, icon: Resource) {
    Column({ space: 8 }) {
      Text(time)
        .fontSize(12)
        .fontColor("#999999")
      
      Image(icon)
        .width(32)
        .height(32)
        .objectFit(ImageFit.Contain)
      
      Text(temp)
        .fontSize(14)
        .fontColor("#333333")
        .fontWeight(500)
    }
    .width(60)
    .justifyContent(FlexAlign.Center)
  }

  @Builder
  lifeIndexItem(title: string, value: string, color: string) {
    Column() {
      Text(title)
        .fontSize(12)
        .fontColor("#999999")
        .margin({ bottom: 5 })
      
      Text(value)
        .fontSize(14)
        .fontColor(color)
        .fontWeight(500)
    }
    .width('48%')
    .height(60)
    .backgroundColor("#FFFFFF")
    .borderRadius(8)
    .justifyContent(FlexAlign.Center)
  }

  @Builder
  dailyWeatherItem(day: string, weather: string, highTemp: string, lowTemp: string, icon: Resource) {
    Row() {
      // 日期
      Text(day)
        .fontSize(14)
        .fontColor("#333333")
        .width(50)
        .textAlign(TextAlign.Start)

      // 天气图标和描述
      Row({ space: 8 }) {
        Image(icon)
          .width(24)
          .height(24)
          .objectFit(ImageFit.Contain)
        
        Text(weather)
          .fontSize(14)
          .fontColor("#666666")
      }
      .layoutWeight(1)
      .justifyContent(FlexAlign.Start)

      // 温度范围
      Row({ space: 8 }) {
        Text(lowTemp)
          .fontSize(14)
          .fontColor("#999999")
        
        Text("~")
          .fontSize(14)
          .fontColor("#999999")
        
        Text(highTemp)
          .fontSize(14)
          .fontColor("#333333")
          .fontWeight(500)
      }
    }
    .width('100%')
    .height(40)
    .padding({ left: 16, right: 16 })
    .backgroundColor("#FFFFFF")
    .borderRadius(8)
    .justifyContent(FlexAlign.SpaceBetween)
  }
}





