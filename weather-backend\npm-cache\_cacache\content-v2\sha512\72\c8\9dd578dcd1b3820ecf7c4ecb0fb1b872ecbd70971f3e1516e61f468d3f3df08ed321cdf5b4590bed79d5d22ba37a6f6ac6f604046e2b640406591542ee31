{"_id": "ee-first", "_rev": "24-f301685a1c4195d37a082eb6332c56a3", "name": "ee-first", "description": "return the first event in a set of ee/event pairs", "dist-tags": {"latest": "1.1.1"}, "versions": {"1.0.0": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/jonathanong/ee-first"}, "devDependencies": {"mocha": "1"}, "scripts": {"test": "mocha --reporter spec"}, "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "homepage": "https://github.com/jonathanong/ee-first", "_id": "ee-first@1.0.0", "_shasum": "f06454b54ed5921b51011c6b2f45e2f383ca6ea2", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "f06454b54ed5921b51011c6b2f45e2f383ca6ea2", "tarball": "https://registry.npmjs.org/ee-first/-/ee-first-1.0.0.tgz", "integrity": "sha512-5f94jMk+2PxUxjF+1rGVSrVt6Oa0LrA/dAlC8Izni0y6Xy50v+5mvZattumPw77khUj8aJ9u7Tmsm/zGnOeUmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3GTiaryS9B6VpSkU940aoMPLafsmKg2pQ9vpbksiigQIgMRDyRdA3DQf0Kd5xJFILiGScazMA7GlWA8OnNyHuvNQ="}]}}, "1.0.1": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/jonathanong/ee-first"}, "dependencies": {"sliced": "0"}, "devDependencies": {"mocha": "1"}, "scripts": {"test": "mocha --reporter spec"}, "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "homepage": "https://github.com/jonathanong/ee-first", "_id": "ee-first@1.0.1", "_shasum": "8d5e4fdba0f4a7cb8e790731cd275f938ee42f59", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "8d5e4fdba0f4a7cb8e790731cd275f938ee42f59", "tarball": "https://registry.npmjs.org/ee-first/-/ee-first-1.0.1.tgz", "integrity": "sha512-Mf7JcVM0NEwmWs1sFYiHGwdiExqvuTwmooWyYAPCAHPoHBMc0/39N375ZugjSBLzhrf5ogDhIAmmSQCeqV9HAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjjrRvnfySCw1WuaDd91cD9b2o6klOMwdha/2m547t2AIgX52zt8EhH/a7EtevPwi+uWtfahXjEKmoCaoB3f96bN4="}]}}, "1.0.2": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/jonathanong/ee-first"}, "devDependencies": {"mocha": "1"}, "scripts": {"test": "mocha --reporter spec"}, "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "homepage": "https://github.com/jonathanong/ee-first", "_id": "ee-first@1.0.2", "_shasum": "775dd7c8ed52233f2339563ae4d33c9a9cb2be3c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "775dd7c8ed52233f2339563ae4d33c9a9cb2be3c", "tarball": "https://registry.npmjs.org/ee-first/-/ee-first-1.0.2.tgz", "integrity": "sha512-D1WhxXQ6NmOvGbCREvXTutonL8mdPB2O90PyEzfvRE6vYHAb6bK10vipgCaLebVgLQOnIuWJJRm7rMlTAN6+PQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3YB/KyM7sCywH1rGrr4zuS7QMUks7gvfInRp5r/213QIhAL+wpgMiDHyJmHCz0yMCJEx8HlfHRsQHgtXQmpYsOvtl"}]}}, "1.0.3": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/jonathanong/ee-first"}, "devDependencies": {"mocha": "1"}, "scripts": {"test": "mocha --reporter spec"}, "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "homepage": "https://github.com/jonathanong/ee-first", "_id": "ee-first@1.0.3", "_shasum": "6c98c4089abecb5a7b85c1ac449aa603d3b3dabe", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6c98c4089abecb5a7b85c1ac449aa603d3b3dabe", "tarball": "https://registry.npmjs.org/ee-first/-/ee-first-1.0.3.tgz", "integrity": "sha512-1q/3kz+ZwmrrWpJcCCrBZ3JnBzB1BMA5EVW9nxnIP1LxDZ16Cqs9VdolqLWlExet1vU+bar3WSkAa4/YrA9bIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrhzOIlmXMJr9kGJlyv7aZP3Z58dME944hI76eryN8kAIhAKpDFXPBbuQbEm6MUwHjLgli3jSvaXHgt0o0ucRbS8HM"}]}}, "1.0.4": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jonathanong/ee-first"}, "devDependencies": {"istanbul": "0.3.0", "mocha": "1"}, "files": ["index.js"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "fbd53b0166f23d7712d551c4dd01673f87a74e88", "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "homepage": "https://github.com/jonathanong/ee-first", "_id": "ee-first@1.0.4", "_shasum": "44dbff0250fe667e8a042ec445afd1a9c4a1aeb2", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "44dbff0250fe667e8a042ec445afd1a9c4a1aeb2", "tarball": "https://registry.npmjs.org/ee-first/-/ee-first-1.0.4.tgz", "integrity": "sha512-Y42uJSuzTWPhwSJBym9LqkRuSlQ/Z6yngbXszFWFnB5/Ob0amsAHLZTndneHwFByvjbIfFwN3i9TWOXltYaD6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUqWrfewoFoWTZJWyHx1JmHS6F2HX/68KVjgzzpl1F6AIhANIxBNLYxam/e0v5DcWCUy0t8C3N4P1NnrqezIRtIAKd"}]}}, "1.0.5": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.0.5", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jonathanong/ee-first"}, "devDependencies": {"istanbul": "0.3.0", "mocha": "1"}, "files": ["index.js", "LICENSE"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "c9d9a6881863c0d2fcc2e4ac99a170088c205304", "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "homepage": "https://github.com/jonathanong/ee-first", "_id": "ee-first@1.0.5", "_shasum": "8c9b212898d8cd9f1a9436650ce7be202c9e9ff0", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8c9b212898d8cd9f1a9436650ce7be202c9e9ff0", "tarball": "https://registry.npmjs.org/ee-first/-/ee-first-1.0.5.tgz", "integrity": "sha512-+FCut34oNiJD2jD+YL/onRxOHF5ut3xOGgTIyEIOdYfun8AexYhEyurzv9izwhTft1Z7pdy4VlTq51K/sIsQRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEq75IncPVwTXNxxg7kpCfMCZhsG3lbKyEIlyUpJI+86AiA0CnBwddDsyvgdbHGLzRw3g8wUp2W4jr6e1/31e+DKxg=="}]}}, "1.1.0": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jonathanong/ee-first"}, "devDependencies": {"istanbul": "0.3.2", "mocha": "1"}, "files": ["index.js", "LICENSE"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "a6412004da4745941af2fc98ec30c8da570da7ea", "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "homepage": "https://github.com/jonathanong/ee-first", "_id": "ee-first@1.1.0", "_shasum": "6a0d7c6221e490feefd92ec3f441c9ce8cd097f4", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6a0d7c6221e490feefd92ec3f441c9ce8cd097f4", "tarball": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.0.tgz", "integrity": "sha512-n4X/DaHVKHyDy1Rwuzm1UPjTRIBSarj1BBZ5R5HLOFLn58yhw510qoF1zk94jjkw3mXScdsmMtYCNR1jsAJlEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICqZASfz/TudnMeMiQ0MhUIIvTyjQDI4JwrusLQ3XRceAiBmxGof20xATi35gSTlhvWVKQHm4/VXYFFX7Gbgb+fmcw=="}]}}, "1.1.1": {"name": "ee-first", "description": "return the first event in a set of ee/event pairs", "version": "1.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jonathanong/ee-first"}, "devDependencies": {"istanbul": "0.3.9", "mocha": "2.2.5"}, "files": ["index.js", "LICENSE"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "gitHead": "512e0ce4cc3643f603708f965a97b61b1a9c0441", "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "homepage": "https://github.com/jonathanong/ee-first", "_id": "ee-first@1.1.1", "_shasum": "590c61156b0ae2f4f0255732a158b266bc56b21d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "590c61156b0ae2f4f0255732a158b266bc56b21d", "tarball": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUtx4H3sjHTSa+WQT4//6YPwB6kNDV57J5TMwfv79tvgIhAPnFJlXNMI9X0jWY8b0gWz+kAdwtnfK5HMJt5o2W8Dmk"}]}}}, "readme": "# EE First\n\n[![NPM version][npm-image]][npm-url]\n[![Build status][travis-image]][travis-url]\n[![Test coverage][coveralls-image]][coveralls-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n[![Gittip][gittip-image]][gittip-url]\n\nGet the first event in a set of event emitters and event pairs,\nthen clean up after itself.\n\n## Install\n\n```sh\n$ npm install ee-first\n```\n\n## API\n\n```js\nvar first = require('ee-first')\n```\n\n### first(arr, listener)\n\nInvoke `listener` on the first event from the list specified in `arr`. `arr` is\nan array of arrays, with each array in the format `[ee, ...event]`. `listener`\nwill be called only once, the first time any of the given events are emitted. If\n`error` is one of the listened events, then if that fires first, the `listener`\nwill be given the `err` argument.\n\nThe `listener` is invoked as `listener(err, ee, event, args)`, where `err` is the\nfirst argument emitted from an `error` event, if applicable; `ee` is the event\nemitter that fired; `event` is the string event name that fired; and `args` is an\narray of the arguments that were emitted on the event.\n\n```js\nvar ee1 = new EventEmitter()\nvar ee2 = new EventEmitter()\n\nfirst([\n  [ee1, 'close', 'end', 'error'],\n  [ee2, 'error']\n], function (err, ee, event, args) {\n  // listener invoked\n})\n```\n\n#### .cancel()\n\nThe group of listeners can be cancelled before being invoked and have all the event\nlisteners removed from the underlying event emitters.\n\n```js\nvar thunk = first([\n  [ee1, 'close', 'end', 'error'],\n  [ee2, 'error']\n], function (err, ee, event, args) {\n  // listener invoked\n})\n\n// cancel and clean up\nthunk.cancel()\n```\n\n[npm-image]: https://img.shields.io/npm/v/ee-first.svg?style=flat-square\n[npm-url]: https://npmjs.org/package/ee-first\n[github-tag]: http://img.shields.io/github/tag/jonathanong/ee-first.svg?style=flat-square\n[github-url]: https://github.com/jonathanong/ee-first/tags\n[travis-image]: https://img.shields.io/travis/jonathanong/ee-first.svg?style=flat-square\n[travis-url]: https://travis-ci.org/jonathanong/ee-first\n[coveralls-image]: https://img.shields.io/coveralls/jonathanong/ee-first.svg?style=flat-square\n[coveralls-url]: https://coveralls.io/r/jonathanong/ee-first?branch=master\n[license-image]: http://img.shields.io/npm/l/ee-first.svg?style=flat-square\n[license-url]: LICENSE.md\n[downloads-image]: http://img.shields.io/npm/dm/ee-first.svg?style=flat-square\n[downloads-url]: https://npmjs.org/package/ee-first\n[gittip-image]: https://img.shields.io/gittip/jonathanong.svg?style=flat-square\n[gittip-url]: https://www.gittip.com/jonathanong/\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "time": {"modified": "2022-06-16T05:43:33.978Z", "created": "2014-06-11T01:31:59.902Z", "1.0.0": "2014-06-11T01:31:59.902Z", "1.0.1": "2014-06-11T01:50:57.486Z", "1.0.2": "2014-06-11T03:50:16.688Z", "1.0.3": "2014-06-11T04:22:46.000Z", "1.0.4": "2014-08-15T20:04:59.878Z", "1.0.5": "2014-08-15T21:20:58.889Z", "1.1.0": "2014-10-22T05:53:29.380Z", "1.1.1": "2015-05-25T19:18:28.732Z"}, "homepage": "https://github.com/jonathanong/ee-first", "repository": {"type": "git", "url": "https://github.com/jonathanong/ee-first"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/jonathanong/ee-first/issues"}, "license": "MIT", "readmeFilename": "README.md", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "users": {"zhangyaochun": true, "bapinney": true, "monjer": true, "mojaray2k": true}}