{"_id": "graceful-fs", "_rev": "224-fb1cc0455f1b64384efb7c1f7ad5778a", "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "dist-tags": {"latest": "4.2.11", "old": "3.0.12"}, "versions": {"1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs with incremental backoff on EMFILE", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": "0.4 || 0.5"}, "dependencies": {}, "devDependencies": {}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/graceful-fs/1.0.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "graceful-fs@1.0.0", "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10-pre", "_defaultsLoaded": true, "dist": {"shasum": "ba8e39479ec46658d59eb305f878f8b0820fa8e5", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.0.0.tgz", "integrity": "sha512-1ZK4pj7qHG1b/vOewFQ0ac6p7NgJThZXUUUzkrwtatP0+Z0Ld+MTYTKt6NclZimV53OrmvpunhbQqV3QO8WAzw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH51xtOVsRHZl5mXcfJjWhMql+vdVsNdYvPZIjjtgLXWAiA037ltCJeXavZBRg97X9NIco205nBnkfrBErQ174bFLg=="}]}, "scripts": {}, "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs with incremental backoff on EMFILE", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": "0.4 || 0.5"}, "dependencies": {}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.0.1", "_engineSupported": true, "_npmVersion": "1.0.30", "_nodeVersion": "v0.5.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "63647ef7ca9bf0abc561cdb72d2a58704a11cc2f", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.0.1.tgz", "integrity": "sha512-S01sFcrVzoLg+ytMrCcA9EOIah0uJE7fN7xv8WHtvG71AG1ri+rRRMyCzf1JuC62CJTT1serfB8L9Dp9E8YFNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBtR7GmpZAUJJvndEXxXWvxmzCMmHyMt4WrS/sJ9gJToAiEAuRdL+ss/7knzq1P/CQlcN0hTpDew+3FkSeW42Oflsgc="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs with incremental backoff on EMFILE", "version": "1.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": "0.4 || 0.5 || 0.6"}, "dependencies": {}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.0.2", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.2-pre", "_defaultsLoaded": true, "dist": {"shasum": "79ac9f685c97c391d88a95e4cde5a1313c3807de", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.0.2.tgz", "integrity": "sha512-nXNqJJ+6MqHBQdiLVEOXgzRzhM0ikh7ux60A71xinVgQ9SggcP9xCiRFVeda6EIw9JuZg7z/HY14IJzg4kjE8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH+Mr0V6mvIYc1qyjxQlOGALlDpAzrJIKt1F3Poy2Z+XAiB35klmmnn4O5rYd1c85cXP26N56KrXCgW76sOcokPFdA=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": "0.4 || 0.5 || 0.6"}, "dependencies": {"fast-list": "1"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.0", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-2", "_nodeVersion": "v0.6.2-pre", "_defaultsLoaded": true, "dist": {"shasum": "c36f1d3b31d71b4cef3da303b784074f6d578037", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.0.tgz", "integrity": "sha512-niMszp/VBKUAZu3jbGOz2BtotCAyuJxU6fkmvGkChphgTBqj4u4sYR70NYDcM3HJphToFL4MLB+17y1npHcFJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHlHzsYS14mirPbswzO4TvJv06WfR7T+ZG3FMgFPIBnGAiEA26ru2MP6rtHRE/spz610J2mszp9Sq91R/PVfCfASaLw="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": "0.4 || 0.5 || 0.6"}, "dependencies": {"fast-list": "1"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.1", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-2", "_nodeVersion": "v0.6.4-pre", "_defaultsLoaded": true, "dist": {"shasum": "2f10989f7e9addfcea6592d95f52bb0c2d7e5bd2", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.1.tgz", "integrity": "sha512-xX51oCGuXYas/cfXPswn7EGkcQB59Ib66NuO2N6Dy1LFb0uUFhx7GwddbtPK0g0VcOB8zJWfuOCFGrAW/sBZ9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF36jFPx8VB/vVA24lTSIvd9NpVAqn5Z1hBxnOKH181AAiEA2L7qRjxbEipe5V25D9vN7C/gHAw3DAunMAkd3XuYSNE="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": "0.4 || 0.5 || 0.6"}, "dependencies": {"fast-list": "1"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.2", "_engineSupported": true, "_npmVersion": "1.1.0-alpha-6", "_nodeVersion": "v0.6.4", "_defaultsLoaded": true, "dist": {"shasum": "e82181f54de6620c67034e736fbc0d8fee8c1ffa", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.2.tgz", "integrity": "sha512-mO5kDs1Z2wRrCgMbFeQz6hW2QuubBfq2Ofuja947/3TapCKQ2DhKXCvtdqQBYAMmNLQ3zcRqYVj+VQ6Y84x9bQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTBC/68nb8oIREOFs9w+ycEC/FDJk5qp99t/IM7TEHfgIgD/xLAjnFcOiWDdSu5pnxCaWdJIesLVABu/lOPisHSuo="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.3", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "dependencies": {"fast-list": "1"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.3", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "7c5264a5fd5888cf02545898e402502ff01150ae", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.3.tgz", "integrity": "sha512-TzDIJQvlRm0HvWwNpEhzjLS9Gkzc2chLSwK8HkV5uLtXLWyoYuoDeDBB9h1uOc/qN6zSiB/u3MyTb0jlLfSJbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDX5GDIROu4DAiYLZ4xWbRJBzZMHcd5VOYBGW5Y0cjQLwIgU8nFiAimpaXbTvn6IdTIZZpQf/lB9D4lX3ftM3CmW9o="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.4", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "dependencies": {"fast-list": "1"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.4", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "d53940783394758e59b24e10d355cbcf8c225103", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.4.tgz", "integrity": "sha512-/4meWbMop2faRXSe+mTIvvbhMXJZOO22slaGyHdAh4ChBXot71z0vvrGZgPUrbf9co3ATTZmyNWVuQx2Ku4eNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA9kqfx4h0P0uo8/A5eNIAx70c+OAYq9GzjDDY+lGx0CAiEAxrEieyEbnH8nRpDPdX3dFZK8xVJJM1DvhtyfeRGlPGA="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.5", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "dependencies": {"fast-list": "1"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.5", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "0e4692e2240d3951c135587ff7dc8c557186d037", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.5.tgz", "integrity": "sha512-dCktlWOQ5aKKs3vMS8RENFzcARKPdOczzkcZRnxVTniAx5221IAJYhzjZUKnYhhbuu6xAZcMoABBIdVZmDB/6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBvpbqwcPxbkc+Ukqzmrt4TkJHAgiFt/j16K6OdVWTv0AiEA2LE1OmroNfVTrY27qDsG5Ud57xu++oyp2K/yA9mnJ8E="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.6", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.6", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.10", "_nodeVersion": "v0.7.7-pre", "_defaultsLoaded": true, "dist": {"shasum": "cb87fa245e5669fb7b1da44ceef5920054052e24", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.6.tgz", "integrity": "sha512-RfMMxW7NwrH8dkCGGnez4mNs4kbbM3UsACoVM0KQ4fGo6jLfIW+NPMOZl7TKNjWk9kJLKyeaByZ1aZWBVK9gIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGncaBoMN85VsTwIFB7Qe15RlWhCEHLQ/IYD/ihpzMbpAiEAr0Q4nWy6hnmY+pCqixDFgSlJdUrj0PDYIoMkUQGi8Gg="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.7", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.7", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.10", "_nodeVersion": "v0.7.7-pre", "_defaultsLoaded": true, "dist": {"shasum": "e5cfd7904de546273c1c461ddd053e44e84bc0d7", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.7.tgz", "integrity": "sha512-YdiHtrX6vJchmLqbgFUcpMWMfwA7lDkmKFalhHoOZR1ZTvbPLGJnJSinRqVofnbL6yrfp01JJSbL2KQsJieULw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAQqICGc4VbRkLu7pmxYNo9zu4moOMNjmHvj7TtoxHY9AiEA6QlPG3dOsEJohOlfKyVhIBDlCbd3I1BPGY53Z1K1XUw="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.8", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "graceful-fs@1.1.8", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.10", "_nodeVersion": "v0.7.7-pre", "_defaultsLoaded": true, "dist": {"shasum": "7c9b7cd96f16b83230f61d522156306500914888", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.8.tgz", "integrity": "sha512-LyJG5EnzvAjVxmtVzuz/IOFSkK7dOO3WFx16dFncAcrKWaSynxMNaX+QAl1ZyipG5zv/4R5+NibFUy1wFXhLGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwR1F5pBJKybZNBHfOmwRDvdlPquwcnWEnVCwkinzauQIhAPewCIGkantTZ6Miz62hMuQzuiO9nLftmoW9WCNpRnyb"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.9", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "devDependencies": {}, "_id": "graceful-fs@1.1.9", "dist": {"shasum": "2d8916e828b906a921d7e7de8fc2ba148f03b18a", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.9.tgz", "integrity": "sha512-RBMZDyVTNMs3/l81ImK8Pvo31aozTD8SeEiZYlCXG9O3oe0WMTwaRNK+p035S2mVMiObpAB+fYmEAk/FZABlLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1Nr6XiKe+aSTSMw/4RIDb1eib780e8ibi4F4lElaNcgIgK+ir97yBI8Sv7Ocj+xP33LIbFt0OYrIiG5SnUSUxWyo="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.10", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "devDependencies": {}, "directories": {"test": "test"}, "dependencies": {}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "EMFILE", "error", "handling", "monkeypatch"], "license": "BSD", "_id": "graceful-fs@1.1.10", "dist": {"shasum": "388a63917e823bc695afd57c76d7f3ee3db54ad3", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.10.tgz", "integrity": "sha512-FqQMNo/HpyZvP5DcS9aCtptdr2zyl9oLZ07+RmxTqFsXSp5BG8sMhSAo8qjFG0DKCRuTg4gXlUG1onL4e20tgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOrSDfLJE+Rw9raWBLPNy5TCxffNsZ6NGG+gI325ZlxAIhAI8g0+Z33lCvu+utbII+30lzKA+KV1/osVqPJ833hjF+"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.11": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.11", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "devDependencies": {}, "directories": {"test": "test"}, "dependencies": {}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "EMFILE", "error", "handling", "monkeypatch"], "license": "BSD", "_id": "graceful-fs@1.1.11", "dist": {"shasum": "3a3de260cc4cc80ae13debf31b71f73c2c5eb5e5", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.11.tgz", "integrity": "sha512-oxHSMbM2kW/djTqoUKDSJ9aguFJ00BhVKLbeB0BEIj/3dcsZlJYG+n4qhFJjaMalm5MU3/KoB7DrKRSB0HqtyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBzgu/y0Qm2/oOmByoK8hIVUm5NFSz3MeYdIqnDq8n2NAiAwJURj1orerWIpdKzOPjlEsJBqgA38/+dLMhdhY4j32A=="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.12": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.12", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "devDependencies": {}, "directories": {"test": "test"}, "dependencies": {"mkdirp": "~0.3.4"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "EMFILE", "error", "handling", "monkeypatch"], "license": "BSD", "_id": "graceful-fs@1.1.12", "dist": {"shasum": "baff9d5d87b722ecef6615ea301a5cb9e2860038", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.12.tgz", "integrity": "sha512-N1c2e3rZ6cvp20QsCftUvcTgASU0hSJIl529jxBl3KXgTGzLv0VWTjIB12J2NwB1AZ4nXaQHg5Z+xr6G2hSAaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBq79nWaZ7NntWi+Nq+Ill96LvhLrgqa85EuRHO9bF+LAiAwh+LuYzFO22WzUARe/EwvNMdvQZyIiunUgbsWldY9bg=="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.13": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.13", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "EMFILE", "error", "handling", "monkeypatch"], "license": "BSD", "_id": "graceful-fs@1.1.13", "dist": {"shasum": "a91e1d8231dc083bdaa227983fbdf5010944ca14", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.13.tgz", "integrity": "sha512-t+xYXtifz1BtC/CWrAvB5WZXQUCQDHf10H2zmFkjTZ8e9AXuy9MsQ71sv33lVrmTMOql4hw2qZhH2yZR/B+dGw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGbhcwB/T4YmN7tVoK5W4IBOg8cO9zIPqMjPBBa6q2hnAiEAmYrt85KU9ut8a2N+/pinlH9B77q2ofmzixcnp/epm60="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.1.14": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.1.14", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "EMFILE", "error", "handling", "monkeypatch"], "license": "BSD", "_id": "graceful-fs@1.1.14", "dist": {"shasum": "07078db5f6377f6321fceaaedf497de124dc9465", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.1.14.tgz", "integrity": "sha512-JUrvoFoQbLZpOZilKTXZX2e1EV0DTnuG5vsRFNFv4mPf/mnYbwNAFw/5x0rxeyaJslIdObGSgTTsMnM/acRaVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOAj/fxXooZIZJo+70Z7MWPXvjuPpyEhxroRPWe9ZWbwIgFPl1XllLuwZgxOtx2xJgaMLBj23ooPQNQYu8+5lHqQM="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "fs monkey-patching to avoid EMFILE and other problems", "version": "1.2.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "EMFILE", "error", "handling", "monkeypatch"], "license": "BSD", "_id": "graceful-fs@1.2.0", "dist": {"shasum": "fe2d82a295e30de4e1d1c04ec159e10061140704", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.2.0.tgz", "integrity": "sha512-PbMnc4gdO8Pmvrh3OKc88fWIS1f8WlecRyvW97ul6SQzZ7aIM+XALIldtdxqSnmyb7bmPSI10qDEtfl3Hhh2WA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFaqiiQ7ZckqQ61KnshsLmPqzOmxRswDcx7q8levOSxAIgPJpAhfnHcKgMRnBk9ODOMJiuZA36mgbDA8zzX77RmXY="}]}, "_from": ".", "_npmVersion": "1.2.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.2.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "1.2.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "_id": "graceful-fs@1.2.1", "dist": {"shasum": "b35cc6e623576fc2a278cba12c00dda6a1758d2d", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.2.1.tgz", "integrity": "sha512-2zW6i2wt4J0pWnSVW5JZhUB7PkzLp0kOL1VDtYnimGtLZ+5CA1ZDQzLLcz4MuGjpd8sN2rHWT46+CWlUlKZAww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDznVwB2BMN2bMQvfa4TtEJpizx2CrytKz/06VQwDjjYAIgWnyxnbTeZ3q7/C/+Fb7meVdgON0+SRc734YtWg0Ucf4="}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.2.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "1.2.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "_id": "graceful-fs@1.2.2", "dist": {"shasum": "2643e33eaed1c7277decd37377ff9fb394689cf5", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.2.2.tgz", "integrity": "sha512-fqggsCPx/Qm0Ri09QHB+9GO6pcrkwjBNuga65PTeFvew8leObGQVbcHlyDTE9/bjCQg4XnquosrAafrgkWuWug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtIN/xqiEaF/VTGT1wvkRtTCu84NmBUI0wPosOLT4G8AIgMTQC+5fti5wR9fiZ9REb7Raggi3j5I7v49EmR+F2QQM="}]}, "_from": ".", "_npmVersion": "1.2.28", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "1.2.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "1.2.3", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "_id": "graceful-fs@1.2.3", "dist": {"shasum": "15a4806a57547cb2d2dbf27f42e89a8c3451b364", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-1.2.3.tgz", "integrity": "sha512-iiTUZ5vZ+2ZV+h71XAgwCSu6+NAizhFU3Yw8aC/hH5SQ3SnISqEqAek40imAFGtDcwJKNhXvSY+hzIolnLwcdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICb0VtrcsbpchLlzfHamE2fb/ypnAIZx2vqefXDDlEGNAiAHJq1NFI/xYv6lHk+PcYkjmIUyPfEu6mst/SNCI/Y5tA=="}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "_id": "graceful-fs@2.0.0", "dist": {"shasum": "c9a206f6f5f4b94e1046dfaaccfe9e12d0ab8cef", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-2.0.0.tgz", "integrity": "sha512-xnpYDf+IXR5vLfLqdYu6G8IOyEka5ge8iw5oNYgAwFKgPT4o6E3k9JyXH7yMEAeDf99ZjH31WL1yj/tt7+wWEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARNUFpoYDTmMWerHqWUEbNNcGVk9JwDIv0JWqXZ9G0nAiAixCaurQhqHt5ljbL+vVZ9keEew/5Wr7EcruVH8mmNnA=="}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "2.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "2.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "_id": "graceful-fs@2.0.1", "dist": {"shasum": "7fd6e0a4837c35d0cc15330294d9584a3898cf84", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-2.0.1.tgz", "integrity": "sha512-oJifEVPN+MOe0kEHBEF5ealVyB62w6iTwCINpkY7vA1lmEzDvq6UiotxvwWtke7fPrz5yM5y4rPwdLclgbDhOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVFgBM52A31pW7L0VZvPd5no98m/uCfMlQmH3DOC40RwIhAOHpE5nOBDlcJuR6zY4Vt3WlsGESwPgxhmIYt44fCcjx"}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "2.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "2.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@2.0.2", "dist": {"shasum": "26806eaca4bff8fc5dbc935e696135792175c46f", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-2.0.2.tgz", "integrity": "sha512-k/FIBmqEmSO5e0rCmOuutDOZbRa3+cyRZAmK4l7vl1tHk5B5vdFsWw5GQX7RIjnqwKsZbUsAdsG6Yj9VFmFE7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFK9/VeAgaEimwQLmK6qvX225rLPaJx9tuPDlTDOyxr6AiEA/3aB19Oda3f7E2p5Y4gPTrIw93CXfJG7PJIaphYULfs="}]}, "_from": ".", "_npmVersion": "1.4.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "2.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "2.0.3", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@2.0.3", "dist": {"shasum": "7cd2cdb228a4a3f36e95efa6cc142de7d1a136d0", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-2.0.3.tgz", "integrity": "sha512-hcj/NTUWv+C3MbqrVb9F+aH6lvTwEHJdx2foBxlrVq5h6zE8Bfu4pv4CAAqbDcZrw/9Ak5lsRXlY9Ao8/F0Tuw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBEUx8/8ictipZPlhPinsTLW49asbKF9r0OjdE+t4RLkAiEA5Nf3wyZZqzJwkuwUebz0BtnpEtovbFOBl+MhaPEvWDc="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "gitHead": "cc05f35cf7fadb0d93587484cd0d01875537834a", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@3.0.0", "_shasum": "5792ffae0ed7e318060ebf9f6e7a6e6cf5139327", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "5792ffae0ed7e318060ebf9f6e7a6e6cf5139327", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.0.tgz", "integrity": "sha512-v0wRI/PEfuFxOF3WANJsQ8JD3KF5xyhQu9OW/5GLRuZopm+Mh3CRgQt5xAB000F6ULGZHIa7eo0A+D9s/e//8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFUlGYv629UUjiuQYWWZkMQeauKahtIEegQmigGmB8rDAiEAhtrqH19c5WolfhaJOSEBPnN1574FpShuVXZAzNF1VNw="}]}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "gitHead": "8455559537c99f36ee3a7e129fc24fae50cd947d", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@3.0.1", "_shasum": "93352b5b951b009ef541271204122b612e46edaf", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "93352b5b951b009ef541271204122b612e46edaf", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.1.tgz", "integrity": "sha512-6eWPWUUiuLNegNmJHMRls1oVQw9rK5Juog7KRGWCCcLKo4IQTMF5F8scVX4wAVNvsNIlhobUiOhLS0NpC3gZgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDyjHZSsaDJQWLFIYPjtDlaHGfY1YUYnXM6va+vGus/1AiEAnfrtv9GfJtSO1UmwN0foubDjm5Th7knfjZqkNip/OEM="}]}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "gitHead": "0caa11544c0c9001db78bf593cf0c5805d149a41", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@3.0.2", "_shasum": "2cb5bf7f742bea8ad47c754caeee032b7e71a577", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "2cb5bf7f742bea8ad47c754caeee032b7e71a577", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.2.tgz", "integrity": "sha512-BSRcIZ8MW0yrX5VzVcvqWTcB/A9AItUlH906JAQxCJvc4v0kefzVI0WvzAYlzasWF9iFJayREGX4IwgOdhWLQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAA6hy3gOLhnksyE/bNMlOGIYBaKsFIkIcc0i56wjOVAIgTcWqutr9RFHYl62/UO/zzNEGdtJtsZRQvVFvUWmK2r4="}]}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.3", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "gitHead": "bff25906a7f1c6944455fed3993ed897fe321d0c", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@3.0.3", "_shasum": "277141085e739ae7d54361119a62797b08a1d8c0", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "277141085e739ae7d54361119a62797b08a1d8c0", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.3.tgz", "integrity": "sha512-MsDT2yY+eIwsJYoaftlH1Uy5J/wYq8I8Tb2BzZDwRuBYy0PHcU3xuKtw4bzXcBytYKDdzFot3U8Dzn+57OjZmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDI9q4iAL9mawrT3zVWAeh6nj7EHimRALAAWVsVUZtW2AIhANe4dIHr41mkhLgMEnQP7llZkUnPLsCN1CFRxfm9brXv"}]}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.4", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^0.4.13"}, "gitHead": "d3fd03247ccc4fa8a3eee399307fd266c70efb06", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@3.0.4", "_shasum": "a0306d9b0940e0fc512d33b5df1014e88e0637a3", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "a0306d9b0940e0fc512d33b5df1014e88e0637a3", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.4.tgz", "integrity": "sha512-YtFEF+a6jPcrkwdSSp/fMyyf98ah8WwzqbCGE08ezw32P71Od0R4+C4+NzWP17TV9W+B5Jw5HCN7L3NwljQIIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICL/u0UPP2Z4EolW8MPsFnFJGGHOgscZZvMdZBDCwOEMAiEAi9b5JnYCmQsxSCaFbZWernY9/LxQdQooKWlHxw07Mt8="}]}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.5", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^0.4.13"}, "gitHead": "a6cd37cff01ac3af8d0ab2fd180290538fabd326", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@3.0.5", "_shasum": "4a880474bdeb716fe3278cf29792dec38dfac418", "_from": ".", "_npmVersion": "2.1.9", "_nodeVersion": "0.10.16", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "4a880474bdeb716fe3278cf29792dec38dfac418", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.5.tgz", "integrity": "sha512-yHWUshRWlTbU8sRsazqtoORKZWFy7JMrnfc5vSFIkOxhJAtS16x25H75/USkpxvjF4wWNC/+7XIYUVxTxvoi2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDFNr7GVHRm1xmY3uCi3DL1snLke9qa3y5+NP761JXVsAiBg4TISZvw0dJD0rTn8p+Zu8RnkfjQHtX939g0kiRXboA=="}]}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.6", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "BSD", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^0.4.13"}, "gitHead": "8c93aeee947014dafa113dcc8fa04d2e6a6e3e14", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs", "_id": "graceful-fs@3.0.6", "_shasum": "dce3a18351cb94cdc82e688b2e3dd2842d1b09bb", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.4.2", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "dce3a18351cb94cdc82e688b2e3dd2842d1b09bb", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.6.tgz", "integrity": "sha512-OtQB/uAOCwmCbUCovDuHfmvLoO0SWbMa4NFCfspSxa9CepMU0Dx8nRkY71Vwjhfl1anTKASVx0dOmuaJSzN6wQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDc1vWH/c2x7hnkF8zqLgq/qHkumwI6cg8UlNgb8ff3QQIhAMRRvGGtqCfKB89ruGCnRchCCyjong0lmB59HI7O920S"}]}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.7", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^0.4.13"}, "gitHead": "4aa1ba4a0a1ae59948dc3b14decb7c4a8ab62bce", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@3.0.7", "_shasum": "e935be4b3e57892d289dc3bef7be8c02779d2b54", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e935be4b3e57892d289dc3bef7be8c02779d2b54", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.7.tgz", "integrity": "sha512-AD5e3ip4xajn1lE7ELRerrYi1VGX12jRPXXjvmypChNF5gttrgr+Ws0UTEO8MKFw+VKSyCrKMYs030nkCpR88w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAYp8cIR2AAet2vsvLh8MMzGtCdNO4tUjsEti8lz9d0VAiEA1GZrngjY1mpVlXPOwbGrXzmXyGks6Ne56DLvuVeu5f0="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.8", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^1.2.0"}, "gitHead": "45c57aa5e323c35a985a525de6f0c9a6ef59e1f8", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@3.0.8", "_shasum": "ce813e725fa82f7e6147d51c9a5ca68270551c22", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.0.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ce813e725fa82f7e6147d51c9a5ca68270551c22", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.8.tgz", "integrity": "sha512-MHtzYnGoN0JXaAdPNFgAB52kgyf1A8bDExO1KWHgw7d4zfnkTz7r1NJl3WZmWiI/5bCUlORF8whjufMNrm91EA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAUqLWU/2YDFEe4DPitljnq4HbHHHv1K9/afSSTtzpF2AiEA2tDYfP6kc2LxEed4iBUXaO8El4fvXJVdbo5eDT3WXJk="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "4.1.0": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.0", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^1.2.0"}, "gitHead": "34f760aeb1fd2ca6aa0d13f0597c4d120ff2f830", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.0", "_shasum": "6be6119244f64d6417fe303cc36ab497b5756cc1", "_from": ".", "_npmVersion": "3.0.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "6be6119244f64d6417fe303cc36ab497b5756cc1", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.0.tgz", "integrity": "sha512-IegL/6BepKvNGusT7JUhgwDf37jPptYEFymvD3fNCjCtDJQApmHbOy1U44pGSkcz5nvWe3z+E1IPZV0gon3v2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHl8ru6Rwz+1Jmy+D4l/raQcyrlkzxsB/JM5YHPArQWgIgZ6q6530hV9x4/Ygu8k6/2q980Qjhp7ldIvi/SVLpyd0="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "4.1.1": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.1", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^1.2.0"}, "gitHead": "5bd1470645a44077f7a46b688833295496d1dca9", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.1", "_shasum": "db940cb46e47bd719314a876bace802ea3e90d3c", "_from": ".", "_npmVersion": "3.0.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "db940cb46e47bd719314a876bace802ea3e90d3c", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.1.tgz", "integrity": "sha512-bvwDB2HRPboahTBkLEhqmkKBKvigK9ghGvtpLxA2iOJYwpAOD71I1dJ4wZmzDBYBX57QdApWAK4iVVKI5I79LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEd0jOb3DHQijXQt0PFjkYxjCu9x0cGqRK/cfXxm6kx8AiEAuVzyxItgYeNmaxO154B8utRWInFQaTaK6bru1cjEmJY="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "4.1.2": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.2", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^1.2.0"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "c286080071b6be9aa9ba108b0bb9b44ff122926d", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.2", "_shasum": "fe2239b7574972e67e41f808823f9bfa4a991e37", "_from": ".", "_npmVersion": "3.0.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "fe2239b7574972e67e41f808823f9bfa4a991e37", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.2.tgz", "integrity": "sha512-M0F8L0fpTAUAkc73Qw6272c7+zc2Se6gX4HIsfpv8vRubJZwU8wkvjbKTvaotgqBw6H8brH8hKL1qbt4HIGnEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6PMne5Ho53pDZ5et08EDBdqGhLHptUD31WMWyecCAJAiEA8mLpyCYbfg458eze6GD+jsDZ70EyhajzDD5UINCWV+s="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "4.1.3": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.3", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "694c56f3aed4aee62d6df169be123d3984f61b85", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.3", "_shasum": "92033ce11113c41e2628d61fdfa40bc10dc0155c", "_from": ".", "_npmVersion": "3.7.0", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "92033ce11113c41e2628d61fdfa40bc10dc0155c", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.3.tgz", "integrity": "sha512-6XLXOGsIlU/IRPoFa+15BpPJ/HNbugYORg24AbM+43ggg8dD53jxxyKk2fAs+jNNPvPZKLzKaXNaAtWqA4Ivqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzASuzVgHS63e4HmPKiAqiqhzoBhqEHMklCmbNv+d0NQIhAPJ48PHhX2wjiTm5nTJZKuBNN9KOZVsv3uI5OQsafY6s"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.3.tgz_1454449326495_0.943017533281818"}}, "4.1.4": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.4", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "fe8f05ccc2779d1dfa6db6173f3ed64f1e9aa72c", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.4", "_shasum": "ef089d2880f033b011823ce5c8fae798da775dbd", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "5.6.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ef089d2880f033b011823ce5c8fae798da775dbd", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.4.tgz", "integrity": "sha512-FJvXqYOJo21W86xfBsnT3Ev1hpRWRNdFfNEYSi/uVq+QmNADuhjwzoLtWc/p6lcqLA+l/g9GucxSHQrmOyJJEg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIC+iPI5dv9cl7wssBT0VH55LKiYQo94IFIqR2KSyxCgIhALWyySHtRhS+8sITpUTwBIOUYHfsrCP+zaJBvUWKwXA/"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.4.tgz_1462474854900_0.9423982477746904"}}, "4.1.5": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.5", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "d170f3f2ceb56adb4d6bd50df4b535bed54120c0", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.5", "_shasum": "f4745e8caed5e0dd2ef21bb5e2d229a32e8093c0", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f4745e8caed5e0dd2ef21bb5e2d229a32e8093c0", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.5.tgz", "integrity": "sha512-8Cpxlk0y+CK0BcGAr4ijS2fHc16VUqdHu/zSAFmyx9ui6yjwWtIlOiSSPAlcEIdp0tc4ZH/fNkDQPzRkecS8dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+bZsOFM2Z9UyUL39Xyomix0zp7KVXY5rFc10K+sZKpAiEAvKxC45jeIBKEas9a38IRmzmS0eT/nvnk9iHaOhWBB14="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.5.tgz_1469558843062_0.986795610981062"}}, "3.0.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.9", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^1.2.0"}, "gitHead": "805a75b5c56d7ac5057af689ccb1bfab9d9b000d", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@3.0.9", "_shasum": "44e10a870a068e892485bace909520905b08ba24", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "44e10a870a068e892485bace909520905b08ba24", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.9.tgz", "integrity": "sha512-zZdkDlLAqosT+PPPEwdj6f7K2liHHjMX0ENtpPegbv4RnMhQyfUtdNOUKnKbTNHcsNwXV8fLHE06P5+J0BqgUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA3Awvyp6qd3Mif1gUC+kdF2rdFeuyM0Syb4dXSWoO1NAiEAw/6XKXz8o12QzhAw48/eHb7bZuVmRTCPRK3BOubDMeQ="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/graceful-fs-3.0.9.tgz_1471056552664_0.889725528890267"}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "4.1.6": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.6", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "cfe3ba80e37af542f02e445c89aa59482aa32a63", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.6", "_shasum": "514c38772b31bee2e08bedc21a0aeb3abf54c19e", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "4.4.4", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "514c38772b31bee2e08bedc21a0aeb3abf54c19e", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.6.tgz", "integrity": "sha512-CWgs7ku1PiRyQqePL0W2z8KDyh3XrNQLLkFOQoKOsvbvz1XpHnh/vhn91FC/0KkR33OQJ/oIHkqxMxU9hBMZjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICDbOM4hqR+dIGzGGGiBKumGO3LRzK4/GSOn20lOzUZLAiEAnL4txxDL7ae5E1ruYOiMqm2IQKHiNFf3dByiVdzR6RQ="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.6.tgz_1471616320359_0.39477095939219"}}, "3.0.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.10", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^1.2.0"}, "dependencies": {"natives": "^1.0.1"}, "gitHead": "889bbe43fca07d904ead98a9908a2a23a8aa5c2b", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@3.0.10", "_shasum": "5268b37746ff73a549708f3ce47fb54c84d5b0f0", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "4.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "5268b37746ff73a549708f3ce47fb54c84d5b0f0", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.10.tgz", "integrity": "sha512-MFW6xcbcIr6z9cmwmFc3VGkmbxBlC4h9BvR5jLwtuKNwcAVCUN1gnPXC/5O9LqIE9dELmceE7P+ApgkfZTtmZg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQZFkiI6YvsIm8P3UUCZ8HeZ2rSU7UO11TSV/Ek4ZyBQIhAMdHJ06C7lcFC2qWYFKNvBW432Ok4un7W7GD9ItvWG6H"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/graceful-fs-3.0.10.tgz_1471913784462_0.3585212028119713"}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "3.0.11": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.11", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^1.2.0"}, "dependencies": {"natives": "^1.1.0"}, "gitHead": "03d3cc6dbd4a499b1e13834eedbc469094a27582", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@3.0.11", "_shasum": "7613c778a1afea62f25c630a086d7f3acbbdd818", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "7613c778a1afea62f25c630a086d7f3acbbdd818", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.11.tgz", "integrity": "sha512-TUMHqvtdbiU5R8XmiHolgo/9mrFPzGlPSDgw9inIIGpCkOPcG3BmRmPdnVuzbBvWIgmVsJQ8ig2cwIpbtr6+ZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcJbudcgnuFpKS/ffrVeoGXjAIEWE5VRMTaBARNHzw0wIhAJ3i0MJmLqHI4LHcI3jVYIkK2notVPy+ARcWdyZamBkQ"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/graceful-fs-3.0.11.tgz_1472583712827_0.6562602713238448"}, "deprecated": "please upgrade to graceful-fs 4 for compatibility with current and future versions of Node.js"}, "4.1.7": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.7", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "1f20dab8025e3762b7214fdfc8ed8308ff80f720", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.7", "_shasum": "f8b39fe797b022ae88024cff94a3613197141f32", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f8b39fe797b022ae88024cff94a3613197141f32", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.7.tgz", "integrity": "sha512-sWVvXwt9aKVC5QVitgz0p/R2JoeN3/J9MObdkb42z7YuZ25NXBy4+8XImv9d3e4klAu1LXufpnzB2nVkSPgMqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE0/s3/12eE4ibG+ixMSgJxdaSXF6tMJ9fxrpuJu4xXhAiEA3Ic8l2ebZlCfCkm4pHgNM7GpGSQfwLGzSeuZ0ps8Ues="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.7.tgz_1474925918236_0.6526234275661409"}}, "4.1.8": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.8", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "9681d92f6ab20e789a1023b008a96b02e66a723a", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.8", "_shasum": "da3e11135eb2168bdd374532c4e2649751672890", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "da3e11135eb2168bdd374532c4e2649751672890", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.8.tgz", "integrity": "sha512-eNLPVSOzjCj0nVWIcHwOVgJXn0oSyQ3Ek9WPANSw6+aHq7laItsaOHNstjqYGTG6DYpGgzMf7q+srvgBA4T/+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFrHwRd1waF0uIaoEUMPChEWtbveETJiDrm/IEzz26YQIhAOGoxl9WtdU2wcK2V+pR8UzxV5sFxX1JylYFupZ1n2sE"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.8.tgz_1474927599542_0.5861048712395132"}}, "4.1.9": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.9", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "0798db3711e33de92de5a93979278bb89d629143", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.9", "_shasum": "baacba37d19d11f9d146d3578bc99958c3787e29", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "baacba37d19d11f9d146d3578bc99958c3787e29", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.9.tgz", "integrity": "sha512-XoXYnP6yTKfmZYVQJ089K4GdLHvvHWDYN9JcR1hskHIjAFrtAlryPmJQ+bgqs/L9fu0OjahI9d3MTgbtNvbiuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXPRdY6pqP1WBlQI0y/XWGnollUcwShPsCfe1mkPrcpAiAPiTFSCwKdhdCYv5zFCRiD7WCiVCZ4Xl9iYnELkpXCRA=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.9.tgz_1475103672016_0.7011275647673756"}}, "4.1.10": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.10", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "db8df443122686696293d47f11c227d53cab90f0", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.10", "_shasum": "f2d720c22092f743228775c75e3612632501f131", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f2d720c22092f743228775c75e3612632501f131", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.10.tgz", "integrity": "sha512-fUSlmTortW+/Fr7OuwVfhHAK3/8Q3J2BxjdHKD2pw9b7fSTEtUmf1Dxc+yByw7r/BDVJT1iWKoLXdAN+qpAKFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEYRvcYekg/nJjtMB6o7pBtJZob1UNHh9EzQN8C0dlqbAiAU9awym46cYAsrZsXc28szgxKvzvWHNfvB6r60Wvk1gQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.10.tgz_1478127353765_0.5264726441819221"}}, "4.1.11": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.11", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^5.4.2"}, "files": ["fs.js", "graceful-fs.js", "legacy-streams.js", "polyfills.js"], "gitHead": "65cf80d1fd3413b823c16c626c1e7c326452bee5", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.11", "_shasum": "0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.5.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha512-9x6DLUuW+ROFdMTII9ec9t/FK8va6kYcC8/LggumssLM8kNv7IdFl3VrNUqgir2tJuBVxBga1QBoRziZacO5Zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIOLhQxQ7fEfpt/KpwESceQ+6DaQr7pvY/nufwsfJh5AiEA0X9JJZZCb2MYincHwEXLCVuQS5gj1r1sDcryszm5nVI="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/graceful-fs-4.1.11.tgz_1479843029430_0.2122855328489095"}}, "4.1.12": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.12", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=6"}, "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.0.1"}, "gitHead": "845e0736c3f23ace6b99f402e7d615a95710cf91", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.12", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-E1kqZR391RPU4xZ4uJSKm42R1ZsN8qcolVVlMKDZ1yGa0HVRcNwhjJ74VGDTriDcOHa/LNzFCDHAOx/jq4F63A==", "shasum": "5f732cae9d5023c66371362e72685e9c6cd42326", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.12.tgz", "fileCount": 6, "unpackedSize": 25559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3LkMCRA9TVsSAnZWagAAizkP/3ROSu1bgKvgHuJ3BP7Q\nFA6TmxzkoAH9K55guUge0xxR7SCZI5YxiwImggAmklVXopSQXLl/jo7EfOpR\nHqyiUwIVnDlBKn9AB7CWRgPkIZRH4rGuu0JMiRqCHXdXboDUKn4qONTfI6mj\nfcAoVpAXpwumAP7LJD2olEnHE1j2fUca8RIaJlAAqw8LSiZd5Hdn/+JpLlp2\nK/9kM54BpPL9xM+IfaM07O59+6/TaNL1OcUb06wa1Qj0MP9jMqQPjrOxSOmQ\noKvOu2Vg6jsRTUkaIMRgsWO9puzhz74LLHe+DzBKqa8wVX97EEDd4Pldxeyx\n8UUzmLqQlSmdiNCY6dkR1F7yOQnbWMT3FBp9+tqlW6/IAA1vLnIwbofj1jJX\nBY0F8wi632qdE2gNti8puFqu79JedDLa/FQKyWppJ1pcLSOCrZK7dd6mGipr\nSmsQZPTXFOJuX9BJxH4sFnps/5hU47dpcnVPcPnTQneifVG1FN7l0Wqd+kpj\nxxbnOAnIZ+0q4L/Ppat1ks7c9R2OrknZM7+Ye8wAMT6VCgUkfxOjFWS11wad\nZ3iAZc6o2WZ1N4FJCsWchMXhceeevDhBhgCuGoOedYFXNKcfa3jPeR7oWRTV\no2/A9bryp6dmYYw+rpKi60xhqV/9Islf0bSfR028kfRBuIpohIGNkxhQqQtT\nic6a\r\n=7wOy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGq5MSOGUItBrqZEbATAyD9AUfaWx5NiSu+LGd31lqZSAiEA+a8IhU8cqNdg3y0OKz9gXRgW16x1u6zm2G7KyFp8bdQ="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.1.12_1541191947577_0.6874103790333079"}, "_hasShrinkwrap": false}, "4.1.13": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.13", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=6"}, "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.0.1"}, "gitHead": "66520dd564c7fbf566c2d8f19c217e5b25af5c6d", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.13", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-a3u3Sck0c7/X622CIqiU0q/eRmhEOssVa3jkrfRRxxHPzaW1EcZgsQt87zKipdteGmFEKGGBvIBPr13pCB2HYw==", "shasum": "9291d55cf899209ec0c2c8445d0ff7f6cc0e2df1", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.13.tgz", "fileCount": 7, "unpackedSize": 25976, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3MFwCRA9TVsSAnZWagAAivkP/AlHtY1y8mmXNgKdLPov\n0ULfG8zMLkNfnKx437uFEHjcyjClOFrrV2nOuQnNBR/HYvWp6kSRoRJMX6eB\nzPhPN7bsR2SZGdIqOKwkoweDyFjrA/BPMaHLYPeoY+OkMZ8SdHcOQ4EelvWM\nmRtXMGjB+6W1KOsBjFxqvRKi7eMmkaJf6tyFRsEhju1RUGmhw1VY+F9fS67s\nbn/DCTujUAHxMhzvmcsBscoiI7WN6q/vP1W8cRHSKe+5INPwISp/M4bo+gU3\ng7XwafgK9bMHgSsC2a7sE0i6zPzlre55bEAvDemE7WsORGagrM2AHJw5Ow1n\nnGulaYd5rit5f2++DPGQ50FaGJtlEFqOqS+J1XldGB69KSvKIqjgyqmoG4VH\nBLv9DflSyO5kJJZggtXKMqqwf/GlXCRij4s5sZVyVqUQ8VK1N95MCF+IWm51\nvyVlyg+ZgnuT8LEiDyxDMx27KbwwxUcN3QPLXvp5zuvDsqlePVMg9AzmsF01\niB6tX/ZddeMF2yTbOV8FRZ7bD23jqbWfSapilKNZBgvLpi2ah3Li9suu1B8v\nz2t/oAU6aqC8PQ1D59Eq8t0eq4wYlKWpIHL24Z/Fj+LUdHw/GnpUy60Tu0GW\npwCoqmZVmP9K9THjahagpXKWxsYaewHr+MhBca+DJ9vjCHv40MrOmgauB7g6\nwCoN\r\n=t9Oq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdVEe0TRcQdp96QQvo0zFFk02UZWD+DdSOd9CV5l3yOQIgNqFwHLkSD4e6xxELEEGSr5PDeZpKqssZ+NwrUCTgCEU="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.1.13_1541194095561_0.7560251567767857"}, "_hasShrinkwrap": false}, "4.1.14": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.14", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=6"}, "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.0.1"}, "gitHead": "778be61e341ebd68a1d4daecb1de7cb6a5c8b426", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.14", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ns/IGcSmmGNPP085JCheg0Nombh1QPvSCnlx+2V+byQWRQEIL4ZB5jXJMNIHOFVS1roi85HIi5Ka0h43iWXfcQ==", "shasum": "1b6e8362ef8c5ecb5da799901f39297e3054773a", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.14.tgz", "fileCount": 7, "unpackedSize": 25976, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3NtKCRA9TVsSAnZWagAAn50P/R41gvttUFl7OysdDnVH\nTUFoUzk5WrVSQMO76cf8gQZ46xC4du+1BQhDFJ3gYvpmv57+6MDsgh2IFfCl\ntm0KsuBFnP9C3z8I47YdIqlMoUG+paoIRPrk0YIlqZddnaaf/BRO/9yr1Hb/\nm0mA4puWt+ceJh+pdSu3UzPFNoSSXr/+ViuVQiDVcxFOHHrWCxyXBrRLFc+8\nVTAinVmUEGLi9CkjlwIz99QbjizBd7INzRGCvCFm17XQlRHeT7e3EtRO2ySM\nvOU7o0K02vrP1XWtW7rXvGDATVJnTACM1yPZIhIXdZVxgD5zAEsTjqvvOAYJ\nfY3vqkHE1uoDTbf3mdT24ZmmaMRrOcOwmUPTa5ZxnIqAwOInOeVSwLUMIN+z\nqK+A4nL98U5L568OxpHlbAtbAgIHCPYADg+fT+8Xj7mIGtx/HBN+75h1WHaq\naGVvkK2kclgXFKXzHzCKfDlHe268rvqoRy+6Q9a1d5Cth1aArO6kTNajc4AX\nfco+TDCEg/oMDgCikHI4qL6zXwQ6aQiJjeuHONprnyrwrRhS9iCFD1DSC37Q\nsglCxNioVpd7TlfDg0v3yDFr8gZqo7a2ELoRmog3rwzgGYZ/VA/FJO/ztfE4\nkPH4rW2tbDldWc8bZ3FkH2TFHgwEw2qTgziPSFwE+V7G0GaDrbFjHslABMaP\nvlOn\r\n=G/Sv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNWB+moBWl9WiMZC1CDloW13xOaQEo9Jar9rOMJyMoFwIgeACdXX41wB+OelAl+j+rXY6pmkufpRuwD6Nkd1liYyQ="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.1.14_1541200713182_0.2201051097755231"}, "_hasShrinkwrap": false}, "4.1.15": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.1.15", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.0.1"}, "gitHead": "26456e3deb4a5e85363e92f9015bcefd3b6b13ba", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.1.15", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6uHUhOPEBgQ24HM+r6b/QwWfZq+yiFcipKFrOFiBEnWdy5sdzYoi+pJeQaPI5qOLRFqWmAXUPQNsielzdLoecA==", "shasum": "ffb703e1066e8a0eeaa4c8b80ba9253eeefbfb00", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.15.tgz", "fileCount": 7, "unpackedSize": 25938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb31vrCRA9TVsSAnZWagAAjMYP/2AwGCPcxbX+2ZBkpyRq\nJ85zkTLDdO2AhSNpgApHrPFbvQ2MYSedMmA/tuNOILCd2zulEvadRZLSW5zI\nHUkRFdU20ByFJoLMvoGm8EY9gEnQ3lpIvh6lmgNSYhQhm5khE0XHQ4sAs9YW\nV61Qr4yqKvkxXrMpElTgzkCfEZ79pNzZ58lE0Qvqmcp3OTczsOimh+w5UIUu\nt/GMZpsmLC4VHTj+hSRqYsh8HJFt9Nl2ahXK08eGAroZMRgRY5hA/9aJ2bA7\nHpuyqMH2wjbaJRnT8ORLfgfJ9a8xO+VzIp0ueAWpAjAaCtbC2dKF3/YJ8Bn6\nZUaCWMM1PZG1BIM/4g/q++OV7o9NgJvqjvjMmVZKanzUMBwfD9ygwFcP8oNF\nlLSR4GU6itDJo2oqdtsrTsfDe35sNTl3kwyhMqZPWFlNJ5C02jmeH2qPtc9s\n69lGDH5JNBBKOmJXV9WKfN+mOSXniBnUsUVu0vAm/y9nY1GZQiAlI5MfIW+T\n1a+K2cGakv6c+b2bO6ExwnIjWPwEpKNlPYHdjLv01GoT9qZRh2guD5RBJqDj\nbNToHu3zR6po45ekShHykoFDz3F+mOKWKyfQ1F3Z+z+8udFk7xbO4vxRTC4M\nq1HSgomC6uFS+RRNVhmTS2L4S1izPDbozKOUU+rpk4hQMBhTLgf7nQ0Bates\nG+db\r\n=Fpd7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3PJ+B2CIUqofvAaEukiS+e36IfAQs//38rxXJ0RF2PAIgdDiDfWDCBIzpSS3jWpLzzQkL9roXaudAXw7MxAPwhSQ="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.1.15_1541364714631_0.3224551402569402"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.0", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "dependencies": {}, "gitHead": "585df780323740a2b562677caa08a80de1f56c62", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-jpSvDPV4Cq/bgtpndIWbI5hmYxhQGHPC4d4cqBPb4DLniCfhJokdXhwhaDuLBGLQdvvRum/UiX6ECVIPvDXqdg==", "shasum": "8d8fdc73977cb04104721cb53666c1ca64cd328b", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.0.tgz", "fileCount": 7, "unpackedSize": 26173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdE8ntCRA9TVsSAnZWagAAhkMP/1wpAg/eCBxcFvsgN55w\n3WotZ+6HJqOB+j5nNC0s78Pqc4E+g9VXNuvKVXlDyx7DwXudJgwY3L2hnmVX\nLItj62qFJ3Zn5KayYsYKnUEWM5/I1iYyExsy+6aQs2KfEAqnQgjcSA3ZN+Pf\nmVLv5NGrtrA/erF8CFjMtApK4GggPSZYjWV8ykWGoZTen67QBbQbwygooM13\ncvfXrVUA80lR00/Yy9pFZcjfijt/2jW427t6LxpevrONA4dviVWg8R+l02Gm\nnrIpvCoqt+FWtnBtJu/K+nORmFdpvetixhNkZY0DDJ+FBW1Qqg5pVHkpcXF+\ngTSGJpGvt2p04y5ZXPNnBx7mL37Nt9zblHnY4tJpzDXE11RSnrRc59cE/6UK\nS58TpbUrNdTNgYH9I0KkLXQ7zWgl1TxUDhEtyOAIH97M8z7XYblGLViv5pQT\nPTJnG61H5EVMC5AJKgnImPhpoS5kofNdp8oKOPQ+2GwsITwNv/RJi/86/3GR\n1QlFu4oW6E/tCg3KkskL3KN04Ohgm3rdJZUXG5t/cQTiGlk0H1CVjDiG/yMW\nWOIpO8EAuN7MkCukI4rKgYhR3BxzJ+N1tdw0sSb80x9/6JmUh9Bl4pZvdH2V\naWVdsJX/KoT6xjwiX5vi8p0fCIQswggUKAvRVygKDKuhKz+ubbtbXtQFwuJF\nwsmn\r\n=YcR0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDv1BZrFebFM20YviZLt6qY+1EelO6Jg9+dttrfj4IlqAIgIaBLtF/uKRl6Bzwrm5VLe2NG+679ZnO/KoLDXsEsDdw="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.0_1561577965134_0.17208797293985856"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.1", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "dependencies": {}, "gitHead": "7e8a5605310d1f995d56dcc2690660c58e5554b0", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.1", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-b9usnbDGnD928gJB3LrCmxoibr3VE4U2SMo5PBuBnokWyDADTqDPXg4YpwKF1trpH+UbGp7QLicO3+aWEy0+mw==", "shasum": "1c1f0c364882c868f5bff6512146328336a11b1d", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.1.tgz", "fileCount": 7, "unpackedSize": 26326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRnV5CRA9TVsSAnZWagAAI+cP/09vQFDgflZI5LDhq7Kn\nPzOQDHXsrAHDvKW1F55RxTFIXjPqExI8byFKOsPRDmeK/grBCEiovq3YGig/\nqC/HpiPuxWBo5WoOqScn7nuvbqN5vtAGjSbA9XN1nQHG+yFLv4A9V6/U6YCi\nVMcwIq4J3FzciBWGQ78R4DvmeCLyKCEXoxxQsHNl8+qrWtnrNF2rWvab4dtV\nypHI6HY2DdI5Lx/trflzyrKe3ycS6SiD+oJ+exxL/YewB4xHHftxVhALUoGS\nyT6AVg4vWz/LWdE2M4IG4jzjb9Mk3bBlpODIc1AlfDib5RHQ62oH9roQiq5A\nYKSWQ4BfOdz/c1c+YjXVEkCaSG5IKd9ULq2I/awfI714sy0CMsdFino29umN\nUODANsl9nrk8RGJ65lIKZVeF4EGFKHF7CcbuBpO5AkMKf32/uIXTkprUWrO4\nMVoaxp5HXasSp8fH1y9CryITWwdgFksnFrlgnUL3l6AaNWzLTajEWQmzeVdj\n3ADQ/GglE6bxJMnMzaD2c5KbS3+CnpqKxIqPbiPFtjyJeNGQCjKx/PgXDsp7\nmNo3KAcbTFYBjc/34sczullnv95TGCZOm6Oyz8XncI1afKqdTPBztf6MXmXl\nNKs+Z2k/p4o95MyLTwHBzRAZfXUhA87StuFijMJhyVD5JPx1Ja88ItqoLzog\nJWsf\r\n=Oyod\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDydIC9/u6hyNplA9t+LB7Sn+x7dDAQSKkTYaXdWS/eHQIgHaWI0QpSxsf8yYT3Qncf2TUE0xyggi3X5Sj4yhWJtZk="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.1_1564898680936_0.4561090715904119"}, "_hasShrinkwrap": false}, "3.0.12": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me"}, "name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "3.0.12", "repository": {"type": "git", "url": "git://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "engines": {"node": ">=0.4.0"}, "directories": {"test": "test"}, "scripts": {"test": "tap test/*.js"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^1.2.0"}, "dependencies": {"natives": "^1.1.3"}, "publishConfig": {"tag": "old"}, "readme": "# graceful-fs\n\ngraceful-fs functions as a drop-in replacement for the fs module,\nmaking various improvements.\n\nThe improvements are meant to normalize behavior across different\nplatforms and environments, and to make filesystem access more\nresilient to errors.\n\n## Improvements over [fs module](http://api.nodejs.org/fs.html)\n\ngraceful-fs:\n\n* Queues up `open` and `readdir` calls, and retries them once\n  something closes if there is an EMFILE error from too many file\n  descriptors.\n* fixes `lchmod` for Node versions prior to 0.6.2.\n* implements `fs.lutimes` if possible. Otherwise it becomes a noop.\n* ignores `EINVAL` and `EPERM` errors in `chown`, `fchown` or\n  `lchown` if the user isn't root.\n* makes `lchmod` and `lchown` become noops, if not available.\n* retries reading a file if `read` results in EAGAIN error.\n\nOn Windows, it retries renaming a file for up to one second if `EACCESS`\nor `EPERM` error occurs, likely because antivirus software has locked\nthe directory.\n\n## USAGE\n\n```javascript\n// use just like fs\nvar fs = require('graceful-fs')\n\n// now go and do stuff with it...\nfs.readFileSync('some-file-or-whatever')\n```\n", "readmeFilename": "README.md", "gitHead": "cbfb4864a3a312ba3d605ba187e8c920df1128d7", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@3.0.12", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-J55gaCS4iTTJfTXIxSVw3EMQckcqkpdRv3IR7gu6sq0+tbC363Zx6KH/SEwXASK9JRbhyZmVjJEVJIOxYsB3Qg==", "shasum": "0034947ce9ed695ec8ab0b854bc919e82b1ffaef", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-3.0.12.tgz", "fileCount": 14, "unpackedSize": 17948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdU05qCRA9TVsSAnZWagAAU+8P/12SxA6n34xKCxYHHP6q\n2zpDXyYFU13j9pT6m+JwUy9pSdaQNsbuutjNi6R+f1QfRuiQeGxYQ0YHQU6u\nBinT0V+vlOP5H5TnGwpbSdP0S7Y43c3xm8ox6MHJKm0SF67zbAPDelQUZJwR\nvQsk4X5XN2VTqHlPMuK7LTp2AaWZMyTBLiGFA9KOLyYBuF9oBoTPQCZ0S+in\nV3Kjmto+kqj/pYdYHjFq7yYC6o9IfraJWrnQ1p7VhP6goh2dTNOraKBF9HcO\n3eRT7fohaNnV8pghzy/LPKVLSXtoq/n2Yh9YpfHIxO4uvMr2ylqXYzg/66+b\nunpr71QLoON3Qp+e3pk6oLbuBY/i1w2V+P7I0Wc83kRYEszwkSttLd/gwCS5\ngN7tL2tWMDa61oWODIwtrpRnUtGCmTdCCRXoRJEZily8s6rpz6kR96QEDxBj\nG8Fy83GkO7LY6pXjyow2mHEIlHpIJqjd56MHulu6hjYK4M2sBzYAcnMXxZFB\nAZ/lMrLCKrA1F2UY+cxPD+sw4UDem2iRBPQQEJLNIFqT2n+i1PJVdd8HBYCm\nstX4mfoncmYmO3VSZwhGAdwEA5QDPxkTc5Zr3gF49nhVBWr8NmjyjKgogzt9\n3aXk32jSeMbG/Vh/aG2+UUJ1a+NmEb0OMnyw4flrNGLa/f4gVtiIRAnNsGqc\nFS90\r\n=L0V+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6QsJXBJNMr9LhoUj/rbi8E6SLmB7FXbrn7kTPPcx8+QIgDxRw3jBos7i2XYBiYE+8DXEv3IQs9HJVWiBXqbBNN6Y="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_3.0.12_1565740649533_0.36498180242185585"}, "_hasShrinkwrap": false}, "4.2.2": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.2", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "dependencies": {}, "gitHead": "5552309112e0d09e3c07a3e03ad3d6015b7cdccc", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.2", "_nodeVersion": "12.6.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-IItsdsea19BoLC7ELy13q1iJFNmd7ofZH5+X/pJr90/nRoPEX0DJo1dHDbgtYWOhJhcCgMDTOw84RZ72q6lB+Q==", "shasum": "6f0952605d0140c1cfdb138ed005775b92d67b02", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.2.tgz", "fileCount": 7, "unpackedSize": 27502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdU5zZCRA9TVsSAnZWagAA7a8QAJBSlOXnj/WkrVL3YdAb\nxs1Bbf8NzeS8YYkRsLKOG17p3toNwR1ylelzUHIjsTZgngyXimzGGPnp7qEN\nWYJCRq3T49sHwrGCpvccZXddoW4YikmkCArkk+VNWrqtCQ0jshm6aa/hO1Nb\noJnFkHohuk+OcS5nEo40N0m97cBjiephD18iF1E24LNnw4eXsrCg+3BYplEo\n27S5CP11JKkPVFVx9R1FB/xjYd81XB65WkuXXuJ+iBo59oQ33brcGuZ7y/no\nPdAdiawVHM2Tx1jpPMU1MxSUZ+xzLUN8HlFHSi9YNZ1E3Wvm0SKdlYW0ykOp\n5OXc8eNJDrU6wIm/75tJ6mUKq/+GbtcjOulT3Zu9T97TZHbUB3sig+oH4xbs\nJ6zTCQx6amBQ6Rw7hPFmftWXnos/d3rkN4wqFsZQAA/Pax0BVLMx6VIPg7cY\nD6zuOLrX1Zy0vz24ukzelTsk2SQq13A124QSGyijks1DvBjRNwePjWcPC6W2\n4rVGH+XU21mOeq2BNVQqCUOKDdMT6QVeLNvty9YFnn5iQ5f6vDHFNqVHDGRN\nM8zFIfkQ086p/KebKw3MNL31kV2khe2VYX2JgWie865H2jug1ZIkfXSvgJ13\nGRfTcPA+TWj1OAvEU8rY5UJ+MxvRMtxkd3434hCuZFGuNfBJijCY1O6T8pWH\nbzM7\r\n=Uz6a\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxbWJzmuCRvx50MVSUMFE4zolw7DadTb46dUINKiIznwIhAM0U9XesqxSuIPUH95EhggT7hU7OuD7fn7fMNeMks5R6"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.2_1565760728551_0.5940465008423166"}, "_hasShrinkwrap": false}, "4.2.3": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.3", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "dependencies": {}, "gitHead": "79aa9dd23aa91dc7feb181d01c9f5a595b0f311c", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.3", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-a30VEBm4PEdx1dRB7MFK7BejejvCvBronbLjht+sHuGYj8PHs7M/5Z+rt5lw551vZ7yfTCj4Vuyy3mSJytDWRQ==", "shasum": "4a12ff1b60376ef09862c2093edd908328be8423", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.3.tgz", "fileCount": 7, "unpackedSize": 27588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsNnSCRA9TVsSAnZWagAAAlwQAJyTyoFIv+xnkZa9xQPb\no7mUXo1mP1XFzTKB///oFCpqqN32vEkOLen1S4qlWTg6WPAGGchg+6AZQvFu\nmEed/gO2RLZPRrLhTSSJWnRxxnWJuejAUh5XQ7jHs20NWWmdeyfcrJXZJW9W\npzOunzliHDvHzUb78VuRKjWsh8lDsr1qXPVuUisZgzUyQ84nwgzT79O3jhpi\nsds7iEoS/tUpgN9ZYz8b2T0EmWNf0mB9xvZA6/X07ntXF3jYOPeVEsU1lsBJ\n1+oWuLT+JnsZ40igYIoaQ7L3YrXhcBihWwUCPpgQXxYlIe4OrF+HhUhTGG+w\niCKyRbK/gvECz+aV+xjpRNZKRcqtDDneFneUJ+WyIcN5shJhnTnmtxJ+TnzZ\nOwC+BYu6wAwg4DEhlzLxQdPYhuPbFwRUlD7gUwFt7Wyq3shOqzXjbFzcG1Wr\nsk1ML1zHP5f8VZnDde5wSxBhGpsoDsV/svn/TcNhFA3CSrnmETb9mV6x5KLH\n5QNvZEwR0qzK5HU4k+2S7hMUgIDKqsnKphEDw2G1NYfIk1y01x+S+H8ubcVg\n1XWW3AsTQ+8tENzp7E30n2AfiouwNXURZ18s2iqmRFqLd7meT5lJzk3eskvh\nCkl9ygpkgFl3vp70fvw2QO3FRRhQtAGmjKgo8T8tQh/xJHiVHK0tEO2WzA77\npOL+\r\n=HZfa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHViLqT5oHA9sGmYfz2+oEGMn6kJCpnrEsNPfvLrhPXcAiEArNXplu7flengT0OOe7BbcPlp5wHCgXS/jma2whBKa4g="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.3_1571871185957_0.16354071779999813"}, "_hasShrinkwrap": false}, "4.2.4": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.4", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "node test.js | tap -"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "dependencies": {}, "gitHead": "5a29f6c50ccdb412cb198b06ee248e65f365145b", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.4", "_nodeVersion": "13.10.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-WjKPNJF79dtJAVniUlGGWHYGz2jWxT6VhN/4m1NdkbZ2nOsEF+cI1Edgql5zCRhs/VsQYRvrXctxktVXZUkixw==", "shasum": "2256bde14d3632958c465ebc96dc467ca07a29fb", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.4.tgz", "fileCount": 7, "unpackedSize": 27739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqEhCCRA9TVsSAnZWagAA80AP/Rwsqh5efxu3braWUiUP\nAKODjTDbtn0qRhKdc3hcCAaWqiNGUvsChSnNkQSz9FEWhv0k/TBXSJzGM8fx\n3+gdzx2Tv4CREG1UyYrKF5oaTviB6owTDlQdP5uLgR8U+ZUflijdxMZpBePW\nr7fv7PpmeBscMMttkWiP6AAZRUy/DgjqdHCGfkNuwXvdTC2XWGyjOoZNUcc8\nwlN9uOwtRTr5VZBBikQNKXcjyaaEFygv+FZTMiE0TtmKxBKX7C7opgY/gTae\nmmGIJtddjNjddkv6QcltByDRM5shmDjvRqTes6BwcPNTlWr13z28dlZZN7x4\nYjc12t1FBuNJvkExWIthL/7C79mXj3Kb/++ITPI4qHwiuhznggJ9oLa0/OCr\nL8EVr3D9aJHWC68SPM+ofJR/QeBTIZBmU+nuauDHXaEuYvDNv1kdgB5xSmNO\n/eUWsmk0H7PzIbTftQhwS+hdRAjBkqN2tquev5tlYAkYCubx+dC/706bo8Rc\nC3z2RVwMnACbwQxO2crGW8bRnOvCcQf75NTLKkGOqaFlitOFFoAop1214q6G\ndBlEQlD8Z8dpy8kz7+KO5pjOhr9TlWmUB7DCOAwA3yTl+7vxN49v/4DCoIEE\nKqu026Oe5msMOiCRk4iLzmfKGtyemQpzTe9PlptneOO/olwtgoTX2bB/KBpz\ngijJ\r\n=CVW6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG0OgymrLymK8xiSBs9PScwSCwERvKHcQPgDnZ0Te9jmAiB8eNuzSLkBRTO4cnKUAS4Bas5uCGnRTl1fJnQ7H4AgPw=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.4_1588086850222_0.6614083601528049"}, "_hasShrinkwrap": false}, "4.2.5": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.5", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "nyc --silent node test.js | tap -", "posttest": "nyc report"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "dependencies": {}, "gitHead": "f110c5dea38b8ebdd89d27172b631b101ffb3348", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.5", "_nodeVersion": "15.3.0", "_npmVersion": "7.5.2", "dist": {"integrity": "sha512-kBBSQbz2K0Nyn+31j/w36fUfxkBW9/gfwRWdUY1ULReH3iokVJgddZAFcD1D0xlgTmFxJCbUkUclAlc6/IDJkw==", "shasum": "bc18864a6c9fc7b303f2e2abdb9155ad178fbe29", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.5.tgz", "fileCount": 7, "unpackedSize": 28392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHaDZCRA9TVsSAnZWagAAAOMQAIWcTc1VOVryE+HfWPBa\nH5L2IS/WiZU39AeEf5CKp6UegYOKicMVZ3UpBVyyzyuaZmYjfN9gHN9H7W5g\nMgjFNwwaH6CNXScB7jb+juRV+iiSfB/s+iIh/jJeOcD2sAgTHKty280z6Wxh\nsF9g8qkL7bc218zgFYht6kPNXO9xoOYAV4ary35SAKQqwH7ZebrH9Z7SBjXL\nueurPZ0Zad3JbRJU4lBR4+++YYahXxoJONxLvyUUe8e0cJnqdyYcEcDgu07c\nHVB7TCiN0Qu6QWiz/NoMC4MBERBr8PT4L3c7SdPluZaY9eWJEwWPpmHGMeXf\n8guva0ABMdq6Ed6e7pG7wgzpYLNa++Ogzxj9LvgStT5iNeCLZ1uN/NpUQe3A\n2lBP9o3y/0mABwC6hf8ovMIakeYJU26eSUaVZBufLwUQnmGecvWQrOa/7l9h\n0AdgkymLzma3R0DMfOF8+IL5RLTr8qYEu+LcGfnli9s1dRRuzeGeGT6V9pXb\nD3XLaQWCMf0llXGWdGutQ9IsH8Vt8C7IXfj0tQ5MKDZU1IaWtLjTgEu9NS8s\n5Ej3+sBGsaDtNnRloLzU45uUYRH+ZQlIYdKHC0rrRBjQM1GiGggHh62gk1Ru\n8ch59rgS49tlXm4g286icDR5zpBIzCdBjE63ULF+ZFn5YhW/1UPoJ15bcEne\nBlaE\r\n=gCIL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFW32Jpk+aCgKXChAXU8CGJY823OtUoxpY4DOxtoYlLTAiEA+uo/gTIsrOIwsuPoORWhla4GcgSo+OWVB33VDGCUzvA="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.5_1612554457336_0.30744193485900806"}, "_hasShrinkwrap": false}, "4.2.6": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.6", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "nyc --silent node test.js | tap -c -", "posttest": "nyc report"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "gitHead": "24f88fd7f37829d34ba5df5edfd7fe20df55d74a", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.6", "_nodeVersion": "15.3.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-nTnJ528pbqxYanhpDYsi4Rd8MAeaBA67+RZ10CM1m3bTAVFEDcd5AuA4a6W5YkGZ1iNXHzZz8T6TBKLeBuNriQ==", "shasum": "ff040b2b0853b23c3d31027523706f1885d76bee", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.6.tgz", "fileCount": 7, "unpackedSize": 28583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIsxuCRA9TVsSAnZWagAApjEQAJgF4Ual7RFfMaXfkvfo\nJ44Btf11H4BXMgpSSmIIuIhI0qWdIDvVTMq1inXH2BnLRajsodY1yGjmAunz\n/5m7KpbzNFPVL5GticN7I6bRANTaxwbcnkujaX0fv4zCoMez6v5BBMGOhg4O\ne7Vwc7wOO6lHiY436Xsqa7n+p2G+GMmWiGdAq9NCeTCIujBkc5RjDnN43D44\nRnuen2g19HtTKgFVILtTNkA3wuwM1HANZoIwd789Vq7nlodbT6I0VsTfJ9tq\njUXxAqjcuFEIpeIYWy1U4RCgSqxnkPpWIyszS7VHW018A3VVrrgz0nw6UZ8G\necKaFOSk+Ut0ZYObo3ClJa8N7v6jX8QgauzJ7MOZuS4Z++GGnvMJMKr7mPT3\nx/yY/HXPQMZS8pmJCodPMx8F7mupUTxEFgml7UcaPIVdVuJJkVbB7uldrPEM\n3SNf3n8LqExznrYjZSC+BJLlf/cB7n+JKpk+oeCSO7vyUAEnTfoEclyI1CSJ\nE91leGdo0HaTfiyzf3i5EbOY/uTVGHrRo26N2Xd2Cy4F5/LSBgDBMu/EHAwl\nOUEDTAZ9fVWBLL5/9oNztRyw7R3Qx+UzVA4SUqQ7fKIM5aa6OOEglATWbPKe\nqlSkruG9zp6HewA8Mf4NYQAkWyRpoBSOPDHU0rplXAbFQWJWh+mPoyvzlBje\nICO8\r\n=4Z1g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7dUjpZlFvlXw8ORlYkLxV8zo8YQ9aoMRmNXbABRJnywIhAMvTtjYuSHVZ0OO1cXdLLcv+chq9yJclIBnN+C2cxo5l"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.6_1612893294170_0.11333439159708325"}, "_hasShrinkwrap": false}, "4.2.7": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.7", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "nyc --silent node test.js | tap -c -", "posttest": "nyc report"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "gitHead": "00474f6055cec06a6c5131250cfceca259220b33", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.7", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-b1suB8r7mlSJQIBs6typf13fz55WYPeE7/KYlQUvqB7E3hUkXhz4D8FVHkENHTqG8+mD2yyT9HgT5bNkGNiqeQ==", "shasum": "ed65e76e7c700c3e2c889ca65677ec0d210db1b3", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.7.tgz", "fileCount": 7, "unpackedSize": 29332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDCdLCRA9TVsSAnZWagAAFZsP/jV4oGNIxyS+CMomQjNo\n1bklN3iaxLTmKkA8NcKmLT6/5lsV8d9Db//QbA0gsPpYItmk7oIfrfaQovKb\nEJBtDRviezR3RaoXSqjUL/T0DZ4a/Wyy6O6sWRvMIvDWiXxWg+/EQ0TFqI12\nAeOP8mUMU5vx9SAqHXYnNLcv8J7LWbk2Jwv6tj95UG4gBtSmJTzSFAiSwkne\nn17ysp68JNSz1p6P53BoP724AFL3kPKEZj30Lwq1sb6e+nfxEW6AC2y5R7RZ\nBsNsYRk4sDkE8Dkmgkr3xx4napgXJx/D/QI29SSiRb55AE6g4al5p/REazGx\nzcs0QPM2XtTpZJ03sEZHRYxUYuPz+4WwHhYiDg+RrKzLBmjc3d7PYUWSGKwZ\n//mzTB8XvpgHeolouXAs33io5EBhN4SLEXTzqop8GuuxPYutiwcaqYuNvG+o\nHxj40xuL5K/yF5tTxF/2o0LPDfwjHyPMoXE1tFNqulr5fdrFfBaxR7a9y9cz\npZF2p3EGUEP3KhvLwTD5dJSBDJhl6Bp0CI4NumXUek8aSDceqaMVkVSZ5mPF\nxADpnY9qkzE/NzGIy9lAm+TFsLjhceitBC5R1k7EX+8CCpCMW7EjIWYgqNWx\nlpnrCLv04Oa27Qyl/Bki33VNQrh1hit6l11gSAkhVXo3gdNPEf+CqpJ1Lh7m\nMLvA\r\n=pCgY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFEcxbxJjV24ucnTPAjMR7A4qX/D/AzGUz1v9Lf+mliFAiBfCq/z3XD3XCer7OQvPcn8d5ObHo0YHROvWewF89hbqQ=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.7_1628186443133_0.9389533906869421"}, "_hasShrinkwrap": false, "deprecated": "Please upgrade to v4.2.8 or higher"}, "4.2.8": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.8", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "nyc --silent node test.js | tap -c -", "posttest": "nyc report"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "gitHead": "9ec3413c8eb1c073c42262bf5a2a8cdf556f68a7", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.8", "_nodeVersion": "16.5.0", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-qkIilPUYcNhJpd33n0GBXTB1MMPp14TxEsEs0pTrsSVucApsYzW5V+Q8Qxhik6KU3evy+qkAAowTByymK0avdg==", "shasum": "e412b8d33f5e006593cbd3cee6df9f2cebbe802a", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.8.tgz", "fileCount": 7, "unpackedSize": 31565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDESsCRA9TVsSAnZWagAAlLsP/3rDzdjuoJ9sanmjNWm4\nYz/5w8QknWy7aleMV0uOch9gE60gKF38ouxFkX0MTEQknbvflQv0VYn2Am49\nv1hIpaM22jq6dYj9V4yrEsHYHuEk5djnBp7ENgVclKzdMvKj/bnLs6u0iWJ5\ny/rW/jNFfNN4p6K9pmSfnvdy3zaM3HelZlp8YFKHtMoWz0bm29kfcNp3RyST\n9MUrz3wEe/7ylelkHJOzFsPH7tH3ba88N8yrIyXbGW15EBd1a07pMldztaF+\noPX+/WcoNfM+v78LGfMR4Q/g7KYZjL++XwBs/4qA1F2Xl2YRS853wa4dzuP9\n8j5PcZ0QfzcotObgwU3p/CYviyHnkoldXbhwDWbBuGh5uU71AX/nzLbi60qW\n/v3P8t2TwjbfAlKpOXBn7bp5CyKqoTUtuguOZZfbv9KjOpA0OfsD2E3QLLaX\ni5iflpPmWhCsz6AIOApt0Fe7U8qFw1pKd71NEyO0I52gz3AN1G+kxsYRKTwf\n//9G1GlTWSfRpq000YxjQgY/GopoRiRb5JZZ6b4S1jV/vKg1Nq8kBw+gmAoB\nQykatsZa91tr+H3Pb9uIicteXp16AGh3E/wB+6uT/hxkq/v5pC0UQDXbcIP+\ni/2OIMfFyNVftpsj27bH8U/EXS1eBQXBTLUcwrUCYlqFK01mJ7iEkbz7wbPg\nwEs/\r\n=/pkj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQgUnW06jZ96WAuQJb0u3i7gnAUppMHGGG7LrSD3i7MwIhAPTnt0Qy9LMx4YnxJph2Y7j6eY0nvWvzLH/IFfISsCXg"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.8_1628193964186_0.14011159308314514"}, "_hasShrinkwrap": false}, "4.2.9": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.9", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "nyc --silent node test.js | tap -c -", "posttest": "nyc report"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "gitHead": "95ec3a283dffe0402282ea92f2356d3c166f6392", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.9", "_nodeVersion": "16.5.0", "_npmVersion": "8.2.0", "dist": {"integrity": "sha512-NtNxqUcXgpW2iMrfqSfR73Glt39K+BLwWsPs94yR63v45T0Wbej7eRmL5cWfwEgqXnmjQp3zaJTshdRW/qC2ZQ==", "shasum": "041b05df45755e587a24942279b9d113146e1c96", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.9.tgz", "fileCount": 7, "unpackedSize": 31596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1PCjCRA9TVsSAnZWagAA9vYP/1t1MI/OIRA5LAI0hmvq\nlDAWdl406gZRpier1VHoPhLOZ3bZPZZ4CGXlsWH54Wm1xWmiY6P27RWARXH4\neyBVnEOq+g5rZY/9JDNUNFY+JfTaTnjQ6M1wzgeJjPbF/Y91EDT1+w1U5BSd\nCkp7wQmfEiEqdfJZeq63ZY6odeSdlJB+KBPtpuEmcKcUNuKM7fv8kR/9F5If\nNyZ9dsrjIzRgMKXzsAzJTTWnkMxYQ1WVqmwmlfSZiyzpIwoNmx+yuUudpX4H\ns/ADrQMxIIev5bGQl1p0s3UE0AHIADaQMFicJ1gGL5/Qn6nNWCjkg5L+lWVb\n5NObAkiek2tYRDxwY7wVh0xvTADlbrs6GljN+xItggaaj0ySyz/c65eu3Pyg\nWyyL+sJ7AFSfrVSuBt6mReLOEaonjRT77MZAkH7HgFo54N/rwb7L5JtnhI62\nb4iUvcq9s7+rYKahenljaOVpdtSl565uDhMSaoJQCCzh3E56OR+jIKrJ39+S\nO0U3cPnxZjydRoYpjaBb0/0nXeDAfoP+yL7cSqYY8DxDFBJS+SyA2kB0dPQw\nouDYKmbDsCWcKoSlQ1FXo6/J6ybs5qI4YnWammbT/y19trL3VFnVQFcVr4t6\nMR6duuinVntoBstpnK8/sjZi7OOdzVT/2PCrMSNndwW817r8tGcJbudbwXTL\nUtAB\r\n=3NQn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGdQMtsj6xfzvgrBH9yFOC37buETHytD/6mTSTxb/pxVAiBp+RcD13Kai/YgfpEAIMjEQshUZA56RM7BRPkCOnP8Gg=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.9_1641345187374_0.14528089410157086"}, "_hasShrinkwrap": false}, "4.2.10": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.10", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "nyc --silent node test.js | tap -c -", "posttest": "nyc report"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^12.7.0"}, "gitHead": "1f19b0b467e4144260b397343cd60f37c5bdcfda", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.10", "_nodeVersion": "17.6.0", "_npmVersion": "8.5.3", "dist": {"integrity": "sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA==", "shasum": "147d3a006da4ca3ce14728c7aefc287c367d7a6c", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.10.tgz", "fileCount": 7, "unpackedSize": 32470, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA1uTjaW3iIxS4M3qAd6G2a1eA2VBnJk3uad5b39KE2kAiAM2jgxR4jcxVjstEzRCd5ydGON5o0VkPyEuFNHKzYupQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSyZoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCzw//UtS3HkPO3bCZalf3uNH+6ZTvOyFSFzjOprlRrOvQ5r51hicp\r\npizQBejiOaCcK5p7tBViXT+bV5otgvJ8dXBjiQQpXuSuSVN13MWs9FsNJANQ\r\nlcmzJRFZwdhoVygtRtuk6Dx9EOG7gfcH3Oud/ztts9iSeDLRsZewzkHtqRBX\r\nAfoAaFn9YNZ/14B0QwsbmOHeURedNmdnPLU5hL3o91LjUe3++TrMPA0BcDFT\r\nN8jguwfX9YoD6GkAO/11Iu9q48QALO6WwdU7+WF3f1kp1GdryxfxaYgTZWMp\r\nOrfgCRpDCzdDZkN8AKTlJkRUnDRHuEeiJB5wfONw1p321XwBx4EbqenZQSkc\r\naBhP8Q20MKRJb6A8tuNKyvx9toSeYY6gj7thORU7RhI4JvJJgGSGNyHeAjz5\r\nV5cap9J8QrI472ZxIth3abdws45yP/f4kVtZ+32GdcCrwkQHiL8VfGYuaPoM\r\nyvULvSiKeDkBZR7oGAHUyM2esHaZcqSQDsQ8aP7dCL/L80iW4qQzvs1Ngmqt\r\nGMzRIAh6LzMG/Q/xX0lLBNLLqa3ajkrcyIjpKFgzhS5TqAr/gNI56i4kgTuP\r\nFPxg6QHN/q8tt/MhWjZA6jZjCs/X7ZjNCFKU1qBhf8ecnqdkFZMo2fQo0tN+\r\nLRaRLDpK3L8hBQ5tY0YmrKHqymSZjT43Voo=\r\n=5ivk\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.10_1649092200126_0.3737260118922954"}, "_hasShrinkwrap": false}, "4.2.11": {"name": "graceful-fs", "description": "A drop-in replacement for fs, making various improvements.", "version": "4.2.11", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "main": "graceful-fs.js", "directories": {"test": "test"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "test": "nyc --silent node test.js | tap -c -", "posttest": "nyc report"}, "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "license": "ISC", "devDependencies": {"import-fresh": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.2.8", "tap": "^16.3.4"}, "tap": {"reporter": "classic"}, "gitHead": "514861c372899df14beb7aaecca4cdbb498d7d11", "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "_id": "graceful-fs@4.2.11", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "shasum": "4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3", "tarball": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "fileCount": 7, "unpackedSize": 32535, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvrVOcn7hI1H803c2vtwpp2SWqq+i4Io7slvfmq5ALwAiBUkAIGXz4UnL+r6Mp2H+OE2q0DUtrViYowDGL171hm5w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkE25LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1Cw/+N8TlrsFr9s7NQc7VlIFHMU2rKzP4vToGXmp36eSQFnl6ovT2\r\nORy24D9wbXLJHjoK3OXXfJj05y29v8qiNIEVpSxrULuRhvK49Ck3KkOl8uCg\r\n8gmXQCDCnY3n4MsEQgiUH34r+xJHE1szyaO4asFcfoDWJzq+YkLX2bbNfgow\r\n2Qrv+cuqOJVT2jVXRieFhJd5FWCD9Y4U+nzzY39a+UzvJvpq30lhRgTqZfNW\r\nct6GJx6Td8Kvc5SQ+EwAiFTrp8DUfZrzqD+U+E/GuMLLj3bCczYCagndUU27\r\nf1PNUMkqeTFpjuduqsQQuyyoLFm6cvKU4llHOETAaVTZpUPS6iR9SE91cuQt\r\n+ABAuaxuGuEmLWWBSbaPeOI76+CJAQ4mjwwFEwRSPTUY7wfCuhis4YBzEISD\r\nrDwkGKXwQ2XpLymE9QMAAFc6mxO1WxRbnlaxgmZLBhJGKeRh7KRaQRtI0qIy\r\nPm6M2Tpp+JoFOFIKy5Q8AldLlWWL5zzo71jglrQrGytDwem1sYjGDozXfVgm\r\nfYFO4SV4y0gXeY2Z68y0g+S4BmP+JUWM5v4WCBJxm1gEmEzz4yLv7PXVx12R\r\n10binyrgUoOUUWC7qSeQaVwROIQI8dNhOjSvdOJ3Vlpk2+cebpStOgJ9/eyY\r\nX1M50AoJmZh/Yl4oqHc8jSU5i9HjwWGe4zE=\r\n=HvMs\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/graceful-fs_4.2.11_1678995019142_0.5673425576033098"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2023-06-09T21:32:46.823Z", "created": "2011-07-20T08:49:36.339Z", "1.0.0": "2011-07-20T08:49:38.481Z", "1.0.1": "2011-09-25T00:33:29.942Z", "1.0.2": "2011-11-18T21:15:46.607Z", "1.1.0": "2011-11-23T00:45:14.407Z", "1.1.1": "2011-11-28T23:32:21.075Z", "1.1.2": "2011-12-03T02:27:37.810Z", "1.1.3": "2012-01-19T01:10:19.961Z", "1.1.4": "2012-01-19T18:09:15.746Z", "1.1.5": "2012-01-23T22:27:39.609Z", "1.1.6": "2012-03-20T01:26:15.355Z", "1.1.7": "2012-03-21T21:06:58.684Z", "1.1.8": "2012-03-22T23:20:47.942Z", "1.1.9": "2012-07-11T23:10:33.354Z", "1.1.10": "2012-07-28T17:24:47.924Z", "1.1.11": "2012-09-18T22:15:39.397Z", "1.1.12": "2012-09-18T23:12:22.368Z", "1.1.13": "2012-09-18T23:16:11.936Z", "1.1.14": "2012-09-20T18:15:05.948Z", "1.2.0": "2013-02-06T00:01:39.344Z", "1.2.1": "2013-04-30T17:07:10.366Z", "1.2.2": "2013-06-12T17:30:47.695Z", "1.2.3": "2013-07-10T07:46:03.989Z", "2.0.0": "2013-07-11T07:10:06.790Z", "2.0.1": "2013-09-07T19:18:04.406Z", "2.0.2": "2014-02-17T02:28:44.378Z", "2.0.3": "2014-03-20T00:33:06.873Z", "2.1.0": "2014-06-02T00:35:48.126Z", "3.0.0": "2014-06-02T01:48:19.789Z", "3.0.1": "2014-06-07T18:25:22.953Z", "3.0.2": "2014-06-10T22:55:51.714Z", "3.0.3": "2014-10-06T17:53:52.206Z", "3.0.4": "2014-10-14T20:07:54.353Z", "3.0.5": "2014-11-30T17:55:06.342Z", "3.0.6": "2015-03-11T21:28:56.029Z", "3.0.7": "2015-05-19T01:38:26.785Z", "3.0.8": "2015-06-01T05:08:03.513Z", "4.0.0": "2015-06-26T00:55:13.062Z", "4.1.0": "2015-06-27T18:52:45.921Z", "4.1.1": "2015-06-27T20:08:27.668Z", "4.1.2": "2015-06-27T20:25:15.466Z", "4.1.3": "2016-02-02T21:42:07.205Z", "4.1.4": "2016-05-05T19:00:55.342Z", "4.1.5": "2016-07-26T18:47:23.305Z", "3.0.9": "2016-08-13T02:49:15.525Z", "4.1.6": "2016-08-19T14:18:40.593Z", "3.0.10": "2016-08-23T00:56:24.703Z", "3.0.11": "2016-08-30T19:01:55.423Z", "4.1.7": "2016-09-26T21:38:40.693Z", "4.1.8": "2016-09-26T22:06:42.295Z", "4.1.9": "2016-09-28T23:01:12.248Z", "4.1.10": "2016-11-02T22:55:55.356Z", "4.1.11": "2016-11-22T19:30:31.252Z", "4.1.12": "2018-11-02T20:52:27.707Z", "4.1.13": "2018-11-02T21:28:15.709Z", "4.1.14": "2018-11-02T23:18:33.348Z", "4.1.15": "2018-11-04T20:51:54.752Z", "4.2.0": "2019-06-26T19:39:25.253Z", "4.2.1": "2019-08-04T06:04:41.125Z", "3.0.12": "2019-08-13T23:57:29.792Z", "4.2.2": "2019-08-14T05:32:08.671Z", "4.2.3": "2019-10-23T22:53:06.088Z", "4.2.4": "2020-04-28T15:14:10.455Z", "4.2.5": "2021-02-05T19:47:37.492Z", "4.2.6": "2021-02-09T17:54:54.320Z", "4.2.7": "2021-08-05T18:00:43.236Z", "4.2.8": "2021-08-05T20:06:04.291Z", "4.2.9": "2022-01-05T01:13:07.602Z", "4.2.10": "2022-04-04T17:10:00.270Z", "4.2.11": "2023-03-16T19:30:19.323Z"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-graceful-fs.git"}, "users": {"fgribreau": true, "pid": true, "adamrenny": true, "chilts": true, "eins78": true, "mbonaci": true, "robertwarrengilmore": true, "maschs": true, "dukewan": true, "magemagic": true, "octoo": true, "gonzalofj": true, "epirat": true, "kreding": true, "maobean": true, "stringparser": true, "sloanb": true, "nickleefly": true, "elig": true, "itonyyo": true, "skerit": true, "brandonpapworth": true, "battlemidget": true, "cestrensem": true, "stephn_r": true, "wander_lp": true, "alectic": true, "vbv": true, "js3692": true, "guananddu": true, "nmccready": true, "superwf": true, "bojand": true, "yuxin": true, "abdihaikal": true, "panlw": true, "skrdv": true, "abhisekp": true, "whatsamoorefor": true, "solmsted": true, "tommyzzm": true, "maxming2333": true, "wangnan0610": true, "szymex73": true, "loridale": true, "morogasper": true, "tmurngon": true, "docluv": true, "steel1990": true, "mojaray2k": true, "powerweb": true, "liuningww": true, "rickyrattlesnake": true, "seangenabe": true, "ziehlke": true, "shakakira": true, "kiaratto": true, "hyteer": true, "steakeye": true, "rocket0191": true, "bluelovers": true, "krishaamer": true, "larrychen": true, "nbuchanan": true, "josephst18": true, "sopov": true, "chinawolf_wyp": true, "heartnett": true, "tdmalone": true, "arniu": true, "faraoman": true, "monjer": true, "onurcan": true, "webfacer": true, "j3rrywan9": true, "soenkekluth": true, "morewry": true, "devarajchidambaram": true, "flumpus-dev": true}, "readme": "# graceful-fs\n\ngraceful-fs functions as a drop-in replacement for the fs module,\nmaking various improvements.\n\nThe improvements are meant to normalize behavior across different\nplatforms and environments, and to make filesystem access more\nresilient to errors.\n\n## Improvements over [fs module](https://nodejs.org/api/fs.html)\n\n* Queues up `open` and `readdir` calls, and retries them once\n  something closes if there is an EMFILE error from too many file\n  descriptors.\n* fixes `lchmod` for Node versions prior to 0.6.2.\n* implements `fs.lutimes` if possible. Otherwise it becomes a noop.\n* ignores `EINVAL` and `EPERM` errors in `chown`, `fchown` or\n  `lchown` if the user isn't root.\n* makes `lchmod` and `lchown` become noops, if not available.\n* retries reading a file if `read` results in EAGAIN error.\n\nOn Windows, it retries renaming a file for up to one second if `EACCESS`\nor `EPERM` error occurs, likely because antivirus software has locked\nthe directory.\n\n## USAGE\n\n```javascript\n// use just like fs\nvar fs = require('graceful-fs')\n\n// now go and do stuff with it...\nfs.readFile('some-file-or-whatever', (err, data) => {\n  // Do stuff here.\n})\n```\n\n## Sync methods\n\nThis module cannot intercept or handle `EMFILE` or `ENFILE` errors from sync\nmethods.  If you use sync methods which open file descriptors then you are\nresponsible for dealing with any errors.\n\nThis is a known limitation, not a bug.\n\n## Global Patching\n\nIf you want to patch the global fs module (or any other fs-like\nmodule) you can do this:\n\n```javascript\n// Make sure to read the caveat below.\nvar realFs = require('fs')\nvar gracefulFs = require('graceful-fs')\ngracefulFs.gracefulify(realFs)\n```\n\nThis should only ever be done at the top-level application layer, in\norder to delay on EMFILE errors from any fs-using dependencies.  You\nshould **not** do this in a library, because it can cause unexpected\ndelays in other parts of the program.\n\n## Changes\n\nThis module is fairly stable at this point, and used by a lot of\nthings.  That being said, because it implements a subtle behavior\nchange in a core part of the node API, even modest changes can be\nextremely breaking, and the versioning is thus biased towards\nbumping the major when in doubt.\n\nThe main change between major versions has been switching between\nproviding a fully-patched `fs` module vs monkey-patching the node core\nbuiltin, and the approach by which a non-monkey-patched `fs` was\ncreated.\n\nThe goal is to trade `EMFILE` errors for slower fs operations.  So, if\nyou try to open a zillion files, rather than crashing, `open`\noperations will be queued up and wait for something else to `close`.\n\nThere are advantages to each approach.  Monkey-patching the fs means\nthat no `EMFILE` errors can possibly occur anywhere in your\napplication, because everything is using the same core `fs` module,\nwhich is patched.  However, it can also obviously cause undesirable\nside-effects, especially if the module is loaded multiple times.\n\nImplementing a separate-but-identical patched `fs` module is more\nsurgical (and doesn't run the risk of patching multiple times), but\nalso imposes the challenge of keeping in sync with the core module.\n\nThe current approach loads the `fs` module, and then creates a\nlookalike object that has all the same methods, except a few that are\npatched.  It is safe to use in all versions of Node from 0.8 through\n7.0.\n\n### v4\n\n* Do not monkey-patch the fs module.  This module may now be used as a\n  drop-in dep, and users can opt into monkey-patching the fs builtin\n  if their app requires it.\n\n### v3\n\n* Monkey-patch fs, because the eval approach no longer works on recent\n  node.\n* fixed possible type-error throw if rename fails on windows\n* verify that we *never* get EMFILE errors\n* Ignore ENOSYS from chmod/chown\n* clarify that graceful-fs must be used as a drop-in\n\n### v2.1.0\n\n* Use eval rather than monkey-patching fs.\n* readdir: Always sort the results\n* win32: requeue a file if error has an OK status\n\n### v2.0\n\n* A return to monkey patching\n* wrap process.cwd\n\n### v1.1\n\n* wrap readFile\n* Wrap fs.writeFile.\n* readdir protection\n* Don't clobber the fs builtin\n* Handle fs.read EAGAIN errors by trying again\n* Expose the curOpen counter\n* No-op lchown/lchmod if not implemented\n* fs.rename patch only for win32\n* Patch fs.rename to handle AV software on Windows\n* Close #4 Chown should not fail on einval or eperm if non-root\n* Fix isaacs/fstream#1 Only wrap fs one time\n* Fix #3 Start at 1024 max files, then back off on EMFILE\n* lutimes that doens't blow up on Linux\n* A full on-rewrite using a queue instead of just swallowing the EMFILE error\n* Wrap Read/Write streams as well\n\n### 1.0\n\n* Update engines for node 0.6\n* Be lstat-graceful on Windows\n* first\n", "readmeFilename": "README.md", "homepage": "https://github.com/isaacs/node-graceful-fs#readme", "keywords": ["fs", "module", "reading", "retry", "retries", "queue", "error", "errors", "handling", "EMFILE", "EAGAIN", "EINVAL", "EPERM", "EACCESS"], "bugs": {"url": "https://github.com/isaacs/node-graceful-fs/issues"}, "license": "ISC"}