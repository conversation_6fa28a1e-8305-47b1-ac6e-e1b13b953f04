// let a = 1
// while (a <= 3) {
//     document.write("<p>爱你</p>")
//     a++;
// }

/*
1.页面输出1-100
2.计算从1到100的总和并输出
3.计算1-100之间的所有偶数和
*/
// let i = 1, sum_a = 0, sum_a2 = 0
// while (i <= 100) {
//     document.write(`这是第${i}个数<br>`)
//     sum_a += i
//     if (i % 2 === 0) { sum_a2 += i }
//     i++
// }
// document.write(sum_a)
// document.write(sum_a2)


// while (true) {
//     let str = prompt("你爱我吗")
//     if (str == '爱')
//         break
// }

// let sum = 0
// while (true) {

//     let re = +prompt(
//         `请输入要执行的操作:
//     1.存钱
//     2.取钱
//     3.查看余额
//     4.退出
//     `
//     )
//     if (re === 4)
//         break
//     else
//         switch (re) {
//             case 1:
//                 let s1 = +prompt(`执行存钱操作<br>
//         请输入要存取多少钱:
//         `)
//                 sum += s1
//                 break
//             case 2:
//                 let s2 = +prompt(`执行取钱操作<br>
//     请输入要存取多少钱:
//     `)
//                 sum -= s2
//                 break
//             case 3:
//                 alert(`当前余额为${sum}元<br>`)
//                 break

//         }
// }

// let str = ['刘', '关', '张', '黄', '赵']
// for (let i = 0; i <= str.length - 1; i++) {
//     console.log(str[i])
// }


// function sheet99() {
//     for (let i = 1; i <= 9; i++) {
//         for (let j = 1; j <= i; j++) {
//             document.write(`<span>${i}x${j}=${i * j}</span>`)
//         }
//         document.write(`<br>`)
//     }
// }
// sheet99()

// function add() {
//     let a = +prompt("请输入第一个数：")
//     let b = +prompt("请输入第二个数：")
//     console.log(`${a + b}`)
// }
// add()
// function getSum(start, end) {
//     let sum = 0
//     for (let i = 1; i <= end; i++) {
//         sum += i;
//     }
//     console.log(`结果是${sum}`)
// }
// getSum(1, 200)
// function add(num1 = 0, num2 = 0) {
//     console.log(`${num1}+${num2}=${num1 + num2}`)
// }
// add(50, 100)
// add()
// function getArrSum(arr = []) {
//     let sum = 0
//     for (let i = 0; i <= arr.length - 1; i++) {
//         sum += arr[i]
//     }
//     console.log(sum)
// }

// getArrSum([1, 2, 3, 4, 5])
// let n = +prompt("请输入起始值:")
// let m = +prompt("请输入结束值:")
// let sum = 0
// function addAll(n = 0, m = 0) {
//     for (let i = n; i <= m; i++) {
//         sum += i
//     }
//     console.log(sum)
// }
// addAll(n, m)
// let a = +prompt('请输入第一个数')
// let b = +prompt('请输入第二个数')
// function getTotalPrice(a = 0, b = 0) {
//     return a + b
// }
// let price = getTotalPrice(a, b)
// console.log(price)


// function getMax(a, b) {
//     if (a > b) return a
//     else return b//a>b ? a:b
// }
// let max = getMax(3, 1)
// console.log(max)
// function getArrMax(arr = []) {
//     let max = arr[0]
//     for (let i = 1; i <= arr.length - 1; i++) {
//         if (arr[i] > max) {
//             max = arr[i]
//         }
//     }
//     return max
// }
// max = getArrMax([1, 3, 4, 2, 5, 10])
// console.log(max)
// function getArrMin(arr = []) {
//     let min = arr[0]
//     for (let i = 1; i <= arr.length - 1; i++) {
//         if (arr[i] < min) {
//             min = arr[i]
//         }
//     }
//     return min
// }//区间扩大法得最小值，最大值
// min = getArrMin([-1, 4, -5, 2, 5, -9])
// console.log(min)

function fn(a, b) {
    return a + b
}

console.log(fn(1, 2, 3))