{"_id": "node-cache", "_rev": "182-c28c26d09598b698649fce5322f31033", "name": "node-cache", "dist-tags": {"alpha": "5.0.0-alpha.0", "latest": "5.1.2"}, "versions": {"0.1.0": {"name": "node-cache", "version": "0.1.0", "keywords": ["cache", "local", "variable"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "_id": "node-cache@0.1.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "dist": {"shasum": "4e5c85543a3930454741b21c6c04940017ebb077", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.1.0.tgz", "integrity": "sha512-HoZo0uA8GmknAsj9uYemhvEt96LW/meCjSNjcMMNn8h86hUDD7H/ywi1obmjublqmue+dcTcWAyEbGxvATV3mA==", "signatures": [{"sig": "MEUCIQCesNdnAZFgGm6UYbKytj7Vil/Z471HEd4gzwRFJc7oAAIgaxE92YQdF8AdJ9dbzdNnyRxNyyeJIqNfoicFZZVnpQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.6"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "A simple node internal caching module", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/node-cache/0.1.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"underscore": ">= 1.1.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.1.1": {"name": "node-cache", "version": "0.1.1", "keywords": ["cache", "local", "variable"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@0.1.1", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "dist": {"shasum": "936a268b2709505a42570f3882b0e0ddb8842e54", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.1.1.tgz", "integrity": "sha512-u3Ux+xBBewIWWDz9JNwK69cJSvq2z6vVC5yXh0Hkz9dVxdzLeuUYjCm9LzvqWxKd+i4U1p7LA0bSEWStm/2LFQ==", "signatures": [{"sig": "MEUCIGLHeARZjpzOk+FoppP7SLSt4LIs8+1xSKORqbSl3Dr/AiEAmvYTKBMgNDPutqJHb4vOtcoAhFZfecpMJPzOtmJ1RbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.6"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "A simple node internal caching module", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/node-cache/0.1.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"underscore": ">= 1.1.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.1.3": {"name": "node-cache", "version": "0.1.3", "keywords": ["cache", "local", "variable", "coffee", "coffee-script", "underscore"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@0.1.3", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "dist": {"shasum": "982b9b04ae3e41ff880910ea2f2c3351b4f5622a", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.1.3.tgz", "integrity": "sha512-S6vNtLEI8geXSkHW+g5YJVBrxEhLTC2itHfeSEl0PsGsxk9aXkNJJxbZTx0s+DWZF0qQlYlq6G+OywPpYdTGCw==", "signatures": [{"sig": "MEUCIAfIH/GZEl6Aq7uV1kSm92+Fpb+u4eItnDaABZFB9urgAiEAtEA8mRRY1Rl4/E6LZj1k0TzXkaNbxIdW3tn52a3/B3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.6"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "A simple node internal caching module with expiring values", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/node-cache/0.1.3/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"underscore": ">= 1.1.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.2.0": {"name": "node-cache", "version": "0.2.0", "keywords": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@0.2.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "dist": {"shasum": "96d43eac26e569ec333bdfbfdb60adfaedb2283c", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.2.0.tgz", "integrity": "sha512-MOo+gRLXng7Jq+/yvVu+IlH1IsTA329RmtFOi50k7KDuP0xemXmd1JkzJYNXSKlwyuymDVPfxsAU7Oy9uH2y4w==", "signatures": [{"sig": "MEQCID+lv+GF54qpYSEVYE+CUCii920f2RaoxTQZeTjM3Yr5AiAO3qLG9kBPg/aI+YpOo+z8UG/NxMYyb/gmb5v/nIx/9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.6"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "Simple and fast NodeJS internal caching.", "directories": {}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/node-cache/0.2.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"underscore": ">= 1.1.7"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.3.0": {"name": "node-cache", "version": "0.3.0", "keywords": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@0.3.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "dist": {"shasum": "3d058ad502cd7e662ee8c4c2bb97413681f0f6f5", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.3.0.tgz", "integrity": "sha512-tH6X5hKDq15eL2jeAvTNTJugZGJydRr51rqp53pdF4fn5/49mZGIQ7y9vIpaQsqokEy/dtzSDBVK8LBNkf8O5A==", "signatures": [{"sig": "MEQCIEkhjIRwMdqC2nC6POLbvA/bhiHT09fpQ/Rn8zNa7EjyAiBr+K6unpwX9pQLmy+0Bp2+3dIepd7owhqMpgctRZKBTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.1.19", "description": "Simple and fast NodeJS internal caching.", "directories": {}, "_nodeVersion": "v0.6.16", "dependencies": {"underscore": ">= 1.3.3"}, "_defaultsLoaded": true, "devDependencies": {"expresso": "0.9.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.3.1": {"name": "node-cache", "version": "0.3.1", "keywords": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@0.3.1", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "dist": {"shasum": "954ba098e1480e32c9e9c9461ffa640f8f2799b9", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.3.1.tgz", "integrity": "sha512-/UBFGDmTNcvscldZsxWocisNh4ahvttPVPcTfTCazXwwvN41Rjz1M/Y/TFROYErcktRIyx6Ny0FcC/HPRwk0Lg==", "signatures": [{"sig": "MEQCIDIwwqfJJazoHieCGL5Xr6cpN4xsv8PjZ6uRRCv4IvoqAiAjbEi768G1C0murn9Ey6D/RfOK0sjDDUaH1oA8yTOsDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi"], "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.1.19", "description": "Simple and fast NodeJS internal caching.", "directories": {}, "_nodeVersion": "v0.6.16", "dependencies": {"underscore": ">= 1.3.3"}, "_defaultsLoaded": true, "devDependencies": {"expresso": "0.9.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.3.2": {"name": "node-cache", "version": "0.3.2", "keywords": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi", "memory", "internal", "node"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@0.3.2", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "dist": {"shasum": "3b4d84fa809934bcfd825282d95c76b9474c24a2", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.3.2.tgz", "integrity": "sha512-WWarFAVnN+GlNeNKzrnrBvrzgu3Xrqh8JrQYPkFizAWXmfAcO72Eb1LO3/eoey8GmDt22ojxkWrxW0RqDerRqA==", "signatures": [{"sig": "MEYCIQDCiIW3OvOy7N5h17NUbWbR+m4UzQexEUCMvVaWZTVPVAIhAIgq+1CN0RxEKzJeBh7jamXZH11+TZ2Q8z33xv4VdmGH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi", "memory", "internal", "node"], "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.1.19", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache", "directories": {}, "_nodeVersion": "v0.6.16", "dependencies": {"underscore": ">= 1.3.3"}, "_defaultsLoaded": true, "devDependencies": {"expresso": "0.9.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.0": {"name": "node-cache", "version": "0.4.0", "keywords": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi", "memory", "internal", "node"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@0.4.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "dist": {"shasum": "14cf5c39c0b2a1bcdfc39efeed905ee3a048eb66", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.4.0.tgz", "integrity": "sha512-efyY/GETmn2XKj2p7cJEYMX80RYbFbopH+yepV0an8oEttekckRC3VX3m6eqT68q349sZiJ7P6M3EKo1R++5jQ==", "signatures": [{"sig": "MEQCICG9mHiSJFsKsLSo/M2CKwogRcc79y+tSdH7FzEMvnpsAiB0aG+gmJTKBlqTbaKJBxobe3SiQM+uRmAge/L7H4cwig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi", "memory", "internal", "node"], "_from": ".", "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache", "directories": {}, "dependencies": {"underscore": "*"}, "devDependencies": {"expresso": "0.9.2"}}, "0.4.1": {"name": "node-cache", "version": "0.4.1", "keywords": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi", "memory", "internal", "node"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@0.4.1", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "cb4e2b164c437537c388547d7c1d36a4e28d00ff", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-0.4.1.tgz", "integrity": "sha512-TYxUXrgH04gmvkD3Z+IzaILmiizf7ksPOFN40Uy6z4pIsSkBJ3QeNLDDe5bp6FpNa3nd1wuVH40MoSmbHdEoEQ==", "signatures": [{"sig": "MEUCIGX6GOsJ6Fdt/dEMYkrxewxwBIRgabvlhZ7F25CRJVGoAiEA0gJeZB4zWnRxGfkqzDwD1qR5Su0Ika8mcPzjq/kO99I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi", "memory", "internal", "node"], "_from": ".", "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache", "directories": {}, "dependencies": {"underscore": "*"}, "devDependencies": {"expresso": "0.9.2"}}, "1.0.0": {"name": "node-cache", "version": "1.0.0", "keywords": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi", "memory", "internal", "node"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@1.0.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "8766a415d61060862f40a98b058933aed9585bc0", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-1.0.0.tgz", "integrity": "sha512-P3/b7CN5V7ZGihWU0o33Jet+89fURjESxOcxKNZwqPu5AoFqa/JutWJbA9SY3PNgL+AsdxzEJcHh4FybONn5OA==", "signatures": [{"sig": "MEUCIQCrukm5739PHZYEarJDzUVXF63X/0ZXY0BRl5l6iaoJhwIgYwHBd4ymx+HfQLk0Wq/KEjJdYLiz8wLrpt1/9GGy+i0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "coffee", "coffee-script", "underscore", "multi", "memory", "internal", "node"], "_from": ".", "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache", "directories": {}, "dependencies": {"underscore": "*"}, "devDependencies": {"expresso": "0.9.2"}}, "1.0.2": {"name": "node-cache", "version": "1.0.2", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@1.0.2", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "7cca38371d2a8426a45966831fd3e81c0300346f", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-1.0.2.tgz", "integrity": "sha512-3WGFyx7e+lgbM5Shidyd026P9BDPlOTXG158Ag6yVHnRMkVoNdxrgKhqMYipyVvzP59Tdt5hWT6v035NccGTHg==", "signatures": [{"sig": "MEYCIQDLv+HIcQPQGuKDR5A9rodKv5aGOCSoNgdLH1e7Rqjo9AIhALXG88ORqqSPOclhx3G6aHNUGI11mibIaJpTnW9izMKA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "dependencies": {"underscore": "*"}, "devDependencies": {"expresso": "0.9.2"}}, "1.0.3": {"name": "node-cache", "version": "1.0.3", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@1.0.3", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "62a9449e35e41b805cf4147ab5bd20bdd91e1445", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-1.0.3.tgz", "integrity": "sha512-S<PERSON>qabFYj7zPuSlC79Vq0Ks6QYw27s4PxQR8liGhaRkeNjbZeiLsB9RJfJDsPfg4GpS7JeOtoccPRq3QQDbd/xw==", "signatures": [{"sig": "MEQCIDYiXJnNg/V1d1S4gxBzZKjhKe0hqJDnPPqLM7t9VEu6AiAxGecvgDfmq0WQ9cjTYlLNQXZf3U1lSmXJvYZ7Ts2yzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "dependencies": {"underscore": "*"}, "devDependencies": {"expresso": "0.9.2"}}, "1.1.0": {"name": "node-cache", "version": "1.1.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@1.1.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "186365032d2395bdff73404178fb2bc8981ace70", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-1.1.0.tgz", "integrity": "sha512-3q6GeGOZuI+yeZzM8IV9pjzEXg5v8/w6WfW2uIunDnacv9mDNBlVcUdbJGL2sr8aG7dP7Cw1KApnEDAk9poR8g==", "signatures": [{"sig": "MEYCIQCt3h10qTIs0zmh2bO2R8AXd3clnVbavP452L1pstigngIhALGyAMKG0Wl0/ZCYQOVXoPxckdbyAph/lWMUFH8l4K3c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "engines": {"node": ">= 0.4.6"}, "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "dependencies": {"underscore": "*"}, "devDependencies": {"expresso": "0.9.2"}}, "2.0.1": {"name": "node-cache", "version": "2.0.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@2.0.1", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "443c261c00e71f9767017ea148cc675c1521f885", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-2.0.1.tgz", "integrity": "sha512-+8VRYV/4FQ4zBe/+Ep5KV/c3uDMBg3fuXHbJuCMyLaxrlG/hBvBidpIaaJeQB3UhXvo4K/6/XRgFqTfhcUa4Dg==", "signatures": [{"sig": "MEUCIQDkPmK07IDgGb4C3jKjnYnjJIzX4a1WVf1RTpZ9ChkxlAIgFcSqC6WtFc870oGAvjgehKOiljtlQGLSK11ddW1EqqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "443c261c00e71f9767017ea148cc675c1521f885", "engines": {"node": ">= 0.4.6"}, "gitHead": "0e74cbc205b96eca5799d7f4f872eab8ef28f042", "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "0.10.25", "dependencies": {"lodash": "3.x || 2.x"}, "devDependencies": {"grunt": "0.4.x", "expresso": "0.9.2", "grunt-run": "0.3.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "0.6.x", "grunt-contrib-coffee": "0.13.x", "grunt-include-replace": "3.0.x"}}, "2.1.0": {"name": "node-cache", "version": "2.1.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@2.1.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "a78c1bdcc81df3fe936be9dfa488fa06804aa54e", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-2.1.0.tgz", "integrity": "sha512-IFRBFyEIF1lZaerO4HJCSJKcPqXtwc8dBUAkF4A8Ile1nZysABEewLISFBFN3MsHE1EAg1tLDqOMcu7nFe8shw==", "signatures": [{"sig": "MEUCIQDZuzeTa0p1/Pts0FHnsFNIKZHauH49fdmotsj62ieWQgIgODmoZtZCIbJ4eykyyLvBD7Kxyx4FxIIiQRcdSmYHQ7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "a78c1bdcc81df3fe936be9dfa488fa06804aa54e", "engines": {"node": ">= 0.4.6"}, "gitHead": "d3124dac2867b4944f8eeab2d9ce7539170f9687", "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "0.10.25", "dependencies": {"lodash": "3.x || 2.x"}, "devDependencies": {"grunt": "0.4.x", "expresso": "0.9.2", "grunt-run": "0.3.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "0.6.x", "grunt-contrib-coffee": "0.13.x", "grunt-include-replace": "3.0.x"}}, "2.1.1": {"name": "node-cache", "version": "2.1.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "_id": "node-cache@2.1.1", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "7dcd61a029a5b66c0329b3c1548282819187ce94", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-2.1.1.tgz", "integrity": "sha512-nwjDB62DngFi7vUBxR1N6IjlWU9YMkj20FdurQDo8vFYZCxLdNjaNvaJDU8ziw6AQbi3PVb3zpEvLj8kxemj7w==", "signatures": [{"sig": "MEQCIHrCvhBYYWWsiiHEy9UJ+ZvoMecAM1RNps7uCeuCUoLYAiALhqxKoBWTORKtURBarKzx8wycSpDzdLJPAzFUshJiLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "7dcd61a029a5b66c0329b3c1548282819187ce94", "engines": {"node": ">= 0.4.6"}, "gitHead": "1333258c7bd8e8f27d4f36d4ec37e04af6885a80", "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "0.10.25", "dependencies": {"lodash": "3.x || 2.x"}, "devDependencies": {"grunt": "0.4.x", "expresso": "0.9.2", "grunt-run": "0.3.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "0.6.x", "grunt-contrib-coffee": "0.13.x", "grunt-include-replace": "3.0.x"}}, "3.0.0": {"name": "node-cache", "version": "3.0.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@3.0.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "e51e805df3913a01ae86e2d03bebe57bb41d3bc2", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-3.0.0.tgz", "integrity": "sha512-5iI9p8h1KJ2Ht7ObbdRLGAM8DYjVm6RIezai3cScTwy6YotZnGF2jgtjcMHHcyJlLSRyi/25E6bLugS1k0HQfw==", "signatures": [{"sig": "MEUCIQCwLtoHDpVp3/QT4XRHDF37pDJPDj/17qStWcb8Edii/gIgBakocQNJ6YF1G6AYjasP02IDS8zDn8fl4y/dh3bFWlU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "e51e805df3913a01ae86e2d03bebe57bb41d3bc2", "engines": {"node": ">= 0.4.6"}, "gitHead": "df3c5573161542e9ad435850cd191879cf7578c7", "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "0.10.25", "dependencies": {"clone": "1.0.x", "lodash": "3.x || 2.x"}, "devDependencies": {"grunt": "0.4.x", "expresso": "0.9.2", "grunt-run": "0.3.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "0.6.x", "grunt-contrib-coffee": "0.13.x", "grunt-include-replace": "3.0.x"}}, "3.0.1": {"name": "node-cache", "version": "3.0.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@3.0.1", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "ad3f6c3394e16955e8c32b13c7789424b05f7af8", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-3.0.1.tgz", "integrity": "sha512-Jzn5vJgO7hF22p5er3WowaBId+20HVoFP7+F8TI+g/yfmcrLdDS7Je37pWixep7GJqke5iYn2uXQLBeZLML+yQ==", "signatures": [{"sig": "MEUCIQDXrXjGZYZBF2INU3YQIgbMn0qMeu0pH1EMaxw3jzG3NwIga6xkoqnJmE1/hHyjspIzfVTvBXsjiVLDRR4WVWy1RS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "ad3f6c3394e16955e8c32b13c7789424b05f7af8", "engines": {"node": ">= 0.4.6"}, "gitHead": "fd711d053e105079b5ab37aecc22e6a485b0cb9c", "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"clone": "1.0.x", "lodash": "3.x || 2.x"}, "devDependencies": {"grunt": "0.4.x", "expresso": "0.9.2", "grunt-run": "0.3.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "0.6.x", "grunt-contrib-coffee": "0.13.x", "grunt-include-replace": "3.0.x"}}, "3.1.0": {"name": "node-cache", "version": "3.1.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@3.1.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "6b7728e576810e098b4aa31eb3c1718539b072a4", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-3.1.0.tgz", "integrity": "sha512-L5MSaHhMyR7SXSRPZs9Ya+BjEW/2hhfWwGy6qrMMXjkD0ftbDfrHYMjFFJdWU3vsNRbB2EqMrfdZ3mKGD4siUw==", "signatures": [{"sig": "MEUCIQDPYvAseOKDdId8BEbOZdixbEGpaGk08Ps+Yf0NURl7AAIgP9xiFGwSjEvZs930ovE7frDf2hDRCg4jmYm/DLyFzCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "6b7728e576810e098b4aa31eb3c1718539b072a4", "engines": {"node": ">= 0.4.6"}, "gitHead": "6dec6dc10d6926a251fd6b4e845e3545ce162582", "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "4.2.6", "dependencies": {"clone": "1.0.x", "lodash": "3.x || 2.x"}, "devDependencies": {"grunt": "0.4.x", "expresso": "0.9.2", "grunt-run": "0.3.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "0.6.x", "grunt-contrib-coffee": "0.13.x", "grunt-include-replace": "3.0.x"}}, "3.2.0": {"name": "node-cache", "version": "3.2.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@3.2.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "c10d40ec05c157589844bd6510b6bdded504cbe6", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-3.2.0.tgz", "integrity": "sha512-ANunVAH1mlaY+K0NR6/T06bS+ggQWsfy33Ig3sPzU3qMLUMexldPoTQ3TI+4UbNnC/nRUmyR6+s5cbAG13D3Kg==", "signatures": [{"sig": "MEUCIQDYp/EbKa1OS9DTpwSuxFzdQAIykzHOH5unVCD49iT4IgIgV0N2R9LWmmbVrfW6tB97aG7PP40ZGV+1dmyja0naZ2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "c10d40ec05c157589844bd6510b6bdded504cbe6", "engines": {"node": ">= 0.4.6"}, "gitHead": "8c89829cd72a479a520d710066222c61ec366786", "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "4.3.1", "dependencies": {"clone": "1.0.x", "lodash": "3.x || 2.x"}, "devDependencies": {"grunt": "0.4.x", "expresso": "0.9.2", "grunt-run": "0.3.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "0.6.x", "grunt-contrib-coffee": "0.13.x", "grunt-include-replace": "3.0.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache-3.2.0.tgz_1456314268278_0.6945974645204842", "host": "packages-6-west.internal.npmjs.com"}}, "3.2.1": {"name": "node-cache", "version": "3.2.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "tcs-de", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@3.2.1", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/tcs-de/nodecache", "bugs": {"url": "https://github.com/tcs-de/nodecache/issues"}, "dist": {"shasum": "a7958d32a8a42d9119ce25986567ea2c5f966773", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-3.2.1.tgz", "integrity": "sha512-f43kRZnr5zmm48ja4qk8OTGQ8E80LmOAa6XKne6bGpJioayuKEvQRzk2lm8eCTKBJkxVpgDw+qNnUbmNWbKK2A==", "signatures": [{"sig": "MEUCIGTmddpbYQOSPnOItxB/1Gp6y9BdDBclURAUV4QMcdWpAiEAlfqk1gL7xm1GsmFkUCSLeULF3UI/aU6Q40Sk1JlVRuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "a7958d32a8a42d9119ce25986567ea2c5f966773", "engines": {"node": ">= 0.4.6"}, "gitHead": "bc49b373396cdbe4c1de612b866b4fece4c8471e", "scripts": {"test": "expresso test/node_cache-test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/tcs-de/nodecache.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "4.4.0", "dependencies": {"clone": "1.0.x", "lodash": "4.x"}, "devDependencies": {"grunt": "0.4.x", "expresso": "0.9.2", "grunt-run": "0.5.x", "grunt-banner": "0.6.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "1.0.x", "grunt-contrib-coffee": "1.0.x", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache-3.2.1.tgz_1458545885460_0.7256715400144458", "host": "packages-13-west.internal.npmjs.com"}}, "4.0.0": {"name": "node-cache", "version": "4.0.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@4.0.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "38a062026eebc86f11a7050c1944c18a3088f42a", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-4.0.0.tgz", "integrity": "sha512-35KsANpoTt72r0LtIGCfYy28Gf6E1kWy9jYEnb2DbMK6fB0X27PcQWPEAB04WNdWGT4wD4R5eoVowkY9WrgIpg==", "signatures": [{"sig": "MEUCIQCCSP/GBzb2vLZZGfUgf4l/KXZeZII09L1D7K9vVYm9ewIgJYbUceh8dv82Q5KRbjIVljIZCRcFWWiyNuntYLOUqfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "38a062026eebc86f11a7050c1944c18a3088f42a", "engines": {"node": ">= 0.4.6"}, "gitHead": "651fda48fbcb60b641a4da0f9c76cfd25279489c", "scripts": {"test": "mocha test/mocha_test.js"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "4.4.7", "dependencies": {"clone": "1.0.x", "lodash": "4.x"}, "devDependencies": {"grunt": "0.4.x", "mocha": "^3.0.2", "should": "^11.1.0", "grunt-run": "0.5.x", "grunt-banner": "0.6.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "1.0.x", "grunt-contrib-coffee": "1.0.x", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache-4.0.0.tgz_1474366881382_0.9475911362096667", "host": "packages-16-east.internal.npmjs.com"}}, "4.1.0": {"name": "node-cache", "version": "4.1.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@4.1.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "2a6a66460bf063781138206988237ec02c135157", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-4.1.0.tgz", "integrity": "sha512-tUMHY3++WuM88NMlpmkNZoVwaw9xoXdfMBdQxtTK1c2kMEgjMft2I1HxJcV/qiZ0aWwHZnfRkUtIDVgDLfT1fg==", "signatures": [{"sig": "MEYCIQCtoqOf8873+A6yBUsdce1clbrz9vLpe2QEje+j83QjvAIhAJrHqpwbQS8k5xOYJQnp2c5K+asM+mTX51Ifg8Z0G0RJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "2a6a66460bf063781138206988237ec02c135157", "engines": {"node": ">= 0.4.6"}, "gitHead": "cf9b773cff44419235e5f5a60d34addd942e87a9", "scripts": {"test": "mocha test/mocha_test.js", "build": "grunt build"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "4.4.7", "dependencies": {"clone": "1.0.x", "lodash": "4.x"}, "devDependencies": {"grunt": "0.4.x", "mocha": "^3.0.2", "should": "^11.1.0", "grunt-run": "0.5.x", "grunt-banner": "0.6.x", "grunt-regarde": "0.1.x", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-coffee": "1.0.x", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache-4.1.0.tgz_1474618207666_0.11245799716562033", "host": "packages-16-east.internal.npmjs.com"}}, "4.1.1": {"name": "node-cache", "version": "4.1.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@4.1.1", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "08524645ee4039dedc3dcc1dd7c6b979e0619e44", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-4.1.1.tgz", "integrity": "sha512-1IdglJ3+6RO7j2jGVSbWG7CD/H7axG770BbuopZNDqKpQu1ol89xC4Qc+hd6uBEewjsoCZ6xRIY8BRa5PkHgTQ==", "signatures": [{"sig": "MEQCIDJOfzFDyUO4RqawF8DYm4ooslPwEEjPR2+v0DGFnd9EAiBnaHnoXQaYSRvaQS/cjhxOMkr/HNxZuTaZiWkzgDdvAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "_from": ".", "_shasum": "08524645ee4039dedc3dcc1dd7c6b979e0619e44", "engines": {"node": ">= 0.4.6"}, "gitHead": "37ac785c68716b60cdfdbd52bc51cb60fadf8c79", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --compilers coffee:coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec", "build": "grunt build", "test-docker": "SILIENT_MODE=1 mocha test/mocha_test.js -R min"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"clone": "2.x", "lodash": "4.x"}, "devDependencies": {"grunt": "0.4.x", "mocha": "3.x", "should": "11.x", "istanbul": "0.x", "coveralls": "2.x", "grunt-run": "0.5.x", "grunt-banner": "0.6.x", "coffee-script": "1.x", "grunt-regarde": "0.1.x", "coffee-coverage": "1.x", "grunt-mocha-cli": "2.x", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "1.x", "grunt-contrib-coffee": "1.0.x", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache-4.1.1.tgz_1482319623018_0.19979252549819648", "host": "packages-12-west.internal.npmjs.com"}}, "4.2.0": {"name": "node-cache", "version": "4.2.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@4.2.0", "maintainers": [{"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "48ac796a874e762582692004a376d26dfa875811", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-4.2.0.tgz", "fileCount": 8, "integrity": "sha512-obRu6/f7S024ysheAjoYFEEBqqDWv4LOMNJEuO8vMeEw2AT4z+NCzO4hlc2lhI4vATzbCQv6kke9FVdx0RbCOw==", "signatures": [{"sig": "MEUCIBph3BdYmfOKXoEsq/1M05u+nwZ6vVY6OU50w802m29LAiEAqlwT2xKiwsk0JUTKJ5yfJ66U7VVSvZ+/7wurgXctw4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45056}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 0.4.6"}, "gitHead": "383915735c2a686ca12d882ccbe0f007fbb660cc", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --compilers coffee:coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build", "test-docker": "SILENT_MODE=1 mocha test/mocha_test.js -R min && tsc"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"clone": "2.x", "lodash": "4.x"}, "_hasShrinkwrap": false, "devDependencies": {"grunt": "0.4.x", "mocha": "3.x", "should": "11.x", "istanbul": "0.x", "coveralls": "2.x", "grunt-cli": "^1.2.0", "grunt-run": "0.5.x", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "grunt-regarde": "0.1.x", "coffee-coverage": "1.x", "grunt-mocha-cli": "2.x", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "1.x", "grunt-contrib-coffee": "1.0.x", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_4.2.0_1521641208313_0.42633244151560623", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "node-cache", "version": "4.2.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@4.2.1", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "efd8474dee4edec4138cdded580f5516500f7334", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-4.2.1.tgz", "fileCount": 9, "integrity": "sha512-BOb67bWg2dTyax5kdef5WfU3X8xu4wPg+zHzkvls0Q/QpYycIFRLEEIdAx9Wma43DxG6Qzn4illdZoYseKWa4A==", "signatures": [{"sig": "MEYCIQDFJH043cQaNCAw/xuW7n9Ul4fQWb6xOrL513mfMfgKJQIhAOY3gSOVz4VXyKnhr8iyqUYjzmxcASnIRIl6faZI0SoO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdOMVKCRA9TVsSAnZWagAAgIUP/0wQCP2SiDZFVzCosHrx\nAD/EvcjiF1cIkk1VAXVqObDf9ipDMOuX/eCi/BE+2cm7ONMGYELyE5YsAzRA\nUYOTjgw8+2/bhi8P60vi8h7xxffbT/uaTwthElIpuonmys4ByuXBq6xzZFWq\nD09YGv5uk/8XyG8/B3dAmhABiSGTsO/u5Qyq7evyb27XWYTMmqQC0BtzRX6h\nuvqEjcUsD9C6iZ4m2BpdCn7tsV7zvnyWqvJ/yxyMqG1mBm497X7/pETkxF/0\naAb5YHQe6ED5nPu4a9W37HzyfyHAWAMhUMip9cThpcUh/XU1yC04czVE/CY0\nOVnFCBeNQSxmkXWM1R1G1iqZ5TAyewy3i6PmJ9IudG9C1tS7jAeubB/LjD3L\nJnHjfqxTBmVtUkzbOGcAwV5Gw5mMe9bA2JvD4VFug6N44+uh+EFsM9hUZGTn\neX7npoT/il4hxRGXa38AZcm15gbrODHDk1ZR2NjS4Sy01cebO5rdTKHM6Mpb\n6/bv4xUPX9TPCXVQrcIzziBjwl4p7KluBEtAaYC5hv3a/8i5yjnNuT9MvKJe\nUBAq5NxjIsKDuEkLw3Pw35eIRHFHWyxVRtTYSeTA5PEP64eWR9lor3TaibZE\ngN196PAXtmzIssBZpTRCjUNj6hctQk5ZViPHJY4i0AGM1Pm6Qt0XKlGK0Rk7\nHFAT\r\n=Xy01\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 0.4.6"}, "gitHead": "f0c55ffecf8cbbaab49b602b06fe769f34fcc863", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --require coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build", "test-docker": "SILENT_MODE=1 mocha test/mocha_test.js -R min && tsc"}, "_npmUser": {"name": "tcs-de", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"clone": "2.x", "lodash": "^4.17.15"}, "_hasShrinkwrap": false, "devDependencies": {"grunt": "^1.0.4", "mocha": "^6.1.4", "should": "11.x", "istanbul": "0.x", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^4.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_4.2.1_1564001609460_0.9686213369854717", "host": "s3://npm-registry-packages"}}, "5.0.0-alpha.0": {"name": "node-cache", "version": "5.0.0-alpha.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@5.0.0-alpha.0", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "3055f7fa2368cf0fac42c6a11689a86d21d91ed8", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-5.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-dO5U+By+gI8jChOT5s36+crOrCjxH/Pn/EY367CQu1ABH+1Vx2fxapIeRsycMFDP8491Rc+qSR15BddxLrJnvw==", "signatures": [{"sig": "MEUCIBFS3JrytBX64Qm+BXXKSHnm3yAOQihglShkXgzqe4k6AiEAiXycVHt8tOmtUGBF8gRYcnU1578JMb7HeidCMUhLD84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgdLKCRA9TVsSAnZWagAAEa4P/RzahGswYrqpwhluAtFK\n9OB4KvhSWWSvUsS6IPRVfO0YpNrxGvHZdXKtGM7juU5hmYYlgKap0RjodkeO\n/ZhgJytiZH7NXCoityBM9rUH9JhSAfCZnTgA09/Ybz1IkrqrHlZn7pdGsLNC\nDgAdON24o1pyZRl0m1+2Q911cbOKQ4QHRlbaXrAQ6iJMeeXsuq5bB3IMuzz5\nxnNsL1LheGbkEDnSbmoGgeI7JMIoeCQaGquCLAQ4QzMd9l5CKiZd2cfinYYA\nzv/oPgh6DOwPsOWQPX3o4oxj/kENwyJQ9yQMi3et94nkt0jx9dBtbuvsvVqY\nL2ZEcCL5C+Le523QzTw1DUvTUZBxf6fPWihI9vbPLeRV3bB3xbBDs+ZN1Wi9\n4fciW0VBskoJrMA+k0VAV2kpRgdWhuBzRXphykQFVTauXwxGz6k1B9KV/1xv\nd5HmHOm7MNlrxjbYwvcfGSj3HMRYkYYEQXSzKs5SEgqsNRq9D31TifI9C1ZO\nfWavDThjhNfFLSi48+wRIo+kqGeB+V5zrqL8sUwi4KmL/WUo97T6iqBKY5uZ\ncWfAFAChzfi9+kMpX+qlaVuBGFIlxZrICCSq2yLxkrhPOttCklfC3N8Tkzk5\nYoAMOoklywm+sc9JanYksp1hguWwsoxJ47Fd3jfxP0Bis9PxHCarLdLLlyDD\nZX9V\r\n=13dh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "readme": "![Logo](./logo/logo.png)\n\n[![Build Status](https://secure.travis-ci.org/mpneuried/nodecache.svg?branch=master)](http://travis-ci.org/mpneuried/nodecache)\n[![Windows Tests](https://img.shields.io/appveyor/ci/mpneuried/nodecache.svg?label=Windows%20Test)](https://ci.appveyor.com/project/mpneuried/nodecache)\n[![Dependency Status](https://david-dm.org/mpneuried/nodecache.svg)](https://david-dm.org/mpneuried/nodecache)\n[![NPM version](https://badge.fury.io/js/node-cache.svg)](http://badge.fury.io/js/node-cache)\n[![Coveralls Coverage](https://img.shields.io/coveralls/mpneuried/nodecache.svg)](https://coveralls.io/github/mpneuried/nodecache)\n\n[![Gitter](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/tcs-de/nodecache?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge)\n\n[![NPM](https://nodei.co/npm/node-cache.png?downloads=true&downloadRank=true&stars=true)](https://nodei.co/npm/node-cache/)\n\n# Simple and fast NodeJS internal caching.\n\nA simple caching module that has `set`, `get` and `delete` methods and works a little bit like memcached.\nKeys can have a timeout (`ttl`) after which they expire and are deleted from the cache.\nAll keys are stored in a single object so the practical limit is at around 1m keys.\n\n\n## ATTENTION - BREAKING MAJOR RELEASE INCOMING!!!\n\nThe upcoming 5.0.0 Release will drop support for node versions before 6.x!\n(We are thinking about dropping node 6.x too, because it recently reached end-of-life.)\n\n\n# Install\n\n```bash\n\tnpm install node-cache --save\n```\n\nOr just require the `node_cache.js` file to get the superclass\n\n# Examples:\n\n## Initialize (INIT):\n\n```js\nconst NodeCache = require( \"node-cache\" );\nconst myCache = new NodeCache();\n```\n\n### Options\n\n- `stdTTL`: *(default: `0`)* the standard ttl as number in seconds for every generated cache element.\n`0` = unlimited\n- `checkperiod`: *(default: `600`)* The period in seconds, as a number, used for the automatic delete check interval.\n`0` = no periodic check.\n- `useClones`: *(default: `true`)* en/disable cloning of variables. If `true` you'll get a copy of the cached variable. If `false` you'll save and get just the reference.\n**Note:** `true` is recommended, because it'll behave like a server-based caching. You should set `false` if you want to save mutable objects or other complex types with mutability involved and wanted.\n_Here's a [simple code example](https://runkit.com/mpneuried/useclones-example-83) showing the different behavior_\n- `deleteOnExpire`: *(default: `true`)* whether variables will be deleted automatically when they expire.\nIf `true` the variable will be deleted. If `false` the variable will remain. You are encouraged to handle the variable upon the event `expired` by yourself.\n- `enableLegacyCallbacks`: *(default: `false`)* re-enables the usage of callbacks instead of sync functions. Adds an additional `cb` argument to each function which resolves to `(err, result)`. will be removed in node-cache v6.x.\n- `maxKeys`: *(default: `-1`)* specifies a maximum amount of keys that can be stored in the cache. If a new item is set and the cache is full, an error is thrown and the key will not be saved in the cache. -1 disables the key limit.\n\n```js\nconst NodeCache = require( \"node-cache\" );\nconst myCache = new NodeCache( { stdTTL: 100, checkperiod: 120 } );\n```\n\n**Since `4.1.0`**:\n*Key-validation*: The keys can be given as either `string` or `number`, but are casted to a `string` internally anyway.\nAll other types will throw an error.\n\n## Store a key (SET):\n\n`myCache.set( key, val, [ ttl ] )`\n\nSets a `key` `value` pair. It is possible to define a `ttl` (in seconds).\nReturns `true` on success.\n\n```js\nobj = { my: \"Special\", variable: 42 };\n\nsuccess = myCache.set( \"myKey\", obj, 10000 );\n// true\n```\n\n> Note: If the key expires based on it's `ttl` it will be deleted entirely from the internal data object.\n\n\n## Retrieve a key (GET):\n\n`myCache.get( key )`\n\nGets a saved value from the cache.\nReturns a `undefined` if not found or expired.\nIf the value was found it returns an object with the `key` `value` pair.\n\n```js\nvalue = myCache.get( \"myKey\" );\nif ( value == undefined ){\n\t// handle miss!\n}\n// { my: \"Special\", variable: 42 }\n```\n\n**Since `2.0.0`**:\n\nThe return format changed to a simple value and a `ENOTFOUND` error if not found *( as result instance of `Error` )\n\n**Since `2.1.0`**:\n\nThe return format changed to a simple value, but a due to discussion in #11 a miss shouldn't return an error.\nSo after 2.1.0 a miss returns `undefined`.\n\n**Since `3.1.0`**\n`errorOnMissing` option added\n\n```js\ntry{\n\t\tvalue = myCache.get( \"not-existing-key\", true );\n} catch( err ){\n\t\t// ENOTFOUND: Key `not-existing-key` not found\n}\n```\n\n## Get multiple keys (MGET):\n\n`myCache.mget( [ key1, key2, ..., keyn ] )`\n\nGets multiple saved values from the cache.\nReturns an empty object `{}` if not found or expired.\nIf the value was found it returns an object with the `key` `value` pair.\n\n```js\nvalue = myCache.mget( [ \"myKeyA\", \"myKeyB\" ] );\n/*\n\t{\n\t\t\"myKeyA\": { my: \"Special\", variable: 123 },\n\t\t\"myKeyB\": { the: \"Glory\", answer: 42 }\n\t}\n*/\n```\n\n**Since `2.0.0`**:\n\nThe method for mget changed from `.get( [ \"a\", \"b\" ] )` to `.mget( [ \"a\", \"b\" ] )`\n\n## Delete a key (DEL):\n\n`myCache.del( key )`\n\nDelete a key. Returns the number of deleted entries. A delete will never fail.\n\n```js\nvalue = myCache.del( \"A\" );\n// 1\n```\n\n## Delete multiple keys (MDEL):\n\n`myCache.del( [ key1, key2, ..., keyn ] )`\n\nDelete multiple keys. Returns the number of deleted entries. A delete will never fail.\n\n```js\nvalue = myCache.del( \"A\" );\n// 1\n\nvalue = myCache.del( [ \"B\", \"C\" ] );\n// 2\n\nvalue = myCache.del( [ \"A\", \"B\", \"C\", \"D\" ] );\n// 1 - because A, B and C not exists\n```\n\n## Change TTL (TTL):\n\n`myCache.ttl( key, ttl )`\n\nRedefine the ttl of a key. Returns true if the key has been found and changed. Otherwise returns false.\nIf the ttl-argument isn't passed the default-TTL will be used.\n\nThe key will be deleted when passing in a `ttl < 0`.\n\n```js\nmyCache = new NodeCache( { stdTTL: 100 } )\nchanged = myCache.ttl( \"existentKey\", 100 )\n// true\n\nchanged2 = myCache.ttl( \"missingKey\", 100 )\n// false\n\nchanged3 = myCache.ttl( \"existentKey\" )\n// true\n```\n\n## Get TTL (getTTL):\n\n`myCache.getTtl( key )`\n\nReceive the ttl of a key.\nYou will get:\n- `undefined` if the key does not exist\n- `0` if this key has no ttl\n- a timestamp in ms representing the time at which the key will expire\n\n```js\nmyCache = new NodeCache( { stdTTL: 100 } )\n\n// Date.now() = 1456000500000\nmyCache.set( \"ttlKey\", \"MyExpireData\" )\nmyCache.set( \"noTtlKey\", \"NonExpireData\", 0 )\n\nts = myCache.getTtl( \"ttlKey\" )\n// ts wil be approximately 1456000600000\n\nts = myCache.getTtl( \"ttlKey\" )\n// ts wil be approximately 1456000600000\n\nts = myCache.getTtl( \"noTtlKey\" )\n// ts = 0\n\nts = myCache.getTtl( \"unknownKey\" )\n// ts = undefined\n```\n\n## List keys (KEYS)\n\n`myCache.keys()`\n\nReturns an array of all existing keys.\n\n```js\nmykeys = myCache.keys();\n\nconsole.log( mykeys );\n// [ \"all\", \"my\", \"keys\", \"foo\", \"bar\" ]\n```\n\n## Has key (HAS)\n\n`myCache.has( key )`\n\nReturns boolean indicating if the key is cached.\n\n```js\n/* sync */\nexists = myCache.has( 'myKey' );\n\nconsole.log( exists );\n```\n\n## Statistics (STATS):\n\n`myCache.getStats()`\n\nReturns the statistics.\n\n```js\nmyCache.getStats();\n\t/*\n\t\t{\n\t\t\tkeys: 0,    // global key count\n\t\t\thits: 0,    // global hit count\n\t\t\tmisses: 0,  // global miss count\n\t\t\tksize: 0,   // global key size count in approximately bytes\n\t\t\tvsize: 0    // global value size count in approximately bytes\n\t\t}\n\t*/\n```\n\n## Flush all data (FLUSH):\n\n`myCache.flushAll()`\n\nFlush all data.\n\n```js\nmyCache.flushAll();\nmyCache.getStats();\n\t/*\n\t\t{\n\t\t\tkeys: 0,    // global key count\n\t\t\thits: 0,    // global hit count\n\t\t\tmisses: 0,  // global miss count\n\t\t\tksize: 0,   // global key size count in approximately bytes\n\t\t\tvsize: 0    // global value size count in approximately bytes\n\t\t}\n\t*/\n```\n\n## Close the cache:\n\n`myCache.close()`\n\nThis will clear the interval timeout which is set on check period option.\n\n```js\nmyCache.close();\n```\n\n# Events\n\n## set\n\nFired when a key has been added or changed.\nYou will get the `key` and the `value` as callback argument.\n\n```js\nmyCache.on( \"set\", function( key, value ){\n\t// ... do something ...\n});\n```\n\n## del\n\nFired when a key has been removed manually or due to expiry.\nYou will get the `key` and the deleted `value` as callback arguments.\n\n```js\nmyCache.on( \"del\", function( key, value ){\n\t// ... do something ...\n});\n```\n\n## expired\n\nFired when a key expires.\nYou will get the `key` and `value` as callback argument.\n\n```js\nmyCache.on( \"expired\", function( key, value ){\n\t// ... do something ...\n});\n```\n\n## flush\n\nFired when the cache has been flushed.\n\n```js\nmyCache.on( \"flush\", function(){\n\t// ... do something ...\n});\n```\n\n\n## Breaking changes\n\n### version `2.x`\n\nDue to the [Issue #11](https://github.com/mpneuried/nodecache/issues/11) the return format of the `.get()` method has been changed!\n\nInstead of returning an object with the key `{ \"myKey\": \"myValue\" }` it returns the value itself `\"myValue\"`.\n\n### version `3.x`\n\nDue to the [Issue #30](https://github.com/mpneuried/nodecache/issues/30) and [Issue #27](https://github.com/mpneuried/nodecache/issues/27) variables will now be cloned.\nThis could break your code, because for some variable types ( e.g. Promise ) its not possible to clone them.\nYou can disable the cloning by setting the option `useClones: false`. In this case it's compatible with version `2.x`.\n\n## Benchmarks\n\n### Version 1.1.x\n\nAfter adding io.js to the travis test here are the benchmark results for set and get of 100000 elements.\nBut be careful with this results, because it has been executed on travis machines, so it is not guaranteed, that it was executed on similar hardware.\n\n**node.js `0.10.36`**\nSET: `324`ms ( `3.24`µs per item )\nGET: `7956`ms ( `79.56`µs per item )\n\n**node.js `0.12.0`**\nSET: `432`ms ( `4.32`µs per item )\nGET: `42767`ms ( `427.67`µs per item )\n\n**io.js `v1.1.0`**\nSET: `510`ms ( `5.1`µs per item )\nGET: `1535`ms ( `15.35`µs per item )\n\n### Version 2.0.x\n\nAgain the same benchmarks by travis with version 2.0\n\n**node.js `0.6.21`**\nSET: `786`ms ( `7.86`µs per item )\nGET: `56`ms ( `0.56`µs per item )\n\n**node.js `0.10.36`**\nSET: `353`ms ( `3.53`µs per item )\nGET: `41`ms ( `0.41`µs per item )\n\n**node.js `0.12.2`**\nSET: `327`ms ( `3.27`µs per item )\nGET: `32`ms ( `0.32`µs per item )\n\n**io.js `v1.7.1`**\nSET: `238`ms ( `2.38`µs per item )\nGET: `34`ms ( `0.34`µs per item )\n\n> As you can see the version 2.x will increase the GET performance up to 200x in node 0.10.x.\nThis is possible because the memory allocation for the object returned by 1.x is very expensive.\n\n### Version 3.0.x\n\n*see [travis results](https://travis-ci.org/mpneuried/nodecache/builds/64560503)*\n\n**node.js `0.6.21`**\nSET: `786`ms ( `7.24`µs per item )\nGET: `56`ms ( `1.14`µs per item )\n\n**node.js `0.10.38`**\nSET: `353`ms ( `5.41`µs per item )\nGET: `41`ms ( `1.23`µs per item )\n\n**node.js `0.12.4`**\nSET: `327`ms ( `4.63`µs per item )\nGET: `32`ms ( `0.60`µs per item )\n\n**io.js `v2.1.0`**\nSET: `238`ms ( `4.06`µs per item )\nGET: `34`ms ( `0.67`µs per item )\n\n> until the version 3.0.x the object cloning is included, so we lost a little bit of the performance\n\n### Version 3.1.x\n\n**node.js `v0.10.41`**\nSET: `305ms`  ( `3.05µs` per item )\nGET: `104ms`  ( `1.04µs` per item )\n\n**node.js `v0.12.9`**\nSET: `337ms`  ( `3.37µs` per item )\nGET: `167ms`  ( `1.67µs` per item )\n\n**node.js `v4.2.6`**\nSET: `356ms`  ( `3.56µs` per item )\nGET: `83ms`  ( `0.83µs` per item )\n\n## Compatibility\n\nThis module should work well back until node `0.6.x`.\nBut it's only tested until version `0.10.x` because the build dependencies are not installable ;-) .\n\n## Release History\n|Version|Date|Description|\n|:--:|:--:|:--|\n|4.2.1|2019-07-22|Upgrade lodash to version 4.17.15 to suppress messages about unrelated security vulnerability|\n|4.2.0|2018-02-01|Add options.promiseValueSize for promise value. Thanks to [Ryan Roemer](https://github.com/ryan-roemer) for the pull [#84]; Added option `deleteOnExpire`; Added DefinitelyTyped Typescript definitions. Thanks to [Ulf Seltmann](https://github.com/useltmann) for the pulls [#90] and [#92]; Thanks to [Daniel Jin](https://github.com/danieljin) for the readme fix in pull [#93];  Optimized test and ci configs.|\n|4.1.1|2016-12-21|fix internal check interval for node < 0.10.25, thats the default node for ubuntu 14.04. Thanks to [Jimmy Hwang](https://github.com/JimmyHwang) for the pull [#78](https://github.com/mpneuried/nodecache/pull/78); added more docker tests|\n|4.1.0|2016-09-23|Added tests for different key types; Added key validation (must be `string` or `number`); Fixed `.del` bug where trying to delete a `number` key resulted in no deletion at all.|\n|4.0.0|2016-09-20|Updated tests to mocha; Fixed `.ttl` bug to not delete key on `.ttl( key, 0 )`. This is also relevant if `stdTTL=0`. *This causes the breaking change to `4.0.0`.*|\n|3.2.1|2016-03-21|Updated lodash to 4.x.; optimized grunt |\n|3.2.0|2016-01-29|Added method `getTtl` to get the time when a key expires. See [#49](https://github.com/mpneuried/nodecache/issues/49)|\n|3.1.0|2016-01-29|Added option `errorOnMissing` to throw/callback an error o a miss during a `.get( \"key\" )`. Thanks to [David Godfrey](https://github.com/david-byng) for the pull [#45](https://github.com/mpneuried/nodecache/pull/45). Added docker files and a script to run test on different node versions locally|\n|3.0.1|2016-01-13|Added `.unref()` to the checkTimeout so until node `0.10` it's not necessary to call `.close()` when your script is done. Thanks to [Doug Moscrop](https://github.com/dougmoscrop) for the pull [#44](https://github.com/mpneuried/nodecache/pull/44).|\n|3.0.0|2015-05-29|Return a cloned version of the cached element and save a cloned version of a variable. This can be disabled by setting the option `useClones:false`. (Thanks for #27 to [cheshirecatalyst](https://github.com/cheshirecatalyst) and for #30 to [Matthieu Sieben](https://github.com/matthieusieben))|\n|~~2.2.0~~|~~2015-05-27~~|REVOKED VERSION, because of conficts. See [Issue #30](https://github.com/mpneuried/nodecache/issues/30). So `2.2.0` is now `3.0.0`|\n|2.1.1|2015-04-17|Passed old value to the `del` event. Thanks to [Qix](https://github.com/qix) for the pull.|\n|2.1.0|2015-04-17|Changed get miss to return `undefined` instead of an error. Thanks to all [#11](https://github.com/mpneuried/nodecache/issues/11) contributors |\n|2.0.1|2015-04-17|Added close function (Thanks to [ownagedj](https://github.com/ownagedj)). Changed the development environment to use grunt.|\n|2.0.0|2015-01-05|changed return format of `.get()` with a error return on a miss and added the `.mget()` method. *Side effect: Performance of .get() up to 330 times faster!*|\n|1.1.0|2015-01-05|added `.keys()` method to list all existing keys|\n|1.0.3|2014-11-07|fix for setting numeric values. Thanks to [kaspars](https://github.com/kaspars) + optimized key ckeck.|\n|1.0.2|2014-09-17|Small change for better ttl handling|\n|1.0.1|2014-05-22|Readme typos. Thanks to [mjschranz](https://github.com/mjschranz)|\n|1.0.0|2014-04-09|Made `callback`s optional. So it's now possible to use a syncron syntax. The old syntax should also work well. Push : Bugfix for the value `0`|\n|0.4.1|2013-10-02|Added the value to `expired` event|\n|0.4.0|2013-10-02|Added nodecache events|\n|0.3.2|2012-05-31|Added Travis tests|\n\n[![NPM](https://nodei.co/npm-dl/node-cache.png?months=6)](https://nodei.co/npm/node-cache/)\n\n## Other projects\n\n|Name|Description|\n|:--|:--|\n|[**rsmq**](https://github.com/smrchy/rsmq)|A really simple message queue based on redis|\n|[**redis-heartbeat**](https://github.com/mpneuried/redis-heartbeat)|Pulse a heartbeat to redis. This can be used to detach or attach servers to nginx or similar problems.|\n|[**systemhealth**](https://github.com/mpneuried/systemhealth)|Node module to run simple custom checks for your machine or it's connections. It will use [redis-heartbeat](https://github.com/mpneuried/redis-heartbeat) to send the current state to redis.|\n|[**rsmq-cli**](https://github.com/mpneuried/rsmq-cli)|a terminal client for rsmq|\n|[**rest-rsmq**](https://github.com/smrchy/rest-rsmq)|REST interface for.|\n|[**redis-sessions**](https://github.com/smrchy/redis-sessions)|An advanced session store for NodeJS and Redis|\n|[**connect-redis-sessions**](https://github.com/mpneuried/connect-redis-sessions)|A connect or express middleware to simply use the [redis sessions](https://github.com/smrchy/redis-sessions). With [redis sessions](https://github.com/smrchy/redis-sessions) you can handle multiple sessions per user_id.|\n|[**redis-notifications**](https://github.com/mpneuried/redis-notifications)|A redis based notification engine. It implements the rsmq-worker to safely create notifications and recurring reports.|\n|[**nsq-logger**](https://github.com/mpneuried/nsq-logger)|Nsq service to read messages from all topics listed within a list of nsqlookupd services.|\n|[**nsq-topics**](https://github.com/mpneuried/nsq-topics)|Nsq helper to poll a nsqlookupd service for all it's topics and mirror it locally.|\n|[**nsq-nodes**](https://github.com/mpneuried/nsq-nodes)|Nsq helper to poll a nsqlookupd service for all it's nodes and mirror it locally.|\n|[**nsq-watch**](https://github.com/mpneuried/nsq-watch)|Watch one or many topics for unprocessed messages.|\n|[**hyperrequest**](https://github.com/mpneuried/hyperrequest)|A wrapper around [hyperquest](https://github.com/substack/hyperquest) to handle the results|\n|[**task-queue-worker**](https://github.com/smrchy/task-queue-worker)|A powerful tool for background processing of tasks that are run by making standard http requests\n|[**soyer**](https://github.com/mpneuried/soyer)|Soyer is small lib for server side use of Google Closure Templates with node.js.|\n|[**grunt-soy-compile**](https://github.com/mpneuried/grunt-soy-compile)|Compile Goggle Closure Templates ( SOY ) templates including the handling of XLIFF language files.|\n|[**backlunr**](https://github.com/mpneuried/backlunr)|A solution to bring Backbone Collections together with the browser fulltext search engine Lunr.js|\n|[**domel**](https://github.com/mpneuried/domel)|A simple dom helper if you want to get rid of jQuery|\n|[**obj-schema**](https://github.com/mpneuried/obj-schema)|Simple module to validate an object by a predefined schema|\n\n# The MIT License (MIT)\n\nCopyright © 2013 Mathias Peter, http://www.tcs.de\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "engines": {"node": ">= 0.4.6"}, "gitHead": "fccdd94354c7350d419a3458783388635d3e279f", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --require coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build", "test-docker": "SILENT_MODE=1 mocha test/mocha_test.js -R min && tsc"}, "_npmUser": {"name": "erdii", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"clone": "2.x"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"grunt": "^1.0.4", "mocha": "^6.1.4", "should": "11.x", "istanbul": "0.x", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^4.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_5.0.0-alpha.0_1568789193999_0.897644632490451", "host": "s3://npm-registry-packages"}}, "5.0.0-alpha.1": {"name": "node-cache", "version": "5.0.0-alpha.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@5.0.0-alpha.1", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "3ecded7566b629e0bd77e6bc77d17d18801cfc4f", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-5.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-yXJFnVE03RC0k1zcsdxBmwSeVWWzCarb0wpuo2z0wrwhleTS4rS+uiEEI8RVeyB/VqzMI/fLhmzftgduc/+F/A==", "signatures": [{"sig": "MEQCIDMQdF12XY1kxnFhCFld7igzDKDK4eIy2suElY+TcsFjAiBqufgbZIFGr5uHvLhHGsrneNGc91r8XAaGEQXGNO/63Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqjiCCRA9TVsSAnZWagAAXsQQAI90PkHx62SjEWBhOBLK\n7u3uCtjz8VKOfpm+O3Zm7gK9Zb3bxxdg0CcvXjE50Uljqm4z0t7OLm5PXlNu\np9wqYr64smivKoABtylS1t8jd4Kyi1uggI+RtGkkn0wo+VHyvzRpl8Hc9bki\nD9ecBFbzY+TVGzx4WyP5F5A4dFa+cIZOMTMtyaORPN48zSiFA71GZuwXsWcf\niJ0DwZCbXsDQnahvCgte7nZOKo7TdilyRJWiEISB08xWdA5BaUHtsSrlBxyK\nmXamfchwZsAAOWb+4HzuICxVF3LnxVlM5xhBAw7Ge63nUCjrqX0qMWdjM7b3\nBEy88hpdBg1pi74+ItVZIK1Vlmt2qL+ib4tlCh7oxgL5EkVYT3zXMK2vIiux\nmMUCNVYaOUAbyxPt9RoR/AQYVhQkx+7he7nL+QZlaVGzopn+U8fMkLAWLlCJ\ns2OfNUuqGkpbxeiJmZomhDGZiRANqS7oxqWLETNRWz1rUvi/zyw/W9uCN5zN\ndabewQfDQvtDH8A/eb3GxajMIcNBtekdsTI2he5N5nvEbD+LbxtJY8W2DnGc\nVIrgZKgGInbXszCehIJv46nntj22aIJ8fq+Y1o9MsFnmbD/kMRCPbZQQIpr4\n/ZUZfwIAwnRbtDPj8ac2QDaouhsNYroPyEMk0US7/gd9uLytqfAInX69elMn\nbgkR\r\n=pdGN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 0.4.6"}, "gitHead": "47394959fbee805a9f4d1b4b2cad60eca60530cd", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --require coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build"}, "_npmUser": {"name": "erdii", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"clone": "2.x"}, "_hasShrinkwrap": false, "devDependencies": {"grunt": "^1.0.4", "mocha": "^6.1.4", "should": "11.x", "istanbul": "0.x", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^4.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_5.0.0-alpha.1_1571436673607_0.7586838885340037", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "node-cache", "version": "5.0.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@5.0.0", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "3cbf44b15eb1f955a30f09caaa4037008e84729f", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-5.0.0.tgz", "fileCount": 9, "integrity": "sha512-Wtt0K7r8ojiziSvg7Ga5NZ9wSTTY0rRywnXaqB71NiXc2D6R4QdeQCzwOpAwKzIXZoGmj+ru5OfRnwYdi5T8rg==", "signatures": [{"sig": "MEUCIQDD/k5Hz4ETT9cS7A8qIJPKuCP+NKtbyGNGTtgYdNmFaAIgNP0FmCP6NKLvOg/WksvqbM0b7qAZIZQkXYL6p/bXkJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsIh/CRA9TVsSAnZWagAAauEP/0vUjMf7lc3iVsjKG4pJ\nFJuKozooZgEBjNa7XRfytd6oDXbArbcFwbH6TEFfxW0EJRe0MXUhi3x4u3Nv\nxRBZuP8t68EitNI9u3zDfcYNrOuy62P60PNYSntb/ANjOpNvjNbUf/wOHlFB\nwNZ7seVXS8aAIT2VKlaGVlRxiQdpiYXsYVEwaXztiNoBBnnhU6D9z8MvhrfT\n35JdAVQhBYoFDYbiHskmDnwReLriQ2M11v3ttDN56OivuiWjyVzbOclKLw2+\nati8EWQbE5LR/Iw2tAKDmLC6x09QXldRR/tfUFS09m8QPHju/pBARgX3rNjG\nB/VcU6GLYmw8NpS+PyrW2+NFae6jO7Zq8mo0huSct7HGkq09l0FcGJVs7/om\ndIROcNfpM5nbDOA6wC8VSM16DOKr+qMvQlRrwrzqu5Azt0038paYcrrbnjCA\n6EsVW9DsTZyw7FZTcnXp/WHtFH6vD6ieCLO5rQAVeEmed26Mhc5lH3t+vxVb\nKZfsFoIwYa3DJU9c84kbqTCAzBbR3Mx6eDXK+gLcvGeJXelBNKnM74whBmTT\nAK3dmHfal1nNmorp1/0UDokWtIQI1FhzlWGkQCpZv6cc7bvDNdYv8lqbmpjb\nnqJvSMuuAdKtFDpu3iN3yfZUQbC6f4EIo/2L8GHrJFHMtzxW3CN0jxBzRWJw\nUmRr\r\n=5bjF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 0.4.6"}, "gitHead": "5bd38eac7e832a86fdd5cab8dc596b040c423f75", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --require coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build"}, "_npmUser": {"name": "erdii", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"clone": "2.x"}, "_hasShrinkwrap": false, "devDependencies": {"grunt": "^1.0.4", "mocha": "^6.1.4", "should": "11.x", "istanbul": "^0.4.5", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^4.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_5.0.0_1571850367104_0.711926078585357", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "node-cache", "version": "5.0.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@5.0.1", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "ae9f4e4c486de4731391470bf518d5228ea4877e", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-5.0.1.tgz", "fileCount": 9, "integrity": "sha512-W+a/8TIkGThCdyDeKzDWqf3qUSHDc+Zo/a55AIfGWB5y7l2mMTtBbRya3nzyzB6FJap0vWIaa/4mnFVm6HGfew==", "signatures": [{"sig": "MEMCHz6G6Uwuy4stOD2eoiGz8Ub7Y5FUGcfV9FWH5i70WvwCIHwImBq2tRPmdwDMS7Ix/Snee8w83OWLH60Tlp5efjp1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduvjsCRA9TVsSAnZWagAAP3UP+wVuaIVClWVsGZC9a2q4\nFO3aFDPZaf5NlIDLcTiNCl4IzWZZbn4qOtJF9kNP5nMEjzjDqkbtqZwWCg1P\nRkoX/3w2RA2Mq2rcV3UN6ES4poti3P4B9eGOzwMhXDTP4SpzjPx8civZrOTo\nfwH6BqvxTqN7LFRwf2nl/COxuhPF038xECWeaMo0DtC8cms1Ban4KXP/qse5\nkk55xpsPxRWfmkAygxo3LenGn0tsPQkIZ7FIbv3ziNawWvozYwVNBE+rWkL9\nArcKCpkv1NCiHrYoNl1BVPyrAxkHz0J2bAoxU/ISMks6ekFz1EYNEsmfjum4\nGZsssLTN3iNkxm4Ch0RMMdA8P9BF5mRvBUtnp4A2U9bvizrAI0Clwwjr5dJ9\niyi1nPZxy0ywEityACk/ihsKOd0hgGzPTLmcGU6HJP6xm9/9bl3mP5rnYdVE\nLrmFlaXjVs4ZALXI9hLL4LEA8Eij4oooL9rJwDobuBpEcjlDNNoY8+KqFjT0\nIPc9ZWitns3g1ATOgJep/doy+p0F5hcFTLEsYocRbHBA4Z1xvN3oefZ1SsMA\nHVdpiRkPSGV35o2xrWBQWOpdFArmR18qFGUYB17EV3Njk6in9rAVVrgzQ5yn\nFYtKefikJp/6/kbl8v5R7yH6JkLQaR2ZeBWRxCPrb+FawsDbAovbuRg+/BmA\nB4B9\r\n=voD6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 0.4.6"}, "gitHead": "f5f2cb87ff149c2bbae454bcc72a41321b723d2f", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --require coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build"}, "_npmUser": {"name": "erdii", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"clone": "2.x"}, "_hasShrinkwrap": false, "devDependencies": {"grunt": "^1.0.4", "mocha": "^6.1.4", "should": "11.x", "istanbul": "^0.4.5", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^4.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_5.0.1_1572534508007_0.40356600724923264", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "node-cache", "version": "5.0.2", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@5.0.2", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "f295ce2329f215da726e54c508bc4c5e2e0b4de9", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-5.0.2.tgz", "fileCount": 9, "integrity": "sha512-wqJIYqBbOaYycAHNXgWx7fYJA5S3KcwpJqwuDuHWGKzzG8vIvj80eN47+/FTioUdE96AuiTHoAtsiTVOBLrM9A==", "signatures": [{"sig": "MEYCIQDv1gGAD9/19FBrdSAQmhPyemxyonQ4CaApRw5dMQoa8AIhANB/uSQurbpL+a8IeHZenCUYL4yz53jnv6SQHHpOLHJM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0XNyCRA9TVsSAnZWagAAD9oQAJ1L08obwhci/Q/1fgWt\n8P/mQarayNNDwZajt6UnbZYOsvhd/Ye3nUuVN/nTvld163l5uIzby9i8BgM9\nKX5sOOkECgrZmBR4Wx14xyrNniE1qs87jRfHu9J1EseUOGBqXm3eLUbXxYOD\nGUZhtlfw9/NYZq/JjkpMwHP8gjY5r7YXq886mWUzMUeMk2YqbvXJmnHhIFuu\nXmZteq2VXehzwsGneaDOLMIYHXhUPfpNn2CLUAHtLbJ7X19PCoxFCkveWW0O\nCjLHnd2aac1VmmOFGNqwgL8x6QJ+7IHC44HxYr/xdBuFqEYzmfNhKKZYZWSt\nF/Z8hndMTis93fTl0oYqisPfM9onS5aNNRdt8ZYa/kU99sbIs1NHsS658yoR\n7SIJBHIkILDoMa97bevMbICG0kIUnKGYGhBrqWEE9MhOn+dpeUzVuknQbAMW\nKjnYJ8BzJZZok9HwQRAovJGA3f65Wk38THgpBs1Br2PeyPZiv0JrRTsCs/o7\n4RhqafE+TmI5t92G+kTvKJl6DXeKWwzWlIN4MxiK88ryOUBK073zKzyIfRwA\no7gogknS4i8vbAuKz8Wypi/CvhbSRoHmAZfDh+viTiLld+QfOejSlyQK1E3t\nvRk/clI58QKUHyb3fds8t6yAFBnpazitW+TnQM/erVfjJ/k7B+deCCJNTUhT\nzt7h\r\n=auVx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 0.4.6"}, "gitHead": "6117b2b5cd7bd9467e630d03eb2c5d62479ec945", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --require coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build"}, "_npmUser": {"name": "erdii", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"clone": "2.x"}, "_hasShrinkwrap": false, "devDependencies": {"grunt": "^1.0.4", "mocha": "^6.1.4", "should": "11.x", "istanbul": "^0.4.5", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^4.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_5.0.2_1574007665813_0.8637787610516328", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "node-cache", "version": "5.1.0", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@5.1.0", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/mpneuried/nodecache", "bugs": {"url": "https://github.com/mpneuried/nodecache/issues"}, "dist": {"shasum": "266786c28dcec0fd34385ee29c383e6d6f1aa5de", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-5.1.0.tgz", "fileCount": 9, "integrity": "sha512-gFQwYdoOztBuPlwg6DKQEf50G+gkK69aqLnw4djkmlHCzeVrLJfwvg9xl4RCAGviTIMUVoqcyoZ/V/wPEu/VVg==", "signatures": [{"sig": "MEUCIQCB3BrdAI/3ImLM9vVfDuY/XA8fytndlGQ/0lF2VgIj3wIgZM2KUBv2ct9rZoyDrwy+7+X3bGB5ezFlMccI7A7efVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7PmpCRA9TVsSAnZWagAAfg0P+wQogK9r0dhRATiKMjML\nGTUJZ4i1zBk7kb4L+dy44U4xa7eutrf8nKgebvfvUPT0oGNM14r1t1TpEuP/\nwB4eBVlRyIRgYtqkLLXllEm9PYDvVBzWGgqCEudjt+SMoo2II6sJnhvC9INy\nZSQq8AETSBnRxZ04uOHioOINRunw38hzWoVR6vNph0tLeXtTN2NQW7Xi7fqC\nsBvNlGK70RnlUH0TKfZZ3JnJzegKbORyfg2lvtoBIZ47ibz7hStJMd+T1eOH\n+Rg+QH9Hv/Pbg8ZeDZnLde0UTJht3X1rJo/e061ayeW5girPt4FTEvOMbUK/\nXSbpthGM+Vxc9Eih4ggcDh3SNt/ikkQrexiYu8Is+Z5P0g5xc9lYSJdQ8+iq\n5QSMYiwsMHpUT0BOlfXaUC6qMAraHFXPaIw6c4mhfsdnPtWlSFPRzUQtaShI\nPKzJExxc5ZWSvjf6dfkUUj8kDUlWhOSpX2dZAKZN8E9JfzG1csSryUnLjzTI\nxTJkuRV1xaApcWuSHWbgQXloNCjSA0OIZjX82HEl4ExaOCAW1+3ieek4vRm5\n7MDaAEthejaDVGqwCMlpXc21V4YjjIDdoviRCwDalGT5EdDurX+u5obc0tpd\nXOjYmv4Hmt+nLVDel3+QT2gZbcJQLq74WpOxfNjDjtDZeBIrkgFbp7IpqbHV\nnEvH\r\n=kCoN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 0.4.6"}, "gitHead": "81fa811182ad6c5735b7e0a26dd024c7e21f145b", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --require coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build"}, "_npmUser": {"name": "erdii", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mpneuried/nodecache.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"clone": "2.x"}, "_hasShrinkwrap": false, "devDependencies": {"grunt": "^1.0.4", "mocha": "^6.1.4", "should": "11.x", "istanbul": "^0.4.5", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^4.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_5.1.0_1575811496718_0.9469063312253707", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "node-cache", "version": "5.1.1", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@5.1.1", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/node-cache/node-cache", "bugs": {"url": "https://github.com/node-cache/node-cache/issues"}, "dist": {"shasum": "5fcc887176b23bdcd19cd1461b9544d2d501e786", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-5.1.1.tgz", "fileCount": 9, "integrity": "sha512-bJ9nH25Z51HG2QIu66K4dMVyMs6o8bNQpviDnXzG+O/gfNxPU9IpIig0j4pzlO707GcGZ6QA4rWhlRxjJsjnZw==", "signatures": [{"sig": "MEUCIHeA7zsnyz0hvgCanigbaSQ4ai9RnFmRFEvXNFj8kRdRAiEAuyiVM1EiEx8NGosC86O728AK8V/RFpp2zrK+/xx5zok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe25hwCRA9TVsSAnZWagAANRsP/j03diOjHFv/m+CuCCEL\nkU5Yp+OjHJoVUHazPgnUKFL1Kdtygr381gGepECj8F4FbleOr8GxqE9oMkBE\nb9fY+IT4bwwoKF3EZd18wEXX2jOJYhSNJDtR7eWS/zwRWavJp3bRGWEv3lYQ\nro+HRIA8MIP+s3+1MntzLoatwvoxoq3W1uhz7DShFlo0KzX9w3P8/yBtOjHe\nBUdkSHxdZWVPnzwj/GM0bmlnJ8Mec/TXz3OZQtLyMWari8EKdMDUn4qUuWWT\nIdVews/Wzp8BktePxISsrOSAuZnCNr04xPJF+9AI3hRruqSWuygqIBPU1d7Q\nWrhsZWbpebfszZF9pi8FAf4kDTCQeRwuxMpFBpluEjeF6J5wIYlBZFybim+U\nPfM3zXuWINF2GiYJDvxt5IpDaOMyFPkmtX5rXxUi7Zfupdl9uAjlBWIt5frM\n46k34Ai/VxzGWnk/0PGDojmJUNUE2gCxwBegivm55V8oulj8A7+W0RVU60na\naFF/TjHPaQd5jGLe2jf2U+N04mvLAxw1NZLNyOTJ2Ukiu7zNcYK8KAg3K6H3\ngR0+hvyn5y2k1+LzpVQI6GMzHJlLWljoOWi0OP5M3YyD2OCVDfCxtVXB8odF\nWKdjrEoOThqtO8Ra0K18QDe3gnWpTsxiZSUf7XjFqOoeKTYCHqfE/YXgar/P\nP2SR\r\n=ZoP/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 8.0.0"}, "gitHead": "1ae11d04b01e953525dc5f755ed1112e762139a4", "scripts": {"test": "COFFEECOV_INIT_ALL=false mocha --require coffee-script/register --require coffee-coverage/register-istanbul _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build"}, "_npmUser": {"name": "erdii", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-cache/node-cache.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"clone": "2.x"}, "_hasShrinkwrap": false, "devDependencies": {"grunt": "^1.0.4", "mocha": "^7.2.0", "should": "11.x", "istanbul": "^0.4.5", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^6.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_5.1.1_1591449711638_0.7976327124923828", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "node-cache", "version": "5.1.2", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "_id": "node-cache@5.1.2", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "homepage": "https://github.com/node-cache/node-cache", "bugs": {"url": "https://github.com/node-cache/node-cache/issues"}, "dist": {"shasum": "f264dc2ccad0a780e76253a694e9fd0ed19c398d", "tarball": "https://registry.npmjs.org/node-cache/-/node-cache-5.1.2.tgz", "fileCount": 10, "integrity": "sha512-t1QzWwnk4sjLWaQAS8CHgOJ+RAfmHpxFWmc36IWTiWHQfs0w5JDMBS1b1ZxQteo0vVVuWJvIUKHDkkeK7vIGCg==", "signatures": [{"sig": "MEQCIC2wT/rdW0L6gOo1Q/JR93fIv/FEKKCfSQRqaVyx8pr2AiBGYHZ2zc0aOGQZDtiZH60g5lvuOwa7RsOhacH+dcIXYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/LGMCRA9TVsSAnZWagAAoDEP/0tf4NC5lVA+/AjKQSgl\nAL4eriCA4o9FdxSseGdX9171NxfaDm+dfkkJsNsBuTQae8H8iP6hmFSQU7hH\nuCUflRFOtNruyyK6ExdIwE3PMrn3Pj3mzxe4vqI5SSJq54K/2weKkxcNa4sf\nRNCGBDivs3wTuVPY/hKwAEfSJZwMkfbgMjrO9cGK/m1AbHT+wOOOgUKp0XDX\nimwG4CZmweBK2iwcHSPbMuW1HMbzFvrjxHn+LbV2lzzv4dCx9/th1nza75uK\nPmguIcHHPKJcxU7LM3ReKyijDovIxXB0VBOx7bDl9FtmO+soetwDhIzPLXne\nG1+OxZWeqv58ypyk0beTF3jl1VAk0S1PCH7o8sWX1EeH1HqzhdKOj+QFeHaB\ny2F0bUWk6ZgXFT/UIZ3UMKdYWsz9q+FK1VezEIUX6xlpF0kgEypegi/1ye9n\n6yh0Ak1jP81182IoEVCu+U02GT5WbA+lR0E2zQB1Jt+bTyFJX+zp4BnOR8tf\nbkwS81gEzHVWxPqeMWxP2vpijElkvgHIo+QLIH/xCgcGFL+uagSqco0EOLcQ\nmhDYnyuQmKXNYsdgODaupRUwbfGtLo7firUGLzoQ82XTLwDAjn3FQjDxY9I4\nN22+enFHN8zVGZKUZcJYGZ/nKT19bRY8BiAi0/IAIx5n8cV46dFDVx/X4DJw\n9Bft\r\n=yODh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "tags": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "types": "./index.d.ts", "engines": {"node": ">= 8.0.0"}, "gitHead": "b64434a8303c5881145c68754b674478e714ca3a", "scripts": {"test": "nyc --require coffee-script/register mocha _src/test/mocha_test.coffee -R spec && tsc", "build": "grunt build", "export-coverage": "nyc report --reporter=text-lcov > lcov.info"}, "_npmUser": {"name": "erdii", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/node-cache/node-cache.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"clone": "2.x"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.1.0", "grunt": "^1.0.4", "mocha": "^7.2.0", "should": "11.x", "coveralls": "^3.0.3", "grunt-cli": "^1.2.0", "grunt-run": "^0.8.1", "typescript": "^2.6.1", "@types/node": "^8.9.4", "grunt-banner": "0.6.x", "coffee-script": "1.x", "coffee-coverage": "^3.0.1", "grunt-mocha-cli": "^6.0.0", "grunt-contrib-clean": "1.0.x", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-coffee": "^2.1.0", "grunt-include-replace": "3.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/node-cache_5.1.2_1593618828143_0.5178932268295193", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-10-20T12:00:03.206Z", "modified": "2024-10-22T17:26:04.116Z", "0.1.0": "2011-10-20T12:00:04.799Z", "0.1.1": "2011-10-20T12:10:08.261Z", "0.1.3": "2011-10-20T12:57:14.518Z", "0.2.0": "2011-10-20T14:52:16.323Z", "0.3.0": "2012-05-15T06:53:12.615Z", "0.3.1": "2012-05-16T09:28:59.753Z", "0.3.2": "2012-05-31T08:11:22.209Z", "0.4.0": "2013-10-02T06:54:01.929Z", "0.4.1": "2014-02-26T07:11:09.940Z", "1.0.0": "2014-04-09T09:49:02.878Z", "1.0.2": "2014-09-17T10:21:55.688Z", "1.0.3": "2014-11-07T12:44:15.130Z", "1.1.0": "2015-01-05T08:42:55.915Z", "2.0.0": "2015-01-05T08:50:08.252Z", "2.0.1": "2015-04-17T07:20:50.536Z", "2.1.0": "2015-04-17T07:47:11.844Z", "2.1.1": "2015-04-17T08:05:14.635Z", "2.2.0": "2015-05-27T09:38:55.827Z", "3.0.0": "2015-05-29T12:31:15.331Z", "3.0.1": "2016-01-13T07:13:11.311Z", "3.1.0": "2016-01-29T09:31:37.034Z", "3.2.0": "2016-02-24T11:44:31.184Z", "3.2.1": "2016-03-21T07:38:07.733Z", "4.0.0": "2016-09-20T10:21:23.522Z", "4.1.0": "2016-09-23T08:10:08.732Z", "4.1.1": "2016-12-21T11:27:05.176Z", "4.2.0": "2018-03-21T14:06:48.533Z", "4.2.1": "2019-07-24T20:53:29.621Z", "5.0.0-alpha.0": "2019-09-18T06:46:34.218Z", "5.0.0-alpha.1": "2019-10-18T22:11:13.744Z", "5.0.0": "2019-10-23T17:06:07.280Z", "5.0.1": "2019-10-31T15:08:28.137Z", "5.0.2": "2019-11-17T16:21:05.967Z", "5.1.0": "2019-12-08T13:24:56.898Z", "5.1.1": "2020-06-06T13:21:51.751Z", "5.1.2": "2020-07-01T15:53:48.316Z"}, "bugs": {"url": "https://github.com/node-cache/node-cache/issues"}, "author": {"name": "mpneuried", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/node-cache/node-cache", "keywords": ["cache", "caching", "local", "variable", "multi", "memory", "internal", "node", "memcached", "object"], "repository": {"url": "git://github.com/node-cache/node-cache.git", "type": "git"}, "description": "Simple and fast NodeJS internal caching. Node internal in memory cache like memcached.", "maintainers": [{"name": "erdii", "email": "<EMAIL>"}, {"name": "tcs-de", "email": "<EMAIL>"}], "readme": "![Logo](./logo/logo.png)\n\n[![Node.js CI](https://github.com/node-cache/node-cache/workflows/Node.js%20CI/badge.svg?branch=master)](https://github.com/node-cache/node-cache/actions?query=workflow%3A%22Node.js+CI%22+branch%3A%22master%22)\n![Dependency status](https://img.shields.io/david/node-cache/node-cache)\n[![NPM package version](https://img.shields.io/npm/v/node-cache?label=npm%20package)](https://www.npmjs.com/package/node-cache)\n[![NPM monthly downloads](https://img.shields.io/npm/dm/node-cache)](https://www.npmjs.com/package/node-cache)\n[![GitHub issues](https://img.shields.io/github/issues/node-cache/node-cache)](https://github.com/node-cache/node-cache/issues)\n[![Coveralls Coverage](https://img.shields.io/coveralls/node-cache/node-cache.svg)](https://coveralls.io/github/node-cache/node-cache)\n\n# Simple and fast NodeJS internal caching.\n\nA simple caching module that has `set`, `get` and `delete` methods and works a little bit like memcached.\nKeys can have a timeout (`ttl`) after which they expire and are deleted from the cache.\nAll keys are stored in a single object so the practical limit is at around 1m keys.\n\n\n## BREAKING MAJOR RELEASE v5.x\n\nThe recent 5.x release:\n* dropped support for node versions before 8.x!\n* removed the callback-based api from all methods (you can re-enable them with the option `enableLegacyCallbacks`)\n\n## BREAKING MAJOR RELEASE v6.x UPCOMING\n\nAlthough not breaking per definition, our typescript rewrite will change internal functions and their names.\nPlease get in contact with us, if you are using some parts of node-cache's internal api so we can work something out!\n\n\n# Install\n\n```bash\n\tnpm install node-cache --save\n```\n\nOr just require the `node_cache.js` file to get the superclass\n\n# Examples:\n\n## Initialize (INIT):\n\n```js\nconst NodeCache = require( \"node-cache\" );\nconst myCache = new NodeCache();\n```\n\n### Options\n\n- `stdTTL`: *(default: `0`)* the standard ttl as number in seconds for every generated cache element.\n`0` = unlimited\n- `checkperiod`: *(default: `600`)* The period in seconds, as a number, used for the automatic delete check interval.\n`0` = no periodic check.\n- `useClones`: *(default: `true`)* en/disable cloning of variables. If `true` you'll get a copy of the cached variable. If `false` you'll save and get just the reference.  \n**Note:**\n\t- `true` is recommended if you want **simplicity**, because it'll behave like a server-based cache (it caches copies of plain data).\n\t- `false` is recommended if you want to achieve **performance** or save mutable objects or other complex types with mutability involved and wanted, because it'll only store references of your data.\n\t- _Here's a [simple code example](https://runkit.com/mpneuried/useclones-example-83) showing the different behavior_\n- `deleteOnExpire`: *(default: `true`)* whether variables will be deleted automatically when they expire.\nIf `true` the variable will be deleted. If `false` the variable will remain. You are encouraged to handle the variable upon the event `expired` by yourself.\n- `enableLegacyCallbacks`: *(default: `false`)* re-enables the usage of callbacks instead of sync functions. Adds an additional `cb` argument to each function which resolves to `(err, result)`. will be removed in node-cache v6.x.\n- `maxKeys`: *(default: `-1`)* specifies a maximum amount of keys that can be stored in the cache. If a new item is set and the cache is full, an error is thrown and the key will not be saved in the cache. -1 disables the key limit.\n\n```js\nconst NodeCache = require( \"node-cache\" );\nconst myCache = new NodeCache( { stdTTL: 100, checkperiod: 120 } );\n```\n\n**Since `4.1.0`**:\n*Key-validation*: The keys can be given as either `string` or `number`, but are casted to a `string` internally anyway.\nAll other types will throw an error.\n\n## Store a key (SET):\n\n`myCache.set( key, val, [ ttl ] )`\n\nSets a `key` `value` pair. It is possible to define a `ttl` (in seconds).\nReturns `true` on success.\n\n```js\nobj = { my: \"Special\", variable: 42 };\n\nsuccess = myCache.set( \"myKey\", obj, 10000 );\n// true\n```\n\n> Note: If the key expires based on it's `ttl` it will be deleted entirely from the internal data object.\n\n\n## Store multiple keys (MSET):\n\n`myCache.mset(Array<{key, val, ttl?}>)`\n\nSets multiple `key` `val` pairs. It is possible to define a `ttl` (seconds).\nReturns `true` on success.\n\n```js\nconst obj = { my: \"Special\", variable: 42 };\nconst obj2 = { my: \"other special\", variable: 1337 };\n\nconst success = myCache.mset([\n\t{key: \"myKey\", val: obj, ttl: 10000},\n\t{key: \"myKey2\", val: obj2},\n])\n```\n\n## Retrieve a key (GET):\n\n`myCache.get( key )`\n\nGets a saved value from the cache.\nReturns a `undefined` if not found or expired.\nIf the value was found it returns the `value`.\n\n```js\nvalue = myCache.get( \"myKey\" );\nif ( value == undefined ){\n\t// handle miss!\n}\n// { my: \"Special\", variable: 42 }\n```\n\n**Since `2.0.0`**:\n\nThe return format changed to a simple value and a `ENOTFOUND` error if not found *( as result instance of `Error` )\n\n**Since `2.1.0`**:\n\nThe return format changed to a simple value, but a due to discussion in #11 a miss shouldn't return an error.\nSo after 2.1.0 a miss returns `undefined`.\n\n## Take a key (TAKE):\n\n`myCache.take( key )`\n\nget the cached value and remove the key from the cache.  \nEquivalent to calling `get(key)` + `del(key)`.  \nUseful for implementing `single use` mechanism such as OTP, where once a value is read it will become obsolete.\n\n```js\nmyCache.set( \"myKey\", \"myValue\" )\nmyCache.has( \"myKey\" ) // returns true because the key is cached right now\nvalue = myCache.take( \"myKey\" ) // value === \"myValue\"; this also deletes the key\nmyCache.has( \"myKey\" ) // returns false because the key has been deleted\n```\n\n## Get multiple keys (MGET):\n\n`myCache.mget( [ key1, key2, ..., keyn ] )`\n\nGets multiple saved values from the cache.\nReturns an empty object `{}` if not found or expired.\nIf the value was found it returns an object with the `key` `value` pair.\n\n```js\nvalue = myCache.mget( [ \"myKeyA\", \"myKeyB\" ] );\n/*\n\t{\n\t\t\"myKeyA\": { my: \"Special\", variable: 123 },\n\t\t\"myKeyB\": { the: \"Glory\", answer: 42 }\n\t}\n*/\n```\n\n**Since `2.0.0`**:\n\nThe method for mget changed from `.get( [ \"a\", \"b\" ] )` to `.mget( [ \"a\", \"b\" ] )`\n\n## Delete a key (DEL):\n\n`myCache.del( key )`\n\nDelete a key. Returns the number of deleted entries. A delete will never fail.\n\n```js\nvalue = myCache.del( \"A\" );\n// 1\n```\n\n## Delete multiple keys (MDEL):\n\n`myCache.del( [ key1, key2, ..., keyn ] )`\n\nDelete multiple keys. Returns the number of deleted entries. A delete will never fail.\n\n```js\nvalue = myCache.del( \"A\" );\n// 1\n\nvalue = myCache.del( [ \"B\", \"C\" ] );\n// 2\n\nvalue = myCache.del( [ \"A\", \"B\", \"C\", \"D\" ] );\n// 1 - because A, B and C not exists\n```\n\n## Change TTL (TTL):\n\n`myCache.ttl( key, ttl )`\n\nRedefine the ttl of a key. Returns true if the key has been found and changed. Otherwise returns false.\nIf the ttl-argument isn't passed the default-TTL will be used.\n\nThe key will be deleted when passing in a `ttl < 0`.\n\n```js\nmyCache = new NodeCache( { stdTTL: 100 } )\nchanged = myCache.ttl( \"existentKey\", 100 )\n// true\n\nchanged2 = myCache.ttl( \"missingKey\", 100 )\n// false\n\nchanged3 = myCache.ttl( \"existentKey\" )\n// true\n```\n\n## Get TTL (getTTL):\n\n`myCache.getTtl( key )`\n\nReceive the ttl of a key.\nYou will get:\n- `undefined` if the key does not exist\n- `0` if this key has no ttl\n- a timestamp in ms representing the time at which the key will expire\n\n```js\nmyCache = new NodeCache( { stdTTL: 100 } )\n\n// Date.now() = 1456000500000\nmyCache.set( \"ttlKey\", \"MyExpireData\" )\nmyCache.set( \"noTtlKey\", \"NonExpireData\", 0 )\n\nts = myCache.getTtl( \"ttlKey\" )\n// ts wil be approximately 1456000600000\n\nts = myCache.getTtl( \"ttlKey\" )\n// ts wil be approximately 1456000600000\n\nts = myCache.getTtl( \"noTtlKey\" )\n// ts = 0\n\nts = myCache.getTtl( \"unknownKey\" )\n// ts = undefined\n```\n\n## List keys (KEYS)\n\n`myCache.keys()`\n\nReturns an array of all existing keys.\n\n```js\nmykeys = myCache.keys();\n\nconsole.log( mykeys );\n// [ \"all\", \"my\", \"keys\", \"foo\", \"bar\" ]\n```\n\n## Has key (HAS)\n\n`myCache.has( key )`\n\nReturns boolean indicating if the key is cached.\n\n```js\nexists = myCache.has( 'myKey' );\n\nconsole.log( exists );\n```\n\n## Statistics (STATS):\n\n`myCache.getStats()`\n\nReturns the statistics.\n\n```js\nmyCache.getStats();\n\t/*\n\t\t{\n\t\t\tkeys: 0,    // global key count\n\t\t\thits: 0,    // global hit count\n\t\t\tmisses: 0,  // global miss count\n\t\t\tksize: 0,   // global key size count in approximately bytes\n\t\t\tvsize: 0    // global value size count in approximately bytes\n\t\t}\n\t*/\n```\n\n## Flush all data (FLUSH):\n\n`myCache.flushAll()`\n\nFlush all data.\n\n```js\nmyCache.flushAll();\nmyCache.getStats();\n\t/*\n\t\t{\n\t\t\tkeys: 0,    // global key count\n\t\t\thits: 0,    // global hit count\n\t\t\tmisses: 0,  // global miss count\n\t\t\tksize: 0,   // global key size count in approximately bytes\n\t\t\tvsize: 0    // global value size count in approximately bytes\n\t\t}\n\t*/\n```\n\n## Flush the stats (FLUSH STATS):\n\n`myCache.flushStats()`\n\nFlush the stats.\n\n```js\nmyCache.flushStats();\nmyCache.getStats();\n\t/*\n\t\t{\n\t\t\tkeys: 0,    // global key count\n\t\t\thits: 0,    // global hit count\n\t\t\tmisses: 0,  // global miss count\n\t\t\tksize: 0,   // global key size count in approximately bytes\n\t\t\tvsize: 0    // global value size count in approximately bytes\n\t\t}\n\t*/\n```\n\n## Close the cache:\n\n`myCache.close()`\n\nThis will clear the interval timeout which is set on check period option.\n\n```js\nmyCache.close();\n```\n\n# Events\n\n## set\n\nFired when a key has been added or changed.\nYou will get the `key` and the `value` as callback argument.\n\n```js\nmyCache.on( \"set\", function( key, value ){\n\t// ... do something ...\n});\n```\n\n## del\n\nFired when a key has been removed manually or due to expiry.\nYou will get the `key` and the deleted `value` as callback arguments.\n\n```js\nmyCache.on( \"del\", function( key, value ){\n\t// ... do something ...\n});\n```\n\n## expired\n\nFired when a key expires.\nYou will get the `key` and `value` as callback argument.\n\n```js\nmyCache.on( \"expired\", function( key, value ){\n\t// ... do something ...\n});\n```\n\n## flush\n\nFired when the cache has been flushed.\n\n```js\nmyCache.on( \"flush\", function(){\n\t// ... do something ...\n});\n```\n\n## flush_stats\n\nFired when the cache stats has been flushed.\n\n```js\nmyCache.on( \"flush_stats\", function(){\n\t// ... do something ...\n});\n```\n\n\n## Breaking changes\n\n### version `2.x`\n\nDue to the [Issue #11](https://github.com/mpneuried/nodecache/issues/11) the return format of the `.get()` method has been changed!\n\nInstead of returning an object with the key `{ \"myKey\": \"myValue\" }` it returns the value itself `\"myValue\"`.\n\n### version `3.x`\n\nDue to the [Issue #30](https://github.com/mpneuried/nodecache/issues/30) and [Issue #27](https://github.com/mpneuried/nodecache/issues/27) variables will now be cloned.\nThis could break your code, because for some variable types ( e.g. Promise ) its not possible to clone them.\nYou can disable the cloning by setting the option `useClones: false`. In this case it's compatible with version `2.x`.\n\n### version `5.x`\n\nCallbacks are deprecated in this version. They are still useable when enabling the `enableLegacyCallbacks` option when initializing the cache. Callbacks will be completely removed in `6.x`.\n\n## Compatibility\n\nNode-Cache supports all node versions >= 8\n\n## Release History\n|Version|Date|Description|\n|:--:|:--:|:--|\n|5.1.1|2020-06-06|[#184], [#183] thanks [Jonah Werre](https://github.com/jwerre) for reporting [#181]!, [#180], Thanks [Titus](https://github.com/tstone) for [#169]!, Thanks [Ianfeather](https://github.com/Ianfeather) for [#168]!, Thanks [Adam Haglund](https://github.com/BeeeQueue) for [#176]|\n|5.1.0|2019-12-08|Add .take() from PR [#160] and .flushStats from PR [#161]. Thanks to [Sujesh Thekkepatt](https://github.com/sujeshthekkepatt) and [Gopalakrishna Palem](https://github.com/KrishnaPG)!|\n|5.0.2|2019-11-17|Fixed bug where expired values were deleted even though `deleteOnExpire` was set to `false`. Thanks to [fielding-wilson](https://github.com/fielding-wilson)!|\n|5.0.1|2019-10-31|Fixed bug where users could not set null values. Thanks to [StefanoSega](https://github.com/StefanoSega), [jwest23](https://github.com/jwest23) and [marudor](https://github.com/marudor)!|\n|5.0.0|2019-10-23|Remove lodash dependency, add .has(key) and .mset([{key,val,ttl}]) methods to the cache. Thanks to [Regev Brody](https://github.com/regevbr) for PR [#132] and [Sujesh Thekkepatt](https://github.com/sujeshthekkepatt) for PR [#142]! Also thank you, to all other contributors that remain unnamed here!|\n|4.2.1|2019-07-22|Upgrade lodash to version 4.17.15 to suppress messages about unrelated security vulnerability|\n|4.2.0|2018-02-01|Add options.promiseValueSize for promise value. Thanks to [Ryan Roemer](https://github.com/ryan-roemer) for the pull [#84]; Added option `deleteOnExpire`; Added DefinitelyTyped Typescript definitions. Thanks to [Ulf Seltmann](https://github.com/useltmann) for the pulls [#90] and [#92]; Thanks to [Daniel Jin](https://github.com/danieljin) for the readme fix in pull [#93];  Optimized test and ci configs.|\n|4.1.1|2016-12-21|fix internal check interval for node < 0.10.25, thats the default node for ubuntu 14.04. Thanks to [Jimmy Hwang](https://github.com/JimmyHwang) for the pull [#78](https://github.com/mpneuried/nodecache/pull/78); added more docker tests|\n|4.1.0|2016-09-23|Added tests for different key types; Added key validation (must be `string` or `number`); Fixed `.del` bug where trying to delete a `number` key resulted in no deletion at all.|\n|4.0.0|2016-09-20|Updated tests to mocha; Fixed `.ttl` bug to not delete key on `.ttl( key, 0 )`. This is also relevant if `stdTTL=0`. *This causes the breaking change to `4.0.0`.*|\n|3.2.1|2016-03-21|Updated lodash to 4.x.; optimized grunt |\n|3.2.0|2016-01-29|Added method `getTtl` to get the time when a key expires. See [#49](https://github.com/mpneuried/nodecache/issues/49)|\n|3.1.0|2016-01-29|Added option `errorOnMissing` to throw/callback an error o a miss during a `.get( \"key\" )`. Thanks to [David Godfrey](https://github.com/david-byng) for the pull [#45](https://github.com/mpneuried/nodecache/pull/45). Added docker files and a script to run test on different node versions locally|\n|3.0.1|2016-01-13|Added `.unref()` to the checkTimeout so until node `0.10` it's not necessary to call `.close()` when your script is done. Thanks to [Doug Moscrop](https://github.com/dougmoscrop) for the pull [#44](https://github.com/mpneuried/nodecache/pull/44).|\n|3.0.0|2015-05-29|Return a cloned version of the cached element and save a cloned version of a variable. This can be disabled by setting the option `useClones:false`. (Thanks for #27 to [cheshirecatalyst](https://github.com/cheshirecatalyst) and for #30 to [Matthieu Sieben](https://github.com/matthieusieben))|\n|~~2.2.0~~|~~2015-05-27~~|REVOKED VERSION, because of conficts. See [Issue #30](https://github.com/mpneuried/nodecache/issues/30). So `2.2.0` is now `3.0.0`|\n|2.1.1|2015-04-17|Passed old value to the `del` event. Thanks to [Qix](https://github.com/qix) for the pull.|\n|2.1.0|2015-04-17|Changed get miss to return `undefined` instead of an error. Thanks to all [#11](https://github.com/mpneuried/nodecache/issues/11) contributors |\n|2.0.1|2015-04-17|Added close function (Thanks to [ownagedj](https://github.com/ownagedj)). Changed the development environment to use grunt.|\n|2.0.0|2015-01-05|changed return format of `.get()` with a error return on a miss and added the `.mget()` method. *Side effect: Performance of .get() up to 330 times faster!*|\n|1.1.0|2015-01-05|added `.keys()` method to list all existing keys|\n|1.0.3|2014-11-07|fix for setting numeric values. Thanks to [kaspars](https://github.com/kaspars) + optimized key ckeck.|\n|1.0.2|2014-09-17|Small change for better ttl handling|\n|1.0.1|2014-05-22|Readme typos. Thanks to [mjschranz](https://github.com/mjschranz)|\n|1.0.0|2014-04-09|Made `callback`s optional. So it's now possible to use a syncron syntax. The old syntax should also work well. Push : Bugfix for the value `0`|\n|0.4.1|2013-10-02|Added the value to `expired` event|\n|0.4.0|2013-10-02|Added nodecache events|\n|0.3.2|2012-05-31|Added Travis tests|\n\n[![NPM](https://nodei.co/npm-dl/node-cache.png?months=6)](https://nodei.co/npm/node-cache/)\n\n## Other projects\n\n|Name|Description|\n|:--|:--|\n|[**rsmq**](https://github.com/smrchy/rsmq)|A really simple message queue based on redis|\n|[**redis-heartbeat**](https://github.com/mpneuried/redis-heartbeat)|Pulse a heartbeat to redis. This can be used to detach or attach servers to nginx or similar problems.|\n|[**systemhealth**](https://github.com/mpneuried/systemhealth)|Node module to run simple custom checks for your machine or it's connections. It will use [redis-heartbeat](https://github.com/mpneuried/redis-heartbeat) to send the current state to redis.|\n|[**rsmq-cli**](https://github.com/mpneuried/rsmq-cli)|a terminal client for rsmq|\n|[**rest-rsmq**](https://github.com/smrchy/rest-rsmq)|REST interface for.|\n|[**redis-sessions**](https://github.com/smrchy/redis-sessions)|An advanced session store for NodeJS and Redis|\n|[**connect-redis-sessions**](https://github.com/mpneuried/connect-redis-sessions)|A connect or express middleware to simply use the [redis sessions](https://github.com/smrchy/redis-sessions). With [redis sessions](https://github.com/smrchy/redis-sessions) you can handle multiple sessions per user_id.|\n|[**redis-notifications**](https://github.com/mpneuried/redis-notifications)|A redis based notification engine. It implements the rsmq-worker to safely create notifications and recurring reports.|\n|[**nsq-logger**](https://github.com/mpneuried/nsq-logger)|Nsq service to read messages from all topics listed within a list of nsqlookupd services.|\n|[**nsq-topics**](https://github.com/mpneuried/nsq-topics)|Nsq helper to poll a nsqlookupd service for all it's topics and mirror it locally.|\n|[**nsq-nodes**](https://github.com/mpneuried/nsq-nodes)|Nsq helper to poll a nsqlookupd service for all it's nodes and mirror it locally.|\n|[**nsq-watch**](https://github.com/mpneuried/nsq-watch)|Watch one or many topics for unprocessed messages.|\n|[**hyperrequest**](https://github.com/mpneuried/hyperrequest)|A wrapper around [hyperquest](https://github.com/substack/hyperquest) to handle the results|\n|[**task-queue-worker**](https://github.com/smrchy/task-queue-worker)|A powerful tool for background processing of tasks that are run by making standard http requests\n|[**soyer**](https://github.com/mpneuried/soyer)|Soyer is small lib for server side use of Google Closure Templates with node.js.|\n|[**grunt-soy-compile**](https://github.com/mpneuried/grunt-soy-compile)|Compile Goggle Closure Templates ( SOY ) templates including the handling of XLIFF language files.|\n|[**backlunr**](https://github.com/mpneuried/backlunr)|A solution to bring Backbone Collections together with the browser fulltext search engine Lunr.js|\n|[**domel**](https://github.com/mpneuried/domel)|A simple dom helper if you want to get rid of jQuery|\n|[**obj-schema**](https://github.com/mpneuried/obj-schema)|Simple module to validate an object by a predefined schema|\n\n# The MIT License (MIT)\n\nCopyright © 2019 Mathias Peter and the node-cache maintainers, https://github.com/node-cache/node-cache\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "users": {"jhq": true, "hema": true, "j3kz": true, "mkwr": true, "alien": true, "erdii": true, "kazet": true, "makay": true, "patoi": true, "pospi": true, "wujr5": true, "adamlu": true, "ahirel": true, "beytek": true, "bojand": true, "chrisx": true, "gfilip": true, "jrthib": true, "koslun": true, "macmac": true, "quafoo": true, "smrchy": true, "vegera": true, "aerjotl": true, "barenko": true, "hisplan": true, "mic-css": true, "mndavec": true, "sharper": true, "sopepos": true, "swookie": true, "wgerven": true, "csscottc": true, "djamseed": true, "farkbarn": true, "jamie452": true, "jsmarkus": true, "kingcron": true, "leonning": true, "litmaj0r": true, "tinyhill": true, "zuojiang": true, "alexbudin": true, "asawq2006": true, "blitzprog": true, "chrisyipw": true, "elkdanger": true, "grumpycat": true, "igorissen": true, "milfromoz": true, "peremenov": true, "rkopylkov": true, "roccomuso": true, "wechangel": true, "abdihaikal": true, "acjohnso25": true, "alin.alexa": true, "blackbunny": true, "donvercety": true, "esilva2902": true, "ismaelvsqz": true, "justinshea": true, "krishaamer": true, "lordfelipe": true, "micahjonas": true, "ngsoftware": true, "onufrienko": true, "pietrovich": true, "rocket0191": true, "yasinaydin": true, "arnoldstoba": true, "battlesnake": true, "esraa-ammar": true, "flumpus-dev": true, "ganeshkbhat": true, "joypeterson": true, "juliomatcom": true, "kodekracker": true, "micaelsouza": true, "vparaskevas": true, "yeahoffline": true, "ahmedfarooki": true, "davidepedone": true, "eladkeyshawn": true, "nickeltobias": true, "plashchynski": true, "tobiasnickel": true, "program247365": true, "quintonparker": true, "vivek.vikhere": true, "bigglesatlarge": true, "jakub.knejzlik": true, "andrew.medvedev": true, "federico-garcia": true, "joaquin.briceno": true, "lorenzo.disidoro": true, "anygivensolutions": true}}