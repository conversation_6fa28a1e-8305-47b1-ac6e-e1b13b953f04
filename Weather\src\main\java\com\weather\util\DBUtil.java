package com.weather.util;

import com.alibaba.druid.pool.DruidDataSource;
import java.sql.Connection;
import java.sql.SQLException;

public class DBUtil {
    private static DruidDataSource dataSource;
    
    static {
        dataSource = new DruidDataSource();
        dataSource.setUrl("*********************************************************************************************");
        dataSource.setUsername("root");
        dataSource.setPassword("123456"); // 修改为你的数据库密码
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setInitialSize(5);
        dataSource.setMaxActive(20);
        dataSource.setMinIdle(5);
        dataSource.setMaxWait(60000);
    }
    
    public static Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }
    
    public static void closeDataSource() {
        if (dataSource != null) {
            dataSource.close();
        }
    }
}