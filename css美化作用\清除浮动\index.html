<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .container{
            background-color: red;
            width:600px;

        }
        .container::after{
            content:"";
            display:block;
            clear:both;
        }
        .box{
        height:100px;
        width:100px;
        background-color: blue;
        float:left;
        }
        .text{
            height:100px;
            width:100px;
            clear:both;
            background-color: aqua;
        }
    </style>
</head>
<body>
    <p>浮动元素会造成父元素高度塌陷，后续元素月会受到影响</p>
    <p>1.给父元素添加高度 2.对受影响元素使用clear属性清除左浮动或者右浮动3.有父级塌陷，同级元素也受到了影响，在父级标签里加overflow:hidden;clear:both 4.同三情况可以用伪对象形式来写,三和四不可以一起写</p>
    <div class="container">
     <div class="box"></div>
     <div class="box"></div>
     <div class="box"></div>
     <div class="text"></div>
    </div>
</body>
</html>