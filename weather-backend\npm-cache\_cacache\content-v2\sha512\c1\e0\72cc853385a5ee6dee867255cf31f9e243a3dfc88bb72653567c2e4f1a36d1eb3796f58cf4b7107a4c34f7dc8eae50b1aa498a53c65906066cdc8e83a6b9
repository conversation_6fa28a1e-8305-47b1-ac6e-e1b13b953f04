{"_id": "jest-changed-files", "_rev": "292-9206c6b87ba406026340d02c70d14ba9", "name": "jest-changed-files", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.5"}, "versions": {"0.0.0": {"name": "jest-changed-files", "version": "0.0.0", "_id": "jest-changed-files@0.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ef4e3e7386aad9cd3b6de7fdf286df7a9d1dd17b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-0.0.0.tgz", "integrity": "sha512-C6RCaB31rBk/84sr/M5tnaqOUCJghM+jl3s9TaTordcXHb3hageSC+u5eV3ClDQ1j7Gvvbz3DMFD+9nTMxVl5g==", "signatures": [{"sig": "MEYCIQCc6bgG9PHcpjSMMO2aqZhdcWdC2QMi9l2D9Fg+fgQK9AIhALRpurDTKJQCqFUW7rI1G32Uo0hn+3idz3UtYBGvbNXv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "ef4e3e7386aad9cd3b6de7fdf286df7a9d1dd17b", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "_npmVersion": "3.8.6", "description": "", "directories": {}, "_nodeVersion": "6.0.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-0.0.0.tgz_1462925382843_0.3541862729471177", "host": "packages-12-west.internal.npmjs.com"}}, "12.0.2": {"name": "jest-changed-files", "version": "12.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@12.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34b02c2a022430f91ccbd0c49a9ec4aa13a52db1", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-12.0.2.tgz", "integrity": "sha512-xn+qNQgdZ3G+++pKGOT9Ay50/xwSkpsh1drfKwWhZelZBvab+fzESV1qPLxSIUPVcuebxcnXHeE13vhpSsJ5lw==", "signatures": [{"sig": "MEQCIEHMNHNXuGHaLAIJWW6ztW5z191AkISkSXeHlQFDas6XAiA01D1yGBdUC0AZH5hUXkMQCL+ADc77II2EAkMUKhlYWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "node"}, "main": "src/index.js", "_from": ".", "_shasum": "34b02c2a022430f91ccbd0c49a9ec4aa13a52db1", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-12.0.2.tgz_1463446368205_0.677485344465822", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.0": {"name": "jest-changed-files", "version": "12.1.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@12.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7aef2c698b226f539fd4ee7eaa32dc2172e87f49", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-12.1.0.tgz", "integrity": "sha512-Fa8YR+Se3JLd7/qzcz0+4aqqpEbkkOvHTqCXbNPNpQ266xaSRgB6GcfhLoowYqNJo5ykDw8ntP/MKWkRqdZyEA==", "signatures": [{"sig": "MEQCIE5k7k+teKopXEh5upLSXrF93fUCQSWLUW/bxKi0AdhuAiAzOz+TXOAF+me9oDSRZe4ozfFDH3UhGenjk7TxGnqxbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "node"}, "main": "src/index.js", "_from": ".", "_shasum": "7aef2c698b226f539fd4ee7eaa32dc2172e87f49", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-12.1.0.tgz_1463767563250_0.5847075893543661", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.1-alpha.2935e14d": {"name": "jest-changed-files", "version": "12.1.1-alpha.2935e14d", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@12.1.1-alpha.2935e14d", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f3aded43a0f6c580ac58f15ff81a0c734461bc20", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-12.1.1-alpha.2935e14d.tgz", "integrity": "sha512-5tYYLA6eYU0mPC53CKzn47gz+0i/wuqOXd/njpJlwB2BEdDicj5wI2y0ZEQWfKHBxPpGEgE74w13lUHfWWrgKQ==", "signatures": [{"sig": "MEYCIQCXfSvD7cBsEzqgavtoOISsibaolQMAtdF6sb0vdJIPpgIhAN8ejnTLfR4RbVBVNbeNEhdBcwituH7NSIX+NcP8Wd3V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "f3aded43a0f6c580ac58f15ff81a0c734461bc20", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-12.1.1-alpha.2935e14d.tgz_1466141593766_0.3707309947349131", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.2-alpha.a482b15c": {"name": "jest-changed-files", "version": "12.1.2-alpha.a482b15c", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@12.1.2-alpha.a482b15c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a67f716063afa409dd24907ca2d9d6953cb71957", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-12.1.2-alpha.a482b15c.tgz", "integrity": "sha512-1r9wIZCaXfTWCOnQZ2z66EZakeUeTQbQQn/08NCnu68VTcpINufyM0oK6i6j7CGMK6pNBiKfyL1/V37SztTMEw==", "signatures": [{"sig": "MEUCIHcdiPsKpUZq1hj0vEErQhqvbhcz0icsPi8RqtIllVLMAiEA/Hj4M2c8BCHm8fp+vezi2ulzthzWV96Ogt8gQXrDceA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "a67f716063afa409dd24907ca2d9d6953cb71957", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-12.1.2-alpha.a482b15c.tgz_1466150284555_0.18535276921465993", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.2-alpha.6230044c": {"name": "jest-changed-files", "version": "12.1.2-alpha.6230044c", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@12.1.2-alpha.6230044c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "845fb5e81a878dc98ea4593ea25361aef2d0eb5b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-12.1.2-alpha.6230044c.tgz", "integrity": "sha512-52X4LsQ2bqK0FPaA1mmLvZiRUbks5QDIdQXAqo4Bj6NfZxvdzql9Frn2E6zHe4pxcoVgoqX/clNYvn2a24WGBw==", "signatures": [{"sig": "MEYCIQDcBQC3/UshhXrOv7WeIzxdJhzInmdnerXUpSYalkERmgIhAJOtkDM739Os8FrXk44I9bZt7a6eNI1+Q0cjetcChYEM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "845fb5e81a878dc98ea4593ea25361aef2d0eb5b", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-12.1.2-alpha.6230044c.tgz_1466536516263_0.6967394454404712", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.3-alpha.6230044c": {"name": "jest-changed-files", "version": "12.1.3-alpha.6230044c", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@12.1.3-alpha.6230044c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "718872d71e0b1073951b8daf4f9a241ef4536b29", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-12.1.3-alpha.6230044c.tgz", "integrity": "sha512-p/S3aVj/kNJNy5csXI3lmrYvw+Pjak0dEETk7HiHgWAsGkn+CUnkDaVq2kVzA5W/q65+TO7ZrDMovS4Z+tcpkg==", "signatures": [{"sig": "MEYCIQCiErOf/4MHY+RJQx49/QsyhMvHKrsbLwZ5bN55m/YrfQIhAOD4fVTX3ar1nx/p/Tok8t5MnFkS93MbtoOs0VjVrSe+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "718872d71e0b1073951b8daf4f9a241ef4536b29", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-12.1.3-alpha.6230044c.tgz_1466539911653_0.2202991705853492", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.4-alpha.a737c6e5": {"name": "jest-changed-files", "version": "12.1.4-alpha.a737c6e5", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@12.1.4-alpha.a737c6e5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4de6addb32485f027dc9c767bd67c0f91a1825dc", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-12.1.4-alpha.a737c6e5.tgz", "integrity": "sha512-BoGzo5GUzjuoU00IDQR/+YD/KY+kADSCvIAJQdI89g9RC5yboscFfJI0yZPoff2e5xoGLOwRcTl97zeZot2+RQ==", "signatures": [{"sig": "MEYCIQCZW1txd6m8l6GptmbdJFmNzk5pit3Zu3CcjgAypPXHGgIhAMyA15n0tjdS5J1uwXm88Ssr+VuKIdvk97jSJn636wud", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "4de6addb32485f027dc9c767bd67c0f91a1825dc", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-12.1.4-alpha.a737c6e5.tgz_1466566603652_0.1888587309513241", "host": "packages-16-east.internal.npmjs.com"}}, "12.1.5-alpha.b5322422": {"name": "jest-changed-files", "version": "12.1.5-alpha.b5322422", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@12.1.5-alpha.b5322422", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a790a4ed440e4d853de1a5180ec4830ec75f5f25", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-12.1.5-alpha.b5322422.tgz", "integrity": "sha512-RGadKqTkXakNSMdemqVn4UccUcVxtwNhORIn7wLfpLCDbgLCDCRqPTyYTQenSHPwSVl94SnP1S+iD/3lxx+piA==", "signatures": [{"sig": "MEYCIQD9mSl6/96WGDdxljcLsn4LpPzYEgSOP1uPNpooMi+UoAIhALL9G0m4ZDHVDgRtgGDOerIViFI1zU9cYQ5geRJuxQYX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "a790a4ed440e4d853de1a5180ec4830ec75f5f25", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-12.1.5-alpha.b5322422.tgz_1466577950039_0.04983102669939399", "host": "packages-12-west.internal.npmjs.com"}}, "13.0.0": {"name": "jest-changed-files", "version": "13.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "199ac7b76a930ebb77628ad01fd0b2612e2b815b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.0.0.tgz", "integrity": "sha512-vhbenO4egCIDDwnPFPNcRoiGdnpHWwhuhY3qME9Bha2U67+Avs1wkAckz84jxpxUw7173dA8jjdEu27tnJbGvA==", "signatures": [{"sig": "MEUCIBqdBg9z6d3hsIB0BkmnCfrEXWu9sI0nv4QWFEh83YFVAiEAm3NrehhDp2/Ip06jHN8c0CVNn3XZK2iyEZu31JlfVw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "199ac7b76a930ebb77628ad01fd0b2612e2b815b", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.0.0.tgz_1466626697987_0.9411414009518921", "host": "packages-12-west.internal.npmjs.com"}}, "13.2.1": {"name": "jest-changed-files", "version": "13.2.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.2.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ca4f76a23bece81e5f5f271aa439ee8a6eb3c312", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.2.1.tgz", "integrity": "sha512-J<PERSON><PERSON>uhntZB5lQuDX+juhZKPlq37Uoehsc58f1E7dfa5mehYyPW5g6Z1JAtb3PotbTzReiiZMOy62lm6Gl9O+Pw==", "signatures": [{"sig": "MEUCIQC/Mv9e9uyrs5PSahcPlqjxGUr60C7i90BConeuH8s3ogIgNb3W1Ju/awAeTlGwCicC6zBs96X44LzaQ34+U0Xx+/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "ca4f76a23bece81e5f5f271aa439ee8a6eb3c312", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.2.1.tgz_1467856779003_0.2908498761244118", "host": "packages-16-east.internal.npmjs.com"}}, "13.2.2": {"name": "jest-changed-files", "version": "13.2.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.2.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2ffe93dc6e291cbe3779149076153e2184678f4b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.2.2.tgz", "integrity": "sha512-0WNQM+ECytZx37EpGOTH3/p4nMcKRh4i7CevBbisFZqXTli63B3fPdZ+pSC0x1maJgKhPspaLM0q/6RGZowH4g==", "signatures": [{"sig": "MEYCIQDlwLgfNlrgVWyS0FPOFzwA/nNww4QUI0BCgP9VN7rfvQIhAObGj7N99DU9PsQ3iyDMZ9wlKxpAczBOWOQSrBpaN0mp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node"}, "main": "build/index.js", "_from": ".", "_shasum": "2ffe93dc6e291cbe3779149076153e2184678f4b", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.2.2.tgz_1467857584292_0.06788002862595022", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.a44f195f": {"name": "jest-changed-files", "version": "13.3.0-alpha.a44f195f", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.3.0-alpha.a44f195f", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d268642e13d41fd758bd91f0f03d703cb6b67126", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.3.0-alpha.a44f195f.tgz", "integrity": "sha512-H6Gxmj4zyqjC4uAajxDa9iScLRtyAPZOVepYz2HufZqg6BIe5DMuIKaRxY5klao59WZ0itaJe7/+FJAA6UygPA==", "signatures": [{"sig": "MEQCIDRSwIPlVS9Afr7xVg2sD50jXuhjSG85vp3RnAIyfAeJAiBHcC3DTVJ8XPnNE7o0GXJbBasQkzYsOMR5mVsfA2mHCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "d268642e13d41fd758bd91f0f03d703cb6b67126", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.3.0-alpha.a44f195f.tgz_1468231009996_0.1897469365503639", "host": "packages-16-east.internal.npmjs.com"}}, "13.3.0-alpha.4eb0c908": {"name": "jest-changed-files", "version": "13.3.0-alpha.4eb0c908", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.3.0-alpha.4eb0c908", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "996a6c462d6e948fb2de7e28bc1f396bf6580e15", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.3.0-alpha.4eb0c908.tgz", "integrity": "sha512-yKbv5BXF/4SP0zx6V19UBKgz1eqvwmvvjbhfjZGwj5FQnIwpjX5Pb2v2ZkPWe4YjYp/bXyNh4CUGwydmHEh0vw==", "signatures": [{"sig": "MEUCIFZRDIP/SbAdLqg+gxJYUgtv0W5JDAWeYtG49IMTjCYiAiEAgSyNCNJf9qVdd2p/kVVYjSrCXcTtACitCezh2YWRIjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "996a6c462d6e948fb2de7e28bc1f396bf6580e15", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.3.0-alpha.4eb0c908.tgz_1468231475742_0.8988678324967623", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.ffc7404b": {"name": "jest-changed-files", "version": "13.3.0-alpha.ffc7404b", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.3.0-alpha.ffc7404b", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f9660699c07d3d2e4e02ff5e32f438726436ba58", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.3.0-alpha.ffc7404b.tgz", "integrity": "sha512-7Y97gWg7j08gq527TUoTcNVhtoKFJweBCQf8/Ywp/9S3UbNBjYZn1VDWoIaU8FpWvTrx9e5c2X/D3/v5jfmsQQ==", "signatures": [{"sig": "MEYCIQCs9D8u4QxDX+txXimZ2ARJayr2ptm1GiuNdszOCadSFgIhAMtBmNBmu1w1OnmoPJK1VykRrovZiFiiX2Tg0zk6WBUn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "f9660699c07d3d2e4e02ff5e32f438726436ba58", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.3.0-alpha.ffc7404b.tgz_1468232412307_0.012105314759537578", "host": "packages-16-east.internal.npmjs.com"}}, "13.3.0-alpha.8b48d59e": {"name": "jest-changed-files", "version": "13.3.0-alpha.8b48d59e", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.3.0-alpha.8b48d59e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6dc30ff266150f8cfdbb36e4a43bc4a4d81d07b3", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.3.0-alpha.8b48d59e.tgz", "integrity": "sha512-SaZbAOxfMew55Bj2300zDhrGyeOzfbsymjSa5ooJsiIVRRDQBArEuzkV2xBT1Z9dqScKeu+YvLJeCwSilSkmtw==", "signatures": [{"sig": "MEUCIQD09gUWSZO2HjMA7AwNWwjzXIKagmrblxOcvtcjOZG2RwIgBQaML2PmA6F5S25nET0toI+QxJTchwlEfC0X8sRHezY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "6dc30ff266150f8cfdbb36e4a43bc4a4d81d07b3", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.3.0-alpha.8b48d59e.tgz_1468390447313_0.13230618042871356", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.g8b48d59": {"name": "jest-changed-files", "version": "13.3.0-alpha.g8b48d59", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.3.0-alpha.g8b48d59", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "876e9ea6fbb5915e704546ddb183a81c7820c3c9", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.3.0-alpha.g8b48d59.tgz", "integrity": "sha512-BPtAV0YSGt3KOv4NTLA3MbPZvX4TDsynhn5mN9zNEKwjBuox2PYb0FFJuTrhIY+DykFfTElzO9InFyf6C8OMKA==", "signatures": [{"sig": "MEUCIQDgyXom8wc9Ew5sdxagXDVcMNbTmS68iIab5SXCet5K7wIgCZRAt/t74uRlv0XUFRILb7x67wHOiwKmz8SApN9GcQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "876e9ea6fbb5915e704546ddb183a81c7820c3c9", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.3.0-alpha.g8b48d59.tgz_1468391980865_0.19306636950932443", "host": "packages-12-west.internal.npmjs.com"}}, "13.4.0-alpha.d2632006": {"name": "jest-changed-files", "version": "13.4.0-alpha.d2632006", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@13.4.0-alpha.d2632006", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9fe23b3ac90ba3059401c4beb64c29702c8d940d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-13.4.0-alpha.d2632006.tgz", "integrity": "sha512-W8ub917XX3SqBAcp7Qadl948qi6jyKN3haRZG/R42y2bQ3FiichATk2E4OMTYK0GDZPv9CFtYqAeSWUStQ84cg==", "signatures": [{"sig": "MEUCIFZliE+bYFIXhgcHx9ERG0xm4TeKUtezrkXWOtdW/+9WAiEAviEhPen3fkKxDduGrbG36782EIJIp8EFCIdFyaXJkjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "9fe23b3ac90ba3059401c4beb64c29702c8d940d", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-13.4.0-alpha.d2632006.tgz_1469609832315_0.45186572102829814", "host": "packages-16-east.internal.npmjs.com"}}, "14.0.0": {"name": "jest-changed-files", "version": "14.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@14.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7a3b1b15973aabab2e06a30b55842014d2799bf2", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-14.0.0.tgz", "integrity": "sha512-qq5V2aCewxbYywOUqH+QQnpkRRi95+2cEiYYUIie9Jm3bbRJtay1ID6zDSBHrTNs8iqIC7buoedgKoTW0ycXoQ==", "signatures": [{"sig": "MEYCIQDX+1qa0lXSycqOD/r451nNgJSi6GlC8St6aHQ/Nyd4NwIhAPofYbo/TtyLZbnd+ZrfkIMzQ15xbO70Wpc/2E0Oet67", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "7a3b1b15973aabab2e06a30b55842014d2799bf2", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-14.0.0.tgz_1469610878734_0.25176737690344453", "host": "packages-12-west.internal.npmjs.com"}}, "14.2.0-alpha.ca8bfb6e": {"name": "jest-changed-files", "version": "14.2.0-alpha.ca8bfb6e", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@14.2.0-alpha.ca8bfb6e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "afed07d86ca7fe165d9fa698ba0f75b98907ddf6", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-14.2.0-alpha.ca8bfb6e.tgz", "integrity": "sha512-R9RyJ0y2iqUu34YhsWVFyGl71+YuCAyH15/iZ/bqUJW0losrJN5uKvs7zpT04i4P1FMBBCpjhz+Ydz/xv2J/LA==", "signatures": [{"sig": "MEQCIBCaoJJ/8zaqGjpEHLGjY05+QIoCbmpaJheLkt+8JrhsAiBb6FH2B0+kVl/6IxrMbQpSXyDzNWkoKUdx3ir47W2zJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "afed07d86ca7fe165d9fa698ba0f75b98907ddf6", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-14.2.0-alpha.ca8bfb6e.tgz_1471287276748_0.9236804496031255", "host": "packages-16-east.internal.npmjs.com"}}, "14.2.1-alpha.e21d71a4": {"name": "jest-changed-files", "version": "14.2.1-alpha.e21d71a4", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@14.2.1-alpha.e21d71a4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5339c45e96c34e420d09a555540d50044e86457b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-14.2.1-alpha.e21d71a4.tgz", "integrity": "sha512-TPnxpDhiwcqMm+r47dnBtAPVGDoBxJj35dm9bF7V6F2ebhSq21AkyHF/pTcfV4ppVuVJhdopwmG1nAZgbR4QcQ==", "signatures": [{"sig": "MEUCIA+EHK+TqSjRaWwIe0Q2jt9FLf6qwHxF7NRgReSdIrfUAiEAhvvA1NnmCaakJD/lXR7UnxcBdnwqeFv83I+EvoiAatU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "5339c45e96c34e420d09a555540d50044e86457b", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-14.2.1-alpha.e21d71a4.tgz_1471382317092_0.1620715083554387", "host": "packages-12-west.internal.npmjs.com"}}, "14.2.2-alpha.22bd3c33": {"name": "jest-changed-files", "version": "14.2.2-alpha.22bd3c33", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@14.2.2-alpha.22bd3c33", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "52999efd25dd223a5fd288f05be2550eb7dca0a1", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-14.2.2-alpha.22bd3c33.tgz", "integrity": "sha512-cbzTHIxB+trLrLlBjbfCHc2k4S7uOkRjbVK1+A14GQcs9m5hcREM7EUpnZXJV9BQi3QlEb0DcsPwKou1WtLocA==", "signatures": [{"sig": "MEUCIHhwM5wIiStB3qrkmVkR4FJmeP/E8W+7uqmbeusUjTGWAiEAzeBEk3Eeqq44yz0LgatLX9LUIzOe1bLp/2erbxB/KW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "52999efd25dd223a5fd288f05be2550eb7dca0a1", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-14.2.2-alpha.22bd3c33.tgz_1471388093327_0.5952252452261746", "host": "packages-12-west.internal.npmjs.com"}}, "14.3.0-alpha.d13c163e": {"name": "jest-changed-files", "version": "14.3.0-alpha.d13c163e", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@14.3.0-alpha.d13c163e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d32e609f7a6ff94bbe13bc9da28fc4b214cfa346", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-14.3.0-alpha.d13c163e.tgz", "integrity": "sha512-wZIzP8OQ1syRyYpbiTGeqmaG6ChkFfL1GnYqcUFRsQzZ5C2NjzdE2IPk5S4jUpQBx8rhBAfJIwvOPwR8iXM4Kw==", "signatures": [{"sig": "MEYCIQCfOql1Bx4uSvVWMkWNkpJ0h0vs/0ILo06MKhXPxhE4AAIhAPCFPX/uvdzrrn6L2FFRcMTLjfuUnMySPUkuZZ5Z7gT2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "d32e609f7a6ff94bbe13bc9da28fc4b214cfa346", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-14.3.0-alpha.d13c163e.tgz_1471552524628_0.5858340270351619", "host": "packages-16-east.internal.npmjs.com"}}, "14.3.1-alpha.410cb91a": {"name": "jest-changed-files", "version": "14.3.1-alpha.410cb91a", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@14.3.1-alpha.410cb91a", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4dd16f49fe1731aff4356ca2e46ceb3df2030dc4", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-14.3.1-alpha.410cb91a.tgz", "integrity": "sha512-AIdjvdKZY0nlTIxQHezqdLSI6UyGrfSzU6rjhbI5MxYRgFxGyOw994pKtRurLpjcWU5uxZBIjfyp3LDL7MZajg==", "signatures": [{"sig": "MEUCIGPHC1Mq2RrRfEWQ+82fchwWxZ8yxKKIT6PVBilf1/79AiEAvvkckHqMWrYOzbQuPu8YirN+DTTq8EX5bfbd5kYOc90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "4dd16f49fe1731aff4356ca2e46ceb3df2030dc4", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.5.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-14.3.1-alpha.410cb91a.tgz_1472593926969_0.9019386025611311", "host": "packages-16-east.internal.npmjs.com"}}, "14.3.2-alpha.83c25417": {"name": "jest-changed-files", "version": "14.3.2-alpha.83c25417", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@14.3.2-alpha.83c25417", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7dddf2764760d0d13e2361901bd960c922543c4d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-14.3.2-alpha.83c25417.tgz", "integrity": "sha512-zaC67bdhBgY+0mmWjM6LUzdMve8GEypnwQ11iZrO1QZ8EWjrJdJEVXGD1SSu9aFW/ddJeXZAuf/3M2eZtKirXg==", "signatures": [{"sig": "MEUCIQC2wBlWfxkYh+VhovBL7QiIW9phSGCp8QI1AiDmqlLNjgIgM+wE3uUOxdkEYJcDrIQadSSU+VMPdcr+17KxX0C1jzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "7dddf2764760d0d13e2361901bd960c922543c4d", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.5.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-14.3.2-alpha.83c25417.tgz_1472669435085_0.2648359779268503", "host": "packages-12-west.internal.npmjs.com"}}, "15.0.0": {"name": "jest-changed-files", "version": "15.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@15.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3ac99d97dc4ac045ad4adae8d967cc1317382571", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-15.0.0.tgz", "integrity": "sha512-YTU8Hr8BSVkAs5WeAwv1FVCugBP509OabuDCvU3yU80Ar0KyLeK5xXHb9zjfIX46M4bTWtc1zLrwPAM+LZ1oPA==", "signatures": [{"sig": "MEYCIQDNwj/mEdlY9+v7quHj4sFgWI3OjcgUceRxOYIDLb1C7AIhAOO60EBTvzzZHI5HXdDfYt4VGiTZH+8bM5UzfmBJSPSX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "3ac99d97dc4ac045ad4adae8d967cc1317382571", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.5.0", "devDependencies": {"rimraf": "^2.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-15.0.0.tgz_1472669727948_0.2303699313197285", "host": "packages-12-west.internal.npmjs.com"}}, "15.2.0-alpha.c681f819": {"name": "jest-changed-files", "version": "15.2.0-alpha.c681f819", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@15.2.0-alpha.c681f819", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ddbc71e754d1744e57b3b44fd4b5edf0e9990ca9", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-15.2.0-alpha.c681f819.tgz", "integrity": "sha512-ja8yl2FzbiC93bE699DmHD9DI+bHJ4achh3K3Qza6p1sxGLjbPcE1D7XY2EJH3afmkFoKKYW+7QAjEBm73EAWw==", "signatures": [{"sig": "MEUCIQDQ74VxVcD6w8aWnqoFhnEmGKYbeZ/n+7IKkujj20laeAIgI53CISxk+IxQ8AMcDJ8oe8TH14cHuKnQidnPVEc1cHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "ddbc71e754d1744e57b3b44fd4b5edf0e9990ca9", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.7.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-15.2.0-alpha.c681f819.tgz_1475139736455_0.883036520332098", "host": "packages-16-east.internal.npmjs.com"}}, "16.0.0": {"name": "jest-changed-files", "version": "16.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@16.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7931deff4424182b8173d80e06800d7363b19c45", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-16.0.0.tgz", "integrity": "sha512-AIvr3J3Oq3Mu/uQc1RlfsStQFbmCD6lCKrrpktxg0mftdeKrtN6pUz3mBW3/gS6bCqK00BMi+87DZToqv/c8TQ==", "signatures": [{"sig": "MEUCIQDuPKAbcgG0UQnOH247/EYGaoNHs3vkoblAjxamT/K5EQIgSEzMf7sQRolENqw2D0qY7oZBpj9EhpkhySK+XB+AOUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "7931deff4424182b8173d80e06800d7363b19c45", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.7.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-16.0.0.tgz_1475483909647_0.10657784412615001", "host": "packages-12-west.internal.npmjs.com"}}, "16.1.0-alpha.691b0e22": {"name": "jest-changed-files", "version": "16.1.0-alpha.691b0e22", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@16.1.0-alpha.691b0e22", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6a32d7471809a65ba451942ff9f6a70b0aa68209", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-16.1.0-alpha.691b0e22.tgz", "integrity": "sha512-UFTfkWW8oxM50mXSwBRwbej/vec7UEo+aLG9SwLSZTkFZx9rzNooXXIJeZNFwJiko0Zz5rCkZKLJBEcKSrc2vg==", "signatures": [{"sig": "MEUCIQCCDgwRo7szt4OcBYA1JZdQcLjtA6pTxr4NiYHJNGIOiQIgeYdPMgyzdBeFwiRwBLZYIJqyBa9NWO4WdUC/b3t2vyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "6a32d7471809a65ba451942ff9f6a70b0aa68209", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "6.9.1", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-16.1.0-alpha.691b0e22.tgz_1477639646872_0.027865067357197404", "host": "packages-12-west.internal.npmjs.com"}}, "17.0.2": {"name": "jest-changed-files", "version": "17.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@17.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f5657758736996f590a51b87e5c9369d904ba7b7", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-17.0.2.tgz", "integrity": "sha512-H/A32cl0mczMQ2gMuZbShcdio353yABnUxSIZ+I8uoJgeW3P94uNh0fVyNYdOPZ6Q0IWegJFkER72fNGsTq1Kg==", "signatures": [{"sig": "MEQCIBpIO3K20IKYC68A8FNF/r8F28VnCl6I6IpnLX0VMZB3AiAucKZ+OFSJERi5kKdM3xz/RhXhKOE99n60rKOTLHmUJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "f5657758736996f590a51b87e5c9369d904ba7b7", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.1.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-17.0.2.tgz_1479170359511_0.7218810119666159", "host": "packages-18-east.internal.npmjs.com"}}, "18.5.0-alpha.7da3df39": {"name": "jest-changed-files", "version": "18.5.0-alpha.7da3df39", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@18.5.0-alpha.7da3df39", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1f7ab42f20a29ff6d4135d540da981667c596e00", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-18.5.0-alpha.7da3df39.tgz", "integrity": "sha512-UL04TQmtlI8JOHI8+ocCmuGE4PiOZu5m3tD8iDMiRMffd037wX+/glYKA4DbFyCASDP0nYWv27MJtEXzUHCHYw==", "signatures": [{"sig": "MEUCIBhORwrg4nQLE4jezmelGzyKpH6ke3o55UV53bnu61C5AiEA5S9VX/ZxA5g8ftIg/N2gM4lm0Cx3aX4E1vrq1vnBEXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "1f7ab42f20a29ff6d4135d540da981667c596e00", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.5.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-18.5.0-alpha.7da3df39.tgz_1487350651543_0.1770606036297977", "host": "packages-12-west.internal.npmjs.com"}}, "19.0.0": {"name": "jest-changed-files", "version": "19.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@19.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8c1a43a4ffccbcb8ae12e819104585adf2ed93a6", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-19.0.0.tgz", "integrity": "sha512-gdziE2ir7Dsa2ovIRM3a3pkiQg8MyvrXN5ZadhFm6wHVbvzHg/xVtwMUClsi3rNnnOi0fj8w6l5Ws+e0zHzaTA==", "signatures": [{"sig": "MEQCIExoBWwojV7YGHbSxqV7FcQ+9PghDKF8tGoI7uNoIrmKAiA8F6JbrKkUFCNZGmSRTuC8ZjmxfYTuFpQuUdGbvJdT7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "8c1a43a4ffccbcb8ae12e819104585adf2ed93a6", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.5.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-19.0.0.tgz_1487669412608_0.41831887303851545", "host": "packages-12-west.internal.npmjs.com"}}, "19.0.2": {"name": "jest-changed-files", "version": "19.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@19.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "16c54c84c3270be408e06d2e8af3f3e37a885824", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-19.0.2.tgz", "integrity": "sha512-21P62ADdXGdSum/+6R/IP+/HaPXZCnutrUHIz59c1oGSDLRJaKe8DKZKQcGynr9qWsXc6noQrAGzJbGsouW60Q==", "signatures": [{"sig": "MEQCIEKjEsrU/vBcjwfr8jKhx7BRNvIy7Ff1spn3vfsifLk8AiB0P5zBGVMBtWqFH3ieklVlRH3JFEI3ROsMMnjlGtiAMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "16c54c84c3270be408e06d2e8af3f3e37a885824", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.5.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-19.0.2.tgz_1487849783515_0.767736959271133", "host": "packages-12-west.internal.npmjs.com"}}, "19.1.0-alpha.eed82034": {"name": "jest-changed-files", "version": "19.1.0-alpha.eed82034", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@19.1.0-alpha.eed82034", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "79351ae50693406e2207018db8e8665f11e6f2da", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-19.1.0-alpha.eed82034.tgz", "integrity": "sha512-yzwNiZFV+a/YUYGErq/EbcAE7fcodZXCNxpap941SyiTCL3OQVBrVe99ogX4rV09m/APo6+YMFEPKFViyovv8w==", "signatures": [{"sig": "MEQCIHIJX8zbl5BwQHSys0HEDtCBXSpRM4I/+Sg4gcqYL+yVAiBSdAXtFNTHVmTyzM8Bu46NHBtc9uaKaw20/RkTFoDDsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "79351ae50693406e2207018db8e8665f11e6f2da", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.7.2", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-19.1.0-alpha.eed82034.tgz_1489711276529_0.6391422597225755", "host": "packages-12-west.internal.npmjs.com"}}, "19.2.0-alpha.993e64af": {"name": "jest-changed-files", "version": "19.2.0-alpha.993e64af", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@19.2.0-alpha.993e64af", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "401077a2151be5bbe9a882e8f862e688c4afbdf8", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-19.2.0-alpha.993e64af.tgz", "integrity": "sha512-FdWkKv+Y7NJb62uOrBbDUb2QOpiEvnDjgkqhnxBr1+jD4zcSzTigq+bsCAARRwvRGAkyfZAuC0mg3O1TTGutGg==", "signatures": [{"sig": "MEUCIQCzCvjse9he5snFKkbxJ8z/2m5uR0Q1j55T+EIDtTFugwIgRqIRe5CaY6YRMnA0+YeDKfAvfdKeZUvu6a+x7GRtIQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "401077a2151be5bbe9a882e8f862e688c4afbdf8", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-19.2.0-alpha.993e64af.tgz_1493912252561_0.9187262328341603", "host": "packages-18-east.internal.npmjs.com"}}, "19.3.0-alpha.85402254": {"name": "jest-changed-files", "version": "19.3.0-alpha.85402254", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@19.3.0-alpha.85402254", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1eb49c4117b3a69108f7eb5088c6e078728aebec", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-19.3.0-alpha.85402254.tgz", "integrity": "sha512-GLoONP2xn7G6MI2go12rcL1r8Ks0w4h3sAFEAK94KTBnZUvbR6CJZaZIx1NYfRu83dN3GukC03wvXCBoPh9t3A==", "signatures": [{"sig": "MEYCIQC4xoHXx1Odqyd0B+0JnGzxskwWiqWhrQwCMv8Tb+TwogIhAMpE7LaLIKgu0umaYclMmL/3WNjsUhNi/UMIpyevUqsC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "1eb49c4117b3a69108f7eb5088c6e078728aebec", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-19.3.0-alpha.85402254.tgz_1493984896594_0.6732959751971066", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.0": {"name": "jest-changed-files", "version": "20.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2ad82870a815b40ce3f4bf4555581d387b21022c", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.0.0.tgz", "integrity": "sha512-pyIc530tBPPG0D3U3rEVlFEi68Oqx5TQm8iVQ+6QlCRFNOWWR7G/VG1YOULA420YXEGeJ6hW/zADn1uR8og+ug==", "signatures": [{"sig": "MEUCICGsLt/9YpoFtaA1fyqJ49027tYoRh0lwcey5gkMX169AiEA2CD4Q68k28KpwRgR7YqTkBSeg4dDJNOFvDM1C7EPspI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "2ad82870a815b40ce3f4bf4555581d387b21022c", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.0.0.tgz_1494073947308_0.9374179656151682", "host": "packages-12-west.internal.npmjs.com"}}, "20.0.1": {"name": "jest-changed-files", "version": "20.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ba9bd42c3fddb1b7c4ae40065199b44a2335e152", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.0.1.tgz", "integrity": "sha512-/pmZSbOnbcGyofMvgy4FlmrXn8yiF5mZ/B1yfddnXxl3j5SE7C0TM/ooex3u/Iqj/42rBhHNDoK75JihWLp/YQ==", "signatures": [{"sig": "MEUCIQDqVXptp5MZe6OFNrU0sltQEYB8ndvfXxCXB/RPG8wyJAIgJOKWI6OUmEcbGoMhtb5Zn8rVeHDXqSnvJNfHAGJak3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "ba9bd42c3fddb1b7c4ae40065199b44a2335e152", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.0.1.tgz_1494499800680_0.5100530188065022", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.2": {"name": "jest-changed-files", "version": "20.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4a8b909b060aa373b0cf32d6d856cf451854b405", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.0.2.tgz", "integrity": "sha512-MQlBReT/50b+9BSnSI4BOh/iO6UHP9zV2px3MqihqgFhznMhoYJT3PKqmOavPKD9wAaQDlE4Ud5gFOS+hqCDAg==", "signatures": [{"sig": "MEQCIHR3TplMsomWYPXZA+RemLN5JLOgOPeamNrreczcQlQvAiBkZ6WNB38My+Aj5gIvZhaJ5/2FEZRPRY3q25BaIHIEig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "4a8b909b060aa373b0cf32d6d856cf451854b405", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.0.2.tgz_1495018214200_0.5179453168530017", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.3": {"name": "jest-changed-files", "version": "20.0.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.0.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9394d5cc65c438406149bef1bf4d52b68e03e3f8", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.0.3.tgz", "integrity": "sha512-3o76mBdqPXN5zrak+U9ldALWgYlQQgKh8vQL3nsQO/seybjPqsp2Qp0pGGvVeMOaiwCbOZCrOvb3D8QhG/9Ccw==", "signatures": [{"sig": "MEUCIDcbvoM3UiQ3aHwh1zrTit21y8FPXEfY/aVAoc9pyDiQAiEA1Dp2vu6gIFiMqI8DlQJjV08gxevtmY16f/fdPBy7N+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "9394d5cc65c438406149bef1bf4d52b68e03e3f8", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.0.3.tgz_1495018624157_0.6621430695522577", "host": "packages-18-east.internal.npmjs.com"}}, "20.1.0-alpha.1": {"name": "jest-changed-files", "version": "20.1.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "290c7146fefe75fd12c464b0103f9f15959cec4c", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-alpha.1.tgz", "integrity": "sha512-PPvi1AX2m2eLtnxhB/BwfMukD+joEVnweGElbxjp6ZsWDxoTQHmTkJlBZZX4b5wjrkx1fFcWQ1rnBeig1x2Q2Q==", "signatures": [{"sig": "MEUCICGiM1sm/zGLpsGkHcxCUwFNvdc65AEMTfMsH91Bn0snAiEA5/QqJlRtdaRZN2Db52ajKF0KpxpyvQeq8I8GNNsx1pI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-alpha.1.tgz_1498644974034_0.41803748183883727", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.2": {"name": "jest-changed-files", "version": "20.1.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1ddbe540b995368154ff0068c5c9e299f2d8c883", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-alpha.2.tgz", "integrity": "sha512-DPcPT3YOuecmmFSoYFwAqdELcQ0kdrWltNYTjjHwzZvMQ4M/IdhaeuIkFYTOqYtAhjcSKrv0ij//34lvHM7MSQ==", "signatures": [{"sig": "MEQCIBZ2YYUGIm3jFAHJBWKITS4oHIhixYNpW0ZP56cz0BJJAiBYNWV2MDxTdIVUNfoxWsQUF8fgABD+ZURdggHKG8BrWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-alpha.2.tgz_1498754200625_0.8585629286244512", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.3": {"name": "jest-changed-files", "version": "20.1.0-alpha.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "35faedee3865d05e271879c9ec83cc92b468937a", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-alpha.3.tgz", "integrity": "sha512-PQonA6EKwtJuj0ocVjt2HTNCGQLFLFwJxjM/9eBGSbUfuX25O6itfIdCKBpT97ikOcrlppWTIFuGPyvXKp7Xhw==", "signatures": [{"sig": "MEUCIEAYRfcfchWRVgWm1pH0wwXu02O8EOGMCHICOTz8y4vSAiEAhHOpdK1eG0Qme6GeLaBbf9Myl2e5Yt/3NpVvGJh/pAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-alpha.3.tgz_1498832446590_0.1877789725549519", "host": "s3://npm-registry-packages"}}, "20.1.0-beta.1": {"name": "jest-changed-files", "version": "20.1.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "13a4c75dab27c2ecd8328a351e138f0dc468b121", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-beta.1.tgz", "integrity": "sha512-MohM6WAKlJlnGHKWfO95jlgPbPHpcxC4+WplglMnH2jn4z1AQlbtIrMsNSDCk75R+zOFHZO7p5wUFnN+YeC7MA==", "signatures": [{"sig": "MEUCIQCx/5U9F7IAoBDwt6pDc6j3vUPhLvp37cFzyjgm7iVdxwIgeGb+WZrkjU22W+kZI368pFeoFguXRkWLVbtDWDLsh1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-beta.1.tgz_1499942013882_0.7585058575496078", "host": "s3://npm-registry-packages"}}, "20.1.0-chi.1": {"name": "jest-changed-files", "version": "20.1.0-chi.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-chi.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ee546e3c7c2701f4e9c3d25f080322a7017df510", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-chi.1.tgz", "integrity": "sha512-mvuUBUR5yi58D7msda5q2YJcAkUT6EcpUziUzc6blY9au6XfEyUMAph2lCXaviiA9pG5jFc4W/F5dXlkQDqBpQ==", "signatures": [{"sig": "MEYCIQCVN8k3LsA7ISex9hZO+zUVgWrbP7/Yd0bzXe0hwnhddwIhAKnlSy5/+VdHLdjhvZ3/G6Z8yr5zWt4fPEgZb+PjE27D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-chi.1.tgz_1500027896151_0.04769504303112626", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.1": {"name": "jest-changed-files", "version": "20.1.0-delta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-delta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "912f8eff09c79b28fc7b66513f0ad505f1b378d5", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-delta.1.tgz", "integrity": "sha512-4YfM8Id6m87/pAXAiOzJYwSn1wCCgQ5sddaku8FiVZ9txzGCb9aeLwL+yVMmP4SQR/cP7Z3bcXXRFQ0AtMdmRg==", "signatures": [{"sig": "MEYCIQCu27dEvkW5qM+TKHWdzrsaDbIEHsAq8OU8iW4dfvCXCwIhAPNwN+RF2h4l53mRBeMZaMXBoOd4lbcH7S7GUc+NjHHc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-delta.1.tgz_1500367606760_0.40708530228585005", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.2": {"name": "jest-changed-files", "version": "20.1.0-delta.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-delta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "61cbd1c853b695cbe628d0ffd4f869501a9ce85a", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-delta.2.tgz", "integrity": "sha512-xjUCY4UHFmMP6Fwc8CWTr60jJ+9CUHb60GgUNS7dFinVGXUmRElJL2ZP9LGQHdriVkUpzJVxTqMKaDt8k7gI+g==", "signatures": [{"sig": "MEYCIQCMY9SizVFFWy0BSoFSJVU4/10ZxDKbE4g8vuA7VuTKuAIhAKLN2Z4xogTJH/Z4HC7S0DJ/14vNYT3xa6wAEMqSjVv8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-delta.2.tgz_1500468996450_0.38860831572674215", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.3": {"name": "jest-changed-files", "version": "20.1.0-delta.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-delta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "81aa4364325d44ad4ece0bd238b6b5529037b4ff", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-delta.3.tgz", "integrity": "sha512-04TdLM6utWIjtkH2Bl9vr9SpuMa3UDFUqLsIrTiTRgLjRbp82BXmT5AKcx1wROh7neCuyjW3e8PdIuqebIlxbw==", "signatures": [{"sig": "MEYCIQDJiAy8D3AyJ/cp0B3kqU4Da5du3LVnhQksD+gWCz6cZgIhAPNrDqwDMmkAyOUbHx5+HYxG2rlPGCA+ccn8Wl5jkxt2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-delta.3.tgz_1501020740811_0.5640503892209381", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.4": {"name": "jest-changed-files", "version": "20.1.0-delta.4", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-delta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "609381129e360c8d2f53cb031abbf839690582d5", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-delta.4.tgz", "integrity": "sha512-veG3ByVbsNvmhGLDoOXLR2ECYyAe7kVNg8zltSTn3wHO/hRPKhUaaKMDxPWn6+ru5SJD5xiCime1S41FA3fUeQ==", "signatures": [{"sig": "MEUCIQCVXhrZH6g5d5IWvJatLYFp+UUsuj+6edB7BTjjFODyywIgDIV9kfvtzxK4C3BtTlcXf3K/xYuFNhV6jLmznuQPLF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-delta.4.tgz_1501175944654_0.39448700612410903", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.5": {"name": "jest-changed-files", "version": "20.1.0-delta.5", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-delta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "25fa3854bb9e18ca540511e549ff91006758b86c", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-delta.5.tgz", "integrity": "sha512-Dm37FI2+B/UBfQB4QkFD55kFY72jU0kJR1Ou/AXzOMdo9XYQxcokyxTCPiTuDxRSGxgC0PTsD+4HHhXgDonkBw==", "signatures": [{"sig": "MEUCIQC/ZbYCfq3ReXRl9LbG8tnu3LsFWmf+LG38KgNmWULtmgIgMMoPMAnMknIprW/BtMF1svJExXkk/ncEqsE+bWRYPq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-delta.5.tgz_1501605213259_0.27852653595618904", "host": "s3://npm-registry-packages"}}, "20.1.0-echo.1": {"name": "jest-changed-files", "version": "20.1.0-echo.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@20.1.0-echo.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6432db6d4576df56660e5773b5b9c09642a36070", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-20.1.0-echo.1.tgz", "integrity": "sha512-RN/Tm+VqvaAN8740fXibiCLNLP6Q9OMyFKtPijm1TOhZmlfI7l16VCAwPEwoxR0+cuMfxyD6t4YAmvOQPTpR0g==", "signatures": [{"sig": "MEUCIQC5x9wk7ssy2HTXPT3KGzG3D6N2l6Ii1v2zms7IuHvOlgIgdLKvC0bmBk2KqVSTgkI+iZcmBSquYu0fFEaKEqJCZ7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-20.1.0-echo.1.tgz_1502210985344_0.30726873222738504", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.1": {"name": "jest-changed-files", "version": "21.0.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@21.0.0-alpha.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6fec406f21c264170a4545a1d525222bd905600a", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.0.0-alpha.1.tgz", "integrity": "sha512-pRzY81jbQ18eocraxNEah5+Oy+szo9Kidlempi0NkSJDeBJiF3ZiRR0OxqRvbO2u5i+dDcqwWye03HJHnNjc0A==", "signatures": [{"sig": "MEYCIQDzhZZ/Ycpo8Hq3KFQNN2bVR0VoHehvxBPNh1bK+tAIvQIhAIZrRNX0ckFweIt2mnerFQsZyk6Ong7UDuTkE8LVw/9m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.0.0-alpha.1.tgz_1502446437269_0.8786501209251583", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.2": {"name": "jest-changed-files", "version": "21.0.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@21.0.0-alpha.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8d7ea1b8a4934c8dfb476b286a7de50f3eb8aac3", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.0.0-alpha.2.tgz", "integrity": "sha512-KS5Cmj7IUUiTOCy//jfqibi8HVY3cDE3Cp9+p7mtqlU2VPp35AhI4XFYeQ23OS1+D5Or/t6WlAs455LqxhCk2A==", "signatures": [{"sig": "MEUCIQCBHKe8ubRIc4+ExNqiuyiATYaQNXeN8Vs4Hyfz8USuvQIgKDybm/FIAq2GLxBcoJ4QQq5U3jO+OCYJS1kw2adGawk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.0.0-alpha.2.tgz_1503353206231_0.6656380537897348", "host": "s3://npm-registry-packages"}}, "21.0.0-beta.1": {"name": "jest-changed-files", "version": "21.0.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@21.0.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e4832c5946c292a1e7d7b66150c9d9c06c5d20d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.0.0-beta.1.tgz", "integrity": "sha512-HHk9W5NmCQVzqwSMvIffWSHSAGuoijah/S3VOjerCErXHd6LxJzebiTG+0eOQysaUg3ynXiL84dxHOdAhN0PjA==", "signatures": [{"sig": "MEUCIC+8BDDmWM8CEnxY/2VTRP5T1oEbV1YbTbx1/u+zaKRaAiEA916GimWjVtc0BFnia8RUON72bgLG7nn8DfFGUGfDPfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.0.0-beta.1.tgz_1503609998175_0.542502687079832", "host": "s3://npm-registry-packages"}}, "21.0.0": {"name": "jest-changed-files", "version": "21.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@21.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fa7cfc353187e2fb852dd5830e8d09068dde78d1", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.0.0.tgz", "integrity": "sha512-6xmPTJfRK6rmOTRFZ9Lc9Eyk/ov4H24Ts77bV/PkbSl4T29/e8w3QYxMhrcOLu4Ie561TdCRgq+NISWNojCYdQ==", "signatures": [{"sig": "MEQCIGSQTNgCaKKefizt7VRmhBnECWy02wc3Xk57wqaJqFHqAiB2Ssns12DFbZgO1XzkccrXJ2k0F9SRVsgrf8VzZ8krRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.0.0.tgz_1504537301498_0.7403797530569136", "host": "s3://npm-registry-packages"}}, "21.0.2": {"name": "jest-changed-files", "version": "21.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@21.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0a74f35cf2d3b7c8ef9ab4fac0a75409f81ec1b0", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.0.2.tgz", "integrity": "sha512-dtOoRr+WjmDCbbFkPg0jj1kSgfq5znW1/XKOqwS2Tpp507GMk6GtSz7riTDlEiEauApFbkL+iE23CLjMNGVjFw==", "signatures": [{"sig": "MEUCIAKRRqa9+ihS7AmFiIGTW1vsuusCp5TS26KWohbtN2kmAiEAihAxFfpEJALSJL+x60DjoY+abpLkFS9ayyoeqz+BGKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.0.2.tgz_1504880341833_0.5900698110926896", "host": "s3://npm-registry-packages"}}, "21.1.0": {"name": "jest-changed-files", "version": "21.1.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-changed-files@21.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e70f6b33b75d5987f4eae07e35bea5525635f92a", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.1.0.tgz", "integrity": "sha512-OnjMoORBVG9Jko6Bc3UGJPx9G1PNsETiqpQXZ6wPU3fk1gtxhKYE4Mgdk28ER/CWeg7bzGUcaragLf1lf1tzbQ==", "signatures": [{"sig": "MEUCIQCa57FUqAZynRZjWyqfKQpHxmdJoBTBB2kmgbURVLCcRQIgTdSn/la9Mmfd7FyeH+FfZH5ZphG0RQTofoD2jqXHeB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.1.0.tgz_1505353804890_0.10271805804222822", "host": "s3://npm-registry-packages"}}, "21.2.0": {"name": "jest-changed-files", "version": "21.2.0", "license": "MIT", "_id": "jest-changed-files@21.2.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5dbeecad42f5d88b482334902ce1cba6d9798d29", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.2.0.tgz", "integrity": "sha512-+lCNP1IZLwN1NOIvBcV5zEL6GENK6TXrDj4UxWIeLvIsIDa+gf6J7hkqsW2qVVt/wvH65rVvcPwqXdps5eclTQ==", "signatures": [{"sig": "MEUCIQD4svHKUjpCl/TYuVxiox3U2aY4UWAI/kN81m+xFRyJfQIgFuAb5RI9aR8/kM+zvAK6d7DjRV6wMVshcr9a780qbo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.2.0.tgz_1506457322688_0.4425124190747738", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.1e3ee68e": {"name": "jest-changed-files", "version": "21.3.0-alpha.1e3ee68e", "license": "MIT", "_id": "jest-changed-files@21.3.0-alpha.1e3ee68e", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d73df218bfd3ae5b5be338edba33a587b375283f", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-alpha.1e3ee68e.tgz", "integrity": "sha512-PlYlAz5bVoyn+9W08Nrl/roiouPNeZA2VudXvV3+kFT1BEsu+2yewqGSS6v+1qL7220/rsT+TS5O40t8z0xvkw==", "signatures": [{"sig": "MEUCICboVDc5K9fFwXTCC/720NJLJhpi5Ua6FeiWn+SJ8V/RAiEAxO9wzdwDsmcNWhEr4VDaUi3DIe5Sq/GAM8K1K0mR/3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-alpha.1e3ee68e.tgz_1506608425765_0.9122302837204188", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.eff7a1cf": {"name": "jest-changed-files", "version": "21.3.0-alpha.eff7a1cf", "license": "MIT", "_id": "jest-changed-files@21.3.0-alpha.eff7a1cf", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8a806848d4404728582c3549c9fa8e86ccad2149", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-alpha.eff7a1cf.tgz", "integrity": "sha512-JoT8YM04wXYzdt2Ilm7p3b0dfw7e9Qwv02JlHjrrwqAvqgAOEvVXamLi/cQu1WJWxiTPwW6ivrRLjMY/gbs2oQ==", "signatures": [{"sig": "MEUCIQC4oMaHBGzlfcKvaOOT+cvGnba9K18f0h+02niSvt8S0gIgRiqtCN9nCgpjc0c8nQB/D5yBej5QcdlrXPhUdzpY938=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-alpha.eff7a1cf.tgz_1506876399487_0.6267092714551836", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.9": {"name": "jest-changed-files", "version": "21.3.0-beta.9", "license": "MIT", "_id": "jest-changed-files@21.3.0-beta.9", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b5fd61321571236496c8d190aee469c2a96f0760", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-beta.9.tgz", "integrity": "sha512-Uf3ogn+BM2q4G59OwAiydSyaCaz/14VckI0fTisiDzw41t/CEwuRnAtnlUCbcehAY1uRKaodnjjDA/ThtOtWoQ==", "signatures": [{"sig": "MEQCIA3ImEgP+p/UGiKiw7B2GeIkuM4WXBL8oT8zGBhjQ6iLAiBpnXsMMNaTDDPjXXwQKmFdAevEbS5anIFnfSmXhcjcAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-beta.9.tgz_1511356640757_0.11083427141420543", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.10": {"name": "jest-changed-files", "version": "21.3.0-beta.10", "license": "MIT", "_id": "jest-changed-files@21.3.0-beta.10", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c0df7993f0550fb639a2466d0f72c7980d73856d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-beta.10.tgz", "integrity": "sha512-sA4EfQhsPFPOhackmVCqC/j6haTrwGNL91d0up1luzGl2JPmAohj9IKoltjIdzSKZLV1ODJ6DYZkfP2ExAkYQg==", "signatures": [{"sig": "MEUCIQCxdWyhrphFmunoCCpTK/heaWTesl6Ql55CUMKZXdhN3gIgSmZplK0BVT45yUeeIv+CTRNlWKcvFvqOusIQ2zpHdEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-beta.10.tgz_1511613557712_0.011098002549260855", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.11": {"name": "jest-changed-files", "version": "21.3.0-beta.11", "license": "MIT", "_id": "jest-changed-files@21.3.0-beta.11", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bb81f6434246d61c8f0dbae3f54575cd915257a1", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-beta.11.tgz", "integrity": "sha512-hPzfuzKduezlrpAkPb1kxsbRC8c1GSnK7iYmoR7jJm+84q7kL0v+qHqjdwtcC5CQ+oRfhlWmZfjUUN8hHPVNDw==", "signatures": [{"sig": "MEUCIQDuNf+chMnyqQMwHr8Kn9IkkP6v/myg1CSmK5Fhsy1uCgIgA69jp4Y2WVR1wUAAEZgkS6LlzQvON4+DaDmbL23bVRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-beta.11.tgz_1511965870558_0.6895570897031575", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.12": {"name": "jest-changed-files", "version": "21.3.0-beta.12", "license": "MIT", "_id": "jest-changed-files@21.3.0-beta.12", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0cdfcb29c70f734936da29341b87419555c7bf0d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-beta.12.tgz", "integrity": "sha512-SaVLBKDlsplkTLfFP9OOrpQcyNfc1mEpD4KxavbvVrj4EhRoPA0xwVXD4k1OV0TZZk7uygHLG7Q9myKPboU6eA==", "signatures": [{"sig": "MEUCIHn1qReqkPs6BCtx/WPxaeGU4fhMugn4dSUnTMt8Cu8jAiEAu5UNb2TI8XYQYUh+Hi+LpKcSJZ/ly5unh4tfc8/Uvj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-beta.12.tgz_1512499708439_0.3783143099863082", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.13": {"name": "jest-changed-files", "version": "21.3.0-beta.13", "license": "MIT", "_id": "jest-changed-files@21.3.0-beta.13", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2c2dc8f1ff23949c4882de48b4266f969a8f6d2", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-beta.13.tgz", "integrity": "sha512-0THUPXWdSfJkA7hskvOcoxorEapUdKs+IvK/7b9N5qfNTXUWz9YBaI+Yltj23my4Ua2w8mSsv7BBabF8oqgNGQ==", "signatures": [{"sig": "MEUCIEebpcv0vDvmaNc+6nNdlQg1TfjR/tvOTqPx4b28mgi1AiEAvCFP7Ze3eFxpf/e+m8NgNNWR8fXpKq3ByWstBnni/pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-beta.13.tgz_1512571020144_0.8752819113433361", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.14": {"name": "jest-changed-files", "version": "21.3.0-beta.14", "license": "MIT", "_id": "jest-changed-files@21.3.0-beta.14", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8e39af02162e5cbc94092f87f60024ad5a8f31dd", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-beta.14.tgz", "integrity": "sha512-Tskj40SZXdsFHshXWIgGY64pqVmyrKbdOmXem3pQd+PJGKHFCAKlYXCMqj0NK5Jk7+HDmiTe4wEunwSxjjn7hQ==", "signatures": [{"sig": "MEQCIEd+2JwcLh1s/RxWKHdsraLTh6775zalH5WfHFRp/ViTAiAIU87Osfa3aDj1JAjMcv5GildhazOpBIXJJ53mJETgPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-beta.14.tgz_1513075943208_0.5915891306940466", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.15": {"name": "jest-changed-files", "version": "21.3.0-beta.15", "license": "MIT", "_id": "jest-changed-files@21.3.0-beta.15", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9684c6b498faab7eec658f4a6f7b191224a8ac57", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-21.3.0-beta.15.tgz", "integrity": "sha512-5EE/0LEGmeIwxrxSilDuuOlSkEFJOkYabZ9l33vyVhygMx1fatq98znjAp+QaS9dLg2aOJoUrsYYK7OUJemAAQ==", "signatures": [{"sig": "MEUCIDxHLz4LkJXseawAV72Seh2V80f1hXMdXEwpZttdezPmAiEA/QkYZw4ZRPoPJXf95GEJQgmizsJOZ2WXm+ScQ7w6zuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-21.3.0-beta.15.tgz_1513344448865_0.533620823174715", "host": "s3://npm-registry-packages"}}, "22.0.0": {"name": "jest-changed-files", "version": "22.0.0", "license": "MIT", "_id": "jest-changed-files@22.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "14c5e76764040009af149c99384017867675920a", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.0.0.tgz", "integrity": "sha512-9Um5GIjAzQYyIw9M4Mk62Asw/w3LPj2sjxG9KE/tYOlA6/i+3GrrBrRGk0QkqZU3VDo//KNXXfZsAw0RUvR3xw==", "signatures": [{"sig": "MEUCIDrXQS62Uyff13ChW6oswJkJHtISJ3ltfMO+aHZTXge6AiEAwbjFb6MXJzi8TSir7jznuuau29rr0dSUa3DaprW9iDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-22.0.0.tgz_1513594997453_0.3497475644107908", "host": "s3://npm-registry-packages"}}, "22.0.1": {"name": "jest-changed-files", "version": "22.0.1", "license": "MIT", "_id": "jest-changed-files@22.0.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "195ff30255b67ec8809698dd7d2dc65f8cdfe6a1", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.0.1.tgz", "integrity": "sha512-I1WfAAi+wBgjlMxqRTLT1kOHgyR3egQi4BmG787KleqpoRGyUHkNfDBmzXwxypfxlqing0nqFUCWP5gP+jjVdg==", "signatures": [{"sig": "MEYCIQCLtlLD5FWL10yvDD0JkuJA2vEYIzl7Qm2JgdCTLonXLwIhAIwGKW76Y0mGbpONoKbgX4Q2L5rpBkVA0xsYCcZewzmn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-22.0.1.tgz_1513628956041_0.7250015130266547", "host": "s3://npm-registry-packages"}}, "22.0.2": {"name": "jest-changed-files", "version": "22.0.2", "license": "MIT", "_id": "jest-changed-files@22.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7286bfefa1dc46d29d9a686fefabb71883d365b7", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.0.2.tgz", "integrity": "sha512-DWHl2Kxur56b21XxRZfxMw5dR77sOGQDJV1kl1yM9FC9/UxPCSvlUJg1bIRxWdOii6xsAi9mEDMJyvG0fJw7oA==", "signatures": [{"sig": "MEQCIFuCJybR681BabteWHRrQRZcu5R/fAjs95NsEO7Y1rfIAiBPAEETjk7b1lb72bkb7QEcRvbANLq9K8Ini3KG/1974A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-22.0.2.tgz_1513691576392_0.4390313164331019", "host": "s3://npm-registry-packages"}}, "22.0.3": {"name": "jest-changed-files", "version": "22.0.3", "license": "MIT", "_id": "jest-changed-files@22.0.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3771315acfa24a0ed7e6c545de620db6f1b2d164", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.0.3.tgz", "integrity": "sha512-CG7eNJNO9x1O/3J4Uhe2QXra1MnC9+KS1f2NeOg+7iQ+8dDCgxCtpusmKfu44TnEyKwkIDhDr6htPfPaI+Fwbw==", "signatures": [{"sig": "MEUCIAxb8H3dldJFxepb4S1+v6hdqf92ZXQGTynx6v4FN3XhAiEAz52ftO3P0+1PdfFrtotcMLz0tylPUj2YQOhJVGXCLog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-22.0.3.tgz_1513695524215_0.12458854797296226", "host": "s3://npm-registry-packages"}}, "22.0.5": {"name": "jest-changed-files", "version": "22.0.5", "license": "MIT", "_id": "jest-changed-files@22.0.5", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff944a1100172e9095869f4f5432e3fff09ab4ab", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.0.5.tgz", "integrity": "sha512-WL8DktSnt/U+D7y6ddF42kgJKeyRlPDqy8OL04czLumWfnQDolbuowh5oapKTHBIjtAA+MY1OkGv6yFXDQHeEg==", "signatures": [{"sig": "MEUCIQCyAYz3AJQhjFcxnp1mexdE4PL4sJdtZ8kdOf6xt4weMwIgDrAhkAZeeNnKHxtpqDBKmAPTnXUqoyTJLjKt+MjWeIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-22.0.5.tgz_1515510592371_0.45874929102137685", "host": "s3://npm-registry-packages"}}, "22.0.6": {"name": "jest-changed-files", "version": "22.0.6", "license": "MIT", "_id": "jest-changed-files@22.0.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fca38cf4c89920c66aad1707e91d25bbea238875", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.0.6.tgz", "integrity": "sha512-eRnDzJm8gm0UsSV6H0LvIi5wANWDXmzKNglAl6zFRUv1K9FXTsqInE4zFWUzerGZv7LIvAYaVVAzyMrttbpcKQ==", "signatures": [{"sig": "MEUCIGdeyQI88b8j2bhcgSExCFqcXEV3YtedfIBbuaE/zIDzAiEAxAHfnyC4IRZMplB+UGw8A1TSJL9p4vkj6KzfkbbqCo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-22.0.6.tgz_1515663996781_0.07153794914484024", "host": "s3://npm-registry-packages"}}, "22.1.0": {"name": "jest-changed-files", "version": "22.1.0", "license": "MIT", "_id": "jest-changed-files@22.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "586a6164b87255dbd541a8bab880d98f14c99b7d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.1.0.tgz", "integrity": "sha512-NGc5HF3zXaMMph1L3HwPw7zVu0uAad2CZ+5QYQaP9QzB9jBioukulg2vcy7gdJRfWAr3D8EGlu4jfUHOtLNVmQ==", "signatures": [{"sig": "MEQCIBj4yXntgAwLAKIMME5Qxd6XRkKIb7qoGICdIIDx+20TAiBArJ4fb5v/5EzSY8ghKgj7yDA+YGxIWa0GApu+JmfrRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-22.1.0.tgz_1516017420220_0.6116233053617179", "host": "s3://npm-registry-packages"}}, "22.1.4": {"name": "jest-changed-files", "version": "22.1.4", "license": "MIT", "_id": "jest-changed-files@22.1.4", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1f7844bcb739dec07e5899a633c0cb6d5069834e", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.1.4.tgz", "integrity": "sha512-EpqJhwt+N/wEHRT+5KrjagVrunduOfMgAb7fjjHkXHFCPRZoVZwl896S7krx7txf5hrMNUkpECnOnO2wBgzJCw==", "signatures": [{"sig": "MEUCIDTNAwrpMWuoTODnedr8H7YYC/DoOL5hxuNq+w9SB4U5AiEArjdNkjdnEUGc0wdFcQ31OLXmX/5hD1MhhqykDsEL6/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files-22.1.4.tgz_1516372701619_0.17933774995617568", "host": "s3://npm-registry-packages"}}, "22.2.0": {"name": "jest-changed-files", "version": "22.2.0", "license": "MIT", "_id": "jest-changed-files@22.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "517610c4a8ca0925bdc88b0ca53bd678aa8d019e", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.2.0.tgz", "fileCount": 5, "integrity": "sha512-SzqOvoPMrXB0NPvDrSPeKETpoUNCtNDOsFbCzAGWxqWVvNyrIMLpUjVExT3u3LfdVrENlrNGCfh5YoFd8+ZeXg==", "signatures": [{"sig": "MEUCIQDULfqnE/ajd4xhJJcpJK3Jhf5rff9424SDfy9AnuqxvgIgNDH/3RXs7byTyIAG3jii9yCcIfeiPli8IbgaRPeODFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26713}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_22.2.0_1517999151843_0.29562336097286357", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "jest-changed-files", "version": "22.4.3", "license": "MIT", "_id": "jest-changed-files@22.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8882181e022c38bd46a2e4d18d44d19d90a90fb2", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-22.4.3.tgz", "fileCount": 5, "integrity": "sha512-83Dh0w1aSkUNFhy5d2dvqWxi/y6weDwVVLU6vmK0cV9VpRxPzhTeGimbsbRDSnEoszhF937M4sDLLeS7Cu/Tmw==", "signatures": [{"sig": "MEUCIA7h2k3V7rUX3yj4yP/0aj2FXiCUAFMf2WocUSL8Bc0ZAiEAp1Mex9eWx/j5+DE0hFyIUCxKyhSkkHtazBmBZE311lU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22344}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_22.4.3_1521648478652_0.313021524176992", "host": "s3://npm-registry-packages"}}, "23.0.1": {"name": "jest-changed-files", "version": "23.0.1", "license": "MIT", "_id": "jest-changed-files@23.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f79572d0720844ea5df84c2a448e862c2254f60c", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-23.0.1.tgz", "fileCount": 7, "integrity": "sha512-cF<PERSON>eutS28e3DyfSu5z4mHsvbSv52pAiMPTYvO4UYA8aVibF5TUJni/7tk+A0QrMFz8D0CVfYzJx0woPhUA8UuQ==", "signatures": [{"sig": "MEUCIQD92OIw8ygymxet5ydQeYr2zoCcRp03/Er80yqtLQyqTgIgTezqj1auu5HTzoSWVuW3qBFqUiKdvzEjXNULYuhlCGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCs8eCRA9TVsSAnZWagAAYxoP/2cI5evtFj0DZusCj7ZD\n+LRCM/8YTcKDW4tcbGMGJ7GRA0PK2Z8HvdfujOSDbeQoBUd/WNDCr0Uiqyrg\nP7JoahiyKDG1oi8Msy541ZcNaWp2T9c9WD/szgZYYdyoW9HGsySsW8yJU3Fo\n8dIysj9LK2yGh7bKY7TpQN8JiPBacjU1SDLK7ZlpMP5/em84eo/amcaJK5/X\nMh6nYKn3u3vLs3KC1Xz4Lf4HPRuQRijPX/8lee+G/dcKsNZfWdeHfS9b5rnv\nXxkH2/zwL3x5Qn5Ye6extQ/z+7hauJ18yeDOBamSowj57TcMxUdiGSlebXCw\nn6TClwPZau6GOHSvliVoDloaDFhiYWt+3ruBQl8Lh46bIC9LpOidtGiXhVQK\nhEdsuXdmBi6dUr0CUmGQ/oskN5IL0g623p5BDJdjgzhCwKDVkrfJKk/MlgX8\np9xipdCPh6UdVjheAMOFFaZaVwBXf4jWsuD+2540o3vSObWIPF9Us1A/3rLa\ncGdUntvGChK/y0rlEc5qCj5J4ynmw+S4bdNuCJlzbXoWJUr+AwWeibaiJxUg\nVo7wkZjsvee8R8sDL1vgXvk9tmlwQ3PoFGQsqkPpTy6Tck/R9tP4+99iogEf\nPAhCVCY9PgM7HYAzmhKDP1Uoo5yoSa3B0+i4s+Md+GhEgOkUfGlyOiqE4yxm\nnXJW\r\n=Lrat\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "dependencies": {"throat": "^4.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_23.0.1_1527435037745_0.9782846502033522", "host": "s3://npm-registry-packages"}}, "23.2.0": {"name": "jest-changed-files", "version": "23.2.0", "license": "MIT", "_id": "jest-changed-files@23.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a145a6e4b66d0129fc7c99cee134dc937a643d9c", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-23.2.0.tgz", "fileCount": 7, "integrity": "sha512-FfV6ixjMrYszFC4X3nHLvsZ3dHh/YRQMzfnCmMKLbJjtzs87kFo6PfX+R98vYLYK+kzlTM5u+FWn25qkCSc19A==", "signatures": [{"sig": "MEUCIQCyoM23nZoKQv3sL/2oAo+o6U2YHxf2dhqbOVOt9yNpNgIgL6Tnyjw8RwBgGGfgB08yuJ+T8VKDSqAPCF7DTkfdo3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14070}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "dependencies": {"throat": "^4.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_23.2.0_1529935509504_0.38632832320067934", "host": "s3://npm-registry-packages"}}, "23.4.0": {"name": "jest-changed-files", "version": "23.4.0", "license": "MIT", "_id": "jest-changed-files@23.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f1b304f98c235af5d9a31ec524262c5e4de3c6ff", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-23.4.0.tgz", "fileCount": 7, "integrity": "sha512-boK/GmKE4n9SItOzpMsHv4WrMR284Z49CJztLsmMTi6CSqBT3qQvBIrRRAb/oWe2/B2YJeiRyeeA/bhdDX08Yw==", "signatures": [{"sig": "MEYCIQDRLcA98J0QrYSgiN8parMUZouYqNTE1Hfh46JP1y64EQIhAKCKKcMcQRkPMAwbo0peSAbnyl8sYKX3GltWenp4fSa0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14093}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "dependencies": {"throat": "^4.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_23.4.0_1531237944624_0.3433602530129498", "host": "s3://npm-registry-packages"}}, "23.4.2": {"name": "jest-changed-files", "version": "23.4.2", "license": "MIT", "_id": "jest-changed-files@23.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1eed688370cd5eebafe4ae93d34bb3b64968fe83", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-23.4.2.tgz", "fileCount": 5, "integrity": "sha512-EyNhTAUWEfwnK0Is/09LxoqNDOn7mU7S3EHskG52djOFS/z+IT0jT3h3Ql61+dklcG7bJJitIWEMB4Sp1piHmA==", "signatures": [{"sig": "MEYCIQDMMv03m7c/L//cwP2AdAvdMRiFQvp5XnLDH/7T4kiitgIhAPiuScP7bvrNYJzepJ5zg7S0aBj2v3iLkoi3EZ/9TuhY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbW58SCRA9TVsSAnZWagAAS54P+gP/+odZOKJrE4LqMgHI\nEW7dOL0uTgIlzFCxVHVgfWT7921a5SWaIDEb+TMa1dDNLjav6LZ2bIH59DOi\n/JIuyzbDq+aPbkW4yc3+Z+8jkDyvhoVgQwJX5pDWjCTPcF5myodN0pEUKoyU\njwoe+BHjIYcM1RKSNhKQjfMQOqronFx2d8nz+8hImDc/FujmofQSVtQkIL92\nKg0oac/rciOkhLe0KYVwh2sBID57l9NDCkpAoK/qvuk0io/oaX0sHqqF5r/I\nmmwOqbFXZCOpl59lAoVAU9Ms9RgYVFnlnYT62WxlxqAtsamPLWoB2te++v3n\nILNbso9RS6ZkQj13h3GuwpSLZfKnpoeJaIDcy0RsSDejPeqmWHEvVT/HnctM\nbReuxVb/vH9a3R4V+ux9qYOFa6/0La3tzBp9LFZQwU5P17laBvkWlMAOXUzc\nvPsMb6TcRCpTFq0YQJjzfkZJeEz+14ZDH0oNXUZGLqyS8SPxaM8E12SorkrF\nfLa25JmZA+Jr/+LpRIGi23wn9kidXhF0Ae5B32zuLfFERBlGWMlQRPQbVccw\nBkJJnZyYugdcgxVzXhUSel9012Mg4MazyxiADICITgccZh24P1zu+EqByW5O\n2kP2LLnvx5ZG4daaeizdPuAyaO11HgsnalE62IKbE6zqm+FmFwMsrNmYJ2NQ\ncSIg\r\n=dskX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"throat": "^4.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_23.4.2_1532731154526_0.689427826958525", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "jest-changed-files", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f720a6cd81d39fa2165c98b55fbcbdac93bf3e06", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-Xj4HMFdHKQAi2/qomHoWhYRNldZ9mXoVYPXa3rM/6640BO3GbXcnZxMGBTnC1hLvdzn/nhrjaVqpVu9q5ImyhQ==", "signatures": [{"sig": "MEYCIQCbQ5ofWns7Rw7ilHGMMThSDaDJ4rmN2id+agDj34wl3AIhAPkPLyMoNQBINEgJ/S7xNXSbrcG2VMVEHlcUgBGYdHKn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbycooCRA9TVsSAnZWagAA8g4P/jF7pa11rl1wyBm2KO/n\ngm9qdgIzPVl04xOzJsvpqFYhyH8a92xgpMeb/veSPKqbFZ9XkT5InsFHzyfD\nVTZTLqCuuhYBCM9+sYIGNg4WJL4nrmWG2W8LQC9J4Bq9Qmz4G0Ojm/x7ixmS\nA2K56III9XnFOIViqsfdW2s7oA9i63cFYPsWWUIUaXNI7dvuFkMxGl9/0sLd\n4lE7VDfd68aI9uMMc/XzjgOzqHWchN+DQnugNurwPUg/e7uMOq3pR9Tqw3M8\nkpyjkFkZz7QUnOjVdQBPgcV7qu/p24/fzy80pMfFShobZ/CavPO7dQ64PWXJ\nINWGeyqf/vOmOERS9ZgvtqWxRmc1+LJtgE3WQWJ+56OyfXMeO3K507VHAbFM\nInabFd/KVekwpZdkcYL/f9voVLTt9p2NT6w+BKTqlYgB5nlPdSOH/kzfNffT\n5MskuvWh2x65VarGjRbMAGaj0W9KKNjmwFvu3gMzrg3UOIf42Whgjj2Fal/r\nt/OXtOsjiCjxUMnkM+mfaPdUtVHv9eZs5Gd7bA3luDVB/IZIpaVhEH3psaqC\nz2jdbLbfKJuJc+a795UZEgROBHe6oK7NCatF6+3yDWH+rs/X8ABM5pSFVbKQ\no8WbXgRI84TrJk+h/3jtrqWasHR/wdpN08DRCnjqZbp7HxeQe7IxrQ2446nz\nHfqk\r\n=ZZ7d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.0_1539951143625_0.19770993346375199", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "jest-changed-files", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad39ec0b79406bd22c9cf0c7e19f32b5552f2b2e", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-1KRIJo8AnDU+F9HNdvbdurn5xN61hFUlMf8CSCrPncM7zDlP5+SrcKPNtYPXbiTQy4cXxnj3ebVaZ+h6niEDzw==", "signatures": [{"sig": "MEUCIQDcXsguMX91mBWNkZq73LiMbfZaxrowrPytn8j9TjBTKQIgCpqN5Z6F7HnCumPC7syKfTQGiUcXXGZqAjVYmLim0P8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5CCRA9TVsSAnZWagAAmPsQAJkbXrbJm+Cfiu+Ox2y4\nhkHCoNmJgU0i5XpRWca1VOze0+dx2akA/xPZOJ2PL5EspX9iyoNegyFkIjjg\nJONyE8a9vwMx/GpUkTvXiRDdruNLgRI6mqFLyiNAUZuXXv51JZ5FoOM48M75\nvAZK01gHUsfa4miuUBQCq6Uj9j8Gw1XEDMtGvPARL+6cftkBHmdlu/p4GHR4\nWf/dQzPlS5Zaaj3gvXJJExEhcPh3EbQRqaesfvXSulCqQcZchq+KVl8ydPL/\nC9IbF/uCjNWutBz97L0JEm4riPhmpaOol4MpEPddAd3Y8Xf9RvD2IYxPH0yX\nuWjx/clebgi76ElFAZ5RnqrZLEANkcXNz8lY+rAMgDeWxnFzTNb6iQ3Mdr5u\nhfsRiD8blprUSeYIfVcB47AqgaZmTtjsh8g03rE5vslnE3Kx1e8/D5YiOC9z\neh8shHAS6p1oKXnYjlfKEFCVNgaeVJjGl25cUGWZadBnwDIzZzKNHfYcMRjw\n8XjezuyXiv88WyWBTWKWzyhx6mpKALAuhbZaZK/lIelJGE4E4ty3kppv6ezl\nyhnRNzxBnKy8912nNxqlO3btjN2niffyzPKkxJZYLXL0JLWEyHP9FBfhOAKL\nNsrsXBHIAmzRiULIiNuzNPZh0mef38yqoNX62XwiZw4wyQ/+PlttNbO+zqdN\nDDSC\r\n=DdBk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.1_1540222529558_0.6169039592610968", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "jest-changed-files", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "64aaa9a493f98919824c5fa05696f318ca0abeb6", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-QyV/Fa9wxBALp99pojvAu3jNpCyWkoNciatc5U/5wGLbyFbSYruGs6LG8uVtSJNdDKRh2PssAnzTBWTmVBXajg==", "signatures": [{"sig": "MEUCIDN8bK4QTIdkvcaXExgNBOYeo39AA+oxna4qcAysb5EzAiEAjBk+tID9KJ9zyEBVkTZN5u8EtO5DRIzY3DjuSCRd7Kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0ZuMCRA9TVsSAnZWagAAircP/31Ab7yE1gupgq4KdIzZ\nFIgux3s2BGkhdhFuMmDrjTCd7mT0OMMnbPHbDgG7WtkiAUow1vo6afTo3+PC\nn0qZUup/x4Vd6EuSLcdsQttK9ORHGw/nrbfozolLG/dL1dIje8bU9u/U1hJw\nsvLUcOayt05hqKL7lb/5vRA31qZpy9ifI8g94GrGREPcV2Nzf1gEjtCgmJIp\nI+8B8y40+2rud088i3LGF8vyQt2eB5nAU6LlLSzueAqj+EtkB9UM6z5nmveJ\nilJC8w7wRvBfZl2tlwwXYrBfYgA/ZSVeUPxcOOhuanUzXlX2nc62lFOR+W/6\nHlA4LHAzPLq87HWroDicRjLkpJm2O4l8siyYLqS6cnWqOPLNBh07cuehNL3d\nVG27IyZDUjnSQZVDldvkvNduiEZiGA2AYjPuM79mjWoMh2JFdiBxkNmEB/op\ntrXQ8rcFkG5Bv6HeFh45xSIBB5br7/9QP6EZOpulnmF0iOTFbSsRzhiknGmR\nDEJZU172HmcMN5HFemiPvrEuTLzUV2XcJuBSm1lAWm/FzgXfT4oZJbZZPXjt\nVCXKh9nak6SvCAkAi+BOGn0G+yjyGcKK++z2rBr27UgOdcvE6CGnNnUYH0d1\n4zVfZvoQl4yANL7hkOlhilOxgNt46vNnzo0IfwOgZz21YWbh/WV08VmL+oc2\nzIXF\r\n=bfA4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.2_1540463499904_0.5201523021272683", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "jest-changed-files", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ffcc670aa1734c2d064d7d6c79851da8ca6a563d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-UySN8v8OmcN4QL4zJAjL0EE8wqvltwKH69tKzrbAb7Mj1IuOBdlvccJUoDnKg9MkVAbVqk/3/sgn28tWFf7prg==", "signatures": [{"sig": "MEUCIGhD6nQrO8IK66agdvqjxcgUdMaw+ycrcHLCDiB7WC1RAiEA+RgdlxXdIHR8/K4llTa6lY38oIuyqK+W3uYMfuTRMtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00G6CRA9TVsSAnZWagAAPUoP/3Owld/mC4fXMz+T79kE\nGhvJepcdmv4BceFsSRF0LbVsQbE4+wgbgApO5p6hKs7wNmEDWmn9QF/Ctxka\ncMI3aXpzWL75U9vHU35JlLf0CMBo7NpjIF6cnMcnYXfZ16w+WNoic/TTGxe1\nLlSO8sploGf4fNC7l/3Y6hB2zvx+UDMCtEWXktyZf1fJaeB+R2txAm1uC9wq\nErRuGNd46/SpG3z/cmYtAMtM3frsIggNXmjMdspqadSWfb1r7fR7trQCEkKS\n9PSC80tT9sqlszVpB7xoy/13WZFWRnAE7O3nKSune0czqW3CclYpjPNZN8RF\nHlx/JDeQmHK2IDOW6yPYbyK5OTaHCWkq6krjf4RLw50GIPLRZ0pwz9cXr7A9\nKi5yrLjrlpM8SaSzII2eENj2auXCkFtZrbywMZGsm9X7vfWDZTAW9Dg53o87\nuAYfZT5s9GxxxXSB4RDVsyfXm6mwY1/lHXpwGZQ00J3LfFd3DHeXLoXDjCLL\nJkw/FPEeZwoEKKXbBYM1DaFqUsMqDd4yEzONu3NfkV6qoXxILWXC3QtHs3VX\n9BrDVghLWcbU6BV5MUkAw3gvES2jJ2G+iRs4z01h+oNlY7K5b2yxVC7XRQVO\nkRAc8Hkt2r1qvD/tX5V3tGv9nlW40ItL73vy4xYnr0KK9mnr+85I422xTHEH\nYPmL\r\n=Uwcy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.4_1540571577606_0.7919140417831128", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "jest-changed-files", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70216db5e3420feddc95a6d546d79407988b642f", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-14x3iiZtIzrBnndx+eL012LmLIolX0yWmFzsuXTF5ZDwPa5TR2iylL0TOniL4E3DfcFo6Lh7RMhJ02nsJdx20w==", "signatures": [{"sig": "MEQCIDZPbSpzFOPQ3bLD3OIxsUoN15Ux8ff94Ms25spWkzc3AiBne/qlI5AK5vwVnYUHgBu9vdC4arO+16e67f5xDo1hPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5Ye7CRA9TVsSAnZWagAAvzUP/3zYIeNBWx9wXAhFn+sw\nzR5D3lJ7HmtBzYS27iFnx41Ebh1KhZBR4vg/8nos8iLQRnquVMJFGsUmxDIX\nQOtu7Xv95E3KHGXcrJFQTDMWtuwC8NozcAte3l/6gdc/uivTBNb314EGz6ek\nmjwLrHDueXRmIyfbLmpeN4j+4RnCK738uiANvcTN7BZjn2/UqZ/GEBraQ3xv\nk6kUcjOUeLHnP6Q80ck1oWI7lJSqViqe4YboRYZstVC4XBMWa5oG72xOESL/\n4XXHGhq67Fye4oCmlwEwEntOv4drb9fCCtP/x3JaN9LUf7vwAHFen1PKQXjK\n2OGxlMEoqv8wzax0sH6aBIMFLQTBJfmyfCyxSwy8L3HKdPdjkXd237PtV4b3\nUUtWGSgwDxalg1l8y/0jnpLtRSjk5+t+q1pu3pywXIZ6SK9B8W6Jf0dpAnqb\nxSNk/5v0JfraQqjeP0iyjN9YyVbKVUWdTepi2hvbsA74GHK22q5mwWnubzB4\n7EAmWtSqM8nb8l8Yh+3gOSm6ZKkGUdo0/cP1v/Uyerbnf5BXJtqDD2bNvKas\nBhpVCZQczm6BuvfRXuvcS7Ae7zuf7nIC7gZVZrTlsUPENqC/xGlp4LoDCVaZ\n+mnUjdYfaYmsaL74mAgUNzoOw/nb1Rzla6rEHR7ZQn7OwfihiNj+Y11A+CYL\nPKND\r\n=CNFi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.5_1541769146737_0.11258718463336348", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "jest-changed-files", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f3fd2e96f67f3e1e7e2bc74fc7c81c5e53801404", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-0Z2UpqTqMNhMNEtIK/1nCD7jR4+w1YM7zAaMFQMJG28UiV0Oqarn9u5OeH66BbUvB9FnKvVJTrSPpgPpR9f2YA==", "signatures": [{"sig": "MEQCICVchNpq6ZDENJGuVk2Y/URCJ1ukAdYlJbhahaHKo0z8AiB8LwCEgzwr4hY2tEwzHQrL2w1regaKwwSJ0kxV1i06uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5cimCRA9TVsSAnZWagAAoCcP/R/btPWRNSaWqn84+dhL\nwvNLC3GZt8h2HYk1qR4JyUnMpWC31MNi1NNgiKxAuaybHzcEiam/cGcFThbg\nVQgojtc3O06erE7W5plfunBh4hSBLGOfQIVAOLNPDzeaVpDKdZTX0iQ9mSRS\nMpMujXD9WF1HB/sOj9TrnWoJOrMMmdxyVytZbTRkWeeHY8K842N6uebTP0LR\njJ1lb6aWz5puWkcSREdUnk1NdBpoz7+UbzlExjSPBKOGRSSpV0nkEcvhPdR0\nf7Kd7MCcS72BjeZSXU5LHUqn4c30RA7GQRmgNMbG6sgsFSST1I7Yy/02dSEe\nadjHy51YLzhXeWHYVcMv6c5th44yVyDfWlfLvNGl+dwW/MMPj7ajizCUHHyJ\n5Yh+/FiTGFVBUvZYNKceuzaqHl375Tj2CLRJ5tUs2f5HoFdEmZnMKOpGIdzr\ng+MoHy1tQlTLGg4+ZA81Qa+xQeFYwgVLKDDVggY+RTwloKHlW454OKlILqMG\n7SdpznwoxpMRLPWr/rssgHOVSxXmOcHXOahhSJBRxi7hAuHbXe2FJnXeHQc9\n+z55tCL6rn2wHenyvZKhQak8Fwg6Tu9ea4sMFJDepv58BbTaufGGJiPqmk31\nR490QcV8F4ciP/r8yFcgfbcIgxusqNs/nnxdCUSrPlCsI9n/4yKGa3oH5l9f\nDcLw\r\n=qO4X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.6_1541785765523_0.12944869464272712", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "jest-changed-files", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1ab0577aa2d9de073a5c0edd31e220154dcd0e84", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.7.tgz", "fileCount": 3, "integrity": "sha512-uqdtNy+SZHrqExXEBmJtV6DHl7mN9i+l+xHYaFbf2XWD0JzpAi7dlh203gKBSwqf39Y69pv7IR+VVW6o67GR4w==", "signatures": [{"sig": "MEUCID+UUlp3MdNmrlxwlf2DMjwnaPbR3djiY19ThDGy9QxfAiEA/GgkaIMT+M59IB1eP4RINInQjMPMGpQ6sj7BQ9+ikAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+DCCRA9TVsSAnZWagAAmdAP/A7XCxSlouPlBlskZwYX\nbC1SEjuQNyz3LqwQGJVQQOSWvibXYNjaVxVrLJj2O2j/1TfNyikn7Lw4TcV/\nKEesAMy7JDxb0rznhYtX57GvXeaAbccaHCjOoZYHYWPziEKrXW6SNqsbPHeN\n1dPPqxxKmDUCWp2ZSp8dBxiywXZjdjMCATTZfrrOxnwTNEiFWqIdbv2R11KS\nrzxsgyx2MxCjZ+tcr4R+mdbhSXAw6iKSop68lEcwHP/7xtlK39LrPzbw6rVR\nYgvqd5dS4Np0URdaYLeIKO6CjfwDKwvem5VC5XUoOdTREbxNWJH90zIcQO9J\ndCjWAdEDcyk+qKx7DqAwU/XLUZoQMdivQ85QYH+qXxrL2Bn4Dl4P2QEq4lqt\nX0UUg//Uga0hWGkHAUhZ5kWcOdpmjzMbcwI+u6EdPhYPKCvOtAJrYeGINt3b\nbTfuQzRqwU37BhVzrw353XGEE8Y7SBQcidAs7nPLXX7mCwthEckGkC8zkAP/\n8QU3U20TzKo45fOG0VGFSlt+OK4L4IiYjUpEApo0mUEfx9TQKrI2mra3/dPv\nADfkrQ5dfUhUESrQjwGMPpx92feiYMGcVLpkMmyPFDN7YzMukKZtCYGntJQI\nLhg+jqwJSFH2xvVHbvR260Ez8aPVyyEgxA/sZbgO3PadyOeDP6tik3NqktOI\nfSt3\r\n=IV1I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.7_1544544449491_0.812935611531395", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "jest-changed-files", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4b7f66e767dbb50251663ea96c9292182324289f", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-VhpGYzJvxBFBXb7vHygUbHN/ve2vlgQN7ayPa2etKbxf7F4CJAkLzQRkt8y7PUnhP/oFXq7Kd0OFx7YJxMmbLQ==", "signatures": [{"sig": "MEQCIB2YuxEjzCg1/ygG5kWRxVgX4+v5xA3v+R8oWGv0idDNAiAWbArUG/G0H2w8mgT9wClpfQmlC6nYxzexysvSwU47Bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlRKCRA9TVsSAnZWagAAxToP+weMZe+VkC2LvFybIR79\njMvr39raQB3ZzN5t38JEjj8GoLFpMp1ymQ76Iy95dSjBHBzejjhE2apqUVsK\nhprPPdrmjuL2dxfz8caHEi86usPlQy1PO+xMGtH/LiO91qJKFpqecFLDYkyD\nk7mc82zfESyiN4gXevhGQcTtJB2PpegnB441GPb4eudQ5kbfq3zhakprMsMX\n2FPuB56P+pdyjRRgTk7DmLxZKcEH6qJYHnuzVFMqEseNQjSIb4KBfjG4KDo+\nl8JsFA8jYan9kt6U93ETVENGtJCQfdCPhCc+r/jhF+YQG1BFaiI+y1Ar0J08\n37uNIdPzSyJFOO3F7U+vhpMeZTw8aVkkjsgC2oZCdz+XuXwCodSygSTWcgNY\nKKGDnnSP+tCWfmDQHZLvwSvsTkQzVopztHleJUTQbRXIXJHfd3SAmv6b/Dl2\nX9ttmCeit7D3ERSYNux3i/N4jjh5KsPGMVsq538VmY5+14ek7wjiUdbmUMG2\nTWWztUyW+GD4X8wU1jg5eSHqwbu1eapzYyD1u3O7JYMCN82qOc9IwB57d7wx\nm9tHv+hndB0fYCjS4mrqV+fnWFl5goNHfGp2EhrrwXSVES89ltL7IH1q757w\nTvsEH9/7DCWb6pZuiJMJN6AbhNdpV3khWr2Z8eEJ1bnSQQSQuXva9D6ig2l/\n8XV0\r\n=h13I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.9_1545229385414_0.12539718442995773", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "jest-changed-files", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6843ca85d2ab73207a0a6b4a35671ccf76bc930b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-oD7+0KgW2Zsa4zOBRWDuUpm8z5UZXjNBbmfvdX0arKlULwnD8jZzmIPLSaKzG//ih8W92oIxgaqDAfQQBnCOVg==", "signatures": [{"sig": "MEQCIAy+AQxPAqpqdnVJF0TUPTOtPIpkcHamXbTJ8cX47QtxAiBN+mIQahU0dzqD7pOQ+rE6CzNtJKHimw7sUif2IL9BUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNijXCRA9TVsSAnZWagAAJZAP/RnIByqxL9xubxXDSlnl\nxW9mTisriLA6JqHitsc2BXfQvIQlF90mbbp+/fDhuhnFtSd8zJriELoyXnZM\nAqw4sbCC8DXS3wLC6IPKnDiDB7Jn0jw30JCKwUGqAsPSSaEStcrjCvMyq5m5\nj/MVMR7poqKStGQLugkgb7RV3ryg8MEp/3zDsDJrw1knPIJCioaS70usNqiM\nhufTN4w67lwO0z/lRIG80U0UH0c7jhsxwaPRgl8cDRgtGQm41hKiB9+jmKTj\nrhzoOjQa7UzmNU1wU7a9hmz8OpgXuqRtRAjnj1SWsgfS77rxCm1Du4y4L7E7\nJ9AuPI/4vyuyJ4V4E3kQo8y0Xd++q3bmAO05tTRPWONQAxwpj/KwvLLIJzYr\nOWaEztN5Ty6plmR2d2hKMn+DFzWxHma6tu1gA6HCQJei7cFWuaV+8y6xRIXK\nLPADvUO3aIQeLMaQuQTetnwkRv+ZviChzdExtCGndNWMGqsGQXXl6yB+LNtU\nWMbcjpbIw2PUrdmxjVshe9k+VSuyhVCDvzp5FPFh2DQMyu0WdtwyYidqT3PE\ncn0tRS2BLNQyZ9YvRAG8Y8ka7qmrkuqrSLX4qvJ4uqj8Hjhu/9q6MELoGQUz\nbeDZtDqFPGHzNsu+uSGtpiOIB1KyBbmI3kJLNMw4U64HUx31hC4GxnoGos2s\nTPfK\r\n=79Dc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.10_1547053270609_0.04142259495675571", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "jest-changed-files", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7075268c61f961689c891ff97551b4021bf09d95", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-LT0qDFa6eNhYxhU8qayH4QJO7fGQTT8swQrVC7Bp/q3KToSXNCXt25WqRLGW6NchmXLuhjE7KhuIRGaGvRYeOg==", "signatures": [{"sig": "MEUCIEtR6Gqu6y1+ofKKmrmQHw1l759pLtPPERkZC/WmcMDIAiEAgg8FHBjO0Cn09g0u9NgzGMxW1s0HHcXrv0fameegF8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN4+uCRA9TVsSAnZWagAAYYkP/3kq/wzAgjxrLnE392XI\ngzdy1I/fjWSzOuS+oLC1ggOnugGU3dldTgW0EvjzmTuEQXniMWTE/lo3fsoL\nCISiLOLGsZiU87BwnVAiJa/4U/yLuJL936yIWDSv4Bte5JD1wov7fwtX1r4/\n55TC+COck+ntZtRP49RROsn55FvieBMHLrDJqYTJPppL41UybzRv+RZmaUaE\nWOZ5ivmIo3Lj/5q1rqTRsj/LDKOlZrdCohVcMRDDA90xiG8US73HSPem1n9Y\n9AO+qIIpmDRaFZlYH8C08GSGAIgynl8s6BspYkfNHFSECItCJrdewnwSaPm/\noXk0nyiuyu+v2kQWGrHeBDfJ1q3u6+I1Ox+FWrD8nqllP1hsdIGrKDVGDh57\nnppxDDydX93lvPdFsIKqXuYjkIURDGm2gdGXSAebRInGZz0RMhwaPWZkk8TI\noBQ5dE6/SPE01jZxzuzrFy6T/xtFeSxdo9rTBsvX1E4lXX+vIpWz0BEwms/m\nr46pBILukhehfJQhBNdWPchZPcjBfMEvkew7L1zralRMJpQTj7Yc9C3IHwT3\njLM5tpebszu8LxMFjIJOvPnPcMJ0Q9JdIDhODpw5XzlqFQqBD9fpV0+TJq9N\nfBEKSGLONvHx+xf5cd2K8bvWI1rSPlKrzxqeSWWhm2i3O+ajG1KyDLXtLZg3\nL0kF\r\n=+A9l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.11_1547145132116_0.9278931163676594", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "jest-changed-files", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2335006dfe100005e6a4e39a3860632d0875d28", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-inqojhMabLvyt6jdFyoLvmuhP17qsYjfbyssVzF/yGPUI6eb20kfSXVKxJ/TlK/f9VPa4Vwehu3XKjpZ+Ky9zA==", "signatures": [{"sig": "MEYCIQDRof4YWWBiV/MdzLxwM6mZnE+hH4x025MYUpA1AXqgKQIhAN6gdWH7SlIarFKNYR0AkLRw3wVkLR8H3AQt/42hNR6L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK8ICRA9TVsSAnZWagAAjoAP/2b4j3nmsnxJ4PNitMMC\nqrFPA78k5uW0IkiTvSC9FKIy8jwlf/H1v/oMGG/RgX6dCyX3PrcNhEFnUfnE\nqXK4Pdgwzvnzuoiutz3YKwYLfo6P0YkEq8caB5PR/E4OHyh3PYAXDRHobXER\ntIt56cXXC0iQ8XzjRdnqO+rRdqC7NqWKDPAgesmVIqucA60Y/456jo4u2N9w\nfPEUW7dT0OdbBhiHJyZ166DtRB6h0Tv4o9PM/iuod+5Lc9pJ7tfJOXK6Hf5T\nOi3x3QCDIY3R0Y4PV/eUK6webjI6TVvHPzXCkg2gMPwdk4hFDTtGgIRRY54w\nGVeiKTkhAWRFEx5EzBQww2NqhN/FyzbuHcK14aCtu+V2Xi79ZboHRPa6pW1O\nJJfWfcp+hjuEnfXkarx2gDoLWLY3s1cTyUwcCx4GQcceMJybQbC4zGHYHFGS\n4yGPKRPgS92eWBsvA2rH2AWfIQbz8kAjb253836ORbV6WuNRhDmVgXgGOhj6\n+qqhdtlQzFn0PhuZT9NTLdiv9gc28Mxa42yOBZJqC2o2qfwXZVUIbgMOHaex\nvMBQ1KKqZ/GSoC92LRsY3w9zyRRZxUZAJgGldy/Egd+W0vKeYiwlpVFfIt3m\nLLDVyrGjc/xls5NCKiBZsaWPLzm65ldyZ1E0MjRx/y00FsVYuHPUZvTPYYJA\nQe1F\r\n=ntj2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.12_1547218695670_0.6149617911401901", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "jest-changed-files", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b3a77979886d3205b19faf51f7345331eb0fde65", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-Xd7prHJ9YmfpFBHe0NtK/WTz5ZdeZiP7EXUbpNPGW7ZKYiU9xzMOWzmaKvavJz+31hmtd1a1nqyeV04UtNHBGA==", "signatures": [{"sig": "MEYCIQC70Vh/W63RmT3m9r0+dJcRCbiTOzellrK0/dcFdClrowIhAOvUIksxxrM92XXR4GfPWwKhZGA4Hj0hVAXjxQZFgptF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUECRA9TVsSAnZWagAA2NIP/064naUSOrK1dZrpRwtx\nuHAktOUBLgsjEWNSYCASfvaiSNlDkj2qCpYNFpe/5mCGbIucw0BCoDPhhxto\nwufTHiFki0rkn1KBacii5UVyXO83V0MSU8gF0KMaPgAipRjksty/p1ajh9MT\nqrFI+0StMDUX8gGCjIxr5pmOLGzyjH48AR/f4aFPsmx1bVXxTc9vKIlYsnWP\nenvt5yWNawNuz6RO1pq6d1aniVGm3/QyMpfy5oJSpzWhikMImd8+Bm47ZzCd\nUUPvTmFelDQ3oYDn6DfdEGYj4gElLyKfgK3HCMkyLSBmDOWdHLACZ44wzoPr\nTxy89Y0zc+7EkeJ7ikZbrMPnpTFD6xZRDtWjcu9jIYJdX/3DmAU1ao4If13L\nY4+o7U2i3XO3ZldsEsatTMpygXsz8ME5ur0R9PF6AgLQeugc2Han4AWCH6nZ\nRP4SMbSy6NyeZ9tp2WNfdSQ1E+GdeI49RhevP3jUjZPFe7PVW2L/qR3qwv+V\nR/ZJn0k0xtGSjmAt0uziZM3FVRjMa9rTLbeYRW1LwbgobYO4kXA1vv+w26Y+\n8G1skQP/XxqV0XKDBlKlJ7rRXLwQWYtIgVj5+GkZ3A0SKFFrHWOCOHQ1Br2n\nMmCXex48X5QQg7UW+Gg80lBwYqVdnIUxu2mK0tJiL6WcwZEvsCSa3x+Hw4A+\nqNLy\r\n=QM9X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.13_1548256515526_0.7123931980508149", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "jest-changed-files", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3bdc72e81d880b0201ff5a422f071a090db62b1b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-/0vdi5LGX5og75DYJzq36LsE/6McKCxk7mjQ1HaqsQWkypySEr3nL8uX0Fuq4h6HFgInNp2w8UoVcXRp2KrIYg==", "signatures": [{"sig": "MEQCICa2fujVjuESG2q/xgocyx6kZOJ262REjW4fLYf4VoFFAiBUKRirVJAnxxIg57sRNUgDEL8NBmorlIyf+a0Bgi7njg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSftRCRA9TVsSAnZWagAAORkP/1InQSG7N/hMX4GJQyQH\nkI3RS8PvEUVN/KkOfZk/3H6OMjCVYwHlS4w92vnsvGzHxrDsHyTOpMbVJvkW\nFqicPg3leczEN1+WPZMU4Qf7xVKCS/7aXT0/BGWLBnHf/t/2A++jrNtuRgoe\n5OP/j1B/v+XU6FejgpMR/FGB3jCt8RbrKqiFnAMP7mZy0QhXAbzUOGJHUVQY\nFGEiu1igSCvVQC+JTb/HOjreK8efH4h8GJ1rQJ4BaluhEFJfAoOy85PU9RKD\nBSBiCW+eOHYlgS+iWtyJvDD0sNcMUJCqepoIokCuPVWydwkJAK01JmWcAryr\nvZ8jZZeAd8RHiuKNQgRRu34rNPhP231yFNwReE6SmmLOq7C8hpAvmlBiSsLx\naJAZEl3zkoXRIFE8S4IzWqwngrIbSztHuJIk2asAQgrMlaejKY/8hntQFwWY\ngXIjqvri9TfKOnKCY4ddgzkl0+WOqBB775wE7JtXG0eEMhnE2ot8JyDRxgvN\nwPVKcmPlADolyg6ASpleN8Ac5L1AxvRdcYY/J7eLXmHDbU60M1fNH20cmzPU\n2g/oFUneSqNWGCPj4UpwKuwbZpsejQQ6+0buaJzTKnWa5LYtCz7BAztDbynn\nAhZu0b1Y+BqBbbSOJihv4okGclId9RSw7WNXqJToHgsn461VC71tP/71p5Ng\nek89\r\n=bJN9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.15_1548352336995_0.04337671426130241", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "jest-changed-files", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "jest-changed-files@24.0.0-alpha.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2be49691d5bb64e5983a729812592490cdec87d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-mSCo/IX0Y7KVwlXroYGG9MnMe6cwiySF1HUU5GukzsHOUiz46ipki3RLLd1PMAeTykM8WGupxmuqNvnGuVS0yw==", "signatures": [{"sig": "MEUCIFV6hVftOGOWAS5ZkZq+0ikKv3FUSCFN8STH9VndvJD+AiEAp5fubZM9NTRKZUgISVemMy+LXlaShrYXYJJF4X3jLLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIcCRA9TVsSAnZWagAALs0P/RhdFWSCyeuppNHsjrYi\nAYOvKYuQUvt4FBnNY+hXdsssQEHO2xksWK3FJXnziRw32vZ5iPpTuRtv+pDx\n1sy7K6bBF+pkZX0e3yISer4CDq6Ct8gRCnTz3/HeH46RCXWQYT8ZjrfunWO3\nmavw5jYFcJHatnNU4JlVXA6tPxjb7OeUo+N7D8iklAykvS3s16XadIuA2nKs\npe4+DXr6hCUaJx29aaR/jWujDmYsjOx9aaH7PKx84U2hS0/KYxjcmS7bNEry\nAh/g9/OOFLuqy1XcmsuwVoXKJBDoYwOH1H9RstXgsywTONicUuLGYpwzzbRy\nltUtU0VrcBDDEnp+U77USGYsF0SpRV1H8sVIqANZhrAP4yrwBWBw9UrzBHtC\nDhcmTA0jLDheYPl8NVQYFhCiR//WD2m2aWYa3mxbhuuaQOGuE7fWXP7hJU1N\njQcfK6oBI0gT1o2XmlvXPkIuHHwnD+0VN981BZSJ/GU/d3gpSAzc8ppjy0DI\nhh7DEhk9vk1gQ/crm3VUeIHzLHj/nsC1BsriuUUtwPl1HyfKVRHUEGBohbCU\n2RiZUE5mU2RTOMSfAojY7D1ZBmKVDsQl5yJz1WZqLaIuAFmc61haCC8bkb5C\ng/BBudpy9uv1UboA9lExRjm1vPa/lgI0fs8/buamhGnkCEIRpMHRWQBhw0s4\nYXdY\r\n=qI3p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0-alpha.16_1548423707651_0.33386345501199655", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "jest-changed-files", "version": "24.0.0", "license": "MIT", "_id": "jest-changed-files@24.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c02c09a8cc9ca93f513166bc773741bd39898ff7", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.0.0.tgz", "fileCount": 6, "integrity": "sha512-nnuU510R9U+UX0WNb5XFEcsrMqriSiRLeO9KWDFgPrpToaQm60prfQYpxsXigdClpvNot5bekDY440x9dNGnsQ==", "signatures": [{"sig": "MEUCIAY++ju4qr1/FFJiW6w/awSYVLE9GsysIlYBVMVUxhyCAiEAwpB6/0iYsoBvKhFBO39nXVIfL75dBJDHQqv6R/L1NsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWKCRA9TVsSAnZWagAA5N0P/jr8ulfDnX0AETiK3QOj\nG6sNX1ueSDImw8dg+vUobnDbkYDzVFLDCMKjMSAqNbKaTQttDRAcqMYl2+gW\n+zB1rRvkFDRdFkSWi5k2u62RSfXkHdtUHSa766/hH0BfIOJ/cENEGIJPd/Q9\nH/UsSGkfW8JjZCjZhZpNd9ITKojYNwbeH7Kjj4vTKqCl1qCf/61JBz9fS+da\n+v8IxDqu7lu2mML6LA8IQIxBnfsduRZUjWireOKHkuSPS0OHAVLrdDhv7eE8\n3cb/ZsGMiThHrBMma/U9gvxFkx3atLcGxAjcWGEErDRU+1cAnJkiCnFnS2n7\nuX0AeC9XdcKYxGvG936lQG1ocxU0+DswJWtPAv4a30/v+uIWJ6yLWMq3nbOd\nr/YDOp3CbIPvFqWTp1SqaP+GVU2yUWi4C7JRUTwSwEGyBLKUbnnJ1CEZc9jt\n/zz05vmskByLbMGmUl1JxrJCUE0Zm3ZBOK+uKci3jfRv4nZNUkC1z9clz6Xl\n4OI6K++PhPurGt/PpvWclu32bHnB3mzDQZjm7c/Nhp3DM4bgr/Dnj0Iw/3RB\n1oMYzZEOdn0EJhZK5UxLceArzl2eUjIwmcSgbhCbIoD9JyogwJqBZaZ2AAeJ\naQ3ydbJq1LzlFGrluiE63hbVp/bHEMgDs4x4xQcGfUaj14FZ4lcZW5pT/kJ6\nH4SF\r\n=xx9L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.0.0_1548428681926_0.8532084853188966", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "jest-changed-files", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "jest-changed-files@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9fdfa428106a116eca363a15ed61eb8755f82332", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.2.0-alpha.0.tgz", "fileCount": 16, "integrity": "sha512-ChFWRyR9JlKy7Pa+4pMcFP/YBkF28F9Rp1PN2EwnYMJnsFhdCXeSHEsie927kJVWXXThCGQWQqoV9NayASSgRQ==", "signatures": [{"sig": "MEUCIQDj4GYZxAHElaChiq1SFhbJ03kkRBhAr50luxzW68Fp4QIgD0SGER8dKrzuKB0u4Q74zju4wDkSlJCRHG0SG0Y+p70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfovhCRA9TVsSAnZWagAAfMsP/ijbiA+SK6zHItHJuDBl\njArZRWLK2SRkVp3HvBHVGPj/FuTBioDvpHg7/b0J8n55ok3GR3VYQzOY3nXj\n4B9SydsIv00fCGbcJbC2xZ+K95wmrMTWZryOJSCzCMQxU4/lpSivnrSW6hOR\nkwdjg2RTlwI+LMIlY6dRw5ECfJs8xLtf4x3sUSQRfPEVOI/3+uKMG1RBOA7x\n6NGIbJF2wme3ESJAYA8aVc2UBTrdd6EiS5NQRTZSft9QEhBYp5G7CBpX2g+C\nN0j+nQPYKgq/7Yy25F8CAcO0VF6A+XeEymuwMIl/26CzUETMdZ4XntIEBXHJ\nHOruSMOIF/WzfZKdMO90RIjLW08Wi5QGD+nEbvmyja/3ww42Ql6tAlqhNvcx\ni4IcEP3LKU/Hly+L6k1/KUfvVoFvoa+TIaD+Eq/S9ULMi67T5/FdD5zjO5jA\n5E1a8mUihpNUBrqkzVcWUicSTzkpWQPvmN9xR/EVAj+KKbMI1LgBwF0nGdH3\nkdJ8QJ3BmqBPnKK0XtpZ/cF+B/San1+Kj+upmEHZU2az9BSbWeUpbOGkZN/X\n8Wzmf82GCdet6nWbuo1jFACqdwvMYJbhy1rvTCe+v35iEwxqWwfjaXduXgpv\nFmyrrZOBNs+WOvPioYJJcGEYV1JAM7YwLe+ZyYKBiW6VOX9gMAvW00QnWfKv\nHa+S\r\n=R3XV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0", "@jest/types": "^24.2.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/execa": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.2.0-alpha.0_1551797217310_0.8111319964743482", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "jest-changed-files", "version": "24.3.0", "license": "MIT", "_id": "jest-changed-files@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7050ae29aaf1d59437c80f21d5b3cd354e88a499", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.3.0.tgz", "fileCount": 16, "integrity": "sha512-fTq0YAUR6644fgsqLC7Zi2gXA/bAplMRvfXQdutmkwgrCKK6upkj+sgXqsUfUZRm15CVr3YSojr/GRNn71IMvg==", "signatures": [{"sig": "MEUCIDTRCUMl5EQW5P41Rwk7w41/jq/dfSg84iSWair+GgHEAiEA9wU6ac98dZ6S5Y4ZlR5Pi+G+0kJiVp0LEVw14ldI1ZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRW8CRA9TVsSAnZWagAAOogP/iWe5GlkqUF0e+qmt4j1\nr8m/WAy8Nwx5j8SfREAcgVAUIjuC7uGzBL401f8aYn9/hdVp3g6sfjHqOdkY\nvJAa748I7izfhiKdihOdgO1DKE2iQvFMyG1zgYABvvTvRmN9gMVgMiQet+KV\n8Q59EZzLycLes8olvk1RjSfTmyqFk6JAT77+BdSQq/hwppANTGOWf3ez+q+H\nIPBTEFUKPjNxM+M4LpUiFAZhzbPU9C3TxZqzexbps7aEG41bN9lgrBZwiR8g\nOg0gDw3qqym6Uf+iX3+iU7RjWhY6IHJITzZS+/hcOOQyocSuPwhB4IOxHXD5\nbrknerLy2JYSWbxjtCcinA575cVh93vgmpYNJiS+s0rrN8nzIzSfsh+nuTi8\nmpVXHLJyMhmVLEDhTUnIOApOHYCECoI1Dr7gwL3SfgMaHEl0CAtteQoGGnoH\nr9FkF8DHBbHbtJIFDVBL+rGc+zsmipaeSld8tOyuxQXK+lrfANy77J9CdP2D\n6NZhXwNEyAR8BqFdfSEAwTFB9bWfebUQdNT2n4zQcB4lIl1RgWODRsn+MBqJ\ngpgcKS3jlBQji2mO7vJwZ90h/UxJ7b51Zdr2DQZw5rSOzVQwRWhKE/nSdpC9\nuXw/KHhBkajK2/rkcZhLwCOraOZz+wNxfI4UXMmcmECWLcOXDzQSswk5uxQv\njEvW\r\n=bmSe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0", "@jest/types": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/execa": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.3.0_1551963579574_0.02322001425022835", "host": "s3://npm-registry-packages"}}, "24.5.0": {"name": "jest-changed-files", "version": "24.5.0", "license": "MIT", "_id": "jest-changed-files@24.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4075269ee115d87194fd5822e642af22133cf705", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.5.0.tgz", "fileCount": 16, "integrity": "sha512-Ikl29dosYnTsH9pYa1Tv9POkILBhN/TLZ37xbzgNsZ1D2+2n+8oEZS2yP1BrHn/T4Rs4Ggwwbp/x8CKOS5YJOg==", "signatures": [{"sig": "MEYCIQC9NgNLPZX39sWZ0RlAXoTK8/XRD+2fNtxR0zFC0nbKXAIhAO2+CmflVPOO3SOEqWq76E5cSEqeea7RpDUoMsJdnvbt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch+ALCRA9TVsSAnZWagAAWAIP/iyCudVWTs0kNJ6cU5E+\nRIYfPgz/gDrJP10GOMrrfxM/Ej+tsCqMQbcuj6ouYbfQzwyVnx7OfXN0Q+2F\nv6/BRAzItfxjUNuJKj++cA6IwKLkXMSGCj7D0b7/yn/XyXT9CbfrUu3egZJ/\nU79wIxCd7TlD8ddp7D6GsJWNfYVVo30AWWJULuCIj437ouBBtCmdKSdiLfen\nAdYIn3huFH13RjRk7w/f8mCwuiV88GnrSWo1vW6LZRCW9qg35E70K8DhMWzB\nCaDmj/zdnDDpCsPfFojGTd7SOP7fkvDPKxbmLiELkraoQtrl1irZzaZgyZhy\n2Q/WM7jWNpqvItT3VU4ez6+p7oexbVJK96GnjlYc3/+12aYSbsRA7cHQS3dm\nUVjjS6c3kO/imBEOhoup1v7CLMDH2rFAXDNglObEfKWkP6CauiRBNeO0V1Zl\niy4gik5TGu/lzl8q7vmreXKiiQd9h26e8QflM3r7KgeWcZnvxbh1hXQiRKv8\naDdDHtTCG5q7r8ATBsMfv9g0PKtR/daiFZavm472CgTEEA1i9yoiU0JlIh2P\n7PLir6Im1WXLGY91rfnLCgikqvA+h3mgIDVHfDWIQCBwXXhMIZm3braA+Zsl\nLubi4m9qvVJSX8/Y43ciGg1qZ0O2bkaGgM+qnGa1qP+vwIJMbwzV4KXpVUsa\ndRE5\r\n=Q8vY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800533020f5b2f153615c821ed7cb12fd868fa6f", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0", "@jest/types": "^24.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/execa": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.5.0_1552408586847_0.032976419822178915", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "jest-changed-files", "version": "24.6.0", "license": "MIT", "_id": "jest-changed-files@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "37ff2a60a6057dedc068f26e9ed9b77fb21df828", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.6.0.tgz", "fileCount": 17, "integrity": "sha512-Om7dJrGPcH6mMdEjMZ5XxRhCLk6qe1NVSJKOIn4twrtH7s8Nd++qULEH9bhRsdNduR2cMQOQwJ9GIVucWEKrsQ==", "signatures": [{"sig": "MEQCIGsYT3BfGzL2774jLOq1rHvpP/KDv6PVaFCaX5bnezdqAiBsaimm/gYMpBQLjdLTEvBj4ox7+yreY7iX4MQMlJW2jA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAPCRA9TVsSAnZWagAAxScQAItRN7HBz6jY7CUymleY\noUzLCtXNwkkCERPr43g30W0HeyMuhyY+/zpqiAoLnY9TIeiycYd514C0JG7r\n/OPb7UmurY4LyGpEMTHtRg7uYjiCCzV5iFYYfNN/qtzfjfREnwK0lWqLgA04\nRzJrQy9IUOL5aXF5GX5pQjLpIwdZVwAOtbo1F9fFsWucFu5a7Exe19fYxh4O\nnJ6BiwXubio1pLGnJe2sZrTnUzAIICRbXoaggJb7s8JGu6F7rK3CImV24qih\npThq7y1wTSv6maC6qw8Z2UvlufvAg0NPXA37QFgpg0fCNllJwQ87yWD8LTuR\nv9u77DhmRqgpAOLr6b/RfZhZrkUrIvwTbFtS6Bo1pL3H2Eq8ylkvS1L+rDIc\nEp64xya5vrGmHpP/1X0v11R3q6fUnnp0sPo7syxEXB2w6jBFee8p/4qJAVRe\n38bSSi+vWPXlujNugjbjH/n2hGh9cAWs8V5x5BQczRE/gAnl6a8rYuGFtiAi\nv7ldwwe2skK6Bkg1ST8ftyPK2RsJqk2tyRJA51UAMVVcyAdm1reL7e4h5aqA\n1T3B+ZsFxGcb56EVHTN2qNgNs+EQWpbYrxaSNYDHFf8FymxZaPb2iFRPiEcR\nR3O4mEHZvg0BPrdpkcleI7jAQYBwUhEFPU93FPSF+HuWggeGnt6wvqc5ftE4\nbYu4\r\n=lsfp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0", "@jest/types": "^24.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/execa": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.6.0_1554157582582_0.5193708630109946", "host": "s3://npm-registry-packages"}}, "24.7.0": {"name": "jest-changed-files", "version": "24.7.0", "license": "MIT", "_id": "jest-changed-files@24.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "39d723a11b16ed7b373ac83adc76a69464b0c4fa", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.7.0.tgz", "fileCount": 17, "integrity": "sha512-33BgewurnwSfJrW7T5/ZAXGE44o7swLslwh8aUckzq2e17/2Os1V0QU506ZNik3hjs8MgnEMKNkcud442NCDTw==", "signatures": [{"sig": "MEYCIQDuEsSo5exxnhhLEikMGnlriGOEim4yBOzvPXN0mAUABQIhAMoHdugoISFo8Xby/kQnsLQB/ELb6LX04Jt6lIHG/dzP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC6mCRA9TVsSAnZWagAALpMP/2xTgr9Qjgy067yjbXNx\n/t/qYKULY6JzYd/8qnU/l/5Z3064G4Brjg9qAtJE1HJq8gERX4VEmV7p8szv\nk+vaBVLJaSYOqRQbGoimcuyUsxbBCDvY7uO4SYnlpJclbjKIBY9GSXTUCb9d\nhVdXqPbgGaoAdmCy2hEH/E50v1qNN9Jg+hlifOan8y5E6U9aZ3dSCP7Tiv/8\nnMFZjL5/eUTbxKgLK2yfE7UqG4u1wgz/s4t/6MaMMYTq8PKs6JmJpGG2uYX+\ncRbvLYTr70kL6k/wiuRWd6n+g7C054zv7N+JV8xtQllaZ52S2O5Q7QV2SZzq\nbpioECVsWoJ64jZO/NXqKnGp0c4t/HToyOQjPxOw3JqKdBYQX4MEgPH0iA1W\n6hQH9Vs5KT9cA8VDGutJRdCvk1ThkipVJ3uNalxF+sUVoHcsrpuzyblxBoAi\nRmkxiYfXMpawo8GhohKelqrTO+WDMQdP4Osgq3rQLL9d5hpL9VmpwXM0Jvej\n6zdb4R9h9pQAeESmag2JpPiFwInAmljtShdWwFgbZYgb0LHDPxkFkJjsxuph\nPdhbmNWUz6pDS5Ulora8QlqU78CNzGfCevXQvKJ1VfJ1eySkjI+2Ox0bweTh\narBsyL19jxZZ8cUR/QmOMrCxkcwt2A/MklCAa0oppJH888fGZkujjRmwIuvT\n1Hsq\r\n=J5B+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0", "@jest/types": "^24.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/execa": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.7.0_1554263717763_0.2580032895421178", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "jest-changed-files", "version": "24.8.0", "license": "MIT", "_id": "jest-changed-files@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7e7eb21cf687587a85e50f3d249d1327e15b157b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.8.0.tgz", "fileCount": 17, "integrity": "sha512-qgANC1Yrivsq+UrLXsvJefBKVoCsKB0Hv+mBb6NMjjZ90wwxCDmU3hsCXBya30cH+LnPYjwgcU65i6yJ5Nfuug==", "signatures": [{"sig": "MEYCIQCOkuw7tJr9b6fBu4KCiKYE/1ws9kDihtXCSOrVwph31QIhANDYxl6q+7hlv0fjrN3RDHozEYWXo/HwLA0c82ZUSvpD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkQuCRA9TVsSAnZWagAAK60P/RkXwbY1zWOpfI8bjyh7\nyOwHbZGiR2SKOMoGgFizVSHBPPG/OpOjPMO7qJ2ZYGF/TIGtHkPOdzdmrHMz\nf5/HtfbUKNgn0LRQPsTxNsFxaariRmyXORHfpcq0T1KXDPvqLJOcP1p93s/I\n8jkxanWWOkIXQ12zcrp2rCjxnVHpqcD3jzb0ZNgYMEfmLYw+SrsolaaLVxmc\nkRPKTM2SurcYZ9ndpxW+dgDu1W2wiKG5nuJ0A9hpEzFSI9Uw/VbDt72Q<PERSON>ti\noNBbUl9YRduh/5XwIZdw/5U6YPqouwOZmmGB+SgNlDtcL4zljNoMcUvQ/Syg\nTHXmnC/ZBsQ9UG91bxbvWZA4qOOKlZMvzdGMpV4tDzfqwENbK2krCIWeDBHm\nmIAqJ9yEaBvOH4WQiDdfVW3MBbjYeWhNJz318/NUw9wAmv1pRY1yIZy3MMyT\ni9H0AebYX48GHWKrF1QV6UewkhgFRmfEUngxs3soTKM3SmUaKIVbnSyRpfpF\nvZ9r/gbQftII22uNpjmmwOhZ9hS5RB91T77BgsyDHwm2EyBmuIILDkaGk5Ll\nEsYXIgksij7nTEpAICiDdVZhqW9aTgyfN2aG7a9Tv+kijd/LZ32aj2hUpRKy\nQYNsD2P2JAiNCK/CF/l5ESx2Oizw0N/sskGRMj7glr6pwHHoy76+9Uuvurds\nhIvx\r\n=3Tvl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0", "@jest/types": "^24.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/execa": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.8.0_1557021741256_0.2858044246568774", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "jest-changed-files", "version": "24.9.0", "license": "MIT", "_id": "jest-changed-files@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "08d8c15eb79a7fa3fc98269bc14b451ee82f8039", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-24.9.0.tgz", "fileCount": 15, "integrity": "sha512-6aTWpe2mHF0DhL28WjdkO8LyGjs3zItPET4bMSeXU6T3ub4FPMw+mcOcbdGXQOAfmLcxofD23/5Bl9Z4AkFwqg==", "signatures": [{"sig": "MEQCIH9W546+RSMgrBWnYrI/C57Q70hEry1tc8VeOWNNWUeCAiAmIYpIpmGnh5ZfV3LeEeGqkNSaVJy01UG7ily1MIr1WA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVrCRA9TVsSAnZWagAAEWAP/1bjrowsz+tP0uXiktbu\nsTNBCi9Rau8FGyqAHR6KVcqykSQk9Ro2adnYb73lN+fGSI3ci3O3FKxgEN/H\n7D1RTfMkqm6XxEiL3CYoS/KWCWR/d0uWnmj2bO/YAnH66dl46U6ov1lVl3i+\n19DtUmwalwUt4FC/l6LenRHqo7ALKT6VtCCmGoz+hSPb9L+cJlz211zKPOdS\nE8r/A5Yp0JkcVevb309F2E/1IuchgyB0UOgzWfxfToKrMY75mdCc0G1zpuY6\nQcuiMdHVwTCxGc+IoBDcx1iUDZrvUsYZvg72EuWJpDgbsgmGbbCbVzp4vVUZ\nOQKXTuCrtU4L4ltyD0mxqsZ6xQpVukDllI+F9C2VD2KgdcLgIOFc/yJZVgEu\nm25LhZ0TXsy3lWjJDogoQLo12fpbRl9BeVhXX/bJgZX5wg4pwUbogtucG7Bt\nXp8Oc8tVe0eVSysDtZDurvIylE9BFxKwHWGAXgHWaFS/w+z4D0xbO8Bx225W\nXi368jzYw6jNerFWUTOPgjrlI8XGb+xaThrF364j3FA9Vkdc+pg8ee1gzgks\n7Mpv8f+upFGo7+zqoJM72keVtmRzhN3mkUQN/eMxdVUDH3TljoPEAhQC0yfd\nkVeEbAsfzHPlpnlFVB64rIhpUIE7JFq0eszdapfnaEYXtqBrsYJhAFvoN/Mu\negls\r\n=+GFQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"execa": "^1.0.0", "throat": "^4.0.0", "@jest/types": "^24.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/execa": "^0.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_24.9.0_1565934954424_0.1383112765667336", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "jest-changed-files", "version": "25.0.0", "license": "MIT", "_id": "jest-changed-files@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8045a750a4adaa9cdf469aba230ce57b63c01601", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.0.0.tgz", "fileCount": 15, "integrity": "sha512-vNXbagXYy+HNBb5dqvZ0hvy0X7bIES90HYLW3tm9HBlTf1TQxqatDI3L51+ClVtR3bQkqszIOKagD8CzrBWYqA==", "signatures": [{"sig": "MEUCIA8XeXJsKfSMa/62et+BaCWdt5KE/1VcQECHToKsedufAiEAqDAE+P6e/1AfiLqrFsdFirQAG6HiLmS7FRj0HxDRasc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrKCRA9TVsSAnZWagAAF/wP/Rjne63pxAvASiNZ6Eu+\np27/RN+B3oK+NPWPYvVCP/Mjsn52Zig86tl0hx1ZHlknIhah2aj7KleY1QyB\nxtWQQyd3wDMy/xfqfD91ay3nrP5m2kf0LDdy456dFYT+Q6ikcqUc5mMx0VNv\n90cRwXjylVdI3yw1URR+Sm4HWLPxZ4jQO3/CQSoHDDuGuytDUEjF5QaF77JB\n2DxemsqTsMKDKk0aWmasXQ4Bw6CEWlqzsro8yiK8P1kha1hIKktcbsGUC7fR\nOV7Uvg9ds2HU/OcYJ/rqojFyJOsGy8i7k5BwQmflXeXynyHNPZWhHByKhpqH\nbxqNpB6EQvSN6n9G/AachKh0Y+klV45fVWrjhKTxkDBkcshFpqgxu5Hr2pLm\nGa29rC7kvqfcFiXJDRjJFaDVMo2vQPt3kq0LuvVqiFdx/WqQ00zRNSnMERlH\npAhYArjZtAdr5+fOMNOszPuV8c6K62oZsOtkBvYJjQIE1wKqlb3vK7Z5IXjs\ndjU0A9GTv/AzEzkZ5Wgtcx23WGxCBQyADJsWnMYFg77oveRqh3QHx9rhFx9+\nqv8Gq6V5GUMlx+WY0xDCIFZqhulx4orQjQGofOmhhwG0ph/lWw6HH7xUw90K\nbDvt902IRGQQhGz2VnMUO7MGqpN7ek+VuPZxnCROAnu1Qz78j4es3A1rtjKQ\niPpV\r\n=WuCI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"execa": "^2.0.4", "throat": "^5.0.0", "@jest/types": "^25.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.0.0_1566444233437_0.3709338058335261", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "jest-changed-files", "version": "25.1.0", "license": "MIT", "_id": "jest-changed-files@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "73dae9a7d9949fdfa5c278438ce8f2ff3ec78131", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.1.0.tgz", "fileCount": 15, "integrity": "sha512-bdL1aHjIVy3HaBO3eEQeemGttsq1BDlHgWcOjEOIAcga7OOEGWHD2WSu8HhL7I1F0mFFyci8VKU4tRNk+qtwDA==", "signatures": [{"sig": "MEYCIQCvvcB5DVsYlG3Vi4KTNkdAX6wTno7gRUC65J1Xwm467AIhAIdZEGPYyxbhGoiSR+/Zc5rfsZq/Z/doAy10R8swgxQi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56JCRA9TVsSAnZWagAAgVoQAJMJBZSSR94tJPEO8FD+\nDJt/YBGBnzwI7gmD08St1wKKY48cEKU1gTq2wxW/kcpsfhF6aR9V/cc1utMe\nYUbr312W8ZHQRIyeNb7+T2x3YOXGbGMoKeTqS+GQ/lrxkFEAkSMYhBCW7+M5\nw4sZlG6KkMKsrKI8I0yKkt9E2LvwsURgbe45kvkf4TDN8GRwSgW2yP1YV2WJ\nSIL/cMLB6CzSwRHKPkoW7lOWOa97i4tlg7WqHbuH1DCmcpEuY5+Ty6GNL0ho\nOvhICZ74867NKaae3pVf/A1K2wWCIoKRsfsKo1OZvblgrZ5CLNH+Kkrg79E7\npXjeJvu0xt63wq+P2rMlOOyOhxBF3Jkgwxwuwo1vc4xp30ffziQA+S+X1f5t\nparnDtE17puWSnIcLFcSw2d+Ttw6NCz4Kgl5kIDwsjAjcpBu42nqoBAS+hhG\ngLV8H1A4zTglIfUvfavHugM4uNDMIgOOY3IToP7kLHs8YTQuT3zlr5VLjaBP\nmnf2Ck06G2Pi5KE0pHFYYbwVbbtZCiDvcylwY6Or6wOEhX1POCfxLJ+Jx1WO\nHTVtEpIq9xbB69lpeMcjPT1iFBCnwX1pGbfjECblr0ij07qbdH9Q/PBupO/I\n84nZID3xYmb1wLYODhcMWUKEoushm+7RDBDLSBp9LF4yWJ+Yy/5W6NImUUPX\nkKco\r\n=J3q/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.1.0_1579654792910_0.8850920273043708", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "jest-changed-files", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "jest-changed-files@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e8299e61cea5f34cf91ba3393f7731820e68502a", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.2.0-alpha.86.tgz", "fileCount": 15, "integrity": "sha512-hFaGH+SRgi9vhSd/5PXFLAnRCVat+2b4iRSXfY9XydfqreCVwUGQn6r80byFM0URz7bjsVKALYuZs75ssVaSoQ==", "signatures": [{"sig": "MEYCIQC2akUfsMbH0pccHWuOVcSfy+zw4BKBEUxfrJ1kjv28BwIhAJ+0ERNfaTkwN5gyXUthBFa2KW81R4yxZppQAlfNg+my", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HnCRA9TVsSAnZWagAAXTAP/31ORCWuTmjt8X8nmVIE\nOx7UVdtkdZ1Vvgsp81yCrkwsW3BIprihhXwgJcI87HA7BN+CzjX7nbm+9AJm\nxYbV084jaAEg3TjszpqLgZIhjv9BB8QJhlvmf4TmR1NYkLPDg5CeYzy9Fu6X\n34gKYY/FjAJde6WiG8W0Fscn856jtMxeUemSV5Vc2tJ30AtAekmHVL6gPnUa\nL5mIgTb+LUxE3915huUwVgidQ7rcIjwwNyCLFaBR/XoIYvwxivsXxzzeFJe+\nkmtIFwDWTj/ebEAm+/GIxyulJuGR9vYAHLW5VVRR5yf2V1DB+dGTl81G0bgs\ng6VYpS4JIXnUV4wwCGQEpS+T8qhKCrxLtONv/98kZYDPld1FT8b6Yt3AaobK\nP0b46Gi5w2VzsYL9+bqOMoZozzMLGYJUVBkG8KI2xzFZ/rkRnEWM3LjugWMb\n+llXT3B3kmjxHx2a13sdi0bx6k0elh7MESoBZHykbUJpG2lilv9CQckEt0fA\nhF3MFISNNCmxr2mRgwFI6nO72bQZbP5znTkfNCM10jbxQPbPXKfSdd1GKZX9\npAK2/99TDK52Cc1FQ35BwoXDR6/iunV68cxcVkCo2Fb4nCMi9Z02zWwCOUD0\n3wFwHYWvOfL38d9zkiw1VUempwQkBMj9KKiVp8jHJtQMT//HyoLjLnNvmedT\nDaB/\r\n=/I1O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.2.0-alpha.86+cd98198c9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.2.0-alpha.86_1585156583319_0.23639559247782982", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "jest-changed-files", "version": "25.2.0", "license": "MIT", "_id": "jest-changed-files@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2d7ba9a389346d7e83cc10f95ca4cd05350de63", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.2.0.tgz", "fileCount": 15, "integrity": "sha512-4pKQ0Be43Glqptu3HvXL4Vk3vZnAWI/S7nfonVM8ZBECJ85UVs+MOQrXc+4E4j4zIZ7Hoj7puq2g9Hw0ysjc1g==", "signatures": [{"sig": "MEQCIF4Pr4GoiPxb6oArOQxlwHj7038329BYzonQ7XUCL4HDAiBJIdBX7EwSl3JVD8uWQhnP01MWtQPpSXp0x0cIkLwqrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5uyCRA9TVsSAnZWagAAC+MP/jIcIPsp2fCU3CzoFffy\nJ1x0ZxjPAxRXnmsE3NFiCNleGJIG5kbceyYI4Oh9OypEoe0FQJ2wjGlM1DsT\n3Wrt1f99f0DsjItH+xl6bGx7zeV+XkgqS4wacYVRrGRMV9QXVWMAUEY3uhdZ\ntzmQJYjB8vdjZYWSXu4UFTiISvtzoaq/GOYo3SlGc9q3PvT1is6JW7qsqH3m\nDOARAKiMg4ESg+ikTQK1cMp1Lgcia8jwEYrWG9BLNkAxMWbkXzlr9QuQgQw4\nDHAFfP3CrJH750nyPyNMj7HjWVYv+NeGXOyIGY2cuAPi56zqgxZOn0hhK6+0\nRvzFYfRFoRFI3GbCPrvWj0TUBvRRm2g1BqiFan6jE2FuHP7PI3l4wizpLbUz\npiys1xoZ3RZCrGtpugHhmoQsXPgyqjgPZ18AiJ94Ss0RwbG6wxi8qtnGjZoI\nswY8dd3mSBG13UxcffTp73sUY5PTbrt805PnOI/uiWlBy9mV5JzjZJ4sQH1z\nJhthudk3btxj+gQyeDNvyIRQX7yb1Pj+dtPcZ32X7l6LzpOEU1VZ2aFQ4f1T\nMkoNG6g3fQ8PJwYlT++TWu7q7xHCvY9t+QKQDJB+49Bwttb27g45DVgMqa51\nS2aZ+XORlk7pSd9urmc6OBpAmWRhnX6BgTqjloWvRlTwEVquIMcEsWKBCSxu\nCcU2\r\n=UIfm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.2.0_1585159082821_0.3634808638501208", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "jest-changed-files", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "jest-changed-files@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7917e1cbc628761a46af2561abfa772918be3ad7", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.2.1-alpha.1.tgz", "fileCount": 19, "integrity": "sha512-XowH/oBEx7jr4EhE/ax9+NKkyxlQZKyBbFFZaFPdohvUxysQYpnrwSlGnDAV/fsT+D7C8tK4EGkx+MtYQH8NMg==", "signatures": [{"sig": "MEQCICgk2DbSa5EW7dJ0wz30ChQn1/wiADyEqnlwT8o027n8AiB7ZH/OIHv6A8IrhBiiICijSbbgnZF7fZH2mN6xJVdc/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+vCRA9TVsSAnZWagAAvHgP/1jPAc/Gxl00luDy8HO2\nu5BMRLRPC/ukwqFca/9x2HKBYizLvfIOWuwFkc58YpQdvlYHljB9MBMNVQP0\n8tbKBEVX01fZFdK2u893WQsjcLRt2P895rnGmx77BJF2FeQvaIUrJ22jZkda\n5t6gqrOlyS+OymNrYhsrY0xsC2mQHo6T9F2XpcOz+eGW6eCz8BP9HfVxeg76\nkLgpmcRjPYqGTGWO/M2kfs+/xO1utn0+W5EP2UXTjjWxBT0GoHgApKylgyDn\ngx3I33dl8B5yVYLdJd1Mppsbr4+4U12t4KahLxi2BU9Uuv9IjGElj2Q6Xr0G\nej2sNnWOdaN2abLSPq2mz9WKfTAzZx43w5nCLoNQG63HxmgAWWLlcucCGbsD\n2dFvKg7YPvvzaZ8ODk2QTtwYxChAJIxSxYVr62FOvwiYcQJrHFYp/4EchxV7\nldy79JQ06jeWdLEkn6CScGif7aH0tdn06ZeEyu/DH/8oA6dtfAGf3XiZ87IP\nPc/KwPsvpANVc4HYs/ynmdDR4Bfz58Aj5RSFMoHxKicAPIbYPWT5mVEzdBus\nHRaNuQ06jszG1n0vwQE/WS+jrWEwV9kcXG6AibHcdqLb+zlFMbA4AoXS7arV\ncb7DeFzR+VLRz4WpWkJzVfvWhRvqsimh/CCMBuEaP269mAK/DYqTSgp/TukH\nReXK\r\n=g7Vy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.2.1-alpha.1+5cc2ccdac"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.2.1-alpha.1_1585209262979_0.9852883543505608", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "jest-changed-files", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "jest-changed-files@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ba78f2f5e85eeff1dcc902c3f3f674989365d160", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.2.1-alpha.2.tgz", "fileCount": 27, "integrity": "sha512-mZg/G9fVfTzFAiQr50deLNwIepN98dms9lwMEhi/uBCOrukxeSgOZ0GhUSwerujIWAlVb9ZlXnH5qshbtSMmTg==", "signatures": [{"sig": "MEUCIBgVUhlBZQOnVyfIxoBYWe6cd9x6eKEnv+R0jREOSgW3AiEAiXo4Ujnj9hy2x2rx+fEVNlFGhMhBkvyd/38K85tPFnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGN1CRA9TVsSAnZWagAAG60QAIaiDDCsOeyiMsk4EaKe\n56f1jXSKT96sH6hDxrApErkmsNwPbSCSQkWbYxYfk3iFuRSjREKz1vCL4emI\ny73JRcUxjFfLoQ3FCW3cOb6ABf7n0NTD6E7P9ghcTSHghZhQFayysC7lA9Zd\nLsKLllizYjpNJ7xdzhudgR0dOfJ+bV1l0cM0oAVltERdgStDKOb1IxxTp24A\nNnetuf9RHIbJ9GzE7JyzoWbc8LfuqJxagQaQ0jjMHvJZa1EH+lSuuL5E18rL\n3cHnesHceac0CBIHEnKhR1SCWP69VAdYO2tPSUt/HqXFVLFIl62gbpafH5xi\npf5uKmlvoIka+5npNT6mtcTlpObvl859RzgRDXEBVwG3SPd/V/gEsywmDtOz\nHZXwKSjnu8OHORrA9RaEzFt+WbgqLJ3bMJyRbvtYQC2bhQAdfP6k+8D6OCYS\ns2zqikupLUSIG8LoczZ8RgdtT70PNJhAl2NmVpWfc1QQVigbUW7kmFEus/wo\ndBJbFwedFd1MSEewey2cw90EAk8K3dZ4Sh9GuiX8QdiXM4ySUT7ZL3YxhIiz\ntVruAWCole2pG1JYl3HlHfNRiVDkAaJH1k4KHjbLeoxq3/TVanlwrz+b3zDq\n0qDw+HIdzBpxfZ0/xQDH48MxWvEF5i2IwaBSRmG3YsdmgrE6VdD8+93BWL0g\nwVfk\r\n=7ISB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.2.1-alpha.2+79b7ab67c"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.2.1-alpha.2_1585210229315_0.11289537775644831", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "jest-changed-files", "version": "25.2.1", "license": "MIT", "_id": "jest-changed-files@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2dca2c81980479c940addee863d8e73cc3a6b322", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.2.1.tgz", "fileCount": 19, "integrity": "sha512-BB4XjM/dJNUAUtchZ2yJq50VK8XXbmgvt1MUD6kzgzoyz9F0+1ZDQ1yNvLl6pfDwKrMBG9GBY1lzaIBO3JByMg==", "signatures": [{"sig": "MEUCIQD+jNNf/FfCZUBQb7M8mW9DPj0w5jjxVktkX9FreWb3lwIgG0p95R5d0VjxcNFmeizfjc+s57JoNKkhDJYF2WTyICU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9XCRA9TVsSAnZWagAAs08P/139vpcaqElk6/yAwbG0\nsAoBYuvdiQJS92ibF2fujPn2IcqzY51FKdboFONaqBM3VWV8poy+hUMSg2o3\nDqnsA94Cko8CCmvxl35+O/lsbkZXXC2Kvo9x5Ow2mKhuKArZ4aTgEC/Em5/k\n3dbh0UL+pRINeM+C36FRvWgh3xfgHoRUY4Z/epc+LdNwCz6MMTYyfNbp15Ap\ndgGBSeC0xw680pLTDHvlQbK2l2Lg+wBNUPYoCp7lLBx2Ew2fxlCG5LnWgPUi\n27ev40y6WR3W/eGi9lL4u04FG9mADrnZsNmSYcL8OBqtn/xrYlUwZ6uW/Qzi\nZpj56LQZIKDaODtNymv0OtPd4hapVOczVH+RBJ2uAfmDuVHm+4wNn0sie+F2\n5FsDCaI/dR4ezzyMA6Z0wnl85Z1zXayaf7q2xsJ7htFPcicEvghCW5wmyMao\nh15BVvwmrqFEr9ZEJNsfgWRa7JeaenA9lCMFkSv42VyOV5H2go6AXK6p4eGt\niPeSmdP27Keb8Qq/18ePUujL8qleEGuuaMGBOdKBWR3C2vHGGSowIZHP8Ppn\nsftf0LXzoMoyv1j+O6mZsBcGYbNVe+PmFCMPlXB+AHFfphGexIlcCfLZfUIh\npfTjd22ArCFA8IqBFulwXKks3Mbuet0Qxi25RoLfsJrWbOHFbLfj3ewMcqO7\nsa8g\r\n=A6Eb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.2.1_1585213271245_0.524777918712882", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "jest-changed-files", "version": "25.2.3", "license": "MIT", "_id": "jest-changed-files@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad19deef9e47ba37efb432d2c9a67dfd97cc78af", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.2.3.tgz", "fileCount": 19, "integrity": "sha512-EFxy94dvvbqRB36ezIPLKJ4fDIC+jAdNs8i8uTwFpaXd6H3LVc3ova1lNS4ZPWk09OCR2vq5kSdSQgar7zMORg==", "signatures": [{"sig": "MEUCIQCD0o5qd+anCDAKhi6a+Q9evI0oJ2TXKUfzCrn7S8hPtQIgV6mAPeLeTzeRsWw2pHDIDNV1eAwg+0MNRrAwA+/LHBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+QCRA9TVsSAnZWagAAd4MP/i4+AT2pbZSTctKbW00C\n4eN/r8VLgJRdkXlJrTDwaBpZGp2yFurhXqkzEKg1HltpVdIzwgWNfscYYK4g\nxnKHrWZ/KrYf1kkutP2BvUbLZ9nY2298nKN0iGHpfeb4sFHpnQG9rRqtarZg\nPU6W4h7daFeqPI7+GDQRt85xXRDH9PTgSXG+xT0yNQq2mt4+GJxdfSCyxuZE\nRcA7KuyiEdjfRqfOhDTwh/2rbevLDPGeBC8NyW2nP8FFSmM54OIxRnfV0uqy\nuIkr7H18DYXovAwWmoPTRNf00o9um85nLUrWjdWD4yLT2C0syoqk/NAdtJvn\nt8NUwhpXG3xi/Tqhq/XOCqi8a/1QivcJ+liec26xgcSvtQIL0tIuWQDb7kTR\nKdnwUII2RhFKdm4HV5gIl5jupRBqyGWPKT4JyHyns93dm6k9CAO7tHxWEIGa\nOP0K+tyvyubyul3JqY/f/W6zVFhjqdbCJ5O4q3PGl0WswWcELVGcokFtMAX1\nEWJHYSlKfktEum3pAaEeK0GDDWEggnAFz/GddSLkDl9IcpzP32eKuvXH0vv+\nrsxXvoszN0lCexC3XwTArDalLOtbJWqWocdXj8NlVwteVyVYCamPrcubLI8V\nkdrgF14/3dASuECFsEALBz3PNVIeXJFdNR5yBnUctwVSY/lHWlrhcFEHymWl\naFC5\r\n=F53m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.2.3_1585254288283_0.8140100973682709", "host": "s3://npm-registry-packages"}}, "25.2.5": {"name": "jest-changed-files", "version": "25.2.5", "license": "MIT", "_id": "jest-changed-files@25.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "680dbd4749dd26d93bbeef98df1ac8186d36961f", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.2.5.tgz", "fileCount": 19, "integrity": "sha512-GbNNMMLB8HAGmhxotQgrR7CnFTovnuGtkkvIXO/sMajYkSkXUxsQqFplzP5p6v7HDI7q8d3yLi+LFE/6gsixmw==", "signatures": [{"sig": "MEQCICrtBgt5t6pJp2RiMY2YvF2ghxZWiGfPfZ1FdWKbyWlHAiAC/inAQUBog1qZFUJjzFALhgQbezYVoDPnJhHsHxGwrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb0ICRA9TVsSAnZWagAAESYP/3BXojeWRg0DBovsnLz6\npLcdqBdfpFy2rIhdVm+g0TMtzZ1ncvspsnZ6EU6ZDWt7Qiyo7yIuEK7Dq6lp\nSe6scJhtc8iFIb+I19C19cv2rOfypXWLx2pMcHOR9OviuB7JfmPww9wNK3/s\n2jpNAz6knUxFt3fTXMiQF/8Q2lJ2afwvlxA7izr0Z4zhYtGiQ0HpGloZzU6U\nAeA+i5xRtTJ3Pn16lxmqw+80LbJuZQC3s+i0KEYikA5ztGu8UCKSpL2sAAic\ncjYvOf9KAwwBR5e0IUOx2HlzIR2HEyLXxR3oOx8yD0OiigVbNempxGh+5jIp\na4mjD9AwbZrWP+ym+I6DbC8QOookE050ti4bqCYlsXdAG5YjaHEfzoSoFtkN\nYGmbGvbbJ8R3Q+bhVK7IzSnOggpUjIA9W40p9x6ePfgU+o8qe2fYoxgTmqbL\nFuvcAhuwhKdOB7KZWk7O+5rLGvEru+PQp+3dxcnjyNqAXwFjSgqyaVrnUXRo\nX3Why7nWZsBld8Q76iIebZWv834E1zcZwjtDl1juaQDPEYQ9msMM49gZLLEi\nJSP5fgR3vVvaDXreu4cS8LGd0roX80xc7Cw9LM2UBZvmLveuP49HdmHny45c\nxGK7SZf0dUpdbIuThEiMns4Fs0P5UbJ9jwHYaq9QSbcRJCEzt3ljhMaFiPMK\nS4dn\r\n=edZ+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "964ec0ea0754caa2d8bef16dc89c1f926971f5eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "deprecated": "Faulty publish, please use 25.2.6 or newer", "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.2.5"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.2.5_1585822984324_0.3156377821942531", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "jest-changed-files", "version": "25.2.6", "license": "MIT", "_id": "jest-changed-files@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7d569cd6b265b1a84db3914db345d9c452f26b71", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.2.6.tgz", "fileCount": 19, "integrity": "sha512-F7l2m5n55jFnJj4ItB9XbAlgO+6umgvz/mdK76BfTd2NGkvGf9x96hUXP/15a1K0k14QtVOoutwpRKl360msvg==", "signatures": [{"sig": "MEYCIQCY0cycXWVwMdQHNJGR3ZEQmWq+tNB5AILiZErYUub75gIhAMLDwiY4WxsSnNQClGtuxMrQgkmZqwyyoxIIxH3zYKb4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb56CRA9TVsSAnZWagAA6n0P/2F8BXD2kaEFARTAJnmb\n+3zh3wP7wpSsifaqZd3GGLu1H1t3MoL++6bAWEyHRaDsyDrPEF00Hi/3hYPA\nudwnt8v0KiKGJDNt98N+SSwmmFTzOka+ekR+wybc4NgcZo+WusyLN05gZ7YF\nqPIpSBVU/1DEY+X9Pj4IAUJPHwcdLLwOuWJFKGe/yEFxUFt5QGTgVmUrEkdQ\nVHqMqDqVDgCZ5VOwva5nzrQL6kH1M32m5H6NfU06QC1o81xmB8i2cLPegLYD\n+0faHD3Juz6q33/fGwZ9emZlQoFIowJZtjM2JrQdnRlCQ7KmAR34GHmUL+6/\n/klmlQ5VKdoPuJrv2qsxApOe5JO1OOcwWzE4ElMQoFnt4qVT4c5apayvOX/s\nrwBUbhtVP907IsJWGyfaDTcag4vVp1xOlPjlzrl6+R3Fbe7QAE6j0En74La/\nQU5u4ysPE7y0a4ccuOOiVGuZCEh3HL09+cEBpOm2uon7O0uRieocnx1otlul\ng5n3xwDtwGyfn9OM0045CWGxrGDHjjdfdQuI9kKQ+34p1UPqAiQqEFFEcyWt\nZ0qDngeVts2nneDZGMAy2jeauoz1ahS/ROp2pqX0A57vpVZpUdFGPzyCajgB\nAxxTJhV4KgZRpHMOzrAW+vy2sKwJiyjhQT6M4AwP5gjxxjohsNzP27rDFIZq\nce4y\r\n=KRd9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.2.6_1585823354179_0.059315573007996836", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "jest-changed-files", "version": "25.3.0", "license": "MIT", "_id": "jest-changed-files@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "85d8de6f4bd13dafda9d7f1e3f2565fc0e183c78", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.3.0.tgz", "fileCount": 19, "integrity": "sha512-eqd5hyLbUjIVvLlJ3vQ/MoPxsxfESVXG9gvU19XXjKzxr+dXmZIqCXiY0OiYaibwlHZBJl2Vebkc0ADEMzCXew==", "signatures": [{"sig": "MEUCIQDmFb9BBXg9citB9NKrEGSGt7SfMhdqAPcY/TGGfHPe3wIgDCFBckDlxfxadr7n6t8e5qJBVF5xOQb7qZEPyLG13jU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/HCRA9TVsSAnZWagAAO3IP/jratcSWroTJKJNMCfts\nSewqnxEiZAqGLauF2rJ3XpdNi5y8p6JafZHJKW095aP2eTPGR2ms/+2yo7q1\ni4Tgx0ft45RguIGqMBk8JO+GzuWVfhzg+vqInvmWTE+Mq5dzCzJ7tZcVVkkH\nYenRtfFngmGGmUXWztKnbxdcp1VHDhBA8rSx1lEKS6utdobOJRt638r8B1OA\nYSWY0Ke9aKe6Bd9bnvgmXLTPHSiApeEOd5njGkbGMhLD1JIo5+lwvyIzHf7v\nKgbF1lcP0v0JIPYTHkwiaFj/z9fEtPp7KwmqO6B4M/SYf1zvoZ07AAoFdWcf\nYx4waRSndJnjaC36eg7MOF08BTioEypSpXtbd+NE/mLSDzi2eCRb8BmvJU6T\nYuicCg3staL3duQLFHS6oNTMfoH/Ht5AnWCmzDJ5Wmsh0Y4nWKnmgSkiXfSA\n7IVmBMZ8OF0b4RYaKQFU57H9ZG1y2SfQ9BOhoQnhLHXevzFAJ4zh39eqhaDn\n45bY02NDBqhYNAaSOmNaw0p7JQPbWugyqeuMf/jQGSVzXMgB7jlBCP7vjhvl\nLmZgdhsHC2stv+DfEm+rLT+Te6HZ+cwSjr/LFuDlr2niruZr9BMhJ5HvTAVQ\nkPOVfFexHe++hkzjUuc0Rb2LrPFrdRtkmNv0PB5laN8gK+ed3F+nNkn8Spfq\n6WFw\r\n=lCTp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.3.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.3.0_1586352071020_0.4112968605386562", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "jest-changed-files", "version": "25.4.0", "license": "MIT", "_id": "jest-changed-files@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e573db32c2fd47d2b90357ea2eda0622c5c5cbd6", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.4.0.tgz", "fileCount": 15, "integrity": "sha512-VR/rfJsEs4BVMkwOTuStRyS630fidFVekdw/lBaBQjx9KK3VZFOZ2c0fsom2fRp8pMCrCTP6LGna00o/DXGlqA==", "signatures": [{"sig": "MEYCIQC+ScYpMS7CY/YarzH8XpwK9ZXzK4APxeIGxymKhiG6xwIhAOEt0dFO84vFq90udCpw0L24shZDbd7pETN0QFbPdJQc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMehCRA9TVsSAnZWagAAA8AP/0L0APbSkiqSrkmfvPvG\nNsnYKHFvf02Xs/n8dJQsIqGtzvFkWnoLOOU/asNQKA3eGVBt0Ym7i1B8aBpD\nOP5CFfECDtun2NIARs3vagNorekXejmQK4XMyvGg8ouSYpJD0iEKBfZNon3p\n8xGOg2nZ5cIz382KMInUI8nLBN/EQQZ1CCqzuHxZ2kPJvwuxy1OQgs+n3aTv\nEKpe8fNfZ/OoL8s3P7Rdii/RvxlJymYCSK4TAtvh5cpV17k+CmkXXBZGtJ4Z\noriq/7+pQ2DKJA0tAjQrHRf4CRjapTJAkB2NmbAMz4LtCLwtOAZwPVcYz0Jo\nSo6FBYgUUgNmBeYarOoTdjoT2hsVrnTJoPHf9qJhnMIg8mDU4BsUdjCTRRqb\nrFmc/pN7eUhkIL6AS/GtfwjfwB1ixSuVWFYPK2e/WOWhQMPUgfhEGIGgp5Bv\n2rb/OowOL2EoKAg3MMosl+qAnTZvMu6lpOQ+qDoMkeyw35vzc71HCbPOKdtE\njvPIdKN0bKd5IamsEj6R5PzQILvGHJnbIB/+7SmqR0eC8Z3TFI1QgBNJ7fiG\nfIu8pUIvmLQj1il1a4QZaDlcr3yi7w4BD8vofvMbgf4/aPQg1GGGgN+pzszT\nadcPpLzh4tCgefkRmUPSD7A2PxMwwOCvyEYkWFM1t7YiOmQSqs4BxrtJ/j00\nb5gP\r\n=vncV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.4.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.4.0_1587333024539_0.34914995787915726", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "jest-changed-files", "version": "25.5.0", "license": "MIT", "_id": "jest-changed-files@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "141cc23567ceb3f534526f8614ba39421383634c", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-25.5.0.tgz", "fileCount": 15, "integrity": "sha512-EOw9QEqapsDT7mKF162m8HFzRPbmP8qJQny6ldVOdOVBz3ACgPm/1nAn5fPQ/NDaYhX/AHkrGwwkCncpAVSXcw==", "signatures": [{"sig": "MEYCIQC20x4ETUnNf/EGuFf4gHjgPEg0f0UZRrFMhJcRVLyxrAIhAL/JcPaU/ooeJB3Apf9pq/anHXVGDWsIK5eAd2h3+3zL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfRCRA9TVsSAnZWagAAUXgP/RpghwGzg047xUQyrGpG\nRpUSMt5bbY2muwXV40W6TRF33uK3wyK/0j4ZEjmIht6u+RdtqElF4ZER72B1\n+O5exmzdYuTRbtr2lE2gj4H+eAKG6jiow03XqTzY1ZW1AxcxsHTZWW0I4UUB\nNsBQ3u5HPqGHDT7uK9ej0+h5jJX5AN5N5OGIUoIVF5G4rctYCSvkJv5Vqqu9\nsvJKJG4G0P5V8P/r+ylOhxiTYSoAeLKieZ7sXRAb65YjCG9cNq50pyagZlgD\nAxJMQDTZX71erSN14Y4GkTwGUAOpW4zUKD1dSmfv5bVRF6l+vc31hi7jSXM/\nLwJ/XkiW6fkKRt948I/N7E1iCXe8PbR4pGdAD0MD1kmN6PgMcd506PijbdQs\naFZzkrfowIlHqVwGzzsF1WEpmSeQkCcRDcy9KMkAf2G1M4X6Hr9QKT06ub1Z\nSZWCIDCv0iJgAOMJPjebucdramkMdBYfdcwLxCtRmjq8FBTkvW3aGOPNrlaA\nKG3AAZJSoJCpnlT8p1AAZUfs4JorR1Eg1DqCD9KVKdTk+jobCkT4sPnEWQJ3\n/o285N5KioMQunE5O4aarmVUN4AjjjFStdVml0izeWp9+OqzVnduKnD/Y3kh\nQ8ObN4AfCCmuzHWVg6DEzGJIsWA5xLMRyXNCr8xmegBvMM/XYSLo1YCA2Bbh\nPXDk\r\n=a4HI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_25.5.0_1588103121143_0.6382119910688255", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "jest-changed-files", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "jest-changed-files@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2bbe5ce3396486c61235d16b003752770cb6b5e7", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.0.0-alpha.0.tgz", "fileCount": 11, "integrity": "sha512-qRMR0awxfE+NJFSV45sEAwc+3c21xmLPF0cNq4E0MjTt0SnOMPqxFcm3PVimIogptfM3PXd4jlFhmNYnMAdjig==", "signatures": [{"sig": "MEYCIQCkETEyLDEYH4Fs7/QfBOd1eJAP/LzJLW2hiwNQPWB/nQIhAIR+VIb1zGA3ShjgSVB2NJc1webC4YNm/a+Ymy5j5Gj3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPMCRA9TVsSAnZWagAAJ+8QAJKT+2m5w8bBEpCOxQzP\nNaMYBFyK/ESlQudCrD8qE3I3PQU+jNW2Qwi9maKOYA6a9IAk63Z0u9ZhrzKR\nVzY1w5EmQdMHgSwTKvPmJKYLaUwQv19IRFPixnyKcrx2CyuVMS0cS0kbIong\nmbbfiNjccZiCPUm7EGjE1TeTmwfrIv/xVl742UhYi54XJFQgs8xhTZMhtfDi\nzcXzxv99N8vkzLoM+KOXNWq++5B1Up96PlaXXO0er65IXBX9vGQHrKSvRb87\nYw1qwiCWSMwbYtBcyLV7Qw29Lzmre8QMhk5fQ4/+Axz0BVTQVuSY5V/327hi\njjcQk8JauZS7wxL/o7QpUggKERqNDJ1BH5rSae832suhZW5DLhRJUr++9WDR\nyCXEsaOpBy7CQsFa5J9ajfvgF9OBVPra1JHq0U+OBrUL9pvhlDa/6HnIKo8H\n9zhjLoKSeb0QjJquAmG1RaRNRp66IPZtTRFtf84tEuDOzB5Kwtjlhi/JYzbJ\ntQuCIMfqikdPvXifOhuIsLIl3a9gs8mrUPIjMUMu8d7Jl5qMxaD4gLiSqpUq\nvCI6m42xuEE0rvG5148Jxsgnu6LSI8PrA47pPXp6QweRc6kUnU7ywwPbGiiq\nTk0qIqBPACIxydpn8NKnXuqoRX60CXdi/gvDv1rQHLWbVQt+cTckjvv5vlyw\nF2Ew\r\n=rLS8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"execa": "^3.2.0", "throat": "^5.0.0", "@jest/types": "^26.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.0.0-alpha.0_1588421579956_0.5229878570018467", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "jest-changed-files", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "jest-changed-files@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2dd9b7973b11928558ce50f6d94bc66b31264c75", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.0.0-alpha.1.tgz", "fileCount": 11, "integrity": "sha512-5uW5Mjl0WhknfKIs0rkwr7bfsVYrHHmTAwF8qZXimAO112BIHHn7gRWRgW1Iq9w5GDfiXHbDk/rtvcEnsKoYbQ==", "signatures": [{"sig": "MEUCIQCy3tT20ACjLYUAFkt+gQ5DTYGMniUgdyD6GZXL6gBFDwIgUjJIfgb18Ah5pMjU/VVJzJt9l06j4rsbrJa3sn5My0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHgCRA9TVsSAnZWagAAfj8QAIplLejfkrWhZyQ4z/9m\nAWgEYqGT6d7MmqtVRGx57fj/SvmOfX0NEeyqgNKIv5KftobKC0Qgx81QvKXb\nSD8d3PFgr3KJtgCa7ShIwTvgVcriHkE2VkUHxTWaVfTF5TOeFV7krJkGWQ+i\nRhB1cDq1p3ULGBTk7w+wczmiWDMHf3kEu/DZ/xIfLsFASFnN6qiIAqgLBbbM\nTi1Zm6DkK0c+oqbJNGsu5UFAxv8MhAjH2HcgdLkkdSB/hLxnL/+IVNGIt3/k\naxtGXvLDlP6q8BnLuhe8bBX+Whq/egH7erSCCA4CsOg0jCreeoyRsho9fkOm\nVcQD0XzhxKOYYeHShNeCQ0uZ4lkFwBWWptiTut7gJ+IMsfWyvFnfOYVpFnRL\nlPcY3lz+PY3SKSiZ6c745cHHIA0lxO+znUdmN0ZJum3bw8G+vhAnygzODU5R\nNkxIgMymjO0iUpIFqdluOF3kEODIGV5RBnjUBpB0DTEB95SstagjAJDxfg9F\nd1LSkjgQOUgTn3ePk4PDFdfNf3E9k0fc9FZOfC1LXwFWB5X4PrgHS54FBxkW\nIAheDCafG4mjJBCqULgXcyPCLfdKln5V5uHmpnLuUVFpOf+Ow0d8DHejEdt5\n8v/fxHIc3+VH8TGYfYfV6vDt9CniEXGbC7EVDC2jali7iCeIZ6AWraJa919Z\niy5S\r\n=GJ+R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.0.0-alpha.1_1588531680382_0.30328083310743104", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "jest-changed-files", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "jest-changed-files@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f65588ede16c784d72b53081b495b69f62c775b4", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.0.0-alpha.2.tgz", "fileCount": 11, "integrity": "sha512-BmeFJ+e6T/EBFjtEPnVMUxhRCvuXpdAO4gYsxD+rq6dEEsxGz6vZGkKkBGpf193teuIZrglzZJOZSGs8W1l/dw==", "signatures": [{"sig": "MEUCICdcku0tIpTFtVMYtOvaN6toow5jlYX8dHVIyIC73tDsAiEAm442t8I3+5wltDC8uNM0Qxxbo4vvZK4rGqiP/F2W5GE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1ECRA9TVsSAnZWagAAlYkQAIQd69OiNTnXui18b9eJ\nqzYNNnXEZNM+OkgF61h/LfHy3K3PNIzHoMX849inkoTg9wk5yr+CsnTU3Oxn\n8pnMEZMV2jNtELiCtdpfFE5N3XhTeEV81kjn6tkybgEZfoBc8z+qdxjxWbrL\nudqjOUweToId00Pr+yJrPvwnaDlSueE23JX1mu314HcfBM1ioQh22CVYUmsF\nAFY1QqfwyMRUfGw6HAnLdPFCCLD7Q6RevqqN2sutDGK/9dPs6BhFlvnf0ZDL\nTCBGsiVIO4v2Aa855WlyIlr1avcqtfwjgSkLNAl3G1Yx9J6a3uG24LDIrkmM\nqw5TgTXHxIXQFeV0CZ+n84sV1ql6EdncoxBbmI8T2f/nCfykbLH9IXDslVQt\npFbYp0VtCizUI9cmiAl+FbUxYkMRZv73uqmtq4dO8HIQ5NjEYZOoW1en03gz\n54x1IcNnSCphEYsgkSH90Q6tA7PatfQ3U8DGpgA7XsaU4fuf3Xk2UtKNazYA\njtg7ymupdnrLNumhnnvIKcjRMv9K+JscoAylku5jQeixWbA+1ZZp1+HpGUIV\nfZcVNcAT7RCRsBlxRzQ7fV/IoZYrYwwV4u9ZVx/FACXc0l3W3/TmaxLToMdP\nooRDYr2EwdJPw3NvdlCM6GUrNrmUN+o3RXJSnXMOsUSB+eFE+q0vLzGKDxqZ\nFO6m\r\n=qt0p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.0.0-alpha.2_1588608324217_0.9377388402789055", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "jest-changed-files", "version": "26.0.0", "license": "MIT", "_id": "jest-changed-files@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "de561be392a2fec526db8edb270fdd071fa20175", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.0.0.tgz", "fileCount": 11, "integrity": "sha512-0fu24D0YHpYRVkqQA7qCUSNqI5i3LUH0joRHXCbj0gv/n0UTjoV7vMLp6H6T2ZYxOSSdMbBwVv985kRuRrJzTw==", "signatures": [{"sig": "MEUCIGEqGbOBxEYAA4Q5tP2uIln5sFOSd1YvMFKxRADrL3w7AiEAn13D33d29xGb0CLQwUPImnN7Uzi6AlZ4ZnpK2e+iMSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaACRA9TVsSAnZWagAATvgP/2C7bkKXUG++Lyb6o2GK\n8HHZAApF3IIuP68YqKX3imHjBd6ghjSr+B10PdBOFOfb2rN5W1YgMN4uVAzi\nIpnhCV6iII7x/hhnnB9+LbewcC9Hcz4swogCCxLwX3wk1WftnxMN0/BhefPR\nyJB8JBE2I/ZpivjHWjTX4U9cJl1zS0YEbn9SijF8syfLBOciATDqRhRzZ5rW\nF3v4fTF1pdUTx/2vsxQvur0SlPnnEH0FE1NzqL1thNsTOD9Eb2UxO3mehJ/C\nHpOez99qYMkMBDFUISKSDHZBcOy2n7ClT7OnyYy6kOtqgf78RlYBhL0/L/6I\nblZqPz8//a5iMN3ayk/bctpsH4M2KrTKV//UkWycr0MxUOhWfXGYz34RcEHY\ne27FIOIXfpNF/xhpaP74KZUSvJHtRhYgjQ4vwQVNjbDbxazLoffB2HrtwzFa\nBbYHJlUsMmeB2ifQShMJV93xqsGO3aQw0bztON4i01YdhklsDOcur4YeZ3lf\noOaJr98k94uwcTN+IWYvrlOmSr+OOCYFbBdPl1kJbcbM0k6lEr00fLYlLSmg\no+vIT3S0KrqRFFxydg7511jVOWgmywLf9vmp2os0FuO/lyio31u8AEyLdb0a\nh1dSLvp+qttD47nb8L3l6hy3rHrBjn2AFg2RQhyHuW9JYmUnu7rkBY7xFm4J\nW6uY\r\n=QTn2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.0.0_1588614784476_0.12728024024965046", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "jest-changed-files", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "jest-changed-files@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e49f1583844fbeafbb67c80c142cf37c43190162", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.0.1-alpha.0.tgz", "fileCount": 11, "integrity": "sha512-0NcMzj2X/UjBK4uIZNmtpZt7ZZZ5hiZQsyQJkElLUoF7Pp4zW1aeCIZcbz3OHb7zl8wVEudwp5oZPLTy9/glSA==", "signatures": [{"sig": "MEQCIGz8f6GG7LcXj+AMwEIzID2fX3DxfhUQ/ZpqZkolOa+WAiB4tnHtqT6RL0yE9qUS5AMCE2maXR7hZM5IZAEeSO2iJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQdCRA9TVsSAnZWagAAaKMP/2Clu/xDtAqBHIlvZR1M\ngjHQ6DQMHfbmhhJzsPhZuQOGvGVsNVzZ3J5Gzv2C4SJTG26cRnRTt+N+9uKB\nPGWlFc9No9W2vO+wVyha0gdVbHBGnsHJGeFq3lKRNDiqeb2hRTMuNHcNlMWA\nN+yYkSzGyTqhKQKp1h+vbB0ksV/j0vfSKaQlWs7DoYeC3f3/wNlR2VzHhn96\nNTCWm1oc7ZzQLaGC3t7htZDBU4I7Qc2fpIqMQRxZA9b3hujzaF3YZSIBPF/9\nBPogOYAgemM3Vq2jylaamHSnsXlLVK/UzewzWdyoNR3EP43Y7wo5pLnHnlmd\nPJmNDIgccN6wd24kYcrA5fEqjsVHp6z2K3P6Apt04C2bp/iK0BsrKQQqel8y\n8ESWAAnXx1yJjVEkzYuiRXZtQeIzUKwzLcjsgBtLJznPh7JNwB4shj+h61TL\nU4wAeqtkxT45gKOgRe7TKBxUIpJctsj0wLt+Zkqtymwk+0wNnt1vgIfpHTFV\niiAYaGcviNGCQ3Bc3Yc76liw+KKwhtmfJ83/i5Kufl4PXhDdmNDJYGcJKA9F\nX1k+PBI9AbRpFdv+zUSxLFsX8f2tHj111VtQUPk1+a6M/GYGtScD84YqDZ5g\ny1wDjxzQgeV5ZRLXbrSIhQ5ct+tJhhsYYCTMEeotOMRym6srOAIWj7jnLQMD\ngPoR\r\n=x+Ab\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.0.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.0.1-alpha.0_1588630556675_0.9485938550957638", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "jest-changed-files", "version": "26.0.1", "license": "MIT", "_id": "jest-changed-files@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1334630c6a1ad75784120f39c3aa9278e59f349f", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.0.1.tgz", "fileCount": 11, "integrity": "sha512-q8LP9Sint17HaE2LjxQXL+oYWW/WeeXMPE2+Op9X3mY8IEGFVc14xRxFjUuXUbcPAlDLhtWdIEt59GdQbn76Hw==", "signatures": [{"sig": "MEYCIQDG5yOs3w28yz4U1Z5m/OVMsvnzKuy7tkTm4EgiqDBvWwIhAO4KjhdcIfR7rkt1RlhotChZUFWWnKk1ABIweFf7ELL5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesUKsCRA9TVsSAnZWagAAGSMP/16xkgUP4KGw75ybD1FC\n0cdNSrolLJsw9uyVzn4Z477hbw6FytrKJUiPwRaRgANwBvwYMoOL/b1Yn20S\nMyCZPQfb5IE5RczKUISmtGzoR1R27lCv43nGLXps5PQmgvdBu6sZ/wh6y6oO\n7uLKCPAao7PptiW8KQpw/oGgjXUmETYDEN8uf9yrPFMqeAYEsmLysluU0UPR\nalPHEeuF81J7OYuqZhWXG6Am4c1HcAlyOFIy8bdosI7pZ5QJFFMCayKa9dyq\nOPdYjrq+jSzvXAAcpzp2P+BDgZdvLgEQ+my25m9RA5u6j2nft2dbBa9sGGCE\nINChk2bNe/EMXT6Bg4VWI4WW1EaRrjZTzd9EtPaKfFdiFeb5JWQ6QZXdCBIb\njW0WKdt8QMT2jvX0+7RzSPxC7siEELOMO4CkIxgYHmG5hq+QDJU+KHhGLNCa\n/B3lEAbveqSprlqOf4rfO4PqJipVJefgpEhtg9J9hq9PqZcy6WfB6LASudFo\n85ZJcTzK82jwhS75qSUR1vcCqhXl0+w7RN9mINuzask12tXgZpWUlHykIIzd\n+ftVnPmdyR5fq5Te3lLhMHhj8wFdfRaci6cDXKG7ss1D/d5AuSchK27V8m1e\nAn6hgXXBsqj5U6A98jlI0x+GCgV/VMiJ17zmulK+O6vuHoK5s+lrEpqyPOmk\nnZY0\r\n=bbd/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.0.1_1588675244197_0.8600431565160334", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "jest-changed-files", "version": "26.1.0", "license": "MIT", "_id": "jest-changed-files@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "de66b0f30453bca2aff98e9400f75905da495305", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.1.0.tgz", "fileCount": 11, "integrity": "sha512-HS5MIJp3B8t0NRKGMCZkcDUZo36mVRvrDETl81aqljT1S9tqiHRSpyoOvWg9ZilzZG9TDisDNaN1IXm54fLRZw==", "signatures": [{"sig": "MEUCIQCfXM/aFOLBdfKRi5SFXRtCTIT2cGUbiT2/C5Aq0MaJDQIgPe6vHNJFYEOK4BKddKwGgDUgOQJhk4TG5H8Dk/Ieqfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hx+CRA9TVsSAnZWagAAOpMQAIrcEbyGC+KUVCIIS56e\noCtrpOMN3UC64ejtkeAuhhJT3i4JHS24P4Wg8YQIJyRlzpeTljqc7SKXhMjH\ny27nDZ3apR3SxE4BEUt6/yDNw68TKNbYg6bUOULE9JzfcWwo1m5BWHV3hOx/\nQKBcuGKHneggNMfVy90V6eYA3707fSGtHtpYmZGyHtCrV4aCva7ijgMe1vjW\npiTtVZuTZLpI0MTzBnilpVX4omjjMcE284Qrz49PggcpcXUJnSLO1OWVasFC\nIrEmGOJP1yRKh6UelGKsbiW5+F+B7w+N90oZkyHAv1aU/wm9wHzNy1vJ3oik\nUw1JX0FQYXK358QlTq3Q2XvTzbTX9k0sfQWjwhr3GCwY+RH87dSvaX596mJ6\nApMEWdt6r7TSMewaq3fuLUkXNgsqc/ZvFbVKgsRH3FoqCUIqYpLs6OFGj/dd\ngYmaHsHNR2F6q8QeoLB8sKOOBEvE2ZG1nC+BUGbdP5DzLx3/Uz6dBoODTwUr\nWQcuHEiBJOBI4fGNI6WiVY0JvLCaWsBtlHKXQ2GJjoYP8JbHMAqrxGuAC6sj\nRNqXxKjB1AHvmnvu0SdHjc4m+6zLUpjehSQIzQxTNfQh1WIfC/qTr/ZY3i6M\nJtu/VcoLOSBY7dpfnQqTLLPBDcKIgo/0KeSQHLw41uOzQgQ4qsU6zI7tRUw1\nsE8q\r\n=6OJN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.1.0_1592925310416_0.38804237104443917", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "jest-changed-files", "version": "26.2.0", "license": "MIT", "_id": "jest-changed-files@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b4946201defe0c919a2f3d601e9f98cb21dacc15", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.2.0.tgz", "fileCount": 11, "integrity": "sha512-+RyJb+F1K/XBLIYiL449vo5D+CvlHv29QveJUWNPXuUicyZcq+tf1wNxmmFeRvAU1+TzhwqczSjxnCCFt7+8iA==", "signatures": [{"sig": "MEUCIQC0AEw2FAlMxQbmBlQ4PniiP3RXPsZO/wjL0sGsPRSSWgIgCxHomAChZY3y+kAa6jfYz1RhTNJB10yONXBs9kDVQ9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzeCRA9TVsSAnZWagAACqEP/A2/yof6jld3hc3GPsjU\nnSHPOu0knMlCaj1qpdWANoQXkMxI8y019QkOFDykHiHPUvP7tCAKcLBTtZPv\nlhYGLO+OrG/SBBOQLjRQ1IqQmwaHIzz4ELKQXMejRjIbKM6Gh8uoOuM0FVts\n2wAh3fAlU8qlB/fRggFpBNRyqaQdJR4YpHjkyspfhdWEpMK1AlWc3vrWYlZC\nu5WvKcUuaAcr7AXok52lv67RqtEed844hClFqeWsm6Go9IF0H7TBs7JwkK7S\nyrWAFsXApv+MAtSM5dRvjUyDWJEqhhy6AaZFCWySF6DPVcAYxijtCuUu4CgU\naWGsfYv0MP1tdVw693d8BRXVaBpEozog2iOg4WcxIplxte25fyIEzVySmoIQ\nXf7sPtltcE3gWIjKLbqMAy1OXQA5GTcFlP3EjEoE8qNz+lBn9juzEarYfg+d\nGdXTEXSc8oUxIy2N8+x7g+KModTdkVO2m42V+7RTuoCY2mNLH7OawkKQpQYu\nZv9RR4Gk1SMP2iLuaYPxAtSnHq9/QG4/TGVHbet2XTF3xn/x70R2dukFGeZy\ngBuJ7r/M0a/p4+YFk7lpUaPOojBPZsDlOkSy0UJoKuCYJRVbFuAqrl4fU3Xb\nwni8P7XaJs7lEfa4tmNV25564qwSw9F4ZZWkwdRMRK44plE6J2hrEDWrd1HO\nOVda\r\n=OWV9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.2.0_1596103901894_0.015457417788679928", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "jest-changed-files", "version": "26.3.0", "license": "MIT", "_id": "jest-changed-files@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "68fb2a7eb125f50839dab1f5a17db3607fe195b1", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.3.0.tgz", "fileCount": 11, "integrity": "sha512-1C4R4nijgPltX6fugKxM4oQ18zimS7LqQ+zTTY8lMCMFPrxqBFb7KJH0Z2fRQJvw2Slbaipsqq7s1mgX5Iot+g==", "signatures": [{"sig": "MEUCIF8n9vCBKgq+5mtujpKkltASTQRVicEQL1r5nmiqgdLRAiEAwiwyB9RbMZH/cmIUZiWLIdASpNwUZOIJsCmAeFZHL/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAjCRA9TVsSAnZWagAA6CwQAJjJDz7Y6uGyy4RZewbz\njA5wL7crQAIt25PM6p0XMrJaF3CIMyO/F1LmvZazRy15Uz8hQ9rcyh44sWVD\nO1f+pEB2yiCbJoNKMptLduwZ828EN2bQMwTmWD8h24l4kLzRArnk7eRf+udf\nv++B7gi4BGIncenD5GtPE8IN7FHra3rnI8sWE9S1WrNmgqLG3E5eHOfSyaP3\ndHrgV4Fs22+fjQ/TxuP9ME7wn8cH49IADXRRIO+sU6e1bp+D0SB0X6gRvZP2\nNnW4oEngd4KuWgUEXrbLApLF2EjQcJPZLmGvFiZF7dAssUCeHfucmA/N5O1B\nT0tNExydUH/nRgMeQggSlkT5XiWSUAQMSOHg0oDE1FS+A7b9bgrz4+wOSycs\n2rDHVz1H0IUyYy5L3pHXDIurOLn72qbX5YU0HC4pttkF5BTSMmfUB7KbAbzK\n6OTTZRiKxpZuzSTYcEx5WF0GpLPEE2CItbaAzWzfhG6fhy/m+BQ/opmLvT2d\nXTlzrs59cg2gC97v+W0VCa9C+mbkf3f5GMPpe1uFJdIyuHsnmMGklrZ/sc8V\n1wr+xFDOdt2xPDs0kaqiXmPMtKGcW7Zx8ZaDrLo+Tznl4F+MHmiHLyCKaebQ\ntBfnHKg3B97GjtM8aP7hmAc7U9U7hwsuLhT0BesKM8dDEDxW1fO3fidKrvxT\nXgRn\r\n=0c01\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.3.0_1597059106643_0.20676503487565356", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "jest-changed-files", "version": "26.5.0", "license": "MIT", "_id": "jest-changed-files@26.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "181b901368decb4fc21d3cace9b4c4819232b667", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.5.0.tgz", "fileCount": 11, "integrity": "sha512-RAHoXqxa7gO1rZz88qpsLpzJ2mQU12UaFWadacKHuMbBZwFK+yl0j9YoD9Y/wBpv1ILG2SdCuxFHggX+9VU7qA==", "signatures": [{"sig": "MEUCIA2F6RVX9JNC8f2QLFuKqgIxexZRDd5xsx/KDqMVC8IoAiEA5/1Rc7PTD+yoPRg3JoEY33bJm2lEmcV4vnN0VwvIqg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeucvCRA9TVsSAnZWagAAi5oP/1wZ0cM8ckyQs2gHDv4J\nG4+zmnuf8x+fijORK3BXQmmgfG/j/JItCF7vFbf3dDb19mR/6Bss+Kquk1zZ\nb0YC3bsZtIr7lSoPUH8BF7NgfsZYTQXG2GuUrqSQaWslCeiuQUPjZY/6Jfig\n21QZLSUT47M78syE58RKhQgXJbWwLv+X7rFB6v1rufHZvSpY71Fab6Jp+bnZ\ncy0Hn6hQGfNPthB5pUQ/du3Us7TltuOBvOmcOUu2T56tWhZPGYrMVHiYrP/y\n71nDNx7YOrIMBHKrcFg1HtrKX1+B8KjxoLs1w+dDaVbObWuGui5Br48VEfBi\nc6xHB3nz/ATBBoa7N5XbDFY0uoz83rtz0Y770vfx5oT6UhWkYbq7tXfUOIRG\ngb7sTzYZNZJTKuXNVm/quWRJp9fKdaaovr6OPotDHGhyJuzqAIhpeK4IJfpL\nUqH652dQyU/jG1XKHNGc94O7/kbyDRxRK/GIzJA1nXkbdyR89Cy+EmIW5x2+\n2ToUSLaCPujrJDchIf/j6skLajLsy9KTBiD8Hyhgq4pkPAeFagR/ydox0mvM\n3InoAypd6/w8kWAYg/uG93qicR2CbyYhfPmSxJyj0AujsHJqpZQVl3gMtuOd\nlgs0T0C2iJfo6/EPqlRQnKDboe4NZ8sMR/vjSwi1m5s6qq2iuFRe6JDm4NT0\nG3D4\r\n=ohxj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.5.0_1601890094711_0.1570725488931719", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "jest-changed-files", "version": "26.5.2", "license": "MIT", "_id": "jest-changed-files@26.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "330232c6a5c09a7f040a5870e8f0a9c6abcdbed5", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.5.2.tgz", "fileCount": 11, "integrity": "sha512-qSmssmiIdvM5BWVtyK/nqVpN3spR5YyvkvPqz1x3BR1bwIxsWmU/MGwLoCrPNLbkG2ASAKfvmJpOduEApBPh2w==", "signatures": [{"sig": "MEUCIHBAZBmprjmQLifENf/616F0iBDyXeDdo96NfPdEdCOlAiEApgJaJ5BLiNyQLiJUVzmvI8FnbdbphngT60IP4Y2PCBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEx8CRA9TVsSAnZWagAAqcEP/ic9vQfAW1XnQ/zLvyZz\nb72Us3FBt7/e1VxuVSCYTwLzKFuPNk0ggy+ZWcpynzHZIr9c9quzJy/t0Bcc\nmHLAidr/PiaOcSTAZj3Eh92M6pTLFQUcy8j43tgI4WoPNztV3b4zXmQCl794\nHGYsbG3Gyx1DpRYBfDpIn/nhfiCq2Rgcm3MNvzOYmXzDC83pN7khh0wdRP2v\nLRuE5HWQqF77mjURaZvtLOhQo5hERdNdrmQwsY2Omr96x0zfKT5c9rvY8VQR\nl7Qn44L6Upd/VmU/nr5blCvkoJq1LW69nq9tgtWxPnnePynfRK1T1wX71uke\ndLerYY7awlj+UNpJo3MfxwgM17agXwbExM79xh98PB/dzsZF/WK6Cm8Gn+ru\nVdx9wawYX6lm2peWeWNAJa9J2AELFmyfuulKm+itZXCau6h2kdbilHCYdhsh\na4vCAaxYfiN1iNkP1ci2XwDFk38Kdkjnk1LiwTDGTbo/v91lfAtmVshXynP5\nUxrfDaTR1zSyyorOH92/mAISNBh8kJriBGlexHAsLcCqTlwLmpxdxshn4hV8\n95DjHUnzDFXHAKfrTrtQebaN3QMrg1h78bPq+AYVLW5qmYOG3+1bORulNuPk\n8c/YeOqGPEi5LoVbeJzCat4e7sIIooTw5NOF6OSkChL2TKBUikGmkvMvVzN3\n42mY\r\n=5LQl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.5.2_1601981564358_0.755641086958496", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "jest-changed-files", "version": "26.6.0", "license": "MIT", "_id": "jest-changed-files@26.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63b04aa261b5733c6ade96b7dd24784d12d8bb2d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.6.0.tgz", "fileCount": 11, "integrity": "sha512-k8PZzlp3cRWDe0fDc/pYs+c4w36+hiWXe1PpW/pW1UJmu1TNTAcQfZUrVYleij+uEqlY6z4mPv7Iff3kY0o5SQ==", "signatures": [{"sig": "MEUCIEVXVk2Lrpok1fg2cMaM4hBlVH4Vm6MDAY10cKOxXgBtAiEAtx+BnnQTPxiANcpwy92+aQECf/zMchg0nPR6v8NgUzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX9tCRA9TVsSAnZWagAAqqQP/35EHCo1mZvwv7Ibtt3P\njhLjKeUL5CKA5tNYJO9WLZxJfeCQ37AobUMfaix/qtpuIcLo00fZz2diGmou\nMmB0RExZo2ke0M0Dde2yr8iop1fOWWL67J2aN1dKO7TOicTB1C1s7+PyTlA1\n4ZdSVo1FeES0cRxRL1njU/al2PwKsX0nWNcZtkGKXMeXVqYIt1diiLa8ItK3\npWP8yKruPmeDqRKi4WacOV2Tp4zES5DbCG3yCVYUvYuk3JBcTUG99mC1nIlG\naM5YJZnm9D0wwXSWxm7rhOMQOKflU1A87VJMu4Wr6AuV/ciPiNPo7da5t8iw\nnQDTsdf8VmkmmgBwNGYrr1j+sq+gqtGQ5Fw3ypc4gTd2ebWy1wt5wFreTpyp\nFArKb0uHLsM4HaQnCZwGlnf1vumgY0crYk3ROvcKnQ9FurgegeqYxwF1/NAU\nRFUX81RO8CLNDYGCTCIVHhpu9u6PZosy4COsiqpa2qZfMX84m7gEobbBhNkl\nI4IPiRpMgKW78kvMcpa6PICigpJ3Fn9ssEs/jT4r0GtAPfImFfj00zG4as+X\ngUDkVlMV2/tAHPAyufTPumxaaKps5Fa6+r/RfJS3tReaAiwGEUBdBj2uhLLB\ntBUmPUVhCldHvK12M1LtId0LDlqezCVcQ1F+7WNGlrJ6Q75Y2YyqOTf8V4pP\nOu67\r\n=AhA8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.6.0_1603108717366_0.993807946210918", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "jest-changed-files", "version": "26.6.1", "license": "MIT", "_id": "jest-changed-files@26.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2fac3dc51297977ee883347948d8e3d37c417fba", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.6.1.tgz", "fileCount": 11, "integrity": "sha512-NhSdZ5F6b/rIN5V46x1l31vrmukD/bJUXgYAY8VtP1SknYdJwjYDRxuLt7Z8QryIdqCjMIn2C0Cd98EZ4umo8Q==", "signatures": [{"sig": "MEYCIQDrSxLbVFIY7JmzFpiFIJOZb7woQpybf3+8x2E2qQa9+wIhAOFvZZg0IgtrbmwT61vAO9KeNCedqyiJ/ZVuUen0cQxK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkpz6CRA9TVsSAnZWagAALR0P/iB3WJeeKqqHM/BOrMPv\ng4oKtY8hYKZa/2xC06PESZdeytzYZnlmbI1GS1w45q2M2+ZrC6g++7EvgNHQ\nZhg6sa5HlTGzIbO8egC4EiS8iZLroMtwClTfUzd4tKeWhGBYXVqaPy4VCzRO\no4zZk/lO8/OWxPS4zL9W5uM5XuAMwxjHwa+AIt+NLLuyg/o3eS+emb+umzoy\na0ysgzrEUcrN+yjMGSbZNq61a7bmUtbLB0/f2fyQcUl98KXg4sYKQpKAohm3\nRvlD7FlEhLGRcE/3PRqQGmNcNFf/BQVyF5aLB5yF0T2wHLMTySj0nVod7Aj9\nOZJS+WjMt/CMSXXHrKNYAN/6+AUtgxw4HWd/SXjsMltIcos1rVcnjqm1JEyZ\ncQECFjuLlaN4dp3uIUnjDLnSeabzj67MwfRQimMOOxf5DmOKlpo+wCFLgO3R\nFaHVSOghCXr8F+XM5UkdUEjimId3VOHeBima5hRRyym1kz0WdE2vsb+QB5XG\nNN+223AhHisrBWq+iHR/k6jer5TKxVDU77DR+GZi1JMexhayW6Qy4clQsB7c\nLQM1Wh1XX4G0Sc3ec9ZB/UKCRXoOrwOs+lRCrf4vcdPYC/OsN6y6ws+BL6EI\n6QHh6jRl/f4H1An8glU/mIQZGgyo+3Z28H4frhER+2YMc1ydna7h5GTwCgHt\n6TTp\r\n=IH0O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.6.1_1603443954925_0.5606448802590689", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "jest-changed-files", "version": "26.6.2", "license": "MIT", "_id": "jest-changed-files@26.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f6198479e1cc66f22f9ae1e22acaa0b429c042d0", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-26.6.2.tgz", "fileCount": 11, "integrity": "sha512-fDS7szLcY9sCtIip8Fjry9oGf3I2ht/QT21bAHm5Dmf0mD4X3ReNUf17y+bO6fR8WgbIZTlbyG1ak/53cbRzKQ==", "signatures": [{"sig": "MEYCIQCxLYVHHanmGBdBE5XC1S7UBKjQifYEwYJrWorjWB+dxQIhANeVk00XIoLK5qgzXhaPiQORLe1AumvEiCCWSJ/U7RiP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADJCRA9TVsSAnZWagAAFpEP/0w1zuNNncYj6UMKAzpl\nNgdPpHMsi3fT/MY9L+QZmx15L6zn8qu/OFNCh2bXo6Xc9TW0xPbPu4athzNx\nvUvb4F01pkZqpiJ7ngrtDZxv/WtmxHsub1wTzMt4Y6OfxkF6JUZ5JaqIGJUK\nkHN6VOP7JFWcWQNn7MeUaHA2VrU0qSo+dsBLFW4AeH9HSif2kIS7l+rBRqtH\nWKj2g406+VIYKf0plg8RbVgUE6So7Z1O7iIGO1Sby0Rv/JEfVINACbJLOwZl\nJgpcGIT32DpdyOFtVH1D9oMgDh4atwSxNZx2sIqVz9didaO1PJHt0qjLQuDU\nTYFhkoALVL2z0B9IihepI0IsfN+tf/JU5ZUr1HJTwZpQAOwDOcXQBGw0lqlW\nH1CjcyILltCvlK0mCF/pu/UvPaRBDTPLia2NaeD0cSZHU7v/rOQJ6BGYFZK+\nSagRDQ/s3rCx4+glv5URYVYXBW60cuPm/I944Nc+UWslrNmwZzx5Q4H8ukBS\ntk2deA5zNlYthQttQZgePNkgOG8gXOXGkhhDTxJLKdDhs/HXiBI6UU1i9Wvp\nqjoMn0Rs2UWPcGSQOWdCIJBBMv1N9TFRi0PznQlK3NQ4FrpTkhAyrGsYGcx1\nKlIlZhPRW6FdlgvK+W0G+39s6NTPrthuLTdYIWjfBl7UwbBfDXUJtGxcd0hQ\n7CDU\r\n=gS4i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"execa": "^4.0.0", "throat": "^5.0.0", "@jest/types": "^26.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_26.6.2_1604321481453_0.6048962541622716", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "jest-changed-files", "version": "27.0.0-next.0", "license": "MIT", "_id": "jest-changed-files@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5ebc355f1c36ee99b6257f51fd017eeea5b8db8b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.0-next.0.tgz", "fileCount": 11, "integrity": "sha512-<PERSON>h<PERSON>yni22u4euoF0a9mOReKitwWLy3qjk5KVmw8CmGwTrscFWSUE1EooXjBL5n5WtiMmNNWUqU45lMbbXCDfkOw==", "signatures": [{"sig": "MEUCIQDP6TpLvpzLSOBhAsXGugXtpNeMmHeGoQDiNGOLzZVK6AIgRQqUET2avyHiSxH+DmFrInU85F6nIA1Bm/bw2FAW7h8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8J6CRA9TVsSAnZWagAApRwQAI32OJuxBY4WKjCHJ+Kk\nD4GlV5EQLgk/aLCiTdOIRC29JkDAbR8j2dkjQSHyIukNqxCTTNUx23PC1v+g\nxnGiTlG8l4zxu+18QXeiaqeWrkryXQ7mjx/iR1DpSf87oAg1kGMLp0crWZ3k\nJ4FC1D+BevfWNuqq1Qgc+JIc4FeZCeLmQlxfHhabhgqvAil8gvrye9ZcnLNX\neS33PA5T8761B3SHE4nPNLzrYm1/KJAVfKlWCVr5ppxs5jnKGp1QQ/SUj73k\nTJIxM/z6Hf66WdSRFCAUMBgMn25Igoo1gAzm5RYNoycMwz/msR3ULn/bfumy\nSzYpIozcfY5BEPrtRrP1+LPnGqLxQwU4auCJHvLEFlQZpj7ZUtQaz7HoTQwt\nEiMDEo5mPSH8GwIpdYe2+3ADi7p5dwA0iTlgfD+2ASdV6VGIuDcQIeiaOJhj\ngDrYSlgXQh3tN+v2PKtRuwcQ35ekCE2UueRMxpon7XckKtTHGy+NlWHSrRXw\nsTAq1MB+euvkV/ZdSPh5xqcgXH46s3TSQ06TBWqTIdLJ5PZwfVrkNrTSwa3h\ncmdKrF3LmqPJcVaQxIQDrZqxF+Z79IMCFIG48Af9HUUj3ASNovL7/pmkDK4K\nWVtgZo4lrmPG5kDEcMTjUy7F4uwzSM1yGBLUNVwHkqnpbv4BfhQ1qAUE74Lc\nWt7O\r\n=g7qS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"execa": "^5.0.0", "throat": "^5.0.0", "@jest/types": "^27.0.0-next.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.0-next.0_1607189113954_0.3274769685544361", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "jest-changed-files", "version": "27.0.0-next.1", "license": "MIT", "_id": "jest-changed-files@27.0.0-next.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "084a5df6027eaa5e771f81074ce31894a1db1bf2", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.0-next.1.tgz", "fileCount": 11, "integrity": "sha512-EyHpXUoJ4zjrT37r4k/Qdlt4S+N29wgEO8n4tjEbDM33TsK56ZNSWzGJn5Gk82uDm+J+Ror0SAGhHU6KwO1HIw==", "signatures": [{"sig": "MEQCIClyC4ruDB1KNziWgutw1iVmfmV7UNnC2PER1Mku8s/9AiAO/97au60DVYx/FZZNnmHY6J/NIdvZ5Gtr/3IYZAfyhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziNpCRA9TVsSAnZWagAATYAP/0CFCWwEsglXGbjnWwE7\n5VeHlGaJSDBp4d3drqQU2Q2wsIrb+KHS/pGrdNwIHyQN4OX41m8DY/BXh4Pl\nsClIC8W+YSTI4QJWfouGZym1a0NJRe+W8IFqNjhtbZQamE3zb8Xe8R5Y30tC\ng/QvqqKHHvNnft3SgYXbumoGSKL1KU7h013DLYIsisxQwsaX7pYu9IoC3dCo\nz2ijhuiU81hBDqV+k/MzYyYvdwFpEDzxYwKtx6cgJUAhZst7dDAyrLssn8Lk\nzhFtgzyU9CSMDN/lf+BCDxwDOOUS1CP377Jhb3axBkPF1UmKXwHP4IIeTWbV\nXbbULEdjvsD13jQx5vre6kKhky7SRnOAjYx2nzdfqzmZFIgnz255h0p71rF2\nt/4bgvTxUUykP3CHN9Ui10jJN6bf/t2v95OU4dnsut2yVYy3CMIYnuEf2wcN\n7WpJjZ6q/35n/YpP8tYZ4asg92Da+ra6GyJZD5w6tmwtBv94Nx2LChF7RJ+3\nYRchu6R/H+s6eJgN180plv6RkFBJhDzhgCN8i3DViC2gAmjWlUk8grzrXTDf\nIvSPCePxXa6PAGJVIj+rH/RX8dYaXNkLg8TwonYsenYgTMcRrfjO4ueVXnZy\nC0ajioM7WHvBO9/Y8oe256o/gIR4UGmVfXBdyGm+HfxLqd1siLE1vR/T5oXw\nstlT\r\n=wTEf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"execa": "^5.0.0", "throat": "^5.0.0", "@jest/types": "^27.0.0-next.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.0-next.1_1607345000679_0.9594095435306591", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "jest-changed-files", "version": "27.0.0-next.3", "license": "MIT", "_id": "jest-changed-files@27.0.0-next.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0c7ae1fa15a028991feb5baf16f55ce22dca3c7e", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.0-next.3.tgz", "fileCount": 11, "integrity": "sha512-s5Y6dxVeWAYEIvk0guP9LlcxAKffDwmH8uIgQ761vDg6eKB9DO1BtBP28kuIuwUFIVqVcOoTX2QDV5v5grcECQ==", "signatures": [{"sig": "MEQCIC3uAcYuECJpGZA+E5zXPl2u7NwaVFOYdN6d52Xe+PUoAiA1Fl5+egmHpLIulCLMFK4IU947aPLZPFr31IW74JbTXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuWsCRA9TVsSAnZWagAAt7wQAKKM3QPlYTk/AS31NO7z\npjtMKA907QTWTUw/nJNGX038en1tBPLPTJ3ep+zn8QKRHzTMtit3oD0Sv9uf\n1Nt+9mjMrOmQbP2Ev6wbRzRlqVVFKDhY6RFOT9+ya7YR3XzLjZ4HxlQWcINf\nROYcLeiTqXe2DVvIaKKm1OCnZ4VRKy6J12ZgpC/Irl1Z1uME88CQr2phvD2X\nrUS9Je/5AyyZVJjbc1IrxyJSoqPMGtU8rxxKnbT5GZWxzbRKO8SEq8n0lbWz\n0z68E1KlJ2EYNwUvsxQnjTK5HF6IXc7az9rTuWleFXW9I0IXOMKpiTJ9+Fdz\nA819VPf83+8ukjdh9pm5DXBJ/w97pNlauXbsQBIbqaAMf80WOthYwF/GogGo\nXltS+Ejqvid8AOX018XHdLUxDum43q56BTvNMYWZCd7MvQ6uiRwd8c4Bwviy\npP36ARDPMcASRr05Vd/xZ5FYnofZZUvz+aTyFEr/qJsJaNsi4SK76eYRbtRN\nGeKPmUej9YF2/xMQN6xt1MX0KOsOs0lxn/fS3H5IIupl14PIuG8VNsyCsy5V\nmXmQ4HZkICkyq0QcYj0k60IJ/VuLsny1TFK4FYzKc9Dj9VVfln7hAv0flBUK\nsaxZprvRwyLwu+M/ymsRDiCXyvmqv90IMNF3+0EZYWH5oPdDNn6ZNvRQIrUN\n+HKT\r\n=886N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.0-next.3_1613686188153_0.2344483134097919", "host": "s3://npm-registry-packages"}}, "27.0.0-next.4": {"name": "jest-changed-files", "version": "27.0.0-next.4", "license": "MIT", "_id": "jest-changed-files@27.0.0-next.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0a7737f77f5cdbde2fb78468d338f22e43a9b641", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.0-next.4.tgz", "fileCount": 11, "integrity": "sha512-wqT5A8GykzQX434EeNRFnFk0xe9aiy3kJ+S6K9GgsTqJ+Dn3EhTB6DmKxVDo6cii+CFxs8MDMLIuijpqw6eK1A==", "signatures": [{"sig": "MEQCIFh5XmJfKVFGYpeIqNGXVu/8VwMchoizvandFZEcbn7VAiBhNnzrjwoFpyUBLadr5EvTJizld+DipSzRx0gJt5dtDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRipjCRA9TVsSAnZWagAAgoYP/2paCTFa6Qi6HIQF89pv\nW8NxYtsZ+W80FbXLTWDD/Qj1dMKKMe7AI8JTVz7nyV6noaTN/IM2SVZp4uMb\nUVE9kZovC/FZBUhpRxzkyA1cWrAEbCQgO+ye+4GZaiJYhDrrzyg3C4+FNQ/N\nW9cMxosGDydYlQckvNJFNpvcPmL23YrrSGrTzSS0u3Ge6IA+o8cpA4qbnQU2\nSm2ZlWCyDsq/4zQgcpD0Bp/2siOUVM9fBXEw/ofeLnTL7/0Aa/D03Quguvyt\nqLimoBYb+F8E+p5XhSEfDwqMhwzhfcuiuzjrEwVCQ3WhM+bkgoW+sCGt9GSd\nJNHqmAoSICajSY3N2X01le7PIAwq6JMjPWcQ4oLdXOgP9qxPokTe6yTB+lbn\nbGGmsf1ReEUe44/Nx97o0ySyXUjjhN9gVHApWV1a3RlEExvWmuyl5p0hfKXX\nzI8g4afhfwUKQabdHCOxx9fpInXKBzSnFd/qlRsoiHPTFjN5wD9BNplWde3A\nax/V6gje/JvCt2JNf3ZUfCGLCYb3A3mh4ZjvXDyGIQWD+lfRf/rW1wB149wS\nD3XD6NqdGmDjTsGvKyLSZ83gVsWiI0A6URwKrJfZKtZ8RHHjuxljv4EpyoG2\nsg3rdHv07TORCU92yiFIlUxCq7/rhws89Xt/4n4I7aGvlfSt8Vo3GhI0Eizf\nyZRm\r\n=H/R+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "941c414f0b757fdc619778c46c21fda26b3e5504", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.0-next.4_1615211107190_0.12385410502140815", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "jest-changed-files", "version": "27.0.0-next.7", "license": "MIT", "_id": "jest-changed-files@27.0.0-next.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ee07bcb2c55581fa3cbf176e00bffe607cabe2b3", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.0-next.7.tgz", "fileCount": 11, "integrity": "sha512-3J5/+7o7DIoN5oKIeFQfUIBwsnJIoYTnaNlEcmMBI1Z8kKw7cgqVxodG3Vcduy1FYlKmwsd1YB7yEtePyBovUw==", "signatures": [{"sig": "MEUCIGOLJHM0WWxOK/TkqAAXWaYymEMMzpN1Q2R7SlwIe9fPAiEA+ViCgumost35QkLXxt9PBNFhUq9dHabLGEHBYJCXoDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCMCRA9TVsSAnZWagAAgYAQAKCWBKxx08c0cpkTZlpS\n5tHg1UKApE/HIiQd99qq9pVnkyJ6xOr5tAHrydN1az3p0uRNOb04obYbVukA\nsjlZWZWwl6wuFqXMhsV1ZZ5rfkWaQLJlQKsbp8H/mjSCU606sXamx67SnGIy\nhRGXSzqgxx/F0tsl/3zz85VMei3plzWpH6NF9sW4tExxlDMCE74DE4cl8qbb\nG8vwCNvbt+wTgcZXj7d7zRMqf1gI0eChPXCdNuiPHOZmET9KT8XpnEY/EW5B\nOi5442w/2L4MzGvQ/DVuv7pJGFrpHuvZtcCV2ruy86MeqCmJoZYNkq/msh8S\nOLz+X9pYcm2IZ8XQMYja/kS7CFyhAgaexlZSYe1Hsb9fAcu5x4l5YReWHidh\nOsNqAn57dtKzkwW8dVyZOFY4YjLnJTbZ8R0d4NcF8welNQ6DFm+G1d3ra22b\nhLc45zZqEZ9oenB6hXSUhI5ebkKAmvaXvwxdBwFfrhCHaC4e8CRlXSyc3hmZ\nor1BHYmiJQ0ZDg/Yz4JUMOflDFh4zREuK9yRSJVXNDM9bCEcIImuWHlXL3el\nIkyMu5H2XDCPQ2aLp2huDS/gq6FdVUp5m+9Da2SD/+4s2ojAvD54lS1gLj/F\n73o54QkOjQbMlXpOLN3G5hwpWesAvuzvywrb3GjbMraD8WtJovMYNLo1jxAZ\nuNAM\r\n=+lhI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.0.0-next.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.0-next.7_1617371275671_0.44636275422725835", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "jest-changed-files", "version": "27.0.0-next.8", "license": "MIT", "_id": "jest-changed-files@27.0.0-next.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "49665c7852c315da415de5380f8022b39a68ae99", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.0-next.8.tgz", "fileCount": 11, "integrity": "sha512-7ToSqPeXPGfK0ibRbkzke3E3vq5YhtANm4jWsD41RKQ/T2dG+jB+OEF6OVaLffF/ahokd77WYqkcvwLzhdm8LA==", "signatures": [{"sig": "MEUCIQD0Bf1ODfEZyy0QIW5r8WNkKEfRCBF20JC23lTUFP4KjgIgI1P0m5Wj0KfEJ8yw5sieif8ImXerj+nX0met9V78VkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzUCRA9TVsSAnZWagAAVqYQAKS7Itu8BZ1snDLGuJtv\n47UcZxHrMrU5wCRb8wMaaumkWuQGxwof9I+KnFx2IiA41n1SjINtgKdggXju\nDKe7dqWOVv7elMdimC0fk2ZmMkw/Rz9/fIc+XKbwG2THTM+C2J+1Rf9Sh2hd\nKrlfvJJ9Bb/VOqDez87oD9YIp6si7jWh3/vLbpt49fSkW9NpKYwrc/IdmHDB\n2HwwLjslDihNTKkfeVGLrnVN4uE8HizbFTcdsH/uEZ8HO57VRMGHnOkeLQe+\nxMKf0+sma3MRw21vXw2xRXY3eqw5nHY4sDJXOtGDS/wYetPYjHNviF18OYrr\nbvA5Vk3soioa+v3Hbrx/13iZp9utJ4KRnOKiA5YyctY6b5AtaJBYjoj+CLzS\n1O/7UedSBAeXcQD0uCDBhtUh62apK2pgLI4M/krt3aA8VPpqwXrdWCde/YGQ\nQMQR+lmGZ0s8bEue1bF530ewFU5ZUMRy2bDB+zeZIgZ4iuIEknrFyBSkpWIB\nTa7DlPzrMl1jdI/KogkvwirZSr3TB1QLSRp1uxX+ZifZXSVBUMsgRDMe6LAJ\neRl8FnTUYsaq6hDj8gZStfdFusV5di+sP79dCxamZYx9pf0QQdHqJO2gQfBF\nhDx4UlJIPJkOvvwIHhS8nHuNZUKkDyyQWejYuzWRkFcOKmjkLeymNwdDWLG7\nICGa\r\n=9kl1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.0.0-next.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.0-next.8_1618267348381_0.6676826310400557", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "jest-changed-files", "version": "27.0.0-next.10", "license": "MIT", "_id": "jest-changed-files@27.0.0-next.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5169eb3dab8fafc295655ceb338d77c12a6fe61a", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.0-next.10.tgz", "fileCount": 11, "integrity": "sha512-M2fQvGfhkFpdENK028VL4VeyV+0an1/a4Y4bFIeeOXIdzQtnv0yygS2wgUN5obR83xS6GHnIWeGonNoG851pXg==", "signatures": [{"sig": "MEQCIHV9ChC9lEJjOEI1QIbLjNDj+y5x+cHRrxUgOg1/JE0lAiBkRLSfedoY1v4Sb3Hwi0WskJdM0K2a9y3P/9VZx+VanQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4ECRA9TVsSAnZWagAA4ioP+wRUnlLu/GItOVXzG1fA\nzc7Xg2eUduX3BRpmH13xSnEu0WeNz9tHwccz2txMzDofYzVCNl3FMnyKMZ9y\nOBVP0VHYnN06hCE9dssqUqCWVZjlTX8x8wOHTeOerSyr+4gkx3EWt78W/DjS\nNgyfQKmlRduPpKvNMcmCmKpe1paF9PTyH6fk8ld0s96jmiORTgJphotYZTel\nepLDWpysKl8TdD91rHPaVRf3BOvYV+J/f6LxqY7q2NCSaIc8gkmzbjs6Rtxa\nZy5gav9PG31XbXPPwo8z96w5UXHduufXiX0cOhMX9RfoF8EjUYmIPF/Vlcwf\nD29qli3jFQfmqJHY6VQG4umx8B812yRjTSditlxDrwGgU1xo4NO5iZkflhf+\nWmP/nr78sahvn/+3EVnLuarEO51WtQe5FnKfPTYZx6iFXNJxLM75fkrYrTM5\n4ZIAgbTH6ngK/z6q98oNG6lUWjlGlsSrvkhQOsKLQOYfk26bHC1N2SBvk5zV\nm8lFi/vxboj3sokNpDZF85xlPNU1SqIzxeb2524i569gDMHyK52UvJ6in7rC\nZVHlJjaT+WcjlYxToULC2AiAkvI+UMp7gEvvDDE1qIFy+2xmdODR5RadjzrT\nUSSsbcSq9Zg0ddLRuhRfql2/bOC7Te5EiawJ792ZsR3KHvmNkHf3w6C80Uxh\nawYU\r\n=QrNo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.0.0-next.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.0-next.10_1621519876192_0.1601040172892496", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "jest-changed-files", "version": "27.0.1", "license": "MIT", "_id": "jest-changed-files@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b8356b3708cac9d05ebf6f9e0b32227b514945c8", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.1.tgz", "fileCount": 11, "integrity": "sha512-Y/4AnqYNcUX/vVgfkmvSA3t7rcg+t8m3CsSGlU+ra8kjlVW5ZqXcBZY/NUew2Mo8M+dn0ApKl+FmGGT1JV5dVA==", "signatures": [{"sig": "MEYCIQD8B2nvGmrzDqizqUihlkxbqS3GBGkhRovjL1W8sZKl3wIhAJBwAQMFCH9WgMlM/vfwMOft5jSD1R9yWMILoYU63pmO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwmCRA9TVsSAnZWagAAqhgQAIXtyLcMvG3e97G+UQr5\nVQDFtznyG6SzY1yVa7UuQ7g3SXeAmY/6fuSIRAPkQZ+9A1AOBgUc32pb+9Uk\n+aByFHewQdqx/kzhyj9jOny3yHeyFYqUKAnggQmNWrdPCa+j/f31s+euRr27\njEyY2iZlwR2cdp4PSPHaIjObHrk6f6m34z95ByyBUbM7tFLCB8b0dJqD7c/E\nJI5ypqOzJRG/Hm0PyuVpExIYX2Be8zKHc6SRloa4bkSCchw4nhcVAeAvLFgn\nMCN052FR5igqBJZOV5e6YBJkS7jvfefeaY+LD1ypg4RUM8vk9nm/3cTL+yOY\nIR2WmhT3SrEyVaM12bcOdtBa0kjj+85MlxsSfqWOB+QsWoJvTqQN/hk0068d\nea1pWKxdiawCxzg32R1r0/DaPL0G1V3DVD/9+rTzY0ttjg29DJg63o6Q+EqG\nChL0rrzGxvGCbxtfJlYRJA189cR8TVOlG1qLaxTwhIcFfpCRulSWSrBbqD7w\ncxA0uVT1/LOj+L1K9v5k6wKkf+iFp1kNet5SDVI7dH79DluLQ8UP704xAhAE\nWxwpfeGDzzUMC7+laFaGU6RXkxo2OaLOeVs65S4xf2pA/McPiIY3pxosf5hs\nZ5qib8nun056T56OIsNrw1b+/eFGMK8Z/+amfTL90O9W8+uK+xRgG9Z2pGvX\nwNeX\r\n=F2vo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.1_1621937189729_0.45045616648537257", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "jest-changed-files", "version": "27.0.2", "license": "MIT", "_id": "jest-changed-files@27.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "997253042b4a032950fc5f56abf3c5d1f8560801", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.2.tgz", "fileCount": 11, "integrity": "sha512-eMeb1Pn7w7x3wue5/vF73LPCJ7DKQuC9wQUR5ebP9hDPpk5hzcT/3Hmz3Q5BOFpR3tgbmaWhJcMTVgC8Z1NuMw==", "signatures": [{"sig": "MEUCIBtsrnRVOAdlXcAI+OvrHgtZlbHnP+rCyLyCLInXuUg3AiEA6nb4gZzgJayZcL12qepa1qUoPkoAvGAUOib3mO+ZRfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi5wCRA9TVsSAnZWagAApUMQAIhiXScXT29xfcayhpdN\nUmkacC99zrPcx9tKkDORt6mLaLTHXFjtmn2c+E+xbPyNj26xnXdAZLG5kQ9A\nGcmpKXTPDDGOFPqFopBKum+fkvcozTmFFFcDWDLzRKOtRwL5LntbUaYa123q\nn7ElwPIhSfCsd4H43dvuiqb8ZNoduooEURLYMXY5fpszAclgvp7oNFylLqVw\n5Gm1VdiJK7oCXHNNbEGRsNPA1L/HOeHoOk5nF4Pf4fz19/1CLec5CX3Q3gUD\nhDCJ6YCvSBanqLZUvqxJykX6WLHhs1loXoliqcpSGU1DIoHsIJDwtwtxKcC6\nHnD2YYFqyGznr2f9gYYTuqx60/9U/1ga3EeKOwt8TfQuvchq4DDDv7rBlt73\nX6oR8Zi6FsX0TYkPzbX03keB/CFupGDGjxF/Na8VVatfY8UWrXm7DQLWwm0z\n2YlkiGlGx1FrS+28CILs3RGO3Z/HXn2gh36v8p83XZxX480z3+lERtyyHrGQ\n609cROAYdK+2FgpnkXJ74AGOWgy6TlU3f7aKBb1IYs2+rYmg9nXyHyZCjdUY\nr/prf80NZ+Fu6I3p2wr5juBabf5lNT7iNEkgH4bG2kGm+4SPViuQegADaTox\ntYz/oWkfmCvI1heX0YteU46K1WQ2jkpY06TIFuL9fYiJRELoqb5JLrRJTs2d\nEVLg\r\n=mBEq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.2_1622290032136_0.31871927320573534", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "jest-changed-files", "version": "27.0.6", "license": "MIT", "_id": "jest-changed-files@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bed6183fcdea8a285482e3b50a9a7712d49a7a8b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.0.6.tgz", "fileCount": 11, "integrity": "sha512-BuL/ZDauaq5dumYh5y20sn4IISnf1P9A0TDswTxUi84ORGtVa86ApuBHqICL0vepqAnZiY6a7xeSPWv2/yy4eA==", "signatures": [{"sig": "MEUCIFJqsZG5qMVAVHt78bVCeFjJyfP6P0cDmHDBH3LPIu2BAiEAlGZdo4Y8suDmvf5PKmsKEPOaj8WmgXaHyqomPGKh5jM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFgCRA9TVsSAnZWagAAOnwQAIj5AMdpcxCF9VhMiHpE\nhwsWTW/RbJgwi/EX9lfCzkMLbwkwO8p2nm6fh6EdXbX4UHNHtsM995v3KEsJ\nCZOuJVruS3dduWaCNLarG3zl+rZZotlRaVERf71KW9H510aJ7MC48fl2tBxZ\nh0KpZc5zIYYgBdSnieoHDf7ZubU/GdzTPGHzRrt94I9ImwARsTQB5aLwN9dI\n7COIwwEDArioLHN847aXacBC6LR1v+fBBsGMzMysS5YTKgb+HHBsw7m37n9s\npbbiy1PR099ggR18f6NahjfAZ21pz0BaQAi4V9wUBbq2a/RzxJx9zrfpIzBT\nTLx8nwgMHcVIpwgwvlI5kqRyX/m4XbBWQteBk3i/sSZos6JuCa0vGcRsw7x9\nnBPRcGNLUcEyQFf4q9urjnDLTRj37/lzDjEFsHNXToNdHeqcvkGBWmIsDjgt\nDnQKznjUeqzYY7HeyMx5CcCplXG2FKHvoO0PaAItnqsjuG+IEj5tLg1gZn7t\n4oRDRgMOYiUHqySQzCCVPiuFL34A661EyfwNxOI8p54JPUDpHH80YjE3NV+v\nliJl8WMq3Z6EtptMB8t7ipJKS39oyl4M2r4fEkiDNR8wji9mnuCyHsemX64M\n7/iM34q8RKxZJHEHZjDN6qf2+88N+7bTzBGG6nhbvNCP8iWaT1aPeGZWLede\n5rqe\r\n=5+iR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.0.6_1624899935998_0.2386794735748754", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "jest-changed-files", "version": "27.1.0", "license": "MIT", "_id": "jest-changed-files@27.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "42da6ea00f06274172745729d55f42b60a9dffe0", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.1.0.tgz", "fileCount": 11, "integrity": "sha512-eRcb13TfQw0xiV2E98EmiEgs9a5uaBIqJChyl0G7jR9fCIvGjXovnDS6Zbku3joij4tXYcSK4SE1AXqOlUxjWg==", "signatures": [{"sig": "MEQCIG3N+XoQrcSHZMGepCTIIiBRzAtWxRyJ0MD9g2Bvz0tZAiBIzaLCcTF+lBoqbmUPy5xF2cVP8u7l2Ywz4IWuz85Rzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeICRA9TVsSAnZWagAAEEwP/0F+zT04W27JjzMrxSPf\nb0OSy02DoUHk0q0kyI6RyjY6u+txsgZSf3uvCPvDr/zO5aAZwJFj65SXr8Ui\nijNZjcr/YS5NDWp5PjqrV0ukKo7LPyeQ2L0KsZvlil/dRV6So43f2Vts6xqY\nSithR5bW7VfGxbFs9suEXEpeaP2q0SeDW5SRSLf0Pwa7cQL2g+5sJZ9YuJ54\n5DEfVPviqNDZNjlVhS6q1pAvxCE9XxOSpjPQAX5WcmUVdwrG0PG5bS6dJt4q\nFNEtBVc2AY+Su86sXNApAogi5yiTRcj0BrJ9dXHdpee8BeyarDCRHIbr0O74\nF6vpvNYZg7SC27YvgD+ZVY71EOQgQgvisMqEAb58SVuky5bHhc7GPzDGFr3E\nYC2iVeiolKacI0xoihZKPBtWQPcs31eLDHKFhRyStjj5mlQXqjqmJfli3oVz\nPqNQwtEWJF2S1V1lVgL9xRy88ijXK8gx9syc5dPSxUWbGE22vkL9DmwMgEp6\nzwGXyonDzF7+6dAYkhgYBxPvSLZz8l9NB8Pywo3WfYSiYG0jZqRWta/H5mJV\neyi4I1LnLHhC7+fPx4SgR3VgSGWxeY7NAxsrp+Fr7Z/faVRxqRB42i7TIR1E\nJ5+jEfw0KK2E8dD+25pItARiGD49vUikWhr7p4jqLqk8HaSsw5Ium/LTKZoR\nTADr\r\n=u2Vx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.1.0_1630058376396_0.4161838942437266", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "jest-changed-files", "version": "27.1.1", "license": "MIT", "_id": "jest-changed-files@27.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9b3f67a34cc58e3e811e2e1e21529837653e4200", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.1.1.tgz", "fileCount": 11, "integrity": "sha512-5TV9+fYlC2A6hu3qtoyGHprBwCAn0AuGA77bZdUgYvVlRMjHXo063VcWTEAyx6XAZ85DYHqp0+aHKbPlfRDRvA==", "signatures": [{"sig": "MEQCIEE0LgR8Vu93NA9e0eAUogtusTGD00gtCWu5okGNKhiwAiAxQb3TkCKgyf78htdCP/g5oJtmjhuewWClo+5BPu02lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIx6CRA9TVsSAnZWagAAy1YP/i/l7qZWCU2JjPaOlFJL\ngKFq9p778J7u4fBy1P2VNa1KHl6c2mk35+NHmy3wJmJMtubBM50+YedMQ0ba\niRZBRSLWf0Ehyxik8Z1du6LG9/SuMyhTZ8M7eE7JCUlOcw9NZr8q4Hs48ji4\n9F3Jzh+7aRwWRjSlYXkCwnWz5SgxzEvEqC19MoqAwa+WHTKy53UJ/OE9Fkau\nwOV3riXtjT9lznhHNCPFEAYV6lSLaoHfXr/Dojns9Lf07TquBn/CpfbQVTa2\nnPHm9hcWgLxmXyGZG6BhGbOfgqmNf94LzHy9912iy+RXWbpAJZvWiQg2Ne6I\nnX9lX5JIh3zET84Qq+2uhaesYp2vv/FYa/Npj38z5AAJ0CFaHhf3qdp5RLXy\nbbN6Rt0WkE8g6FyR4f/5wFtd0ozsY5DXTN22ByyKDzRd25Are4nS4AxyiCmN\nbhVNhNk356HtPuApc2MOwAZTLIXI0GFQRVdDWwXraHEG66AYYawVSSx7GLgV\ndLFHZ3xEnEPomsGfN1R0isWfZkzhLor21FEwTrEtPpzIsCDCWk5uvMd2UEs5\nTlQP312KyCe5d+35P2uvKrhWCq5kvJCdWeNYwhXBFgsIA4EPe/SsZqcpdiTj\nAmAy26sbt++fqVqnEguwpAIcmnf40TmPGz6fgV3jdCQZRPeEO60qpUWVepdH\nbtzg\r\n=BAVB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.1.1_1631095930831_0.9441975464001864", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "jest-changed-files", "version": "27.2.3", "license": "MIT", "_id": "jest-changed-files@27.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "83c42171d87c26d5a72e8464412cc4e239c01dda", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.2.3.tgz", "fileCount": 11, "integrity": "sha512-UiT98eMtPySry7E0RmkDTL/GyoZBvJVWZBlHpHYc3ilRLxHBUxPkbMK/bcImDJKqyKbj83EaeIpeaMXPlPQ72A==", "signatures": [{"sig": "MEQCIDXjw3GfRwWNm5VXy4xARzu3evMS+juvaWcSIqrNp4K9AiBl0TEfAyNq6DouNVWu1UFco5qAbDLNDu7XCk2Q4UMOVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14820}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.2.3_1632823881177_0.7670362891604412", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "jest-changed-files", "version": "27.2.4", "license": "MIT", "_id": "jest-changed-files@27.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d7de46e90e5a599c47e260760f5ab53516e835e6", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.2.4.tgz", "fileCount": 11, "integrity": "sha512-eeO1C1u4ex7pdTroYXezr+rbr957myyVoKGjcY4R1TJi3A+9v+4fu1Iv9J4eLq1bgFyT3O3iRWU9lZsEE7J72Q==", "signatures": [{"sig": "MEUCIFOigwoO2RLIoxMj0WUWpt/h+DCyORPJtaC0mDvqIdubAiEA/H7a9rgeoJF9hSx3QsXd9291XHC/e7GFyKA2cOjhTbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14820}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.2.4_1632924286936_0.36844752178780493", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "jest-changed-files", "version": "27.2.5", "license": "MIT", "_id": "jest-changed-files@27.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9dfd550d158260bcb6fa80aff491f5647f7daeca", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.2.5.tgz", "fileCount": 11, "integrity": "sha512-jfnNJzF89csUKRPKJ4MwZ1SH27wTmX2xiAIHUHrsb/OYd9Jbo4/SXxJ17/nnx6RIifpthk3Y+LEeOk+/dDeGdw==", "signatures": [{"sig": "MEUCIEkZCH3MHgMUKa63WsUvqkyHrEgBa4C+roERNRrjHTMlAiEAn46cwlyNYM6NYkrUjCh0wfaCZb8csOVj1ifWowq3AVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14820}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.2.5_1633700359400_0.39754817309552104", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "jest-changed-files", "version": "27.3.0", "license": "MIT", "_id": "jest-changed-files@27.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "22a02cc2b34583fc66e443171dc271c0529d263c", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.3.0.tgz", "fileCount": 11, "integrity": "sha512-9D<PERSON>s9garMHv4RhylUMZgbdCJ3+jHSkpL9aaVKp13xtXAD80qLTLrqcDZL1PHA9dYA0bCI86Nv2BhkLpLhrBcPg==", "signatures": [{"sig": "MEQCIHsoE6zfskn0KyZPJn3vCAzA6CeCCOpGBaoE4vskiBTIAiAoWoyvNj23xT6EKIwJBDvIzjiW3jaHrkV7lY2wp67PhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14820}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.3.0_1634495686161_0.7568184490503629", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "jest-changed-files", "version": "27.4.0", "license": "MIT", "_id": "jest-changed-files@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2fada5f9952e3cb8af83e89338a089964b55ea5", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.4.0.tgz", "fileCount": 11, "integrity": "sha512-TacYni8ZumaB10L/fGRH92MbLYkn+MF2KtgHeAOcwnOzfmt+S6CDmJeslZuLOpnRUQKkV/Vr4qPAlrBTE5r67A==", "signatures": [{"sig": "MEUCIQD9/NrMb1JFB0Oc1nYVs2IFOsq2YUYajGVVmV/c+MnErAIgW76ZsBZ4FAoVNzLC+5WEANhiAJjI9ZUPj3QxnmsWeWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd8CRA9TVsSAnZWagAA5XcP/ArXXFr3ewP92l8RYUi/\nLLyQ9oPkpu61cm5G/7sFTm/9mDWLbdPd8pcOijLUWgyxAwIbDohLL0Xgk2YR\nz2beNuBP/AbEkGf3huBcxl3LKXgZqXe+DL343bsqeEwrvsF7e1asB6t9aso8\nBoBvOd8l6fYu0Z2D5//j5sCtUgA9L4ZE95+BgMwC6V8Ml6UtB/sD3Ug99bBp\nlvK0NJa98jc2iQMlg8Mcl+uQU6XNAFVMlLeZ8E+e3ZEsuP4vX/9XkT25Ap4X\nFyBUmWBKySKgdgJDmGPg2NehV+Mcrj31tpcSgRdVT045HRpaHVPgUZC/hp4o\nhZIuhPsjB+EUUYeGXPwNMT8/8kAABwNFmNXbUHY3MINAZ0Wf2h7tNjAt22/u\nYf3+1fiS7mr66Xjm+/deGN3WP3/YRujNss+YmKaKyyUgJd+lNN0+VUhw5hgN\nxAtdDDqNKeZx1c5pyQorfCVe4MJ/iXN1+Uhts31eHmJ1jCLUH/4Qxz41V/Hl\nKk2dsMuo2PlVvSrjG1Wo9YSJ/fFBUiVJR+F1YM7fw8caLkfpDFfhYIHDoUap\nOKIfbyIENzumxPdoh6ORGTa9RCW9qDLLFZaVb3pf0zCNqdQvw0qYJa1Fhkdo\nEu/YhqLzRWI0ZBklSMHO/HFebjgnIkqgn0KgZLeuVLWFOBjGsPuiUSPHJiYM\nRC90\r\n=jXUw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.4.0_1638193019851_0.6782900592338705", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "jest-changed-files", "version": "27.4.1", "license": "MIT", "_id": "jest-changed-files@27.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a74527d4fe1690f3390173a650b988ca5ac6f533", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.4.1.tgz", "fileCount": 11, "integrity": "sha512-tjbk3DJkgiFDlwYJDcAdFlfGaEYgIV8McSwXzwN221wohFYkC85XPkLqXaAswVGgzHyCNima+eniLVTir+4QBg==", "signatures": [{"sig": "MEUCICiSzzDBIk6rcSHIVHJ4qGNMdt2aUwbNi31E9GaL9Jx2AiEAiHcnk30FGS8f/jiGf4yIzen1xWboFKMMPABopRhtFX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeKyCRA9TVsSAnZWagAA55kQAKDDQNVQNvsTkUnglBOc\nTFlmJTzx1ebr5QPdq4K0TwtyBAi07MFbunPmurbmXtG6NIZZMHYdv4Jz4pfp\nKO9sgmvIwCgUKeXATvpVUNGg2s9NsMUZN1VvwuWwHAtSOXjcXlHTwmLHMeYr\n/RpcZl4ndTgSGXbzJxWP8Wf67xA9Ea9D5xNVNWfUEplNAiuNtGS+cR0QcwlZ\nRTUt7Ga1Sb55RuSjxbbh+ufvHvdhBwZFcgynM+t5gkaGG7b9GbmadRXGmv8U\nQgjn6peBj4W8n2C7hnP1GFDZCm8GWXo3XzSjWOtiUVdp9o19tNa2XnzYHZs2\nx2KoGp4kwe+YhWJx3xvp2egnfgn7n71/SiW1qjNodW10U07Sys5z8kiQ+4IF\nWrPrYf4MVn7usfXzHyYTRHNJ0JvUaj8SUg9U6PTh7ARMN0JTwFm5iNTmM5mW\nJVFMlE+fs0+cutp4tiFTrdSBDojXHcgt+uJLYgusoRym0rMWIUY5xQPHdLy1\n0O7g2/FNoXGUVYrIpeQ+WgSMmAfjiTEOOIjlGLe7qtlFZ0pOSMQmaYjTNSq9\nIKcUpKphbft2RmqJld/PlJ9c/GSU/akJq8rGSu/w3vnbRH+r9GqaVYUFmIpL\nBVbglSweOOMHrBKFNm0YMISqWeB2zZM2t6mArPEhMkjzMJf6bNakOL73gEGM\nskjJ\r\n=mnz8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.4.1_1638261426679_0.966207982330731", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "jest-changed-files", "version": "27.4.2", "license": "MIT", "_id": "jest-changed-files@27.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "da2547ea47c6e6a5f6ed336151bd2075736eb4a5", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.4.2.tgz", "fileCount": 11, "integrity": "sha512-/9x8MjekuzUQoPjDHbBiXbNEBauhrPU2ct7m8TfCg69ywt1y/N+yYwGh3gCpnqUS3klYWDU/lSNgv+JhoD2k1A==", "signatures": [{"sig": "MEYCIQD7V5JK83SFFQ3pHIuoifj8Q1KcDa2PwEXUf0LiCe9afwIhAKO0Mx6UPo7quiM3SOTNNo/ROd//JIL5wTZc/jIMQOtz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDDCRA9TVsSAnZWagAAHVIP+QFMYy7vTICOFeATsE/I\nQoRQXJum650Ib21o5mRjtpbDYiZeCfJ8E1kIOkxbl8cC5aK1i8U1eK63rVrY\n7AfAcsCrCXMLRUEvIpBuXYbJ/ES8N7WLu5WKWiWHiER92lRfV4dcVZiMVAeq\na/j3IHTOpn8HNww51ZJdJytxyETTXZoL7GH2Sq15cjFbE2mUXVEmifJHYHdj\nH/ZGmKCEP19RGJaDWDrxA7AzJqSN05QeInc5dDmt35QHKRrSyvfugrtMmNGr\n3TqWwgBILbrUt7VqHJh7TVEprcuQNVtF2KdIs52ODLHT5Z9u53ng4bUX3akT\nAl+2KaSKDDHCIydeRoC8ckwL7Rrt/5YmAGcwWKwnK1D0eFp85D0hJjcfXX/e\nqq7QZ06ZwCrg6VQN3SnVI7Ous7fXIpdAkIeHK8xt2bfOZ0ID/TRN8XwJyqga\n9lqAE3RtWrzC5lFfMXNvw1PfRtZnn498wERkpk3vRdXEREH6vXDVx+rSm0C9\nZwZ3Cz0jcKzM9UqGSHGzLXfRRjZPb2WL6FJu6wPMGmsiJ6HE37qAbCyfEzN6\nFLn3JJNeQ13zIRL+dTUFEEkIvs0zjd0/uPPxwgwKToWCruxv4DTgDGZ4zqu8\nNWDz1JNHGvMlH8CNssjDtZsterHtKJ6U3t4+VHuKhXPYYSbZlvTprsK4qgQE\n8Fwm\r\n=O4y9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.4.2_1638273218893_0.022149104765119887", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "jest-changed-files", "version": "27.5.0", "license": "MIT", "_id": "jest-changed-files@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "61e8d0a7394c1ee1cec4c2893e206e62b1566066", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.5.0.tgz", "fileCount": 11, "integrity": "sha512-BGWKI7E6ORqbF5usF1oA4ftbkhVZVrXr8jB0/BrU6TAn3kfOVwX2Zx6pKIXYutJ+qNEjT8Da/gGak0ajya/StA==", "signatures": [{"sig": "MEUCIEfbYsjjzgwaylPwc+IPqVzR/sidGa5PKuSR7wcDj6VkAiEA7CzlRban0U/3YSDfEbTBC9r3zczTtjVT0bS4MG90wrE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp6CRA9TVsSAnZWagAA8LwP/0mg6NEkRqqRyHdmLetI\n06QnZr++bIVT7v24pHkfIjFMrtLCmTCEK0ptNHpMhl0lGED78QhlJxo/noYn\nWiGI0tv1MY3S7RvfXdvB+etEos6lL+cG38T6xxhN2GKoyNAZ46X5JhRcutUq\nVidTsexWGEs7nf3UHurJeb0elNE97wKEAc3nfKNGrARAvkJDsn/jg3sIpV4K\nsxaFeQl3boIytewobfx22qbZSGmBpZ0zR5EVY5NmWN2lZ/fMU2q/N7B6rVzt\n/+uY9TldQGknUWgjuWhxv1qEeeI+L6E0hs9sip3MhxAsLUlZmUN09TvqKrkj\nMPxalCLf/zQyTC9ygJOuOVurvtSSig0MMR2raT8lDHSjHKDZnNxQMtSSSBTA\nP+7TUfIOEt1yV2kb0hRMsBjOQwXQPsdEY99J2bE6qHTFM0Sbkru9blMa/8R9\nw8ZWNyyFqFktUAcWVYgGjDAUcmFRJjBZHTJZFG2sV8l6kcjG73DwHLqbe/2x\nYS6zdAcPinWGa/yEzH+bjlmm7bRuvvnz0q98gtRJq1fQ/x9puteH6pZ4mA12\n2wPUUxAXoCxjs4l1jLRf3w5Yif/8bAeV8VzDNbNq6Rdie4mVySe/t/NQZ42P\nzL9P0TEf+YLgqS9q9EyLtrA/B0nAkoPNijWqo61JzC4VkqBZPIdORkIC8UGK\nLFJy\r\n=I8Fq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.5.0_1644055162136_0.03572027963353941", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "jest-changed-files", "version": "27.5.1", "license": "MIT", "_id": "jest-changed-files@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a348aed00ec9bf671cc58a66fcbe7c3dfd6a68f5", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.5.1.tgz", "fileCount": 11, "integrity": "sha512-buBLMiByfWGCoMsLLzGUUSpAmIAGnbR2KJoMN10ziLhOLvP4e0SlypHnAel8iqQXTrcbmfEY9sSqae5sgUsTvw==", "signatures": [{"sig": "MEYCIQCSaER7i1nPhDj8H/JtWPOUFpr8kwJvSV6LM+yGZ1m3igIhANysrvneDzPrTOgMYwbNinv/pf0CwoIHxKA33pZi88cw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAkthCRA9TVsSAnZWagAA13gQAKGK8RtmqJQQisWTtjpl\nUdTq7wnt8zaJMqrxpaAf7lgUsN33scICC+EMsnJvsnbfpzrfV1ap+LIsU8ZE\nU3cQfHqsGsgPIV97LEh+wTn9EzabY10ERCzy9N79xDuckI6A1ms6bZR4MJZp\nelIdhePvqGGs3IlvcEB5UNJyQE5Gggs/EYh52puFMqWcw6ES0RdaRbu/PNO3\nCxcUAzLrqk0aYMqI9iQJL7Ef7Czy6PEC4sR69AGKSJSq4kRb332aTFFY9cP9\nW15GRnLR8mdrHBjUjm22oWCCLO9Gmve7AdbmKaG3q6QqjG7opJJGQngLnMdh\nrxWdaxU1c0UQyjbx10cuWEswkAsGowrqrWNkSLeESYWzkOFKQZNJ4NiNiSmc\nc7EFQenV+0/hzmOSmSxLphhLHAW4FOmgLnrwzyae130noa0zKp8AdUXLMObe\nmh7C2vEiykCisUYdfSPUklV5TZaEAqWRJWEeK8/AjiJi76IB16sMsW+X8yyw\n5qsXQ+QPvPduyOFCTHRC4s35v33dgGslKXpBO68GGXErbI+vTN816tLPIzc/\nNfrJvLY5IfYWTpIDCA70+zrFNf/bQ6JFKLB03Op5tUzp8pjVwijWmy0Zs+x/\nzjf50a+9R65pxjW904lgEJwCkotU4WGQwuVJwd3epav52WFYt5Mmpx8qRzqM\nWVP6\r\n=Mx8G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^27.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_27.5.1_1644317537779_0.608590865924679", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "jest-changed-files", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "jest-changed-files@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "15a56fe429607d871d10e40137d937a4ecb44ac0", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.0.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-Ng6lCxLppbJeDAcS2huW8TWB2Y8ZxZGaOAM8onMLvC0Mzvc+OcFf21Bv/7sZC9vZdUzeqj9Z7iyFyBLqhlEnfg==", "signatures": [{"sig": "MEUCIQC/6s0xmD6rnYC5O815l+j3UgqdUImJqeKRb3NMBfr8fAIgQQ6hoYSIlMQ74yFgKd662wXd8QoOuQASR/TkBWxsCRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa6CRA9TVsSAnZWagAAAPcP/3DltBsXYTiDdQLcUnsa\nqMYPbTKmzL6S+E57iA/qhrPJAkwWloi+vv1LOID9LK01Fa8pWmwcYvuKGLZ/\nbVRvMnLscyPuewP++078q8BZTj6IacRvJSj2RyYVswvCBUOnb/1tjOp2yQYn\nQ/44QxEoxbSVeoHDlG9bkKqJjeWNz3k+XXV8KG10SrqbkylIBWarfqWJCyc6\nGDhWNvstf6RFi3QPlVNz4k7Y5Hm7lTy2ARSwsswc2YzDIMiug9pTCRDOVJ0Z\nRJTpfaxaw5MPH4VDQBRN2j6wW3DWc0c06KosFv/n5+Hk4IA1E8+Wos78VkCI\ncgP8lJtWr23Dw8MjE/c19wLFBHOusCesmQXhdAav8X/ijjqXEA78deGJK9s+\n3QizzjpL4+2AEizFNNrItPcrMe0Y3hKb8ABS27mQZkIYCFxK9YTm8vaMAjHR\n1ycs03Liu88pUMpsZTWTHg7iB0iHeObiLo5/YOQ++P5a7FHUnMnHSH1hT+hW\nsfO3DvVAyB3CR6sMEScTZUNp1CS+wGlydf8ukn8Ew2Ilh+284/M36p2ic/3x\nzg0S5MIRolhRsAZsaJGt58VcBjraOpmsPFc+rNM4Iqb/NhBKYxqklZGdykys\nmM9KAOnPRxWQLsW2QDwgxBmOuDfbs4Laujat2Yc3ljgSGGx49b3EwT3p2iq0\n1/CF\r\n=iTYm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_28.0.0-alpha.0_1644517050832_0.420292458655531", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "jest-changed-files", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "jest-changed-files@28.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a10dd861a474c4373c4612efd9b6d2fe48229687", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.0.0-alpha.1.tgz", "fileCount": 8, "integrity": "sha512-7IVCq7hQNERzgrFINzoGYPu/4SDDg0fjznUsw7BFhuwVkxE2iCoeRjemqXZgwHrz0g7q2twdf6fgUrtnyRSeTg==", "signatures": [{"sig": "MEYCIQCwlobEBdL+ppveJ0UI2FjWZI7XiEv65Shx2r7XmriE1QIhAI5HEmFXklyEZrBigiajtTIgqeDzTNVLPypO7Yq+r1ps", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqiCRA9TVsSAnZWagAA3uQP/jirADBN2OKHxBajas4o\nA/Ef+Q8VA+h0+tlOdiYVKuSmnwXtcpa3IFhtEmkz+v16kjGMmPNGa8wd5z+r\ncb1N/3GlnIL/tZwQUY6WUUT0moN+UHxNWJrHi+Amae9ZAf03mUYscc59Wb9U\ncB56pxo4wrcCzMeGdDn3M3nYvmoCzEbzYQPzlEMMuWQxePojdh2IpdFKm967\nd0c6LHVsOZphBExoRncarPCyakDVZYr1YoNx0GUhfnN9I5UFHLzn+3l/Qf8n\ncIPeGSfGfelq1Y6DDDgVJNCH12MW0tNk5TLbpMcInjaL4nvwwKQO96jNiGUh\nQ4iaiGDZqJXFKe/5WOqOIYjuZ9wo2MJYFD+bz5uyA21fYrHjVhfd/ASZ5GGW\ng2Rix2Pd7Hs0nFNFK+NPOYHv7LEwHmcOGVL8+7X82FjOt5zbEwffVAXciT2c\nH5clsNALoqahqklpMRCbyhgJkExkpdtR29JbPQaUKa2jEUL69NYrv4mbPZ2d\nFC5NcsBdSRYVYDq46+PYyEKonKstZF2YqFpptLCmfVzGC6hx3mslYVeW7Lfj\nKCSQg+FGxRIPnHwKuFv9PLhVFyk9GCuGjnHl9fCWEN6iH+xXuVQrGHuu8y+e\nnks+6vwQ9dEu9UD1ZN2g/vnKdCa1RKifQC9BADie0AOr9G8Q827LsPEekg01\nKiml\r\n=+RMF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1", "@jest/types": "^28.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_28.0.0-alpha.1_1644960417925_0.4137601862798097", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "jest-changed-files", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "jest-changed-files@28.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0fd1bc04a9e7e249240d1c806e0b7067723e956d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.0.0-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-C7EMXWOc8Ps2ej3kPxY0dXXa6a+IfWUbsTiXNSPTOtebU3dJ/hyaCNx190bERSn+ogPEk2jCi5k6rEiJm/7Wew==", "signatures": [{"sig": "MEQCICEnpiP+gkZ1MlEGWU9Qerwmn8QEb+1IgBJKdCLHznL3AiBPYo5kbekjU3fgq4aDdeIAf+ZRNvOfkdGobze4GcuXQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT5sCRA9TVsSAnZWagAAPfsP/3ccyxgK8LvZcOCcJrHW\n51vn1c2TD6H06gNF85xl2MFQvxpVouaTypB0h4DXWUoTYJtrFFbZccnG70kA\nhPNnq+GXCx72PpdFNi2EPq9gN3DjN8BDQOw6jXxY1tqu0iPTXRv/Lx8omt7a\nXvTh/3pzcaPgX3o/Lofm8oVLFOMjtyb/XP80oLeQovWY+JxPvhamBkiff4NT\nnsSZ8BRRjZLJssb/l5WY/2YwqOEIOGrPWWDnVI8K/LbeLOE2NpgLQhOXPylO\n4jPSI+Jycsf8GjIUUSgK/aJbXqp/CYXMceMgz6PINrh5QMV4n4zI59YJsF40\nAYJBVS6AsGDiRxhe8nXN8REZ2rMAuYAbnwzcu7qyVAeVQlQGvY9CmsIjcZqX\nM9hjvfRmg8UQ7qAvEPLIX3xdNrxy3ysQ8GVADiBWl/V72WtMZkulJgja7vOZ\nPholszphvOyumCxPoVV9eJEa+DnNhmHcWIgvpDi4hEa5a52vaX7caRjA3sZC\nQ2+ZhzITI5rQDafJ+P7Nc5DgZcUZPpAKUK6vTo5P0drVe4HYh5FALc6EVdpD\nzG7rUuMw5HnBYrx8PZ6MNEsQn52JGGnB9HbS1zIktbxC92CEASJgUdXRyEMi\nLTzaIa56j8RgtmQlf0Gut0F53aIKF/b6vvxCBKAycBHHneCfqk/XE2RP6Pvt\nAPw9\r\n=H/W0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_28.0.0-alpha.2_1645035116839_0.7470878656837718", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "jest-changed-files", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "jest-changed-files@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7a00da99caf3931277772279fad6ae62d864c3e5", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.0.0-alpha.3.tgz", "fileCount": 8, "integrity": "sha512-ip0mVkZDlKsORb0AYdYHRxxabHu2XwKFjTIGF4J0/dEMcvpuSigd5bIRv8J5sig+nYFq6XwXehuhnVqTNWEQKQ==", "signatures": [{"sig": "MEUCIQDacGvi+BN/0N5D3pKewNqKk/XbfXRLz1m6yuJjcpLtlwIgIFybgHH2T9S9z1gqvAgmYD8REXlV6dD+scEdRzVJwv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwTg/8CcM7OJ+7RJ+bIQql2pmF2QLUPNczMGCJQkpTYvvOfsM3ln0N\r\ng4vYZyzdkt7YZbLpvw9u9MoegRxHFkBRIBEc8VkrFGkZXB17EZsIVesPB4cs\r\nHW+2rjldsgPkXdaVWK7bvbhWJdMp5Wp30NHb/WnOHOt8bo1NVcS+Vikb/RSo\r\n9/7UgUMv9qbCS6tY4l9Uy0uoT2PYiJms5x+MVlz2GfPIaXiFfpqhHO4Yz2B6\r\nj9fKLeWlcpAn5I+F/VYgS26ER7gRegw1mC6H1vjD+Nv2UZaEgDXEN6WhePHn\r\naS9viEEFNDxkqFJgpPizVen2X4iB/kB5msqTVygZDP7p8qwCVuULmGt2pNxT\r\nJf5pJqdgeGAkjs3HeCHX5ywJog8u+mxbFFtN3SI4aJvIzyfo3HSvV5RoimvX\r\ndgG2G5IVjI/dJsTGHOJA2iQK4xr/OckZgPlGtdEt6ys7RUzZrsSnC2LGyomm\r\n95zoCq1jlpqsCxWyT2AJdSw00N2ia4MYLRAOmnZscUqMAZyaIYhUPnCrfYF5\r\n01pL2c9xpxy57G2OWePcN0/96N1iRrkXV/T9y+OluqdwUu42lK2M75bkRH5h\r\n9SF/5hUAqk1ProPGSMWK31H2fJABqbAtPRo9ZuP4Qv3wVVYeCZFk5zEA2pCi\r\nugC6xf7sD7kSS5nMdQtsoj3s2394WiQl/dM=\r\n=8trk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_28.0.0-alpha.3_1645112540001_0.22082239992078034", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "jest-changed-files", "version": "28.0.0", "license": "MIT", "_id": "jest-changed-files@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f06ad666ef6ec766a77e13cb3f55bd5c0dec0461", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.0.0.tgz", "fileCount": 8, "integrity": "sha512-9hFz/LuADUTv7zN+t0Ig+J/as2mtILTmgoT2XQdG/ezGbA1tfqoSwEKCXFcDaldzkskZddbh+QI2sACQGaxg6Q==", "signatures": [{"sig": "MEQCIB0pvV8u3AcjB5XMgl3B4VkvQQIAJ+szQzW30fV/g2BfAiA3+ithe4srmpXXogstrVybRuLYJy12KHb4l4PdMk+c6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqocQ//Tp4nig8fd36C59Z4MViUsyqWpUvTPo8xUO4jhPnm34lrg/kq\r\nq6eiKYTYtwQd12mJtjNK5eCy4qzBAnLop54y3oUKCSw20r79rjjv3G4n7HKb\r\nOD9dvw+Fd+2mctYp9LrT4vmpHVlUkbpneOIE86BBdm4vD0mp9t8B9PKkfo3n\r\nySZJdKsXW+7r//jX/pA+10PqY2/tQjejg5x5tIi9S0cL0q9eUyIhWuNzIJYE\r\nHDZgLC6DCJPmLPrAO/tL9SRjGG/BLI6d5gh++ABWk2JucP32bIEAhffoveh+\r\nnWfQd376mS9ktXjjU36EA+s0H3j3WuXxq/EcRqEZXPJEsHbX8gGT/EoTuLfM\r\nO/12SXznyDcPtgVB92nw2xNUWTiEccZZqS/KS9seEMDQfPCxvxXhkmcfyLG4\r\nF3cgf/qg17mwego2wqoMlKKjBVT8iIDK/VDkhwmyPTu01ork+r1eVDr5kVRp\r\ne2gFQ8akxy5yJ6h1BGA1nif77MkjszUWQt0PkoRJQ7NR3qL6VR0ALHKAzyos\r\nONmGdv7EquqTPaxxoLEoqC+pTl3tlrrlNlzEG748aA2zBkCvEjb2G5XA91yC\r\nkNYxmhK0zCkG14FkcBqEPPTiOK3kptKwXMPLUZ16MUyx1NqrJ3on/Cv2as6a\r\n1oH5/i2DYfqmU9HEjGK3/Dy0LUUNGgG6fnQ=\r\n=pcWZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_28.0.0_1650888481780_0.052726343465977044", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "jest-changed-files", "version": "28.0.2", "license": "MIT", "_id": "jest-changed-files@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7d7810660a5bd043af9e9cfbe4d58adb05e91531", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.0.2.tgz", "fileCount": 8, "integrity": "sha512-QX9u+5I2s54ZnGoMEjiM2WeBvJR2J7w/8ZUmH2um/WLAuGAYFQcsVXY9+1YL6k0H/AGUdH8pXUAv6erDqEsvIA==", "signatures": [{"sig": "MEYCIQDBqjrXhYE6wgZVqWHUkcRo784O9Ulru9402mt4E8sXwQIhAIVzEwrslTqLtfJsKCgMFbEgWT3oRWTY7H7K77xlHOh5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqwjw//TEQkpaXeu1dkN/o40N+VWCJsOv+BYt4adJVs3kSbDCSO8LT2\r\nWk9uFnM/deMWSa0ySpqUJwIdRRFk9ctOAFwmf2C5cMDpgDOYakW0SsrTrVec\r\nB9hlwDB4RpAOEgITamPDOAiKY1GOV/qGlro/Rl5gHNrlzwHc3k+wGUhL8QH5\r\n7bb9hOKU6JS97PSTccQt120Agxi1O5LcYGeWNc3vr1Ots1ajuEiVwqElbE+L\r\nylh1EwxJK59RFr4+3mg84TzLbaqVQSMrQIdPVEbOiLA6cSg8VWqAUJs8HC3t\r\nolv42nUOhyBf0DbYFqB6P52rNQ4Hs9DcZQy9SGKVBOa9MQLciFysTRWCVAQc\r\nYxeSN66ABxRFBZt3FlZ3Jtnbobxns9KmYxINk9vO8YnzOnaoafj8JvxmuIaq\r\nriwj2TxhtZJhdjc60ClQUO/GzjbjbcSrSyfa1YromEMSUmqEml+rHOkw6rGn\r\nP5ahKLGQpwIyQg7XX6rtxRkNpKtCwnQBLCqHJG3/myUt75LbeQbNH/5n+AQe\r\nsP+p0/K7HocdAMmpD9DRhreRynzElzyY45DVKM61PZH5iagNAYx06JcyvGpv\r\nE1VaFuhzwvXCikMRNyqRGfHLA9c0hiestQOqdHC36CvVQm5Uak/M5E9F1TVQ\r\nD1AafQgHV6V2nO6YKymqhunSUOaDdX6fNE4=\r\n=lnF6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"execa": "^5.0.0", "throat": "^6.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_28.0.2_1651045440069_0.4554430567304215", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "jest-changed-files", "version": "28.1.3", "license": "MIT", "_id": "jest-changed-files@28.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d9aeee6792be3686c47cb988a8eaf82ff4238831", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.1.3.tgz", "fileCount": 8, "integrity": "sha512-esaOfUWJXk2nfZt9SPyC8gA1kNfdKLkQWyzsMlqq8msYSlNKfmZxfRgZn4Cd4MGVUF+7v6dBs0d5TOAKa7iIiA==", "signatures": [{"sig": "MEYCIQDE9z6wnXbYC83ujfyCZ/DNNAAEacazRieUanlS9Gz6QQIhAP4jUgQqTXmXlNZpGWFfCrP3FbV35gX26IlBRKzvbDNb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmXw/7BPMiHxxwb/5q6ZQKa/f8a0MlGG6a9D9L0166cLoEWjk03qhz\r\nFCqRQ4YGwrttljyOEqt2p67Ei1VcGQvQoNFZTb2pvobtEJgIURsdDrJO6CKo\r\nsPF9NdU/JBA1gTejtgW0fsnTJ2ivCso9mQ0alXIAh3bzs1mIZHbU6JAL1idx\r\ndII3E/N1YSV4kxR7DeIWXBUn78JFQPXeQyu4Sf0+zmkgFBAHCsEZEpZVjsBR\r\nHAFqb4W6xza2z8gJ+nwX9Eqm+OadEAavwDblL2PKo1U6GKp7cLUZh72k5biS\r\nsB/eaeZzqoIf4SyPsSxRhyWqgRvk32L9lzGi1B0+RYSnbm26/7wN49feELnx\r\nf/zRNP7IoKGSBGJTbBN55nUybtRE3g0q+0/2XHxRw71njDMco8seD6DWjTug\r\nlHafbo4N5jbLWVHrGNYYjg603WcXifpF+CXc6WKYs6XM8pyb56RumGiWsLZL\r\nHKO1Cl4NV7tIoSiczP37HH54m+ILVkHPJJlXtuCZADxlmm6O9d7riujMq4v0\r\ne+lQ+LMhO+81WUDMVsxar2TXbu44/3doaCuKSVzrXWAiSTs1kTk2aEzcAuHI\r\nYcOSZAnibRVrsH8Otho44m6pPwoWacxt2KGRrAC5pWDQNPv/E2XKmbrNn+HG\r\nb9sy+uro3ofdbhhKxHQH6QO1nkOC1MmrL3M=\r\n=s59W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_28.1.3_1657721545901_0.7105837684015384", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "jest-changed-files", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "jest-changed-files@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "576c0b4f35704858a8430d5ccd732593c0a9187d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.0.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-haYxV40w1LIxYNWPiznnmDbWWl4MqU+vatnyrjpgVJ7EloguADYU1qFD+7/cMFQZGWs4yAVIgNujUQlKkwm76w==", "signatures": [{"sig": "MEUCIA/prRPe2C6QTD6N/rm6TFd9VXh+s9nF8F8Ox4sApAmOAiEAspP7wTVby9zbPCk16gbrUwJBrR3/nfLt7gUpq4E/qh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtgA/7Bfr5tWjKzK52K7JPREVcj03k5cU2YLWXMgbRvtiRu9qI2oYb\r\nBWG+puw3HOTrfM5LHfHy2NegI9mkrATrz2RbdRvh1kYu8lR37Lx+HXO7PYhc\r\nu8AzaDW1YxWdlnr9wj6D92C3RXMUl6jnGXgAB/r3ryiwJA/ZQZ2G3t2n6rP8\r\nYCaMxjAL0akluOZm93D4XUL6Hn6LTMQjbdHX5jouY8sSo/D2f97APelFhaQR\r\nChqCp5/xLHaxmZ9vDc5yykoaBL39PoR1mLy7lZJgMIPe2j5Oh2QNmw/99xuL\r\n0RBcfJN41ZW3FcKiWHjKlBtxAb7gewNg77wd6zSR9qaEj1JOiJ06jnZxA2BE\r\nfBKWAK5lq3cfS5z91um733MLxFlsVlyhOTYG3KwdDmwWNvYATCfym2hjvocq\r\ne0Z666H+N38uHsUScfxYhK0wU3fIaLJFvxuEws3qCT+sHopwazPKqUw/nBfu\r\nuswLQL3u0c3yrJVDazVz8HU9Uk0NIDch67Ib5jKjjNbyjjdhxfRJbmjS5l0b\r\nIwEHE+21FKZdcCytaknKDLjOzcMVQ5dcWtVxkB27jp/ySPiQBs/0fxdokSbT\r\nRFwCGiLkbBJviTFHer8UkpKbHDVC0u5tFrtUjgpO5hMt4//YAri/6zEq5Q/m\r\nYjvwLbnPhZ9emmK7pj3C/ZOqV+UG5QWIVVw=\r\n=SFf2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.0.0-alpha.0_1658095625779_0.5981355164170583", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "jest-changed-files", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "jest-changed-files@29.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34a48244feab42545fe4e4bae758fc157546fdf3", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.0.0-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-uM3qPjk0SLcgxYMRgCvXLErhMoFfsYgI/p6c+IREqhCeCeodZUZ8OWS1jMgc5jNmXsPBeilGCJPIg1cvHkrmpg==", "signatures": [{"sig": "MEYCIQCckQ8kadQ36bQZbjAc+TIBdGBifH/l3FK40qLKMnt3XwIhAJnnMfTzXb7atfkkbgpVtGj5izNJh32h3bXO9Xmy9iHq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjCA/+LuRcVAmkjg9I1kSuDtGjZ9Gvc9JcPnJZqL8X2YW5T6s4ELE7\r\nsr/YNgeLtSawZ0r0Wyw+L/lC3snHfQB+7VBE2NmKEKZ+CroN4bWaqfk45LO1\r\n1koI03fl36cM/jNknLJ82oMyMbCttdQ9hoRwNiGQQ2NPejmZ4DCHaS6MSpTV\r\nBvgkAhDZ+1kMF17EbhAp/JQ8vjd/gFEgczumjtpCeRCDi81m3aWqF7+Sd2n/\r\nASyj108B/wfEbA6HpdkX49qWFsfNlQqgdkOogXUOgwXt/hxEP6TuXdQWfDYf\r\nBPvRGvXvNTRs7ItTmm6tnBQ6rHzXTppGlKucKW/c4XXJveIPe2uxZcbfeXAU\r\nZ1TNLQHcfsJ9ct0sXBZigfru3eUAUq3x7Lp2tYCR0yARqSXOcHxxF3Z6Bj70\r\nZnmr7DrBmLtEOIxZosxBRsuwvOEvIBNIaBlFeLkaAiTFZ9PJ67EqsUIsLRAq\r\n6FHEmTrtQtznJ8qb5/b0vEayYM+KWR9EFsQajpN8qNVCx0U710fOec70Ud2t\r\nDHbn7y7kGJ+sFKgv/NrPcuYQd8yVS6rb/RF0rjbIjS/ErhGDF8Hzgg9gZw3v\r\nLYeiiCpEahy90t4u+lLNHWpFJExqaq09aAHiQ7/ynImLKp+QB+dNxHMqEnOZ\r\n2/j/xupHHG4yRAnQ4KB2InDSmLgJ7v47JJE=\r\n=jR5p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53c11a22213dfde9901678a3fdeb438dc039066a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.0.0-alpha.2_1659742345710_0.0720787999718886", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "jest-changed-files", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "jest-changed-files@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "75eec5fc33e708697df83c7e7fbfc4a534ee24f1", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.0.0-alpha.3.tgz", "fileCount": 8, "integrity": "sha512-qR9Tl9SZ+hoet7XpnBPoTsYi+E9XKXukqg28f/4GH8oltapQpZxcBQ47XwpHURn0+BzGZcfXvQr+/OuxTmE7Xg==", "signatures": [{"sig": "MEYCIQDJIhloQyidgUAyBSzwMdH7GF0QhBk3pay1CztIdPdr6gIhAPpRw7YD92TrXORQ6O3rwkI1b/lAvBMOvx1bpf2bPkAo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/Hw//eSqEKmjh9xkPlGYYpOsCB2mAE+WO2jLHasN2+cLKETIf8/KD\r\nYttg8IT2UbN58BZWfHiXzXYD9iCAbnKiZk8g2vXA9e1J89YHXWfGLf28AXxb\r\nFTuvOKlWtBM3ZxW8JacMa9JDaS/pfJ3dBIw6x2NH2i8erl83RzjOTelIlFKn\r\nUTmRiuP3NglS6I4V6ooO/6FBPKBsXSHZhaS6/+mSTR6h5pvnmFrG0TJzOAAw\r\nBYI5uUyYicA1Nb0B3QxeUZeqlsMvrwv/jbiXnWIrQXBrFY8vrYLbIWvhiYzf\r\nkO4YGqpRd4NQCSEk7K1Yt7dOp14SxXdMLbEUW0aKLSCGaLeZ3uxKBK9o6ouZ\r\n1CfmDSEx6TAHybblbtAZG05g7UV+HNrVrTcdPK0zhui6S9VYzH9W8P8/fVSK\r\n59BGxhew6IlEncJ0K9/QKT6ipl0G/0bLbkniQuGPBoC2gBELEzXBP8OaqWhr\r\nfGuzzL/iQAP0ZeqKfG5FTvmrq0XUf8vNI5BGbse4i7l6lt26nhyHarkzBXp3\r\n1AyrXmZnEHuhR2GPo5UPrxXPxsrf9SYfwopmf/GfNTpj5ISmgD8D+5+AArsk\r\nYNB+aPsx084EzbhCpwS0HSzADihDmBP6znfoO1z8Ex3HimOT2TIlzoJiWibn\r\nkQyVjuRI6C/V+x5sR+ClQ6ffgBe3i4VR58Q=\r\n=UTrA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.0.0-alpha.3_1659879691862_0.8942269447741027", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "jest-changed-files", "version": "29.0.0", "license": "MIT", "_id": "jest-changed-files@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aa238eae42d9372a413dd9a8dadc91ca1806dce0", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.0.0.tgz", "fileCount": 11, "integrity": "sha512-28/iDMDrUpGoCitTURuDqUzWQoWmOmOKOFST1mi2lwh62X4BFf6khgH3uSuo1e49X/UDjuApAj3w0wLOex4VPQ==", "signatures": [{"sig": "MEUCIQC9O3YrdcrYEzgkM5RZeWSJgRvZpbswuIys1xQz9zOfZgIgY0wpOHo0rNh1GcU2uUKSk0CIt4CYcbK3k/s+OJjnYIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmod0g//Wpyv730pP3Jm3UKQ2QSGUnD2awULV6omZdU5V/A1XWgDVttv\r\nFG/K9TRgTHyOak1Ir4rASVBmiX+SpDLvYS0/KIXKbcq6ciUth/ORDc68WxC0\r\n7aVjQerTynY6foyPLi4TeZvnzmi/PvoOSagOsW8oHhwTUBUE4NEn0nT5vk0Y\r\nOwIZNmo3U8/lKMzvBWE+AGmpDn6bpbvvdlqzXFFThewY0O3cSULgq8wo1g9Y\r\nKurTl6tubq4ONt8XoKgmlLF+B5hPB7AJO/x0hj5v+zErhnJA1yhMDs6sfPeQ\r\n8Z2R3XsbstMhtYiNcEsRRnohncmrDr6GE4aqdBFOwGatqzXxa49qFwnKsDr/\r\nSQ0XgqNECBCnSzIPlgU+ncAm/eMnj5CZjzng8eP2fZmuMinjO5ayxDtDvfb7\r\n4QTVO38MQaTx2U/SQuoiUy7ufzyox4HSZWiefifA9Tzwvh4PbAKVlT9NBH+R\r\nW84J/lfqeVrAX9JMB/+SyVWzTJSB1mkwLdXv0S/Pp0tmefopbKJkuC6Swm0p\r\naQNRueRm7AFb7G9DtyV9qYYJAbMzdqF8ClGkaXA8qcLMePpLMUFhbMFJJ8lc\r\nZXMGzC1EK3GUJPDFhj3PVl8yds63wYRNumbf1vCwrgfUC19u/ONjnR6ZxbxO\r\nceZO4ZwCN77vh8is1heIVIQZrwJucCOqSq8=\r\n=Wd6o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.0.0_1661430804465_0.9027682905191932", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "jest-changed-files", "version": "29.2.0", "license": "MIT", "_id": "jest-changed-files@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b6598daa9803ea6a4dce7968e20ab380ddbee289", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.2.0.tgz", "fileCount": 8, "integrity": "sha512-qPVmLLyBmvF5HJrY7krDisx6Voi8DmlV3GZYX0aFNbaQsZeoz1hfxcCMbqDGuQCxU1dJy9eYc2xscE8QrCCYaA==", "signatures": [{"sig": "MEYCIQDNmJBZMX4JCmIXW2Z8v2CVLuwq8MreBkcKmfD/NCNI+AIhAL6oSyyied4MSESS+CHxH/9XwgbdxMirsDVL7TjvvvnA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmol6g//ey0A9jyvOCKol2l43yCUpL9E74w0iLrPVXH52eyZIXHh3RM2\r\nC/uBri+blEQdt114stooExTYn5gfU9ngZF4mXbEHkUNWgIBF+zODNRQDG5bu\r\nxXniMRxh8bTO/M9WUMmrrZXBJEowas6Wr74KCr0ipDoYfjLUFY/KmzvA1SWx\r\ne0nnfh/i+sTcXgld1iucbEhRNauG9nX5IgfFHqNJgNB29PU4BxYYORuBGJ62\r\nEd3D4CMrH04qR8ARymCSr2u+kSSJPDWpoBknddqpsN1SWsFiCCRsSJpE1kWf\r\n6fWZLZSPxWUkWMib2Nq2mgHCAWIpqjSmn8oUv+50+v56SLR9YCW/vGlrHE/N\r\nL4QnHHLk+V4c7W2BwdWtJaAHDLbQJoObC6uVM/qcqqJdSVtrZKqsYRjedByH\r\nYlpE65yxVp2zHboRCjgTJ56UDCXBF8XQzGH+JuQOFaLyC1SQ5vTwCr7PdXXr\r\nfnmvNYKTCduW51T5xwk9LHn6glrJy6DPAh1hI5riJszjxknQ/rr0Nb+Uo0f3\r\n6oaEBTINefg+F0+7H2PzXUBdhhQeso+tVKH5SD+tQYO7VyWKAmkbN+NmwofJ\r\nwabd2eq5nmWL17SPHyqbXZnp+3fkWZNDBmkU6OhWpxRVbWN4vLjus+pKMjEt\r\nLMu14mUBJzR9qMc4dA6G5i6zWV2qiS4vMog=\r\n=FDoo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.2.0_1665738821270_0.10104644840290966", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "jest-changed-files", "version": "29.4.0", "license": "MIT", "_id": "jest-changed-files@29.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac2498bcd394228f7eddcadcf928b3583bf2779d", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.4.0.tgz", "fileCount": 9, "integrity": "sha512-rnI1oPxgFghoz32Y8eZsGJMjW54UlqT17ycQeCEktcxxwqqKdlj9afl8LNeO0Pbu+h2JQHThQP0BzS67eTRx4w==", "signatures": [{"sig": "MEUCIQDa3hTXGmnstGzDf94q0LCSCtrpi6+dsI76rvyMnwvlNwIgDfvcUttG3KspAUcTlQXWmPaL7AbrlNRGFCQY5z+K8p8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7kvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqggg//fvnuPNyXAsZxSPANGMrE8vXmEjNbVvE6cfQWd0v3eStXsve0\r\n310EdeKBHY9eCpKcDyoiQ3OjBD1K9F2wpnXBRmK149ibDjuQ3WNYWXolhwzH\r\nREXRBuFEpOdH24HGM+tNzu9czhGu9xnrqp3jU+1TpLeF6jqdecsewW4p3fjj\r\n9IX9wwiEo6fBARP7clkAfu7Cz33MokQZi5L5CfpBaGn8QEnnr68syXbglCFW\r\n/Bxi/JNQkhPWjQfoIBu7GSbaTcq/i8OjuJewX6f0t7egtVcSrb7yR/lab0uh\r\nm+cOVRFpoD3J3iLs/WHhFYDy9MFWAzQxj73Hes0VbrJ9PKYp5DSqBHUJsTIr\r\nPgM+GIeZYjGOTXv/zFRk5V+tpok6tmjzcZ+CnJjqna3CzaTbtbl0FayMNQfZ\r\njAPvwEOmJzCDmzE7PE9j7hf0VEjE6hGhVNtDntZieBMvRJgfUqPP211qxnx5\r\nGI+8iN3mXmsanuV+U2f4B/AQ6fBzE8/I1Tqkp5+WbcDIQ8vJnOHQKekJVjwN\r\nfJW4mW3ea+jsFOSGdPumbaCqQjaJe0bUdL/nfY11Kyh4k2Hxlu+CY58KVWB7\r\nvqDsucs98s8EX/LczsFrfMQyn/Km4mhKnZOoxLFGvOaTDHlTjE/fHmpbMEou\r\nwE5tXXxtK8kWyt49wKAWmJMlogLtRgc2hLQ=\r\n=ODmZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.4.0_1674557743397_0.666145041484627", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "jest-changed-files", "version": "29.4.2", "license": "MIT", "_id": "jest-changed-files@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bee1fafc8b620d6251423d1978a0080546bc4376", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.4.2.tgz", "fileCount": 8, "integrity": "sha512-Qdd+AXdqD16PQa+VsWJpxR3kN0JyOCX1iugQfx5nUgAsI4gwsKviXkpclxOK9ZnwaY2IQVHz+771eAvqeOlfuw==", "signatures": [{"sig": "MEUCIFsisrFtWi760JdV63HWkjk5GNKmyFvdhQjsWZvoDfpwAiEAn1YiY6CLsByKP7QHbngsHSE78y/K/945WBqJIrefxao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQjhAAjC9snOKyJGSkrFDOlyr/YPqqXywkovWXCBzF9atmI1d3QXOk\r\nwS4ixo+V0MAs5venaLTVawFInFPdoTq4FfjOmz4l5EvkZj5qHOXsrPqpZI0U\r\nUJ7Moks757cgVzFKoX8tCXUvOSNZ3dgCsZo55/OvaMHhyN+PI5VQzO1jcPf3\r\nX6mvS/7Ca1uMskQPDKDX5NDA1BF/IXavNK33zyftDtRhvaBfDAUKdZrps90k\r\nh1+AsQIu3ExX/FovFspXIFeNjorNO3t9vp09OnoXzSjIV/1bqPgqa2BywFPZ\r\nPmmH656hZ5BjFy+iZqlN3XTzDgYLq3DyU+5HQkLP66COlgMNClfvUFULzTNM\r\njr6sl8PUeo2z2vhvcV+goyxxeRM7ChJOjyncs+pnIGH0VRePijVnnKB4qNuj\r\n5dtmtsvDnaWRhwPPSWHgYjeR7nXi0yd1lQQGL+fpRzj96KJb8qcsRlLeb5hk\r\n9O/VSVAG8zW2NKDiXFwZl2NEk+SJmBwukOvPk32Da4aTifdqxaXoOHhu0RQ4\r\nYPWM7xIFXqB7+8ROV90jqxlK4n5znEAVRc5YAVKuw2L+LRUzied3EvSlAavW\r\n6nJILc7PNQEguq7bjAAdX8L2KMBw0KyKjOjy3zsOZVVV6GAm4x9PuQFwTr1x\r\nBUOniv3ea9yhzOccqDmun5XlUtY/br6ubwI=\r\n=ykjH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.4.2_1675777521816_0.23587246408877416", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "jest-changed-files", "version": "29.4.3", "license": "MIT", "_id": "jest-changed-files@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7961fe32536b9b6d5c28dfa0abcfab31abcf50a7", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.4.3.tgz", "fileCount": 8, "integrity": "sha512-Vn5cLuWuwmi2GNNbokPOEcvrXGSGrqVnPEZV7rC6P7ck07Dyw9RFnvWglnupSh+hGys0ajGtw/bc2ZgweljQoQ==", "signatures": [{"sig": "MEYCIQDJd7asEJEFGxdTRODLj17Su6vos6criDT/GvUkqew+sgIhAJ4GeL91ZQHrcGgPM81BjQCskJw3qhaig+73CMbytUvQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnKQ//bbe5UZYsNnEEh3yfFDS4vuGFD9SvIOipYv70vYIwAoLhx83e\r\n9eVkt/LayfZ//7yWcV+2Hwh610ooa4qbiCz9R62uJcUm462itBcjDTdO60Oo\r\nwQEjvx0sIli2NfngnClTnmosej8i7I52sEJfd+ntPrmNK3InaUiSSzkGTaRN\r\noeuDdIhsi9d+7p6RAMt3qNPhdrSSxlU36R7SwEBzLlGbzZYgZOV4BcL/mo1w\r\nCBQ4mi6DtZRADH+VluC/FADhwmIfrWi8h93NhYcgEMvZo212IbHUBxNSuAEf\r\n4BqnHJK2CoY23k98pUfBvNVJTLPVPTrgtHTc2YbmMDRIXOYl6iFBo9TaWB1y\r\nUPl2lEztGBWy1EqMrxACQigz90Hg0RTGwBQMm9iQXUvcdchJRU0NGThloF9k\r\neYUafrUPt8n5y4bRPB4rNQG0BPh0tDfp/3Y1yV2h3pL8sFpfN1AIJDq5TdE/\r\nfy53Lb4DpF2A1Ed9Cvbq3rJLG4c/GgokhsfG6c/tjXvv4TZbU9ORZOlP0HKd\r\nA5weaSbnPr3utS4L4E8XDqVy/4ND54A101Pll3C87yPCNLGt8cQxWB+7QNlY\r\nXcspEcRM0nkNubt9AeIIsRpH2Dyx7T6NdpL8UEdsiQgjkyTGLGZegzFF3ece\r\nOvNSZb0jN9RgHnVbXRzQOkYvkGbHOzIqxJ4=\r\n=kzeD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.4.3_1676462236247_0.23833298590625995", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "jest-changed-files", "version": "29.5.0", "license": "MIT", "_id": "jest-changed-files@29.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e88786dca8bf2aa899ec4af7644e16d9dcf9b23e", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.5.0.tgz", "fileCount": 9, "integrity": "sha512-IFG34IUMUaNBIxjQXF/iu7g6EcdMrGRRxaUSw92I/2g2YC6vCdTltl4nHvt7Ci5nSJwXIkCu8Ka1DKF+X7Z1Ag==", "signatures": [{"sig": "MEQCIFGAdIGRKa+qlnrIKuuu8hGnqb7eF5Hpm/eu3PVTha0HAiAXGMpnc7OCGNQIhHzhivd/DX93Iy1p6GxksEqUt4Dnng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeujACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHdQ/+NQuAFScp3+YWf9Ez145QinkRUioZNTz++DmPThIKZGxIrmS0\r\n+lcqKKY11apV8I8GWO9oS/qAfBb2kk44CoQEeZubOfLOjEblBPMVqpFE7bJl\r\n9NVTmMfxNBlMYKyUgYlr6j86GYkxUzrn26N5ERVNXHAfwE3keRyajBSb+9oV\r\n8xZloe0rw/N8Mlnw9FXsGgug0DCiE1Jy+IgKujCr8HjDwf8DimS2KczlcraP\r\n9yqARlvuD5+vngRUaQi+07FCfqbNwnbXfcnymrVLns1L05V/Pm76aBU7CHso\r\nX1RFREz47O/nfjga9Oq2dGE1XqO3x5ZDDlvRySQ1q4ChO0uQ1al1La7oWXLW\r\nSEwQgrLTOx90SDit44/U7BtfgfJ31kdevfXN/sJSMo+j7WtQ53ILN96VqhrT\r\n5yqqwFi5nnoEXF/2lL+j/VB8rxElm/w9lUJTluHzqpoU2VSJJyiFSObni8JW\r\nzVL3qodWVpl4Fy6Sghf6kz3MNbT2c9K02vN7s91FxObPrcy5DSvRzkVeQEEF\r\nV/fs9Kcx9xTnCYqvQPpAsWwcrsayW6MAb+LPsPbwVuKGXSg2F0ke0VCMR5Wn\r\nPty0NllK+hhWFZMfEfAzna60S8lqps+9gYz6X0ytlkrgeAxu/TdO07uvOgqM\r\ne3FlZVagavXAFQZeE+nMnHwyDplbWLFAZt0=\r\n=40IS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.5.0_1678109603768_0.679372666467938", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "jest-changed-files", "version": "29.6.3", "license": "MIT", "_id": "jest-changed-files@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "97cfdc93f74fb8af2a1acb0b78f836f1fb40c449", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.6.3.tgz", "fileCount": 9, "integrity": "sha512-G5wDnElqLa4/c66ma5PG9eRjE342lIbF6SUnTJi26C3J28Fv2TVY2rOyKB9YGbSA5ogwevgmxc4j4aVjrEK6Yg==", "signatures": [{"sig": "MEQCICO8JXEKIzqIGlvrDzBP1aVgi+VdQ1Xcl4FhIP01ursdAiBthvKegwH9HBiuD4dffj9w/81cIbNfGaR49/9Bkjlcmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18369}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.6.3_1692621548387_0.5672860777474054", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "jest-changed-files", "version": "29.7.0", "license": "MIT", "_id": "jest-changed-files@29.7.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "1c06d07e77c78e1585d020424dedc10d6e17ac3a", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz", "fileCount": 9, "integrity": "sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==", "signatures": [{"sig": "MEUCIQCOjlH0JSTHcirNjstuOahZ0isYjIIF6Nb2pYDxeKD9cwIgatYY5SToTSdMS1J3tuD+I8usWCK3WORmONj8Nxo27gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18244}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_29.7.0_1694501021515_0.12156264451124787", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "jest-changed-files", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "jest-changed-files@30.0.0-alpha.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "67d794a0d98260c9dc366769d0725d8ad5a76806", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-KMjOYyJutnJXm8uh43ELq0b9agy15M3RRX3wCFCG/3+uZrDiatFIsHlISD50RPz9+LP+8IyrB/55RGIsiOSZfA==", "signatures": [{"sig": "MEQCIAtL7HjoD9ySIwGQLJCHC532rkuskZwt/CljG68dHaWdAiAiHi2JGNgE43uOtQ22KBYGEgS4ZBzCr81vqdk1h1KH4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18438}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-alpha.1_1698672788439_0.5462490924515848", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "jest-changed-files", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "jest-changed-files@30.0.0-alpha.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "63467c03e25f1ab7f1a7ba7965e0678ee7b7ed6c", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-4NQOypdWACbgFZoWVkLe+V+n2B71x/esHJkrFOnoAiPq2vchXRG2bsJ4mtpiSqDWaY1/MOMIMnLc/hy6oX81/g==", "signatures": [{"sig": "MEQCIBJNhcMfG4yLV/KAnpYsgqHjmgdcwjd/FEJ4xCxCbpSUAiAiXGxpo3NPF8xyqcsBEpyasd+QVb1p1fingHUymRO0kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18439}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-alpha.2_1700126898981_0.12323930468453415", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "jest-changed-files", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "jest-changed-files@30.0.0-alpha.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "b942f7f358e84fc4207bfa837dd6ac247358dd36", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-C48A0AuLOacGfzXJp+Ur46ftlylSQZaFaefxnpzBYR117mnH14Br1wBUGEp89hQGVZFpc3ZJGr62u78mSk/Vmg==", "signatures": [{"sig": "MEYCIQCR/U3LwyvgiPFvdpR4I6i129ovEPehGT+j8QkjbCno0wIhAPzR8c7mRjaa9O+xslhmjdhrPVp4rVhzCXyaxXzqslJS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18486}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-alpha.3_1708427337820_0.43402921566521124", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "jest-changed-files", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "jest-changed-files@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "eda505156f5cc37e28bcdf0e6d773464693b83d8", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-GZsW7ctDaXXuA7kQNUJJKiY5KZJVr1Fj1wHNvczaJZ3JdzipP/43uR2sYRDMZUC9EyAQYHM43RW7aAtK0UrEGw==", "signatures": [{"sig": "MEUCIQDh18rJ58j13fhlxmhDus8oZ13OP6eowcqQpZ4T/ZQsWwIgWetuQescOLS/6DyzZJEXBp+/jW8PHYKrIuOgcVsQAtk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18113}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-alpha.4_1715550200679_0.6832959117243513", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "jest-changed-files", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "jest-changed-files@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "97c0587e1ac7e7439894869a7cd45900707019e6", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-B6<PERSON>janWozUsg3tho2CFo4JYZ3iqMOdXexOEre1CQHTUecwAH3uNm9aIy4E1QTX41xEGEbPdBq7b1SHZKe6yPkg==", "signatures": [{"sig": "MEYCIQCxV/Y0/cIDejffwVqBdW6fQIWx6Ro/fnhzsmtWRN+/sQIhAKqPJNYTCEPWIR2xsJHpt2J5pslzr3471PD5Q129Wo3C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18113}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "A module used internally by <PERSON><PERSON> to check which files have changed since you last committed in git or hg.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-alpha.5_1717073041975_0.7051625630798515", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "jest-changed-files", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "jest-changed-files@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "5d5b6ccaee88ac1a4fb60be4e666103f5093f29b", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-Fmyt6W27L4fRBl/gReFC4WU+3XIqB7ySHu+a9QxrERapfCb43o7y81TCvTwJHSw5dxGzXLOObVB0tRMDWMafnw==", "signatures": [{"sig": "MEQCIBNTEgqwW5A0TlVXDjoPGXzZCsy9K6nkO1gQ5O7lGgnqAiAv+/kyQ9a5vF4iBRY8PLVmDqy3gVlMXPtxel/veF0OhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18069}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-alpha.6_1723102983170_0.30329543916599877", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "jest-changed-files", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "jest-changed-files@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "d4374384e0328fcf5f815170d2633508b5bc6877", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-H8LBMHv5FZ+zkSohhq6vYxVlDl4bogTw/8/Cm78Bw+jfTOH+DkrWUESC8GLCDgK2YA12PiczhCrgun9yaBwn0A==", "signatures": [{"sig": "MEUCIApPryid7CZWYMGj2HNUmx4EbJyjbc8QOfKAfjhePL/1AiEAnPK1/CAw1Gw52Gyc3rtsDsu3juLDdYes7Rg3rv0o5xI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18070}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-alpha.7_1738225710066_0.41743737919424917", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "jest-changed-files", "version": "30.0.0-beta.3", "license": "MIT", "_id": "jest-changed-files@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "cac1364039c742e8e581ba2a4ec6f7472cb063c8", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-uawFbHIWBzFah47v1I6O1Toaw86IhQ9eFLC0W1CtVS8e9QoScWi8GVHk2Zibqqr8rFjj8NNWOpD1eN0/k3C+Zw==", "signatures": [{"sig": "MEUCIGjMHN9lzPgZf7Rjyi5FrQNPlPkGDABnCFQQJvTmuhG+AiEA4Pw8X7vzVSJU4N/UNfk5Ev7JshI0ByUWpYjX9/KkKzs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17557}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-beta.3_1748309263285_0.16646441080508634", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "jest-changed-files", "version": "30.0.0-beta.6", "license": "MIT", "_id": "jest-changed-files@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "237908833f05c96b1448c45df3c8433f09cae961", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-beta.6.tgz", "fileCount": 6, "integrity": "sha512-F+AXg5Of0norWZ/9zAx7yELCc4QdiZDqYU/mjx1JL1zZZl+PnVLX80wlAMXwuNu2Mnxp/O+crfKrMfcRe6FVGA==", "signatures": [{"sig": "MEUCICuQO0F9p1Kn4oPHx+4VkANoBpAv8AJPF+UZjUyi+/c3AiEAkvAdL7zvMURy7xDF2qaMyhZOmo6qSr+3bSr54Crp3XQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17568}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"execa": "^5.0.0", "p-limit": "^3.1.0", "jest-util": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-beta.6_1748994646010_0.7178040109672359", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "jest-changed-files", "version": "30.0.0-beta.7", "license": "MIT", "_id": "jest-changed-files@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "3e337c7b99a37e38d1e176b1c9614c5250265818", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-beta.7.tgz", "fileCount": 6, "integrity": "sha512-hrwER8VO2HLoUvBYL2t5JzeRJiywiHnGVXagHnOfQM8qAYkVOGspnOEON/Eq7Ry8M1H4KEhVnVLzg00DaTUvHQ==", "signatures": [{"sig": "MEYCIQCHCQjGH/5xllPbwkSJAYaEquNGR0ajwX54mKDPJ1JWrQIhAN3D5Ru3SHEyaMZ+N9OuYSwVvUBvUQJi8dFZkSX4puep", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17568}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"execa": "^5.1.1", "p-limit": "^3.1.0", "jest-util": "30.0.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-beta.7_1749008138469_0.6341791451165206", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "jest-changed-files", "version": "30.0.0-beta.8", "license": "MIT", "_id": "jest-changed-files@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "4f11e48e7c2a82573bd8f01c261c5769f6f10d97", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-beta.8.tgz", "fileCount": 6, "integrity": "sha512-IZp0/0H56JYO4C2eN/WjYIShOjGd2GUkKFNb2hIwjZjAPFNpcHDro9fhwRQlxW0jLRbsyg0/MrC8+imb9xVg5A==", "signatures": [{"sig": "MEYCIQDJhPl6aMqZ8ERwBQkmSAwGXSKr+h9mkqWopD3bqu1ZsgIhAP5XW1e25OkX3XvdjuZd0WdWqXEtAXOpemXj/DnOaolM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17568}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"execa": "^5.1.1", "p-limit": "^3.1.0", "jest-util": "30.0.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-beta.8_1749023587749_0.46804620452889045", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "jest-changed-files", "version": "30.0.0-rc.1", "license": "MIT", "_id": "jest-changed-files@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "7eb1ad69760442ca930f0d6764a56ec426406677", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-PySTjS6+qlPhIciQRFk4Gb17EzRvzrJkKe5sO1QxWVnwsMt4+/Dgcxp6tNAql83Tbf9tWL15n8vzg8YUUpCDiw==", "signatures": [{"sig": "MEYCIQD59Z8nmHkGciOWeJ7oWEx9v7HaUb2fzYuImnrWVXVZrgIhAIqZ3jSD0RXp+OtNmalT0WIowj0LywKuOVylmQAol6jD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17564}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"execa": "^5.1.1", "p-limit": "^3.1.0", "jest-util": "30.0.0-rc.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0-rc.1_1749430962020_0.9026387387980632", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "jest-changed-files", "version": "30.0.0", "license": "MIT", "_id": "jest-changed-files@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2993fc97acdf701b286310bf672a88a797b57e64", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.0.tgz", "fileCount": 6, "integrity": "sha512-rzGpvCdPdEV1Ma83c1GbZif0L2KAm3vXSXGRlpx7yCt0vhruwCNouKNRh3SiVcISHP1mb3iJzjb7tAEnNu1laQ==", "signatures": [{"sig": "MEYCIQDX+vLn3IeWj4eyjkwP3SSA1uparhS1Elv86eX0KMMjJAIhALX5uoS8E6bkR6G8WEQkUtC7BHQAT4satpblSEx57UQC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17554}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"execa": "^5.1.1", "p-limit": "^3.1.0", "jest-util": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.0_1749521748275_0.05487915649099517", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "jest-changed-files", "version": "30.0.1", "license": "MIT", "_id": "jest-changed-files@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "13d07afb5b04e9af4d295e7546a5b5b76807eed5", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.1.tgz", "fileCount": 6, "integrity": "sha512-5F/T4oaUdWPE6Ttms/hq5M4YVJA1+s1lWqmUK27xfnj1MBy6HmtnRpXXD2KuKZbD5ntwCxTDVAaRrDyIh+HkBg==", "signatures": [{"sig": "MEUCIQCiLAwGilYsQ+G+nSbEF/hhueJCkN3dFNimzE6WtYTRqQIgYMRLLCfuaegzbBhDUpw8HHHLRi+Q7S9StHUyEL37yLw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17554}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"execa": "^5.1.1", "p-limit": "^3.1.0", "jest-util": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.1_1750285886401_0.3983234275657366", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "jest-changed-files", "version": "30.0.2", "license": "MIT", "_id": "jest-changed-files@30.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2c275263037f8f291b71cbb0a4f639c519ab7eb8", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.2.tgz", "fileCount": 6, "integrity": "sha512-Ius/iRST9FKfJI+I+kpiDh8JuUlAISnRszF9ixZDIqJF17FckH5sOzKC8a0wd0+D+8em5ADRHA5V5MnfeDk2WA==", "signatures": [{"sig": "MEUCIQDuoTIBAvuVRHQYlmDJkaTSDps8WwC9wvfXWCkILxcApQIgJu38VIcPX7CurJbBSobsZThru95YnN8PPXVdG5HAlx4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17554}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-changed-files"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"execa": "^5.1.1", "p-limit": "^3.1.0", "jest-util": "30.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-changed-files_30.0.2_1750329975995_0.1968550115114316", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.5": {"name": "jest-changed-files", "version": "30.0.5", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-changed-files"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"execa": "^5.1.1", "jest-util": "30.0.5", "p-limit": "^3.1.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "22236cf58b66039f81893537c90dee290bab427f", "_nodeVersion": "24.4.1", "_npmVersion": "lerna/4.3.0/node@v24.4.1+arm64 (darwin)", "_id": "jest-changed-files@30.0.5", "dist": {"integrity": "sha512-bGl2Ntdx0eAwXuGpdLdVYVr5YQHnSZlQ0y9HVDu565lCUAe9sj6JOtBbMmBBikGIegne9piDDIOeiLVoqTkz4A==", "shasum": "ec448f83bd9caa894dd7da8707f207c356a19924", "tarball": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-30.0.5.tgz", "fileCount": 7, "unpackedSize": 18398, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICjPzJsi/LFstU0F2Lr93PIcstFV7jKe1/BQUY0xRevaAiA2d/HPE+z4ITeDcPHkUVIolXdSAsNfiuJDiHsyaXskrg=="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/jest-changed-files_30.0.5_1753151311132_0.06420427968013387"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-05-11T00:09:43.234Z", "modified": "2025-07-22T02:28:31.763Z", "0.0.0": "2016-05-11T00:09:43.234Z", "12.0.2": "2016-05-17T00:52:48.776Z", "12.1.0": "2016-05-20T18:06:03.663Z", "12.1.1-alpha.2935e14d": "2016-06-17T05:33:14.295Z", "12.1.2-alpha.a482b15c": "2016-06-17T07:58:05.039Z", "12.1.2-alpha.6230044c": "2016-06-21T19:15:16.770Z", "12.1.3-alpha.6230044c": "2016-06-21T20:11:52.154Z", "12.1.4-alpha.a737c6e5": "2016-06-22T03:36:46.702Z", "12.1.5-alpha.b5322422": "2016-06-22T06:45:50.623Z", "13.0.0": "2016-06-22T20:18:18.571Z", "13.2.1": "2016-07-07T01:59:40.223Z", "13.2.2": "2016-07-07T02:13:07.005Z", "13.3.0-alpha.a44f195f": "2016-07-11T09:56:53.028Z", "13.3.0-alpha.4eb0c908": "2016-07-11T10:04:36.248Z", "13.3.0-alpha.ffc7404b": "2016-07-11T10:20:15.296Z", "13.3.0-alpha.8b48d59e": "2016-07-13T06:14:07.795Z", "13.3.0-alpha.g8b48d59": "2016-07-13T06:39:41.371Z", "13.4.0-alpha.d2632006": "2016-07-27T08:57:16.017Z", "14.0.0": "2016-07-27T09:14:38.969Z", "14.2.0-alpha.ca8bfb6e": "2016-08-15T18:54:39.548Z", "14.2.1-alpha.e21d71a4": "2016-08-16T21:18:38.799Z", "14.2.2-alpha.22bd3c33": "2016-08-16T22:54:54.925Z", "14.3.0-alpha.d13c163e": "2016-08-18T20:35:25.645Z", "14.3.1-alpha.410cb91a": "2016-08-30T21:52:08.827Z", "14.3.2-alpha.83c25417": "2016-08-31T18:50:36.966Z", "15.0.0": "2016-08-31T18:55:29.745Z", "15.2.0-alpha.c681f819": "2016-09-29T09:02:18.367Z", "16.0.0": "2016-10-03T08:38:29.861Z", "16.1.0-alpha.691b0e22": "2016-10-28T07:27:27.112Z", "17.0.2": "2016-11-15T00:39:21.432Z", "18.5.0-alpha.7da3df39": "2017-02-17T16:57:33.471Z", "19.0.0": "2017-02-21T09:30:14.562Z", "19.0.2": "2017-02-23T11:36:25.479Z", "19.1.0-alpha.eed82034": "2017-03-17T00:41:16.855Z", "19.2.0-alpha.993e64af": "2017-05-04T15:37:33.306Z", "19.3.0-alpha.85402254": "2017-05-05T11:48:17.647Z", "20.0.0": "2017-05-06T12:32:29.081Z", "20.0.1": "2017-05-11T10:50:01.280Z", "20.0.2": "2017-05-17T10:50:16.015Z", "20.0.3": "2017-05-17T10:57:06.275Z", "20.1.0-alpha.1": "2017-06-28T10:16:14.996Z", "20.1.0-alpha.2": "2017-06-29T16:36:41.592Z", "20.1.0-alpha.3": "2017-06-30T14:20:47.894Z", "20.1.0-beta.1": "2017-07-13T10:33:35.556Z", "20.1.0-chi.1": "2017-07-14T10:24:57.145Z", "20.1.0-delta.1": "2017-07-18T08:46:47.662Z", "20.1.0-delta.2": "2017-07-19T12:56:37.374Z", "20.1.0-delta.3": "2017-07-25T22:12:21.824Z", "20.1.0-delta.4": "2017-07-27T17:19:04.725Z", "20.1.0-delta.5": "2017-08-01T16:33:33.402Z", "20.1.0-echo.1": "2017-08-08T16:49:46.219Z", "21.0.0-alpha.1": "2017-08-11T10:13:58.130Z", "21.0.0-alpha.2": "2017-08-21T22:06:46.326Z", "21.0.0-beta.1": "2017-08-24T21:26:39.146Z", "21.0.0": "2017-09-04T15:01:42.597Z", "21.0.2": "2017-09-08T14:19:02.721Z", "21.1.0": "2017-09-14T01:50:04.982Z", "21.2.0": "2017-09-26T20:22:03.607Z", "21.3.0-alpha.1e3ee68e": "2017-09-28T14:20:26.658Z", "21.3.0-alpha.eff7a1cf": "2017-10-01T16:46:40.390Z", "21.3.0-beta.9": "2017-11-22T13:17:21.674Z", "21.3.0-beta.10": "2017-11-25T12:39:18.811Z", "21.3.0-beta.11": "2017-11-29T14:31:10.628Z", "21.3.0-beta.12": "2017-12-05T18:48:29.382Z", "21.3.0-beta.13": "2017-12-06T14:37:00.244Z", "21.3.0-beta.14": "2017-12-12T10:52:23.276Z", "21.3.0-beta.15": "2017-12-15T13:27:30.444Z", "22.0.0": "2017-12-18T11:03:18.707Z", "22.0.1": "2017-12-18T20:29:16.123Z", "22.0.2": "2017-12-19T13:52:57.358Z", "22.0.3": "2017-12-19T14:58:45.066Z", "22.0.5": "2018-01-09T15:09:53.263Z", "22.0.6": "2018-01-11T09:46:37.717Z", "22.1.0": "2018-01-15T11:57:00.330Z", "22.1.4": "2018-01-19T14:38:22.511Z", "22.2.0": "2018-02-07T10:25:52.511Z", "22.4.3": "2018-03-21T16:07:58.698Z", "23.0.1": "2018-05-27T15:30:37.854Z", "23.2.0": "2018-06-25T14:05:09.573Z", "23.4.0": "2018-07-10T15:52:24.692Z", "23.4.2": "2018-07-27T22:39:14.630Z", "24.0.0-alpha.0": "2018-10-19T12:12:23.736Z", "24.0.0-alpha.1": "2018-10-22T15:35:29.743Z", "24.0.0-alpha.2": "2018-10-25T10:31:40.073Z", "24.0.0-alpha.4": "2018-10-26T16:32:57.962Z", "24.0.0-alpha.5": "2018-11-09T13:12:26.855Z", "24.0.0-alpha.6": "2018-11-09T17:49:25.649Z", "24.0.0-alpha.7": "2018-12-11T16:07:29.668Z", "24.0.0-alpha.9": "2018-12-19T14:23:05.571Z", "24.0.0-alpha.10": "2019-01-09T17:01:10.722Z", "24.0.0-alpha.11": "2019-01-10T18:32:14.312Z", "24.0.0-alpha.12": "2019-01-11T14:58:15.849Z", "24.0.0-alpha.13": "2019-01-23T15:15:15.624Z", "24.0.0-alpha.15": "2019-01-24T17:52:17.116Z", "24.0.0-alpha.16": "2019-01-25T13:41:47.738Z", "24.0.0": "2019-01-25T15:04:42.040Z", "24.2.0-alpha.0": "2019-03-05T14:46:57.402Z", "24.3.0": "2019-03-07T12:59:39.704Z", "24.5.0": "2019-03-12T16:36:26.988Z", "24.6.0": "2019-04-01T22:26:22.712Z", "24.7.0": "2019-04-03T03:55:17.951Z", "24.8.0": "2019-05-05T02:02:21.433Z", "24.9.0": "2019-08-16T05:55:54.585Z", "25.0.0": "2019-08-22T03:23:53.549Z", "25.1.0": "2020-01-22T00:59:53.026Z", "25.2.0-alpha.86": "2020-03-25T17:16:23.466Z", "25.2.0": "2020-03-25T17:58:02.941Z", "25.2.1-alpha.1": "2020-03-26T07:54:23.103Z", "25.2.1-alpha.2": "2020-03-26T08:10:29.459Z", "25.2.1": "2020-03-26T09:01:11.418Z", "25.2.3": "2020-03-26T20:24:48.405Z", "25.2.5": "2020-04-02T10:23:04.452Z", "25.2.6": "2020-04-02T10:29:14.351Z", "25.3.0": "2020-04-08T13:21:11.170Z", "25.4.0": "2020-04-19T21:50:24.652Z", "25.5.0": "2020-04-28T19:45:21.377Z", "26.0.0-alpha.0": "2020-05-02T12:13:00.085Z", "26.0.0-alpha.1": "2020-05-03T18:48:00.492Z", "26.0.0-alpha.2": "2020-05-04T16:05:24.349Z", "26.0.0": "2020-05-04T17:53:04.593Z", "26.0.1-alpha.0": "2020-05-04T22:15:56.766Z", "26.0.1": "2020-05-05T10:40:44.303Z", "26.1.0": "2020-06-23T15:15:10.539Z", "26.2.0": "2020-07-30T10:11:42.011Z", "26.3.0": "2020-08-10T11:31:46.753Z", "26.5.0": "2020-10-05T09:28:14.816Z", "26.5.2": "2020-10-06T10:52:44.483Z", "26.6.0": "2020-10-19T11:58:37.484Z", "26.6.1": "2020-10-23T09:06:01.963Z", "26.6.2": "2020-11-02T12:51:21.559Z", "27.0.0-next.0": "2020-12-05T17:25:14.062Z", "27.0.0-next.1": "2020-12-07T12:43:20.784Z", "27.0.0-next.3": "2021-02-18T22:09:48.681Z", "27.0.0-next.4": "2021-03-08T13:45:07.357Z", "27.0.0-next.7": "2021-04-02T13:47:55.829Z", "27.0.0-next.8": "2021-04-12T22:42:28.512Z", "27.0.0-next.10": "2021-05-20T14:11:16.311Z", "27.0.1": "2021-05-25T10:06:29.892Z", "27.0.2": "2021-05-29T12:07:12.298Z", "27.0.6": "2021-06-28T17:05:36.167Z", "27.1.0": "2021-08-27T09:59:36.490Z", "27.1.1": "2021-09-08T10:12:10.946Z", "27.2.3": "2021-09-28T10:11:21.308Z", "27.2.4": "2021-09-29T14:04:47.266Z", "27.2.5": "2021-10-08T13:39:19.577Z", "27.3.0": "2021-10-17T18:34:46.282Z", "27.4.0": "2021-11-29T13:37:00.030Z", "27.4.1": "2021-11-30T08:37:06.890Z", "27.4.2": "2021-11-30T11:53:39.078Z", "27.5.0": "2022-02-05T09:59:22.305Z", "27.5.1": "2022-02-08T10:52:17.904Z", "28.0.0-alpha.0": "2022-02-10T18:17:30.995Z", "28.0.0-alpha.1": "2022-02-15T21:26:58.140Z", "28.0.0-alpha.2": "2022-02-16T18:11:56.971Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.195Z", "28.0.0": "2022-04-25T12:08:02.011Z", "28.0.2": "2022-04-27T07:44:00.243Z", "28.1.3": "2022-07-13T14:12:26.090Z", "29.0.0-alpha.0": "2022-07-17T22:07:05.953Z", "29.0.0-alpha.2": "2022-08-05T23:32:25.886Z", "29.0.0-alpha.3": "2022-08-07T13:41:32.028Z", "29.0.0": "2022-08-25T12:33:24.620Z", "29.2.0": "2022-10-14T09:13:41.439Z", "29.4.0": "2023-01-24T10:55:43.540Z", "29.4.2": "2023-02-07T13:45:21.997Z", "29.4.3": "2023-02-15T11:57:16.390Z", "29.5.0": "2023-03-06T13:33:23.951Z", "29.6.3": "2023-08-21T12:39:08.565Z", "29.7.0": "2023-09-12T06:43:41.720Z", "30.0.0-alpha.1": "2023-10-30T13:33:08.740Z", "30.0.0-alpha.2": "2023-11-16T09:28:19.161Z", "30.0.0-alpha.3": "2024-02-20T11:08:57.957Z", "30.0.0-alpha.4": "2024-05-12T21:43:20.892Z", "30.0.0-alpha.5": "2024-05-30T12:44:02.146Z", "30.0.0-alpha.6": "2024-08-08T07:43:03.491Z", "30.0.0-alpha.7": "2025-01-30T08:28:30.289Z", "30.0.0-beta.3": "2025-05-27T01:27:43.450Z", "30.0.0-beta.6": "2025-06-03T23:50:46.203Z", "30.0.0-beta.7": "2025-06-04T03:35:38.646Z", "30.0.0-beta.8": "2025-06-04T07:53:07.921Z", "30.0.0-rc.1": "2025-06-09T01:02:42.186Z", "30.0.0": "2025-06-10T02:15:48.456Z", "30.0.1": "2025-06-18T22:31:26.588Z", "30.0.2": "2025-06-19T10:46:16.156Z", "30.0.5": "2025-07-22T02:28:31.563Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-changed-files"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"royriojas": true, "wangnan0610": true}}