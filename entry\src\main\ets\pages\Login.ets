import { router } from '@kit.ArkUI';

@Entry
@Component
struct Login {
  @State username: string = '';
  @State password: string = '';
  @State isPasswordVisible: boolean = false;

  build() {
    Column() {
      // 白色背景
      Column() {
        Blank().height(80)

        // Logo区域
        Column() {
          // Logo图标
          Column() {
            Image($r("app.media.weather_icon"))
              .width(50)
              .height(50)
              .fillColor('#FFFFFF')
          }
          .width(80)
          .height(80)
          .backgroundColor('#007AFF')
          .borderRadius(16)
          .justifyContent(FlexAlign.Center)
          .margin({ bottom: 20 })

          Text("融心天气")
            .fontSize(20)
            .fontWeight(500)
            .fontColor('#333333')
            .margin({ bottom: 8 })

          Text("开启智慧天气生活")
            .fontSize(14)
            .fontColor('#999999')
            .margin({ bottom: 60 })
        }

        // 输入框区域
        Column({ space: 20 }) {
          // 用户名输入框
          TextInput({ placeholder: "用户名" })
            .width('85%')
            .height(50)
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .fontSize(16)
            .fontColor('#333333')
            .placeholderColor('#999999')
            .padding({ left: 16, right: 16 })
            .border({ width: 0 })
            .onChange((value) => { this.username = value })

          // 密码输入框
          Row() {
            TextInput({ placeholder: "用户密码" })
              .type(this.isPasswordVisible ? InputType.Normal : InputType.Password)
              .layoutWeight(1)
              .backgroundColor('transparent')
              .border({ width: 0 })
              .fontSize(16)
              .fontColor('#333333')
              .placeholderColor('#999999')
              .textAlign(TextAlign.Start)
              .padding({ left: 0, right: 0 })
              .onChange((value) => { this.password = value })

            Image('')
              .width(20)
              .height(20)
              .fillColor('#999999')
              .margin({ left: 8 })
              .onClick(() => {
                this.isPasswordVisible = !this.isPasswordVisible;
              })
          }
          .width('85%')
          .height(50)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 16, right: 16 })
          .alignItems(VerticalAlign.Center)
          .justifyContent(FlexAlign.Start)
        }

        // 忘记密码
        Row() {
          Blank()
          Text("忘记密码")
            .fontSize(14)
            .fontColor('#007AFF')
            .margin({ top: 15, bottom: 30 })
            .onClick(() => {
              // 处理忘记密码
            })
        }
        .width('85%')

        // 登录按钮
        Button("登录")
          .width('85%')
          .height(50)
          .backgroundColor('#00BFFF')
          .fontColor('#FFFFFF')
          .fontSize(16)
          .fontWeight(500)
          .borderRadius(8)
          .margin({ bottom: 40 })
          .onClick(() => {
            router.pushUrl({ url: 'pages/LocationSelect' });
          })

        // 第三方登录
        Row({ space: 30 }) {
          // QQ登录
          Column() {
            Image($r("app.media.qq_icon"))
              .width(28)
              .height(28)
              .fillColor('#12B7F5')
              .objectFit(ImageFit.Contain)
          }
          .width(50)
          .height(50)
          .backgroundColor('#F0F8FF')
          .borderRadius(25)
          .justifyContent(FlexAlign.Center)

          // 微信登录
          Column() {
            Image($r("app.media.wechat_icon"))
              .width(28)
              .height(28)
              .fillColor('#07C160')
              .objectFit(ImageFit.Contain)
          }
          .width(50)
          .height(50)
          .backgroundColor('#F0FFF0')
          .borderRadius(25)
          .justifyContent(FlexAlign.Center)

          // 微博登录
          Column() {
            Image($r("app.media.weibo_icon"))
              .width(28)
              .height(28)
              .fillColor('#E6162D')
              .objectFit(ImageFit.Contain)
          }
          .width(50)
          .height(50)
          .backgroundColor('#FFF0F0')
          .borderRadius(25)
          .justifyContent(FlexAlign.Center)
        }
        .margin({ bottom: 60 })

        // 注册提示
        Row({ space: 8 }) {
          Text("没有账号?")
            .fontSize(14)
            .fontColor('#999999')
          
          Text("注册")
            .fontSize(14)
            .fontColor('#007AFF')
            .onClick(() => {
              router.pushUrl({ url: 'pages/Register' });
            })
        }
        .margin({ bottom: 40 })

        Blank()
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#FFFFFF')
      .justifyContent(FlexAlign.Start)
      .alignItems(HorizontalAlign.Center)
    }
    .width('100%')
    .height('100%')
  }
}



