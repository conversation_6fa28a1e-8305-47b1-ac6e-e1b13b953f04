<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .nav a {
            display: inline-block;
            width: 124px;
            height: 60px;
            background-color: pink;
            text-decoration: none;
            text-align: center;
            line-height: 50px;
            color: #fff;
        }

        .nav .bg1 {
            background: url(bg1.png) no-repeat;
        }

        .nav .bg1:hover {
            background: url(bg11.png) no-repeat;
        }

        .nav .bg2 {
            background: url(bg2.png) no-repeat;
        }

        .nav .bg2:hover {
            background: url(bg22.png) no-repeat;
        }

        .nav .bg3 {
            background: url(bg3.jpg) no-repeat;
        }

        .nav .bg3:hover {
            background: url(bg5.png) no-repeat;
        }

        .nav .bg4 {
            background: url(bg4.png) no-repeat;
        }

        .nav .bg4:hover {
            background: url(bg1.png) no-repeat;
        }

        .nav .bg5 {
            background: url(bg5.png) no-repeat;
        }

        .nav .bg5:hover {
            background: url(bg2.png) no-repeat;
        }
    </style>
</head>

<body>
    <!-- 使用类选择器不用加点 -->
    <div class="nav">
        <a href="#" class="bg1">五彩导航</a>
        <a href="#" class="bg2">五彩导航</a>
        <a href="#" class="bg3">五彩导航</a>
        <a href="#" class="bg4">五彩导航</a>
        <a href="#" class="bg5">五彩导航</a>
    </div>
</body>

</html>