# 方法的重写：对继承的父类的方法，子类重新定义一个同名的方法做覆写，不是重载

![image-20241127161002310](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241127161002310.png)

```java
package 继承;

public class person {
    private String name;
    private int age;
    private double height;

    public String getName() {
        return name;
    }

    public int getAge() {
        return age;
    }

    public double getHeight() {
        return height;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setHeight(double height) {
        this.height = height;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public void eat()
    {
        System.out.println("去码头整点薯条");
    }
    public void talk()
    {
        System.out.println("我爱说实话");
    }
    public void sleep()
    {
        System.out.println("一天到晚没事就是睡");
    }


    public person() {
    }
}

```

```java
package 继承;

public class student extends person {
    //定义学生额外的属性以及方法
    private int sno;//学生编号

    public int getSno() {
        return sno;
    }

    public void setSno(int sno) {
        this.sno = sno;
    }

    public void study()
    {
        System.out.println("直接开学");
    }
    public void eat()
    {
        System.out.println("我吃的是学生餐");
    }


}



```

方法的重写：与方法的重载不同，在子类对父类的方法的重写，不是同一个类中的方法的

1.重写的方法，返回值和修饰符可以不一样

2.我们使用子类对象时调用的是子类重写后的方法

