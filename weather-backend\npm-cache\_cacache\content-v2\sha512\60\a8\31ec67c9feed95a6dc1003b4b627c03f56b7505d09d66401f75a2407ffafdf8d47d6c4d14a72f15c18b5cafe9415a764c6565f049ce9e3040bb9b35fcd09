{"_id": "path-to-regexp", "_rev": "300-655f36b60935a577f5d2c4c3920ae60c", "name": "path-to-regexp", "dist-tags": {"old": "6.3.0", "latest": "8.2.0", "express": "0.1.12"}, "versions": {"0.0.1": {"name": "path-to-regexp", "version": "0.0.1", "keywords": ["express", "regexp"], "_id": "path-to-regexp@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2383ddd9c24c6ecf8bc9e39711e3ecb37c61c4cc", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.0.1.tgz", "integrity": "sha512-5rRtUuKZCAuRQXzKcKSh5WObg6h+d59kgAy+pfymaBkqmIaf/MkwGh9jOoW6JCuvil3LMvdT+sLI4I1uK/hWNw==", "signatures": [{"sig": "MEUCIAjJA353Z5NLH/fnq/Mg3+IFegoZ/yoRKO1zmHXanT7eAiEAxYJX4bXY1hsYtuoJrrcwr65BXgVS8p3Z/+gfMQDfB78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "description": "Express style path to RegExp utility", "directories": {}}, "0.0.2": {"name": "path-to-regexp", "version": "0.0.2", "keywords": ["express", "regexp"], "_id": "path-to-regexp@0.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "489feb060b314443a5494ab1da2efed2040ab24c", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.0.2.tgz", "integrity": "sha512-oElk+kl0DoaEHoLJNXJ+YwH5vJSvU9sNIfnBC1DcwfJ9dOA0MLqZjVYfq/wfbJwpeDkD+3nfR9pfL8wLfrZKLg==", "signatures": [{"sig": "MEUCIH1240qGeKSyDaiWXY6KCiabJCWeEkbd/5qHwi3AjpFLAiEArR0WMLKvWumN2u9LqvuJZSNvgoJxHmXZpXVPKiOxh4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "_npmVersion": "1.2.2", "description": "Express style path to RegExp utility", "directories": {}}, "0.1.0": {"name": "path-to-regexp", "version": "0.1.0", "keywords": ["express", "regexp"], "_id": "path-to-regexp@0.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "23dd6da3e04f2a3e97ba275e7c025c918b50a46a", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.0.tgz", "integrity": "sha512-2yBUJ1ecZ2dCtWg4hap+nXVNU5DJ7su9rZVhW2MpzbIF9IKLJoLSyiG02Ay8Kr63p//ZWal9SoWWHBYd31BmJg==", "signatures": [{"sig": "MEUCIQDxH2+P+AiOehUKEpYHy5iz4/LBOhA3R14u83TA+oBLkgIgZMF5Aye3tkGwDj8CuhlElM6YA/hmbtikgxDlxfqifxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "Express style path to RegExp utility", "directories": {}}, "0.1.1": {"name": "path-to-regexp", "version": "0.1.1", "keywords": ["express", "regexp"], "_id": "path-to-regexp@0.1.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "27d101134fd0fda80923cf2102bc12529841002e", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.1.tgz", "integrity": "sha512-24vSprhCSutOfwXE9wTSukb0vPRIvkANt2jJoe7L6FVrAk9qzdR0tPz3LOWtI3R5fxeqnYEvGmyW8hg0+ODmlg==", "signatures": [{"sig": "MEUCIQCQVQ4L41jnQERvRVIK6/4qz8MMPqx3/6WyZxfaYA5oxQIgRwVNBLai6/KwcToMsU2CB+GebsyN2P9dL4T7j2SP7bM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "Express style path to RegExp utility", "directories": {}, "dependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}}, "0.1.2": {"name": "path-to-regexp", "version": "0.1.2", "keywords": ["express", "regexp"], "_id": "path-to-regexp@0.1.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "9b2b151f9cc3018c9eea50ca95729e05781712b4", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.2.tgz", "integrity": "sha512-BZU7Qr+qKkXJX9UBypMNikdZ85cQSjtfMhUykJFLJn4SNF0jhEbb9nMRpnNdA76ESryY8JpMA4k6XKdz3JS1pw==", "signatures": [{"sig": "MEUCIADlHAc0JYXec4d39uaTYg2DeUVYXamAqnNKSxUtNBt0AiEA+vsugiUtR6lAuCgOlagG0G1+jBWCFVih1OrnuMpbpqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}}, "0.2.0": {"name": "path-to-regexp", "version": "0.2.0", "keywords": ["express", "regexp", "route", "routing"], "_id": "path-to-regexp@0.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "8ac7593477f8c321dc5a2aefffcc28e74cdf9c82", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.2.0.tgz", "integrity": "sha512-zULpMPDIHTXVYYCRSIkUcVaBkMPPIwlq5EOFlKuIuMYRZTDuRwH6UCpa8uqAZMNY4YVQ7ep7oaJB6rNEe4aJCA==", "signatures": [{"sig": "MEYCIQCJdOWyB1BWPgtP47zZIwJ95fT4vVcfgCRYKDkDER3JfgIhAPoEjYD9UwkVJT2Ep8fevoL8QnoPRlVIr/uuy8kOBPEe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "8ac7593477f8c321dc5a2aefffcc28e74cdf9c82", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "~1.18.2", "istanbul": "~0.2.6"}}, "0.2.1": {"name": "path-to-regexp", "version": "0.2.1", "keywords": ["express", "regexp", "route", "routing"], "_id": "path-to-regexp@0.2.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "d7e13bfc1ff9082d6723a27b54b7ae6bccbe80e3", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.2.1.tgz", "integrity": "sha512-I/adsr38EzS1uxrqJzQEiswCNt5ByR4ecr9lBTZB8kHnoyyVEyjFCQ3nHs2XsnxwNKHqfHHkQ9bTKyy0b9/PXw==", "signatures": [{"sig": "MEYCIQCok17jk5R/6CZnViLGSy0AnG/zR8+QFo57gyGHswHkagIhAKAhPUtCvMJZBYprxY8SoFmXv9ASh7fX9WYTJcw1Tqn7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "d7e13bfc1ff9082d6723a27b54b7ae6bccbe80e3", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "~1.18.2", "istanbul": "~0.2.6"}}, "0.1.3": {"name": "path-to-regexp", "version": "0.1.3", "keywords": ["express", "regexp"], "_id": "path-to-regexp@0.1.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "21b9ab82274279de25b156ea08fd12ca51b8aecb", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.3.tgz", "integrity": "sha512-sd4vSOW+DCM6A5aRICI1CWaC7nufnzVpZfuh5T0VXshxxzFWuaFcvqKovAFLNGReOc+uZRptpcpPmn7CDvzLuA==", "signatures": [{"sig": "MEQCIGnvoWup2ljywFzc/QcpMYHxRo5LV9h5cerGEzgub9BRAiBl8VjA0btQ/h93daeiXXSfRLTeK9ge3KO9LNPxsVWGlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "21b9ab82274279de25b156ea08fd12ca51b8aecb", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}}, "0.2.2": {"name": "path-to-regexp", "version": "0.2.2", "keywords": ["express", "regexp", "route", "routing"], "_id": "path-to-regexp@0.2.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "605fcb541f6ae51fdd0643e00e0f1453fb56c1ef", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.2.2.tgz", "integrity": "sha512-8ASTOsBzNVHDgJxXi34U8GYTVVTzbuLmDl8Hg+BFYq/GkH1AIam8FceV+Q4otBuIdvyyCNhnkdK/RKdswUHyyg==", "signatures": [{"sig": "MEQCIEfBwOzHNzHEp7jhJ+Y31ugrpuLkBvl7MjBF3+qYG+32AiAZWGiK70n5HzvR+D4cniCcYuUELlp9yAuZw16Z5g04BQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "605fcb541f6ae51fdd0643e00e0f1453fb56c1ef", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "~1.18.2", "istanbul": "~0.2.6"}}, "0.2.3": {"name": "path-to-regexp", "version": "0.2.3", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@0.2.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "b695cd2d139d3b502ede11fdaf5326c05b48fd04", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.2.3.tgz", "integrity": "sha512-yALCQuaZEi2Rzo39vxVrL7hPZfjtSIlAmCctRlYywtLTRBm8gN/p4t3BG8WT8R1R15NyacypPG2Xr/r8jm+f8g==", "signatures": [{"sig": "MEUCIQDYgxeVRXqdhKyiO+WgVBHkiC52XOg1hyuLp0XoiTkdjwIgDVhn4V7iSCtDURge9nlGFG2qr/2t61SzHIW3GNKTk78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "b695cd2d139d3b502ede11fdaf5326c05b48fd04", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "~1.18.2", "istanbul": "~0.2.6"}}, "0.2.4": {"name": "path-to-regexp", "version": "0.2.4", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@0.2.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "5a56488dae6f4ddabc401729a79e3bb829db9dc0", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.2.4.tgz", "integrity": "sha512-UQCbVPJ0hzX+aDlFZTsg03zzoEqcaq2+1UQE/PPYJATLikSj5wuQcsS7EAUFiGolwMqf+5thGKSgHBBQlmrxRg==", "signatures": [{"sig": "MEYCIQCNxz1LoULU9T4yFXK5Qu0/if0OS35Wlad6eENcqzABaAIhAI2SQONKz1HDjGwrX9qFKwaXKtTrkzD7QM9p6RqmswYJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "5a56488dae6f4ddabc401729a79e3bb829db9dc0", "gitHead": "877ca4b845d2112150900ed4926e6dca5951613a", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "~1.18.2", "istanbul": "~0.2.6"}}, "0.2.5": {"name": "path-to-regexp", "version": "0.2.5", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@0.2.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "0b426991e387fc4c675de23557f358715eb66fb0", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.2.5.tgz", "integrity": "sha512-l6qtdDPIkmAmzEO6egquYDfqQGPMRNGjYtrU13HAXb3YSRrt7HSb1sJY0pKp6o2bAa86tSB6iwaW2JbthPKr7Q==", "signatures": [{"sig": "MEYCIQCX9dAUmn3R+evYjl74nHSyV9rdSPEmFssQ4NzA2Wb9MwIhAO4gmS2mQsWAFhKYxnn0H8qXpNn2MWNkPdZsBkxyzIym", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "0b426991e387fc4c675de23557f358715eb66fb0", "gitHead": "fad140982d9baddfcf398bf7ded44b7cdbb7cf8b", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "~1.18.2", "istanbul": "~0.2.6"}}, "1.0.0": {"name": "path-to-regexp", "version": "1.0.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "timaschew", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "6fc04df3f802bcb3e76ef65ec75de2aae38f4a26", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.0.0.tgz", "integrity": "sha512-Mfo1en3p+yNlYf75wcX2r95BI69tAwaVeb99gFIuL/woTMiS7wNhUXTdsyohZ9E763LvDudURYJfndaxZ7RKog==", "signatures": [{"sig": "MEQCIH0vW4L6mYz57joFgTeUVmh6O0Gt196vEqtjgzJ5JMj+AiBbQ/6xo/9qOKypqvyjEK/p/vaugHJH5qEUdnHzxj3FhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "6fc04df3f802bcb3e76ef65ec75de2aae38f4a26", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "^1.21.4", "istanbul": "^0.3.0"}}, "1.0.1": {"name": "path-to-regexp", "version": "1.0.1", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "timaschew", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "0b87a97d09ed6c301508e710272852b24360c8b2", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.0.1.tgz", "integrity": "sha512-c4/gnbQ2ROUHwgoa7OguiHy+Hl84kRz15Ou3rVSKUrxLfBiSn/YDyryoMrmF8AQSnBYvgAlLKcHlYjOkh/3MqA==", "signatures": [{"sig": "MEUCIQDfrOYzFkHoz1jWUDAYnZzsRnygVc8oZ+XDkIDohDNFZQIgeDzgfJ9rHLNEW5mWLOVy1DSnUBvluPVxvjUCRzZvPi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Express style path to RegExp utility", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.0"}}, "1.0.2": {"name": "path-to-regexp", "version": "1.0.2", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "293be955eabc0504906e0f9e129dde8ac111a21f", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.0.2.tgz", "integrity": "sha512-itqtdWw+lLJYRHwMHODNR+RZUAaDMYVGLQiaOEW3YQj7MsIIFGKLODalwAhfvE2uKi87usVIAaZyJU1HQh2xng==", "signatures": [{"sig": "MEUCIDxOcDqeG0tzp2EHrvC/jI5Ql2eJh7HOYBtZYAmOk9RuAiEA62Vb5Pn6r01E/Nwn/br/Hm0tnnYS6ft3UgO4QmRg3Tg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "293be955eabc0504906e0f9e129dde8ac111a21f", "gitHead": "59cb06498efcba7f7b73608fe675ccc663b660f2", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Express style path to RegExp utility", "directories": {}, "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.0"}}, "1.0.3": {"name": "path-to-regexp", "version": "1.0.3", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "eea5a32cf82b7141d4987bfe7e0557990e2d260e", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.0.3.tgz", "integrity": "sha512-S/tUBpRfSQMr13Q1SWARN0Vns2+2obEA/u69SWYVVB09oV8vWpoMA+JVQ+ue4UwqN6iY48lTqx5H8TT1XfSoUg==", "signatures": [{"sig": "MEYCIQDloXZN98Fh3MSBLBHk6P8bERJv5qJmcBpGaDABhwIjKAIhAM/mqoZapESTgWoucQ87kG2ytYaljDzeVnJr5KHy5nI9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "eea5a32cf82b7141d4987bfe7e0557990e2d260e", "gitHead": "a76d908bf45b1534f10701bc5ba0f40567097274", "scripts": {"test": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.1.17", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "0.11.14", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.0"}}, "0.1.4": {"name": "path-to-regexp", "version": "0.1.4", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "65868166d96fd548de3bbe7dc8e8ab694a8bda57", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.4.tgz", "integrity": "sha512-ipFmXkWHafNVZwQt8/hFoSOY0ebNgRss5HfAxf1ez2yNV08b93oywkxUu7WTbG4OSunntF1uAOfxCxWivrek6w==", "signatures": [{"sig": "MEUCIQDj+iEZHlAHXwH0BF4liZCKSE2NkEC++2tDXki6/lViJwIgUEN0Jglxrv79PGsI2MBSZ2BERkrnw5bq40W/b0jQuws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "65868166d96fd548de3bbe7dc8e8ab694a8bda57", "gitHead": "66f8d3f63541b176a7aadbe69e0cd9f78fe206ce", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}}, "0.1.5": {"name": "path-to-regexp", "version": "0.1.5", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "a81f223d192e0cc6a92ef619633cae1fede52c5d", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.5.tgz", "integrity": "sha512-8x+GUxAaDYB57BlfGT4inO3/5nx4l/OAUKNTLWvn6UOyzr0TI1k5Cs0aSSXs7nSBzNWIb9w74KhiDi4HeBVYAA==", "signatures": [{"sig": "MEUCIQCZdKDjkbK6irE4cHaRbqgQdh2sK2QJeFkLN7xVzcJvzAIgVngw6rDGPgzauzb/hDYW94GN1ZqXZ0Oy1RQyQZqe3sQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "a81f223d192e0cc6a92ef619633cae1fede52c5d", "gitHead": "fa40b5f34d507a7afdef9dc8ae78f847801e05a2", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "1.8.1", "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}}, "1.1.0": {"name": "path-to-regexp", "version": "1.1.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "40c5a8aed1298e44e097b8dcbd2d2697b83d89e8", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.1.0.tgz", "integrity": "sha512-MeCrvFjNHafbcSXFQdaxdJH7ti3X2zxnqYcOf0mjxnFkA3pMhz1ZZRVhW/lwKxE7/I3iWQw3cYjsMOZOxIkfzA==", "signatures": [{"sig": "MEUCIFy/cnuSdGvQfZwr6HrfM7NK8JBBdmtev9AuAapTbIqjAiEAkmL+TGttpc8T2YdjVT0EQ6lFcztt0TH54d0uQnRd2zY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "40c5a8aed1298e44e097b8dcbd2d2697b83d89e8", "gitHead": "c368dc9a90ee0e5b8cafb9f8f25d7c86dc8bca16", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec", "test-spec": "mocha -R spec --bail"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "1.8.1", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "pre-commit": "~1.0.5"}}, "1.1.1": {"name": "path-to-regexp", "version": "1.1.1", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.1.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "8dd70fdecb4da27858aee1e5e3b6f0eda8f45a35", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.1.1.tgz", "integrity": "sha512-7BOJF/AASK0xJy/AhzCb/szQmMB1O/AjOucqCK+JEyvJVmkn33pKFhLk70TGUbQCFMXFYL+EOC4frWluFNivKw==", "signatures": [{"sig": "MEUCIAGPsWugEMl1zinaej4oQ35oii+afEvW5P3vH7phsN63AiEAjNKwqCNur+1H1ohgzYLF9lU1BWXPGfGDKjlJJknXJDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "8dd70fdecb4da27858aee1e5e3b6f0eda8f45a35", "gitHead": "5ff1028cca4fc7440bf56f44451052ba67c215ca", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec", "test-spec": "mocha -R spec --bail"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "pre-commit": "~1.0.5"}}, "1.2.0": {"name": "path-to-regexp", "version": "1.2.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "81da890cb13bacc657670e0cfeecc90fd703b387", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.2.0.tgz", "integrity": "sha512-AYPBmMLhJh8EGVk4HgZubjwY0mFB1rEAaBnUAT/7b03m7ugK3KgNhSBUg2YXlNx7w+Pq/er4qtmWJoANQza2sQ==", "signatures": [{"sig": "MEYCIQDd8SpC7OCeomAvTpecnOtHBfS34h3ONPfrIkoUp+z/JgIhAJsd7eu1onP+77s9qAD1860O/yBA76XwBVfTNf92H+zL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "81da890cb13bacc657670e0cfeecc90fd703b387", "gitHead": "7aff887e73ee8bca5cc98ee6239616da07eb8523", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec", "test-spec": "mocha -R spec --bail"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "pre-commit": "~1.0.5"}}, "0.1.6": {"name": "path-to-regexp", "version": "0.1.6", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "f01fd5734047b6bfbc5f208c6135a33d7af09c36", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.6.tgz", "integrity": "sha512-Ut++lkiF1BogIUY+3SADhfDDdvzb8EOU/D8JutrlrfEuh+DGQbo5c78P96dSuapLEfeH7rpWhiBfJCDAGvSJVQ==", "signatures": [{"sig": "MEQCIAGUGkfPz6cCfiOE5KOaL0kmwd0mT6BQKCWRjGbKbj2DAiBAaRL4HWCTGx7A3S0EPUexawuwTONUdFkCXVPAh5pdwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "f01fd5734047b6bfbc5f208c6135a33d7af09c36", "gitHead": "41abe347ea83b203a711856df51c50a51deb03a2", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}}, "0.1.7": {"name": "path-to-regexp", "version": "0.1.7", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.7", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp#readme", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "df604178005f522f15eb4490e7247a1bfaa67f8c", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==", "signatures": [{"sig": "MEYCIQCNgJyJuCxu+89U9T456yiYOG57PnK543ETAiv+XAbWdgIhAJ32XMZU6nL0LBHsGf3TeOEsChzN1nT7pw52oZMg/J/v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "df604178005f522f15eb4490e7247a1bfaa67f8c", "gitHead": "039118d6c3c186d3f176c73935ca887a32a33d93", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.13.2", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "2.3.3", "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}}, "1.2.1": {"name": "path-to-regexp", "version": "1.2.1", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.2.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "amasad", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "b33705c140234d873c8721c7b9fd8b541ed3aff9", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.2.1.tgz", "integrity": "sha512-DBw9IhWfevR2zCVwEZURTuQNseCvu/Q9f5ZgqMCK0Rh61bDa4uyjPAOy9b55yKiPT59zZn+7uYKxmWwsguInwg==", "signatures": [{"sig": "MEQCICRyiC82Fo0EP/RccAfzXc53X4jjEiMAo0yyE0nTBV+sAiAAxF4xc3JdsHSDG6GwiNVdSqrXKsa74KYVlJUmLuU6wA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "b33705c140234d873c8721c7b9fd8b541ed3aff9", "gitHead": "484d7a85329fa5f741fa7bd1d6272fbdff00448c", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec", "test-spec": "mocha -R spec --bail"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "pre-commit": "~1.0.5"}}, "1.3.0": {"name": "path-to-regexp", "version": "1.3.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.3.0", "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "cristian<PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "b32ddce482da48876c3e5677447b0213e694c7b8", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.3.0.tgz", "integrity": "sha512-U51em6NMu85voQ4A2oFo0gHvdmqCrd6nXJnKFBqQUEk+KopZ/sjUvcOUX55blUH9gDSes0AboXN6DmrjmkaUyA==", "signatures": [{"sig": "MEUCICkjKSzfdYt+tvHdlM4Aa1wHnQ3ksAOEuDRFD/7+gd4cAiEA2CkVPEzRq4N8hBYkZHJ2BInG0wASWsSkVCxffACmmsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "b32ddce482da48876c3e5677447b0213e694c7b8", "gitHead": "b6a4dd1216e5ad6ca93944fef4987d4b96499bc1", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^0.6.9", "istanbul": "~0.3.0", "standard": "~3.7.3", "pre-commit": "~1.0.5", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-1.3.0.tgz_1462745824858_0.*****************", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.0": {"name": "path-to-regexp", "version": "1.4.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.4.0", "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "cristian<PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "0820f32b4d2338cbbb8a12b614d20ad59457a4ef", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.4.0.tgz", "integrity": "sha512-gWLzkn6FMO9CjhtQBK8Es0KhZsASlvU8lKMX6fv2CNTT6WYXJW0zlnZP33mkoDNAY7el4erlnFhaw35b1DT65Q==", "signatures": [{"sig": "MEUCIALifrG2BxbTWQ603rKHwBKAsDE5dNJuhVngaeBkra01AiEAtj8MVYVUM7ADqxs+A4OrpyJOfdbRKKw2yTlI3dDvLh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "0820f32b4d2338cbbb8a12b614d20ad59457a4ef", "gitHead": "27d8e89b77fe9c8dc51ca66d6196cee5b7842a50", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^1.0.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-1.4.0.tgz_1463636268107_0.****************", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.0": {"name": "path-to-regexp", "version": "1.5.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.5.0", "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "cristian<PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "c38e7efa3c00dda2e61f41addf74babbbdb69ca2", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.5.0.tgz", "integrity": "sha512-Q9x+yEZWhbTbSjLRM4sWAEGq/QVH5ZRwZLTVIp3NfhXVs8+WnK59YqawmVlhseh/Wgm9JZMft6ToSmkuS9KrBg==", "signatures": [{"sig": "MEQCIHDFpgIw9H9HHRTkuGsLP2zNyx1xMwnKP15O6AYwaJfbAiBiTvPMS+jWxzEkjFMyCOJBW7PRiygomztCdGW8yySfxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "c38e7efa3c00dda2e61f41addf74babbbdb69ca2", "gitHead": "f6e1b2a5185f932b70e1f75f24acba5caff008bb", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^1.0.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-1.5.0.tgz_1463767793397_0.*****************", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.1": {"name": "path-to-regexp", "version": "1.5.1", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.5.1", "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "cristian<PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "625f98affdf68b3df2191b6a0fd9dc922335db53", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.5.1.tgz", "integrity": "sha512-czcbtYh6gw6X687/YV+fp//UbYOI6CubPjM1KArTufTzwM16UOWNawCSDsA+IQdOJZsiDYJH2akoEdSdfxRXbw==", "signatures": [{"sig": "MEYCIQDJfU/qGW6e+mBtgki5FqHenXyaXa7RkypG92rIY/Yv9QIhAONHRu/OHgmTIH9g0hsO0zWQnxAoNV9acOmP+lVGdk1g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "index.d.ts", "LICENSE"], "_shasum": "625f98affdf68b3df2191b6a0fd9dc922335db53", "gitHead": "d933b45c24d79d58fc808d0580fa092b7b9300b4", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^1.0.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-1.5.1.tgz_1465400064074_0.****************", "host": "packages-16-east.internal.npmjs.com"}}, "1.5.2": {"name": "path-to-regexp", "version": "1.5.2", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.5.2", "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "cristian<PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "97743e23874d7a85f22807535389f1e1aa12280e", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.5.2.tgz", "integrity": "sha512-+tU4DFVGCbfo1WmRM/Iv9csSVzsN27d0uAQa8R1gsBV5zULz7p24JolGs56twNtJchk8zQbMmsxKpUN9eF13rQ==", "signatures": [{"sig": "MEUCIAcXIo5cZuRbaH/zHBYp4nbR9dAV/wlS/BvXHnoK+2dNAiEAuiDBh9fPaGQnrmgUHwJg/JV4zu01xwE5gJDmrQIkLVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "index.d.ts", "LICENSE"], "_shasum": "97743e23874d7a85f22807535389f1e1aa12280e", "gitHead": "e2470a5ab8fd18b3c21b8d61bc1a2c4fa63b5110", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^1.0.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-1.5.2.tgz_1466042132651_0.*****************", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.3": {"name": "path-to-regexp", "version": "1.5.3", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.5.3", "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "cristian<PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "7221ddd42483538bddf9fead942a79ff3164f57a", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.5.3.tgz", "integrity": "sha512-bqgexHATMvdKmLuLFDxO7cEy6zGYVuURhAbJZYVbBR6XnX4KmXXBOt0OKVaDGOJ5l5UY86OXpKE2RHD30TbbOQ==", "signatures": [{"sig": "MEUCIBP86xA2jYEcHZuhDk8d+N5Cyqs3akYF+axmcm3iMaQLAiEAsmIz6m1JYQprAnWM4odu6w661ilb5PwZ016GbUEPmKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "index.d.ts", "LICENSE"], "_shasum": "7221ddd42483538bddf9fead942a79ff3164f57a", "gitHead": "7bbe1ba23ded0848b1d10bcab7504a127359a014", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^1.0.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-1.5.3.tgz_1466048195033_0.****************", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.0": {"name": "path-to-regexp", "version": "1.6.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.6.0", "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "cristian<PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "4c59cfeab5e360a2657b180730a4bb4582ecec5b", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.6.0.tgz", "integrity": "sha512-gBSbrnOy4LjIgDE3vMLh41A+dJoi0GLTb+Fb8NSHnSO1r4ps1Kn3KYp4OPMSotbNTqdxkAB99N6d27C6j6zz+Q==", "signatures": [{"sig": "MEYCIQCoLIMx2mrI6hFRvDHC6cgPLt6hnRTYUS/MWmNcZUawEgIhALlcY11eZ5dcBMd+WvQpNsbsNpGQE8Z6CdWUiq0DlNLf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "index.d.ts", "LICENSE"], "_shasum": "4c59cfeab5e360a2657b180730a4bb4582ecec5b", "gitHead": "bdf17de3dfcf62b410e7cab15998c6e32361c7f9", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^1.0.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-1.6.0.tgz_1475519937646_0.*****************", "host": "packages-16-east.internal.npmjs.com"}}, "1.7.0": {"name": "path-to-regexp", "version": "1.7.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.7.0", "maintainers": [{"name": "amasad", "email": "<EMAIL>"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "calvinfo", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "coreh", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "cristian<PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kelonye", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "nami-doc", "email": "<EMAIL>"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "rauchg", "email": "<EMAIL>"}, {"name": "retrofox", "email": "<EMAIL>"}, {"name": "stagas", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swatinem", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "59fde0f435badacba103a84e9d3bc64e96b9937d", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.7.0.tgz", "integrity": "sha512-nifX1uj4S9IrK/w3Xe7kKvNEepXivANs9ng60Iq7PU/BlouV3yL/VUhFqTuTq33ykwUqoNcTeGo5vdOBP4jS/Q==", "signatures": [{"sig": "MEYCIQD6GpsmStwdA6G4GKvSfzNHpgo7aIW1mVsNdT+J4hxc8AIhAJfjRcOPKF8XWuLpQGPVKRtoks+hGwz0t2e6S8tYRFMe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "index.d.ts", "LICENSE"], "_shasum": "59fde0f435badacba103a84e9d3bc64e96b9937d", "gitHead": "a99ec3c149e8c1d91fa533aa54d3ee7e34449bb3", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^1.0.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-1.7.0.tgz_1478630327407_0.****************", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0": {"name": "path-to-regexp", "version": "2.0.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@2.0.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "b77a8168c2e78bc31f3d312d71b1ace97df23870", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-2.0.0.tgz", "integrity": "sha512-DPZblKdQsbV6B3fHknj89h6Nw/Z5zFK0nFX+DVN7y8a+IUHf9taJWvMK+ue0+AEjXrke0KVRCcfm2pOYGSRk8g==", "signatures": [{"sig": "MEYCIQDXpUc1388rh6Il8mdi9VvXHuZnHrzEtaNOnlD9/K52kgIhAN/y3iBzC0DBZxdN9vu8x3drouIn0G3PNhMsXRt5iY9s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "index.d.ts", "LICENSE"], "gitHead": "c98ca8d46a807145933d0bfbfe63a79bf0aa20e5", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "8.2.1", "devDependencies": {"chai": "^4.1.1", "mocha": "^3.5.0", "ts-node": "^3.3.0", "istanbul": "^0.4.5", "standard": "^10.0.3", "typescript": "^2.4.2", "@types/chai": "^4.0.4", "@types/node": "^8.0.24", "@types/mocha": "^2.2.42"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-2.0.0.tgz_1503527454886_0.****************", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "path-to-regexp", "version": "2.1.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@2.1.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "7e30f9f5b134bd6a28ffc2e3ef1e47075ac5259b", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-2.1.0.tgz", "integrity": "sha512-dZY7QPCPp5r9cnNuQ955mOv4ZFVDXY/yvqeV7Y1W2PJA3PEFcuow9xKFfJxbBj1pIjOAP+M2B4/7xubmykLrXw==", "signatures": [{"sig": "MEUCIQDjf2QsHsAQ5yiAzaEK3mDiKE2mUADTGEOojdcIe9ebVQIgCR0YvC8qd5fO3OaPvEtbXBVM3HZ9APgBACiLvLrLJe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "index.d.ts", "LICENSE"], "gitHead": "42a3869820a8a02f4545c6b9c460175a983eb6f0", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"chai": "^4.1.1", "mocha": "^3.5.0", "ts-node": "^3.3.0", "istanbul": "^0.4.5", "standard": "^10.0.3", "typescript": "^2.4.2", "@types/chai": "^4.0.4", "@types/node": "^8.0.24", "@types/mocha": "^2.2.42"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp-2.1.0.tgz_1508521635899_0.****************", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "path-to-regexp", "version": "2.2.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@2.2.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "80f0ff45c1e0e641da74df313644eaf115050972", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-2.2.0.tgz", "fileCount": 6, "integrity": "sha512-zJcOPeBsraLjWXwUzFMPzH3QO2CmO1yRggtADPJjOTyCF5csQxfUGJL+CbyyRvIS09wOipi4F/fgRhdmVGSwxQ==", "signatures": [{"sig": "MEYCIQDCn7lfv1FZIDG30VJzg00b4V4ViolAtuI2IFWDMbNs2QIhAMwOoajKG03pb6bBLO6P0dddYnq0Xl79R2gDJCCwT53f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26717}, "main": "index.js", "files": ["index.js", "index.d.ts", "LICENSE"], "gitHead": "3cf45556002978802ed365d81f7fe1b6487703ff", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "9.6.1", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.1", "mocha": "^3.5.0", "ts-node": "^3.3.0", "istanbul": "^0.4.5", "standard": "^10.0.3", "typescript": "^2.4.2", "@types/chai": "^4.0.4", "@types/node": "^8.0.24", "@types/mocha": "^2.2.42"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_2.2.0_1520402693889_0.****************", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "path-to-regexp", "version": "2.2.1", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@2.2.1", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "90b617025a16381a879bc82a38d4e8bdeb2bcf45", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-2.2.1.tgz", "fileCount": 6, "integrity": "sha512-gu9bD6Ta5bwGrrU8muHzVOBFFREpp2iRkVfhBJahwJ6p6Xw20SjT0MxLnwkjOibQmGSYhiUnf2FLe7k+jcFmGQ==", "signatures": [{"sig": "MEQCID3bpCy7JyvL6O2W1jCHRro5cfhiNIiltl+J55xNsG4jAiBK9u3KRSzEd8gQaPK8Gtgd3b4y4ACad+v5Z6s5Xk2t5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3z6qCRA9TVsSAnZWagAAasAQAIahXJ9o/eNTV50kUK9Y\nFKt35Fu/QDl8MGg3nEXTk/5+RbPO+zexPHAbpIR4wgwSgq8MnCe4ZjpsmWiI\nzUNLQTK3ampkqRwN7YEyPzdXsqr9lmeH2DZJc+Q5m3dyjYt79QvtGxB8H0ND\ny7vkyL7vjEdVY6s+cPNeiZcEJsYLheMBE+X9W9oCogkpMThmxjcA4g0f3obi\n5YmUo0k2PGtRrGveUM9TmCQ1vk8xl+RMt0Aull+BSFgyfTDjoOcGeXlClXBH\nJqDRkXssQcGKne34MiyYNMHAe+A0RQYpisEZF8q4kjAXSi3IRq4P2MhQ1qIW\nbA1oqXWtolEFtvSz8sLIok1iO0HnNmBwzmpSEkjClhbfD6HkicZ4eboyJXOI\n1VqiezHpDWzO7u6KuFHrszHwwcqUHZTDrHt51b4XWlMqBr6IZFOVgK7eb2QZ\nAgwQjHobF2FpwNpSD+hWHwSDxT+F5JW63F5guM5UwjEKIfd4gCh9WbB1Xxpe\nkhBIYOGjaIBruLwKVqrsXIIEaXcwryQzBNdyn2baf4I/N8iYU5fa1YyGhi2v\n1EjRiRMjow1zdHanwXI87roqLxTfHP0UiAdWCiIGJuguuTYkTW42s8iWx1FZ\nJyuAp7ISKecAMHhDCM503GVTt40UeA2ot88fyWBgS7FZpd9izFC0e2fiET7r\nRK5R\r\n=wfap\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "index.d.ts", "LICENSE"], "gitHead": "ef07df50699d14659e672740643f905e2af252aa", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "9.8.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.1", "mocha": "^3.5.0", "ts-node": "^3.3.0", "istanbul": "^0.4.5", "standard": "^10.0.3", "typescript": "^2.4.2", "@types/chai": "^4.0.4", "@types/node": "^8.0.24", "@types/mocha": "^2.2.42"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_2.2.1_1524580008999_0.*****************", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "path-to-regexp", "version": "2.3.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@2.3.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "690d682cb9e2dde26d25f41bcc2b4774b67d1fa2", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-2.3.0.tgz", "fileCount": 6, "integrity": "sha512-wWRwboLa//uIppXIodKsl+qe4zAUNHwGBZUIkc32xR64fcSqGXCnxEZ3Fyl8M2muy9fq+mv2BQbFmZB3hZ4Bfg==", "signatures": [{"sig": "MEUCIQCtNh52olWhBxg7RC2qO96Wy1xaw4WPgLB+1opHLR4iZAIgd+zV/vfekruXLP5UO6rvX6xvmMuNpT/tM+WMkTXX4Vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26981, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbevT7CRA9TVsSAnZWagAAIRUP/0cX+zVisA0RDZ/oJbh5\n3uNjXGY5PNftGlb0AJzTXW2YKVCwJhaJ68ERv7Nh40KNlxhcQhyD1DivQb6C\nkEtnBJ0x8cNP86wAahpFQk5h8hZdgeVnsjnTDRWyjDEmp2mKB9hDrb2pTmkU\nq0TtMmrqKNTE32B9Cng72fJ05EO6GImKURk9KXJqp+fYustQK22HHN3LyNQV\nST9XMfEfIpYjzr65oiq11ceenU/+jrHinN79QN5JPsSRaGcbEYLl5+NeyqmM\nivenbIbmNIYaN7g23aR5QJ/XTmhcikFXoifGVD+gdT0msLNNreJDRzEJt6VF\nlQnj3XfqHWhSPVtZ7KHCiOYhwtD7YLpX2bdAYftW1Q53wnEnjoY7Lfxoqd1e\n7AtnjF6MGvEEFK/FHRHO+ao3y3f1FOj6yL1zJe+EK1hrEOuplPMqMG8HsiCg\nFEcTm31nw7ciwi5Gi6segCle0frLswKYRwYFlY6cc4Te0h6cFRv6sBN2CvRj\nsyeQ6rR/wl4zHFeA7jiDUyOYyTEfyqSQfsB8WjHhxLkCNtBjTfRygu294qtI\n166rKoreU72oNTIvozalQwmcc272RCeDLSruy7MY41LO1DKZhohYWoeyLCG2\nEwFYrQryVaVSQJyUtXI59XQBZAGDBML4yM93y2RQpq2mddIfQNpBEiO2YuTo\n1Dd6\r\n=18IO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "index.d.ts", "LICENSE"], "gitHead": "205665e451521270825ac5d74273e70ed5e8c7c3", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "10.8.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.1", "mocha": "^5.2.0", "ts-node": "^7.0.1", "istanbul": "^0.4.5", "standard": "^11.0.1", "typescript": "^3.0.1", "@types/chai": "^4.0.4", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_2.3.0_1534784763241_0.****************", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "path-to-regexp", "version": "2.4.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@2.4.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "35ce7f333d5616f1c1e1bfe266c3aba2e5b2e704", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-2.4.0.tgz", "fileCount": 6, "integrity": "sha512-G6zHoVqC6GGTQkZwF4lkuEyMbVOjoBKAEybQUypI1WTkqinCOrq2x6U2+phkJ1XsEMTy4LjtwPI7HW+NVrRR2w==", "signatures": [{"sig": "MEQCIBAihG8RcMgLZXC2fCWtkYsTuCYPH56lJ/gr4X4ps+IJAiB2HGcTx4KW6fV3z2sYh8JbyE/hOT7Wv3XKvzXseDaf8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgz5cCRA9TVsSAnZWagAABV8P/j3O5fXfCEuI/D0M4ATo\ny7iDPQB+jEO81v7UbtKjca0IPpr/n1fkcX55Mr/wfzWlksctYk+EbwDVsole\nJAuXy05mirFkPyf78lid9YHnaFnSMUaNlhIz5kkABhPmH67DCYVMdZp6Gtys\ndUIxu//oroXDVd0Z4mmjkUfiToVCDD67uMkOxsJ7d2smR5XMhXfPtl4zHSIL\nJshs1jAuYXgz+zeYfkhMgg1owUqE74ueXORKEqmk8UUT6CNjaxv6tIfqv4DI\nkGnX+oIDOJgw8LaojqAV1sSEDWtD8AnnA5/DeQj/ADhIiLG8Yy4JVPdL3Mo9\nRpsRA8SYetpc3y49uLmlYgXG1A3ayR6RIBmqCWnU5hOrQT5p8kZmvlmRvSL2\nGDrfBW2WdvIBOzZTsOX6nSAg3w7C7abLp/MO1PeanXxmIXQKQ/oAf3asH35U\nJmEYT4Rf6kKnr/JlaweClhyvs91gJgMwSlO/MDeB7tgyFDjRwDJicVj9ue0H\ndYavvdZmr/CO7j4Zfn4ryaY+N4Ao34Ooa563NVj/At40n2HeRYCtik3CONNM\nYvuHwahO7h2limYCcrhTJSXuJ59HQiLedKL3FpYbCwMgGFPmw213GOIAeC+a\nbbZjYpP+fYLZGyxFdiaUwYpfkVkEE08q5zfX9pe7AZffuoqhUsJAH5kl6Nne\n56TB\r\n=8dfi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "index.d.ts", "LICENSE"], "gitHead": "bcba87cbd47d8aa3f826a88a7f6ef5a77072c71a", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "10.8.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.1", "mocha": "^5.2.0", "ts-node": "^7.0.1", "istanbul": "^0.4.5", "standard": "^11.0.1", "typescript": "^3.0.1", "@types/chai": "^4.0.4", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_2.4.0_1535327835872_0.*****************", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "path-to-regexp", "version": "3.0.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@3.0.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "c981a218f3df543fa28696be2f88e0c58d2e012a", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-ZOtfhPttCrqp2M1PBBH4X13XlvnfhIwD7yCLx+GoGoXRPQyxGOTdQMpIzPSPKXAJT/JQrdfFrgdJOyAzvgpQ9A==", "signatures": [{"sig": "MEQCIGs9pREtcEhdCCDNRJSHCcz8HGhPrsEqdoPrZdlLcOb8AiB0W9QREY9+LCBAtiaojA/sEd7OpdqYHVe+OwPAaXQivQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcO9hnCRA9TVsSAnZWagAAGzYP/RwVfwRM8T3aUyH29j8w\nKM4wjV6nOOS0TQXCl2U0ie1Z+hwv7yt65YYmvsuagF1ZbGtpXam7sVUUUfwN\nEudiEBap9ghQSRu1lU2CxDU/pkJm1lcuHgHs/nDyxkTGOeNxs2gqVnQlRNZU\nFaTui+RZI0710WUW/44hrAQj3Cvf3CKqxJUXjaO923kcSqaZAvxPPs+8FYsC\nKBMmddzrLbDBHCIBhQ+5BcDeLe+Bd/eQGZdEDWNATOX5Kngk3fG8FoW6Ejpi\noosNai0Ksr132rL8ZB50niD7Phx/FYchqyePghtG8nIrLxBcmn7tj2M+4Dw8\nydLnfPj5hova+YU2jTCa49HSmx9bgf4UW8Hcj8OcenMRJytq2rvAVp/W0lCA\nbyPuCA6zd/vtsGI6dtjZQ/juXt1udDusXAy8aytZaFHHIYd2Ru9e2SBhNDpg\nvsleLA6/pZHQFz+HvXgsdJrOD/oVY5Ka3UzuVzCUYyvdf2hJpjppY8kpvbWN\n9wHS8dfaDlfOh10NzVpsAF55+whxAExbjmC6i4+vr5gaahtQRPve/OZdVZnu\njPASkQ6D5b81DkVXjLJWpxs/VKlmcHVU1yTsK1rAUfQal3keBJ2dJ7XAPfAu\nPhlecsczU0JVfT4xeZDHLljtB0f7B5McI9xseJRIB42iClOgn4GQ5DwDxrYM\n8OiY\r\n=bFfL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "796f3fdae1186f3ef7afe029555e2b141ab9ece7", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "8.15.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.1", "mocha": "^5.2.0", "ts-node": "^7.0.1", "istanbul": "^0.4.5", "standard": "^12.0.1", "typescript": "^3.0.1", "@types/chai": "^4.0.4", "@types/node": "^10.7.1", "@types/mocha": "^5.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_3.0.0_1547425895290_0.****************", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "path-to-regexp", "version": "3.1.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@3.1.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "f45a9cc4dc6331ae8f131e0ce4fde8607f802367", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-3.1.0.tgz", "fileCount": 6, "integrity": "sha512-PtHLisEvUOepjc+sStXxJ/pDV/s5UBTOKWJY2SOz3e6E/iN/jLknY9WL72kTwRrwXDUbZTEAtSnJbz2fF127DA==", "signatures": [{"sig": "MEYCIQCLPd0l6bWByp2YQr4aajNp/lClelT7v4Xo2jspe65OQQIhAPpLV5qkZyCpem3PXm+DIYk4QV1IhzYHrUZQMXlZ/831", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdafCeCRA9TVsSAnZWagAAQ+gP/3pV6hwCl7uSt6t6LqkL\nlMUXgsuFfS4qWWC2hfOuKKCxs4wvBHRG0f8oAYNYDMptx6PjYycFt5GfgBM5\nnlnJH1J76IAz0NeqyabMpyqidMO9KO38EXCDAB6s4IO2ihUUZlP8xk+lHCWg\nGDGOrrtnfyi48Liwi4R7Iiy+yJ1hvD9DlzwSXhOyEmGXOVxEA0e5e9R9xRF/\norwgE//Th0F3WmA/Vfrox75JFg/KQzcFO30Rgj+Bjz6csv1aZXgChFKAxBMr\nVPwe2oS6D57a7clWslc5kZWHGa9s2jup1KjXD54JGwcGHp728m+j14UpWqr6\n20KXh78APgZxdeGVQM+cA4TjI+HV71bzJBrGUOYS9pHb5ncFi/BaBA1Z3u8i\nzAsZLvzXpcgdxFHNtDwUyPNWLTsOw//xft03zIxsaNxwtp6MHEBufoIumYBZ\npFgxj5Ze540moQ0bFz6w5KRFyQWmbJ0DqiCWKSGXimWZPrzlgFMMSGgAhaGS\nFWXeCLiu3kgTFnk6hLq1+1ikCIIVWaxYz+ZBopbbhHfx+qAIIMpGopfXkH2v\nOUO7l08WysJ1TxjZqqfdz3OwWiYBgQtIqlkcwDKHz+wxQ9TB4HlZtJkQjTmd\nVDwx+3Wcz1eJ6EPBw3dto/SsaOOJL2BlI5Hp7nu7tUQXItqyAVn4K3V8dRin\nfyqj\r\n=O2zj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "f232e6d3fc256fc4def7062c7542c4230c6bf6cd", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "10.16.1", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.1", "mocha": "^6.2.0", "ts-node": "^8.3.0", "istanbul": "^0.4.5", "standard": "^14.1.0", "typescript": "^3.0.1", "@types/chai": "^4.0.4", "@types/node": "^12.7.3", "@types/mocha": "^5.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_3.1.0_1567223965722_0.****************", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "path-to-regexp", "version": "1.8.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.8.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "887b3ba9d84393e87a0a0b9f4cb756198b53548a", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.8.0.tgz", "fileCount": 6, "integrity": "sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA==", "signatures": [{"sig": "MEUCIDNa9e4RGjf05rnfaxFoyDxwwA8C31CmSiAQX5fM+mZ2AiEAl05I9dGd+WZUGk6InaiFeW2mzfNnUyEOS/llpICVle0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyNdyCRA9TVsSAnZWagAA3dsQAIdx4UYMUcXZ1jWemWIa\nWmAfv/EDjN9NRragvEchvin/p8JzhVM0eKAyDoHhNRD1gbvDr1JYNmVLap22\nyFuX2+k/KrQVPjeKDMrwpUMTuhXpeDFes/3F/Usu77tVy/0hu7noqKSYQSkK\ngIsJwnm4DZYGbMR3HicrY8scMsCgs3cD+6W1w0qljkbITnQnZCq4jtYHWVV7\n/YoenidG8WJ++wZc0uoELm9QTvRheprKcS6Rb4T7q+q7ElgGBpCLX9RO2xc8\nmPyOqa8RU2SoU4rMe1naIAcwwKrdg8xUCQ06qsZ63Nm1R9ciV9vcppDFBCMv\nGSOOIUAz517UPaKMi/97Blh0wxIIouBxWyKQqEDTd2P9jn0lPJ6rg5WR2+RC\nSpKjxLVhIj6vFUHYqEUN3CwYGdDNkzkgMutdu/lmMePMKXWdswfZux+9/A/h\nfGEomo/H06/8i5CxQl21Ks1Z9fm18beWgmp2m8UCzFxbPSsiztwlefL47LVr\nrkp+jB2sh6m3a/zum+LL/cmBsFskEPu3vghPM4ZWrhqGPY/anywauwHaoJH8\nyDDHJ+zVykS7Tq+rCLUsUcBMealHWneDzEqmfrK4MWbTN5njglM1niaTTheT\nXlDzR304qD8CO0rS+zZpWyA9EZzQvEIYczS6d0VOQ53CxrcVxYcWpfFmf4HG\nlj0T\r\n=EPuP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "79a5dcf5f2a79a99fbaaccae20cd922a745e0f83", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts", "prepublish": "typings install"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"isarray": "0.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "typings": "^1.0.4", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_1.8.0_1573443441622_0.****************", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "path-to-regexp", "version": "3.2.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@3.2.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "fa7877ecbc495c601907562222453c43cc204a5f", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-3.2.0.tgz", "fileCount": 6, "integrity": "sha512-jczvQbCUS7XmS7o+y1aEO9OBVFeZBQ1MDSEqmO7xSoPgOPoowY/SxLpZ6Vh97/8qHZOteiCKb7gkG9gA2ZUxJA==", "signatures": [{"sig": "MEUCIF5oqXUoJgihYVjbNwKnMqXKiOOURaVSq8ixpKQSRbfGAiEAj6gAA41/N8exiFxAzZDve63/Er/qAf9KY+IpRzfQ/1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyPacCRA9TVsSAnZWagAABaAP/2X5SbiKS5o0b64IySTf\nK204AxDYUqid+vRNnT4j45OhKsoAS+VntbEyuRbTtHVDpapk/suuSL1z3hTl\njJwh6uM3/Aocd3G0/g6wlk+mzv87OEn4zJi+8BzGY3G31Z1W/2mKmSfx6QiY\niWYFQiM9iHWRWa3p0NqVV7cJBdh/hPYO9QgbmSauGryttAgWazq7Y2A6pOK4\nMlI5LVxXg42ajWAeApZRn2hFgCPQJOo8JZqQjuMt0Ffkpg1olEinqPRNzZfW\nETIicDxby9aKcDt4+WrQumGP8vLhEa/VbmiAdNW09WI/dMbinEpUeJQVfUxq\nTgc5MEf1J4sJAuitB1KzeUh60A2FphhLZvQoxjrgIF/qALQsjqyC6zv1MRhc\nqtjrNEw4dssSHlbhQ0VNddxoUWJ/4nNGSdhy85dvJsd1e5RX73JvoSrobuL6\nwAjz8isvsfo5VbuvOvjazIskU6T2ryMEUapxhwXGOiPHm7N9japL35o82d9p\nznbawukAgffBDHHPMJfyMLRZHYxnB9vFjoa/TdgJmZdwTD51CAMnMQy/hYVG\nnLdWbXCfkSCqHyboy+uDe53SBzBfuX8MoWB/nBf+KGmpvDRvMVrWNcYq/FW1\n7IGM3QJG6cSTuAZUyPFEw1VgcWsD3bP2OX5xTsqLJIWmWwHz3emA3ErU6YCN\nqbqx\r\n=CVMO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "6d2e8db0f1260921c63330c006f3b9f492b69aed", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "coverage": "nyc report --reporter=text-lcov", "test-cov": "nyc --reporter=lcov mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "13.1.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.1.1", "mocha": "^6.2.0", "ts-node": "^8.3.0", "standard": "^14.1.0", "typescript": "^3.7.2", "@types/chai": "^4.0.4", "@types/node": "^12.7.3", "@types/mocha": "^5.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_3.2.0_1573451419958_0.****************", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "path-to-regexp", "version": "4.0.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@4.0.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "58768f48b2697004b12b3e5cd6bfb339ae8e7eab", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-4.0.0.tgz", "fileCount": 14, "integrity": "sha512-WePdN1ndXgSWXXWrmKJxLSlazaMPeW8UYVQ63NDBHikr61eNUGCJrCEemfaQLSxGHzqzcu1YSlZu7CqqWh2Lfw==", "signatures": [{"sig": "MEYCIQDJKSyQUReKQ0o0MvheQp8NM+4lhO2siEHwCMSBBAoQqQIhAO9fYrPcT512m/n7P3LCWon+bjI1OdMZ+12JnNZxN8rx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyic8CRA9TVsSAnZWagAA5w8P/jqHH0zIaNeQgKuiUxTb\n0l/9+559VQxZLgBxrvWRVXpt3L1Yy5TjkrY3Z/O5T0te/hcaSrgJQIxaDrOk\nIQS7RjtP8qMCaN4+Ys4V1+qS0WmWSnDGv32ndt5NXOttMCLsfl7McYXniHh2\nZvXXrwCSYQQ1W7iRxjscKDgfw3Jn+fdk6a/79B1akfBRU/hAleMaYlt4Wrc9\nR+xUgObQQnm0IJDN0GapZpOPcJPjtejfHIjBN/8TwFbcFKYXb4iBYEert3Sc\nONd/uiY61rTiHtFiGcIDTUO+3vjuBQgcBqjAAl450De2g0po3OF43LZ6Sa7U\nkfjSswQrXrwJ8n3EFXARFuQEP1jkHG9bEWc500Rk10obHgabqq5zuFJTjeRi\nLOCYsg7KE8/uq8VlF94jjyBnXUYW2OzvZCvWKC8do5sHkv5KQsUwgY3HO8CJ\nDmpUzKsoGJzWONFLUHwlmn70/30DZ0xf9G0rTEev9C4CJdlIP+B3L5wqJf83\nWOrrOjt3NdvuqYaHLSQN5yvD4zX10i0OHJ0oFvDG+1LKDKe0AH5RqPVVhz2e\nlxic3riThY/rQRg5LJKUIL1faLMxRFKkJZt9Q72mlm9o2P4gbCCI1fEZF/3Q\nmZ5YAqCuSSjBe9RvfLqH3HsmQtW53oEcFIhAqenPPhSzQF5FR6EaPAvMvfzG\n0hwx\r\n=lrUd\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "38b42224a57199969197a2662ae34b8b3ebba1cb", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "1.7 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_4.0.0_1573529403845_0.050478772222488644", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "path-to-regexp", "version": "4.0.1", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@4.0.1", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "119a9a7f0aa0b8f7048593ee0b7fca1512460caa", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-4.0.1.tgz", "fileCount": 14, "integrity": "sha512-gdAzunCn/o8fI5Fc1M9Ri+qgJSLKKEbfkoDij79QSOsRQVK/cOlWxzSlYJqLkGo3YCLm9ECDxJo53lWEVSp+Ug==", "signatures": [{"sig": "MEQCIBhRbAqUnv5FYQG8jTPOUj0cCUmJ/U4xG0AO5/vnoGx3AiAvrXw5gUwzBRFcM/vzQUfDT5/IOpGaytOh/62P8i7w3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyjBWCRA9TVsSAnZWagAAtp0P/1EOxUnffzxiiDW7V3b1\nDZ+KsqnoaElDOqcfNK61MghxqXc70cTWm+UE/vvFHTF4G87s9NjS763ayutO\nS+9xB7puOFKAmp6b9tTbZlZF1I1vp/Bp8b6aFTMajYYH/3AUWf0KcqQOXjzC\nPuz4MOKblBJqWxGJL2AaVBG4fmiKLFf9mnaGPbQfwH26fZ0JMSF5rR0BItKP\n1pMmXY+574ADjwA0pBuu1xfFsmoYtNHjvPBK+zqIRdQybVdVDW0XWs+eLI8c\nTG5aTWJ7HJOXt2UA9uH8UyzRqwKbBsfIyCxfo0NukzOkbpt5AmNILhtlDXuC\nLOa7iqWeNMo/t4hvaxuM6yCpfQYHnq7BBL7eVYIYGV0xEz4lnb3WBoKwZsMx\ncTSvRBDkNJ9hmC9pvNFMl0iVJQ9dOQ3nsqoOPioIEsWGplSJJD+SPNs/M4SX\nxdEECMz7sMllpfFB5Rhyq24NefA1vX5q9tfp8UIdrjIzFYJahXYT+H1uPtXr\nD4Lt0iQ/rIe08osFtwC9mbSd/V3MqsN5aCyFJ6/qpmt3aqJvyAyxf49gREM0\nApGwxyOv9ZcIFlnMMvsxIIEvPXJatQOKoR2K3ScC0z2acFzZJYn53nGmbi2n\nDiM+jKvWTes7HxnVMuUsVkC+9JsE5NGDyZ/PM76w0un0J2yw6Wj/Wl2UmIph\nUqxB\r\n=5NM0\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "6e55e15e49b3a6a55680fa7e31ad3a01435d3c94", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "1.7 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_4.0.1_1573531734121_0.****************", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "path-to-regexp", "version": "4.0.2", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@4.0.2", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "c1f1508056be0c77971343e82f598d74830b3597", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-4.0.2.tgz", "fileCount": 14, "integrity": "sha512-HsWwoyYDDkKyd/4Plv5Obcqxi1xXhIrG2LRdyX07yAIarSD/55itj/Y/SajVHDtRiaNIwsuujnOlRj5MFXd0Tw==", "signatures": [{"sig": "MEYCIQCinKIJXR2JjCiRp6p7nKUERXe3kVhpHxNCplWujL70xgIhAP6pjeQaCmHXnKp4ZglcUJlO3HBaXpJ4i3ZgF6kjfa2C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyjZ5CRA9TVsSAnZWagAAHRsP/1QjXEAj2jAHVvChWqSh\n0ZZavLdrcf4vud1oTINb/miuzVGm6bHXH5sTXykIKet9bWvNxNmbsKJnlDzP\n5qPsOe0jclcWEzX61+J5VdoIf4FTRGiPBf25ebsNWTlA3XgN1pkmTB31JDs8\nOI9txh4rzNun2AS0yKlH4+xDaiyhNkeRc1jpaR7oxhGGUE1CykDhg0+v5uAD\nnQqo6If/sYWZR22qxJf72nSicQuO40XRQ2kmc4GB6loU2YdHz8amtfXHqyJr\nSb8l0fjK0g+f6NR+7OL7krmLPqQBC4lH8lhBwMHUYx6twMTq0w7mOl2vnsHB\n2j0DHK5uY2WASpfo+9J/H9bGKds0Gh1gM7FlWZCe/56nnH/gVkxWF6ZcCiNk\ndyprMBa/BMrFjS2jda6hYHFInsxY4wDLGbYK64Va1l5uVS0/5xYJk1AvYigI\nNhWwl+EBxRlJbaIvJIQ2lyRyqwZRfW627lDxLdBJz72I1fKRu4kZqFJTODkB\nSF7CobpEG09v4pF0qD6QX9Di6cSO4/f+CQrEkIDpgM0QOoVOqQb3X9qSc5cz\nHWkP9QP8Wi0kMwGmRIJ8rnAgkGH7WLCzpawz7AAt0l7ls/U5rXlyY+xVL/nF\nBhvW1z5ZxglJe8ycX488jwQoquTYmsqksylPHT3r0Krr8es871D2tIe7Jdr/\nNSgZ\r\n=URSE\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "a1ae34664db835385f777cb819fd6f673660d919", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "1.7 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_4.0.2_1573533305237_0.*****************", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "path-to-regexp", "version": "4.0.3", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@4.0.3", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "5b5428d180b0f115ad438e1ef010e63a0a25d7e1", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-4.0.3.tgz", "fileCount": 14, "integrity": "sha512-JkDr0ji2zsBqaz/+X7WDpRM73y6m86Eh7Cba5zgY/bjFpeC9vprRFGptsXjJD7EJqwa7W2ESQpdObm0iwVpKWg==", "signatures": [{"sig": "MEQCIF4jqTUnom5O9azuaj44fyciYec2Thb6QA2Y/kU/EkYOAiAJ66IqlxblhsIC0nGbc51lFZGyHQ+G6Sk4qKR2q6MVaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 473276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdylbqCRA9TVsSAnZWagAA1GwP/102y3vEUHUt9Qa5Ieas\n5sZVHkt+lnWkPpPTM6Dx2g9NGyjw1HMDPOVsV1KrXc7nF5vxfi3iS5CF4CyH\nHFMuZbVZf6yaxy67+nhXwyzGP5rppCE2zbss9LA1y7gm7AJE2XVQTsyJO4P/\nUS9F+K+wyNF6zBIgmGSdsZeKZatFAq7adGLnSo8B6gUnun5L23mLfv686aNy\n7FaDBBuOwbotQibSrouPr9d1Uyh6W2TKHyUVbUi8tBqn5z8wWES3bUhlUbKS\nKZL04DK6d8jPYkA2CD1A3zO9Mo4VgnRben08kd+jmOkUu+k9cYIw3BM3wlG8\nMevHIkSkANG6HEtkz+zXuYgKoYGNx3f3QuUK/HXK+UO62vAjZuTHwDkPd3Dm\n6uZ6WVIUHUtiJwTLQNyaewrP1I2YCsks0eB/Nb/nAMCu4YV7Ptu8SfxpYudp\n5cPQAy87MztWk4HHkcqDQOBQI5mBSwsVIYFi6IEgr0DQrRTa/c1nuop/FT+u\nn9BXR0wWNssN9rv48axhmr1KgpUuDhrhQbs9rpsaOOy7CuTj9XoUjRwdqykI\n2kv/bVygNFSxYVwx8r9tWILcG1cXXaWvEe7wqh26FkQHKokVnBcOSj3xkGy8\nWZJckTeZAtkJLTYglGSVBRvByiKtXIxdwcxOMI2mpzA9JpIn2tUSa8TMXX5S\nM/eC\r\n=IBbz\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "2ae4d25b54842ba471f054a173342a404e083e7b", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "1.75 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_4.0.3_1573541610180_0.****************", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "path-to-regexp", "version": "4.0.4", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@4.0.4", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "9c0edc5500cc976d8e5f2520bf8722fc0b65019f", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-4.0.4.tgz", "fileCount": 14, "integrity": "sha512-Nu0+EVkG5T0wEqDT2roshNDnfoB1DS8q737dczIg+gpxt3QzxfLXi0oiuZso98IQrug9lW/anwi2+dTtrYngEw==", "signatures": [{"sig": "MEUCIH5f47mnlV1nDwt6mSR/VmnJX9e3bsQXSLOTq3YzJK/2AiEAj8gNBPnhiwwpyXo8cfZrmIZnskP1t3NSKlRths1YBJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 472934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyl0gCRA9TVsSAnZWagAAzpUP/iZcZznRrg6jWnV9BSfZ\ngFQR3ONw608moWEXDwmcnAzO0l22PkytnYfvQP0CYIHq30agPwBTQt5e3OCi\nJDAdTQ08yj3lOD/C/wfTFLCAUIkCBVgOZsf39T9md7M1Kz3NB9zI0ajtbsQM\ndTYBB05t7SKcPFexT0yeTenV8q8VZ+pg5iGtpULSV5mlDzUFOHm9wHMJ39xW\nk3o/xWEZilQT2V8YsetoV/KJNvtcS/kFVpwNwZWJsutdbYxQmjy1AGUmngos\na/58QEdQRb89/K5sG0VtZqghL/bE3tAwI6VCcaj/FemGtGfRxczbkI29XN4c\n9PTGDabZLfotU63GSR+DMd2RWEJuWBpvV8L5/tnBMbxF4BKU0Dc1Jj57AA5E\nDsEk370PUV3EUa4MF6SUFaEd3rz3JtL6hUdEf7cbsOD3q3uq8RXznNS0yq+b\nzvsaC8N1czZ9WT4Zqyqdr1sOlfzmPgaj5FCZ9RwQQG/DMeMbPXstbHRCeEe7\nz5SDdItauI99B5Vo3xj9k6zv3fIDRBIRghqCJxssuA2pZhoQWNC1hA5u7apZ\nU9Wzu8dsIVxweshq9XCcGyu6ybbG1aAH5g2XZcFltJiNSsCxwp5jI3wOvl2N\nGvgem9AXmW24FE5zbfcNHt5nSnC5+bU74IvTHwOUgKQhuYFui/iVAXz+Ely0\nllgb\r\n=qXZz\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "f9bf1e06636a3d745b917c9521a94b5b1e2eaaed", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "1.75 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_4.0.4_1573543200141_0.****************", "host": "s3://npm-registry-packages"}}, "4.0.5": {"name": "path-to-regexp", "version": "4.0.5", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@4.0.5", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "2d4fd140af9a369bf7b68f77a7fdc340490f4239", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-4.0.5.tgz", "fileCount": 14, "integrity": "sha512-l+fTaGG2N9ZRpCEUj5fG1VKdDLaiqwCIvPngpnxzREhcdobhZC4ou4w984HBu72DqAJ5CfcdV6tjqNOunfpdsQ==", "signatures": [{"sig": "MEUCIQClhK1iX9q8POd2vwJMYWFGNHV9yWAuL6Qy/cd+BqaKXQIgGHSj3+VRL4VgAjV4klDT+f4TLrE+XxXWex5ULl3AARE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 471565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyor9CRA9TVsSAnZWagAAYYgP/3gtNtAcB7+qnUkrYvnZ\nB7jJCtoSGD9gximFIyIrU6D3u+TMIP5O9ODrfi74snyi98pS2PcNGc8wflnz\nRGE3egYZC12Ojo1DoC7keO1JsXYI5/D1sL32JbdpRqR311j64Ux08UGHgvrV\nyGe1SCBuJ2mgCICT5dz/CRhiqr//zmz36IgkKZsYoWGFE9oNU/c64F2ND4w0\n921dlsj3bFhXeeasOcIIJBHpO34qVK3fLTKXvwhIvmrSGjNdws2q650pfz0P\nHtyT23vu25IZigXquPiflBpTw7QsQ1mAGEyupuIOj8l5Qw7c3iLrUwyGTZot\nK79sQzr4kaqHFwQt807P9QhKumZB7JDjR07j2PU/1FEuNy/LixJomvkTesry\nuWI4EEpwx/ARS4YxaPMqmhhIuqufWjPy5TpWqWOZQa1NE0VLXA+IvuJoWTVF\nC53ggoYa7rTawHkli2gAXUtBu5cQfMSVwrfxRG5h6dZmcOlxgaEUHyotQGoG\neLZjxnLTlpSC+rXh4dZyRPzHyPADQ9ZoMymKJBgQjmxn4rWRX8jrpgp3Lc/0\nUaCcCwXealS26nG+3/Dx/R7/6tQwB1+JOR0thjYdE63GGwy1HJAvqPuPGjl3\nw7Pw0NdkhZHPYbQBpACy4n+slRgUcG4dP3yUm5uswUBVusvF6EZc33Xbxs26\nBpZN\r\n=PUJ6\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "e1ccbe1c83a1a63420df1d51478175d8f8f847e8", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "1.75 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_4.0.5_1573554940397_0.*****************", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "path-to-regexp", "version": "5.0.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@5.0.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "e56dd58778b9dc06e76f0f3e39e741bbddc0bd26", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-5.0.0.tgz", "fileCount": 14, "integrity": "sha512-VOQVA+0mivusQfaveeWlrW7ddXKuStHMTdovn/1epf7cEpUL4dpzGl7eq8ho96H9w90/q66yJLsqn84jSCfkmQ==", "signatures": [{"sig": "MEUCIQCplA66BPUszmHpELT7XW9JjV3Nm4r6ixYjO/F7TGrI9AIgLKqiR7bvA6K+MawFPGlRha2wJOG+zGm9e0tz5I9kBJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 468899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyp9RCRA9TVsSAnZWagAATbQP/0gaUnwfpr7qeQLhOsWT\nlf2zkosGpkbQSGjtirEMuo7bMxjj8i8t8hOszvTjBUPqLrDFdxDHzZlWIu5R\nwLzXO4EYb4BBxALyg+eJfgC1RMRD6YmYr/z3HmFy3/2rS07DH6b5dSuOm2GC\n1/igKX8tmyB7WNIHVodiTlSgpDCENOIoV/gRhXbGISByvDbegBKjIdSTyn3b\nAMfJxvNPpp3hDuNj7DfMuovboNGNNNeGv6O7MBSR543OSy5GTaBOXKKS5PAH\n4sI6UU7eK3SHWdbimt0scSVjTblWi+4oNkNj1JrYLL3WDCO/0me7AlQVATOl\ngBgDHKaHZwW/FgHJ/NGoks4TJ6LxJaO5PKkkMQvMUONtxvctatqjvQf8n7u8\nO4p59gB0aKu+YxShMW/Dh8EfSIkOU2kGsJYGw0SFjWTGXRxZuHmbX5ACTOH3\nygqPkmD5D+NpemL/Ln/yZRT/LdCSL+pVRNw780ngIW8VO7YIcDJnr7VHH3MV\nVn5IbCy41689eZwaZdz54CX9Ho7ye24KFASNsq2jOukQhaT05r6/8G+iwzLV\n0sfX9ksXgBwtxBhT9o2BQzuVHcgmwlA3/cQJWcm1FxOn3PubgO4WO5CYwg9u\n9L+nEaLkXCEYfzhv+2z+IOC2s5CuoaygfTPwOddYxujf3m2aTAwakPSdKfly\nMSpJ\r\n=xmXy\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "8a3710d6690502978c3aaa81e9711d4593d18b69", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "1.75 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_5.0.0_1573560145223_0.****************", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "path-to-regexp", "version": "6.0.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@6.0.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "8797c236ca80f62b9e36e39eb3ef5208ad6ab68e", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.0.0.tgz", "fileCount": 14, "integrity": "sha512-/BtzpwyPV+/I4gL16q3r3xb73zcLyALoP+FPXfp1W60N+qCkKATnt9bihaYMBCSxHhttopoFPw7diGBqcOXkqA==", "signatures": [{"sig": "MEQCIDA+hs+Rg6N5lGlsKMxGuu+m6inHi6uXWw+cIKmW1p9GAiBO32lJScRPCl/n2UL6CKE1keMnuGwELcjjvmqnDpPhCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 468051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1L0HCRA9TVsSAnZWagAAuRIP/3kSAeoYPYdgGW8hBOcA\nLmJjcB+x5mB977eSPQCq+lPLFVnMNXC+4RFlxVuQKovafDRLy9OLy59sVau1\nxoW8QTY9bOCTP7a1JMJwmPQJdFQ24R+7F0B5EdWTBnEt8i3hxMwNzw69GiMY\nUexNTesIvTY0tjsibJGlkw27EMzY/ePCqJU4WSPXDb+yyXvLqpPj0dYCBIe5\n8gUp0adv56isPZwWcwh1x7n52pKcDvhy/Gi/Gq0+3iwM4DK1hudmt0wSjs0G\nNLHwuvpkhwWF+dzCwb5+olswSJfZlGhIWjVTcT3O2hOP6Xq0nljBtNLPR2VP\ntEZ6KP7Fz/eqZBPg8Wdh2f9BbKwoFRSSsEzRHtJBR3Ow2MPVUdOIIgTo5IYk\ne66qOkRUk4bVM1dzGcGV3jalUf8RWKS0lTW3D5/BHBSGXbY4gzJ48ndqma+/\nGJI7LZNTz8pgipeAkz3sEi+Fd/1MRQP2Kh9JlV+KaVZxGWDHCgP8GuRMv8lL\nY4KhD8GABTtlaMsk6CVo3vaAqUhP0SScSPzu76ROTVHXyyq2xZW5DEwElRJL\nMwT77FTZLDRmtxpWSa0SuyCWt8eDwFnH3z152eleb6zxXubVUEWJgOkSWyNH\n49J7s4o82yoTF5DuY5NkerHepN9VugsE9kTfJ4a5L281hbpsZ1GX+gVZwBeU\nb6J0\r\n=U/zE\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "0d83cebcb6c1e16cf84a67b5d3beaecc993b134c", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_6.0.0_1574223110991_0.****************", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "path-to-regexp", "version": "6.1.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@6.1.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "0b18f88b7a0ce0bfae6a25990c909ab86f512427", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.1.0.tgz", "fileCount": 14, "integrity": "sha512-h9DqehX3zZZDCEm+xbfU0ZmwCGFCAAraPJWMXJ4+v32NjZJilVg3k1TcKsRgIb8IQ/izZSaydDc1OhJCZvs2Dw==", "signatures": [{"sig": "MEYCIQCkniNy98dVAYRXIFKYCQ05XsCV/DaUAWhy+PZO4/PLvAIhAJMylsA2Q79HlTPdosv3o6fWKe+Kiiau2OIEDjo5MXJK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 476190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1ZNPCRA9TVsSAnZWagAA5u0QAIPB+iLLHOaiLSK9m3gM\nS7HbJhsmxpWOEPzY+YkWMSZWEI1qpDz53JfjaMSguEZsysD9rJD28KmUoYlP\nmN95HEmjqv3z2YVbCBSeqPvCaxPx3zN6qF/NFMNTKS4ZWC/vk7zeItS60bKd\ngDnhbxVSyJP0CZTJwbMX72ELZPBNQBXJhI1vJrLDUkXBFwweTSaYhS0QClrH\nnjaTRVfKz+6KpXIjkh5r6guQNHX6cWVCahTx162wzg2gyqDt3pSI7P5WbxAr\nPabDtVZX2S+YdI5tcwLD642s1nm1GPixQ438/qwHCV/HKKWhV2av0ZHWlK2i\n+tDdw8yqKVIsp3fIv5KGbs2D+CUok+oqbFNPMzOqe1MHUJKUfY2R49EkKfjn\n+RXCEb3+9KHOsEVhZpGq+wkScepmcJ88tC9iYHvm+hyRODQbl3lWnP2hJBjw\nyGGhkSlNzfq3gH3Xn2GjA+tCvTaDo8nJbrx7L8QFR+J2yg8ERkjOzIJkVsID\nsdwWQT0oZ1j5y6mDMA0e8zP0ySgZq6rTbpGCFCLYqBiWwsR46BZaFz+r2uE1\n7ZozRC6FCplY+Ggi1rS7sIdYTqa8g6mItmhhj05kBw0sAY8rvLXKwwIczdm2\noGcvnMz79EqDSIw7FlJfZGk+PsHoRUtTKJwkezjEBwyRqxbMHj1lFO69BNDu\n0lJr\r\n=voQX\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "4b8efcc506ce11401919d04c40e4b316f71742da", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["npm run prettier", "git add"]}, "sideEffects": false, "_nodeVersion": "13.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.9.0", "husky": "^3.0.9", "rimraf": "^3.0.0", "tslint": "^5.20.1", "ts-jest": "^24.1.0", "prettier": "^1.19.1", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "@types/node": "^12.12.7", "lint-staged": "^9.4.2", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^2.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_6.1.0_1574277967326_0.****************", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "path-to-regexp", "version": "6.2.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@6.2.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "f7b3803336104c346889adece614669230645f38", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.2.0.tgz", "fileCount": 9, "integrity": "sha512-f66KywYG6+43afgE/8j/GoiNyygk/bnoCbps++3ErRKsIYkGGupyv07R2Ok5m9i67Iqc+T2g1eAUGUPzWhYTyg==", "signatures": [{"sig": "MEUCIQD06S3CUI4FzyoL4CGEGupC/kCBiMeH2ZP409lyNYEnRgIgOJxaTWYg4u4pu5mRbTa9BG+S18AgP6fF/CYCRMD6Z7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfc22XCRA9TVsSAnZWagAAeGoP/3UROxSdG31+M9LNl0x4\nWvZDtKYQpVRm31l+szSzlSwtQ06e0oY29I1r9DiSBPkY4OOvaEdJN3G9IWmo\n5Li1Htb3GYeuEkbkqk4Q1Ib6fyl5miYuRPNa7ndf+0ayMy2Z+C228GslJju0\nILzvuzXDGLCZe1LHShkhTbglrrrE0QuMSfjlRfLdwx0CgImYyRyEoeNkhXx9\nIhlzAlrZWrYF5wHhBI3VLVS9B/nx7mqV1XkHbwspckex1oxbghMGT1mqKoNO\njDsb+8ArURIIut0LyAMkjlgepMVPnbmwqsaXgyl/Eusrr1qUyF3sT5SkI9oA\nZvOAcGOvcrs5L7G1QwiwTpIeMJWbAtooxdJe3Al+Em8UtvPk8+74jObo8dAr\nHfQlg0BivYW0eTXP914cz9f6zSDaIrpDiXunplx/otTlqHf3FZYM3702GTQH\noZzfF0hwNYrjAJGFrW+BKzn+aQeAfGN+2+UhVTJxoIJ/6j8Tjc2TuT3rIpM1\n3sNB7Ar2tKM4nAq1BRd2Mp/k3DdEYptvW3PisOLF98kprq4VRRSN0AhRgDkd\nYHktaGkhymVG0cEfaeMbWTzxCEauJKjxbNUI6nD5720jejio1VGjXXLRYVzY\noh1n4Sy4TjqDySZcAbwGJmmtAU2HYB+sxoSPBTRUhNC1FU75JgGm4eP7DrBF\n0pW3\r\n=9dQo\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"roots": ["<rootDir>/src/"], "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "transform": {"\\.tsx?$": "ts-jest"}, "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "main": "dist/index.js", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "module": "dist.es2015/index.js", "gitHead": "125c43e6481f68cc771a5af22b914acdb8c5ba1f", "scripts": {"lint": "tslint \"src/**/*\" --project tsconfig.json", "size": "size-limit", "test": "npm run build && npm run lint && npm run specs && npm run size", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "specs": "jest --coverage", "format": "npm run prettier -- \"{.,src/**}/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "prepare": "npm run build", "prettier": "prettier --write"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2 kB"}], "_npmVersion": "6.9.0", "description": "Express style path to RegExp utility", "directories": {}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": "npm run prettier"}, "sideEffects": false, "_nodeVersion": "14.4.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.2.2", "husky": "^4.2.5", "rimraf": "^3.0.0", "semver": "^7.3.2", "tslint": "^6.1.3", "ts-jest": "^26.1.4", "prettier": "^2.0.5", "size-limit": "^4.5.6", "typescript": "^4.0.3", "@types/jest": "^26.0.9", "@types/node": "^14.0.27", "lint-staged": "^10.2.11", "@types/semver": "^7.3.1", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "@size-limit/preset-small-lib": "^4.5.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_6.2.0_1601400215264_0.****************", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "path-to-regexp", "version": "6.2.1", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@6.2.1", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "d54934d6798eb9e5ef14e7af7962c945906918e5", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.2.1.tgz", "fileCount": 8, "integrity": "sha512-JLyh7xT1kizaEvcaXOQwOc2/Yhw6KZOvPf1S8401UyLk86CU79LN3vl7ztXGm/pZ+YjoyAJ4rxmHwbkBXJX+yw==", "signatures": [{"sig": "MEYCIQCUM4AGebHmVJB4vlc94iFpyM2WIW8y0g7IuV03xVwOkgIhAL0jFPqlXK17GRNggTEsy/oLZ2imeyPzhMUimXlXIm43", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidZp7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpt/w/8CSunwhj96Pt2Kn7rJCovPT/J0B8M1eqbemrgADSkjjmAQzCO\r\ndDPQwhZJbfTxB4nI6dTqe1oyI0hIlHxgFi1WGlXB4FtAowCcmzViQAOLaNrf\r\n14gtPzprH6tiXvLyH+x+Hokf/sIvmSKdt/VXb7vc8VihKg64ute85Txehg4g\r\n/IUUVzHsMPywmDWyIoTT7m9WvWC07FG8mAiC2SmajqGQQBG4ZN87qmw1p9gd\r\nSSLnBn7M7Nd6y5CPZtSR+h9UzmU+8/r5GliCD6518AKjernRDMgkXlklVL5H\r\nhhv6joZaHPj/HchNPdXhFpxn0mBYmSeM+zu7i52/Eugq7F71ufHD3HbvwwFG\r\nF9X276/eqgomdnhuJiKy2582/RNTTDvEqfCqCszBoARkKoKSqmFcWwVhl1Bd\r\ndz9YlICA8D4mGbOqUgTI2VFn8JAkhFh6GijUNoR29vw3ovj1r1rNyhnR2Tkd\r\nNfdpu+iJ1gq5maqIgwc7F0zvNcpaQrKZsetyrE6NcQMt1jFnI4aSxFrRGDKK\r\nBPIdsAosF8SIbud+EKe3Mn8jrTM2TPvMv3iPs+iUo4+MxJ1M9Vcgn5dWvheu\r\nDxZZEcDcMymB2KoPJ9sHIHVsDBzz+XFKuiffUfFuwTue25KG5eNrtlgAW898\r\n0aBlR9IkQ+JXkqVF2AeOE7iKOCPCCFyNb5s=\r\n=dzlO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "module": "dist.es2015/index.js", "gitHead": "b0778f5e8e6c6e9ee4e2f5b34e877cc5229f8036", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist.es2015/index.js", "limit": "2.1 kB"}], "ts-scripts": {"dist": ["dist", "dist.es2015"], "project": ["tsconfig.build.json", "tsconfig.es2015.json"]}, "_npmVersion": "8.1.0", "description": "Express style path to RegExp utility", "directories": {}, "sideEffects": false, "_nodeVersion": "16.13.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"semver": "^7.3.5", "size-limit": "^7.0.8", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/node": "^17.0.17", "@types/semver": "^7.3.1", "@borderless/ts-scripts": "^0.8.0", "@size-limit/preset-small-lib": "^7.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_6.2.1_1651874427419_0.7537906321822747", "host": "s3://npm-registry-packages"}}, "0.1.8": {"name": "path-to-regexp", "version": "0.1.8", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.8", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp#readme", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "5124958014ebf90d4c0d3f29d996a86bdc959ca8", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.8.tgz", "fileCount": 4, "integrity": "sha512-EErxvEqTuliG5GCVHNt3K3UmfKhlOM26QtiJZ6XBnZgCd7n+P5aHNV37wFHGJSpbjN4danT+1CpOFT4giETmRQ==", "signatures": [{"sig": "MEUCIGuh1itVS2cWXAM3ZEp7wC0xLmVGhcwfCMeGAuKWvd8fAiEAiqjjzZGG8lGqyGI23o4iAHh1WJiEJazYF8nbg/YsSwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6109}, "gitHead": "51a1955aba523eabf60990109a738b0d03964755", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "21.0.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_0.1.8_1712462033398_0.****************", "host": "s3://npm-registry-packages"}}, "6.2.2": {"name": "path-to-regexp", "version": "6.2.2", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@6.2.2", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "324377a83e5049cbecadc5554d6a63a9a4866b36", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.2.2.tgz", "fileCount": 8, "integrity": "sha512-GQX3SSMokngb36+whdpRXE+3f9V8UzyAorlYvOGx87ufGHehNTn5lCxrKtLyZ4Yl/wEKnNnr98ZzOwwDZV5ogw==", "signatures": [{"sig": "MEUCIQC5ptvh6z2/GPAxWAQirA91Rd0xfI601ryOhG1PxmZg4gIgZq3L1J4+Tz8pyd2zkqWJJZyjtd5jCpFx9TQZJf+qs4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107717}, "main": "dist/index.js", "module": "dist.es2015/index.js", "gitHead": "28a5b27bb9e7f09dddb573ac50923f2337ea0dbf", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist.es2015/index.js", "limit": "2 kB"}], "ts-scripts": {"dist": ["dist", "dist.es2015"], "project": ["tsconfig.build.json", "tsconfig.es2015.json"]}, "_npmVersion": "10.2.0", "description": "Express style path to RegExp utility", "directories": {}, "sideEffects": false, "_nodeVersion": "21.0.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"semver": "^7.3.5", "size-limit": "^11.1.2", "typescript": "^5.1.6", "@types/node": "^20.4.9", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^1.4.0", "@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_6.2.2_1712463391287_0.****************", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "path-to-regexp", "version": "7.0.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@7.0.0", "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "0222f42e986de6b63e6bbc6dab6a79f5311b9abb", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-58Y94bQqF3zBIASFNiufRPH1NfgZth1qwZ35radL87sg8pgbVqr6uikAhqZtFD+w65MGH6SWnY/ly3GbrM4fbg==", "signatures": [{"sig": "MEYCIQCIZcVA3GyklNDscz8Rs0nvFPfeG4/SDZyQDXZG+bGPUQIhAKngKKvMQDoEdgZsqGPBcwsx6fiOMXro21NhE14xw1c6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59789}, "main": "dist/index.js", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "gitHead": "ec35fbd500a08a7b06e45f2e23dae4b0a3690a54", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2.2 kB"}], "ts-scripts": {"dist": ["dist"], "project": ["tsconfig.build.json"]}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"size-limit": "^11.1.2", "typescript": "^5.1.6", "@types/node": "^20.4.9", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^1.4.0", "@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_7.0.0_1718925050489_0.****************", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "path-to-regexp", "version": "7.1.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@7.1.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "f4ee387b4ff85829e3f863ecb63f61a57aa635a1", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-<PERSON><PERSON><PERSON>+MbUF4lBqk6dV8GKot4DKfzrxXsplOddH8zN3YK+qw9/McvP7+4ICjZvOne0jQhN4eJwHsX6tT0Ns19fvw==", "signatures": [{"sig": "MEYCIQDPbP3dkoh7Y/unEDQFD1mluddKWGGodeTNUIgp82sahAIhALmly6vk11F68zwJJim3eJja1kbpWPdZR3tF6P4MyurA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64954}, "main": "dist/index.js", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "gitHead": "c36bdfa2aa363f573439de9098c2dcc94b16e1e6", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2.2 kB"}], "ts-scripts": {"dist": ["dist"], "project": ["tsconfig.build.json"]}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"recheck": "^4.4.5", "size-limit": "^11.1.2", "typescript": "^5.5.3", "@types/node": "^20.4.9", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^1.4.0", "@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_7.1.0_1720914581765_0.3917942273058195", "host": "s3://npm-registry-packages"}}, "0.1.9": {"name": "path-to-regexp", "version": "0.1.9", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.9", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/component/path-to-regexp#readme", "bugs": {"url": "https://github.com/component/path-to-regexp/issues"}, "dist": {"shasum": "8c7bfecca31b4c83c29ddc7899269aa2794f3311", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.9.tgz", "fileCount": 4, "integrity": "sha512-AM+ti/Ap918nCDfNf+6kpD5PCTZipPOcuDs3CHfi/X5Zk+GauD99pinQosrG6cM59MSUremZPByf82kpHiiEqA==", "signatures": [{"sig": "MEQCIGBkAWPLXRIB+UZ0c0Ki1liArBkkDcMKJT9ebISaLB+gAiBlQSHWJUDtRdqZc91qSm9++9P5Y3VblKraFw4n4RuG3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6210}, "gitHead": "bdb663527106d51f149b12f74fca1018c20c8a53", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/component/path-to-regexp.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_0.1.9_1724290123498_0.024527765076679753", "host": "s3://npm-registry-packages"}}, "0.1.10": {"name": "path-to-regexp", "version": "0.1.10", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.10", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "67e9108c5c0551b9e5326064387de4763c4d5f8b", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.10.tgz", "fileCount": 4, "integrity": "sha512-7lf7qcQidTku0Gu3YDPc8DJ1q7OOucfa/BSsIwjuh56VU7katFvuM8hULfkwB3Fns/rsVF7PwPKVw1sl5KQS9w==", "signatures": [{"sig": "MEUCIQDEf1fixatKhLFxqJpMfbMlxQ6tQobFk34zmNrq6bfz0wIgXWSXEhnk5602kK87ihZXNU5+2+MtQ8Pqwsx6u0oerbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6376}, "gitHead": "c827fcea751b2e9ec6ca68a1b95a82e438728868", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_0.1.10_1725228440463_0.****************", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "path-to-regexp", "version": "8.0.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@8.0.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "92076ec6b2eaf08be7c3233484142c05e8866cf5", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.0.0.tgz", "fileCount": 6, "integrity": "sha512-GAWaqWlTjYK/7SVpIUA6CTxmcg65SP30sbjdCvyYReosRkk7Z/LyHWwkK3Vu0FcIi0FNTADUs4eh1AsU5s10cg==", "signatures": [{"sig": "MEQCICV0U4Z+RYvYUuUuz5LqIgAaHmZS/bOD0Uz/A5SXkczKAiB7peQj22CZ7+IHswHocxUwzJRXl6+kp4+1yL9/7puakg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51290}, "main": "dist/index.js", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "gitHead": "ed1095e0fa78a692e7f3d489e383e7bb1f9d2cc4", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "bench": "vitest bench", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2.2 kB"}], "ts-scripts": {"dist": ["dist"], "project": ["tsconfig.build.json"]}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"recheck": "^4.4.5", "size-limit": "^11.1.2", "typescript": "^5.5.3", "@types/node": "^20.4.9", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^1.4.0", "@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_8.0.0_1725228895545_0.****************", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "path-to-regexp", "version": "8.1.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@8.1.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "4d687606ed0be8ed512ba802eb94d620cb1a86f0", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.1.0.tgz", "fileCount": 6, "integrity": "sha512-Bqn3vc8CMHty6zuD+tG23s6v2kwxslHEhTj4eYaVKGIEB+YX/2wd0/rgXLFD9G9id9KCtbVy/3ZgmvZjpa0UdQ==", "signatures": [{"sig": "MEUCICFWYSrrJ6at96HlyWjhYayu5EWdYNLg4gBWf5d9LXwhAiEAgHLwbkEY2YIxVcgw00WmhGqIbX2ha56IMWDi9ys8Ddk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56098}, "main": "dist/index.js", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "gitHead": "c302644003b09c3a3a09ba645f44dad6eaf131d5", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "bench": "vitest bench", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2.2 kB"}], "ts-scripts": {"dist": ["dist"], "project": ["tsconfig.build.json"]}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"recheck": "^4.4.5", "size-limit": "^11.1.2", "typescript": "^5.5.3", "@types/node": "^20.4.9", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^1.4.0", "@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_8.1.0_1725927937975_0.*****************", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "path-to-regexp", "version": "7.2.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@7.2.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "3d9cc9d46527e2ce2ef7b2cf696aad3cd1ae4f2b", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-0W4AcUxPpFlcS8ql8ZEmFwaI0X5WshUVAFdXe3PBurrt18DK8bvSS+UKHvJUAfGILco/nTtc/E4LcPNfVysfwQ==", "signatures": [{"sig": "MEYCIQCbHpU93PPUp2zJ9FR7hObKe+EwBvNdd9Y8ZXiF+HjjqgIhAPj+hqzjtips5zRDRXXcYY29fxCrxpFDMyqDzYnba4An", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65430}, "main": "dist/index.js", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "gitHead": "8f67b8ba56e4666935176a22fef925b2ad26941a", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2.2 kB"}], "ts-scripts": {"dist": ["dist"], "project": ["tsconfig.build.json"]}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"recheck": "^4.4.5", "size-limit": "^11.1.2", "typescript": "^5.5.3", "@types/node": "^20.4.9", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^1.4.0", "@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_7.2.0_1725929847452_0.****************", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "path-to-regexp", "version": "1.9.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@1.9.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "5dc0753acbf8521ca2e0f137b4578b917b10cf24", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-1.9.0.tgz", "fileCount": 5, "integrity": "sha512-xIp7/apCFJuUHdDLWe8O1HIkb0kQrOMb/0u6FXQjemHn/ii5LrIzU6bdECnsiTF/GjZkMEKg1xdiZwNqDYlZ6g==", "signatures": [{"sig": "MEUCIQDkF7ilivSfE1SlrXJQuxnjuq9U3qhFGVQlbs0OTDH8hwIgbdNTRuHn4Kkdrbu6xtOUtAfOUKWanLlRw4Zp76xLNeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24223}, "main": "index.js", "gitHead": "c75eb105b2a177822c1dfd58e0e032320cd868ff", "scripts": {"lint": "standard", "test": "npm run test-cov", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec test.js", "test-spec": "mocha -R spec --bail test.js"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "dependencies": {"isarray": "0.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"chai": "^2.3.0", "mocha": "~2.2.4", "ts-node": "^0.5.5", "istanbul": "~0.3.0", "standard": "~3.7.3", "typescript": "^1.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_1.9.0_1726003548298_0.****************", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "path-to-regexp", "version": "3.3.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@3.3.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "f7f31d32e8518c2660862b644414b6d5c63a611b", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-3.3.0.tgz", "fileCount": 5, "integrity": "sha512-qyCH421YQPS2WFDxDjftfc1ZR5WKQzVzqsp4n9M2kQhVOo/ByahFoUNJfl58kOcEGfQ//7weFTDhm+ss8Ecxgw==", "signatures": [{"sig": "MEUCIQCyjL62ARnsY+NFZLclhctZFotT/5zBMw8nNkCyjIsoVAIgOmQJJ+6I2EJvD+Cq8eSd+LnTi9xYRoUt+lEQZZuAbiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25834}, "main": "index.js", "gitHead": "2eb12934fc1f15d3b9bad010709717fc53a14b8e", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-cov", "coverage": "nyc report --reporter=text-lcov", "test-cov": "nyc --reporter=lcov mocha -- --require ts-node/register -R spec test.ts", "test-spec": "mocha --require ts-node/register -R spec --bail test.ts"}, "typings": "index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.1.1", "mocha": "^6.2.0", "ts-node": "^8.3.0", "standard": "^14.1.0", "typescript": "^3.7.2", "@types/chai": "^4.0.4", "@types/node": "^12.7.3", "@types/mocha": "^5.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_3.3.0_1726005858115_0.*****************", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "path-to-regexp", "version": "6.3.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@6.3.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "2b6a26a337737a8e1416f9272ed0766b1c0389f4", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-6.3.0.tgz", "fileCount": 8, "integrity": "sha512-Yhpw4T9C6hPpgPeA28us07OJeqZ5EzQTkbfwuhsUg0c237RomFoETJgmp2sa3F/41gfLE6G5cqcYwznmeEeOlQ==", "signatures": [{"sig": "MEYCIQD/rwtLaBXcUnGYGyLq6+Vt8VAgEF2MZujm24KHr8bijwIhAJ6audrRkPV4grpNJ6ePYuNvBEreDBd/6JTGbrwRrqMo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112042}, "main": "dist/index.js", "module": "dist.es2015/index.js", "gitHead": "75a92c3d7c42159f459ab42f346899152906ea8c", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist.es2015/index.js", "limit": "2.1 kB"}], "ts-scripts": {"dist": ["dist", "dist.es2015"], "project": ["tsconfig.build.json", "tsconfig.es2015.json"]}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"semver": "^7.3.5", "recheck": "^4.4.5", "size-limit": "^11.1.2", "typescript": "^5.1.6", "@types/node": "^20.4.9", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^1.4.0", "@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_6.3.0_1726103375853_0.0789731783347889", "host": "s3://npm-registry-packages"}}, "0.1.11": {"name": "path-to-regexp", "version": "0.1.11", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.11", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "a527e662c89efc4646dbfa8100bf3e847e495761", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.11.tgz", "fileCount": 4, "integrity": "sha512-c0t+KCuUkO/YDLPG4WWzEwx3J5F/GHXsD1h/SNZfySqAIKe/BaP95x8fWtOfRJokpS5yYHRJjMtYlXD8jxnpbw==", "signatures": [{"sig": "MEQCIHh6ebSfy0SNMK3PTN77v/4rkCZ/E0UDVcL8dpAc59CcAiATtZuGsBi6bZEe49pEelcjiJGJuGGDKAbFXq/G4gzH5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6506}, "gitHead": "0c7119248b7cb528a0aea3ba45ed4e2db007cba4", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_0.1.11_1726164893574_0.*****************", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "path-to-regexp", "version": "8.2.0", "keywords": ["express", "regexp", "route", "routing"], "license": "MIT", "_id": "path-to-regexp@8.2.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "73990cc29e57a3ff2a0d914095156df5db79e8b4", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "fileCount": 6, "integrity": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==", "signatures": [{"sig": "MEUCIQCzxrAh1w12TdT2L1DO+JsACm/lUDw3NolWeOnen8jtKgIgch/ZxiVSnYOLIltVnG5lJbQbSb0YL3+jNv72k2NeWT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55246}, "main": "dist/index.js", "engines": {"node": ">=16"}, "exports": "./dist/index.js", "gitHead": "776c8986b89b29a368f22c1a6c598242b67fb832", "scripts": {"lint": "ts-scripts lint", "size": "size-limit", "test": "ts-scripts test && npm run size", "bench": "vitest bench", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install && npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "size-limit": [{"path": "dist/index.js", "limit": "2.2 kB"}], "ts-scripts": {"dist": ["dist"], "project": ["tsconfig.build.json"]}, "_npmVersion": "10.5.0", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "20.12.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"recheck": "^4.4.5", "size-limit": "^11.1.2", "typescript": "^5.5.3", "@types/node": "^22.7.2", "@types/semver": "^7.3.1", "@vitest/coverage-v8": "^2.1.1", "@borderless/ts-scripts": "^0.15.0", "@size-limit/preset-small-lib": "^11.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_8.2.0_1727321173373_0.36649469104292254", "host": "s3://npm-registry-packages"}}, "0.1.12": {"name": "path-to-regexp", "version": "0.1.12", "keywords": ["express", "regexp"], "license": "MIT", "_id": "path-to-regexp@0.1.12", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "dist": {"shasum": "d5e1a12e478a976d432ef3c58d534b9923164bb7", "tarball": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "fileCount": 4, "integrity": "sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==", "signatures": [{"sig": "MEQCIG0SIXzS6C/SxbHFz7/1pw7shL2U2kBFTJayVEXCVTojAiAGQ1VQqYW+a+xVt5oXZqiMk6MSmVOmBQ5nw5ZadAcyMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6598}, "gitHead": "640e694c6fd971f78268439df9cf44040855e669", "scripts": {"test": "istanbul cover _mocha -- -R spec"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"path-to-regexp": "index.js"}}, "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Express style path to RegExp utility", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "readmeFilename": "Readme.md", "devDependencies": {"mocha": "^1.17.1", "istanbul": "^0.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/path-to-regexp_0.1.12_1733436486404_0.21783883627456158", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-08-01T22:49:08.434Z", "modified": "2025-05-09T20:10:31.766Z", "0.0.1": "2012-08-01T22:49:10.343Z", "0.0.2": "2013-02-10T17:41:48.985Z", "0.1.0": "2014-03-06T06:35:14.721Z", "0.1.1": "2014-03-10T14:41:25.104Z", "0.1.2": "2014-03-10T14:43:49.209Z", "0.2.0": "2014-06-10T03:52:35.899Z", "0.2.1": "2014-06-11T17:31:21.785Z", "0.1.3": "2014-07-06T07:26:10.022Z", "0.2.2": "2014-07-06T09:25:41.750Z", "0.2.3": "2014-07-08T23:57:48.403Z", "0.2.4": "2014-08-02T08:27:55.044Z", "0.2.5": "2014-08-07T17:35:25.995Z", "1.0.0": "2014-08-17T22:37:49.113Z", "1.0.1": "2014-08-28T01:37:57.382Z", "1.0.2": "2014-12-17T07:03:45.043Z", "1.0.3": "2015-01-17T12:07:04.806Z", "0.1.4": "2015-03-05T03:08:44.032Z", "0.1.5": "2015-05-09T02:42:27.038Z", "1.1.0": "2015-05-09T18:58:21.134Z", "1.1.1": "2015-05-12T14:47:20.532Z", "1.2.0": "2015-05-21T03:13:26.332Z", "0.1.6": "2015-06-19T12:04:42.779Z", "0.1.7": "2015-07-28T03:07:52.808Z", "1.2.1": "2015-08-17T19:25:00.465Z", "1.3.0": "2016-05-08T22:17:07.390Z", "1.4.0": "2016-05-19T05:37:48.670Z", "1.5.0": "2016-05-20T18:09:55.851Z", "1.5.1": "2016-06-08T15:34:26.314Z", "1.5.2": "2016-06-16T01:55:33.030Z", "1.5.3": "2016-06-16T03:36:35.520Z", "1.6.0": "2016-10-03T18:39:00.127Z", "1.7.0": "2016-11-08T18:38:49.258Z", "2.0.0": "2017-08-23T22:30:56.210Z", "2.1.0": "2017-10-20T17:47:16.057Z", "2.2.0": "2018-03-07T06:04:53.987Z", "2.2.1": "2018-04-24T14:26:49.071Z", "2.3.0": "2018-08-20T17:06:03.320Z", "2.4.0": "2018-08-26T23:57:15.931Z", "3.0.0": "2019-01-14T00:31:35.407Z", "3.1.0": "2019-08-31T03:59:25.875Z", "1.8.0": "2019-11-11T03:37:21.803Z", "3.2.0": "2019-11-11T05:50:20.071Z", "4.0.0": "2019-11-12T03:30:04.004Z", "4.0.1": "2019-11-12T04:08:54.217Z", "4.0.2": "2019-11-12T04:35:05.519Z", "4.0.3": "2019-11-12T06:53:30.304Z", "4.0.4": "2019-11-12T07:20:00.337Z", "4.0.5": "2019-11-12T10:35:40.566Z", "5.0.0": "2019-11-12T12:02:25.405Z", "6.0.0": "2019-11-20T04:11:51.111Z", "6.1.0": "2019-11-20T19:26:07.428Z", "6.2.0": "2020-09-29T17:23:35.481Z", "6.2.1": "2022-05-06T22:00:27.519Z", "0.1.8": "2024-04-07T03:53:53.527Z", "6.2.2": "2024-04-07T04:16:31.452Z", "7.0.0": "2024-06-20T23:10:50.676Z", "7.1.0": "2024-07-13T23:49:41.946Z", "0.1.9": "2024-08-22T01:28:43.646Z", "0.1.10": "2024-09-01T22:07:20.600Z", "8.0.0": "2024-09-01T22:14:55.756Z", "8.1.0": "2024-09-10T00:25:38.176Z", "7.2.0": "2024-09-10T00:57:27.642Z", "1.9.0": "2024-09-10T21:25:48.487Z", "3.3.0": "2024-09-10T22:04:18.308Z", "6.3.0": "2024-09-12T01:09:36.065Z", "0.1.11": "2024-09-12T18:14:53.729Z", "8.2.0": "2024-09-26T03:26:13.535Z", "0.1.12": "2024-12-05T22:08:06.588Z"}, "bugs": {"url": "https://github.com/pillarjs/path-to-regexp/issues"}, "license": "MIT", "homepage": "https://github.com/pillarjs/path-to-regexp#readme", "keywords": ["express", "regexp", "route", "routing"], "repository": {"url": "git+https://github.com/pillarjs/path-to-regexp.git", "type": "git"}, "description": "Express style path to RegExp utility", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"*********": true, "nex": true, "detj": true, "dwqs": true, "n370": true, "usex": true, "zvit": true, "elama": true, "hanhq": true, "miloc": true, "qinbx": true, "xrush": true, "456wyc": true, "bojand": true, "d4nyll": true, "deryck": true, "emarcs": true, "genovo": true, "ierceg": true, "iusfof": true, "joakin": true, "knoja4": true, "mfjv88": true, "monjer": true, "necinc": true, "pandao": true, "planir": true, "quafoo": true, "sobear": true, "tzsiga": true, "vcboom": true, "ziflex": true, "attomos": true, "boyw165": true, "cobject": true, "fxkraus": true, "itonyyo": true, "jovinbm": true, "keelvin": true, "keenwon": true, "kkho595": true, "kontrax": true, "pasturn": true, "semo100": true, "shyling": true, "staydan": true, "tomchao": true, "bapinney": true, "bonashen": true, "bruce313": true, "heineiuo": true, "leonzhao": true, "lianhr12": true, "liximomo": true, "losymear": true, "simonfan": true, "yangteng": true, "zhyq0826": true, "zuojiang": true, "abdelhady": true, "andyytung": true, "dillonace": true, "fgribreau": true, "gavinning": true, "goliatone": true, "hehaiyang": true, "i-erokhin": true, "isenricho": true, "mojaray2k": true, "myjustify": true, "papasavva": true, "shakakira": true, "sunkeyhub": true, "tomgao365": true, "wuxiaword": true, "yayayahei": true, "arteffeckt": true, "bplok20010": true, "ericyang89": true, "giussa_dan": true, "henrytseng": true, "hongyi0220": true, "justinshea": true, "leizongmin": true, "princetoad": true, "qqqppp9998": true, "rocket0191": true, "simplyianm": true, "winjeysong": true, "zhengyaing": true, "blakeembrey": true, "flumpus-dev": true, "soulchainer": true, "wangnan0610": true, "xinwangwang": true, "ahmedelgabri": true, "ayoola_moore": true, "danielrhayes": true, "darrentorpey": true, "eightyfour84": true, "hugojosefson": true, "zhenguo.zhao": true, "curioussavage": true, "fchienvuhoang": true, "ivan403704409": true, "jian263994241": true, "lulubozichang": true, "markthethomas": true, "mdedirudianto": true, "mingzhangyang": true, "stone_breaker": true, "season19840122": true, "shanewholloway": true, "uldis.sturms~at~audatex.co.uk": true}}