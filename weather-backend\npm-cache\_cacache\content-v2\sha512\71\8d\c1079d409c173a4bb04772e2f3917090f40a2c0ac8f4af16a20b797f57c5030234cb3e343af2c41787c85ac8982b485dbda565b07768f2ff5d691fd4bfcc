{"_id": "express-rate-limit", "_rev": "177-c1d782f68beef5cb41608b7e1e0d2176", "name": "express-rate-limit", "dist-tags": {"v2backports": "2.14.2", "backport-3.x": "3.5.3", "typescript": "0.0.0-typescript-beta-7", "latest": "8.0.1"}, "versions": {"1.0.0": {"name": "express-rate-limit", "version": "1.0.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@1.0.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "f1effdc270696b339c944bbe1213c7a1e80eaed5", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-1.0.0.tgz", "integrity": "sha512-nX6SAFDyUdokU1qgl8wqXBpl19rmh3SXRcaC5mi6BRS5iTPj74vbxzputq/YPiqZpfSPOYUIsQGQZZ5BMMNzbg==", "signatures": [{"sig": "MEUCIFB6WyYjW99tLnzb45f5HVV4xTQSkYWLwPKrsvGWEIVGAiEAyx4/V8URxtw48+o6cdTIA9A41WS+VFKo0EZ0+Nue04w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f1effdc270696b339c944bbe1213c7a1e80eaed5", "gitHead": "a563bdce8ed17d92b61b4d8f4267081aa4b8f54a", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nfriedly/express-rate-limit", "type": "git"}, "_npmVersion": "1.4.28", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "dependencies": {"defaults": "^1.0.0"}, "devDependencies": {"express": "^4.10.4", "grunt-cli": "^0.1.13", "supertest": "^0.15.0", "time-grunt": "^1.0.0", "jshint-stylish": "^1.0.0", "grunt-mocha-cli": "^1.11.0", "load-grunt-tasks": "^1.0.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-nodeunit": "^0.4.1"}}, "1.0.1": {"name": "express-rate-limit", "version": "1.0.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@1.0.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "445d59a9bbdd910ffcff722b8722e293c8d8283b", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-1.0.1.tgz", "integrity": "sha512-Y2Cp1ULBRKTkWKVfDY296pH3oR/SFQ8lfJjoCMlIeA1g8GbxDdRzaSwFJLNZxY1F0Oi83hbeO2Av6jw7phgA6w==", "signatures": [{"sig": "MEQCID/fY5hWtpuTQ05mqUJljcE/bYNMGRCgg7B7IrA09wOHAiAswoyNfIE6MmVJAQevR1Ymeixtpy/r5lNRVmy8G5x1lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "445d59a9bbdd910ffcff722b8722e293c8d8283b", "gitHead": "d76f8ae86b0a589557baec689f68d8213b6fbab8", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nfriedly/express-rate-limit", "type": "git"}, "_npmVersion": "1.4.28", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "dependencies": {"defaults": "^1.0.0"}, "devDependencies": {"express": "^4.10.4", "grunt-cli": "^0.1.13", "supertest": "^0.15.0", "time-grunt": "^1.0.0", "jshint-stylish": "^1.0.0", "grunt-mocha-cli": "^1.11.0", "load-grunt-tasks": "^1.0.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-nodeunit": "^0.4.1"}}, "1.0.2": {"name": "express-rate-limit", "version": "1.0.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip", "auth", "authorization", "security"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@1.0.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "899005e6f9ccae77d756011a0fb3dbbd1fbb9551", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-1.0.2.tgz", "integrity": "sha512-jxoGxJABJbvYQ0hg0lQCr7IIVZHuqQ/yQeI/V+LB+GzFrLY/nKyqGfdPzzWCXMmL0wFaR8WrWmDKM5z5RnpkXQ==", "signatures": [{"sig": "MEUCIGCuHNk1YGd+X9HN32lOdOWEzD4B6bNayH2+VEcwGle/AiEA1zFLzIpSBg5SNC4J7oUv/Tzkhh1UO52CcjZvj6AgXsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "899005e6f9ccae77d756011a0fb3dbbd1fbb9551", "gitHead": "d3662088d848138bf3cfb56193a45914063e83e0", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/nfriedly/express-rate-limit", "type": "git"}, "_npmVersion": "1.4.28", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "dependencies": {"defaults": "^1.0.0"}, "devDependencies": {"express": "^4.10.4", "grunt-cli": "^0.1.13", "supertest": "^0.15.0", "time-grunt": "^1.0.0", "jshint-stylish": "^1.0.0", "grunt-mocha-cli": "^1.11.0", "load-grunt-tasks": "^1.0.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-nodeunit": "^0.4.1"}}, "1.0.3": {"name": "express-rate-limit", "version": "1.0.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip", "auth", "authorization", "security"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@1.0.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "1649c4b582bf6fd8c068310eca64960b308b8a6b", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-1.0.3.tgz", "integrity": "sha512-3GGpSBkS8VW6H8Bi+SZ/OUaLtXNtYOb4w3ObeqaDX66/sOLb18irrewTEkwjSN1MIfg6artHtbkrsY5U3aUnZw==", "signatures": [{"sig": "MEUCIQC5tDTg1aDxrpNwvOUPIuIm+rmEXdMLfkrKxPfRIn+iqAIgboUIVjjzZ4KO3t/yftXim1b1EQL4nS0Hw/DQ0W7sUkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "1649c4b582bf6fd8c068310eca64960b308b8a6b", "gitHead": "66182cf81f7c47a0120d27d961051dbf6ea72033", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"defaults": "^1.0.0"}, "devDependencies": {"express": "^4.10.4", "grunt-cli": "^0.1.13", "supertest": "^0.15.0", "time-grunt": "^1.0.0", "jshint-stylish": "^1.0.0", "grunt-mocha-cli": "^1.11.0", "load-grunt-tasks": "^1.0.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-nodeunit": "^0.4.1"}}, "1.1.0": {"name": "express-rate-limit", "version": "1.1.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip", "auth", "authorization", "security"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@1.1.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "11333f52aa15e816acc35e12297383897e703c24", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-1.1.0.tgz", "integrity": "sha512-a+gUUzge4sMsKpzD5Ez2bxG4CZLR+7lTgH8k22mhrHOam2RRlARXMS6KIeAZNxB79Z7H7UecyIMO7DzjZnxLSQ==", "signatures": [{"sig": "MEUCIAOftXyBb2bV1xcjsnDIm0sECZdN73B909hEqGv+c9UxAiEAmVu+lB/sTn4f5m92DXhcTSRb96BIVjHDiQr7ZnJr6Ws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "11333f52aa15e816acc35e12297383897e703c24", "gitHead": "c539dd15d98c2a31687ff80a8f861c0db29d6ece", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"defaults": "^1.0.2"}, "devDependencies": {"sinon": "^1.16.1", "express": "^4.13.1", "grunt-cli": "^0.1.13", "supertest": "^1.0.1", "time-grunt": "^1.2.1", "jshint-stylish": "^2.0.1", "grunt-mocha-cli": "^1.14.0", "load-grunt-tasks": "^3.2.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-nodeunit": "^0.4.1"}}, "1.2.0": {"name": "express-rate-limit", "version": "1.2.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip", "auth", "authorization", "security"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@1.2.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "d83fc00ecee970393d6460d704863c700fcc8d73", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-1.2.0.tgz", "integrity": "sha512-a3wabx96IkpdP6YznPO8GYIk11FYkt6kvsmZe30qrjitrJgHy6ZgZp6rGJuLoFzyr95vvQtJv/upCRdZmWUpxQ==", "signatures": [{"sig": "MEYCIQC5yomCjA8DVzKBcSweEQBedmo2bT+AYs0StpgqerQVkwIhAJN2K1R/1vZOhV4pHgt9liK+e8kR4Ig7VG4NZbVnOReS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "d83fc00ecee970393d6460d704863c700fcc8d73", "gitHead": "f6be83244fa3d1d6cb04d779a50a79048e57969e", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"defaults": "^1.0.2"}, "devDependencies": {"sinon": "^1.16.1", "express": "^4.13.1", "grunt-cli": "^0.1.13", "supertest": "^1.0.1", "time-grunt": "^1.2.1", "jshint-stylish": "^2.0.1", "grunt-mocha-cli": "^1.14.0", "load-grunt-tasks": "^3.2.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-nodeunit": "^0.4.1"}}, "2.0.0": {"name": "express-rate-limit", "version": "2.0.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip", "auth", "authorization", "security"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.0.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "13c519f20fa02270246a1b204c2a543418d432c9", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.0.0.tgz", "integrity": "sha512-nVqnp7yLDzMjerDODqEm+AueNYKXjZykzweHKtnXaKZJD/WqenRgXLJV5+XERbwZ1DQZEH0qfFR0mu9Ib+nFSg==", "signatures": [{"sig": "MEYCIQDjPPOVDvscnddjzZTzRbBkAtW1/toOIdS07T+tJU27tQIhANemlmfga7oFl8UhGyzkkoxZEjDpY22e9S6ryMqdCZq1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "13c519f20fa02270246a1b204c2a543418d432c9", "gitHead": "961bf1ea0c1c57bbf810bdb9d85ca62ef5720c7b", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"defaults": "^1.0.2"}, "devDependencies": {"sinon": "^1.16.1", "express": "^4.13.1", "grunt-cli": "^0.1.13", "supertest": "^1.0.1", "time-grunt": "^1.2.1", "jshint-stylish": "^2.0.1", "grunt-mocha-cli": "^1.14.0", "load-grunt-tasks": "^3.2.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-nodeunit": "^0.4.1"}}, "2.0.1": {"name": "express-rate-limit", "version": "2.0.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip", "auth", "authorization", "security"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.0.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "4bdf0c3166fc1f5e103a4bf7c9f1f5cbc674e5c7", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.0.1.tgz", "integrity": "sha512-DlACcLWrp9kU1z04nI21ZB1q806TtkdR2k0IVyLnC9ujM1556H6sp8xV8lYOK2DiYbFumwro5Z/MG2/bmPDp2Q==", "signatures": [{"sig": "MEQCIGn4jrZAabLPzYw6SoKvvOnR8jCg3rrsG1PfE46JUEnWAiBlNH7xRNY4LpA2Gpz8m4q693VQ/IrG44tV9QLQ2LCFuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "4bdf0c3166fc1f5e103a4bf7c9f1f5cbc674e5c7", "gitHead": "75f3055b9500327b74d740901a2fea669b6aa66a", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"defaults": "^1.0.2"}, "devDependencies": {"sinon": "^1.16.1", "express": "^4.13.1", "grunt-cli": "^0.1.13", "supertest": "^1.0.1", "time-grunt": "^1.2.1", "jshint-stylish": "^2.0.1", "grunt-mocha-cli": "^1.14.0", "load-grunt-tasks": "^3.2.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-nodeunit": "^0.4.1"}}, "2.0.2": {"name": "express-rate-limit", "version": "2.0.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip", "auth", "authorization", "security"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.0.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "046b4aa0b6b911a11cdb782220ab98e221d1ac2a", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.0.2.tgz", "integrity": "sha512-BZ+lH8ipT8M0CWTfzAS5+HMUDQ9XZsypTo/N5OQ8IOIC2MY/UUV+4XvZMDJKIddQx545qgK7Lrdli0FHtxwKMg==", "signatures": [{"sig": "MEYCIQC01HLHgyz3WxO2eE3GpI1XVV36c4CwIffpRJJkzuuq2gIhAK3wq1xCSXGJXKI9pJDIJxMymSeiWSwoKF4hy00gvUJB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "046b4aa0b6b911a11cdb782220ab98e221d1ac2a", "gitHead": "7d220e0ea140a0462ca993ddb4d72a4113f6aad7", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"defaults": "^1.0.2"}, "devDependencies": {"express": "^4.13.3", "grunt-cli": "^0.1.13", "supertest": "^1.1.0", "time-grunt": "^1.2.1", "jshint-stylish": "^2.0.1", "grunt-mocha-cli": "^1.14.0", "load-grunt-tasks": "^3.3.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.3", "grunt-contrib-nodeunit": "^0.4.1"}}, "2.1.0": {"name": "express-rate-limit", "version": "2.1.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "middleware", "ip", "auth", "authorization", "security"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.1.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "16b99162bb6a5edb35dfd985c4d9519530ffa086", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.1.0.tgz", "integrity": "sha512-SoAQ9+NcjHCVsCWbcU2kbCxATCPwd/J5CBZp07rZ+2tskNPaTAz5fFAE8SaRRGaHDzRwS8a1E9Ah0LBXhA/BCQ==", "signatures": [{"sig": "MEUCIQCj1sGKZd5nO5choiTa2hePqjNIz+wzUvIJTdKLh52OcAIgd7LOAzVeCNAWwtG5oU2hBVw5P0dxY1QaJxe7gcnXZcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "16b99162bb6a5edb35dfd985c4d9519530ffa086", "gitHead": "42feb182036e7164c991a767e13a0721dfe66462", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^0.4.5", "express": "^4.13.3", "grunt-cli": "^0.1.13", "supertest": "^1.1.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^2.0.0", "load-grunt-tasks": "^3.4.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.12.0", "grunt-contrib-nodeunit": "^0.4.1"}}, "2.1.2": {"name": "express-rate-limit", "version": "2.1.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.1.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "c503a3250ab012d0e4a004045acc61c8877f74f2", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.1.2.tgz", "integrity": "sha512-lL2hh+Upeoa83E3oEqE8lt43g92Kn426wWIi+uMZC9DqTdaavxQ6xOj+gKb27cqI0SL8WZo9O//0FWGrDOwJiw==", "signatures": [{"sig": "MEUCIQCTNvpUu8sx40g+Oqve2UmXsSTCeyy0ESE/rLvhlMqUzQIgDDCLIontkzXigyWH7VmDRZdxcyTIrtyq0z8cAiwnnsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "c503a3250ab012d0e4a004045acc61c8877f74f2", "gitHead": "c06b3bfa573de42df835d0ae55db9012f3b57675", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "4.4.1", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^0.4.5", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^1.1.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^2.0.0", "load-grunt-tasks": "^3.4.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.1.2.tgz_1459341288093_0.***************", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.3": {"name": "express-rate-limit", "version": "2.1.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.1.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "dffd783388e3d0bab1644541a390ce31841fc974", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.1.3.tgz", "integrity": "sha512-0J3nP96a4U7an7SaZ1KtbLFnsggS1huMUL3w5jvo01BXs00pD0d9OGdizW6yAJvf4NB7vew1uHPHkOjlQn1mUg==", "signatures": [{"sig": "MEQCIEIso1iR8oetI7AN8VgW6C6LvOrFMm/NQw0vtLmWFwdIAiAm5xz6EpQK7nsUygU1ZYABD7fNrdBq+g1XVkrWEYrhZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "dffd783388e3d0bab1644541a390ce31841fc974", "gitHead": "08950d7dd21004f15ca55233c664bf43a997992c", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Basic rate-limiting middleware for Express. Use to limit access to public endpoints such as account creation and password reset.", "directories": {}, "_nodeVersion": "5.11.0", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^0.4.5", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^1.1.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^2.0.0", "load-grunt-tasks": "^3.4.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.1.3.tgz_1461592919783_0.****************", "host": "packages-16-east.internal.npmjs.com"}}, "2.2.0": {"name": "express-rate-limit", "version": "2.2.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.2.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "fc69dcf595e8ec01a361f29e949d637ede63791d", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.2.0.tgz", "integrity": "sha512-ChdZomTvmeam0vEA+BgAhSLypjFSx2GkmB2nCPH5sV8t36gzX1fF1WFe3kO+vBaRqfzAT9UYWmcwEFjMMetfzQ==", "signatures": [{"sig": "MEYCIQCGh5Pl/HFPSPcWBMYqrTv5J/HdIFUUNfoO8bv6ZlTiSwIhAK2FnYGjQ4VZAAi/FxA9cJiHSh7IPbMl9881NTtk8OUi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "fc69dcf595e8ec01a361f29e949d637ede63791d", "gitHead": "78d568ddac0639007a61f6af87ac1db7e437f98e", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "5.11.0", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^1.1.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^2.1.0", "load-grunt-tasks": "^3.4.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.2.0.tgz_1461592977922_0.2762043869588524", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.0": {"name": "express-rate-limit", "version": "2.3.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.3.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "673ccaebc2d5654aa47a055be72678e738210d6f", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.3.0.tgz", "integrity": "sha512-XTRMmv7vIjlIijnbdhQ9H/eNfD4SV8NoRyZv/WsfiZrtPnFpb+RH39wHhf4pBDKY3puX4ub3h6v12uVd0xS3TA==", "signatures": [{"sig": "MEUCIQCp/KJBZO8lO2wRwv0MqJhuxPywIlQvSss0jXmri7oNcwIgMFJk1h315xe2COkSE99sutWcpsYubZNQ0F29k/DkoFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "673ccaebc2d5654aa47a055be72678e738210d6f", "gitHead": "5412d1b6a53f272bb07e15c3f6d592d7caf66888", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "4.4.4", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^1.1.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^2.1.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.3.0.tgz_1463597655925_0.9663033746182919", "host": "packages-16-east.internal.npmjs.com"}}, "2.3.1": {"name": "express-rate-limit", "version": "2.3.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.3.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "e6b0fa88d81ec0002c0a2ea5e5658f902c0a857f", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.3.1.tgz", "integrity": "sha512-DvpXNczHBDZgp3dKKjKAIgRf404lWOcjZpIR/cm7ost66cXZwoBkNNWQDExKeZbz2GOQHbioNqfQ5I0cQu6KdA==", "signatures": [{"sig": "MEQCIBD/v6dOe5kUsK0G1nzXXuStm4KBH3bIpAbv3n1Bkey5AiAJUHOYUZUCGR9vKiQkA0bN1a7ORKrWOoczrNQxEUIFOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "e6b0fa88d81ec0002c0a2ea5e5658f902c0a857f", "gitHead": "0ffc4a4cd5d437305fb43d17de220c909e43fb9e", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^1.1.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^2.1.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.3.1.tgz_1463597896128_0.4810517805162817", "host": "packages-16-east.internal.npmjs.com"}}, "2.4.0": {"name": "express-rate-limit", "version": "2.4.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.4.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "456eea8c85e38972e49eae7d07d6ea59e00020a5", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.4.0.tgz", "integrity": "sha512-acCyjhpt8Rllg0gqLrx1vS//JnGRWK44rrHrQh47m/AJLyrltnFhzlPWiaaFUwmrO7x7u++xR3BIDkCyrDHTrA==", "signatures": [{"sig": "MEYCIQC+xXW78BtVEVE5dwz4pa71lPYwK5ZxBrb+HJkwMjQJnAIhAMHZsalBOe7GrVBo6OiV1bkXRWNAuafjqA1ZzoqvCle3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "456eea8c85e38972e49eae7d07d6ea59e00020a5", "gitHead": "795be3c9e56cddb1f1531746265d83e986a08ddf", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^1.1.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^2.1.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.4.0.tgz_1468006976777_0.9473255064804107", "host": "packages-16-east.internal.npmjs.com"}}, "2.5.0": {"name": "express-rate-limit", "version": "2.5.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.5.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "17f0cf8e4b69385e71d3592b80860aeb151f4cde", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.5.0.tgz", "integrity": "sha512-GSwVOX9F85PDJOh77WWBhQxxvn8SxgLBzdKPI/FBUPsGWsX2K5WJKMXhUke/zzK2wnNFRakfmGBOz4+Ex4vHJA==", "signatures": [{"sig": "MEYCIQDlJkZ2oRs0K1f55w8rB9+/5R0gu00J+gMjl89dzuo1wAIhAJHtNNRggL3KvlG6Q4wSp4Zoq41q2dVYSzk9lqwi0Glm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "17f0cf8e4b69385e71d3592b80860aeb151f4cde", "gitHead": "4a47beddb0874babcbcc5b25a113224c48474272", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^2.0.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^2.1.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.5.0.tgz_1473895010766_0.6559324448462576", "host": "packages-16-east.internal.npmjs.com"}}, "2.6.0": {"name": "express-rate-limit", "version": "2.6.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.6.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "ecd359e15aa7f596dc80a604555765c02a3b2436", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.6.0.tgz", "integrity": "sha512-EUd/Th+jEh4+8R9DQEJjjclllyYmAV8Gg+aNUul2899xcSq+H+B7qxDPILQ+tyvMY614bPpDqIv1YKE3JW3ygg==", "signatures": [{"sig": "MEQCIFvX3c3OHASACHx84N1JjzlLm3A+dOgdCf9eXV82FKbyAiBuh0iqTbgg/gzBJpTqmDkuoY9yt50mdAjRA46F7d1gwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "ecd359e15aa7f596dc80a604555765c02a3b2436", "gitHead": "9e2f76407c9ce5bcd401a52634fecdfaa49ba3d4", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^2.0.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^3.0.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.6.0.tgz_1479481705264_0.7925162492319942", "host": "packages-12-west.internal.npmjs.com"}}, "2.7.0": {"name": "express-rate-limit", "version": "2.7.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.7.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "259c6b1c15c48c08c972e5e3c74dd772b7a179d5", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.7.0.tgz", "integrity": "sha512-RTXUZrbJH2Hzdvn5KsziSqnKC00HP7oa+zq421AJ5nAXHPQvJbxUmzDCT+L5QSnSK0W8uSyDgkelivIWcjCbiQ==", "signatures": [{"sig": "MEQCIBEB6HpLCczhJ10PRmRIFdptbBCKWZRmJjkWFSBubisvAiB6PP+iceEJsI2VMnFfWA4DDVDjP6+mI442Xhjk3VM/4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "259c6b1c15c48c08c972e5e3c74dd772b7a179d5", "gitHead": "abf118c1e60097bc138bc61e29630d2da12429bd", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "6.10.2", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^3.0.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^3.0.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.7.0.tgz_1493736523150_0.9613334382884204", "host": "packages-12-west.internal.npmjs.com"}}, "2.8.0": {"name": "express-rate-limit", "version": "2.8.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.8.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "678ea64fe339b5d7c64c4df81538bb12a5ef1cc4", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.8.0.tgz", "integrity": "sha512-FrKQAn6eW3vthVVod64ELvgtvXC6DUzDKlimNd6lyWAXzKZZaa9vd32oc1D8vJnBpr5Sw6DUcfvRIduOSrYVBw==", "signatures": [{"sig": "MEYCIQCjKCfMOQ0rhRrbDcQsS3qtvebyksSgo1gSkaRi90szWQIhANVLv4znECAuwienUIOE8MWld+ePOcr1lFWrAMcbpHYe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "678ea64fe339b5d7c64c4df81538bb12a5ef1cc4", "gitHead": "d798a80e8970c5fb28f1f3ac63bf878564cfc692", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^3.0.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^3.0.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.8.0.tgz_1494871479015_0.41359540610574186", "host": "packages-18-east.internal.npmjs.com"}}, "2.8.1": {"name": "express-rate-limit", "version": "2.8.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.8.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "8b02904f359686393c816c32fbe6887c798f78af", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.8.1.tgz", "integrity": "sha512-doZHf5YA9Dyph9ynMHTh3NniDRUl9+lO8cRb72FQB9JF+86wbHg4JwhvRrbLRkxZ4x/DHYOxbAZI2qr3QqfZIA==", "signatures": [{"sig": "MEYCIQD4dTaw5/zouCAi8CsK2dbLmV81ib5rWVWew7+r5+XWkwIhAMVLkyhTEIRynzmxuJkcmENVngWZeGTF6Mbri7/aXNud", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "8b02904f359686393c816c32fbe6887c798f78af", "gitHead": "dd9d7241655a99af7ab96defa682879c22ece35a", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "4.8.4", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^3.0.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^3.0.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.8.1.tgz_1501011480271_0.6986723390873522", "host": "s3://npm-registry-packages"}}, "2.9.0": {"name": "express-rate-limit", "version": "2.9.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.9.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "62c29fc939d72f0a03a87428c647fc4ee0d15978", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.9.0.tgz", "integrity": "sha512-rnxC466PDesVEwpqwC8Yuc4ecvScXuy7cGNMB/nMsjlWHcX/8YbsHr9TgTHHHUTEX7+884TQr2roPTI1fsIyqw==", "signatures": [{"sig": "MEUCIQDCH47bQ/wA7i+kvnP/sXRPe+7iP5FHrR/sGWRwW9fIDwIgYIZWsxLtdntCmVSaZ8WD8oDeh8hFX+J2qU55yfam7H0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "62c29fc939d72f0a03a87428c647fc4ee0d15978", "gitHead": "7cb873a9e82848326bac2feafc5c2e8811780c5f", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "4.8.4", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.13.3", "grunt-cli": "^1.0.0", "supertest": "^3.0.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^3.0.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.9.0.tgz_1501099569659_0.24823052226565778", "host": "s3://npm-registry-packages"}}, "2.10.0": {"name": "express-rate-limit", "version": "2.10.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.10.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "b6eb37395f9385940642932f9e67bdd2a48b7472", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.10.0.tgz", "integrity": "sha512-odA9+r+3x9OjzwSCPA6UG4FgqpDiW2eikumXuPrUDyUaIGImM1eqy/6Px9CzCV4vt1ziskCpsUW1AO9/KMdJ0w==", "signatures": [{"sig": "MEUCIQC2D/+XWzZ8JXEF6tK5fQ1zJ5U42GpoPFzvEXmvQ8Eb4wIgOhHyJulc3XDlb8Xegad5y5dIXXA3dd20T2nuCe36UeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "6cd60034273fd58533d061f81652383bc897eeeb", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.16.2", "grunt-cli": "^1.0.0", "supertest": "^3.0.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^3.0.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.1.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.10.0.tgz_1512921494243_0.010755656054243445", "host": "s3://npm-registry-packages"}}, "2.11.0": {"name": "express-rate-limit", "version": "2.11.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.11.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "092122218c86eddb56fb350f431e522fb8024ea9", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.11.0.tgz", "integrity": "sha512-KMZayDxj3Wr7zYuwTuDZj5hMW0nhnyJVBVCwMEVKwMdW6CkYh4vnfnUbRJYhKC0v6UuIbPerwKY0dqWmEzFjKA==", "signatures": [{"sig": "MEUCIQDc4SObQ9QcKsODSbR7cuhleh0bXhWl2NXODzpzpgd1FQIgGgbxRyessc4eV+u1fjUbekE7313Mr2CUiL6s/hkm1Og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "f893d58b59d9bece36fcd3280fdaf30cf5ab289f", "scripts": {"test": "grunt"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"grunt": "^1.0.1", "express": "^4.16.2", "grunt-cli": "^1.0.0", "supertest": "^3.0.0", "time-grunt": "^1.3.0", "jshint-stylish": "^2.1.0", "grunt-mocha-cli": "^3.0.0", "load-grunt-tasks": "^3.5.0", "grunt-contrib-watch": "^1.0.0", "grunt-contrib-jshint": "^1.1.0", "grunt-contrib-nodeunit": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit-2.11.0.tgz_1512934858579_0.4344173970166594", "host": "s3://npm-registry-packages"}}, "2.12.1": {"name": "express-rate-limit", "version": "2.12.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.12.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "ee5943a30c8285db3add6fc943f206cdc9d6ac01", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.12.1.tgz", "fileCount": 4, "integrity": "sha512-TdRwaU49cxPmiFcOcqH1cOcZwt9z2+2y/bBQCZJUao7ZDHe7k9ifHhGPgVzHNDZjla0YO2mFSyrMmvXsghVs7Q==", "signatures": [{"sig": "MEQCIB5nBNNWem+vfStmiVvOemqmtGgbwSkBph6n7rBrsZ7qAiBw/8VaPdZA2YrPVM6ozs8uKpTXK6YzrfJaayGr/rW04w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14364, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZGAoCRA9TVsSAnZWagAALU8QAIVkCTMRREyyzTZA3ZDI\neNAdHzlY5b/tcDot7Tqkg/qVMTv8kh4Fw5NCiHrHeYAD7F0YLAhfUqrUcRGj\nNr/z41FT4RxPRuELzSJHvZPzUVbj25KKAnfTFniJA+TGRCF6EC+7g5Wz+WVO\nt6tHIGiMt6pzUuiu6MXAWIixbLQX0++imJ8iANw9Qc7rVTNHyTxPKHYdjyvZ\nhwD/F6Qnd3gjOPcDDY/5bYpYOyc81L+nhrhoFT1Z36yaM7MwOdaYjhK87B90\n/WHdguxhQJnquBHYWi1SZ9O3D4xCg9OkW8tb8wxXSSfC32zKem0AnXI+JBUr\nKDSwvF9I8oDoO/ePXoT6IGUWIIMrjXp4Ed/xxZKYULlpR00V3TVf/hL/kI2e\n4WNiuCPXYpStI3mL1K0R3BZgZrIYQb7+3IJEl47opImbOIvSTdD6VO6G+jrI\nq/frdgKVsqOCzeW8bYI1Ieob2S0o4Pw13sf7HoIR+n1Phr6DCXLQvznFp4U4\nCQ15O+tw3lBMxKTGGA7H/K5YW9x9YCyCawhW7rouMPA4Rr2+h30JWQikPNFN\ng9k3Ck2zHTI6RwKqXNcd0LYO55kG8Y4bBIDKQVFmWuqkIm7DA5+WfS5LOC1i\naBlFv9IV3tNfRYV3OWU0rzbo1M96fFgHkXaLq3vjXTj3NMQOnkvOSBx7OQwk\nkYel\r\n=x+sn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "f7a8de3299f60de33497d3eadb0329877431769b", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_2.12.1_1533304871666_0.23338250077409572", "host": "s3://npm-registry-packages"}}, "2.12.2": {"name": "express-rate-limit", "version": "2.12.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.12.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "bb4a04e8dc246340610824dd987c648cdef04ce1", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.12.2.tgz", "fileCount": 4, "integrity": "sha512-83yAEGbPZuTdFQSvR58JTxK+MxctawDzbh21WW96YuW9RY0GbcKoyh1UEWiU7is4YJUpuYAuBrj9fBzSgzX04w==", "signatures": [{"sig": "MEUCIQD0V4b7TxqJXaeJq3QT6erXMOs7p7f10H1iiUSnM1MsuwIgdgdfKlMMlQaNefGJnyOyJ/8hkVPnExbYLfR0pjAhI6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaeFgCRA9TVsSAnZWagAAsYAP/1LY7CFXsJG0R3ZlKMa6\nFlKjjGVaZ7LMXsdUxnz2rwVfEVnvlPcWhZAkESDFaZ6i40ihT++VwIn33TH9\nUyDPQ1OhkQhYmr7FY5RwJJe+QuYRL4OlNSNBABGfsJ1h3P8oAyvkhd86p7bM\nVjiJCo06rALLkYkAnlqDvB1RD4mThZJ/ulFU5s0PbxrXyEebNYyo9ycj3wtd\nN2kHue0jcW44uYirM6xG/vYywVJ0ZNqNgdcmxxooDcagmLKkcnfdBOFIGVUu\ngCSxdbbSjw5WFPkoefYb1zwk+WvS6xpCecSQEYCb7S1lMtLPeOKXcMMW5aDd\nlUM482KU3P/cYWKN4N5e1tQyF1f6xSWwFPBpUuzjW0dFk4/O2oLvv5qBgLF0\nbvUPc0e3l2WdOx6p4jPO8GCxAsys75+18gZs6kdjD+5Rbc/bt8YmRS2nCBA5\nCpdqLHu53x6vVjHX7vBGQN7mDTeW41orodRYEUezdRgChYB6r30BQ9yHIBLk\ngXAv38IWTGWl2urO7RmN+61FTFWbSl8pwg2AiSZB3Uikcf4uM9E5dLLdDQsj\nDRqz5RAXy5HlhfMqCYpKwn7X6oW4ceVCI7+aRq/a1iOU7ekHLcO//khKKbGD\nTWTCbCN+wg8FdELyCvgiePd12Xeo38sc//oNeVoMPYt8NPFbDmSlN6ib52BP\n9HLF\r\n=eHIc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "596ada393abfe4f7cb27791ebda7d55af94d23c1", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_2.12.2_1533665632490_0.8229561482297196", "host": "s3://npm-registry-packages"}}, "2.13.0": {"name": "express-rate-limit", "version": "2.13.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.13.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "83a00a7ca8ebfec58a036ebe4ddfd4bef9e88dbf", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.13.0.tgz", "fileCount": 4, "integrity": "sha512-4ojX8aDviGuUFliFiA5GTayOuYOYYpdwSzve0WKHM8WZGW7Tuw4z7g8nfzVWSZJL2stby2ZtFLuZB6SOxKoRYQ==", "signatures": [{"sig": "MEUCIQDcC2CPraNKdZ9GiQvgIW3nvWvM9WybmZfm1HZGBtrnGgIgENm6lUqKupNQYuhECQVrg0DTvD/VUPWPmWEmmNEZif0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbe1YZCRA9TVsSAnZWagAA9AwQAIitPasKZFOVQpvbMQ0t\nhhLo8+opgYEQyim4rQLRHRVErMITdrmbUPzGgWkw7/XhbdG/8o8sDGz/iXMd\n5WVjsb4SgAoci+FWprzI4YCoccNqT25O7dfoj6vlJ8WvDZEIFkIDpkq69lBn\nlPeZBsRdEuhhUKkZ6X/fuN+zQC2m32yNtd0AFDBajwtK0kvpNjrYZi4Kuq56\njPQV50QuLnSUuLw1YzGYEF6XFh9kBfg/M/g+mAkyUIuznl74OrUccxxpO/Hl\nJaO/jUKcQjIN6sec0iwnSdYX7nwHNdxacLeD0xuHNSLVH3/Azfq8GnT0v4gC\nPDPrzRpHlE0+YBYTsNNWpfWDyZ8E4J/ubARKAJ3qd1gbPm+Q2SIsgVpEU+Zp\nctlcEACbtLkSEBNcsGjYEENbzowWhezccY632caUaZQa6P3DMCl/i1S3ei/D\noAALxZpPZ6/wkCVqSFL7YJTewAngrQySvdAO3XMI4cD2C4om8uBLbATsyELi\nE8/fdrCjZc0ZlRhqrfkyVUo1NT0+RNZOmpEMv/GJuusiXyUnqGAP4O3yH0KY\n/jwyz0bWSjnu5oK+ZpqCYQJr4YSO0BPmQSGDhYMaoqpuOsIqvPdA/qI1tks+\nsoGBPumMbkubInzRVsYXcCsZAaui5Mqdh+mPy4BMM/0mXRutK4Hnw0fJIjVA\niSLb\r\n=UDMA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "2842e5e3e26893e517b1770e8ae3c3ec9727e259", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_2.13.0_1534809624875_0.59360555009325", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "express-rate-limit", "version": "3.0.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.0.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "86e02a5ab8fc33f73d8ee84d10393c98625a735b", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-5OPgKcUzy0aa2dEi7fUN79Nf9o21C6x1aQrstfaVlocnCRhSBAByBKNdbdgM5HGH35WR7stIccZFKO0rIPXUhg==", "signatures": [{"sig": "MEUCIQC7fgflGHfUn7T2xStQJbMFkIYIBlX3s/ao166d3wGnpAIgARdAvPpPmXDzabepUKcifkwZCeofQWK0qGRwQBzRzis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbe1fyCRA9TVsSAnZWagAAQVYP/isYxn+AQwyB2FDtMxbR\nL77fqB2ElQHBCkpl7O+36brOYmV015E/2IYBRUvptzZK7Don5rKgOlroLoq4\n+5Dzeb+lx6uwSWng5orEwUUavne6fzPkWeBfGvIFkIFcl9YeyEyR/P1ORNpF\nGpbjo+wyY6BI9JXqkyozr3bzrNdYif9/CjDEGdG5E09NRYigiDS4XBlgCKub\nIZ5dTvY29cSU8t/1pUjjt7ke9jvZTw3QGV97ms3t3R/F8SDAhUWtwG0muqo8\n926lh72WTQXBXN3ScczGTS34aI3GpdKvECY0/m/zZPrEfLbJlyqc3dl673uP\n2rowKKAYIorkV9GB7ZxdU7qdQ4kfIxQbhBY9bOZZiK8Jt/k+tVAshCk1qREU\nlZlP1QU9PtlWi4KJt6anLqpuUI9LnqSVUj/zynQPUi1rUsphbWqpK8MBTBGX\n06WPfpXV9apfAnJx5hYVDVT8W/T8zSR6X2LvqEUxep/WfaqugCPl4wQpwp0b\npXp+paOSZm4nIEsVOGVo5Vi08aWDI2bBn+6d0ilEH/0jeP8XA8Cp+uYDn05J\nGQjEP0Rel+sS/UbBLfoGjvBippGmO1oXBgk9E0Gh5GD6TnOYmfA8PCvjB/4Z\nzkc9qxQRGr2wEqxppjM/H0SXMemBgIT+5oVjf9ho3v2BAFmdGEzM2nqq4uvy\nQew4\r\n=HeQF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "f49fbe289058b546f5c68535146dd9f772bc7499", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.0.0_1534810097527_0.16486344267543873", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "express-rate-limit", "version": "3.0.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.0.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "248a00c1c97589307cc21a7803be7f9f895766e8", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-C3AkRknxm/xCukbkEKNNgoTV2ZM4QrrdAIKE9SgI26UHKquc9B33qwVmTjTuMU4v0rOBgBPAs4K9stBy96gVQw==", "signatures": [{"sig": "MEUCIQDWizGpifq3nI7y1FGAxBc+PgeUMUfwYwy0NvRi6KpmbAIgTPYN56YNiRjG5BKUqMwb2M6F8sofpqq1eKOATQ0USTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbe2haCRA9TVsSAnZWagAAg80P/3e7c+3vyQxhEw/4rB+c\nWzSJhP5saQnbYieqc+uzZttBduaKbTcdxbS+VHMw74iBhNpMK0AgO3t8yK48\nECCBQHAJjFr3RAzziB+vd9lQd0L7r6tWnz60k0MlYmbIkaItP2Z7a0r0WT60\nX2/TijArxHenxdk1P2e6Fl/sSH8njWHpxI6AOaZSXQx1CI8qVvBo2ydujneN\nTx7feVMwaz+mU7QP31ukWObVCC1jRI63Pa+z8pnYs48BXuQ6U/2uaRoG3X6+\nZJxVsbTyyPmIglNvgOZ23aCZoOo9bnajUm2yL6/p7XFOGioMgiB/jdbsx4Zj\nIv6bImgwT8DNAXsFEpu47GURmI3Z1859IB6lpoSlo+ydus6jUvjp0RpSAr1R\nq08H/WKAy2U8ccg/mU0F3quZFBwb+S6csIt1LOMXZQTynOcogeVVn5MAN4nF\nGFi9X1QiKUiPbdt6h759RUORuOcUSuKpm2sRR5B6YSfdPSFafKyWETVTQf9w\nkZz9d8xjOqBISahsE8jGrCApHlhXGUtIIg/XOmb5Y2OT46qdOpKOoQBv1Npo\nqGnQ5LsBFaMH5j76Wwy4+f+9reOeCA5ngnTuRcPKlD+UO0+Icyk6TI3Y+uL7\n92JJAF0G+g0X9Tc256fblphaeebaAdVE9kvP5Wa/Tn6KXmlGz33vw4yxomkx\nPgcj\r\n=p+0I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "e9b94635573e8d61a4f0ced08d7568c4fbd99583", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.0.1_1534814297343_0.9218806933591384", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "express-rate-limit", "version": "3.0.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.0.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "f8705b21a08f8cdc084ec4ca25f8e77b424e4060", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.0.2.tgz", "fileCount": 4, "integrity": "sha512-t80Rb3cuhyA+RHcuKMS0l/fDSlnez8LeL2zAo5J1uKHkzX+JWyV0khlCGkS1z1k6X0xlqG9yufdcyZ/zkk3Z3Q==", "signatures": [{"sig": "MEQCIEMfbb8EtAmBZ5w/uKey1GOcG87xgKRyqhSArH3IoTqPAiAOOetlISB0x1QsN7uqlHwtVnLDL382tbRBRz/XPbRrow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfBWCCRA9TVsSAnZWagAAyVkP/3LjMJlxCVIYFiuP6hlR\nVcATbupQGNK9mTuBuKZ1nZ29yzwNI59+xjUnmEkmzFOBRbUW5qs8mRsqU8x1\n5O5jfz0uT/bCPoPjtUk/fZEWx9wrvRyvKbjsJcDJZwr4PWTsdYfdSzjYpee0\nidbWFAQRewA9P+jjAOWmF0d8/9YNHiuyajc8u8nMjukr+tPU1xaZq19ZAQBC\nCq7CVDI4ZdMfS3U7kxVbhuMggvyUhGKiXuHnJKQhnDFbnFQbC619+rwwzU8H\nLekGj2couwU0uls7YCdwebDwZmCeTTSDqEmhTY95Vna/fg7XSir9OOlcjb1j\nCk+1TqIknfD0LO5qGB1+FYpk6l4qvlI+Vye3lnV1BedFrEm1P3k8A+Y/IEgQ\nb3GTohP/kMLU8QdOhfuhzbr/Ae+Nnbc6vpZzJS+vAphFBs8PvpQ9FUuBXcJp\nXvEbDLyuV47yTuZ6QAOqlXfwghg5totOuG+Is3H1XM/quEunR03YeU2mb5+V\nttI4bnbbm4a12XipVSS3xXDhkAomPH1pz2KD6ozTMN1f68tTFW1wIMiGZoe1\nkooMMHa9SKg4TFeTbj9jMmXpkXhv2l2lO0o73CclUbVWDty5d3UxsoeCqhjC\nMh6w2EjJReXoXsjC0s/4KcJ9xrmAYoBbLw7fcuA9FMxDs09F4cHqbcfv3Sif\niYyi\r\n=tyyi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "f8705b21a08f8cdc084ec4ca25f8e77b424e4060", "gitHead": "3b5fe600b3f88add876add420e6898b83dda0f3f", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "6.14.4", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.0.2_1534858625560_0.14036382492255184", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "express-rate-limit", "version": "3.0.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.0.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "2d695c0e4dea4d85f32b1fcb92a43c37bba8c1a8", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.0.3.tgz", "fileCount": 4, "integrity": "sha512-eC/Ae6sn1DmLOADlDHGAyRMHE5IX7XH/w9S90Gnf+OpilyU2XOWR5ZMq9oqG1X1sJ9rAjD1G8QsP7POMmZkNVg==", "signatures": [{"sig": "MEYCIQC5K+Riez+DDaYhMMZ67UbgUvGrxrmnUbOS4HFVr2ngfwIhAM9a53mAcsOxqIXNuafLSTPDOIrN51XS1nsRYL+ZDT9r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfaDYCRA9TVsSAnZWagAAN3gP/jYFuNlA98B9ULcCHVb3\nzG3DI/1a4Ctr3hmFP6gtZNnYiB1qolsNSxuKiOKppzVbSJy0sXaDwmN7o6FB\n2Yp87kDS0a7TDPR+4KZR777piT50YMQ/pqxoqKdZG3ITPqK9nDyKDj5y6EVe\nymqaS9TLJ9y0xvasu634fIiKnI9d2r8BjzhMb28oXW8oFLJaOrw579/oz63u\nT1nmue6KvG3FTh6C4eQdoPkARnsGteJeHL6Dg1Iop2SRkOjKd7PBMka5Rc5h\nYDYx+tz68IXTMvwCmGupjTyfaNz5cJ/uP3VsO2VNT0ai/XmPcUvVakRbyw6X\n4xMvF+s/U8e57cSmyQuUjMo0rqnM6f4EGDJfsb/lnZPrBRVkYG0ohs47umSM\n3I9bMiTiTpeGnhGjSKgXVxs/v2yYU0mkKp2uGn3ocVxVC+DIldVJdAPvsKXN\n2adNZQKTg8CEUWozkZ3HWt4ssfiPyYkRhWko0ql6Tm9gflhYXl8QrlakTNLp\nWsoncQ/CklC2BqDVy3ABF0g+8T+Bf/z3iP7u5iDQwXhVeYaCUJqYWy7CU7Uf\nnNeUTp65kkS+07LCF9NgNRnIvxsZOA5xl/su/0kr//0N2DNfArGceZWWaXw2\npofuoPH40NpWQH2B2t8Vy3ivEvXEDckefEgMstOPZOTQtJFzEc+T0cxilNYt\nsoSm\r\n=2rfC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "b2d02e35bcc679b0e5e156d60769a9ec46e5d7b8", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.0.3_1534959832347_0.6883365705161213", "host": "s3://npm-registry-packages"}}, "2.13.1": {"name": "express-rate-limit", "version": "2.13.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.13.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "6c897ff201caf4bfeb50389a324fdf468e122e8b", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.13.1.tgz", "fileCount": 4, "integrity": "sha512-GRDmlNpwPH6nevZc3UliF3sdlQTTyAfKG4KIaXOH2yz9Ud8QoSXMa0uaPxvy08oBNT9aeUFFm7ogcfSdp0kUyw==", "signatures": [{"sig": "MEUCIA2SUmy1g7fdicecdQfjvjjp9WroE3nRCjWt4zWHFlZUAiEA9wwSgQzQ0VC0asfwnHZxxVIcxyfAaFqF1mMoOiDtr6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfaE4CRA9TVsSAnZWagAAnFgP/RhRwBcL/rfK6H3FfDRm\nziKmorE/XwrrKOiTlCm25JVC3c5pM+HEtzZcWuoId6nR99kjj4XOGNBN2g4O\nsAecKgJ4OFUrSPayLiZnnelGKB8T4MU5kveKcjYuTCS8fyS+IanaelE8MMkf\n6dozzPbc/u9IpClJWSw5UYYzvyAl0nBRhk61mufCLCU+uxdtaE/JWkx2QCiC\nmQVnF/tVOlLI/JeBVS/cV6GKe/GaBBNm6qQRaWyg3vT3ERqHSb6GVEh0jA3x\n+Td386j7ECKTSQ82PMHBhBAdmxS+Ze8jQBc65M1hB33b7YnYhiPzyWADyHZI\n72rzXJ5S0q7ixPr1lHKKGCuqID+Kyxbf0jWS+/XhrsdFL1PTFHVEBfSO3yio\nklIDJjbJOltcXam0Ck7LF1cm9QLIf8j+Stku/H689Qfko1y0RW4SROWRRgAf\noEiWZt/Q0chNIzi+7fKfWQYALPn1NZ3iPijxmw3iwIFcdlxCc0QwcwHuE49H\nCeDiFce70f9Nesc/yBZ2oyQ3Kr4jwNxpq8ydBI6KKzTSZVOo3nEcRpLZoATe\ngC6cy8MybdDoZOvjM/2X2FprbA08IbO2M5KGvdZ8mSO16ezn1XXmM0ROiY1e\n9uoG13P0vYuwpMKkSlbMW7aFPSSLDbKAunhNyLgxe/kxEPWry82FyLsh44z/\nRtoz\r\n=LSQh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "_from": ".", "files": ["lib/"], "_shasum": "6c897ff201caf4bfeb50389a324fdf468e122e8b", "gitHead": "419b651230a93b761c7a40f067e6ad54d43e6f64", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "6.14.4", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_2.13.1_1534959928162_0.10679855541403271", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "express-rate-limit", "version": "3.1.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.1.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "d00ace41fd0ae1173ed3fc76812bf59035aeda2e", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.1.0.tgz", "fileCount": 4, "integrity": "sha512-LOEEqDKAldk0oACcn00M3ttqzX29PFyD63QKqr8cvP5Zlf1Ua5cr8qw7zfLYVWtfC9FVSzZ/47LybyfZOJoV2Q==", "signatures": [{"sig": "MEQCIG+kGY9+YFAnpVkkGXepMS6XuxAOUm7PCVDpnx8pgJdtAiAsg0rqyvwyAnu2mnM4MbMCmZIuWO/K0fbSO0ef4pR+Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbftWLCRA9TVsSAnZWagAAryQQAIsZOFmX1uW1GQmNGjI5\nvYTBdJLr3ZriJ0LLXlmk72JVdKaRRE+HP9vuwnQQ2jrmRDpr3bWuPPmUtlIi\noEAygRvgfxcEoHelOnguB7BfZGdEfd5XOXykOXeaVCDiy+M3ZXQW1YvN46dd\nMv0p9gaJt/TMC3ly1fDZyCGRk8hnCoPulysvdqZggkc4HM0rbNw2NPvW7YFX\nlLXM4orp0wL3B1NeESOIiSAGCANbQA7ihr3aoQzCaPIyf2kMXpOavKiwRH6G\no17oWYNb36EBiatZQW8ySCNufdcWyeAuPvRcS9zI5kmCWyfMuV+0jXU8c1TS\n5Cv4ymGd2r+CTXcH5woxvg3t9kquaBu3zdnhpR/sghx/Ts17uNpyQzkRf0QO\nIjG01d1VFEydtX/lxxFsRMzzEoLjLZIyD1dZHoZkzeXKXkevkhsCw2tO/QdC\nkVGhDtRvSpj+6QnqxBjLCZYeQYFn3reJXhazy1FAqpHAAS/XUAo50+tM8j4Y\n2Vug9pQ1nvdEZIcNFD/kCvD+kQSY002sPhRHO7Jgq6MeDMjgE2dHXr0xzMLI\nJ9kTKBLfoZZd9pYDhG9Smnq9S0MVEVymJ5Ib6mnGrtRo2PDoQKeuYoBvyGHe\n9ZdvTvTlM77gHhgikY3ipTuZFqbq0ocu/CGFAcFMZcFrnC3rHGF315mvVqRj\nKQuH\r\n=Dzqb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "548acbee07ac9fc28cf39c85122e9e37f850e060", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.1.0_1535038858361_0.0013616151364079787", "host": "s3://npm-registry-packages"}}, "2.14.2": {"name": "express-rate-limit", "version": "2.14.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@2.14.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "d1effcdddc7cd36ba91ef71da3cc737d037a6ec2", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-2.14.2.tgz", "fileCount": 4, "integrity": "sha512-aVYzfYU2Bv+v6ry/fBpTrX8MorM0p/TeDnx4CqiLevg9ftG8eW+pyuZ6JEreGdG2t1vXyTGHuSNRKLeYixuqZg==", "signatures": [{"sig": "MEUCIF/DBAiLUS/xjDAsO13yo1m3HEdAZuo3L3IRQpCRTRQiAiEA/jb9Fws9+zcVGhbWOpOcElz3MT/zGh2v9+kqVJIScqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbftheCRA9TVsSAnZWagAAV3IP/22xjNOKmLx/NHCFIPns\nXvAm6JAtJV1QGmhlMGChR3eWS0whOBBs2r+F/f+jX7cSxRPVMnQrvW/pGGHI\ncR6SYuD15cMjJTHLHLzssxBBJHkh2qXGvnWmTtNkdMBWQ/H8wByxf/V2bhUX\nzyXvsrJMGco8z8+UokdQIfl9WKNZAXzMx3ICb0n7zud6n2t7VxqHtCGUcnqU\nkEXjYIegG2bKYbImjtAWJ88Ti4KuQFiW+eS8V2/zQI/gh3fDRCWfT4dZisxx\ncgpXjQXlumRkzLWBQHYCxV/uCK+mXv+a+zLIM3CkRwybSh9dODb0LYWqXU33\nkF0+UO1tp+Iotakg4eVdqe4900upHrakxmewq9M+2YsFonUcTDmjEptKpOch\n/OCHwZTzZq3rgT49iiy8OBxjNk8wvCeIrnPLFAtj1M+wF1mizTE+tmer+1ax\nTr5mFaP3Tzv5oOOCmDfPNiQAOM/uuHFe4LVteIi9zBaaiGFFX7W2Icmi7UfB\nZf5QIssZifdNlvQBNUzIwhr+7H8OaGCHXQ+nhMlJ5REr1jeAEliyA9MPANUi\nMC6kT9YzLUw2RMx5+ageYzzOuKn7eBri2A1cJDMgf8BXckHbffw6/ohFUWV0\nLe6yCMGOfzfo1rM9WZ1OONotX+sNS6eYdMw0IGeYAwF8d55G/6FJxdukdKpD\nwq2s\r\n=+3e1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "files": ["lib/"], "gitHead": "e1cce0b8d4eaafd0dde566b95b3b60a6f0370c84", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_2.14.2_1535039581163_0.19848970979912872", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "express-rate-limit", "version": "3.1.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.1.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "a1e431df7f12f8cc39d691b72bcac09a3771ff7c", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.1.1.tgz", "fileCount": 4, "integrity": "sha512-txZEOYvyhqsAjsb6QxpRB9qWkDUgxFKMy1lTzl6NOGMk/eQMuvJ0kX94kU41epjTLVmPDGfd9zYtxkzW5BiEZQ==", "signatures": [{"sig": "MEQCIBPEUb5XwnzNB/4WOU8WScpWHSd2TaG/Sn7QDStKKUPTAiAL0RCDYvplBbsuM1j2CmTyhA/X8SlvcrWJtwfi2sGgjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkY8wCRA9TVsSAnZWagAAy1wP/2KRlmPzB3VQS0dMThna\nM9sX0GOxsjwpEFerB9A1wtxPAdcpnC5Rd4Ps/CGxV5No1PWdpnZYhM0z/R9D\n8CSzTBHTHSUJ+D7LfcIlfFW6YMiKVFwTIcglMz471UR5so78QEP2hCwGCXuq\nkfQlNDJYzUAsgFT6KEmxk/8qjlaxrwkEkP/CTuAzKl+6PJIOjFHcf1jsIFmK\nKxBU76Sx7jQL4QDjpPVqR3/ydNXcwRRB8mbUEtMUyUmJb7LWRQmI16xPZXN8\n9JevuboRIT27X0+QwviGOLLmlu4EX6xdeKo8xPZieCJlou8q4yE3r1dlwLrX\nahD36crRtXGyF87gAUnzwIsWyxVHrGfvoY7yUgoojk/4zd51ULky84m7IDPR\nl5E1qE0afwJg5DvA2Wzto18ujDyR4QyJ3WtsvPNZARaCFS2G/U44hnTf2gdr\npnXMM3U+X5+j1K9BIKXNth0aP/4lm8FY/LB6UeECsZm2wALCHg25JENFmpuw\nmfizl9755vPUwq9mFCWG26yOv4D3jdBqoGvVj9v7WD970A8skAZQSeIZrk+9\nTIS7mdzz4DBtWB3Hx73pwpkxmcUNRv9fLmmrPbBzI4XcmrMU6moF3UyX12JC\ner/79cCmfWQ6yz5wHJjorNaNzrL/vEAOkR/dNmDgDmMWFXmqnhKChZLEttYh\nWPdH\r\n=1+AT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "9fd746cceb21b00e593abe2f0941510684ad69ff", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.1.1_1536266031277_0.22080281191971252", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "express-rate-limit", "version": "3.2.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.2.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "92368aab15a6b17c68399d4d4b4289850f13c4fe", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.2.0.tgz", "fileCount": 4, "integrity": "sha512-oJpdtmt+mJivUCS9TVnlDAh/otWno4AaKz2cZkhbfpBna4CXB/pQjyUfWv2G7/09T3HqOIvB/93kU+eSmbeeTw==", "signatures": [{"sig": "MEUCIQCAYo1gQCxNxDTYwrXSbAwsL7f17sLQyUWQlFeKjNAGMgIgeMvoBJ/0ZFNeFS6hj9X/wEY8Ev7sbJUYqVbJBP85Y9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboHHyCRA9TVsSAnZWagAAYv4P/2Gx3QC7pOjmvgqb7AI6\nOaYpxyS3gKPIl+sgodnT31YtPJaSQIAk6Ljg7r3thz1BaZejb9sNP6JU5VEo\nkXhSkXNnEtJFnBMD83+c06M/6HfHrTz4ifZL13F6WgRChTrmByFLtQNIMO9G\nGhtPLHoBTZ8yVExElDisSk2jLUfV/Ovt695WNWtQ9oSSxW/+AaJZIRRvQMLF\nYDJfRrW7nGPMQEDALryAoJcbhER63GkcUxeq6QQ30nq0nHX5TGHJF2Fswwn8\nm9bbXh2E7C8bqCQ254ExafqGoLY/XNhDKRDl2occJD40X+LEC8GcXoQy68v9\n0q+t+qE8sLqIS8RUu8x2GlxGVzDHSiHpx+iiwSI4C037iuuArEKC+CZRxV1m\nzHWQC08vNocfg74ogtXKJIgiFiy5Sw0cM7ylVQoslFz5Oh3iv5w5yh1UJ3h3\nv3GjbuUYf6KYZc369yZk4Y0MA8atWMKBxiJHrRs+TcTsRzrOXqFqN5Yk4kOU\ncTnvAgFbWX1ea2knwnjkZcvn1CSZrLfWxlHbTqX61mvloY2VsMBsN6OIYwjN\nzKkZ6NwE63orHlL6ZEUCDivGLyULBFj1qSu259UaQL5IYgX7yiUFhCYag4yw\ndb1bWzhKBOF1ukSW80TSuCAwZ+AnkqqTL93GY+N92YUAd9X7854Bj0KnNHxP\n1juj\r\n=pkn8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "a1fbf54e82c0fc55aa65b123b6ebac46e04585db", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.2.0_1537241586021_0.2171069424564167", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "express-rate-limit", "version": "3.2.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.2.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "939e64d7a2173c72c1c3229896e6911791de5972", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.2.1.tgz", "fileCount": 4, "integrity": "sha512-tbt1jLmMnhODORAeb6JOZzXQNdjo+ewpRfI/DhCWeF3hnFC2F2eSco6ZONgDKy97+wUFjOL40Y9c9LGgk5/IyQ==", "signatures": [{"sig": "MEUCIQC+HOz8BhL1l3pAvCHPnZtPdeqhJqg7RfNvXGqBns9DEgIgOOPhbEITjFUVsk6Jc2fIPOucEUEYKAfuqQvuAXUl2XA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvAokCRA9TVsSAnZWagAAeF0QAIeXsgR4iL/3wJowLrhH\nBFX6GnHKvdaXnPmTCv6q+ofl9fvRWme7gko8pTYt5q3F1BWk7UP8M9NcgSO6\nFDiNxcGHViN5fDB0iHImK20/OdlfFITajsM6T3slBN0khT5SSbpALCqpaJml\nqAVewCMwJSyshrMgfLJudkVYoKS9hP3OX/JVo8EJapVP/72JX2oVjubIwSMO\nFFw8iQjxoc/bNFadAfADhpSPwCCXOK59hsDUI8myprGFviD0MCFr7qBTF9V6\nLaO8QXXvp/VFmHJPdh40w0p6EI4fMNmc3VuvptYebll/Y0Sm1G4t3WQt7snF\npBOnt+4Mk36D6x3TIw0bZ7umuFAD/9B5DXN3iBp3N0kulheOyrOI7j2dITPx\nPnJ4piGzGDyOh8VPd+8aV/yUP5YoaU2DE73q1d+i0FZWkqwSq9HPw2SKGzGP\nPs3nyi3oGMaxhrPGg5IQ8wEon6p45KboHRRKGhOMfxf8uSvikLpw+0f/CaN0\n2nynurPirnxwg7+ztGPZhXe/51ahZysjPdP8c/+IvQCivnLfRAVKbi6X/hlk\nj1JCNygZfj4Zy5UuNt2sXTUEtpdYqGcWKB2UeeRcKypKipMKjcIL7i7dAJ//\nMP/w7iOpYd2F5S9MXCyw9DU2zCKVb/2Ut3lMoYr3gu7xpBnHo7Vi7U9H65uk\nRjkm\r\n=taYx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "2ab4a6cb394f10af3510a838146ed26d7e913b9a", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.2.1_1539050019895_0.49721581767446743", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "express-rate-limit", "version": "3.3.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.3.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "e7f3ee6564d4f8f5a2eb918fb71a79bee9afaf2a", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.3.0.tgz", "fileCount": 4, "integrity": "sha512-vDZuNdwcB0RvcoumxfQKfcVE4g1qbxD90Jv0B11skDqFbq9r3T/8KodIQyxNRp2HppuNz8R89Bu8DgTjhB85pg==", "signatures": [{"sig": "MEUCIQCgX4kseMtDZ+2ztbVA6VjeWsgS4w/rc2uV1f4iBvp4wAIgTZnTvVKE2eyeNtYyum/dn55P3zvAK/izv3wx/9Nvhho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2FCiCRA9TVsSAnZWagAA5doP/ipaiHFROR/s5RYa8MRn\nI8itHnwlo295ckSFFnXPSDJNyQh2E8t7DwTLPQAn/1BjbL3Yy6blv/pT7IIv\n6swTnYATpvXA3ffdlHfQTMaHVOAwwr4FNozXhMuM5oWERwVQxXtQaZ3UYnN9\nD+I33kA4+pLlNkOIwMyDO8Ea3bVIOa0irlTU36pJDVCD2dTmveUBIxnGHDW/\nIVwlprLfsEZD346wzkL6u3lgFNXkE/bv1qgIVUh9nu2h0MexOXG4UaBzJgeH\nL5FDgdZZto0/ecwlQICYyT0jLa/4wVjuLANAErcFaUkha/lsWSpyky7Y2Aly\n2Ik/0yXLd3gPNmanP8ak1T2qNMNbpMyzeK04xyIuue90RuNNIxYFlbutRXcr\nR/EyJNGvEJ6R6qSLYwOiZBBP6tZQYo6HoABmSQrHgBPIaTWh+10NkB9hPKyg\n/PJBm7UAGgsT9pc/GWXte7r8vwPdzOct8NilpQjZ78rHlyWN1ty8s0fx86bP\nVD5BA+XDo+pJMkzlLzhgC6FVFM/oVFAxvLv18IdJ3N3iSA1FJHkRDy+S4C1Q\n6dqjbZLOo6nfaKkZuYCfbLagGwChZLedWKU1C/YJhh8oLkAajaOigbyMzKr+\nP3ZDLeYuFMB2wny8vDzAcLG46L5UyTZX5TH2+MemjkayyI/VlIRsZ2UcX9Sv\nNuO8\r\n=W1Sa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "794eb73a6e10910c8c0cbae8b91e7625f35c9674", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "11.0.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.3.0_1540903073355_0.8479028750897057", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "express-rate-limit", "version": "3.3.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.3.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "e334e53b82bd3516b3cfae800dcdb8ddb7d64ae1", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.3.1.tgz", "fileCount": 4, "integrity": "sha512-nah1w25D+JEaswqZ3KSS+AREm6ecuNeOazfXxDmJqzRSyoVPsDs8SGQMYIYjkSpPtx/pCLHgBCAs9N+O2qZj3w==", "signatures": [{"sig": "MEQCIAEQlUolpShCzIZQa0ZO1G7p3SIeEytpoaArIxV0ez2TAiAKai698Cmtp+7iNEfkCIZPWxe5wcBXjDSF2jUidnaN+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5E8KCRA9TVsSAnZWagAAH28QAI7PlxMB544yJC/NOen+\nYgXXsH0qYW/hcn3N0ZvLRo79/rPbXhaSrKv79/vRxCeO7Kpf8BhKdWx5eYlT\nzFQARCUMTQRGHkp0VlJ4/mBlCUyfV07WwO39bApdFKkBC1aFQY4qpNTOGjTd\n1Ck/f4OGYmBIYqwDjEQOo/Nl3ABhxMpEAJZ+kXYzcQPLSsDRProAflmYK46r\niB7qZMab5SGFFunj1sbc2t3BheucRDWaNDAsij1nLh31jyfhZWlJkr6f1ZRR\n/uYMPMSe95Fk8wHNJog5blT7poIYOj5jWVW0pvlebwu+xXbKYm7YA5UkaJ73\noq00oM2G82XGsDsmI1q7n0Lc6e3qUEMXw+nhuqX4V9KvO9E83pP9Kt1qymM9\n9UKWVhM5x9oYXYftaoPqTDFTuNXk5SKw+Nuc1FsD8mzuTfl3WfVGnp40KwmO\nXkNCNtCxlay/vFp9qA8xrk1usPIIYaAPs6loiVQnaDe5U0NPRR5MiHGKnyeV\nBJLNb8RH+pySvS63Z7LW2tFKBsbfhqYagEbQ6M9b+oNqh1yRViqZnmIx5/TF\nh9TQC3zBIEdpacOdWEU9T9JIWylUyfVN3hb8E6CGgdwqWuM4cjv9vgv8KNg+\ngaElBrVLu0sVRaG2ky+WHNkROTrtWDZ+AjG3F87JGFOBLmHT89XJlpfxqzSw\np6ZF\r\n=0XCX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "f763e8080812cf54b0869a072879e2840b18dbdb", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "11.1.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.3.1_1541689097456_0.1192907455963843", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "express-rate-limit", "version": "3.3.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.3.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "c5b2fc770d533878ce01a5dbbfadca340f3b8915", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.3.2.tgz", "fileCount": 4, "integrity": "sha512-JZnnTf6ZX9ntQalCZiPHsOG9zhxyRGqfaur+WD4yIcdqzf5FJQao5dmxXbWHk093K8WRSYwNwnzkFXVYnBNudg==", "signatures": [{"sig": "MEUCIGy2b4PlPjCfoa/4wVIVRU9XLNCRE7u7cd/MkVNlSckGAiEAk82LycNIf7D6hhzOXZa+CWSKQJ/SJRYFnVN12yFFIKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6cNyCRA9TVsSAnZWagAA3wsP/3hDZKZ6QMG9eprFZba5\nvDu85TsmtCenaIWItfrRhAWFnCovMgU73GU6rb6/WGtp/f7G74IzXRli8UDc\nHwsx2Ln9xWyhxze8I/bvkYQ4cQ1frPe8PGQhVzU+kI4CoaWqgdic/H/rGqgg\ndLiD6celoXnCqs8iJlP2TVlfRDByBEg0lEZK1YGpZsGsgRPS8b2hwXBKkyOP\n1EnYMUQylMJuNvkqAM/EbbFfhiuEbnjTLOHZ3RLemu5IC22FkxFKJ90UVfB/\nrSyniy03xdQwTpJc0rngvljDdkA0Z1tUbWX2KOJYzro2rV+d915Wqw5WHUWe\nMIBLnMg3IAPFgQ/VfNE0RE9k4tOqnzYy+4vcfPh74V5NBzvBaIyV1wv4Rj5t\n7SNvzq6cEdCrDHndvFsQ/O9vZDqYlfXw74Ss37qpmD+pp70guaZDCTUchy2U\ng/crjp2PJK04v+GukkrQib+20O1JPfH9SaVOxYiudyIPNL+e0k8jqpEf3kwv\nz++dYr/sgv8ff5L2XB8+AM9U9HJju8Uk2XwBh8FGIo7RjPLl1uv/vH3cU4km\nYqDA0uscqo+iBkF98ZmxOtf0LYvWTQA4Ututgk31kkoCosbJ5GB4BX+hxneB\nz9W2qz9pwuRPfa9D/cu4yG1smV9YTF3OeyvHQYkXX2aeEdwkhBaexln1WGxN\nAWm3\r\n=1cEW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "2f0d649d8dd3aae17b3efcb3aaa32cd91d301519", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "11.1.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.3.2_1542046577955_0.37493080808801493", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "express-rate-limit", "version": "3.4.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.4.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "c053ee294feac6d3529863de549438749fd83ef1", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.4.0.tgz", "fileCount": 4, "integrity": "sha512-SktWQGHhTQfIOZykiVIaoqmHCptqq177fEbumVytWsMpEqe+g78IFrfzivJTimoCdMZ5+vYJ5/a/w1darXMv+A==", "signatures": [{"sig": "MEUCIQD+UFWx/bOfjyrtS5RhlHkt4Gj1wFYRFX+iwhkf28wqegIgI2yEKYgXrJtG7n7/Q5uxdcwrk+ce8xhlPsvedoTghpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbFfJCRA9TVsSAnZWagAA5ncQAIw5tfSEx40EUaagajTm\nYCC0ysrPN/EAygI+WByf8Rxd+4Io6igY3TJ7EzqhWzFQ7K87jUjOVJx7efSR\nwpFFRygdnAA4yAnEYbtSzfp7nzzQlCXm+syJYtjBb+BVHMp4yO6+nBh4uvfR\nKflIc05m2+Z2YUIW2ql6MxjC4UyQiFCHJNck7zDijM08Zxx3PfcQV5IhH3oJ\n/NiwNqJbPZAP17gyh03Kn4WvRRpKE7HLIqi4TT7wkv4J3CwJSiL5JQqpG2gY\n8uylRnA/xgTEyXkjRvm/6R7V2c2lcQJFqWmEdaV7yCyOzLhzgt6nE9lvpPwJ\nws/RkNM5X/HlUlav8NgJV5HvT6kdfywCz6Fc49J4hO4rZcE/XK8twrTEYCCf\nA1R//aN8oSoUrqT85i0aYiLUYMXJRRgqUdHrZZSnb2Umh34cQ4/0a5awI0aF\nduzlgcfTcsPe7pR3MM1KIarwDICKWSh4hrSYLEW9RbJeeensY6Ssy9RCKPq9\nzHscUVghzpbCw99Ylc8wBWnI+9zEV+yW3nhbk+pnCOo7O7ghSO66niypk7uS\n5QPp0bR7iBgRN3A9IA6MJngSjp2M47WMUQbXQiFHNeU32G/Nmtvt8pN0b1IQ\n1r4XTvjyN1cup0wIwuYkbRzJTBC7899kmKQZERMEyqD5EalG2L+1dg/CLjnF\nJioA\r\n=bKdC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "abd3548bdc85b2f92a5d3c79fde13215b310c593", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "11.10.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.2.0", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.4.0_1550604232999_0.4670220245848269", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "express-rate-limit", "version": "3.4.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.4.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "be15f84e257893bb9e6de6d098dd2f84b7e0d0cc", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.4.1.tgz", "fileCount": 4, "integrity": "sha512-0Em4GrXBb47EN8C5sk8w5H0X0BH0kgHlVMfYjOaPTmRkABSfIbqpiCMc9LxWT27+X1RkpG6bGnt1Fa/UHwajbQ==", "signatures": [{"sig": "MEUCIATZHCKd0/GpaLEgePoxX0VMR/T94XiCUJkUANVyPJAtAiEAr2+9kqMLPLwPeWe6UO5Ypy6EpOmyPHvZKiTey2oCqqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctiLGCRA9TVsSAnZWagAAIWYP/0uCj/lhkorGLFKvpL6N\nLpAa0cLltMFFC2q5mR4njh3ZyeWh0nFgLZn5qK4swvHCE6Gr1m03C7lAK1wZ\nZ5lRdY2020lyhJkuF5vJ+wr4uUIGAK5wma+wQVQLtz3hZnPKdXWSUQz3KEve\n8DEU/I3B92zoHAtaoP80Lt2WHCr7vdB/Ektk3aqvmsV0EkXhtI4GME5+ibBU\nnUjPMkrgQmM11hTbxRj7kn4rdAsrfm1vLsmib+e2WbwoE20XdPa3Dos0LjM0\n4qzFgGoiJtj6rUAh1GkBcbNrNAIdEX6os6bTKT8PS7dWVDcZ4c207/ArU3TY\nmxUT5at/vpfuTbJg8JKyh0MxgGlSsbIdv4aFWVoaaorHES65+Sg9uVIrZ4Sx\nJBnA+QHN0t5MR1ARDDdI9e2U7iP90jow+2/Mu7K4TwCGDnZEPQ0PHSLHBIC8\ngl67ssRUC1Voc70PGXG1je2n6yfQtTeJ865OvuNA0Z1FcVlPIVYlL9JevThh\nATV0O3tCYjMpXDvpYItdfqwvnsGFFV1LHozSv+ioIOk3rQJbV4RWhO7ElKL3\nUInV8KaAkERi+gxqzgdqtEKIxH+QEAu3BMr7a7dnjX9A8zn9hr+tKTuIBfdQ\nF5C0af0qdcwxtAZPPdFPiDQSQTGithiBvvh0rLWuI8Q6sHl+UjlC1YFqCck0\nsLKs\r\n=ruzF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "aa39dde884bdb2f6a81db22702836fdb2c74ad3a", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^0.14.3", "mocha": "^5.2.0", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "1.14.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.4.1_1555440325706_0.4397409141731259", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "express-rate-limit", "version": "3.5.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.5.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "6aa3f62f5adc4dbea9162e8101208d7aa5745789", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.5.0.tgz", "fileCount": 4, "integrity": "sha512-DLUgv9lqUCEil5RV5naS/rABzfi/zOEfgU7Fb/0f+QyRbM5pHCvZozhWEeD01b0V5RsyGBNtRhp2YxfvrlgAaA==", "signatures": [{"sig": "MEQCIDqY0sLNcPhgXPo6QXr3gY3yVZAouLvtLn2RM4e1qHXLAiASK/1YeSRPs6ju3MSnP5nWl40sTk/qpH24NHSl9rhHuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxxLhCRA9TVsSAnZWagAAzcgQAKCbPKrsRHruArASOxea\ndQzq8uHA3b5EywvC5bKGb93KGPuOwTYlQ0dIaNuj3RHd//iZ1vqIVM8kguJJ\nVPsF4FHuyISDBn3rHrKmiw6wiFEuhU933gzAoatTXDcj/EGRjD37IbEVuQ2b\nxtM5wZhTiuAz7UX7RmQyXP0RyEvgsIQTA+Sly0K0FQD3B76vbMl/JCwHTXY8\nExVLPAYaN3bbWDGkRHPJK3U4i8hQ1SQWCnIkHVn8Xu4lEePVN7EZthj/01G8\nKWTOpwmB7mQLzIDwDjyzFMXT5YIVW9xE7n67azsW6/lg3TTPGD3bFNENgbMn\n4eGhRvRbwSz8a1tTCW4Pe1MM500+Iozk9971h8Ft/S94u+FvdVIhjEDodI/p\nNqQOrFiwyTyGsCth3067qFQeOz4oEZ7U1sdb0QjVAYZtV8JikZMqqd5BLgp7\nB7WfyrhZpaDjw2UvpY33YH9ZoF449UNx048RbhoTV5kFHAlmukJuHzbuvgND\n0GotQwOXmIKn4sRFE3nOCM+5pHE9mutgcr9cPuf6kVPUqllFKjASmt3laQhR\n3u97KTWW2GQ/kn+iN3RXqzvsF3ZllNHm4pbd+7sjvP3LA8WIkIbH3IZeV8KM\nPvtCC4XxCzeCEdRIJ7WX2QPfrbiivJcHffYNA2Uoz7b+SdTpITBt7S7AiTwm\nD805\r\n=Rd+z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "3184b765eb35dd2f1970ad20943a30dc17754627", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.1.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^3.1.0", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.5.0_1556550368121_0.9220219460753385", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "express-rate-limit", "version": "3.5.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.5.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "159e3bb2b92b8d55949a416cb1fa8ad431b8044d", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.5.1.tgz", "fileCount": 4, "integrity": "sha512-aoxJLcqOAs2nEDwrQKrwCRoWdYxS7Qu+W1lSe4revazBxT/mTgEQrltJxt4z/AnAy/Qcm42M4ND+q3vI7AHL5Q==", "signatures": [{"sig": "MEQCIHxz/CBaxEeGRJ37x7FU5Ko4DiJwLpIiHIROGRZsOvsqAiBMXUfWnQQlHzS4Oyxtjiuyu7vMUHx3GEsUAGIKbPiKog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1aZbCRA9TVsSAnZWagAA+VoP/1InFDV33lcFWmE39nAP\nSNAY516RJc4DrVwPvZeuVzFGUwbCHNDNGsNlv2yfDsSbPYalewLgEktiYf5i\nnK5Q+2Ov84Hr6NmHwp/Wz8agNDsil7MgI3EojfRsfGyFjsdzvJWEWLM4Mbtl\nTs4rM6OqsvgzG+kuSoaw431IJrAeNnjdbzi9Mi+/l4M6SlLURS172Tm+BZVV\nWfAI0mlIXwFg1svbTaleys/WvUOSMHdWu2LV0DLXS0pZrmJR3mjpL14StqCZ\nedb0GZ1sLuxgn5TxaXbbG+x3B+Dz4LeU3Ub51tP2lgUfdwZWc77F8VHLQtHH\n5F+O7pp6rfWOfC/e/UwE3Chyf+8Ty+lwQDsGLLNYLSALohXlAOku6jKOaidy\no+XTfbgHVqaOKE4rOYS5FWh1usx/b672ZDEG6/0ieBosJ6M5l/qTGXCrB4wm\nhMm9LVuhgAOPPaQdeDK4rlSLR7ifW6b06rk/tVqosKxEUf5g66EKAy9HQ7nV\nJKy/BxhK3IObjd60Zyfquwba0v0WdL7IjvHHLGrpaexnJwv9P8XaTXQH1z/W\nMz+FbR6R/TBQtrFa7S7j3CLuDyJ5IsjAu8BBJG+q2NQUvLgKHLcmhYe6U0dz\nUMoHzEb22r0oCDgzVwRQLBWF9d8JSwhynFQzjNlliMQR29ij/w2YDnD11LQA\n4UpG\r\n=TQx0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "types": "index.d.ts", "gitHead": "9b4c280616fa60eef6cbeafc31f01dfaf7dab0ba", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.5.1_1557505626937_0.9031020688695286", "host": "s3://npm-registry-packages"}}, "3.5.2": {"name": "express-rate-limit", "version": "3.5.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.5.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "c019d506fbb77f59b03c140eec499483d4f720b6", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.5.2.tgz", "fileCount": 5, "integrity": "sha512-RWNMh0iqQSYcP8FaYbcbbUTM0kz8QiD5wiy27QuATbnmBfX57l8H1047EMHoYGgnrhLoPCDxeamcQQaeor6EQQ==", "signatures": [{"sig": "MEUCIHVlQpNR8SFHJTHwnEQToda4Yp2r+y4+kacplaLIhVvJAiEAq4cMBhTIxyReRCQ5j1ZvHF2vVVtxTFIBOS0UDBh+gC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc4/p2CRA9TVsSAnZWagAAPNMP/3sTxI8gSpHV1MTMEVYP\nDULDie7S9cdc7XU1cxzCNBQekZCkNjjUxeH3D4iT03Gyq3H9RQy2xu5eIf6K\nc9k26SmqZKShL2WVBOKP9kUE1skHe1UYVAdx21XyBN4gotbMfg9ItZpR+f7v\nQRqyfeRGGXwqwEJ0Jca6PGEBD5oZZUEaoQxq3SZueuJ3ZsDA6Gbn++dHDfST\nMjTt62mzrIQ4tMUICncPGPpRLphSqydpb3EFolWgt1O9ft6Vc4jG6/o81S/g\nIQfsD2dKVTeGP+0xdMakPbtBo2IO+KCyj23JHA7nvtubI5hZnc6av0c9aMrG\noXhVmw9lepBtMoyzyxnRXoRPz0237t5TIal1Oy6Kp+kMS/xDRISu9tsvGuRI\ndyDmTbql0yEZbtzPbjHLQ/8205T78mXk4pyhmCCEy24MGNM0x0ASOln01a15\nO5kEA3/DsyUFU3/Qen/g27x0fJ0JkxGIHDj9rfwJHr3A6ZIJvG4TjFf3GjL9\nfAK1C9ybZnTAyM3rEIgIdaT4KuN3/nDx+eBGxf/9KDorzxA4o6fzLlm6Zq9v\n9XHiPRxEhHi7yx/cNMKMDDeaCGd2Fuad74tOtdnTx31lqqEwQH8rIpQ05rxY\nDEbflVFKUdxwJSXfl6v+WT35rfmB2Td78nP3ZqEjHV6g2OwSlla5P7dnVQgX\n+hQ8\r\n=GdXB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "types": "index.d.ts", "gitHead": "ea1ebaeb3755df09868180f959aedc8ed776e28b", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.5.2_1558444661684_0.1487176967616144", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "express-rate-limit", "version": "4.0.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@4.0.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "e1c36ae1f8f50eab1479df176203830402d7804d", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-xxlbPLmF2GgiDZzYuMXRsTCxRYYVhnc8/Xm6+VrB3rrDaBAmZsapuivrQajqysIqKW/G/Zh/rfLjDMUflLKDrw==", "signatures": [{"sig": "MEUCIEAPov2mzl4sNe+TyIpRmOgnlyiftDhwSX0thNSVkIcGAiEAhKpbG4LqHJC/V9aVO5xJ8dVG8M1FWxhpd4aVSGePeck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5AF3CRA9TVsSAnZWagAAis8P/0cwnQmetNLgjeZWV+6i\nKkqi/GTRG+WVIHtDxqtSh9G13sILw1pu39tANrUijqJxRpLbvVlM6cxIv2Bg\nqsAaZvsePg5Bwt+NcmjvdOqo0qFSdDX75Y3UfsCe+ygk2/0zxXl+nXaiL45Y\nQrtC8VoZRETB1CVvVBm8an3bRSj+DCyZ0iUXGqwm1LMoZfeivINB0s04n/g/\n/Ph6sk7a63CtLL1TCw+s2OhRugNJunIwqBd5cD/qZPkEJj4sS6esUstkWaZ7\n0kFhwZi2k7GEGk6/Pg8OZqwN7PPLXJwR8Vp8JbRM97MUWrK6Dr1GN6dmCWBC\nbYe6cecRK/m8qEEsR3W0WP8xUhljAKSik9pyy7wiD5g5zYhMAjtJqWRESFSJ\ngrqO24eF9yajLz9giCTBB1RjZTKKfz2FB2vYhusd6wd+nYe8UOFtWtL+BXL2\nu1tijJn431migHLbVw52A8SMI+qro0+iKjyWRcUufp14R5br9Og5lzG2OOpU\nkrqD7Wis/XMCCgTQHjnNHuGOKSVIn6tZLA25Is5NB44fB2UpZN8HbVLUdFVt\n9QAkzJEsAdFCUnVfdM41cMlRR2zNXUmYWg+SfKxJVl4tnOPtolKi397Cz7/q\n+vY75/j127XorTVKhR82jNCi9HO1G1XEoMareAwSvJjh47rGKqTRsKmhdDHz\nr7z+\r\n=X4tm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "types": "index.d.ts", "gitHead": "876b020dd3fd9c7f4ccef99c337de7c59479ac2b", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_4.0.0_1558446454706_0.5869253247937694", "host": "s3://npm-registry-packages"}}, "3.5.3": {"name": "express-rate-limit", "version": "3.5.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@3.5.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "dd3c321b8b99e2e74388aa86c8d14d5c02a24426", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-3.5.3.tgz", "fileCount": 5, "integrity": "sha512-V6YEfLt5oNYKIJPBJQeE1xTM6JeeP/e4YXZGPgheo1nF4vtWHUFHmcNsOPxDa9VtIB1zOZ1j1DKScewVetw8Ow==", "signatures": [{"sig": "MEYCIQC67PuBgVThhBSHW3ED32PQDga1ZfP3Lw9Rfj6EalgHSwIhAP7QGzr1svqyKCSMtM55iBnLtbgXes4mUrJVHrYzV0ke", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5KYZCRA9TVsSAnZWagAAm6EP/RjBCC7U6B0jSuXX0bGp\nMfSvwSQy9IHShhtSi1RkDDmA7YtaUaX1wT9dz3MzOFAnxVsVf3Tm/EMDDBtN\n3R1Ek27shnZ4ZCDkd/nPaZWURQiqxriTZKQJk8/tKnAF7cPUa+7JuOAuKUC6\nTWUmhRwNi9GpTYuRI85tmhjED5CWvAo5KlJHW2LOd6rOyRh0+reF38oLuKK9\ntuNORfaQWMQ9qsSuYxB4XWMyAnJpwQPu4XAN0PfawusHCmT1P/cl7IJdD19u\njA2vSbatyFCDKhlSbW4GKWs+dqlav+SPdjeveIJTz6dNFtU7rPpNcJ7fQ4YL\nXwKZ68lqvnYwMXnUHYvFz8GqtuLv5pft+EWbjSfZ8XsXhge9oh2dc1vewjZK\nODd6gi42Zc9NvAl5Cv1xvqMYZPrKeZEtTd6cSXrvRwXRprU6ogyZquF42ybB\naTtYbamO3iDOVJnOjyQi+XiEGXSSQd9Fp9RgLK+RCT5Rbb/voB7wzXdBegBF\nupcHTAPTvzFsZrzsckmHPFPYpYTy/vbbDCZQNrTYtFap3g1xH4yjcohN1OAq\nPLsbr57bTvA2CuizjaJc1lWiPdnCdArAzOSGd+ywnqDF0XTouJbaj/PUpPwR\nU+WV0eCxot041UHdjILrALJ54bccdLiFFZ4sb2N/8mYoqqpzYZVn+i0aq36R\nMAr0\r\n=DHuU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "types": "index.d.ts", "gitHead": "74ecd741c26a209a5177296f33956d952f55d8a6", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"defaults": "^1.0.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_3.5.3_1558488600398_0.39618854807003134", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "express-rate-limit", "version": "4.0.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@4.0.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "de61c81a8b113db824925310b4793d42b099bde1", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-4/RUgqJwIjnPuFUmSm+2CsZkreqlDJM2GHdgvnuW/nI+Vtl+j1MJ4nqkdO6zRf5RvBVWZVDQU8B50rKk0elHzg==", "signatures": [{"sig": "MEUCIQC06Uk8oIayGKP0Jlzl1PJ5fq/BgqouTkUEXITWouVKYwIgJ4Uc+D2m0elyX57DcViz0iC3Al8vBu58w0KMo9o7D+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5KcACRA9TVsSAnZWagAA3z8P+gMDPxS9MJHYHpA4GId2\nYwVufBWkdjfDFVR7514Bzu/6pZsjhFve2G74hDbOsJDdHW238ZMzTWOp/rck\nXPLqe4f3/HbixvWh3+wvCug0d45W57Atrp20iVwCrabF4lmL8ClmrRI8rNdB\nZN8nj4655JYKQnttbkgcwzUJVxSbEJXTofj8j+bPJynJzZOxg2kjFu4OR8A5\nZ3uFtTUIgnGRybb8T28W8z/VsSDirs0YVkY+0nnmbpuvmRs0UE3UMMLOkIBR\nM5ZfBj3fuSU2Foo/s4XvZwGO+C8yrLXD/8W3ZqxT6rQJGSCQvahrS1y4DCNe\ngnVyLpJo8rC8xp4HZcLYAyvLYL05yOjwJX9kSddprw/ggslbbaiF+i+7xwB5\nLfNlEJIVJDNDFZwSFJdjvcdjSKPVk8ItHCZyMQtgmtRzNSp0UrrAZAuMjv3V\nlDFCRrDjZgEC3yqQZgFOc1u0W+pK5FQy+wCbqC13GX1EfxrbhEdwad3UWhZo\njybnkThtUbO2qiJxdVlzne7jLakh6YXnEs5Q02XvvChMEx3fL7E2sdVCt4QH\n9XEUoxOlifWgwOc68ZqJN+aQ/2mKGeRhVXmiYQTp987jHcGLFKseHpbdmNF6\nSyOxkkIN9YVIZii84DoZSFqTCX54/epumxE+LidPcYW8lYDKZOZLOppquB7J\nrNbN\r\n=m7Kj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "types": "index.d.ts", "gitHead": "5e123ad26ee6aa2447b11eaef370031e95980b3a", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_4.0.1_1558488831985_0.9600359319207308", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "express-rate-limit", "version": "4.0.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@4.0.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "89daacfbc3817ea77a4b3410e4126d8e61ac7396", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-aTll5BfaKrZkE1C0A0cPMuPownp2d/EfNd3haakZ50lokP6GcjCcvrTonpoJgivj7mJOrJKj6gaBid2+ppXojg==", "signatures": [{"sig": "MEQCIGxgfbzzCzV2EvBNq8CUriMsQan8sjHNtnm7dMIgzPVdAiBzJeAlde0Z+rtTNRnTf+OVBqZk6atCeEgGO9ug+LG5ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc69cDCRA9TVsSAnZWagAAsNQQAI7AZAMEYtorFwvPbBma\nFDfASPUL8YCFe2Shh3CD5OsYGMXoEL/hM1VF1L5NTs8wU4Rhd1OhFcxFA08M\n4sBxsxcrSMNRy0+6+xs5814ZDiUZNZ9TvjD98Di72CmTGsW1qfE4RAdVHiq2\nrRhwO3qYQWOqLjTMbZhWWi3WnzF7Ij8iOGIt5H7eb+Ms+CMU9Cchfs1KGolM\nQ2mxRj2iKxMJ9X2gh11HgjDs+exZele2i+UeMrvdj1ZMVeRGHqSne5tzW+pe\nv11HAUEn869/eoAxqlFfVMfmX1LToNMG5fCC3WGzPMIKD7SwAjkZ8FjXnyNA\nlwsXuCR9iwplLqiQEYmgK3S2ecTy6XS+oTVedn6E/CMIr2ZcRGz2IeURxuix\nHkaFVaZmbza3p7QWAvoN/sTDwd6LL63KwbNv+ZH8LwMveA9sNri/EsfERlx4\n6qCkb7hw5dDzxoY8rxWmS3CiW+WdC9TWaHS5oxe1m9L4z2HN8B6y3AudVumC\nCRO5oisL6JLlSLAFbYcj12FePdkffdy3hd6g01CgJjEu5ngG9bQRBNBysi/q\n/XZF7ClC7TTLgp31+hamoTlJk93xNR/LOOAcViuNfbQTPNMvvWbPGknSx9xS\nM3ctnwIf8yZLhfmKZmyMZlEo+KXIQ477hOOsQ6GdSs7hUD3yEGlaJjyXjKsL\naEXo\r\n=t53V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "types": "index.d.ts", "gitHead": "67cec62f64204b7c47cf8564621b39f1353d7b02", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_4.0.2_1558959874245_0.9917357751928111", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "express-rate-limit", "version": "4.0.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@4.0.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "70ec815f6ba2d6a62400c8a7bd341c420b4330cc", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-9+v6DXQdLLd4EqdEkXXM1ctdVNgjw4w8V1WLvlpnIb2SZ5wJ3Kvu8FOMzBPIAX+iO1F24AG640eOOHCv/0GmKg==", "signatures": [{"sig": "MEUCIFeT466TrJcjnjUz8HyKzHim9ySTy4lyZPVs7wXofX7PAiEAhGbFNaG4vvsZfr6bHIJgxHtd0/0j4c2IQ/AAxPH0wPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7sAkCRA9TVsSAnZWagAAnEAQAIf4aC3aVGNNZ0Zlbdjw\nruStPP8KtYVzNIn/sGbpzQNSBlyt4bC55eA7ywxfEWcMACYHtVubRV9796ar\n6mK/0j7kLaT08hEhy8ntkpFnMoalg8PMVGOxJL9Z5ZVQdDKT9zeIBw7TxUbR\nSjcfhNB8em1SdRTphCcZCicvZmJALoDxFQgihZ0LhNUp3qlAH5v9zYnkp/Qs\nEOzxY0BTvYtuQW+TqObboDAShbH7Jvdhi7bbguSmwzippYxMgLpMY7f3aYmq\nNlgVcXVrgtBPt618A6tdoLjVLFiLMS70zoW8R2hVUeMAVqlortxMGG6nXiBv\nFIxuC1NCKmadtoiMnXHsbkOj9X8t+VHgkp62eeVO88Yr8X3HI4RSm1fsZYxl\nrQirxZteiyl6jZqoAj81mztpJxmyhpcGiM3c6szOeasfblKTKPFt+FVAMANJ\n5LkHuhrc1QTb8fmf4BIOaCGW7hQ9MaLfp92GGVnBOukRGd+O9CrYIi5EFdbi\ni93feHYIU++uymvHm2/W7e5qp99yXjTyeIFDwUkhm4WK0hkSBNclemtFniEi\n1Xn1+pvImAcRsiwxqYihBAo5U+NuhtlBc6rl13wCJsgL0IvJUMapatQJ58Yy\n+Zn6E7PDkMHpYFUxPa0mtRyzLY21lgjG9guOffAU746ufpJUS7QaudxLy1gv\nAuIn\r\n=U373\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "types": "index.d.ts", "gitHead": "b2cb5de5a110bc1ebd754a0f23ed0bfc5373cd19", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_4.0.3_1559150627671_0.550001689144894", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "express-rate-limit", "version": "4.0.4", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@4.0.4", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "a495338ae9e58c856b66d1346ec0d86f43ba2e43", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-DLRj2vMO7Xgai8qWKU9O6ZztF2bdDmfFNFi9k3G9BPzJ+7MG7eWaaBikbe0eBpNGSxU8JziwW0PQKG78aNWa6g==", "signatures": [{"sig": "MEUCIQD9NRbLto2kQb27c12N3AwIrrZndLoq5RHyaKbvppZrBwIgcXPRkP5mWKNUjazlNPLFXcfyChqvZTaCJFrCdKC5SJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8sUMCRA9TVsSAnZWagAApkkP/0tWat9l06YhlXr6uoyy\nEo5pxJGOcRRy7EeFjX+SaX8pgK+O6Z6BV3UyPHeBGQOJt1U0Y0KnYWWRJWsF\n1VpxT/UuMdJe+CFItIepJ+VI2TZd0ey8i6oQko3Qiwe6iE+yjzYEKv2DpDNZ\nTJ+q4vanzByvccXHr1NiFW5+R77P5SW741V/I/fYMRYz0Gh/qT1FwJBkjp9o\nHLE6yCwEIMkVo3B+pzaf6686U7vlyCltt5iCNMXSTnlqCUHhDwrfZre+PQb5\nQzEdAWo5b8Ssy27FB9Iu9GqUCZ/8Zx/727RTfMPH3+DisHaLbEUnEMupipPr\nzhc4JsiFa9wy0eXBbtTa5dPDuIHL1IAESFCH7mgGpw5mFDSJXrztt3WoMU77\nXmc51BmOtuZwkSGe0hfxqhsjqlByrRAbMy7FoNMcuBvR2gOYFPrk0mycgn6D\nlcanfw3IadQWvTN6lpgEq/QemNijEQ6YgD1fvXfmaolfqBVuwQojVgwNHGDE\nnM4VPSyjpuZIjMW0dP3wO0aHopI16CFLu8U16j2b2DljK3FJAVQDKXTGh0yS\nNUlVagpnOdkZ8IQ3uOaroF+EK5RqlVskjVIE45RTfy3sOHU8AMCg9vnHd/80\nSm7UGqIH+XCRFYGPhjWu3kRqpDP9+lpu8J6kyjGhgrteHCy650xwi+ODTlvL\nr6tU\r\n=3qVj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "types": "index.d.ts", "gitHead": "a06e2336efe0b2a0f239307a1fdd0a957bb8d0a9", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_4.0.4_1559414027950_0.4337192144707174", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "express-rate-limit", "version": "5.0.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.0.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "9a6f4cacc388c1a1da7ba2f65db69f7395e9b04e", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-dhT57wqxfqmkOi4HM7NuT4Gd7gbUgSK2ocG27Y6lwm8lbOAw9XQfeANawGq8wLDtlGPO1ZgDj0HmKsykTxfFAg==", "signatures": [{"sig": "MEUCID0ru7lWfHNFkAlvN5T96ehJ6S2PpNfkaNv/ZK0js8MkAiEAgsfIDXjsSVQXUgov1dVww7hAl9bqpq2jWhZcskxUQLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+vvyCRA9TVsSAnZWagAAKHsQAIlMLTgs7Pfdmmauj3oE\nwQJWPO5R/mqcLxYrzM1LNkbOCwZv8kUuyxopItqEXeaZi1iMtTftBIw/BiBd\nRhYj5JqMPtb9YSxjEZy5wr9u+NIqesG8AejZfbRs2KaGhdYDf1iMgKuX1/bQ\niu/X8FuRmRSTBnE2WS6gpOhNqj4qSZ0s/71thbrhVKJWJ57FRTUiCl+Au2/Q\nGo/gRb+tFxrAOK8uOsnDRlGgVp/fBwObOJIGWgP/lfDuS3gZ7PAJDv7rYutM\nlrcoWiRaMswF4TCNwuUvrhr+jn7fOKsRV8o/0iPDU0FvHAUSYGQlZ9EcnSsz\npCupIBv4GNFJZXpnN4pxbgvWJesjJvhw2sRHZEXlc1sV8LGfb4BJlLdW0c10\nISRD8gULfdfKHX78Bf/uWnOxYdOCrvDol//6PR87q2SRrsmja/3esaY4vVcK\n5GoCRdRxQohKb2iTI0yVtooOI23oktJbDE6ekZ1GUngAQpIjYXm/xO3pWDNd\n0kSXCBGWSHx6dFPvYdXBIbTRB9yBe9XKxmAx49c0JfjQe3u90E6yHcY2DjRL\nFkn+2ojVp2LLx7umjHgobO2J7j0AHQ8G4AWigzX2lkntjIf5Z/BaYDZh/l5L\nfeWX9Qw2qymhT16kwPD906W449SwDWwohNjWOXRrGYcRV5OHkl8A8rMJzkfq\nRJoC\r\n=PnY8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "cdb6db46a7c5620bd449abe1537aa04fee7cc879", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "pretty-quick": "^1.6.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.0.0_1559952369210_0.5757450816669021", "host": "s3://npm-registry-packages"}}, "0.0.0-typescript-beta-2": {"name": "express-rate-limit", "version": "0.0.0-typescript-beta-2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@0.0.0-typescript-beta-2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "842513afb14687257ddab3bce8357db981b0a265", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-0.0.0-typescript-beta-2.tgz", "fileCount": 2, "integrity": "sha512-5YYLCRVCWT4BkTVSbpxizs9PnHiH+WgXDFl3igdvjV7T20jkThA/TbyN75xI9EqD1+r9dy6BZHCw1ngc0lGLJg==", "signatures": [{"sig": "MEQCIHBlxKuzoYDFuoNcNgxMiCvI2q8r1fzm6iJ73nI9EtZzAiAIinGLUTK6TZf2e+3NwQLkvzbz2UA8fYLvfsV2HVJbcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJKs/CRA9TVsSAnZWagAA5QgP/34UK2e085bbphhrOkYX\nLDR4MDchAoToyNqN/pm0dxufrt1YXYsg+/rjrF4jYzrTXl7rBiXYaAMEt/p8\nhCqnB+TQTaVUNm3IsEqidEk5in8wHlM1uLC/6erAtcPw6YZLYl5IJ7vlIEXC\npmaewkRfuPdZi/nVXnlN4LyGM6satRN4gzDUfRTu1Re8yOD/bsjmpL1pt8pd\n9qusWp6incU45+4/kR2R1gbt+h56MRCJiayiSxpo0C5rEmGShsqhxu68GaZX\ns4G9wOSuShx+N3xkmwbT3UEXEbj27XCwTYsvykqelh5XwuhtgGfTW7/e4UX8\nzs0jNXAqFhLfb8zIlTbd4pa0dyzWNV1YGum9VKackmyrl8tdg9KNCaabzW2f\nFCVI3fQ3u9SokRhh076b0PBDlReycal8CJJqkl9amli/mxCT5eUcTNVVFvx+\nTZ8KDv1RnLmwyKJNBJZ/ul9J06l6kcgFKTkbu6NU1o5Wlzys9yp0DyTHUizw\nIf1NvL44zmRJ1eVd2QpwiXttQ3MWB6zRg69aUXb8OTECRSs9wZKAIZcTiGLc\nyeo0pAqOF33rLUkBbWRB7znWgbh5vnrANoJ5vZ/zlBN8ULWT1Y2IBNY8CyBQ\nAwHj7gZERmImxxkFJpNczrpCaHrSuVIe0Vp1LDroHpwu1EYe6lgnOhkfUhsG\nASkk\r\n=/sBC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/express-rate-limit.js", "types": "dist/express-rate-limit.d.ts", "gitHead": "cee977c1a128ed7a4d7130ba9c1253b6db51475a", "scripts": {"test": "eslint lib/* test/* && mocha", "build": "tsc", "pretest": "npm run build", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "typescript": "^3.5.2", "@types/node": "^12.0.12", "pretty-quick": "^1.6.0", "@types/express": "^4.17.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1", "@typescript-eslint/parser": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_0.0.0-typescript-beta-2_1562684223192_0.10701872754598041", "host": "s3://npm-registry-packages"}}, "0.0.0-typescript-beta-3": {"name": "express-rate-limit", "version": "0.0.0-typescript-beta-3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@0.0.0-typescript-beta-3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "fe98fe3662763ff4fa585e1108015af17b24933a", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-0.0.0-typescript-beta-3.tgz", "fileCount": 8, "integrity": "sha512-MFHd+ixGfDqI6NGCogP5kLId+A46cn9ISpWkvizY+dAAsd3h0z71CLt63N4CvD9AgqewQCLgqGPrbpKjzzL4nA==", "signatures": [{"sig": "MEYCIQCKifFZWeNdPp9yaQMlyK4hX6kyzZeJVBGTjgORCKU8BAIhAJsjJweqzia8NP8MMRf3xMxTx3IERyKnp00wg3xUCZsT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJK5vCRA9TVsSAnZWagAA/7EQAJjH5kRimftbn2QFeLwn\nctZK+4YDsF5e5+iqotLWVBVBOCehKOgPfE6LdYG8qx+O7Wf6gQsHjpSVXnRe\np+S95xcxeDhmSRgA7A7pIsY2C/HjlNKTUhQ5STn4003QMTfxB89c6KeGT/T7\ni1SRDxj30j2AYd1TtQX34wpomEqDmsIQQkWmmBmeeINfmSTyZwX0xXDOX1cV\nov7jAa8oFu6mEJOvSDyT6KKJwJvKweyxvXSNVvAEImJCDqDrjO5JqEQtm9Pj\nsfNDoibEsJXa6kdzNyR1tCa0YwGcbvZlcaY5kdTTwNBWKAxDtdd4y5wup/i/\nMZCO2kG+48pCGpWXOIU1juPH8Bl5WMTKT3zOeEWiWgBKajjckH0PpiHqZo/i\n6SiE3jdNUbrZGP9+D5dO2AFyAId/GU9ws56Em+IfxpLy3sRRrdLmmZhxVDce\nxzWsjxSeVLYjWGrUBbBT3iVH8MKJpW1vlRXq3EMPfkkkAklu38xklBFsxATx\nPSE1+P+g1sdsoRoqczTZqhIRiUZPkP4c815RERk1syt5lk6rqrxWefSX6va6\ntOpW8zyHqTWoABFR9LOdkJtwOCnQ6uJHawGqrYYMCMVdcKEPHck3W1alHe3T\n7RY/uLNAejMb35edw2zuk3hU/YUr+7XT4r6zn0VaAoH3Q73lt3+2yQgo+/q+\nRL7v\r\n=db5m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/express-rate-limit.js", "types": "dist/express-rate-limit.d.ts", "gitHead": "c933a2d79ba07b62440d9e3ffd1294322d649530", "scripts": {"test": "eslint lib/* test/* && mocha", "build": "tsc", "pretest": "npm run build", "precommit": "pretty-quick --staged", "prepublish": "npm run build"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "typescript": "^3.5.2", "@types/node": "^12.0.12", "pretty-quick": "^1.6.0", "@types/express": "^4.17.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1", "@typescript-eslint/parser": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_0.0.0-typescript-beta-3_1562685038925_0.3006567295017759", "host": "s3://npm-registry-packages"}}, "0.0.0-typescript-beta-4": {"name": "express-rate-limit", "version": "0.0.0-typescript-beta-4", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@0.0.0-typescript-beta-4", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "84ad235faf171bdd36443c504601123b391a6d22", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-0.0.0-typescript-beta-4.tgz", "fileCount": 8, "integrity": "sha512-GwGpYQ3Bd69zoiBFrxe01NxxPIXV8MVKkRwrsEixq31VlolHmiIev1mI0bG9JPG2BcDdMRZR617Dm3RcT0z76Q==", "signatures": [{"sig": "MEYCIQC7r0UWnl5OTqIaxtEp397hdkJBfJzLy9VtfWP3W6GY0wIhAMTUSMGJtxwc8YGtr99re4Uoi9c0ZSKYDe183EdcraHG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJLBuCRA9TVsSAnZWagAAkfsP/1MqEgA83gI91UccYzSO\nQSBOHU6PdQ1XwvCB6Lz5B4XkKsl6XWjVunnOTWZ/pLKL3daEK1JLPrsFBgnP\n/zImM0Gxq+8StLoM2ACDO5XNGaRFTGkm8Q4qKh/VRCR/uCUAOSzHWysi15NI\nw6cpssFGGLtMREVqVijTK2QiiUmHz1b1TO+eeGDfSgDGM+w2Ch7XGlgfOwgF\n5tYLDb4hpaSCKoSQbWE6tQJjKJ9bVjR+ouIwIjlqT4cwbVFxmrbKzWAYgMiv\nSbew3Fs7d09xCZ988pNTkdi913faXbOj8AQLxyD6s3v0/OH5DLHErSXIkigi\n+3NIT4fzcqQzNGCu4pccTQaEK1Zy5y06FSZVlcIvwsmmqQtKhYY0hA018wOx\nBwlhmJIdQOb+GY7fUuBsDymdXeQ2DL0X6UVPxH5Ja7fst0enxV0ptZmasZoB\nZjxIVLgLQFnx4qX0ZkmgQl2sSJuZCyiZP0eNxshKDpRrty4FbAAU+XeDJKbn\n9CyZZjxh5iwEYgQclayb5BqhwFnMWDdF08DI/7WCSku+u8XW3H9UHHoMXUof\ngndDwLSqHO7PUoTPq2LZVNgKAkkgmxhUmugW1zFXoLw+G2rkCNp5y+jB5fty\nrNu2FcuzpNFFha7Y7dmrd2/3MhOWMIfxHd+b+tqIXJkxG2SATx8yNmVu41yr\n3Jhl\r\n=Q5HS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/express-rate-limit.js", "types": "dist/express-rate-limit.d.ts", "gitHead": "8a62e10561a91e4bc7a01cbec0298be0157f30eb", "scripts": {"test": "eslint lib/* test/* && mocha", "build": "tsc", "pretest": "npm run build", "precommit": "pretty-quick --staged", "prepublish": "npm run build"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"husky": "^2.2.0", "mocha": "^6.1.4", "eslint": "^5.15.3", "express": "^4.16.3", "prettier": "^1.17.0", "supertest": "^4.0.2", "typescript": "^3.5.2", "@types/node": "^12.0.12", "pretty-quick": "^1.6.0", "@types/express": "^4.17.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1", "@typescript-eslint/parser": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_0.0.0-typescript-beta-4_1562685550033_0.036784958699135784", "host": "s3://npm-registry-packages"}}, "0.0.0-typescript-beta-5": {"name": "express-rate-limit", "version": "0.0.0-typescript-beta-5", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@0.0.0-typescript-beta-5", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "1325d6a4379fe5fccb4b404fff767a258a77404b", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-0.0.0-typescript-beta-5.tgz", "fileCount": 8, "integrity": "sha512-zKMFBnEh3lUOrO0y8gtHqLKyZbG9sWJVVBTImwY3v2bVIokwLmulr7NrE27TjLTOEgE2bgBjlNsCTRib90VHlg==", "signatures": [{"sig": "MEYCIQChvDFXk5/qIeieMS09N78Gf9CjL0GSUb00BvlhIeFxdgIhAMtIxmsMCkePW8zEbNOCztLe+iZmuZpAWv1rGy3g7WYX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJ37SCRA9TVsSAnZWagAAMOkP/RuDL2V5+3gt2e8SBFy7\nREW4aZXtbSImDmSMFJnwQp4zebk7rDvIJXff8tdVADEGvbhWrKWWG4aV/5ZT\ntaRvlgKMsZ/qYuiEYULyqLR7h1mUkvUjueEv6xuD9i+1ZbNkiCRE0Dkrob2H\nQt/Ue5MOEGSaamfWcRAxygv5OwFsCJLGdN/MaJOyFKcz1FGFmQcZjl+k991H\ncHGOhKUmH1tkIfHW3LfeRYR4WhTQ6zVHiNWl0l4eHeAR5KpswlbGQhXK3Jko\n490345RQD/hFPqw1GDGcw+Jz0RTAfT64CwtZg7bfCwQozmzPJ2nGG2YJwfCV\nhcoAvnCspoBcPj/G37oeKD10maHB9UqzbCUoHkCRJrVEZkdktnFNReXCZWQY\nMyxytCjXiiS/z5vUMhfunvCN9yCb7LgCaXJCs3UUWVrqS1y+iiGnU74kd0bY\nQ8sC0jei1hFSr/B+FBsZhqNeRY2pMHd+uoQbCZ7DC/FPK4HPz7wq4bD9NH0L\n9/V82Wkl0QAvDKXzgM/03uUUILsWsqye/ts1Te4ya6tASsL7vInSw0MNpUQm\nKdH++vLUOM3KFxl+7WFqUTKzMRzysVVm2sUX1xrWJJJ53MDu4UCbBoVhTw+6\nly+Wr5aHWhVysv2hwj296xGp2YeTU8pwxyuz7JUZCXr1MWe5LjRSFINIKREN\n3moI\r\n=uxc+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/express-rate-limit.js", "types": "dist/express-rate-limit.d.ts", "gitHead": "609a03c8183ee228c977653f111be61968cef72d", "scripts": {"test": "eslint . && mocha --require ts-node/register test/*-test.*", "build": "tsc", "pretest": "npm run build", "precommit": "pretty-quick --staged", "prepublish": "npm run build"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"husky": "^3.0.0", "mocha": "^6.1.4", "eslint": "^6.0.1", "express": "^4.16.3", "ts-node": "^8.3.0", "prettier": "^1.17.0", "supertest": "^4.0.2", "typescript": "^3.5.2", "@types/node": "^12.0.12", "@types/mocha": "^5.2.7", "pretty-quick": "^1.6.0", "@types/express": "^4.17.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1", "@typescript-eslint/parser": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_0.0.0-typescript-beta-5_1562869457834_0.023333798969506603", "host": "s3://npm-registry-packages"}}, "0.0.0-typescript-beta-6": {"name": "express-rate-limit", "version": "0.0.0-typescript-beta-6", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@0.0.0-typescript-beta-6", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "eb4ec9db2cc14d589f5347a7c2d47c2634c98676", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-0.0.0-typescript-beta-6.tgz", "fileCount": 8, "integrity": "sha512-wpNnuYoVjtelR4+FOlhWaghnOfHmZu9udVdSFG2V7fHuYC4eWDHNtKnX+F+ySzeVB0pnhteViovLbD/D+MmcLw==", "signatures": [{"sig": "MEUCIFZaMb5rV2iWymXwP3aO6fx0J157XoMueSbLP4cks9pyAiEA+sMYMPCFdyTCkq+ADfQPf2xcBaWzdcOpTCEFbsruZ5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJ532CRA9TVsSAnZWagAAO64QAJ9ecAlj+qjWe5DiHaAA\nVVGYJ8TGOiMg0yP0JBMogfEOSviOgFozwhDzpkpeo6YU1Urlrts6R80btBqF\n+bZG3Fd/hz26lXEjY2LbpM5p7vOY7z5sYa+6zPFYO+L1pvbbrB3Ms6vYUSTy\nP/UUYOwaYn/roz/GWFzCSyV3ibFWJJyBiKTXe0pLT0PDod75S3X51ypxYZu3\ngybriLVGol3dpgfXDJGj1gidHVd01AHVWplxp+ML79Ptiy0dE1UpbE/aOy4r\nbxpqLdavMsJjhl7G8/JSnWUS4lBtHpcXLYR+lFg6+EHdep0bXwBfw5WuXbDV\neMMyFmLM70i03o9LEPWhdtFWfj9bBCqJ/OqOCLXkbp+FVWLxYoClpmRkGEOC\nqFH4d/RrM6R1vB+e9EBxPqUX+zMES4p+uylfdainQVJ7katrQf0cjVp2DPjs\nZlSPhahYwW4GvXJXO10/DNEpU32jCq2mj5iSvWmFDv9AHTfy3RB69jUno5DP\nrZxpdmoogyql4hd/7E7YV29BiuZCrBZ0GZwN7JW8qOjfWjAKWOHsM8iK4O/Y\nvUQVU9W2K8vNMQrsjO2Cg/pkMZYBaWrTuM9Ku8iWcj7FPTsItG8hQKPVTmiq\nNB2einn0kg29+WxA5U6ArH+qKuszmR0ILJQyQoqLx4bUUvK6JA4K/olvWWfS\n5K07\r\n=9IPe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/express-rate-limit.js", "types": "dist/express-rate-limit.d.ts", "gitHead": "b370897d3f2782e6c48a4c1cf7651a3f42dba027", "scripts": {"test": "eslint . && mocha --require ts-node/register test/*-test.*", "build": "tsc", "pretest": "npm run build", "precommit": "pretty-quick --staged", "prepublish": "npm run build"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"husky": "^3.0.0", "mocha": "^6.1.4", "eslint": "^6.0.1", "express": "^4.16.3", "ts-node": "^8.3.0", "prettier": "^1.17.0", "supertest": "^4.0.2", "typescript": "^3.5.2", "@types/node": "^12.0.12", "@types/mocha": "^5.2.7", "pretty-quick": "^1.6.0", "@types/express": "^4.17.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1", "@typescript-eslint/parser": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_0.0.0-typescript-beta-6_1562877429388_0.24673624891235213", "host": "s3://npm-registry-packages"}}, "0.0.0-typescript-beta-7": {"name": "express-rate-limit", "version": "0.0.0-typescript-beta-7", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@0.0.0-typescript-beta-7", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "5397cc433504f3d15a15554d08162f986dbbdb40", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-0.0.0-typescript-beta-7.tgz", "fileCount": 8, "integrity": "sha512-KUIZE9mVVY7YbgeDuAYjyAJNvuB7jet1l9/kycz4Wb+Q4TAeEkb0+QOSvF9GfOXOGdunyle89BrHbdQS6R3t/w==", "signatures": [{"sig": "MEYCIQCyuOea3NeKGNWGscCVpO/W8O505S55W78LOXiBTRhwQwIhANjC5fcksyXT4FVFu86mnsIBRNjSrtGhuLezkHmvOFuX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKKqqCRA9TVsSAnZWagAAADIP/RIJ6HeHf4W+Hme7naPy\nE+qP6ESioVPS3HEmyGRnZEKVenElTRawtS40UHE3uijyuH9xHQHrq32nFPs2\n9UUc5eD159rNhn/BUb7BgUYaQSQymzQVM0gpqBA5/zWOf033z3IN5aPgqdTn\n+SqFCKjXf5ga2qOvo/WSIMl8UTtwC1iomxcVUdrqLM76OfAsd6Khd/0uytkJ\nNf8byIvUJMt+LqtwzUAelWysqDD446HH+ej2H/s/DjWumFd7tFF7fnu23S6r\neEv679kB1wquZ7PjufH22bDsKWAJXkC00VLG2PFTFv/fmsLPCGCEgmtpyJVV\ncOKjV5MXscDbn9JRYsMsgoSF1NJ2M2ym9iUFc4j9sR5N0XabfsJ9rP/xqSf0\n1y+oPnKPyBFiO352uKeEvLw/z/lIkvuUlEncNHwC2sXENVatp4Tu+cQfiVVi\nv+/lIFXwDLRvls+o9PbSufH+4w90G6MiX4W1fBGIk1dEpSj2+m/C2uzsWCI3\nT3vziqgJZR1SZ5GZcsr3eQ52Eb79ZYdaZPg0SSLputucxbU1U+Rehk6OymeX\nedpKwRsd2uJtcUw2XQsH/TPOWc4aSnAQoAICyQ2gX8qf+8Bp/O8ofEt/Pp7L\nZ6O3jMbM9MO+uz0r/hBjHPS3VbN4Orvs/ApRcDcXoNGSv8TQMxeoyR1L1w1M\nBZgv\r\n=BUgQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/express-rate-limit.js", "types": "dist/express-rate-limit.d.ts", "gitHead": "3d09922bc62cef2741a5d726ec5368d48d6c3cb5", "scripts": {"test": "eslint . && TS_NODE_PROJECT=test/tsconfig.json mocha --require ts-node/register test/*-test.*", "build": "tsc", "pretest": "npm run build", "precommit": "pretty-quick --staged", "prepublish": "npm run build"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"husky": "^3.0.0", "mocha": "^6.1.4", "eslint": "^6.0.1", "express": "^4.16.3", "ts-node": "^8.3.0", "prettier": "^1.17.0", "supertest": "^4.0.2", "typescript": "^3.5.2", "@types/node": "^12.0.12", "@types/mocha": "^5.2.7", "pretty-quick": "^1.6.0", "@types/express": "^4.17.0", "@types/supertest": "^2.0.8", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1", "@typescript-eslint/parser": "^1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_0.0.0-typescript-beta-7_1562946217511_0.36888825229894606", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "express-rate-limit", "version": "5.1.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.1.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "572e75c47ef890a6c9a3347f27bf3557d571f9ed", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.1.1.tgz", "fileCount": 5, "integrity": "sha512-puA1zcCx/quwWUOU6pT6daCt6t7SweD9wKChKhb+KSgFMKRwS81C224hiSAUANw/gnSHiwEhgozM/2ezEBZPeA==", "signatures": [{"sig": "MEQCIEtHk+kcrEsb8spBc2625uMerBsrbN9Wx5zWCINydz1bAiAWwHr5QwUYC1QhGZy+hfjKds7S830z/LJRszRAht/jwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRc4oCRA9TVsSAnZWagAAI9IP+wVJSpQWby4H/hoRzUZ9\n0zvceADxd2D3uP2eCa1/n21KMiUddwKRCW5wCuLOyIRJOGI8HMQ7k30J+kuc\nN3plF/9fWgwNQlG29PE96PwTXXqmy/EbMe3lbVLmXLVh+iiv7Cj/DRAHzOg0\nFIoXSWc/amp2+JG7Ozw0pP6Peuf+enQdzQMH1XS8vIjFQcvpndiKkQ17u/79\nrSi8RehealyTzVYfKUW632MpDjBrUZU+nv63Wz6Ejy/sXKpyDK+uWa1wPdRC\nA4BJ7nyAmv5EPlgUahiLRU4ZNPxb0yqyA9K2gVoKsGdMRLRhU+YSO6Vf8unQ\nByd7L7aCaNg3wavaFC0nMatmVVuSHpN2U9k++zC0AL/mBGHjBxi7fBoAS+0Q\nEaC/IQY7aK1lm3xj+gIv7Ph0ZcU1pw+XHZh7btq29uzByewKhpCmvLcc2XIy\nyvR3KuxSfiDRUoiO6wdMa2vmykIThs/h2+8NZnUkEPPk13dJ+tX1CWq4z1iQ\n9wTctOmO98GuoFmZDydFp+G3MvI0iSp9yChK6SEFbKPUYprKupzTcMbvVr0F\nBpzbXXvGpQuBpsPaqwwiCRsLeKe33NbeT3UDwRV7P8KaRS4w7m+cFq2iPvJo\nEOJKUKOfKRewAgSyXqwFc2OzZ+IwpQ7c5NWmmE2+cA7A8gvYq9vRJ/szE6zt\nhs4H\r\n=kw0L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "23d8ae795b46411aca3b151fc27615d1d03e6a28", "scripts": {"test": "eslint . && mocha", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "13.8.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^4.2.3", "mocha": "^7.0.1", "eslint": "^6.8.0", "express": "^4.17.1", "prettier": "^1.19.1", "supertest": "^4.0.2", "pretty-quick": "^2.0.1", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.1.1_1581633063887_0.5510599445989075", "host": "s3://npm-registry-packages"}}, "5.1.3": {"name": "express-rate-limit", "version": "5.1.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.1.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "656bacce3f093034976346958a0f0199902c9174", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.1.3.tgz", "fileCount": 5, "integrity": "sha512-TINcxve5510pXj4n9/1AMupkj3iWxl3JuZaWhCdYDlZeoCPqweGZrxbrlqTCFb1CT5wli7s8e2SH/Qz2c9GorA==", "signatures": [{"sig": "MEUCIBbHER5Ij9GazjDhdhkbpIqgaBjGPaOdj9apCvCfhV5SAiEA79TFYprcqJ0Y1Qm6UIhp6UW/3Q/2mD8A4MyhHZlA0Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqZzlCRA9TVsSAnZWagAAVPgP/2ngxFUn2Zv/4GVCkmwO\nYJGkUDsa+ox9JOwgO/oxbopTsKRsC/aRRI6m02G+YNwfbOdB+hwNWeyzq8Zv\n+EUJthUGom4ku4SUAUyn2zRkstFkZ5H6Y5eP/6KWJnDgDY2UYCXb21D5PvR7\nBPnYH4IuT1woSdeSApQm8Y2C4x7A88rN3SSKPuHTN/+3UsEj2FC9IXY2WZ+v\nKEb6pHWjgKSILRAMcIzCKrwt47P0BDdq1WDf6cdItsuFW36bDtueNnzlB4HQ\nXnkq3a8Nq2e2WinUnK0GwSQkkMrW/4rsWPEjsrjqPCtI3zok8xxoc0Tmp0Qn\nx6/P4d1HjHNFhJSjY9Z/ZxX78iRx/fVdWWZJaVP3Uqvi1r0PtpneMwppKJLV\n3/aVjR6MVBJND63lKRBEvnfCN3fL1Ng8sXO7IRwdUb31zsuF8w3u8PpteXm2\n3ZT4MYBqECnVW7156/Meg6ZNAuu2zdQDgXX57rnCgv2M/2GTsVKJrL3N0BZd\nQ/tN+I+aSxQ2KhzeKWqgQYwPwaPLhXdw+paaK5abALjTtqwLbAK9SgaQYhu6\nav9TgnBR/mOCMcnlbxbRRsFLeIv4jI2KUolExKhwuof63EPE13wmwBOJr4dG\nXm4HkoCl3oveQJdrZenJGo909EjUZceYgH++bYM2YG2t0ofxpXWSvhR2ZQ3o\nR5ZG\r\n=rhsS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "41dc7e638c24d501e691bdad1b34825e23b5cd49", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^4.2.3", "mocha": "^7.1.1", "eslint": "^6.8.0", "express": "^4.17.1", "prettier": "^2.0.4", "supertest": "^4.0.2", "pretty-quick": "^2.0.1", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.1.3_1588174053412_0.04446174338670561", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "express-rate-limit", "version": "5.2.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.2.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "9f0b65fa6eaa49f9bec4f0fffe80c361007f7e8a", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.2.1.tgz", "fileCount": 5, "integrity": "sha512-revjeRfaInpFjEdFGkvsoZmsvqxWVGegPa/C9stNxR5jf2xGZG/6mIG4f6apFzkTvzccALNS5qKls7NPoXxb2Q==", "signatures": [{"sig": "MEQCICZelVmGEhEXarMgY3EKCzAr4VOMJUejZeLxOFP/tTnqAiBQmWToN2uIRHmPQ1FsKah8eHyYHvmtpHZgCJcKa9nNnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftplqCRA9TVsSAnZWagAAxvAP/jY3ODDu3UVukoTikRzz\ncazSL0xlsDmHYxBbtNz6f9+Cow5mGDKChTsn9gTXu+pJGWlOLYF3aPuGhsHN\nq3GYM0LcsaBrY+g5hDpAsRFVySc/jcNrT8rrFIY7tjdGwVXML8ZbQa/dT5fL\nNVavX/VN8xytGUQ7afhgDBopYhGPFye+DRK+qTFIl4XuDL934yUPSvPMP6vw\nqM6zw8lRgi+2UukUB88uY/tP3bWHM4faEuzIcsGSn4uCuO7cuS3+ln9QE1cw\ncOd0xxBIt//8qQaxykUXw7XA1JOJS7JbGHDVXOxmKcm/XDlmXhVKAwZ7+sBc\n5NuUn83UAMOOAkZf7Ad0RgiuFQCJBHasMOW1N2nPu34I8XNHmWXsiKfjOSZd\n26SKWsMLhVSt3brtNCYaBXUdy7FPtkj+DHFFqDkdPBgx0LT7i84lc6ROFShk\nPPT8wCeqOsPdA2kJlf3TNdWnwNgs5IYaVefrUckSSmyCMCTy4TWX3W0uwPWH\ntyJU/6PmVuKV24ro3B7xH+4s+oM4IrzRQqXZf3MWk93UKglEoFu6tz14djYl\nVKmJOmbuDSHq9N1UumN88o32O2/UMlkEczf9GDt4YrCogAUrC5OiXpmvoLpi\nGHYp/56JALP1+mLWXXKqi/4o77z85f9EztseAUJ7njg88JNlijW+1SbMNO3z\nr/8M\r\n=Dhdn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "03c4bc38b749519e6dfe575e8b516f447eb7eb2f", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^4.2.3", "mocha": "^7.1.1", "eslint": "^6.8.0", "express": "^4.17.1", "prettier": "^2.0.4", "supertest": "^4.0.2", "pretty-quick": "^2.0.1", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.2.1_1605802345823_0.18918549564142895", "host": "s3://npm-registry-packages"}}, "5.2.2": {"name": "express-rate-limit", "version": "5.2.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.2.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "024d9b7218c530142ae622fc18a66ce075ad3e58", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.2.2.tgz", "fileCount": 5, "integrity": "sha512-4ibK8Xi3uUdUua1kJKEImA9J2kC/UtVshxmDOtV6FKA8cy+Qm3O1yd3kSYFPR9hIDJ3cMExp1faEpmcHzmaOQw==", "signatures": [{"sig": "MEYCIQDKLofVwVgIwE9c1vhqA26rVXPfdxDcnl/I1wfJcaJLdQIhAMuVqqTCd2T8XASaRgOguL5+n89oDiz6iW5d2Q43lvRO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftpxRCRA9TVsSAnZWagAA0TIQAI9gPLgVvgXHaSxRunxZ\nZS14f3+G149ZnwNPKAgyBaPVe70H+88AS6Zy5MJQuiHaIEL7+a3uzS4JDrXF\nzyoZLrW/8Bjf/HR0KVxl2kHtzvOVHXH7BK9uzPPidpfN9NOvWzhrTvkvSHZQ\nGKq+7zUhitlAB+AYeg+sPPaHUw5KdJHLT6vtXxUv3/dBR1tIxP99pWVa/TZL\nRLzuDS5lDR/7jebS18OURvwsrxC6EEEpvroNwRRdy0PV//bOazTAYj7HZQjl\n/95tGJTahnBOCxWXIvPvFiEmqTtcG7TeIAnsa9GRZJFI5O1CfWrQtscBm54a\ngQM1110qZOUCB2FtYm+1rWlGTrKalTIkvHFdyEch1bLEu9g5+rPTWJW+DgON\nydFRrklNJTbPFNwHu3mqZKnzzQa9gLQZJ/t9HCuGvG51S993Nm355itOGBa4\nyfDTiNOlmHR3YSUsxip/SswbxzxTT1vh4Z3cCVbkQYr0tK/lTDW98lSpS4J5\n1gSaOC6pK0/H8dxJAvGJ4B80WaqPhwDPDVWFovaNRQIyM+4zx4VQnzyezk+t\np9RTzBdH5yK8mfZxLnMswQMiXdYlYGaR7E8dqXaNju6gTFNWnTQREOgmpWuQ\n5JlcSZy49WUBPYydksJc1BfTfPpmS7SeEs3wRhKbzU1qerbWCd0QF1+YgpUx\nhfR7\r\n=iSrd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "6e6369e5524a74d83dde7b7c6790b1deefd7a3b5", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^4.2.3", "mocha": "^7.1.1", "eslint": "^6.8.0", "express": "^4.17.1", "prettier": "^2.0.4", "supertest": "^4.0.2", "pretty-quick": "^2.0.1", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.2.2_1605803089039_0.7275828074225694", "host": "s3://npm-registry-packages"}}, "5.2.3": {"name": "express-rate-limit", "version": "5.2.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.2.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "ae73b3dc723decd697797611bd96e9b34a912f6c", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.2.3.tgz", "fileCount": 5, "integrity": "sha512-cjQH+oDrEPXxc569XvxhHC6QXqJiuBT6BhZ70X3bdAImcnHnTNMVuMAJaT0TXPoRiEErUrVPRcOTpZpM36VbOQ==", "signatures": [{"sig": "MEUCIQDOntg+JkKJlZO3xrQ/ePOO4CpNFxcKaLJ9pHTIi653+gIgXi7g6zzXDel/6eI8+G2eF9UnaSDJ4uY7q/CFt9BkLn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftqSvCRA9TVsSAnZWagAA5aoP/RnUEC00NBCuo4NQWorC\n3Mb5qxDONTkiMe836MiNtqJkQNrR0CyZeDFfcDid0duUIlOPPqI27GlJSaaZ\nu+zD+v6FVHEaEmYR2OwF1G6AQ0wPZ0OlirnR6a/SeUj1v8AEIV6kzszlGHI2\n4pH4dnDBMiMywW7xxp0ZX/IZ9EskqnDXpjLThSJV691K81U0gtWQ0JBRP/aN\nu9/kv6APRh/gPxOIQa1oj6UFm/F6xZEoMJTN6ekI9rsm854f2nCf/WnLbat8\nAMj6ojRKZMg031UqorLUE2GIygoDJ46/u4+okeFnJ/ephhxqHjAKrMMsXnD/\nVRVDs/BnNeYTYMoROUWzoIPehfr0f1v/mpyPPYvkDX2SNEEtsYsI9GZFYZM0\nsii74yDILrEUP75IgnVS3SwPu2HFSm/mo60B/OzwXbVioAOCTSQVt8ekmMUX\n5wcw3yzgDVwDLjtSGXIAsfIzeK/DYy2GI+BDZPO/BpFmLuHyITUR3h5dAD8W\nHyFYcLh49sWqRqZEdgA9x0WtP4bA7av4TO5kx7lIgsMUe72l0F3l9kwNtlp0\nDnZO7zybn976T2/sexM/NYragHCueq05u3WNHCSwK8WkZiKuVVpahIRR6n4t\nml2dudvQQuVmGKd8mVxhUgI3i23z5NNoQ7QjdrY1bCH2ejYKY1cTNG3ZtWps\nDB2U\r\n=N2GH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "2a48493756ff2f04c79be1be0330f5781e2855dd", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"husky": "^4.2.3", "mocha": "^7.1.1", "eslint": "^6.8.0", "express": "^4.17.1", "prettier": "^2.0.4", "supertest": "^4.0.2", "pretty-quick": "^2.0.1", "eslint-config-prettier": "^6.10.1", "eslint-plugin-prettier": "^3.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.2.3_1605805230657_0.004247953155696926", "host": "s3://npm-registry-packages"}}, "5.2.5": {"name": "express-rate-limit", "version": "5.2.5", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.2.5", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "956fde02aaf28724c0fd01b932986baa35143ece", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.2.5.tgz", "fileCount": 5, "integrity": "sha512-fv9mf4hWRKZHVlY8ChVNYnGxa49m0zQ6CrJxNiXe2IjJPqicrqoA/JOyBbvs4ufSSLZ6NTzhtgEyLcdfbe+Q6Q==", "signatures": [{"sig": "MEUCIG4cKC613YsBAHFBECpxW0IIMvcRIpcVdOtWetfIeGtXAiEA4YaKP/TH0U0Mybtmrr3830vAdq6HnnJ1DU5tZzB7d7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIV1xCRA9TVsSAnZWagAAvjAQAKFd6Na5nnd3qF5Lyu0n\nMZIXaRssALAFWAojZOesoDt3Zu54iZDcQvV4Vi+2lJvFsb+Lt8SXe38nExD3\nZe7VlC2TgdW/27nGDmHxjoEvNezE9HjVUvNGxpuuUhaeerM/ENtz+SEKLST0\nrmVj6lZbN6yZikKzpdlFCpnvqOl+1ytOFw23+6UH6D8ZH/pWqSNE/h719Br7\nHJm3RaylRnp9QfXwYy+yPtphB/l5ejGTyUlM3hUaMmkOmApx0GsTR8IKl6io\nZPZ8w7Kh7Nwc/AIJ7Nbc9OseZHwCYiFWAZpn37jjMZb/oTM/z7VJICK5H1CD\nKhIXvNI9C3V5v0AUS3fgVsafB4sX4Dm9SJsDU6O1E4BAuGWlKXVjY3XCMQuz\nJ33xDblZqwrMFVIfexYR1pyM7soA03xWUmAVEnyuOohx43iJPPJcycXU+Bxy\nu3Q55Vtn61IOcSQxA8vzpNZ+yvcnlxA3NDTLSCHIaZgNp0zHhZfJrT4pmMiu\njtOEShUqZm01gWBRO8PW6y5GyV5AqgsbOLxuHiSe7OUyxlg75OCGlBWi8BTa\nKRYP2JbaanS9psBHSPHAIqh3sVJ9NIAi+8lp86rhYFljNYBspGr6JcWZRH7T\nFc5d9WPc33PMGNYIs66fQa3jQ6V6XT0bXKY1yvNabwyxbW6X5nasL/7y27bs\nCun1\r\n=Cu0B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "5429151643795234c5c0ca5d7731e75cd2dfab54", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.20.1", "_hasShrinkwrap": false, "devDependencies": {"husky": "^4.3.8", "mocha": "^8.2.1", "eslint": "^7.19.0", "express": "^4.17.1", "bluebird": "^3.7.2", "prettier": "^2.2.1", "supertest": "^6.1.3", "pretty-quick": "^3.1.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.2.5_1612799345019_0.006403082068567656", "host": "s3://npm-registry-packages"}}, "5.2.6": {"name": "express-rate-limit", "version": "5.2.6", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.2.6", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "b454e1be8a252081bda58460e0a25bf43ee0f7b0", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.2.6.tgz", "fileCount": 5, "integrity": "sha512-nE96xaxGfxiS5jP3tD3kIW1Jg9yQgX0rXCs3rCkZtmbWHEGyotwaezkLj7bnB41Z0uaOLM8W4AX6qHao4IZ2YA==", "signatures": [{"sig": "MEUCIH8fAJ7oJPxBnogRc0UVtAfkaeEWkji8You8mIUrETjSAiEA8/qp+yoF1seEE6tJeN+aC+c6o3F9zK1FZRJuTj/Jap4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19984, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLSwFCRA9TVsSAnZWagAA5iEP/3EKQDB8C64kxjh81lHD\nD6/xUsFU+Yjr+ARm+j9JTqYmokFnsgEqp7yaJUIJM59QpuUa4Rh/zxX2fb0W\nDLBsNgWAjdxaRANT9eBUJA9DMLsoVuDn/65k0A3h6uJCG0Iw1DtJKdhxbC8C\neDFnZ8BC6SB+N2T22O17DqJV+6efkXYzLOgxZlzucYCL486dJZM40cvR4dL8\nZhjFGXblmmFF4VMshF4BE4qdVFkCsQXv1NF+OcFFjtyklwjd5gleD+QGqhI/\nR/dPkEIzRn6KY/xHl2qY7+OXGgtSaBfWKqrccFXVHbKuF19duXSvqas+V95p\nMDxDtnqvSQG3Imtt0iMLnLuBbaA+N3PrHPBkXjy2M9qF7FGRMGkfiddZGcLk\nLfo50AoDnWU6AM+DkQyZgmYqy9Bkpp88alD09++oHse1QAmlXOa9hsOOWuIp\nVvItSeIw71VuQSTDM8BmAQcqzdZG9id5L7fD31s0bB8P+HaACqGMRngSGhiU\nedtHWrf22F3s/qwz4BhRAzqBbPVEW/jU5dEZaY9wicu607aMV+yBj88e29G9\nJiBjhV10wRPCO7p/sT3fu7UgwdlZ/dvVooI6DgYqV3P8UuS7tPJpNZW4ywQ3\n7C/OtHlRDi9M13oI8It+YjlCbjDTwnKlIOgcbYaz/UamBHaiammHA/Tfc8nU\nm5PP\r\n=ndM4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "683e89bbce099473f6de64d1a2a67af96b4398f1", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.20.1", "_hasShrinkwrap": false, "devDependencies": {"husky": "^4.3.8", "mocha": "^8.2.1", "eslint": "^7.19.0", "express": "^4.17.1", "bluebird": "^3.7.2", "prettier": "^2.2.1", "supertest": "^6.1.3", "pretty-quick": "^3.1.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.2.6_1613573124543_0.6250209791029837", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "express-rate-limit", "version": "5.3.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.3.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "e7b9d3c2e09ece6e0406a869b2ce00d03fe48aea", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.3.0.tgz", "fileCount": 5, "integrity": "sha512-qJhfEgCnmteSeZAeuOKQ2WEIFTX5ajrzE0xS6gCOBCoRQcU+xEzQmgYQQTpzCcqUAAzTEtu4YEih4pnLfvNtew==", "signatures": [{"sig": "MEQCIAW9Cgt6O2Dwx2yaPP52q/q8L5ZXiImGk83c9HDnr1rPAiBIiU7zNU1l0ANR63h2e+4TpcKedFH1qTDxrF+Cwd/qdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg3eQjCRA9TVsSAnZWagAA4W4P/1joy5ljAVxv8GvWLGGE\n+x73ZsytW/eqjBwqnbqbOB18Hdlg4MHOlzEKU3mkwWzK3EXl3v8bm7U6CBRC\nbho4ZEVFJKH7dsV6uzKsgod4Im1WTftWu/cSvrEy0v+ZpWvQAb3Osm0/0zyl\n+e3EuhfDWS0QyJJhWejXSAdtpY+VLs4KNa5pwDbK6KpihPwzpaoweq4z/JJr\n5fg4RnxRPXX0MeYeqoLqWocB755S3zaW6c/GHyIKm9JYvPIGl5rvTpAqKUjr\n4hkfpJCYtsmAP41NFb5zLvo848fiicRrF0gLgeteNYEYN7NToucbNkg73+Yg\nAc3wQxHRuOqtdwrhl1+n+0RHHxD+6jC5xsmsYXb3za+wGWiKKPkxUCcnkslL\nC1duXvrMGt7fIzS22CoGSjr+YJwj1JLxIz+YvN//c/eJSIL3ol3sNvd8YCiT\nC7NyOL80JH8Uy3EBZtSATTOosF1IH1/TTzZfktO/E3aToFbtDsPNe2HWc+DR\nQDCwYHqZ2kB8JntmxzosKzi4HYre3QYAtc/tAhm+1ptPpg9Z5fch3mqTdy3V\nOSi7k4ZXIy8zAQ+5U9IAUV08+gjyKP+/dFlpV9o+CxqO1vD+H3S3lEE7RzpI\ntNRmaUobGgkG+EnycYjGnI5N+uZkHw24nL8R/EzlBSdSMp9U9l+QU2ZGCpUQ\nqtok\r\n=RbtY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/express-rate-limit.js", "gitHead": "c4f9c46ddf6d6f86861007df431d775deb5f379c", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"husky": "^4.3.8", "mocha": "^8.2.1", "sinon": "^9.2.4", "eslint": "^7.19.0", "express": "^4.17.1", "bluebird": "^3.7.2", "prettier": "^2.2.1", "supertest": "^6.1.3", "pretty-quick": "^3.1.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.3.0_1625154594415_0.8609301197694739", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "express-rate-limit", "version": "5.4.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.4.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "2c17427ada8f2a19dba153c917937da849b4473a", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.4.0.tgz", "fileCount": 5, "integrity": "sha512-sT+rk1wvj06+0MpEiij7y3kGdB4hoMyQ+a5zcESUpDMLhbLXoYIQI6JfsvLBz1wOhmfF//ALG/Q59FKMI0x2Eg==", "signatures": [{"sig": "MEQCIEZAUz/+UXCVctM8DSxJJt+jimPXso33FV2Z3OzidA0KAiAIBxB8IbhYxPI4CtACM6y7vg6XtNWZ2TdytDA7qZIfCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21430}, "main": "lib/express-rate-limit.js", "gitHead": "67aa4fcbb65b18a53c5e9b5715c60de60037f2b6", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.22.6", "_hasShrinkwrap": false, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.2", "sinon": "^11.1.2", "eslint": "^7.32.0", "express": "^4.17.1", "bluebird": "^3.7.2", "prettier": "^2.4.1", "supertest": "^6.1.6", "pretty-quick": "^3.1.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.4.0_1633115136891_0.9687065007641276", "host": "s3://npm-registry-packages"}}, "5.4.1": {"name": "express-rate-limit", "version": "5.4.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.4.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "1cbdf895b9ecbf00eb661f993d00a24ce4d26cf8", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.4.1.tgz", "fileCount": 5, "integrity": "sha512-ZQh2h3qiu7wWdvWNYHznBhaOp2ZIXNnT4hl2Ff608STeWtCuJ251NzqQlk7mo5wnO2HmrydBYHuVA9Z3S3ZtXg==", "signatures": [{"sig": "MEUCID7vKOhn/+g3Sb2viHqaoQJLCm6rdN0BZ680CX7ITWefAiEA8rg2KSkbhL6Ng+3JllU9OfhxvY8oHO3aa23vzrqyE4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21611}, "main": "lib/express-rate-limit.js", "gitHead": "67d365f552a1412a1657bbb318027615885f9a01", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.22.6", "_hasShrinkwrap": false, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.2", "sinon": "^11.1.2", "eslint": "^7.32.0", "express": "^4.17.1", "bluebird": "^3.7.2", "prettier": "^2.4.1", "supertest": "^6.1.6", "pretty-quick": "^3.1.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.4.1_1633464732438_0.8106318835507771", "host": "s3://npm-registry-packages"}}, "5.5.0": {"name": "express-rate-limit", "version": "5.5.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.5.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "27dc48b5cc325448df47d02d5f4a2183b723781d", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.5.0.tgz", "fileCount": 5, "integrity": "sha512-/1mrKggjXMxd1/ghPub5N3d36u5VlK8KjbQFQLxYub09BWSSgSXMQbXgFiIW0BYxjM49YCj8bkihONZR2U4+mQ==", "signatures": [{"sig": "MEUCIAm89qanvXeBYLkWLLc7raHDgtr/MyUBFhA8JQBx4nyvAiEAnSByCrJ+d5qAAb9nvoXz1JJNUVWIwelaZs6HNuP+Ayk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21790}, "main": "lib/express-rate-limit.js", "gitHead": "bbeca82dc736a21c5ef51a1f0b6e5ec833c6c71b", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.22.6", "_hasShrinkwrap": false, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.2", "sinon": "^11.1.2", "eslint": "^7.32.0", "express": "^4.17.1", "bluebird": "^3.7.2", "prettier": "^2.4.1", "supertest": "^6.1.6", "pretty-quick": "^3.1.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.5.0_1634068746223_0.6478101269585446", "host": "s3://npm-registry-packages"}}, "5.5.1": {"name": "express-rate-limit", "version": "5.5.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@5.5.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "dist": {"shasum": "110c23f6a65dfa96ab468eda95e71697bc6987a2", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-5.5.1.tgz", "fileCount": 5, "integrity": "sha512-MTjE2eIbHv5DyfuFz4zLYWxpqVhEhkTiwFGuB74Q9CSou2WHO52nlE5y3Zlg6SIsiYUIPj6ifFxnkPz6O3sIUg==", "signatures": [{"sig": "MEYCIQCcCn+CwfD2caB0kwQLG7yEPLYJf3NEpWKFoQrq1CUyzwIhAP9NrHNUn7G9lj6xcTND1Ca8rRqEb8A7TJ6Jw1Fw3cBk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22105}, "main": "lib/express-rate-limit.js", "gitHead": "b9e0e59d1b65621d92827ec50208420936114106", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "pretty-quick --staged"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "_nodeVersion": "12.22.7", "_hasShrinkwrap": false, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.2", "sinon": "^11.1.2", "eslint": "^7.32.0", "express": "^4.17.1", "bluebird": "^3.7.2", "prettier": "^2.4.1", "supertest": "^6.1.6", "pretty-quick": "^3.1.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_5.5.1_1636168614258_0.047891164380319484", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "express-rate-limit", "version": "6.0.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.0.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"import/no-cycle": 0, "import/no-named-as-default-member": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "6afdb010d07d5e0ec2639fde25f742a4c77c7a0a", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.0.0.tgz", "fileCount": 29, "integrity": "sha512-QlBnH0mWYYA6H5voSdpsKWNKtG4LTYOn9PPFkI/p56Xynns4ouGXFa2N6JhkWSfEov8pHUJhDuQFVid2o2T7Rw==", "signatures": [{"sig": "MEUCIQDED3VcMax4u9gibLEI/Ia+UdLx/G9YPVXDPNfBR+KDtgIgQIt5boEDbTIogKNJAqJ0chuTTmOsAcbLS1wjOWMXr7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxgNCCRA9TVsSAnZWagAAvVsQAI29x/BiZBkz7YnOE0Xi\nS6YD2E3jG5BsS4RSglJ4Q5UhFbPY2ObehKSCM7Yntc6LYJtAELaC2q56rbas\n4NZmkVYh2Cdk35bHJXdkI6aZmTLaod9Aw7VdtRkOMEu47gG44N230vz2GJqB\n0tBhW4BrWe3sDTqqKSfyIuH/7CThRJT3WWuoXMDHsz/oIr7WpI/kt+lMZ4mj\n42Um34TdSUHPnqYIMFlS69TPNTdeP6Pf8JoFVPdXpjDrUkxrsUO50PR7obYZ\n/XERklPFDCQmuzzRusCNbqpOAaA41o8QsZkupj7fJX83Jv6uthwBKROLIlsh\nP0mddiR9aAP6cj9DqyCgn8+1EoUhnLDzABEqR95qJ5cvOKZ3j8KPhuB9jLjS\ntmGeUMU+PaftJBc6MYkomk4WmMyX9OQxvgHt9QirsYYudEz1XakwWYRG3xrr\njkRAhhG1PnpQkRP2AVEmmpdMYGgRyIqbnQ3YtPpCktOMR4ohsdp5v8SG0dRr\nqvSn84y6HqeapZswputVe6TTLRqx4EXcuh6ZtGC4SWGOG4t9aY/sFeMdB8O0\nh6jRWGUajVfP1/Vm2G6y4nKRuNYSeq7p0m3g7zJ0dsq5/cKDgfKaES3U+zlN\ndWePHJuD2MBE4uLFet+krNu1vvdMYYAyFMswKPoUCDLjpr+ju/JWf3N1m3m9\nYvc3\r\n=xszq\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "dist/cjs/index.js", "type": "module", "types": "./dist/cjs/index.d.ts", "module": "dist/esm/index.js", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./memory-store": {"import": "./dist/esm/memory-store.js", "require": "./dist/cjs/memory-store.js"}}, "gitHead": "81346ba9af65447673525fa47f28e77b3a7c9b8e", "scripts": {"lint": "xo", "test": "run-s compile lint test-lib", "build": "run-p build:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "xo --fix", "compile": "run-s clean build", "prepare": "npm run compile && husky install config/husky", "test-lib": "cross-env TS_NODE_PROJECT=config/typescript/test.json NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "tsc --project config/typescript/cjs.json", "build:esm": "tsc --project config/typescript/esm.json", "pre-commit": "lint-staged", "view-coverage": "npx serve coverage/lcov-report"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "16.13.1", "typesVersions": {"*": {".": ["./dist/esm/index.d.ts"], "./memory-store": ["./dist/esm/memory-store.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.3", "husky": "^7.0.4", "del-cli": "^4.0.1", "express": "^4.17.1", "ts-jest": "^27.1.1", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/node": "^16.11.17", "lint-staged": "^12.1.2", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.2", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.0.0_1640366913949_0.8738865456676621", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "express-rate-limit", "version": "6.0.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.0.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"import/no-cycle": 0, "import/no-named-as-default-member": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "f6d0b0417dc5dcb7e4d4936f8bdbba20f408860f", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.0.1.tgz", "fileCount": 31, "integrity": "sha512-4J8og2zuaafv9egUfQ3G5+hRZfTtckimd4leYPkEXNn2XOQ/IBJIwDmHrwbd2ZbI6UEX3AlyAKLG2EWiXvgCig==", "signatures": [{"sig": "MEUCIQD7SsZ2PMBe6y29MWzh4geWARLxqaYHC4122iFDvaDwpAIgTKREfGCcyk0scCTRwr/Shb8eLgUd+l0uAKdGPYQAB7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxuxnCRA9TVsSAnZWagAA4TgP/0BJVdfBlYw+o1zEq+9r\n+eRAwPzJaaqUvB7lqe1uK51UBIQvpknANoyQ6hwDn3rUJKO3009UZRuw2/Tp\nsYA64qUX5OTniCX1ArP5a2i4GJAu5bvxSNO2q3MYMn0HhWR5mxSaPFnyk07C\nzi35RT4tmOolBilFDL9dyTUdG9gyZ4bIbQGrA7l6kH1zgDrB7l7BCT63Iq05\nqurLtOQ5EA6VaZ5xatF5LlTHVMzT9ijHWroaXfQTx1JI9PlL6TOQFgeb/K1S\nMB1LkAcfQXGubRw23vFaOASxCdCERyIn6PjMYgY2C26QS5Ev/WkAGHXkKkTy\nvfwL1jm/H+CjmDdXDtPKNCwyUomz7D6gg/HcmoHGDlPkXh2pUdlFbBcJgMbf\nt7cBzY03MuZsIVgll7cmxoJTHrKFUn0CVzYtpescT+4Avx8CBoP90g9ZqZuZ\nVhaAVgXkI5F+SXpoUh7a8jYq5lLOCMODIylUZVVOUsKpF0cMdxQeSF3HgQGU\n8m5Y7g2698ASYun9qw8KVCHCBOlZLG+To8knWIBDpmhQTg6ZcZVmLJsTBJ2L\nAB3UKlL8NfdqYyP0yjpR8uS8ER9KnuFyubFKOE0Rdrcz+50ts7knX2MveME9\nBwVk7gDGTqGQr61XEtn34I0gV+5EWCKCgSmwAQAHfRYsgWibicZo14odjRey\nJwsI\r\n=LgxK\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "type": "module", "types": "./dist/esm/index.d.ts", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "gitHead": "b160f0086d761e4cfc2b613768670e45947e270f", "scripts": {"lint": "xo", "test": "run-s compile lint test-lib", "build": "run-p build:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "xo --fix", "compile": "run-s clean build", "prepare": "npm run compile && husky install config/husky", "test-lib": "cross-env TS_NODE_PROJECT=config/typescript/test.json NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "tsc --project config/typescript/cjs.json && cpy --rename package.json config/node/cjs.json dist/cjs/", "build:esm": "tsc --project config/typescript/esm.json && cpy --rename package.json config/node/esm.json dist/esm/", "pre-commit": "lint-staged", "view-coverage": "npx serve coverage/lcov-report"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.3", "husky": "^7.0.4", "cpy-cli": "^3.1.1", "del-cli": "^4.0.1", "express": "^4.17.1", "ts-jest": "^27.1.1", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/node": "^16.11.17", "lint-staged": "^12.1.2", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.2", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.0.1_1640426599276_0.042164434738028334", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "express-rate-limit", "version": "6.0.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.0.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"import/no-cycle": 0, "import/no-named-as-default-member": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "aff7ad09cbade378351f12af72317b7d08857048", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.0.2.tgz", "fileCount": 8, "integrity": "sha512-LWi3RZTdSXLouwgSZDZU7m8SX2fC+r4jHioZ12qK8blD0Umf6WvPxAn3YEMNCgY7gr8Ek4jcwvHigNe4KuDYjw==", "signatures": [{"sig": "MEUCIHlidSrniCijG44S3V3rOOYBlIAWRQcSbYcsSikfX+2DAiEAlmGx7iZKtAfKzWszKoiIgnb9YKv4F9f83BEZPFLXXvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzVPyCRA9TVsSAnZWagAAIxUP/1PsaUylWralf3/kVEdk\n05wy2bRv9lbuCwDmQcXwlUd7BNmLU2Ue0aVJsDrvXNK6ZGzpPIRtJDeTb15+\nWqueE2ApOWdxpaI6jGc3mubzb7/Bq6+UQAOBDgE0HQrYcFCjbzwjp6Uz4yei\nSNGiwKUa/c6PdSqTKGl3cmPqtr2eBgQWLA7GWqgpwzjgvwY1Z7VqDKkGytTl\nCZdgVmOq2+lLKUfcRyUdiBakZSm4jEoJCLXvQlPmS1nKoRMOBeJS3HlkQJ0n\nz/UESBx6hQ9gd3k55igSmbvO9VXDeU/fxNhTEXYva8O+dtUb3l8oaScQFqXV\niwgfqwRtoZ5hJofho+sxxHYX48eoviKmJq5hXrC/FBY9aWZ7s7yk+C4HQcd8\nzO/OUt8PXGpOABEBewk6hOJeRN0XArkVC4iBDmVowDEdE63E0L2lY+L0vOSh\nbhyx7miVescqlxwODap+FJzDW87k0ztdnHv/YI93SbqHSbvPPQu4B0VG+v5/\nktARcbL9uiRda9IFAobzcS4iXb4kHfTi9h5RatDI5RxdyskItOE6DTzVCoVd\niApjrrGD4md6trLcGQ2qW10z4YV2wvkYqXfzbNE+X2x0F+RUE7Aun8cfr5+d\n52whNCkE1/E8wCjxvI8vvuMgOGfr9luQZhkG2AhgiuyYv8VzHq63dPFwvCpC\nJq5s\r\n=RPCN\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "09444436a201d31841e5e9bbd1fd6a16ca2979db", "scripts": {"lint": "run-s lint:*", "test": "npm pack && run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild source/index.ts --bundle --format=cjs --outfile=dist/index.cjs --footer:js='module.exports = rateLimit;'", "build:esm": "esbuild source/index.ts --bundle --format=esm --outfile=dist/index.mjs", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.3", "husky": "^7.0.4", "del-cli": "^4.0.1", "esbuild": "^0.14.8", "express": "^4.17.1", "ts-jest": "^27.1.1", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/node": "^16.11.17", "lint-staged": "^12.1.2", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.2", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11", "dts-bundle-generator": "^6.2.0"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.0.2_1640846321949_0.017371263257926373", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "express-rate-limit", "version": "6.0.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.0.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"import/no-cycle": 0, "import/no-named-as-default-member": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "8360308ea1e5ccc0e6939b0a69adbf5166e1f93c", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.0.3.tgz", "fileCount": 8, "integrity": "sha512-8uh5ua7doYEP5ISoh5t1uRKvySGIvrzHWz+Ssl0BXS8JEJzMXi8cNHCFr957eWLcmg0jdHdMH4xG53I8E7mUWA==", "signatures": [{"sig": "MEUCIQCnVPYg0yzVxPZdl4KTPhbvyS2V+44U6yDabLwoVLTKdwIgV6/76mmsvaYM+EbOBJSo4uEHMovvEwI9l0dToLjS0Zw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzeW3CRA9TVsSAnZWagAAI/YP/0lrkheGPIAgFo64w5Xg\n7fSLdwqpl5VnGqRlqEK4/xrSoDWVEgC29aTYpP25QnTEX3JFXVLT56cN1ofZ\nLeOYd43kkrgthQAtFk//OvR0Bd+ykeEeQLYI637cTXsMYdLERjxNByuBREQJ\n63gOpXkn/sbYkRuo9HIiJAug1HX7itHPyCFmo4Jk8UIC74sFiNqofxvxa1NL\nexFCa6B6aIYE2+jB/QpvC0GfUUA7L0lTw8c19Xi01yD4tH3S0YNYMENMF0Q0\nOO8FlePcwUgexV18MEbgeRfZSoMINaptuNwKiOX242tce3n7FcJfjMPpGiI2\nEykRT3bToo8aChpAfxmR8rzm1f89gvzYFqp/z3tx0BZ3u3EV4XWVhlzivPAK\nuQD+XaB0eXVxv8BqZKOfgHpiXd6z14+YUjLBO+nGLNrKbEqdte/KAHHyqpow\nSmA77ZMfTVkDJhLFIImFpSyNlWRLySjm+IyZr+aI/LXg8ARcnj7beQm0AGtq\nvRwpG0rzi9pXY4Sz1ndQkS066ZNbI1rz9ltSQOFDCK1HuGbz0ihtje858bHm\nAS9B2soCb9xzhUGOMTakJR59x+aJny7cwC+T7RAjO/KhiB6O7Kaxbp5Tcou7\n/JA01RRLkuK+pMvfTq9Q99RBQ7vMIEEAcuUOufFIRIrQtX6rPoKEUISfGaId\nTI4z\r\n=awyV\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">= 14.5.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "de47e0787383695567bf87a3f65d07e7ea56ad59", "scripts": {"lint": "run-s lint:*", "test": "npm pack && run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit;\" source/index.ts", "build:esm": "esbuild --bundle --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.3", "husky": "^7.0.4", "del-cli": "^4.0.1", "esbuild": "^0.14.8", "express": "^4.17.1", "ts-jest": "^27.1.1", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/node": "^16.11.17", "lint-staged": "^12.1.2", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.2", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11", "dts-bundle-generator": "^6.2.0"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.0.3_1640883639104_0.20485790137287285", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "express-rate-limit", "version": "6.0.4", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.0.4", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "1f3d663e8d640be1c8c5e1ad2fb55a7d7a7b118c", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.0.4.tgz", "fileCount": 8, "integrity": "sha512-TratTfxxTAFb6ZUAxPIigqhcS0e7ql9XDTorjD+SihV5ua5h6agoKyr45iKM6m5OzTppesh9o/RCuvf5eTiwCw==", "signatures": [{"sig": "MEQCIBAfnb/72UuXJZZGo8+jynfaaPZA+gj+VVv/nS9WJQ0OAiBJP2YA7iCJ0L0L0H2NkVztj2QX9YwvgdO64OTLDHO1kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0a4sCRA9TVsSAnZWagAA/kEP/jRrHRspXN2EdCyAr3o6\nx6wTUIlsF7t8pX/KyroV0pFEd8NRTDpscwr+XU53fMwWSw27696SWboHMp5o\nvcwAcBjMmXNPT0I94uwffvhGOXow/dv84crE5leqZ3pvQEwM1jNIY7ZS1X6L\nssKwBAExLastjSD73QnYW+6ZSFksgoMomUvOOBPzOLgV0D2bOHrGsXcKfF9N\nFIxANBs658ZdNHVmzWx3rj9laysZvIscIyXvSCzOKKznygs5y74AvSENGcrL\nbAiuvyWTIKl07SktmX2T/KgEldJ0w5bfL1oV4zAwOCqwerqYys4+p7Kyv4eQ\npO40Grys5FyqmFCEF5mksr7yzLVJL7a09OyDB1+PmPCTrCDM39cSu/pxOVa5\nuhwkaObq1KgqqTuYaVxabUbIQYK3MlT+IluTfo0oXzChmIFbxX1YsyMryXka\nmj2E1T22lwpfeOP9kBZ6Cb+NYNRlfhZvmzsC63R6h4mwEC9Bq0JJUnf26k2S\nXkxVJZU6rfsKg3VhJ0cgKQges+qFKoqAYhdMfiyyvYmuA2eyYFpPAkGFXsxC\n3T+biCJM8dgvgTLHwhvHDic4fj79nkkKUD35U6fuP2eCtgc/qVK0JfXlSBjA\ncFQ/hsy4xKFJnZycbcFhWjcmTm2+qNIXdm0iVBFWju6pV9/PKy5pyAZG8orT\neGVq\r\n=zl/K\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.5.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "2ac0274069fbb7eeba9be246c01372175ed131b8", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "npm pack && cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit;\" source/index.ts", "build:esm": "esbuild --bundle --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.3", "husky": "^7.0.4", "del-cli": "^4.0.1", "esbuild": "^0.14.8", "express": "^4.17.1", "ts-jest": "^27.1.1", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/node": "^16.11.17", "lint-staged": "^12.1.2", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.2", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11", "dts-bundle-generator": "^6.2.0"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.0.4_1641131564516_0.6426592294382067", "host": "s3://npm-registry-packages"}}, "6.0.5": {"name": "express-rate-limit", "version": "6.0.5", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.0.5", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "caee8f6634ed9db7b6bf7d5d7086f90ecd92e6e9", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.0.5.tgz", "fileCount": 8, "integrity": "sha512-EB1mRTrzyyPfEsQZIQFXocd8NKZoDZbEwrtbdgkc20Yed6oYg02Xfjza2HHPI/0orp54BrFeHeT92ICB9ydokw==", "signatures": [{"sig": "MEYCIQDaNuocn0CCODrW1WO/WPT0Q6K4CcR3/g0VcXjYYoapJgIhAM/S27K2EZV9jXwhz7pLgKSq8cbEocDtGTzRyU8zSIDC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1u3wCRA9TVsSAnZWagAAahsQAKI7Qtx0CsyDiKUeDvDH\nzjCWEkWV12GDyeYWyWf9DA6mJ+28aC31hkWj9kbWTHtff9FDdJHWD5Q0+zSn\nkTH4neOkaAg0GeLpbhWhNzrIfAIdZBxNug1Sgkcyl4p4LlwkGMRxpxo54TgK\nYLNpBiFeZOL6pW/8Xyj0aUSYil9sDy6+wRhfOmu61wcTFCzxvH/IHeVaMY6D\nBuCagAzNDfLkZ06clMF+R8Ze/v+KCBoI574mw+P8n4aSD2424x+RIkanou4l\npEJUiiTDkDtWORCU0aPV5L+YzUlyJpQ3i5an8OfI6e6fyhGcLiACU5pn102I\nqUH2ym3OD4E0IbMCqL/eWQIK2CRCjtPQe0Te9PqMZ5iJK5MnPlMEc4X2aQX2\nQJcObFGkUiQXXnnySqRNYLeA8+f1mxQOA08bG9sc6i8TpYLQBUu9wPHzM/5L\niBQkWa9iTvFuANVX3nvuIiYf4Sp5g/LHuC9xykjRu9EH9SpAdXyrQjF84/EH\nuthsOgvA+K3JBkZ2cEKWKm79W7LYQ85m6TLe0WFNxLGjPvIlXzzWfTLfzgp0\nr90eDQLVttSiXR+wFiRIhoH9Ticema8BSlMn93vVY2itiFq0BWAKyORBkYO1\nJK/TJhWU5SH5RA4PrJVdmjl/l2NRJ02QK5pJDn2hfqkdairDs9jqZLffN3/m\n1I+A\r\n=F4/J\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.5.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "2fdc2ffd9477d8c9320e490bdf1fafa9dbd2313a", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "npm pack && cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit;\" source/index.ts", "build:esm": "esbuild --bundle --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.7", "husky": "^7.0.4", "del-cli": "^4.0.1", "esbuild": "^0.14.10", "express": "^4.17.1", "ts-jest": "^27.1.1", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.4.0", "@types/node": "^16.11.17", "lint-staged": "^12.1.5", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.6", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11", "dts-bundle-generator": "^6.3.0"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.0.5_1641475568279_0.21217399761129063", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "express-rate-limit", "version": "6.1.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.1.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "aa6fd49cb4f53337420b83ea826d47dac613d121", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.1.0.tgz", "fileCount": 8, "integrity": "sha512-OWyJUDYVq/hRxGU3ufTnXDer5bRBwFiq5D35ZSZ9B2EHdjulWO4bwrbg+iIrapodDZse/35obeOj7igRHuP3Zw==", "signatures": [{"sig": "MEYCIQDpcDibOHBHZ+HRix4hh3w081+P06fypPjH7o0jE3EO6gIhAKrRah9Mg/eya0phdLUW67HjyawFQjYcCsMGqLOsb0KL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3ld/CRA9TVsSAnZWagAAyfIP/RGL3vbuN1wIRgl7gCyV\ncKJtb3B9sLYO+Or1EP6N+cAOEzXL3ZTp4sOq7zbqfiQJqLdHrl2M05nER0aZ\nhtWVLhYjaybaAoX4r8Ra+v6tnQLDf/E9gVOSdIOmvICqIlaH5RS9L3LECJ1q\nzK9xwBrOCG2kHjUREEtTr/UsX3J1lmIwsW5waBuYYKv8au/c3+XjfT+D2AE2\n8+mW9wK2kc8aZMadFd9B/XSeXw6E66nqudDkqvCtHZHQ+sgWTWz0IRETnF1y\n9+zNOmczyzLY+YK1q7rnpzbTkSuKfuMRc5fgPZOnGT2t2PLL4b5gVPgK3++H\nUagvQrzkDuAvA7cgVCcfKFeev8DzCO8/fjqgQ1FYABdgeIGxPikm8H10OK7z\nLRz51OeCEkZi2ifvzPBXI5l+/5IfNovVcR9sLANzhDg3MVPR5Eq00OMrFDbY\nY+HPAzp4k7rgSNnJe+z3T6pyy3q7N36u9i0F5fUQ1EMuJYu94y7NyhOwZjym\ny4OyjbnY7RdWbB4lxNHQK6B1/hEeBto2na4jWEJLr2AokTi3ytaqqPb+X+XP\nF14VOLL7iSfnVIQqSs2/lAmHUIVISoDFpymTkDfXdQWllLha8yM3+Leil2v5\n8mkcUNtKyCLV5I/3jqZJIvHkz1LJaW0jqQfVdwoAIzAcXbZTHkMvhFJltFHn\nHk69\r\n=UTEY\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.5.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "7adc75624de8d1b182a4bc78eeaba508497588b4", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit;\" source/index.ts", "build:esm": "esbuild --bundle --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.7", "husky": "^7.0.4", "del-cli": "^4.0.1", "esbuild": "^0.14.11", "express": "^4.17.1", "ts-jest": "^27.1.1", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.2.1", "typescript": "^4.5.2", "@types/jest": "^27.4.0", "@types/node": "^16.11.19", "lint-staged": "^12.1.7", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.6", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11", "dts-bundle-generator": "^6.4.0"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.1.0_1641961343366_0.11753132533141941", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "express-rate-limit", "version": "6.2.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.2.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "be644be082d4d5da8c368aed07cef80152f6e218", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.2.0.tgz", "fileCount": 8, "integrity": "sha512-q9xfttbPX79HiBsHA4LT3PZEeJR96CJ5/2jloAKSEECMx8XlOOOpjxx6iK/kBw3hFJ8uhx6Q9lCfSGp70yV0tQ==", "signatures": [{"sig": "MEQCIDIW+iIvfoNACHDoghG+Ik6He+6gMB7oR9U/7VoXTJpOAiAmj4DkO6yd0lOMyDbd/OuoHVM2rCHlc0jQzfa7JJOvoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7AtlCRA9TVsSAnZWagAAWVAP/15cyOiOqQr/lpgaKo97\nON67p1ikr6+FOLvW7B2+oTm5tSlCkuaBu6AwXFHAj7fQz22A+37e+x3TSK0S\nq9LQAgDOcFKcZlX3siu1v8KdwST4yYltQ7b7cq7ni/po2jpcpzc3d9tLBNXR\ng7G8JXJbuafbA7GWun++B8MTWu7covk2Z5hayzWmMvOV9g9FMjZHxnjKoRQA\nCswn4nQxQe9xWG4CRyiiW6gWHIMjS5llAeqsHpdciu7uWc7j1wIIzX9/dgYN\ng9vWpxz4JB4LKfw4TCpHP96+t53Jt3rjnJ8xoOrT+yBgS7jlQ/wsXO0vNgHB\njdht4NUb8R0ctExU6rjI10ms9nRT1wH9Pdl2vBYssM+jnJVsC6TtuPaVgOOa\nPAaf18QbIlHe/JzdDTsUTkUMlGvwhK7hSNiGcTIIshoSB7UmjKgS6QVHZbjO\nl33F5Dw18mKYM0BxeQmTvFQzkQ8UVKazbObLGYo7i/+ZHdE6O3X01uVzfoJd\n1qcHdD3BqxigxTVYaN45vSU//JeG3j+Mzmpq4hgFzql/nCWIdC/fjUr1ItR4\ngUniOgus2rCmgJPkLe2rX29XhovPsm6HX1uGyrjqGf8pmcO95yWg1UUP89XU\nRZZlxvTFGiBHIZjqFoyE1M3OUa3UbAwHEmF6KTLL4SUJMFGXFFFUM5oRUBk3\n2uAK\r\n=GCXV\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.5.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "a8dc1f7b38b47b219a6578b455129fc180634ba9", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.7", "husky": "^7.0.4", "del-cli": "^4.0.1", "esbuild": "^0.14.12", "express": "^4.17.1", "ts-jest": "^27.1.3", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.2.2", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/node": "^16.11.21", "lint-staged": "^12.2.2", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.6", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11", "dts-bundle-generator": "^6.4.0"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.2.0_1642859365280_0.1602881872965891", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "express-rate-limit", "version": "6.2.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.2.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "4a7619634fb24417ae723ad2ac3707b38e2e1c64", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.2.1.tgz", "fileCount": 8, "integrity": "sha512-22ovnpEiKR5iAMXDOQ7A6aOvb078JLvoHGlyrrWBl3PeJ34coyakaviPelj4Nc8d+yDoVIWYmaUNP5aYT4ICDQ==", "signatures": [{"sig": "MEUCIQD0v2/VmpG5BMFKlpi5ksvOnuHvryjf4e6gN51tsm5pqQIgWqYqs0ARYnk0T7FcGm6ITjv+qeSQcynQnuy148KBw9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBSyECRA9TVsSAnZWagAAAhsP/ibWe9XrDc1orgYpshc+\nb7c7TXWvTELBOhZpf1Na6xPiLtC4zsNnCh5h7NNVpErUetT0VBXwVEKFbX0+\nAzBEi38tVRDLm+olPtbAsyaCzDTxlAAk2Z3Y0ZBbr0zo7ywA6AIWA+AhMod4\npwvqvKspByZYjnMT1vfOBs3QzuDz18T1JUMbkxowB/uRdJfDRmpNDrC3lVCd\nexonwT4u7PvBaOvcuUkIKljec6VC1AsqThP2zTUiordcAgYHXxO71Gra9Qn4\nyPWSDtcOWt974QbnA9N2kjxOj0q/QABtBBFxgH0F0bIau9EEezcZfo4cMcIy\nd8ifOamdLgXpM+YEBdUgX4O+lt/BHJgO9NDkbnzU9HSVz3+b+vMQwOfN9rvG\nVzTqx53GGfq/3KQbxrMVTU8fXaFB9GTwMNiAUGv2Cv07p8v/MXeXN3oH0oiY\nJJYW2u+GXlztWFRoVLyM9aK/u6Jd47AZQA0e0O2KnERGRK/QjmFBiOvL4DLS\nD5mJ12k28DOJEtYAMpG+b7NFO8e3C/Xph8ajV44W/9tFDBaTdZ3PXKD6Mqyn\nubwSqWT9/JqtkNNNbLJYoWAKToRIJ1eF+QR4hCmoPrjVWPtpe2EnDApYAV3M\nQFaUv/F1ruiEW33J15IwUjfpsHTJ/nH5eRZTp0si400mVqgBTPaXqIAtOk/3\nIY1o\r\n=LwS5\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.5.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "49294c8b72c4e46756d554f594eccfd1b1a7811a", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.7", "husky": "^7.0.4", "del-cli": "^4.0.1", "esbuild": "^0.14.12", "express": "^4.17.1", "ts-jest": "^27.1.3", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.2.2", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/node": "^16.11.21", "lint-staged": "^12.2.2", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.6", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11", "dts-bundle-generator": "^6.4.0"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.2.1_1644506243889_0.7651811426577069", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "express-rate-limit", "version": "6.3.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.3.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "253387ce4d36c9c2cc77c7c676068deb36cc0821", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.3.0.tgz", "fileCount": 8, "integrity": "sha512-932Io1VGKjM3ppi7xW9sb1J5nVkEJSUiOtHw2oE+JyHks1e+AXuOBSXbJKM0mcXwEnW1TibJibQ455Ow1YFjfg==", "signatures": [{"sig": "MEYCIQCvBasbKCzTHDCBChzzHqhLPU8olIdHN82PSkLuYLEueQIhAMZ37WV35537QTN+3AxVG9Ihqc+5p62GyFs+UKatcMZJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiEOGVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUbBAAoAMWJ9NLlsd27kJ3p7FCa2B7cfTNMp5cck0mNr9AlKhlSDMH\r\nkxPBTigPpW52j9yEW0krZysm/a4FA8tA3JY0q/CSrJF2/4ef6Ho07F3jqZTW\r\nhapE8saFdfusi0n+bzaL90+ubcivBZGhjAL74cvyCpQXMRCkzFPz9EKYnooC\r\njLxUU/ZFSOGY+sZDU4DpeHAp01KB254PGkXiB918UK95P+wOhP+lRp7kpJrb\r\nyCkjN/ixhO/uROHPAMN5Kbq/VpDOwmwgK3oF4o0onAK+sEEbjz+nqJ2vYRRS\r\nPYdGM3TD2rfmMAIG58CRk9vTb7LzVGffzGDoMqKTYuKUA8Gn8+khSAzp1asi\r\na8Y6jtOez78i29dmI07l3l0/thcETFvY8LKWqI1y0rtLR0v14Yzlo+DkmAZy\r\nRxdLm39Z/440eTDwhWSB7iuIJNFMybiws/qx5r2aq7FE74Edc3CJJZ7Ca9UK\r\n37+Rk8Rmu2PXYYjhqXHToH8Ko6gszfvzgcJ00P999SvsJfHR626rG9Avhx6a\r\nWwoIoCx/dvjhxPyxapWSFckfYvDxGgDdvI2c4DzZ2bdZxf/2VSF2MPw3uNbd\r\n+rNVpZiuB6xO6i3HJXPi/nkIG/Ec11vp0mrc8jX1prvXw04F76ki5DejzNKu\r\nfaoB72DUVooqy8/r6nsVhRA58H0fee9pesw=\r\n=tCck\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "435e1e404b28bd6f9e10edda5137e65009f685e9", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.47.0", "jest": "^27.4.7", "husky": "^7.0.4", "del-cli": "^4.0.1", "esbuild": "^0.14.12", "express": "^4.17.1", "ts-jest": "^27.1.3", "ts-node": "^10.4.0", "cross-env": "^7.0.3", "supertest": "^6.2.2", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/node": "^16.11.21", "lint-staged": "^12.2.2", "npm-run-all": "^4.1.5", "@jest/globals": "^27.4.6", "@types/express": "^4.17.13", "@types/supertest": "^2.0.11", "dts-bundle-generator": "^6.4.0"}, "peerDependencies": {"express": "^4"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.3.0_1645273493437_0.8100994785553923", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "express-rate-limit", "version": "6.4.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.4.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "b7066afe21157a012ed2b7c9adde386e712485cd", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.4.0.tgz", "fileCount": 8, "integrity": "sha512-lxQRZI4gi3qAWTf0/Uqsyugsz57h8bd7QyllXBgJvd6DJKokzW7C5DTaNvwzvAQzwHGFaItybfYGhC8gpu0V2A==", "signatures": [{"sig": "MEYCIQDsYoqtlYV/q7VvFEVVo5ZyH3r812ARVbMgDmWbHl+eeAIhALybqfDmsTRxIZRzZcmD+m02F6FHLeru6fsFMKofbpQ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZXLcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquMQ//eIzvPEKkubI1EOgvqK/404odVynLBQDktbU9xMuemAXqQN+I\r\nCH7zSSLdOglgyFWAoPMjxPr3MvthqcXiS6c1C5x9Opquc0Ir7f3LkLBS391J\r\nAyRZKBlY9fzHqona/s2zHYrKLFbZx0rp4UxcKl3LtAzUseMzQCccx0x5kbnM\r\nvnWSaLfNmF8CDs8HM01XSNiPTSuOtkMsucQp2e1foqt218UfeUKA1TupCnPd\r\n91zodWYUreTq43XCaxCWjHiepsbv5EFjNeAWhXFDGYyD+14OlrJEnjSIx8j+\r\nnSge/jgMxcifvluNijYno9HxfCYAQGnqTRV1ftNOrXR0W88yqtlAb7XLuSMZ\r\nGh3+2x97xORqGQbAU+7eicF/+NPDVdHxstPGXyfgFhOnxqYXkw9eDADGRVBF\r\ngujkPsVHC8oixxBAq5N2ACUqWq+G02L++gguwjQzNy4wesiySJFE/cBZjZJQ\r\nH2n6uWTyEweIg6mxyzayJBphCqYEvQOLmIzEhVOZaG291t6X7VW3GLQlBlfn\r\nq8YE/MEyPLDCAtTnY12UByOvPuTWzMIdXFPPazBuXLvYCh2uWtfr2aXGuQCD\r\ncYiWfJhZmWnGUmm1oFWFkpi7yTp/y/5ORu/KDhDaici+REgpxXYz6zzNHOVt\r\nux8GrBnpfXcJnx/STYBOfsuf2BUhpJ7CtoM=\r\n=UFtb\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "e7820d21335a149d638bdda913fb3ec035a84a96", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "18.0.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.48.0", "jest": "27.5.1", "husky": "7.0.4", "del-cli": "4.0.1", "esbuild": "0.14.38", "express": "4.17.3", "ts-jest": "27.1.4", "ts-node": "10.7.0", "cross-env": "7.0.3", "supertest": "6.2.2", "typescript": "4.6.3", "@types/jest": "27.4.1", "@types/node": "16.11.27", "lint-staged": "12.4.0", "npm-run-all": "4.1.5", "@jest/globals": "27.4.6", "@types/express": "4.17.13", "@types/supertest": "2.0.12", "dts-bundle-generator": "6.8.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.4.0_1650815708544_0.003080821636361364", "host": "s3://npm-registry-packages"}}, "6.5.1": {"name": "express-rate-limit", "version": "6.5.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.5.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "2b4c329f03265f94f19613519b169afbd018e783", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.5.1.tgz", "fileCount": 8, "integrity": "sha512-pxO6ioBLd3i8IHL+RmJtL4noYzte5fugoMdaDabtU4hcg53+x0QkTwfPtM7vWD0YUaXQgNj9NRdzmps+CHEHlA==", "signatures": [{"sig": "MEQCIHlHOC4oxCWxw45fMRELjUA+cm4z5fRR418Zc8dJaP3iAiBwc7UtxmNN6TFHAb31AkRjbIPlT23qCyRWCg1jL0EIVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3Cb/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYMA/9FCeXbaVwSjwHyhO9rG8OdK928a64Yi9A/FAEgLbXpmmUcmsp\r\noiP2YX8wscSZOrKiIvfWC+cDdZRCXmIFR8IxSKn0Z565oJBMw59GVx+Bb7u+\r\nQcoH5tlctWohWGOGzoUFMFum0mNtkr1usPX8pkAaWBP/AxzwvjcAE+ySvWFD\r\n5DX1lbvFY3vThA8JHCpQyV8DzySAUUNMMzqfHi7lz74a4TZNG/nYpCewZaGE\r\nXtOtVpO0MpLBOnBJLFOuZWYWWBCsgdAZDoP3WeDDlU1AKEoTki6TEkooqUku\r\nyfeBscIlydYsopRr+ONFqdU3fsjw56VngMTgV3i0JX5hPRJwUdgpRwZjire9\r\n0D8E12Q6XinWCEWTuPRyY6SDJQpRoPcgi7arO/HZGjZj9LlTq2cpfOuLnNN7\r\nfyQ6Y9e8pNd1UghRZk/L48trcmlc5aNve106MJZIHRbvSflf8X7ACGGQMN+l\r\n54SyjciB/BRBthBCZsj4MrlVj3vUb5BGkJXGWhpVBiG4CgScgpQwjNyKWh8h\r\n2CK4PPGHP5CSQR+PnjIgHj0EzKHwKz81qEalkRmF4DVSEOSbhHHqslMaFvyt\r\nCQ4XA3rFsyvwQ14hMD/bm7t4Tv/5eherNe0ZJNBFzGfpXXTJgvwTeVQn6r0r\r\nWjNH3sAGi6jLmiF1YL07+KJks4dQMufLjU0=\r\n=Bnbz\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "12deb5cc3d77710b4bc7fe36bf4ac439cb5d43da", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "18.6.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.49.0", "jest": "28.1.3", "husky": "8.0.1", "del-cli": "4.0.1", "esbuild": "0.14.49", "express": "4.18.1", "ts-jest": "28.0.7", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.2.4", "typescript": "4.7.4", "@types/jest": "28.1.6", "@types/node": "18.0.6", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "@jest/globals": "28.1.3", "@types/express": "4.17.13", "@types/supertest": "2.0.12", "dts-bundle-generator": "6.12.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.5.1_1658595071150_0.6492614207031631", "host": "s3://npm-registry-packages"}}, "6.5.2": {"name": "express-rate-limit", "version": "6.5.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.5.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "5d2322e680ed43ae303b775fa7e19496c9014b58", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.5.2.tgz", "fileCount": 8, "integrity": "sha512-N0cG/5ccbXfNC+FxRu7ujm2HjKkygF2PL7KLAf/hct9uqKB5QkZVizb/hEst6tUBXnfhblYWgOorN2eY+Saerw==", "signatures": [{"sig": "MEUCIAoHwtJpUveIYw7OFSDLYPZzifQ1+QrTMCoQEMfA9yufAiEA4S+/SOmra8Adtkex092zyvFCG8e9/px2VO0Jz3yZx3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBn27ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoy5A//XqwyEe0nsqvNpf7wFDbW6gMIkgcpHXVB2Yyn7WPNKq42F8VL\r\nE3jtm86+9aYtxH2StCw0Cbwu4nSmYflSWPsv0vH3otKpu+1B0zFTnMTM8keJ\r\nMtQBHGqJi7Gy/yjOQlIzKvsLUB190S2aUGJpN7UDbTO/2EFP0A7DiRLdd+Xe\r\nUiwHFvRFzpMlVlTiX0nP5vCeTW6qpS/qPeGAsCl/y50xuiA5LY7BK1FmdB70\r\ng9nWvWrmVjg9LNVcmzO7kZEjEgpx86iOB6o4SzpJiWeEDDlOFd9XJ628PQXD\r\n+5zyhOOAI3roov5Q69RjIlI3ymM2dGlxKbMlxHo6kVAU8b4+mRDkSBh4YFhS\r\nlXUvR/qUNnjKHXRpW+K8G0zwSuX6YOEADcMz0dPzaSrLu7/3TV1e+r6wxqtO\r\n9UsoN3mIRtzEK9d3/SVgFfEuWvBTwXZG2pPAUseiW3TeVz1aO/NJ75tw4R0u\r\npNCm4ulAVO/1WjrXFvxC4tfmaMsvFE+2BlUoaIva4EvmjqjN4sqE7tnOAZ2s\r\nWZIi6RqIr74B8sseSH9zQ4HQDKjO7fQHWTIQ7WN42gZE56wW2pqdutX1COYb\r\nlUuCgGGQfHTu/RX0gWcbUB0ONImsnLLQsa4TViACSdI/NYEq+EJK/H/qwWjC\r\nQbQoiThO3oKaCvg1tFiDczhAe6Guie5FyY0=\r\n=Ulyn\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "c2e2082ae2a9abe37967a006e7b871f5df088d17", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.49.0", "jest": "28.1.3", "husky": "8.0.1", "del-cli": "4.0.1", "esbuild": "0.14.49", "express": "4.18.1", "ts-jest": "28.0.7", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.2.4", "typescript": "4.7.4", "@types/jest": "28.1.6", "@types/node": "18.0.6", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "@jest/globals": "28.1.3", "@types/express": "4.17.13", "@types/supertest": "2.0.12", "dts-bundle-generator": "6.12.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.5.2_1661369787288_0.8357823259493307", "host": "s3://npm-registry-packages"}}, "6.6.0": {"name": "express-rate-limit", "version": "6.6.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.6.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/nfriedly/express-rate-limit", "bugs": {"url": "https://github.com/nfriedly/express-rate-limit/issues"}, "xo": {"rules": {"@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "3bbc2546540d327b1b0bfa9ab5f1b2c49075af98", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.6.0.tgz", "fileCount": 8, "integrity": "sha512-HFN2+4ZGdkQOS8Qli4z6knmJFnw6lZed67o6b7RGplWeb1Z0s8VXaj3dUgPIdm9hrhZXTRpCTHXA0/2Eqex0vA==", "signatures": [{"sig": "MEQCIH7fyvO6hwv/OJoYdqAUg2wnu+4SeQ+lfOoqXxgq1+0OAiB9aBaL7f2IBOxaZE8jcJ38fh7v7wD0TFN5C6zKg5eIsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFP5EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpiA//Zhc3R+ge3WUtqkiekaYAWeUiRrZ95sObQrPn7wgu0ctjxBJR\r\nh5U/kpS1dPTtGLLIb0jxtJT03OF94JartFsdQNaYg0Mnf+pXah/tWJGXqtyd\r\nVE0G8Qw68UKSAR0pRGxJTm58chphuje3pkEv37Fi5I973/uSdNNaQxMe9jmg\r\nwpSSHuSIzrXseJUTBNUfuF3hST/Nqq3Crd6+uYxltrpGARhsds6rhpr+LmXt\r\ntTt/dcUHLLTPcLrdPJN3xtoFyBxXvVLF1M6vm+x3IKQYd++prI8WAPfnOUcC\r\nMK2Hgujj2XyDXVkLP42lyK0J9XLiTvLCWOWACuz/ZO2kSMx0af83LKFOXZcy\r\nJDtgt6xHAw5g0XUciNZz8xgb8xyFJ8NrxWfJoMAl07vmy7tnPPUqEAA5EBHs\r\n4bvu7LAsxfHzu0xXsJsZNf952kg1ZSjhm9DfsLzoVOYI6L4wt139G1XGP1k6\r\nMpKo1W3g5baKac61mXto8AKLH71FLKFsqWI/W+JKEzysSrRRZHPqvYkf4CfR\r\ndYOi4GQpT85qs37jQ7Ugb6MJWD2aQXc2acxzwEaGGgkcdgOGu8xV+5rfAmkW\r\nMzcxT6Ysh41vYG0HYsowDUGZNtABnHUQmWt+2iHG9SAk8Y8nvy2VZW5PCZ+5\r\nL0QDeqP17goEuRSH0qfNbKby/fnNX1O+5N0=\r\n=4WbZ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "a567fda35e2335a38ce7a83838ca26f39811e89b", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/nfriedly/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.49.0", "jest": "28.1.3", "husky": "8.0.1", "del-cli": "4.0.1", "esbuild": "0.14.49", "express": "4.18.1", "ts-jest": "28.0.7", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.2.4", "typescript": "4.7.4", "@types/jest": "28.1.6", "@types/node": "18.0.6", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "@jest/globals": "28.1.3", "@types/express": "4.17.13", "@types/supertest": "2.0.12", "dts-bundle-generator": "6.12.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.6.0_1662320196421_0.06725458585811928", "host": "s3://npm-registry-packages"}}, "6.7.0": {"name": "express-rate-limit", "version": "6.7.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.7.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "6aa8a1bd63dfe79702267b3af1161a93afc1d3c2", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.7.0.tgz", "fileCount": 8, "integrity": "sha512-vhwIdRoqcYB/72TK3tRZI+0ttS8Ytrk24GfmsxDXK9o9IhHNO5bXRiXQSExPQ4GbaE5tvIS7j1SGrxsuWs+sGA==", "signatures": [{"sig": "MEQCIHRaFV3PmjbtvNdQbhqz+NtE7RMMbR2zbD+DXHetyuzKAiAmfNeDUJdsLHJIuBezcrBQEYgQKMaqjZ8G31AlzPLEvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc+HpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpldw/8CmX//CL5vD+vLEz2qGjD7pYx9IJ2z/5DPmlm7jTetOYYxo6g\r\nIKpTbP9raoUYI3DqkWM03j+iXm5rK9eQBbf62Q+b/14krN7+bRbPf0il/uw8\r\nPI0Jfozl5Pj50Rto6xpSPYA5wZ1A4MQOYRcyoBJFUMGGK8zsctkzGn9zVLFa\r\n7w5A90j6bzrD+ygShc7npS+whzoUM+v4xLCLnW+VlmiDABoZvmu846oLn6uV\r\nDa04Z/iqqwZ8+YUYPcQugPQSls+cyC1ioLylae4eG9zgP1uKzZf97eGOhmYB\r\nfWpiHHjwrKEw8o7p5qhsB71IiU1kw9ImLjPR44cHMS948c7Rs1oTYKtzQLIC\r\nH0tIdv0UF3sKx27HBoveWXwl3CNSyK3H8C3Dm/S8z7bzAl+LBSBBs9Z6HASm\r\n1Q7Imk91zElCR39GLuxFYI3gKFUEdH2EYeqmrgqa3uHBqk0ptz1Z6gneFw7L\r\nzuLAaja8FqElcV363N+JOkxRZNX6pBFStjY2YRwb9MP2OueC9SY52TtdrYaR\r\nKqhEIX6qz/KO+uIA64Cd/wCQnMfO2hFOE1KviJFiiBAVVx/EkUQPjC21og89\r\nS/B76uYdMb98vSE16aVYzEI8c10F7fo4haO2IxHVdBC4VzLTgkI6+FFz22Wr\r\nNsEhpu3I+tWCH8uf4S5Tw2F4XTgsUzOyNek=\r\n=gXMK\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 12.9.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "gitHead": "8fdcbf01843e54dbcf971439623fb314b7391ccc", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts", "autofix:code": "xo --ignore test/external/ --fix", "autofix:rest": "prettier --ignore-path .gitignore --ignore-unknown --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "18.12.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.49.0", "jest": "29.3.1", "husky": "8.0.2", "del-cli": "5.0.0", "esbuild": "0.15.14", "express": "4.18.2", "ts-jest": "29.0.3", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.1", "typescript": "4.8.4", "@types/jest": "29.2.3", "@types/node": "18.11.9", "lint-staged": "13.0.3", "npm-run-all": "4.1.5", "@jest/globals": "29.3.1", "@types/express": "4.17.14", "@types/supertest": "2.0.12", "dts-bundle-generator": "7.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.7.0_1668538857182_0.7073476153755591", "host": "s3://npm-registry-packages"}}, "6.7.1": {"name": "express-rate-limit", "version": "6.7.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.7.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "ca93de8eaa25878b539d478948fc94592742e526", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.7.1.tgz", "fileCount": 10, "integrity": "sha512-eH4VgI64Nowd2vC5Xylx0lLYovWIp2gRFtTklWDbhSDydGAPQUjvr1B7aQ2/ZADrAi6bJ51qSizKIXWAZ1WCQw==", "signatures": [{"sig": "MEUCIQDGThlo2e5uRBHkbxyTxdmc9RJ9FrSsxlIK2BIHLhjwAQIgUD0txkBl+pDfTENMo5bp2IhV4d3TqH3BOYzQFcu4+Lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82869}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "6ee8113b559c4e36c9ed4f0d3e02d7cd49b3bf78", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "autofix:code": "run-s lint:code --fix", "autofix:rest": "run-s lint:rest --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "20.3.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.1", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "5.1.6", "@types/jest": "29.5.2", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.1", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "8.0.1"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.7.1_1688679066155_0.5943004784931363", "host": "s3://npm-registry-packages"}}, "6.8.0": {"name": "express-rate-limit", "version": "6.8.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.8.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0}}]}, "dist": {"shasum": "4f1f8238ba209caee2a43be921813f1b70e29beb", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.8.0.tgz", "fileCount": 10, "integrity": "sha512-yVeDWczkh8qgo9INJB1tT4j7LFu+n6ei/oqSMsqpsUIGYjTM+gk+Q3wv19TMUdo8chvus8XohAuOhG7RYRM9ZQ==", "signatures": [{"sig": "MEQCIC98sJa0Zl5unAXtRMXnK+kIA+EonKOCXwRGFWmGHx36AiB8eZHxou8Rp4QIgai21bZlbv30ym4dP5faXLTshdHNMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91226}, "jest": {"preset": "ts-jest/presets/default-esm", "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "3b8ccf782087cea0fed070135b77f187bd28f282", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_NO_WARNINGS=1 NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --platform=node --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "autofix:code": "npm run lint:code -- --fix", "autofix:rest": "npm run lint:rest -- --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "20.4.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.1", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "5.1.6", "@types/jest": "29.5.2", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.1", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "8.0.1"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.8.0_1689899962175_0.9251967145363227", "host": "s3://npm-registry-packages"}}, "6.7.2": {"name": "express-rate-limit", "version": "6.7.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.7.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true}, "dist": {"shasum": "4bbc7edc5846e2d7a7a9f160b02d26ac80ea88f3", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.7.2.tgz", "fileCount": 10, "integrity": "sha512-qbwVV6EsD2pc9sHNaEKSd0geE8Qfn0/Kn3RLQR5Y3BQ72tm7sbnD2MwHyOsBK9ky5ELsWn6y0kI0glIMosYtmQ==", "signatures": [{"sig": "MEYCIQCLrkZDLgrqLixP3DcOUK+xS999ubLxlsLmIZeX/IW4lAIhAJK35r8fZqVg7XEzHqYOj9tGlc0byiQCO+W403GYp5in", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82869}, "jest": {"preset": "ts-jest/presets/default-esm", "globals": {"ts-jest": {"useESM": true}}, "verbose": true, "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "8780cd5179f17430d2d8c569ea8aca672037f4b8", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "autofix:code": "run-s lint:code --fix", "autofix:rest": "run-s lint:rest --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "20.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.1", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "4.9.5", "@types/jest": "29.5.2", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.1", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "7.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.7.2_1690462712556_0.18600386815700176", "host": "s3://npm-registry-packages"}}, "6.8.1": {"name": "express-rate-limit", "version": "6.8.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.8.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0}}]}, "dist": {"shasum": "f614bc3a7040fe6f75dc3198536f4cc13526f989", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.8.1.tgz", "fileCount": 10, "integrity": "sha512-xJyudsE60CsDShK74Ni1MxsldYaIoivmG3ieK2tAckMsYCBewEuGalss6p/jHmFFnqM9xd5ojE0W2VlanxcOKg==", "signatures": [{"sig": "MEUCIQDN7YffNRx0MN3XfVCXz5evrDehXSkqC/QLtRVFc0Ro/gIgSHRF9oSfVKQKeRHhBn1K+jdbIco0VMVVkv06piZQfOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91942}, "jest": {"preset": "ts-jest/presets/default-esm", "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "30c3187bdf6bb3446bc34f31f3b4a18efb2071af", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:*", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "autofix": "run-s autofix:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_NO_WARNINGS=1 NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --platform=node --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "autofix:code": "npm run lint:code -- --fix", "autofix:rest": "npm run lint:rest -- --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "20.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.1", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "4.9.5", "@types/jest": "29.5.2", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.1", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "7.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.8.1_1690463318556_0.6665481779165985", "host": "s3://npm-registry-packages"}}, "6.9.0": {"name": "express-rate-limit", "version": "6.9.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.9.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0}}]}, "dist": {"shasum": "afecb23936d9cd1d133a3c20056708b9955cad0f", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.9.0.tgz", "fileCount": 10, "integrity": "sha512-AnISR3V8qy4gpKM62/TzYdoFO9NV84fBx0POXzTryHU/qGUJBWuVGd+JhbvtVmKBv37t8/afmqdnv16xWoQxag==", "signatures": [{"sig": "MEYCIQDHy1jhC67eUxuRriN417j2TGi5B8SrErKhRNSEqbKnEQIhALO/1FFEbOm6hRz5m4JzqpsgX6BSPGgUQTKxJ/aWRbc+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98282}, "jest": {"preset": "ts-jest/presets/default-esm", "testMatch": ["**/test/library/**/*-test.[jt]s?(x)"], "testTimeout": 30000, "collectCoverage": true, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "collectCoverageFrom": ["source/**/*.ts"], "moduleFileExtensions": ["js", "jsx", "json", "ts", "tsx"]}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "242e224cee92b69c709231c3e9b9eea149951d8d", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_NO_WARNINGS=1 NODE_OPTIONS=--experimental-vm-modules jest", "build:cjs": "esbuild --platform=node --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "npm run lint:code -- --fix", "format:rest": "npm run lint:rest -- --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": true, "proseWrap": "always", "singleQuote": true, "trailingComma": "all", "bracketSpacing": true}, "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "20.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.1", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "4.9.5", "@types/jest": "29.5.2", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.1", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "7.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.9.0_1691292945499_0.6373058206696085", "host": "s3://npm-registry-packages"}}, "6.10.0": {"name": "express-rate-limit", "version": "6.10.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.10.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "e881fcfb1d2a5b364e4850bacb333d537b05e414", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.10.0.tgz", "fileCount": 10, "integrity": "sha512-CtGn2IyklQnIWpA4pcRaovXkNR8psDQ9Fa0y5u7Yhz5TL74dNsm7oXpfm1HPKUYiNe5w0TPEyNbIrvNVU/xUIg==", "signatures": [{"sig": "MEYCIQDi7GY3KC1cexUVgmOlaN0M7mR6hVxeECjHgTqUKxKJjwIhALL+wHVOlY9Hr78Gnaw/XsvMKPJSn6wVGAAqFdbJmp6M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109611}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "57ee25cb66bf1838898641f5641b496e9e7efa04", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_NO_WARNINGS=1 NODE_OPTIONS=--experimental-vm-modules jest --config config/jest.json", "build:cjs": "esbuild --platform=node --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo --ignore test/external/", "lint:rest": "prettier --ignore-path .gitignore --ignore-unknown --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "npm run lint:code -- --fix", "format:rest": "npm run lint:rest -- --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --ignore-path .gitignore --ignore-unknown --write ", "{source,test}/**/*.ts": "xo --ignore test/external/ --fix"}, "_nodeVersion": "20.5.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.2", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "4.9.5", "@types/jest": "29.5.3", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.2", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "7.0.0", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.0.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.10.0_1693438342608_0.31761000490978053", "host": "s3://npm-registry-packages"}}, "6.11.0": {"name": "express-rate-limit", "version": "6.11.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.11.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "bbb474c9765e5027ac92683a494e06162ea7c542", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.11.0.tgz", "fileCount": 10, "integrity": "sha512-H9afltGTaEZcvenAB5LFgb/ysTMHUzMxoB3TJM6UHP5FtAP1p2+heMj1xwTei54Zm4I9I/2qsS5m+XrdKQp/Hw==", "signatures": [{"sig": "MEUCIQDK4NnJdCFSFLSorIpqN5RPUcytF+btTYCfiJWM4hgx4AIgLXgjzqiIDwP8yX0wk/nSvDo/2JymTMGiR9cxRPnacrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113903}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "625ce86459a544f97743f0876a2dd490ea8c400f", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_NO_WARNINGS=1 NODE_OPTIONS=--experimental-vm-modules jest --config config/jest.json", "build:cjs": "esbuild --platform=node --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.6.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.2", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "4.9.5", "@types/jest": "29.5.3", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.2", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "7.0.0", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.0.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.11.0_1694008532997_0.6141092517837137", "host": "s3://npm-registry-packages"}}, "6.11.1": {"name": "express-rate-limit", "version": "6.11.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.11.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "52e05c5d379cd5d06ae29665862436eb712e414a", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.11.1.tgz", "fileCount": 10, "integrity": "sha512-8+UpWtQY25lJaa4+3WxDBGDcAu4atcTruSs3QSL5VPEplYy6kmk84wutG9rUkkK5LmMQQ7TFHWLZYITwVNbbEg==", "signatures": [{"sig": "MEQCIGdMRx7f9Jf5iuIKYUZcTPjAkw1Osx/3QfI9iwR/Dq4lAiAsGtYkq+nkWXd9Eu8vQRvdwrYV5Febi/anleJy8h4cCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114989}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "08f936db43f4db01f8b5487a40e122cce59a96fd", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_NO_WARNINGS=1 NODE_OPTIONS=--experimental-vm-modules jest --config config/jest.json", "build:cjs": "esbuild --platform=node --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.6.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.2", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "4.9.5", "@types/jest": "29.5.3", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.2", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "7.0.0", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.0.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.11.1_1694365816233_0.8862023946792541", "host": "s3://npm-registry-packages"}}, "6.11.2": {"name": "express-rate-limit", "version": "6.11.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@6.11.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "6c42035603d3b52e4e2fb59f6ebaa89e628ef980", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-6.11.2.tgz", "fileCount": 10, "integrity": "sha512-a7uwwfNTh1U60ssiIkuLFWHt4hAC5yxlLGU2VP0X4YNlyEDZAqF4tK3GD3NSitVBrCQmQ0++0uOyFOgC2y4DDw==", "signatures": [{"sig": "MEYCIQCJFBlrInMtrI5Je37UpUFK8vP+R6wuEgLQIdLvr8ZBAgIhANhsMbBgu/XDejUsn5smd/WmHzgecldlVhcgSM4oTOhT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115361}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 14"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "1f7d05e4716ee7dfbe08fe0abe64d4cd964a4b2e", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "cross-env NODE_NO_WARNINGS=1 NODE_OPTIONS=--experimental-vm-modules jest --config config/jest.json", "build:cjs": "esbuild --platform=node --bundle --target=es2019 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2019 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.6.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.54.2", "jest": "29.6.2", "husky": "8.0.3", "del-cli": "5.0.0", "esbuild": "0.18.11", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "4.9.5", "@types/jest": "29.5.3", "@types/node": "20.4.0", "lint-staged": "13.2.3", "npm-run-all": "4.1.5", "@jest/globals": "29.6.2", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "7.0.0", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.0.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_6.11.2_1694484138349_0.39566582959071006", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "express-rate-limit", "version": "7.0.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.0.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "606d393bebf62f2f44d98d76330151587015cba0", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.0.0.tgz", "fileCount": 10, "integrity": "sha512-zKMQ9meikj7j3ILeVvHIaBejAYljgDBtGuCfbzNS2d0VCW4s68ONdtEhBJnOGW/Ty1wGeNXgC4m/C1bBUIX0LA==", "signatures": [{"sig": "MEUCIQCUbk/JTZHY3U5GuCzV+XiBH6BdKaXxLFXXU/YYlfsFyAIgXcbaR8S6gk8zt9MwUOce/kLlxXMph762t2VJIKIqqB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134024}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "1b6c5efe1eeb73b7e40c031ed50b9e2f4d195a3f", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.6.4", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.2", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.4", "@types/node": "20.5.9", "lint-staged": "14.0.1", "npm-run-all": "4.1.5", "@jest/globals": "29.6.4", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.0.0_1694535910433_0.3933439390699942", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "express-rate-limit", "version": "7.0.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.0.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "933af24166990ea4fc8004335e6cd6c86fd31562", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.0.1.tgz", "fileCount": 10, "integrity": "sha512-oTIPm094gh8c7nbShl4TNLqnayzOcbDGY7dCRnFqUAvptyb0pp5231LaH34JtvVEbZlOJMiixikU5AVK8VN3FA==", "signatures": [{"sig": "MEYCIQDyat6GQQeL6ZDKD7eLMgB1veIMpMl/aDBobu+bfv9brAIhAJOf0bNIQweMgZfj8IjRJ1JF83dDpljRJo4c2oz3OU24", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134071}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "e641a54ee86a49260152d71813e481abda990e1c", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.6.4", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.2", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "cross-env": "7.0.3", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.4", "@types/node": "20.5.9", "lint-staged": "14.0.1", "npm-run-all": "4.1.5", "@jest/globals": "29.6.4", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.0.1_1694850576095_0.6005758058605783", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "express-rate-limit", "version": "7.0.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.0.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "0257a823d1547bca35c268d4111e2c7da2473407", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.0.2.tgz", "fileCount": 10, "integrity": "sha512-EZoojG9civtJ6GRR7vE0JErow5q/ltbIl0RGbYhrNJKwBC9/kp2HckpdAvQkkE0sRAAtFDBvILvwZSR2kQroDw==", "signatures": [{"sig": "MEQCICy7L6aQiCazuVO2cgVSk1KdbaOzQPa2jMtA3sCkU+ovAiBH1WhzbhfZ2vD0XIGTsBwjovs+kqsMtLDEC/3qeCxj+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135112}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "c1fd7b82bc2ef9995bae160d52003eaeadff9616", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.6.4", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.2", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.4", "@types/node": "20.5.9", "lint-staged": "14.0.1", "npm-run-all": "4.1.5", "@jest/globals": "29.6.4", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.0.2_1695757818633_0.1011228557182573", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "express-rate-limit", "version": "7.1.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.1.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "0942f76e78a5089fd3e24ef0a1a81c7e4ad2a92b", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.1.0.tgz", "fileCount": 10, "integrity": "sha512-pwKOMedrpJJeINON/9jhAa18udV2qwxPZSoklPZK8pmXxUyE5uXaptiwjGw8bZILbxqfUZ/p8pQA99ODjSgA5Q==", "signatures": [{"sig": "MEYCIQD3a10grwi4bAyQRhhbI1syXxKtbhWGUXBdDfziUi85jwIhAJ+dYozf35juhDWckb3S+aQjCI+L5bZuK/THcCxF5cd2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135705}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "c946e3d0de03b94901dc11e4aba9f0786895067a", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.6.4", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.2", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.4", "@types/node": "20.5.9", "lint-staged": "14.0.1", "npm-run-all": "4.1.5", "@jest/globals": "29.6.4", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.1.0_1696429171482_0.8001555638046223", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "express-rate-limit", "version": "7.1.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.1.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "a9fe92d90a1091f0d2b1d07d97d6df83b412471b", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.1.1.tgz", "fileCount": 10, "integrity": "sha512-o5ye/a4EHCPQPju25Y4HChHybrCM9v37QtQDqXUDZGuD+HB7Cbu8ZhJP6/9RORcSNtkCpnEssa6oUgJgzc7ckQ==", "signatures": [{"sig": "MEUCIEMxShe1PCXk9BQRnMJFNkn6xkpSwIvU3P2X5BUJDcScAiEAx/toEr3KooZcIojvWSFTtgiRLEsaPqAMHJOSuwlJ0Gs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 135919}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "8e2a93e1bf31e97415c0333e72df46e16a43b310", "scripts": {"lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.6.4", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.2", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.4", "@types/node": "20.5.9", "lint-staged": "14.0.1", "npm-run-all": "4.1.5", "@jest/globals": "29.6.4", "@types/express": "4.17.17", "@types/supertest": "2.0.12", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.0", "@express-rate-limit/tsconfig": "1.0.0"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.1.1_1696848774025_0.2088178361161115", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "express-rate-limit", "version": "7.1.2", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.1.2", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "42156c9135ca7b77d4e0d74b06162bfe02cd45f7", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.1.2.tgz", "fileCount": 10, "integrity": "sha512-uvkFt5JooXDhUhrfgqXLyIsAMRCtU1o8W/p0Q2p5U2ude7fEOfFaP0kSYbHOHmPbA9ZEm1JqrRne3vL9pVCBXA==", "signatures": [{"sig": "MEUCIHXP6h/5K+U3Vfmz4Uz3iWWe1vUX/4D7DarQwNYCk0tLAiEA9J5ZnKzCetOvkwlvJGxt779PD4BvM6aw39RLqlZ+lwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 119663}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "04a6f18fef114ec674ce9fa7aa0d158b7757a967", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "^4 || ^5"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.1.2_1698063530299_0.8597901540307615", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "express-rate-limit", "version": "7.1.3", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.1.3", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "0eae6c7733316f3d9403a71ad488e31e94ca0aa4", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.1.3.tgz", "fileCount": 10, "integrity": "sha512-BDes6WeNYSGRRGQU8QDNwUnwqaBro28HN/TTweM3RlxXRHDld8RLoH7tbfCxAc0hamQyn6aL0KrfR45+ZxknYg==", "signatures": [{"sig": "MEYCIQDoyR3dD6vZp6wmV/MB8FjkBrg7V+rVWuC221LCChA2LQIhAJRMRHB249NHKrGcEL0l5zrrL28pZ7eXhIO6/+n24tdS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 119935}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "a5c66a6684044df7c982f162b4adbc8dabfa9417", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.8.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.1.3_1698335855644_0.5226672367913348", "host": "s3://npm-registry-packages"}}, "7.1.4": {"name": "express-rate-limit", "version": "7.1.4", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.1.4", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "c321fe186a8366eacdb2c5edf2ad6a2f6d93e576", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.1.4.tgz", "fileCount": 10, "integrity": "sha512-mv/6z+EwnWpr+MjGVavMGvM4Tl8S/tHmpl9ZsDfrQeHpYy4Hfr0UYdKEf9OOTe280oIr70yPxLRmQ6MfINfJDw==", "signatures": [{"sig": "MEUCIQC2vilQRRplyybkPw729kFzdgdqlpvIJ8YuPUEuMCX9IQIgK+GV7ygNIUecM6LJtijMo1gdZDVJQUj6ZRaSShNGHIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 120334}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "gitHead": "6f81e8ea60996374153145e5824d2a2d3a19d2d8", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.9.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.1.4_1699281494303_0.9612730009261101", "host": "s3://npm-registry-packages"}}, "7.1.5": {"name": "express-rate-limit", "version": "7.1.5", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.1.5", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "af4c81143a945ea97f2599d13957440a0ddbfcfe", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.1.5.tgz", "fileCount": 9, "integrity": "sha512-/iVogxu7ueadrepw1bS0X0kaRC/U0afwiYRSLg68Ts+p4Dc85Q5QKsOnPS/QUjPMHvOJQtBDrZgvkOzf8ejUYw==", "signatures": [{"sig": "MEUCIBa8huamIzfHiA1KG3AGgr9A/hCpYbP0YS0J9QbsyRP6AiEAjd8+peZS8DGn8YzZsw/oeIATcbNy+tzvm7McPLQH2ZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 106071}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "782773e488da0accf34145fc61af25f879b93934", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.9.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "mintlify": "4.0.63", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.1.5_1701110916452_0.7894917601094942", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "express-rate-limit", "version": "7.2.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.2.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "06ce387dd5388f429cab8263c514fc07bf90a445", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.2.0.tgz", "fileCount": 9, "integrity": "sha512-T7nul1t4TNyfZMJ7pKRKkdeVJWa2CqB8NA1P8BwYaoDI5QSBZARv5oMS43J7b7I5P+4asjVXjb7ONuwDKucahg==", "signatures": [{"sig": "MEUCIQDwO1GklXS5ArKBcb4VnqcSG0lzG86WbrcDqTASHF2LPwIgHpEe9MiCKBgZu1imUclPGOz0bKj532Z+fkwR2MHYcvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 112374}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "f77addc8db9532924d920d4d310f7d91ba85885c", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.18.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "mintlify": "4.0.63", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.2.0_1709415648610_0.29450916064959864", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "express-rate-limit", "version": "7.3.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.3.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "b3ea0dc4fc3ca9739e3af04565184f6edcdf0240", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.3.0.tgz", "fileCount": 9, "integrity": "sha512-ZPfWlcQQ1PsZonB/vqksOsBQV74z5osi/QcdoBCyKJXl/wOVjS1yRDmvkpMM52KJeLbiF2+djwVEnEgVCDdvtw==", "signatures": [{"sig": "MEYCIQCjBX6gumHrvxLH0yGTTgPH+USDrg8kqMSM8vAm/1MyRwIhAIGLjK9bA9dT/+XUxZ9Rijy4dpCbwjxDpaT6OR1+KPM/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 114453}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "82a5de648f92702485635ae79f1ffacb7ce4ec3f", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.13.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.19.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "mintlify": "4.0.63", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.3.0_1717202429108_0.5626888528322609", "host": "s3://npm-registry-packages"}}, "7.3.1": {"name": "express-rate-limit", "version": "7.3.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.3.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "c0887ba746cdd358d17b8ab63d6eba1bae0f670b", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.3.1.tgz", "fileCount": 9, "integrity": "sha512-BbaryvkY4wEgDqLgD18/NSy2lDO2jTuT9Y8c1Mpx0X63Yz0sYd5zN6KPe7UvpuSVvV33T6RaE1o1IVZQjHMYgw==", "signatures": [{"sig": "MEYCIQCDB7DmQlGUWEBqVoxD17SXwxEipi8DkJXjQOLdK+yeEwIhANsCfhuSY3k6Y1lMjJNJobEKxb8jos+Putb9h17qJVBS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 115388}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "609972a282118cde0c6ce62b23c79ab1b6875f2f", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.19.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "mintlify": "4.0.63", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.3.1_1717777821794_0.1331068726241691", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "express-rate-limit", "version": "7.4.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.4.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "5db412b8de83fa07ddb40f610c585ac8c1dab988", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.4.0.tgz", "fileCount": 9, "integrity": "sha512-v1204w3cXu5gCDmAvgvzI6qjzZzoMWKnyVDk3ACgfswTQLYiGen+r8w0VnXnGMmzEN/g8fwIQ4JrFFd4ZP6ssg==", "signatures": [{"sig": "MEUCIB3UTVoG2jAGQ2USTDiv74URShwmFYuCeupTno2cSjpIAiEA1Fq/jKQBZ1bzcMZvUEfVD2k6qfDwjEd86XFTxcTw/F0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 116886}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "68e0dcc2d8464d87d89a3ce44d5905009c6f5c94", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.15.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.19.2", "ts-jest": "29.1.1", "ts-node": "10.9.1", "mintlify": "4.0.63", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.4.0_1721749536394_0.5951585330213953", "host": "s3://npm-registry-packages"}}, "7.4.1": {"name": "express-rate-limit", "version": "7.4.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.4.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "90954ecbcde9e7ae7b5000325395f86991191d94", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.4.1.tgz", "fileCount": 9, "integrity": "sha512-KS3efpnpIDVIXopMc65EMbWbUht7qvTCdtCR2dD/IZmi9MIkopYESwyRqLgv8Pfu589+KqDqOdzJWW7AHoACeg==", "signatures": [{"sig": "MEYCIQCEuHLbXbV9oxpfN4xz6ncrExhJVYdu563Q/Q9umwBepAIhAKqqn1rT/oHYFBKhe8i4tgmShIZQMqxCPjeZOIPHia1l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.4.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 116884}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "eaea95b4f7d8008340eac82fb261ebf8b79e6192", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.21.0", "ts-jest": "29.1.1", "ts-node": "10.9.1", "mintlify": "4.0.63", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "4 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.4.1_1728049791917_0.8297536263056633", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "express-rate-limit", "version": "7.5.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.5.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "6a67990a724b4fbbc69119419feef50c51e8b28f", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.5.0.tgz", "fileCount": 9, "integrity": "sha512-eB5zbQh5h+VenMPM3fh+nw1YExi5nMr6HUCR62ELSP11huvxm/Uir1H1QEyTkk5QX6A58pX6NmaTMceKZ0Eodg==", "signatures": [{"sig": "MEUCIQCa8a+sxBPxaqdfSpv84ol3jvo3cOpZESkGzlob3ihiwgIgTtJ1WNza17X5/gnLpe+A1wbgwBP6A15aZsn0QI02q8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 124427}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "fe46b43783313ff6660c8bc20233e0bb4a470c7d", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.19.5", "express": "4.21.1", "ts-jest": "29.1.1", "ts-node": "10.9.1", "mintlify": "4.0.63", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": "^4.11 || 5 || ^5.0.0-beta.1"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.5.0_1734239095221_0.1909110283797948", "host": "s3://npm-registry-packages-npm-production"}}, "7.5.1": {"name": "express-rate-limit", "version": "7.5.1", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@7.5.1", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "xo": {"rules": {"n/no-unsupported-features/es-syntax": 0, "@typescript-eslint/no-dynamic-delete": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/consistent-indexed-object-style": ["error", "index-signature"]}, "ignore": ["test/external"], "prettier": true, "overrides": [{"files": "test/library/*.ts", "rules": {"@typescript-eslint/no-unsafe-argument": 0, "@typescript-eslint/no-unsafe-assignment": 0}}]}, "dist": {"shasum": "8c3a42f69209a3a1c969890070ece9e20a879dec", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-7.5.1.tgz", "fileCount": 9, "integrity": "sha512-7iN8iPMDzOMHPUYllBEsQdWVB6fPDMPqwjBaFrgr4Jgr/+okjvzAy+UHlYYL/Vs0OsOrMkwS6PJDkFlJwoxUnw==", "signatures": [{"sig": "MEUCIQDhb34ACsHhkT7Vz2/oDl3aKInb1Ubt9+eyOqPW3fVqaAIgeE+3hWLbMQLPfx332tdGBGadC0bUijHU3FUHEW4plv8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@7.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 124481}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "f2287179c3785fe0377006d23cb9fa3c8bdb2574", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky install config/husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "xo", "lint:rest": "prettier --check .", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "xo --fix", "format:rest": "prettier --write ."}, "_npmUser": {"name": "nfriedly", "actor": {"name": "nfriedly", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"**/*.{json,yaml,md}": "prettier --write ", "{source,test}/**/*.ts": "xo --fix"}, "_nodeVersion": "22.16.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "0.56.0", "jest": "29.7.0", "husky": "8.0.3", "del-cli": "5.1.0", "esbuild": "0.25.0", "express": "4.21.1", "ts-jest": "29.1.1", "ts-node": "10.9.1", "mintlify": "4.0.63", "supertest": "6.3.3", "typescript": "5.2.2", "@types/jest": "29.5.6", "@types/node": "20.8.7", "lint-staged": "15.0.2", "npm-run-all": "4.1.5", "@jest/globals": "29.7.0", "@types/express": "4.17.20", "@types/supertest": "2.0.15", "dts-bundle-generator": "8.0.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": ">= 4.11"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_7.5.1_1750475304978_0.0183677994686251", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0": {"name": "express-rate-limit", "version": "8.0.0", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "author": {"url": "http://nfriedly.com/", "name": "<PERSON>"}, "license": "MIT", "_id": "express-rate-limit@8.0.0", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "homepage": "https://github.com/express-rate-limit/express-rate-limit", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "dist": {"shasum": "2f011f2fd42078100f50341c416e7c15b95c4189", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-8.0.0.tgz", "fileCount": 9, "integrity": "sha512-FXEAp2ccTeN1ZSO+sPHRHWB0/CrTP5asFBjUaNeD9A0v3iPmgFbLu24vqPjiM9utszI58VGlMokjXQ0W9Dbmjw==", "signatures": [{"sig": "MEYCIQCL+rbDT/YOYPAkJFc2QZpI93Jwky7UmmNGIYYVjFXgPwIhAIX6j+lD8Nc00eraCYz1H9B9fJYTWuTv4+OX8SleaOz8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@8.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 134072}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "engines": {"node": ">= 16"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "funding": "https://github.com/sponsors/express-rate-limit", "gitHead": "c299d5c89f9a75e52bfc28b81c22df0f059520e8", "scripts": {"docs": "cd docs && mintlify dev", "lint": "run-s lint:*", "test": "run-s lint test:lib", "clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "format": "run-s format:*", "compile": "run-s clean build:*", "prepare": "run-s compile && husky", "test:ext": "cd test/external/ && bash run-all-tests", "test:lib": "jest", "build:cjs": "esbuild --packages=external --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = rateLimit; module.exports.default = rateLimit; module.exports.rateLimit = rateLimit; module.exports.MemoryStore = MemoryStore;\" source/index.ts", "build:esm": "esbuild --packages=external --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "lint:code": "biome check", "lint:docs": "prettier --check docs/ *.md", "pre-commit": "lint-staged", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "format:code": "biome check --write", "format:docs": "prettier --write docs/ *.md"}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "prettier": "@express-rate-limit/prettier", "repository": {"url": "git+https://github.com/express-rate-limit/express-rate-limit.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "directories": {}, "lint-staged": {"*.{md,yaml}": "prettier --write", "*.{js,ts,json}": "biome check --write"}, "_nodeVersion": "22.17.0", "dependencies": {"ip": "2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "30.0.4", "husky": "9.1.7", "del-cli": "6.0.0", "esbuild": "0.25.6", "express": "5.1.0", "ts-jest": "29.4.0", "ts-node": "10.9.2", "mintlify": "4.2.15", "prettier": "3.6.2", "@types/ip": "1.1.3", "supertest": "7.1.3", "typescript": "5.8.3", "@types/jest": "30.0.0", "@types/node": "24.0.14", "lint-staged": "16.1.2", "npm-run-all": "4.1.5", "@jest/globals": "30.0.4", "@biomejs/biome": "2.1.1", "@types/express": "5.0.3", "@types/supertest": "6.0.3", "dts-bundle-generator": "9.5.1", "ratelimit-header-parser": "0.1.0", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2"}, "peerDependencies": {"express": ">= 4.11"}, "_npmOperationalInternal": {"tmp": "tmp/express-rate-limit_8.0.0_1752596262501_0.11545924185767009", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.1": {"name": "express-rate-limit", "version": "8.0.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "author": {"name": "<PERSON>", "url": "http://nfriedly.com/"}, "license": "MIT", "homepage": "https://github.com/express-rate-limit/express-rate-limit", "repository": {"type": "git", "url": "git+https://github.com/express-rate-limit/express-rate-limit.git"}, "funding": "https://github.com/sponsors/express-rate-limit", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "engines": {"node": ">= 16"}, "scripts": {"clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "build:cjs": "esbuild --packages=external --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = Object.assign(rateLimit, module.exports);\" source/index.ts", "build:esm": "esbuild --packages=external --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "compile": "run-s clean build:*", "docs": "cd docs && mintlify dev", "lint:code": "biome check", "lint:docs": "prettier --check docs/ *.md", "lint": "run-s lint:*", "format:code": "biome check --write", "format:docs": "prettier --write docs/ *.md", "format": "run-s format:*", "test:lib": "jest", "test:ext": "cd test/external/ && bash run-all-tests", "test": "run-s lint test:lib", "pre-commit": "lint-staged", "prepare": "run-s compile && husky"}, "peerDependencies": {"express": ">= 4.11"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2", "@jest/globals": "30.0.4", "@types/express": "5.0.3", "@types/jest": "30.0.0", "@types/node": "24.0.14", "@types/supertest": "6.0.3", "del-cli": "6.0.0", "dts-bundle-generator": "8.0.1", "esbuild": "0.25.6", "express": "5.1.0", "husky": "9.1.7", "jest": "30.0.4", "lint-staged": "16.1.2", "mintlify": "4.2.15", "npm-run-all": "4.1.5", "prettier": "3.6.2", "ratelimit-header-parser": "0.1.0", "supertest": "7.1.3", "ts-jest": "29.4.0", "ts-node": "10.9.2", "typescript": "5.8.3"}, "prettier": "@express-rate-limit/prettier", "lint-staged": {"*.{js,ts,json}": "biome check --write", "*.{md,yaml}": "prettier --write"}, "dependencies": {"ip-address": "10.0.1"}, "_id": "express-rate-limit@8.0.1", "gitHead": "b70f456969776326614713cfab01b6ddc076120c", "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "_nodeVersion": "22.17.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-aZVCnybn7TVmxO4BtlmnvX+nuz8qHW124KKJ8dumsBsmv5ZLxE0pYu7S2nwyRBGHHCAzdmnGyrc5U/rksSPO7Q==", "shasum": "3bc13aaf9f448085686180ef60679a68ea89654d", "tarball": "https://registry.npmjs.org/express-rate-limit/-/express-rate-limit-8.0.1.tgz", "fileCount": 9, "unpackedSize": 133214, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/express-rate-limit@8.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDmKf8jpJ1vB5o3M81z+ul+sP9ETi8FmilyU1xUyj2aSQIgUOgvq5PmiFnXU2TZ7z5etGZexq0OWnEPlEvHYO/OjwE="}]}, "_npmUser": {"name": "nfriedly", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/express-rate-limit_8.0.1_1752700346946_0.3686614956277956"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-12-11T01:58:05.543Z", "modified": "2025-07-16T21:12:27.548Z", "1.0.0": "2014-12-11T01:58:05.543Z", "1.0.1": "2014-12-11T02:01:17.663Z", "1.0.2": "2014-12-11T15:05:32.874Z", "1.0.3": "2015-07-27T20:48:03.264Z", "1.1.0": "2015-09-02T16:05:14.697Z", "1.2.0": "2015-09-02T16:21:12.698Z", "2.0.0": "2015-09-29T21:27:37.030Z", "2.0.1": "2015-09-29T22:13:19.612Z", "2.0.2": "2015-09-29T22:17:23.231Z", "2.1.0": "2016-01-20T15:54:39.521Z", "2.1.2": "2016-03-30T12:34:49.122Z", "2.1.3": "2016-04-25T14:02:01.652Z", "2.2.0": "2016-04-25T14:03:00.014Z", "2.3.0": "2016-05-18T18:54:18.892Z", "2.3.1": "2016-05-18T18:58:19.005Z", "2.4.0": "2016-07-08T19:42:59.530Z", "2.5.0": "2016-09-14T23:16:51.886Z", "2.6.0": "2016-11-18T15:08:27.264Z", "2.7.0": "2017-05-02T14:48:45.199Z", "2.8.0": "2017-05-15T18:04:40.238Z", "2.8.1": "2017-07-25T19:38:01.317Z", "2.9.0": "2017-07-26T20:06:10.714Z", "2.10.0": "2017-12-10T15:58:15.215Z", "2.11.0": "2017-12-10T19:40:58.646Z", "2.12.1": "2018-08-03T14:01:11.721Z", "2.12.2": "2018-08-07T18:13:52.588Z", "2.13.0": "2018-08-21T00:00:24.962Z", "3.0.0": "2018-08-21T00:08:17.751Z", "3.0.1": "2018-08-21T01:18:17.410Z", "3.0.2": "2018-08-21T13:37:05.622Z", "3.0.3": "2018-08-22T17:43:52.447Z", "2.13.1": "2018-08-22T17:45:28.392Z", "3.1.0": "2018-08-23T15:40:58.418Z", "2.14.2": "2018-08-23T15:53:01.261Z", "3.1.1": "2018-09-06T20:33:51.508Z", "3.2.0": "2018-09-18T03:33:06.176Z", "3.2.1": "2018-10-09T01:53:40.027Z", "3.3.0": "2018-10-30T12:37:53.523Z", "3.3.1": "2018-11-08T14:58:17.593Z", "3.3.2": "2018-11-12T18:16:18.236Z", "3.4.0": "2019-02-19T19:23:53.146Z", "3.4.1": "2019-04-16T18:45:25.853Z", "3.5.0": "2019-04-29T15:06:08.280Z", "3.5.1": "2019-05-10T16:27:07.080Z", "3.5.2": "2019-05-21T13:17:41.793Z", "4.0.0": "2019-05-21T13:47:34.857Z", "3.5.3": "2019-05-22T01:30:00.524Z", "4.0.1": "2019-05-22T01:33:52.265Z", "4.0.2": "2019-05-27T12:24:34.567Z", "4.0.3": "2019-05-29T17:23:47.827Z", "4.0.4": "2019-06-01T18:33:48.089Z", "5.0.0": "2019-06-08T00:06:09.325Z", "6.0.0-typescript-beta-1": "2019-07-09T14:45:16.674Z", "0.0.0-typescript-beta-2": "2019-07-09T14:57:03.296Z", "0.0.0-typescript-beta-3": "2019-07-09T15:10:39.060Z", "0.0.0-typescript-beta-4": "2019-07-09T15:19:10.169Z", "0.0.0-typescript-beta-5": "2019-07-11T18:24:17.998Z", "0.0.0-typescript-beta-6": "2019-07-11T20:37:09.501Z", "0.0.0-typescript-beta-7": "2019-07-12T15:43:37.655Z", "5.1.1": "2020-02-13T22:31:03.992Z", "5.1.3": "2020-04-29T15:27:33.568Z", "5.2.1": "2020-11-19T16:12:25.961Z", "5.2.2": "2020-11-19T16:24:49.168Z", "5.2.3": "2020-11-19T17:00:30.779Z", "5.2.5": "2021-02-08T15:49:05.132Z", "5.2.6": "2021-02-17T14:45:24.702Z", "5.3.0": "2021-07-01T15:49:54.570Z", "5.4.0": "2021-10-01T19:05:37.135Z", "5.4.1": "2021-10-05T20:12:12.780Z", "5.5.0": "2021-10-12T19:59:06.389Z", "5.5.1": "2021-11-06T03:16:54.480Z", "6.0.0": "2021-12-24T17:28:34.110Z", "6.0.1": "2021-12-25T10:03:19.446Z", "6.0.2": "2021-12-30T06:38:42.092Z", "6.0.3": "2021-12-30T17:00:39.272Z", "6.0.4": "2022-01-02T13:52:44.656Z", "6.0.5": "2022-01-06T13:26:08.466Z", "6.1.0": "2022-01-12T04:22:23.546Z", "6.2.0": "2022-01-22T13:49:25.429Z", "6.2.1": "2022-02-10T15:17:24.046Z", "6.3.0": "2022-02-19T12:24:53.568Z", "6.4.0": "2022-04-24T15:55:08.722Z", "6.5.1": "2022-07-23T16:51:11.368Z", "6.5.2": "2022-08-24T19:36:27.539Z", "6.6.0": "2022-09-04T19:36:36.620Z", "6.7.0": "2022-11-15T19:00:57.416Z", "6.7.1": "2023-07-06T21:31:06.430Z", "6.8.0": "2023-07-21T00:39:22.367Z", "6.7.2": "2023-07-27T12:58:32.778Z", "6.8.1": "2023-07-27T13:08:38.737Z", "6.9.0": "2023-08-06T03:35:45.701Z", "6.10.0": "2023-08-30T23:32:22.857Z", "6.11.0": "2023-09-06T13:55:33.181Z", "6.11.1": "2023-09-10T17:10:16.449Z", "6.11.2": "2023-09-12T02:02:18.599Z", "7.0.0": "2023-09-12T16:25:10.663Z", "7.0.1": "2023-09-16T07:49:36.325Z", "7.0.2": "2023-09-26T19:50:18.888Z", "7.1.0": "2023-10-04T14:19:31.658Z", "7.1.1": "2023-10-09T10:52:54.198Z", "7.1.2": "2023-10-23T12:18:50.463Z", "7.1.3": "2023-10-26T15:57:35.872Z", "7.1.4": "2023-11-06T14:38:14.511Z", "7.1.5": "2023-11-27T18:48:36.683Z", "7.2.0": "2024-03-02T21:40:48.791Z", "7.3.0": "2024-06-01T00:40:29.269Z", "7.3.1": "2024-06-07T16:30:22.094Z", "7.4.0": "2024-07-23T15:45:36.551Z", "7.4.1": "2024-10-04T13:49:52.139Z", "7.5.0": "2024-12-15T05:04:55.384Z", "7.5.1": "2025-06-21T03:08:25.153Z", "8.0.0": "2025-07-15T16:17:42.676Z", "8.0.1": "2025-07-16T21:12:27.149Z"}, "bugs": {"url": "https://github.com/express-rate-limit/express-rate-limit/issues"}, "author": {"name": "<PERSON>", "url": "http://nfriedly.com/"}, "license": "MIT", "homepage": "https://github.com/express-rate-limit/express-rate-limit", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "repository": {"type": "git", "url": "git+https://github.com/express-rate-limit/express-rate-limit.git"}, "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "maintainers": [{"name": "nfriedly", "email": "<EMAIL>"}, {"name": "gamemaker1", "email": "<EMAIL>"}], "readme": "<h1 align=\"center\"> <code>express-rate-limit</code> </h1>\n\n<div align=\"center\">\n\n[![tests](https://img.shields.io/github/actions/workflow/status/express-rate-limit/express-rate-limit/ci.yaml)](https://github.com/express-rate-limit/express-rate-limit/actions/workflows/ci.yaml)\n[![npm version](https://img.shields.io/npm/v/express-rate-limit.svg)](https://npmjs.org/package/express-rate-limit 'View this project on NPM')\n[![npm downloads](https://img.shields.io/npm/dm/express-rate-limit)](https://www.npmjs.com/package/express-rate-limit)\n[![license](https://img.shields.io/npm/l/express-rate-limit)](license.md)\n\n</div>\n\nBasic rate-limiting middleware for [Express](http://expressjs.com/). Use to\nlimit repeated requests to public APIs and/or endpoints such as password reset.\nPlays nice with\n[express-slow-down](https://www.npmjs.com/package/express-slow-down) and\n[ratelimit-header-parser](https://www.npmjs.com/package/ratelimit-header-parser).\n\n## Usage\n\nThe [full documentation](https://express-rate-limit.mintlify.app/overview) is\navailable on-line.\n\n```ts\nimport { rateLimit } from 'express-rate-limit'\n\nconst limiter = rateLimit({\n\twindowMs: 15 * 60 * 1000, // 15 minutes\n\tlimit: 100, // Limit each IP to 100 requests per `window` (here, per 15 minutes).\n\tstandardHeaders: 'draft-8', // draft-6: `RateLimit-*` headers; draft-7 & draft-8: combined `RateLimit` header\n\tlegacyHeaders: false, // Disable the `X-RateLimit-*` headers.\n\tipv6Subnet: 56, // Set to 60 or 64 to be less aggressive, or 52 or 48 to be more aggressive\n\t// store: ... , // Redis, Memcached, etc. See below.\n})\n\n// Apply the rate limiting middleware to all requests.\napp.use(limiter)\n```\n\n### Data Stores\n\nThe rate limiter comes with a built-in memory store, and supports a variety of\n[external data stores](https://express-rate-limit.mintlify.app/reference/stores).\n\n### Configuration\n\nAll function options may be async. Click the name for additional info and\ndefault values.\n\n| Option                     | Type                                      | Remarks                                                                                         |\n| -------------------------- | ----------------------------------------- | ----------------------------------------------------------------------------------------------- |\n| [`windowMs`]               | `number`                                  | How long to remember requests for, in milliseconds.                                             |\n| [`limit`]                  | `number` \\| `function`                    | How many requests to allow.                                                                     |\n| [`message`]                | `string` \\| `json` \\| `function`          | Response to return after limit is reached.                                                      |\n| [`statusCode`]             | `number`                                  | HTTP status code after limit is reached (default is 429).                                       |\n| [`handler`]                | `function`                                | Function to run after limit is reached (overrides `message` and `statusCode` settings, if set). |\n| [`legacyHeaders`]          | `boolean`                                 | Enable the `X-Rate-Limit` header.                                                               |\n| [`standardHeaders`]        | `'draft-6'` \\| `'draft-7'` \\| `'draft-8'` | Enable the `Ratelimit` header.                                                                  |\n| [`identifier`]             | `string` \\| `function`                    | Name associated with the quota policy enforced by this rate limiter.                            |\n| [`store`]                  | `Store`                                   | Use a custom store to share hit counts across multiple nodes.                                   |\n| [`passOnStoreError`]       | `boolean`                                 | Allow (`true`) or block (`false`, default) traffic if the store becomes unavailable.            |\n| [`keyGenerator`]           | `function`                                | Identify users (defaults to IP address).                                                        |\n| [`ipv6Subnet`]             | `number` (32-64) \\| `function` \\| `false` | How many bits of IPv6 addresses to use in default `keyGenerator`                                |\n| [`requestPropertyName`]    | `string`                                  | Add rate limit info to the `req` object.                                                        |\n| [`skip`]                   | `function`                                | Return `true` to bypass the limiter for the given request.                                      |\n| [`skipSuccessfulRequests`] | `boolean`                                 | Uncount 1xx/2xx/3xx responses.                                                                  |\n| [`skipFailedRequests`]     | `boolean`                                 | Uncount 4xx/5xx responses.                                                                      |\n| [`requestWasSuccessful`]   | `function`                                | Used by `skipSuccessfulRequests` and `skipFailedRequests`.                                      |\n| [`validate`]               | `boolean` \\| `object`                     | Enable or disable built-in validation checks.                                                   |\n\n## Thank You\n\nSponsored by [Zuplo](https://zuplo.link/express-rate-limit) a fully-managed API\nGateway for developers. Add\n[dynamic rate-limiting](https://zuplo.link/dynamic-rate-limiting),\nauthentication and more to any API in minutes. Learn more at\n[zuplo.com](https://zuplo.link/express-rate-limit)\n\n<p align=\"center\">\n<a href=\"https://zuplo.link/express-rate-limit\">\n<picture width=\"322\">\n  <source media=\"(prefers-color-scheme: dark)\" srcset=\"https://github.com/express-rate-limit/express-rate-limit/assets/114976/cd2f6fa7-eae1-4fbb-be7d-b17df4c6f383\">\n  <img alt=\"zuplo-logo\" src=\"https://github.com/express-rate-limit/express-rate-limit/assets/114976/66fd75fa-b39e-4a8c-8d7a-52369bf244dc\" width=\"322\">\n</picture>\n</a>\n</p>\n\n---\n\nThanks to Mintlify for hosting the documentation at\n[express-rate-limit.mintlify.app](https://express-rate-limit.mintlify.app)\n\n<p align=\"center\">\n\t<a href=\"https://mintlify.com/?utm_campaign=devmark&utm_medium=readme&utm_source=express-rate-limit\">\n\t\t<img height=\"75\" src=\"https://devmark-public-assets.s3.us-west-2.amazonaws.com/sponsorships/mintlify.svg\" alt=\"Create your docs today\">\n\t</a>\n</p>\n\n---\n\nFinally, thank you to everyone who's contributed to this project in any way! 🫶\n\n## Issues and Contributing\n\nIf you encounter a bug or want to see something added/changed, please go ahead\nand\n[open an issue](https://github.com/express-rate-limit/express-rate-limit/issues/new)!\nIf you need help with something, feel free to\n[start a discussion](https://github.com/express-rate-limit/express-rate-limit/discussions/new)!\n\nIf you wish to contribute to the library, thanks! First, please read\n[the contributing guide](https://express-rate-limit.mintlify.app/docs/guides/contributing.mdx).\nThen you can pick up any issue and fix/implement it!\n\n## License\n\nMIT © [Nathan Friedly](http://nfriedly.com/),\n[Vedant K](https://github.com/gamemaker1)\n\n[`windowMs`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#windowms\n[`limit`]: https://express-rate-limit.mintlify.app/reference/configuration#limit\n[`message`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#message\n[`statusCode`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#statuscode\n[`handler`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#handler\n[`legacyHeaders`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#legacyheaders\n[`standardHeaders`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#standardheaders\n[`identifier`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#identifier\n[`store`]: https://express-rate-limit.mintlify.app/reference/configuration#store\n[`passOnStoreError`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#passonstoreerror\n[`keyGenerator`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#keygenerator\n[`ipv6Subnet`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#ipv6subnet\n[`requestPropertyName`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#requestpropertyname\n[`skip`]: https://express-rate-limit.mintlify.app/reference/configuration#skip\n[`skipSuccessfulRequests`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#skipsuccessfulrequests\n[`skipFailedRequests`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#skipfailedrequests\n[`requestWasSuccessful`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#requestwassuccessful\n[`validate`]:\n\thttps://express-rate-limit.mintlify.app/reference/configuration#validate\n", "readmeFilename": "readme.md", "users": {"cxm": true, "nazy": true, "spad": true, "seldo": true, "ywk93": true, "lonjoy": true, "touskar": true, "clong365": true, "hexalyse": true, "jmsherry": true, "klombomb": true, "mcharper": true, "rochejul": true, "tmurngon": true, "tsrisudh": true, "zeusbaba": true, "crellison": true, "dylanh724": true, "goliatone": true, "juju.chen": true, "luukmoret": true, "maxwelldu": true, "mjurincic": true, "theaklair": true, "ashish.npm": true, "brightchen": true, "hengkiardo": true, "justinshea": true, "leizongmin": true, "rocket0191": true, "santi8ago8": true, "hibernating": true, "uh-engineer": true, "andrewyang96": true, "martinspinks": true, "predatorkill": true, "processbrain": true, "wfalkwallace": true, "elitelegendary": true, "shanewholloway": true, "spiros.politis": true, "william_an2000": true, "yazanrawashdeh": true, "ys_sidson_aidson": true, "obsessiveprogrammer": true, "boopathisakthivel.in": true}}