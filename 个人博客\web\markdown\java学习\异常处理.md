# 异常处理:

![image-20241129100733244](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241129100733244.png)

## 异常出现也会跑完整个代码：

```java
package 异常三连;

public class _main {
    public static void main(String[] args) {
        try{
            int a1=4,a2=0;
            System.out.println(a1/a2);
        }catch(Exception e)
        {
            System.out.println("出现了商为零的错误");
        }
        finally{
            System.out.println("异常处理完毕");
        }
    }
}

```





## 异常类型不匹配的时候会报错:

```java
package 异常三连;

public class _main {
    public static void main(String[] args) {
        try{
            int a1=4,a2=0;
            System.out.println(a1/a2);
        }catch(SecurityException e)
        {
            System.out.println("出现了商为零的错误");
        }
        finally{
            System.out.println("异常处理完毕");
        }
    }
}
```

## 常用的异常处理：异常三连



1.对于可能出错的代码，

我们将它放进try代码块中

2.程序会将try代码转换成一个对象，我们在catch中定义一个异常对象，用它来接收，Exception几乎可以对应所有宜昌

3.如果catch中定义的异常类型匹配了就会执行catch块

![image-20241129101243746](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241129101243746.png)

4.finally块无论出不出现异常，该块一定执行

5.他有一个好处就是我们可以不中断代码执行来跑完整个代码



## 异常处理throw，throws：

![image-20241129103921186](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241129103921186.png)

## throw相当于抛出异常的意思：throw+new+异常对象名，没有异常我们制造异常

## 此处的异常可以通过两种方式处理：

1.可以甩出去，给使用该方法的调用者处理该异常，方法要有throws声明异常类型

2.可以自己处理

```java
package throw制造异常;

public class _main {
    public static void devide(double aa,double bb)
{
    if (bb == 0) {
        try {
            throw new Exception();
        } catch (Exception e) {
            System.out.println("出现错误");
        }
    } else {
        System.out.println(aa/bb);
    }
}
    public static void main(String[] args) {
        double a = 4, b = 0;
        devide(a,b);
    }
}

```

```java
package throw制造异常;

public class _main {
    public static void devide(double aa,double bb)throws Exception
{
    if (bb == 0) {
        throw new Exception();
    } else {
        System.out.println(aa/bb);
    }
}
    public static void main(String[] args) {
        double a = 4, b = 0;
        try{
            devide(a,b);
        }catch (Exception e)
        {
            System.out.println("出现异常");
        }
    }
}

```

