{"_id": "safer-buffer", "_rev": "9-194c14f9664bd82349dfb53b880b0067", "name": "safer-buffer", "dist-tags": {"latest": "2.1.2"}, "versions": {"2.0.0": {"name": "safer-buffer", "version": "2.0.0", "description": "Modern Buffer API polyfill without footguns", "main": "safer.js", "scripts": {"test": "standard && tape tests.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "devDependencies": {"standard": "^11.0.1", "tape": "^4.9.0"}, "files": ["tests.js", "dangerous.js", "safer.js"], "gitHead": "a9f897f4a3abfe6c6a2b657aec7b93ea26078e82", "homepage": "https://github.com/ChALkeR/safer-buffer#readme", "_id": "safer-buffer@2.0.0", "_npmVersion": "5.7.1", "_nodeVersion": "9.8.0", "_npmUser": {"name": "chalker", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xfyC9tPVPALdc+v+iF5EgNWLq2tBaVLCqcwPjvZ6oNzzL/q4Bh0D+dFy90dVlX6RkCzqamJhBcxgG72DCmZ2XQ==", "shasum": "a780a965ff6375210b360a969581ceaf60600193", "tarball": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.0.0.tgz", "fileCount": 5, "unpackedSize": 19297, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFq3qENzvih40is7AR5vN3RssckvKDNt8ZzfjhB+thY2AiBkTKjIy7w6zmZ7iABLc4iLg+Ts+7oTwzeQ0mUbU2NNYw=="}]}, "maintainers": [{"name": "chalker", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safer-buffer_2.0.0_1521448517452_0.9654642196058854"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "safer-buffer", "version": "2.0.1", "description": "Modern Buffer API polyfill without footguns", "main": "safer.js", "scripts": {"test": "standard && tape tests.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "devDependencies": {"standard": "^11.0.1", "tape": "^4.9.0"}, "files": ["tests.js", "dangerous.js", "safer.js"], "gitHead": "3e812676ce51ba04a3d58c4f1dc9a80907789234", "homepage": "https://github.com/ChALkeR/safer-buffer#readme", "_id": "safer-buffer@2.0.1", "_npmVersion": "5.7.1", "_nodeVersion": "9.8.0", "_npmUser": {"name": "chalker", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3i2tOIUGJ6ZOIJ0FPTN+K/6iFBZUGB6fCee1PQGrLaioDFPLWQCaRJeBMMTpdSMCRAmuyu7FSJKnjriAmE/aQA==", "shasum": "9b158aab695aa93d8259a038d9f7836c8fd7fabf", "tarball": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.0.1.tgz", "fileCount": 6, "unpackedSize": 21003, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAVdCo0SCvwE3pIS0XcI0hAGmFH9QRMJ3UXmWZXlvIjAIgHY/wJdNV8J6TKpRZqn2NZ+C407PIrmp9JJ+lXWs9YyY="}]}, "maintainers": [{"name": "chalker", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safer-buffer_2.0.1_1521458495616_0.06119043006489222"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "safer-buffer", "version": "2.0.2", "description": "Modern Buffer API polyfill without footguns", "main": "safer.js", "scripts": {"test": "standard && tape tests.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "devDependencies": {"standard": "^11.0.1", "tape": "^4.9.0"}, "files": ["Porting-Buffer.md", "Readme.md", "tests.js", "dangerous.js", "safer.js"], "gitHead": "ba8472e414180be4e567c47789f8a20cb73affea", "homepage": "https://github.com/ChALkeR/safer-buffer#readme", "_id": "safer-buffer@2.0.2", "_npmVersion": "5.7.1", "_nodeVersion": "9.8.0", "_npmUser": {"name": "chalker", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-n7d2A1kx/NW7bow4A2jx111qpYsF0qDhvfHssl+FIuXY8skGswVCIXoo8AUt72d8vK6R3teMjHIV1Yt7Q/m08A==", "shasum": "d5e5d219697d92d1c138e0037525fd99d0ebbdf0", "tarball": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.0.2.tgz", "fileCount": 7, "unpackedSize": 36955, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDF0kn9QhKIGa6/WtY75QJeh+S6hYvM1xt1GbQGzQvrqAiEAqBF8GMCJiuEOm73S0w2zKBZOXMREqaPjCG51GSGqNJ0="}]}, "maintainers": [{"name": "chalker", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safer-buffer_2.0.2_1521545289647_0.7300470347310737"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "safer-buffer", "version": "2.1.0", "description": "Modern Buffer API polyfill without footguns", "main": "safer.js", "scripts": {"test": "standard && tape tests.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "devDependencies": {"standard": "^11.0.1", "tape": "^4.9.0"}, "files": ["Porting-Buffer.md", "Readme.md", "tests.js", "dangerous.js", "safer.js"], "gitHead": "38d80d6be470a792126e42e5ee2c447c3da18b46", "homepage": "https://github.com/ChALkeR/safer-buffer#readme", "_id": "safer-buffer@2.1.0", "_npmVersion": "5.7.1", "_nodeVersion": "9.8.0", "_npmUser": {"name": "chalker", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HQhCIIl7TrF1aa7d352EXG+xumPERvoIWxOqq2CagDId0FVGtlG/fuQ7kZT+wZ7ytyGiP3pnYUVni5otBzOVmA==", "shasum": "d9f653a55538c8d7829cb1a92e90bbcbc5ff5d3b", "tarball": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.0.tgz", "fileCount": 7, "unpackedSize": 41366, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB2D+/HFpEgfBYn49RjSr7UHCFA6hnWJIzocP8ugHqLSAiA0jd/ECaWHOCmwsaPFb9xobv3RYtDihtylhQuY39ge1Q=="}]}, "maintainers": [{"name": "chalker", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safer-buffer_2.1.0_1521659685140_0.5417942695599183"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "safer-buffer", "version": "2.1.1", "description": "Modern Buffer API polyfill without footguns", "main": "safer.js", "scripts": {"test": "standard && tape tests.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "devDependencies": {"standard": "^11.0.1", "tape": "^4.9.0"}, "files": ["Porting-Buffer.md", "Readme.md", "tests.js", "dangerous.js", "safer.js"], "gitHead": "e87995376d301d7559064cfacdcbab918e7ad0fc", "homepage": "https://github.com/ChALkeR/safer-buffer#readme", "_id": "safer-buffer@2.1.1", "_npmVersion": "5.8.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "chalker", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sSsAhyRw8mBRo96T6nsCCAQosFTc79+wYWbiECHP5P03a7wE76VAbbLysuJ8EEfBhElIsLZau+WNWES4Y/IQSA==", "shasum": "2e945f82c9a380e8e3b7c1a1bc21c976bb49f3ea", "tarball": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.1.tgz", "fileCount": 7, "unpackedSize": 42153, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIATHETjE3ADsHMAt4kviihS+LZJesuXnc41E51YvdgHfAiANEXlkZe6mkA6UzcTcKU9SWbJlQOOa/KapOsfj3S8VJg=="}]}, "maintainers": [{"name": "chalker", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safer-buffer_2.1.1_1523179067144_0.7766754431469289"}, "_hasShrinkwrap": false}, "2.1.2": {"name": "safer-buffer", "version": "2.1.2", "description": "Modern Buffer API polyfill without footguns", "main": "safer.js", "scripts": {"browserify-test": "browserify --external tape tests.js > browserify-tests.js && tape browserify-tests.js", "test": "standard && tape tests.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "devDependencies": {"standard": "^11.0.1", "tape": "^4.9.0"}, "files": ["Porting-Buffer.md", "Readme.md", "tests.js", "dangerous.js", "safer.js"], "gitHead": "e8ac214944eda30e1e6c6b7d7e7f6a21cf7dce7c", "homepage": "https://github.com/ChALkeR/safer-buffer#readme", "_id": "safer-buffer@2.1.2", "_npmVersion": "5.8.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "chalker", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "shasum": "44fa161b0187b9549dd84bb91802f9bd8385cd6a", "tarball": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "fileCount": 7, "unpackedSize": 42299, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiWlmbOFsq/xKEX4UKFrw7JuXKDGPMQwhMUq5cAT6LggIhAIHQagK183Vhp/6SzFuUWxphk7AbQiAzPiOAMO7etVRH"}]}, "maintainers": [{"name": "chalker", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safer-buffer_2.1.2_1523184162015_0.8333925439572323"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-19T08:26:02.082Z", "1.0.0": "2018-03-19T08:26:02.318Z", "modified": "2022-05-16T10:23:00.724Z", "2.0.0": "2018-03-19T08:35:17.564Z", "2.0.1": "2018-03-19T11:21:35.766Z", "2.0.2": "2018-03-20T11:28:09.716Z", "2.1.0": "2018-03-21T19:14:45.213Z", "2.1.1": "2018-04-08T09:17:47.195Z", "2.1.2": "2018-04-08T10:42:42.130Z"}, "maintainers": [{"name": "chalker", "email": "<EMAIL>"}], "description": "Modern Buffer API polyfill without footguns", "homepage": "https://github.com/ChALkeR/safer-buffer#readme", "repository": {"type": "git", "url": "git+https://github.com/ChALkeR/safer-buffer.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR"}, "bugs": {"url": "https://github.com/ChALkeR/safer-buffer/issues"}, "license": "MIT", "readme": "# safer-buffer [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![javascript style guide][standard-image]][standard-url] [![Security Responsible Disclosure][secuirty-image]][secuirty-url]\n\n[travis-image]: https://travis-ci.org/ChALkeR/safer-buffer.svg?branch=master\n[travis-url]: https://travis-ci.org/ChALkeR/safer-buffer\n[npm-image]: https://img.shields.io/npm/v/safer-buffer.svg\n[npm-url]: https://npmjs.org/package/safer-buffer\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n[secuirty-image]: https://img.shields.io/badge/Security-Responsible%20Disclosure-green.svg\n[secuirty-url]: https://github.com/nodejs/security-wg/blob/master/processes/responsible_disclosure_template.md\n\nModern Buffer API polyfill without footguns, working on Node.js from 0.8 to current.\n\n## How to use?\n\nFirst, port all `Buffer()` and `new Buffer()` calls to `Buffer.alloc()` and `Buffer.from()` API.\n\nThen, to achieve compatibility with outdated Node.js versions (`<4.5.0` and 5.x `<5.9.0`), use\n`const Buffer = require('safer-buffer').Buffer` in all files where you make calls to the new\nBuffer API. _Use `var` instead of `const` if you need that for your Node.js version range support._\n\nAlso, see the\n[porting Buffer](https://github.com/ChALkeR/safer-buffer/blob/master/Porting-Buffer.md) guide.\n\n## Do I need it?\n\nHopefully, not — dropping support for outdated Node.js versions should be fine nowdays, and that\nis the recommended path forward. You _do_ need to port to the `Buffer.alloc()` and `Buffer.from()`\nthough.\n\nSee the [porting guide](https://github.com/ChALkeR/safer-buffer/blob/master/Porting-Buffer.md)\nfor a better description.\n\n## Why not [safe-buffer](https://npmjs.com/safe-buffer)?\n\n_In short: while `safe-buffer` serves as a polyfill for the new API, it allows old API usage and\nitself contains footguns._\n\n`safe-buffer` could be used safely to get the new API while still keeping support for older\nNode.js versions (like this module), but while analyzing ecosystem usage of the old Buffer API\nI found out that `safe-buffer` is itself causing problems in some cases.\n\nFor example, consider the following snippet:\n\n```console\n$ cat example.unsafe.js\nconsole.log(Buffer(20))\n$ ./node-v6.13.0-linux-x64/bin/node example.unsafe.js\n<Buffer 0a 00 00 00 00 00 00 00 28 13 de 02 00 00 00 00 05 00 00 00>\n$ standard example.unsafe.js\nstandard: Use JavaScript Standard Style (https://standardjs.com)\n  /home/<USER>/repo/safer-buffer/example.unsafe.js:2:13: 'Buffer()' was deprecated since v6. Use 'Buffer.alloc()' or 'Buffer.from()' (use 'https://www.npmjs.com/package/safe-buffer' for '<4.5.0') instead.\n```\n\nThis is allocates and writes to console an uninitialized chunk of memory.\n[standard](https://www.npmjs.com/package/standard) linter (among others) catch that and warn people\nto avoid using unsafe API.\n\nLet's now throw in `safe-buffer`!\n\n```console\n$ cat example.safe-buffer.js\nconst Buffer = require('safe-buffer').Buffer\nconsole.log(Buffer(20))\n$ standard example.safe-buffer.js\n$ ./node-v6.13.0-linux-x64/bin/node example.safe-buffer.js\n<Buffer 08 00 00 00 00 00 00 00 28 58 01 82 fe 7f 00 00 00 00 00 00>\n```\n\nSee the problem? Adding in `safe-buffer` _magically removes the lint warning_, but the behavior\nremains identiсal to what we had before, and when launched on Node.js 6.x LTS — this dumps out\nchunks of uninitialized memory.\n_And this code will still emit runtime warnings on Node.js 10.x and above._\n\nThat was done by design. I first considered changing `safe-buffer`, prohibiting old API usage or\nemitting warnings on it, but that significantly diverges from `safe-buffer` design. After some\ndiscussion, it was decided to move my approach into a separate package, and _this is that separate\npackage_.\n\nThis footgun is not imaginary — I observed top-downloaded packages doing that kind of thing,\n«fixing» the lint warning by blindly including `safe-buffer` without any actual changes.\n\nAlso in some cases, even if the API _was_ migrated to use of safe Buffer API — a random pull request\ncan bring unsafe Buffer API usage back to the codebase by adding new calls — and that could go\nunnoticed even if you have a linter prohibiting that (becase of the reason stated above), and even\npass CI. _I also observed that being done in popular packages._\n\nSome examples:\n * [webdriverio](https://github.com/webdriverio/webdriverio/commit/05cbd3167c12e4930f09ef7cf93b127ba4effae4#diff-124380949022817b90b622871837d56cR31)\n   (a module with 548 759 downloads/month),\n * [websocket-stream](https://github.com/maxogden/websocket-stream/commit/c9312bd24d08271687d76da0fe3c83493871cf61)\n   (218 288 d/m, fix in [maxogden/websocket-stream#142](https://github.com/maxogden/websocket-stream/pull/142)),\n * [node-serialport](https://github.com/node-serialport/node-serialport/commit/e8d9d2b16c664224920ce1c895199b1ce2def48c)\n   (113 138 d/m, fix in [node-serialport/node-serialport#1510](https://github.com/node-serialport/node-serialport/pull/1510)),\n * [karma](https://github.com/karma-runner/karma/commit/3d94b8cf18c695104ca195334dc75ff054c74eec)\n   (3 973 193 d/m, fix in [karma-runner/karma#2947](https://github.com/karma-runner/karma/pull/2947)),\n * [spdy-transport](https://github.com/spdy-http2/spdy-transport/commit/5375ac33f4a62a4f65bcfc2827447d42a5dbe8b1)\n   (5 970 727 d/m, fix in [spdy-http2/spdy-transport#53](https://github.com/spdy-http2/spdy-transport/pull/53)).\n * And there are a lot more over the ecosystem.\n\nI filed a PR at\n[mysticatea/eslint-plugin-node#110](https://github.com/mysticatea/eslint-plugin-node/pull/110) to\npartially fix that (for cases when that lint rule is used), but it is a semver-major change for\nlinter rules and presets, so it would take significant time for that to reach actual setups.\n_It also hasn't been released yet (2018-03-20)._\n\nAlso, `safer-buffer` discourages the usage of `.allocUnsafe()`, which is often done by a mistake.\nIt still supports it with an explicit concern barier, by placing it under\n`require('safer-buffer/dangereous')`.\n\n## But isn't throwing bad?\n\nNot really. It's an error that could be noticed and fixed early, instead of causing havoc later like\nunguarded `new Buffer()` calls that end up receiving user input can do.\n\nThis package affects only the files where `var Buffer = require('safer-buffer').Buffer` was done, so\nit is really simple to keep track of things and make sure that you don't mix old API usage with that.\nAlso, CI should hint anything that you might have missed.\n\nNew commits, if tested, won't land new usage of unsafe Buffer API this way.\n_Node.js 10.x also deals with that by printing a runtime depecation warning._\n\n### Would it affect third-party modules?\n\nNo, unless you explicitly do an awful thing like monkey-patching or overriding the built-in `Buffer`.\nDon't do that.\n\n### But I don't want throwing…\n\nThat is also fine!\n\nAlso, it could be better in some cases when you don't comprehensive enough test coverage.\n\nIn that case — just don't override `Buffer` and use\n`var SaferBuffer = require('safer-buffer').Buffer` instead.\n\nThat way, everything using `Buffer` natively would still work, but there would be two drawbacks:\n\n* `Buffer.from`/`Buffer.alloc` won't be polyfilled — use `SaferBuffer.from` and\n  `SaferBuffer.alloc` instead.\n* You are still open to accidentally using the insecure deprecated API — use a linter to catch that.\n\nNote that using a linter to catch accidential `Buffer` constructor usage in this case is strongly\nrecommended. `Buffer` is not overriden in this usecase, so linters won't get confused.\n\n## «Without footguns»?\n\nWell, it is still possible to do _some_ things with `Buffer` API, e.g. accessing `.buffer` property\non older versions and duping things from there. You shouldn't do that in your code, probabably.\n\nThe intention is to remove the most significant footguns that affect lots of packages in the\necosystem, and to do it in the proper way.\n\nAlso, this package doesn't protect against security issues affecting some Node.js versions, so for\nusage in your own production code, it is still recommended to update to a Node.js version\n[supported by upstream](https://github.com/nodejs/release#release-schedule).\n", "readmeFilename": "Readme.md"}