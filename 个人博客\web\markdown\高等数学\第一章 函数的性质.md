# 第一章 函数的性质

![Snipaste_2024-10-23_14-35-40](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-23_14-35-40.png)

### <u>特别注意奇偶性的判断前提条件是定义域关于原点对称</u>

![Snipaste_2024-10-23_14-44-09](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-23_14-44-09.png)

<u>可以记一下第三天条方便快速解题</u>

![Snipaste_2024-10-23_14-46-53](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-23_14-46-53.png)

![Snipaste_2024-10-23_14-51-50](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-23_14-51-50.png)

![Snipaste_2024-10-23_14-56-48](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-23_14-56-48.png)

例题说明了我们只要找一个可以让最大值可以不受限制的那个x范围或者x即可

![Snipaste_2024-10-23_15-00-35](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-23_15-00-35.png)

![Snipaste_2024-10-23_15-06-58](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-23_15-06-58.png)