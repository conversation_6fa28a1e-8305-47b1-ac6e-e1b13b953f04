{"_id": "cookie", "_rev": "142-b7d4264540db1e8a28a3f3551b6ab5d3", "name": "cookie", "dist-tags": {"latest": "1.0.2"}, "versions": {"0.0.0": {"name": "cookie", "version": "0.0.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.0.0", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a134b9c981df85c8a67b1620be5a36c0db1bdc63", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.0.tgz", "integrity": "sha512-dfZ/RKwTO5TsQSEAyfdwM0eaxt4/PAzuHUY4rnNrK53nuixwuozjlBjLxFPteUfBed9wjPHWlFy54vlcwt083w==", "signatures": [{"sig": "MEUCIQC/P176YciuQyqxJA4djoogRLFL37XRrb7q7lN6OPrRsAIgMJ4wD/+DcA9RUKc80cp1IaFzwTOnhNJTAAqJx1jhw78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "cookie parsing and serialization", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.x.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.1": {"name": "cookie", "version": "0.0.1", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.0.1", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3162dd34ea833740e2e0d6e7129f2dcd55dcf7ed", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.1.tgz", "integrity": "sha512-3Hx6vLDTn3UoJgYbSa27zIrNGbVN+W7LRZSBh7LCP9TeqM7EnmHQogrBxeD+Ge+aDWX3z9T8Mp2+fq470jPJMg==", "signatures": [{"sig": "MEUCIQDqsLWMjs5DewVN5DZXa+7ghh1GXxQvL+EioHSh7h9NAwIgJWwfcQ3pN0mNPQOmsMe/4mbmnr5lBpugP+a/m94F0Hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "cookie parsing and serialization", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.x.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.2": {"name": "cookie", "version": "0.0.2", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.0.2", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "17aedf62bc6af53745fecb55c45c3f097c2e858b", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.2.tgz", "integrity": "sha512-Tpfsfohu2GI3qFEqXH3bufaylHZM3CEuhZKane1Vtli9V8dxYk9L7n4sjxQKsewk7o+cjzVPQJAdTFEW2u9guQ==", "signatures": [{"sig": "MEUCIDeJSTIEWrTeSJN0QKzu0Z2cYid/YFuaM+UM46UhNukUAiEA+i4YY2i6d16uie0SPiXvngLbvTS6dm1RsTi+u6/r8uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "cookie parsing and serialization", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.x.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.3": {"name": "cookie", "version": "0.0.3", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.0.3", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "732b0e64cb77186954f5e36b0b6bcfd062a12e91", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.3.tgz", "integrity": "sha512-nuvBgiOhOcmfKwv5t+4TMKs/4NU8jSLg3wnjpwAkZcjFa3kritGQpMlfdom7ZDnxbYjUAl5UbIbRbJdmRKRcmA==", "signatures": [{"sig": "MEYCIQC8+mlRFbm3izUzszS+mBAJu2zHKc66SQYSBPSohIVU9gIhAIL59oZSoFdgPZtEgtTMzdu3WgJ0FQ66t6c0Ew848x6x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "cookie parsing and serialization", "directories": {}, "_nodeVersion": "v0.7.10-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.x.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.4": {"name": "cookie", "version": "0.0.4", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.0.4", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5456bd47aee2666eac976ea80a6105940483fe98", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.4.tgz", "integrity": "sha512-K4/8ihPVK55g3atBFCLcDWzHnrqZBawwjQnRGZ9A4Erg/uOmZY8b9n/tssKt4odxq3eK0HTQT6NVgtKvLSoKEg==", "signatures": [{"sig": "MEYCIQD/pedDJ0nsQaEryt0yVFVZPz4kd8R3hPdeCBLhkcfhOAIhAKmD48IZjM9eqfP5sngrbSkYpJ5GkHxAtyweENmZUpIt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "cookie parsing and serialization", "directories": {}, "_nodeVersion": "v0.6.20-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.x.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.5": {"name": "cookie", "version": "0.0.5", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.0.5", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f9acf9db57eb7568c9fcc596256b7bb22e307c81", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.5.tgz", "integrity": "sha512-STLsAHdtBDF5GJiPHc4sdfX5qzri6bcSxdSlW/o4IYJAA5yZxh3ZZsvctsKRNbhpP328sN+A2EjOF9vcW/LhdQ==", "signatures": [{"sig": "MEQCIGGLY4eyvIOKvApddSI2FnAA8poJEq1VJeYUO9wNq7vzAiBo4DF6Sxslnc3uw4+lZ0CLGuTSlboLlnoA2DCFBhd1Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "description": "cookie parsing and serialization", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "1.x.x"}, "optionalDependencies": {}}, "0.0.6": {"name": "cookie", "version": "0.0.6", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.0.6", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7bc6bb50205dcb98cf13ad09d6c60bc523f6fcb7", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.6.tgz", "integrity": "sha512-f8pdZ++OJo3HtU2lBP0f56fVN7Lgq/ICviGy2XrGDgC0v3wd/2mke5fGr0LM3PEOz2AyHlQ1CAb2i/PxDRlIsQ==", "signatures": [{"sig": "MEQCIB+mbAZzZasJThKnyjca4UNBeK6IOkQuZP8Ll+85QoHvAiAvyDjf274LVoGNPam0JRQzpqqJncYuUw+iOlTS2I/xIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "cookie parsing and serialization", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "1.x.x"}, "optionalDependencies": {}}, "0.1.0": {"name": "cookie", "version": "0.1.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.1.0", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "90eb469ddce905c866de687efc43131d8801f9d0", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.0.tgz", "integrity": "sha512-YSNOBX085/nzHvrTLEHYHoNdkvpLU1MPjU3r1IGawudZJjfuqnRNIFrcOJJ7bfwC+HWbHL1Y4yMkC0O+HWjV7w==", "signatures": [{"sig": "MEUCIEyl4WzqplYueG6EyhLjb/ZxuegmPKtsFccSOGjMgknuAiEA81GAYF9uBUQc9QNQdkwvQ4FUXaaKG+yQkTEfQsHvS2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "cookie parsing and serialization", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "1.x.x"}, "optionalDependencies": {}}, "0.1.1": {"name": "cookie", "version": "0.1.1", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.1.1", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/shtylman/node-cookie", "bugs": {"url": "https://github.com/shtylman/node-cookie/issues"}, "dist": {"shasum": "cbd4b537aa65f800b6c66ead2520ba8d6afbdf54", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.1.tgz", "integrity": "sha512-dHcrl81dfXXv+oyegOhauihVzLzrJdQx7XKE9o3nQ1UXNwMRm+phmODJy7S/KAhJj6rNvMR+58nDzoME4ZzCTg==", "signatures": [{"sig": "MEUCIQCR1Xcz2aLm3ho6h8XEVs//a4E8zW+7zK8FCzZoPISEvgIgNwzYklVe/n4iBw04luk6gIKjSs8ngvgQMHKn/fIY+38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "cookie parsing and serialization", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "1.x.x"}, "optionalDependencies": {}}, "0.1.2": {"name": "cookie", "version": "0.1.2", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cookie@0.1.2", "maintainers": [{"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/shtylman/node-cookie", "bugs": {"url": "https://github.com/shtylman/node-cookie/issues"}, "dist": {"shasum": "72fec3d24e48a3432073d90c12642005061004b1", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.2.tgz", "integrity": "sha512-+mHmWbhevLwkiBf7QcbZXHr0v4ZQQ/OgHk3fsQHrsMMiGzuvAmU/YMUR+ZfrO/BLAGIWFfx2Z7Oyso0tZR/wiA==", "signatures": [{"sig": "MEQCICTQusQhWIsNZacw+Ui1Kof9LKcZ6j60vk+dBPkkFm3iAiBnjenKjgi2tzUvNKMSR1ntefgQg8SEkmKoGu5kZYhfiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": "*"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/shtylman/node-cookie.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "cookie parsing and serialization", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "1.x.x"}, "optionalDependencies": {}}, "0.1.3": {"name": "cookie", "version": "0.1.3", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.1.3", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "e734a5c1417fce472d5aef82c381cabb64d1a435", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.3.tgz", "integrity": "sha512-mWkFhcL+HVG1KjeCjEBVJJ7s4sAGMLiBDFSDs4bzzvgLZt7rW8BhP6XV/8b1+pNvx/skd3yYxPuaF3Z6LlQzyw==", "signatures": [{"sig": "MEUCIQCCNZnnlhQ7wLLXXybUVfwXeWzmUf6oO6mLK/tVPQXAGQIgKk958UOvVPNCy0+nNSS0yjrVa4fhjbH+cBI4FhEX02I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "README.md", "index.js"], "_shasum": "e734a5c1417fce472d5aef82c381cabb64d1a435", "engines": {"node": "*"}, "gitHead": "f46097723c16f920a7b9759e154c34792e1d1a3b", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.x.x", "istanbul": "0.3.9"}}, "0.2.0": {"name": "cookie", "version": "0.2.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.2.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "9708beeaa361857de7d16516fea779572625caad", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.0.tgz", "integrity": "sha512-K+vrjxCYfXeM5wYEuBLkDsBdJz2yw1yDM3uAZ/I56sipmNr3ssNVrKfmko1z1N2c7Bu5ho9UFUR6nYCskH47xA==", "signatures": [{"sig": "MEYCIQDhdas3gc7457nYJM/VRudZSCFTlmtqdUncR5NiSwuIAAIhAOXYymPjHZXgALso0I9+CifcgiQP728MTbJboB+RC6X3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "9708beeaa361857de7d16516fea779572625caad", "engines": {"node": ">= 0.6"}, "gitHead": "e0d36be803099855dfa323de092eed97bec155bd", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.17"}}, "0.1.4": {"name": "cookie", "version": "0.1.4", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.1.4", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "4955c0bd32fffa83b7433586185875876ea04e4b", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.4.tgz", "integrity": "sha512-w2MOEWrqy1q7Z3iyP0eMGaSmoL6qyqH2+h7hv1eRB3e70hBdqHj39jp1nT5FxyePotIoStLu7FS9Mkp4JQoZGg==", "signatures": [{"sig": "MEQCIAmKwzauuefhhUhGVEoyzKu6nY592kU6dwLO1jEI/PnbAiAyQQ1rcO7ofo5CBTpLdiyNYApPO949Hv4iHTz1iYmL/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "4955c0bd32fffa83b7433586185875876ea04e4b", "engines": {"node": ">= 0.6"}, "gitHead": "337c0f1be395c1b62b8cae4306a745012c62a989", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.20"}}, "0.2.1": {"name": "cookie", "version": "0.2.1", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.2.1", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "e1bc7c07d1985c17ad7347502bac1a0eb072ac9a", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.1.tgz", "integrity": "sha512-a0nMJ599C1Gv3FJGasCIVCTJYaYep0znzxC14H+p6oTHQVx2ltZNu+G7PLbUu+QoYbCsY3K1d6juDhq34IBN9Q==", "signatures": [{"sig": "MEUCIQCQmLb09492r6nsbQkU+QqMtlq/3rBB4dyalbZqncGbmAIgHD8+06KIDR84jw4gOaEeWZXSEsOgRg4RN4ddvG0g5L8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "e1bc7c07d1985c17ad7347502bac1a0eb072ac9a", "engines": {"node": ">= 0.6"}, "gitHead": "f3b5262b23b8eb64c9cbebc6f6271894889b14b1", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.20"}}, "0.1.5": {"name": "cookie", "version": "0.1.5", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.1.5", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "6ab9948a4b1ae21952cd2588530a4722d4044d7c", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.5.tgz", "integrity": "sha512-/lhu+NGBI5pOLXILS07DrPXYX0QDD/ejVhbwoCUcLPBqMEK9b++f9rUhAlhLkcTz9mV6QSeD+w3cHJ96rMZaFQ==", "signatures": [{"sig": "MEUCIHzsfyFjLmIAYopsnPyC0myGoJ75IfBBhJI3IxCIo67pAiEAyiY2GdKtVwqEkjpKZlp9sh03omlXPD7AglgzKMgMCLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "6ab9948a4b1ae21952cd2588530a4722d4044d7c", "engines": {"node": ">= 0.6"}, "gitHead": "0dfc4876575cef2609cdc1082fccf832743822c2", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.20"}}, "0.2.2": {"name": "cookie", "version": "0.2.2", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.2.2", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "579ef8bc9b2d6f7e975a16bf4164d572e752e540", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.2.tgz", "integrity": "sha512-QT1/SH6oF6jrC9K4rlWpa/5FgqUZuh/Ohl4NvGAgSm67DsieBdTz/XsiVQwBKEJMnw7Tui5uBuC7k1yUAmPO2g==", "signatures": [{"sig": "MEYCIQD7f1pH5KyaghFHDU3Xdccvdt8DnoW4pZe8i7I3Tm8aIgIhALMkrHe5DuP/buJj/U6/ET0J1Xr4j02caX1JO+9wAg+E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "579ef8bc9b2d6f7e975a16bf4164d572e752e540", "engines": {"node": ">= 0.6"}, "gitHead": "9b481be547730c5f487364b720ab298d097541d5", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.20"}}, "0.2.3": {"name": "cookie", "version": "0.2.3", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.2.3", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "1a59536af68537a21178a01346f87cb059d2ae5c", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.3.tgz", "integrity": "sha512-mnzsjgoobV+vxz57A5ezsr9gFB4y90Yqsu2Go95nNuO/WBLLPH43gNCHzqcXG++JcP339z6IAGVo0g4qBfo6dg==", "signatures": [{"sig": "MEYCIQCErcpABARWT9TD47UBtyb5CdBXrt9oM8beMG/IFU/gYAIhAJ3w8l62NOwuXgDjCnVfgAIeqoMKJuoRRQrIB+0uQAbN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "1a59536af68537a21178a01346f87cb059d2ae5c", "engines": {"node": ">= 0.6"}, "gitHead": "35326af88e9665bb8ea1be280cb827523e9360a7", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.22"}}, "0.2.4": {"name": "cookie", "version": "0.2.4", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "a8c155aa7b9b2cf2c4d32ebc7b9a0aa288ccc6bd", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.4.tgz", "integrity": "sha512-wQLxYCPiulwnfcvEZHF8YVj6cxvkpOBFgN1nL3Ukgh+D1+4A1SUKHdxR7h+T9kcuC54mFWoeZdnLT7ZeIC9Emw==", "signatures": [{"sig": "MEQCIFd7QlvY9HrpU80uZlKOAV8rqWe1dyA5vgdnVabYJFLsAiATJ0Kni9GYp4N4e5GDUGfCceYIgjw0isSNVRGdahVtTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "a8c155aa7b9b2cf2c4d32ebc7b9a0aa288ccc6bd", "engines": {"node": ">= 0.6"}, "gitHead": "0c6fe48e2976d66ed73c03817bb5cb10180b50ee", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/cookie-0.2.4.tgz_1463790764235_0.7945549874566495", "host": "packages-16-east.internal.npmjs.com"}}, "0.3.0": {"name": "cookie", "version": "0.3.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "a4bdd609d86748a5ce6c64d7ede6f4840ba434d8", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.3.0.tgz", "integrity": "sha512-hPRNPncC/li8RRZh6m5gPYf6f4CrDZK9+JFLAhHAKAxABwRPCcOy98DQ0W1mvSQCL1LlyNI5MsR981n6EqHCZw==", "signatures": [{"sig": "MEUCIGhD+ZSRMtt5B/eMW/csD6/WWX98flradQ453TY3nKrVAiEAxcBP+nUeQSceuZ0F8p2yUwbS8eO+HBiSUpvEPjNSa+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "a4bdd609d86748a5ce6c64d7ede6f4840ba434d8", "engines": {"node": ">= 0.6"}, "gitHead": "91b733fbe29ae6fcfa305f8e8ff31a1c2e651feb", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP server cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/cookie-0.3.0.tgz_1464310839393_0.722841773647815", "host": "packages-12-west.internal.npmjs.com"}}, "0.3.1": {"name": "cookie", "version": "0.3.1", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.3.1.tgz", "integrity": "sha512-+IJOX0OqlHCszo2mBUq+SrEbCj6w7Kpffqx60zYbPTFaO4+yYgRjHwcZNpWvaTylDHaV7PPmBHzSecZiMhtPgw==", "signatures": [{"sig": "MEYCIQCgvOyvh7LyYbqRRfP+P1EWsK+Lihw9fWoempNpPWDXWAIhAPeJXOxCFkwj6kP3fd3Z6QuGtfKjMTN7OnaKjWkPjz1m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb", "engines": {"node": ">= 0.6"}, "gitHead": "e3c77d497d66c8b8d4b677b8954c1b192a09f0b3", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/cookie", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP server cookie parsing and serialization", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/cookie-0.3.1.tgz_1464323556714_0.6435900838114321", "host": "packages-12-west.internal.npmjs.com"}}, "0.4.0": {"name": "cookie", "version": "0.4.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "beb437e7022b3b6d49019d088665303ebe9c14ba", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.4.0.tgz", "fileCount": 5, "integrity": "sha512-+Hp8fLp57wnUSt0tY0tHEXh4voZRDnoIrZPqlo3DPiI4y9lwg/jqx+1Om94/W6ZaPDOUbnjOt/99w66zk+l1Xg==", "signatures": [{"sig": "MEUCICpL17EzZZiF4ZfyjmKXAdwEk2ngytW6WMM4zpa+8DakAiEAp5rStJSo+4RI2hj/KdFjVAbE3wQbuD8fCwOdLgJBAtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3NhPCRA9TVsSAnZWagAADA4P/0Y8HkoR7zm45iuEtjAd\nEfcXA4oFAC9rTJtAWNcmDGwTW1HxfIX3M9ci8WSjlwbEqWsvE6XJYq4yjhZq\ncwj7IRftR1aGaGusZPrBQTXnwqMvRKTFG4ZMwH/IeeoEtmBq9hJJZX8yXjNL\nZbaAq84JTiGriMb9kjS+sizB6dHMUrCN4SDTP6EUEAVuF6lIVrikG3G6i7am\nqXeFIVXJQFn31/MRV/258l6eOefuPTlgWqpiBBLSScxSOSsyLhW2+FTpZ1Ga\n8wqAhQVf0JATKrElCH5x2u2slNxOI8FltAwxfMFHzN/5Q2XlOFznqFqoO1l6\nEYF0NgJQZXBQuKWIAVIyqwBZ5fQ9+lhjKtVqCGbcw9I2U+TRK0eHMNDgI+pR\nebdSwAhVWGTp0o2ahqhQBW/CB+tPgIjgy0lPRg5ioPs9noBb6AZZ5H9I0Ffm\niWO1FsneQYbFL+2IM/P3rIQefwFTParMXRuq70XxuVcU+cTXNi4X8lzH8KiJ\ncnq3Pit6czof7+OvNBWQAIq++d6z1tMq+ELOqp7L0QogZl7OGI/nFtZQrTbn\n2VtTg4su5j/zkE350pwTXirnRkS/9AiignuLco2H9PspMxEadPnmmakwErZJ\n+KXAFLAHZhgxfa7meSbFFoYDarbpuizUzDDULbUM0murTVR2dAYZUnCByEoi\nvmIe\r\n=Oa0u\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "aec1177c7da67e3b3273df96cf476824dbc9ae09", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "8.16.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_0.4.0_1557977167056_0.23732140409492142", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "cookie", "version": "0.4.1", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "afd713fe26ebd21ba95ceb61f9a8116e50a537d1", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.4.1.tgz", "fileCount": 5, "integrity": "sha512-ZwrFkGJxUR3EIoXtO+yVE69Eb7KlixbaeAWfBQB9vVsNn/o+Yw69gBWSSDK825hQNdN+wF8zELf3dFNl/kxkUA==", "signatures": [{"sig": "MEQCIFraquf5MROagEGJWcsDul6SQ0y3192R88zCxcqMwnDJAiA9NKcmoUVWFR2xGonLOLbEWPMNdss4Kbu5UN57NN7MEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJen7jNCRA9TVsSAnZWagAAX/wP/j8Mcw3MQyifqWMFSZTL\nwpTBVC0/hRHh581NsIKtfF1fzWvRHKu+VPlGhMg1vqXoZweebf1H5Mwhlz7z\nDEiUiPQgCi6hXgdsUMxc+iDOFy2fQoPOKtY4LnfDA/vZMCok8QFLVWArl3CC\nLmA5Amh/Zfud0WkGzDGrjRQKWHRRAUVrQBZ6ziTM5KplDWy1PBSE59M3WYMY\nUFOdCEqk4xGEvW8YjkAkRSiY5Z8r8wcES4GkxaLsVFfpLJbI0cbRCen6b1uK\n4ZTGjwpnQKHPaywefIX1UTV8Em6D0QGweyc4CgTicMz+lPD9uZT8jmZr+Apy\nkcfen3enN1sKrn0e4p6AXOu1Ku38KeK0nLyU3ueo7/KzbAf8XVkbbsWdZ1Yr\nteOjmHDqv5k7B7616PSjyQJRmXhfB2FgR84MmyUbeEtOSStBaNPI7BxzA0Mj\nKvSfVDGxxO0dTb3Uu6QRF4tl8T2AFKarNzpik5VNvJpYsueISnGVG4J480YI\nz8L/Ayn3+BmW7GLtVU5nfXaMZBjGNryRRQFhju7tS0JOzbRgmUCRLK77NGUX\n+warQVVAXDJJgAVexSen90OLR+SSNq84HIBT9/UP70OLFxwndxB91XuDTuMO\nNot9PtZFUNZnv9ArYN/HGCRKDkJm98Gn0Pu5eJ4ITNA3iCE/urbSnVLzigSA\n11mU\r\n=IPIC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "b22458dd9f7ca94705fd7ee25780836601b913aa", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks --ui qunit test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "13.12.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.1", "mocha": "7.1.1", "eslint": "6.8.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_0.4.1_1587525837181_0.04343373246138138", "host": "s3://npm-registry-packages"}}, "0.4.2": {"name": "cookie", "version": "0.4.2", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "0e41f24de5ecf317947c82fc789e06a884824432", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.4.2.tgz", "fileCount": 5, "integrity": "sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==", "signatures": [{"sig": "MEUCIQCc/iA6Ebuxw296YF1n3NkFRXNlpdCsRtXiSoWAkUtTywIgQkXiuyTi+kj2rS87pqz9kE90NYgAGaJO6h95ZnWVcHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+xPaCRA9TVsSAnZWagAAKtwQAIuzkn+ot2Nzsnc6ZRYJ\nr/e1MNEBVeNVpmIae1RuGJQhXV58BWbbzSIMpN5kULGPHy4rJY0Fe+WQn17g\nilJMiQKd6CzDbylJTZWOALx7zamDL3HVravvrRgpsjMcix4VM7/v0trzDppy\nCRXx1JO+f1PcMkcnYQhSTODYIzf3cAcP2TXIXG5ZhRT6h15MkbBRSB1XrcHN\nNlobt7M8aYeoSDIMd5aC5wTWeyfp98unQwVvLzC2k2KbUaC9+dvSrKLRoAEl\n5dGUJNhV52FO2fQOWLinu4WmGLnXw7gG3SA1s56esZblSLvYtQhFmRapgE2Q\nqMP/zhAOR8qUM+XEp8e++ht7Nn4Cv1Il8DG1hqIhX0KrtgNS5kY+NBvB5PQG\nDXYcAg8HEntQwkCclubr/cuF3JN7igbuue+4lF9bx/h2nMay7J9mJn55Xc3G\nI9CqI/4SVZfViBiYLU9XodfRzBNkIjZINoaZNIhFpLmbAuvr2GWSbu4BXyL8\nuCd5eCLjtqUaSApazorqpChMefB4Q8nAkXA5WYIPKNC5wNwmEy62V3zrYU+4\nuMB+7s/1kNH6CaU80TQkFi+YP0TwIAxvX9V7++8aIMyGAoFMv0bxIbUJQ5VQ\nqTe+ILLzvcIqqwomwz1m7FZg4t/8NZI7MLxQs9zsrjnpJthcQ4B7d7bQWOBX\nSEcc\r\n=yR/c\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "55bac40d944e65554ecce9e5d567d17fb62d9ccc", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks --ui qunit test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "7.32.0", "benchmark": "2.1.4", "top-sites": "1.1.85", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "2.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_0.4.2_1643844569878_0.06540750379621363", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "cookie", "version": "0.5.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "d1f5d71adec6558c58f389987c366aa47e994f8b", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz", "fileCount": 6, "integrity": "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==", "signatures": [{"sig": "MEQCIHTyGhjx8nRt+f1Wkf5RFsF80x/VnzKUBa6tr07jrRCHAiA/qDBmYZDmUd+wGBg6BX6wbiLq+PG5JhA7t8k2aVfPIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVLptACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfQBAAmCi0G6edCjzcJvhorqbfM5Yfq2fTUyHQto5S49qphrYaZhAw\r\nVhmxClv2Uy0LFkdY/WMIsE9pihciNNiHfonfz4Eis+HxoUKaGYQHAayPGB/w\r\nGHK54F7ETV8dQrOao/YHrSjTiNvmXlBoMDym7M7GVBKCWw1XOSUDt393CGZJ\r\nbpppE/dsaTXwYdF1UillWZCyE1R0mYcMw837P19rrdnSA/tjjaq3fpfmZCEk\r\nEtXgq7PwDgKFtsap4NT67LcqnLQRBpY1VsK7O71qFWl7KN34haJTiGaBZIH6\r\ndzQIC9eU28IF+TqoR/zErnUy8d5NqEl4J1kSItKOuViuJz+T7EJFdLJ4aluB\r\n1FMnpGk4vGMz7fkJPJKGrZv4qraihUz/KOkGxbawVqcN+1AgwdbE3tX/dS97\r\nAlTRyZqCiBSkpQdMNeOnLhZSPHoEXsuKgmkSh8LFxCVzaRrt8VhnDizQrwPA\r\nv53ZChQnDNYh3wYSWHayt+SvFLG+waCByshO3B4LvkEHjmLrGhf9pvmikO5Z\r\naoCas8zVWrV5M0oCE/0cEcvKQOATMzvXJHWAceUvdCSjgC8bbVhr5sMoL60L\r\n83AmM3cCtL5RUomN2k4mcpvpOmQLXZkvXjv9jFt6uwaXsmbQu2jP72vXKfV8\r\n3oDJ66twILRP9sTA5NiBirpnWOgohtjCLfo=\r\n=xQmr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "663c9aeb85c9e046ff2ad2cdec631afdd40a7965", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "benchmark": "2.1.4", "top-sites": "1.1.97", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "2.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_0.5.0_1649719916997_0.8696374152245456", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "cookie", "version": "0.6.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "2798b04b071b0ecbff0dbb62a505a8efa4e19051", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.6.0.tgz", "fileCount": 6, "integrity": "sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==", "signatures": [{"sig": "MEQCIEL2HouINUZMTIik3Y3nbvRXXwWmfFN4K7ptI3x8KlV4AiAJDsHTaKH4r35aCzLz9tnjrAATCN7WAW351QYb2GvkNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23736}, "engines": {"node": ">= 0.6"}, "gitHead": "38323bad3aa04bce840103ff6075bc05cc0bf884", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.53.0", "benchmark": "2.1.4", "top-sites": "1.1.194", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_0.6.0_1699333269681_0.1654855182681345", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "cookie", "version": "0.7.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.7.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "2148f68a77245d5c2c0005d264bc3e08cfa0655d", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.7.0.tgz", "fileCount": 5, "integrity": "sha512-qCf+V4dtlNhSRXGAZatc1TasyFO6GjohcOul807YOb5ik3+kQSnb4d7iajeCL8QHaJ4uZEjCgiCJerKXwdRVlQ==", "signatures": [{"sig": "MEQCIG3V+4VnRNUAXH/4ujIS7bVCP4zbU7rhne7YZvr+1j7HAiA690soiDn7+r0NEwOnJ7ssi1ZBxVMpcYXQfhy1INprsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23253}, "main": "index.js", "engines": {"node": ">= 0.6"}, "gitHead": "ab057d6c06b94a7b1e3358e69a685ae49c97b627", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.53.0", "benchmark": "2.1.4", "top-sites": "1.1.194", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_0.7.0_1727910385662_0.8847838214673549", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "cookie", "version": "0.7.1", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.7.1", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "2f73c42142d5d5cf71310a74fc4ae61670e5dbc9", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz", "fileCount": 5, "integrity": "sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==", "signatures": [{"sig": "MEUCIDKL4lc0hvH9qowdRFxv5qw1rB0u+IxHTqUPE76emvk8AiEA+9e03z9xhRG3US5bb1Sbp8J4OJ2Scaa5p45AV4EXPxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23319}, "main": "index.js", "engines": {"node": ">= 0.6"}, "gitHead": "cf4658f492c5bd96aeaf5693c3500f8495031014", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.53.0", "benchmark": "2.1.4", "top-sites": "1.1.194", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_0.7.1_1727979437785_0.8068038504614219", "host": "s3://npm-registry-packages"}}, "0.7.2": {"name": "cookie", "version": "0.7.2", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@0.7.2", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "556369c472a2ba910f2979891b526b3436237ed7", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz", "fileCount": 5, "integrity": "sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==", "signatures": [{"sig": "MEQCIDm2q0PZlFEgY8XPuCXZEtK9ipN/2FVDL23gDjKWfQrUAiAyqfSsVQIrmonFoOR0wyDolkwCbU/PyQaDeLT6zBKrRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23382}, "main": "index.js", "engines": {"node": ">= 0.6"}, "gitHead": "d19eaa1a2bb9ca43ac0951edd852ba4e88e410e0", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "update-bench": "node scripts/update-benchmark.js"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.53.0", "benchmark": "2.1.4", "top-sites": "1.1.194", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_0.7.2_1728272488623_0.4466938835350256", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "cookie", "version": "1.0.0", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@1.0.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "1eee6e952e3e06f79434699fe8370c48f3a5480b", "tarball": "https://registry.npmjs.org/cookie/-/cookie-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-bsSztFoaR8bw9MlFCrTHzc1wOKCUKOBsbgFdoDilZDkETAOOjKSqV7L+EQLbTaylwvZasd9vM4MGKotJaUfSpA==", "signatures": [{"sig": "MEUCIQCSWO5E6xquddNmVD/5YhvZeEgZaPa0FtqfGib/x1xcegIgckiRbstZVH/vB/bvdAJL1fklCf5z4X8LW33NqTBx1QY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45181}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=18"}, "gitHead": "c69cef646e790823f8345ec9b44aba454ceb93fd", "scripts": {"test": "ts-scripts test", "bench": "vitest bench", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "ts-scripts": {"project": "tsconfig.build.json"}, "_npmVersion": "10.8.3", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"vitest": "^2.1.2", "top-sites": "1.1.194", "typescript": "^5.6.2", "@vitest/coverage-v8": "^2.1.2", "@borderless/ts-scripts": "^0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_1.0.0_1728426301364_0.4066358865030213", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "cookie", "version": "1.0.1", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@1.0.1", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "e1a00d20420e0266aff817815640289eef142751", "tarball": "https://registry.npmjs.org/cookie/-/cookie-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-Xd8lFX4LM9QEEwxQpF9J9NTUh8pmdJO0cyRJhFiDoLTk2eH8FXlRv2IFGYVadZpqI3j8fhNrSdKCeYPxiAhLXw==", "signatures": [{"sig": "MEYCIQDVTuh4DamUmCfnuSBittKylpinhjLm9a9QxLsf4feZOwIhAIXyoaPvc1c8JvB5sD9bxYHhtqA/3xhEfNQAqat9k9cp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45951}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=18"}, "gitHead": "ba9e6775ad28e6fd4e88c27aadee93cf2b0d7a1d", "scripts": {"test": "ts-scripts test", "bench": "vitest bench", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "ts-scripts": {"project": "tsconfig.build.json"}, "_npmVersion": "10.8.3", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"vitest": "^2.1.2", "top-sites": "1.1.194", "typescript": "^5.6.2", "@vitest/coverage-v8": "^2.1.2", "@borderless/ts-scripts": "^0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_1.0.1_1728667115103_0.1047538322453021", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "cookie", "version": "1.0.2", "keywords": ["cookie", "cookies"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie@1.0.2", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/cookie#readme", "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "dist": {"shasum": "27360701532116bd3f1f9416929d176afe1e4610", "tarball": "https://registry.npmjs.org/cookie/-/cookie-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==", "signatures": [{"sig": "MEUCIFlKqps2ND6I4CX3YDlEtWCW8vPlgDkWJ/ziBqPKA8vsAiEAu5TRmr/L4SkTfRNslDFoy38yNtn1Tn3MIhIEQ7uTnp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46521}, "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=18"}, "gitHead": "e739f419e56442b754e4fea6dbcf98c1c8d00dda", "scripts": {"test": "ts-scripts test", "bench": "vitest bench", "build": "ts-scripts build", "specs": "ts-scripts specs", "format": "ts-scripts format", "prepare": "ts-scripts install", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "ts-scripts": {"project": "tsconfig.build.json"}, "_npmVersion": "10.8.3", "description": "HTTP server cookie parsing and serialization", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"vitest": "^2.1.2", "top-sites": "1.1.194", "typescript": "^5.6.2", "@vitest/coverage-v8": "^2.1.2", "@borderless/ts-scripts": "^0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/cookie_1.0.2_1732124387563_0.2704754858994214", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-05-28T23:56:11.938Z", "modified": "2025-05-14T14:55:58.052Z", "0.0.0": "2012-05-28T23:56:12.299Z", "0.0.1": "2012-05-29T02:15:56.897Z", "0.0.2": "2012-06-01T17:57:58.161Z", "0.0.3": "2012-06-06T18:50:00.041Z", "0.0.4": "2012-06-21T16:27:06.621Z", "0.0.5": "2012-10-29T17:26:30.049Z", "0.0.6": "2013-04-09T05:59:56.056Z", "0.1.0": "2013-05-01T19:18:22.075Z", "0.1.1": "2014-02-23T15:56:33.086Z", "0.1.2": "2014-04-16T23:00:21.566Z", "0.1.3": "2015-05-20T01:22:20.719Z", "0.2.0": "2015-08-14T05:15:35.455Z", "0.1.4": "2015-09-17T17:03:42.289Z", "0.2.1": "2015-09-17T17:08:41.911Z", "0.1.5": "2015-09-17T18:52:10.481Z", "0.2.2": "2015-09-17T20:40:15.826Z", "0.2.3": "2015-10-26T01:02:06.233Z", "0.2.4": "2016-05-21T00:32:45.246Z", "0.3.0": "2016-05-27T01:00:41.646Z", "0.3.1": "2016-05-27T04:32:39.156Z", "0.4.0": "2019-05-16T03:26:07.333Z", "0.4.1": "2020-04-22T03:23:57.297Z", "0.4.2": "2022-02-02T23:29:30.095Z", "0.5.0": "2022-04-11T23:31:57.179Z", "0.6.0": "2023-11-07T05:01:09.857Z", "0.7.0": "2024-10-02T23:06:25.847Z", "0.7.1": "2024-10-03T18:17:17.990Z", "0.7.2": "2024-10-07T03:41:28.828Z", "1.0.0": "2024-10-08T22:25:01.600Z", "1.0.1": "2024-10-11T17:18:35.310Z", "1.0.2": "2024-11-20T17:39:47.758Z"}, "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/cookie#readme", "keywords": ["cookie", "cookies"], "repository": {"url": "git+https://github.com/jshttp/cookie.git", "type": "git"}, "description": "HTTP server cookie parsing and serialization", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# cookie\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Build Status][ci-image]][ci-url]\n[![Coverage Status][coverage-image]][coverage-url]\n\nBasic HTTP cookie parser and serializer for HTTP servers.\n\n## Installation\n\n```sh\n$ npm install cookie\n```\n\n## API\n\n```js\nconst cookie = require(\"cookie\");\n// import * as cookie from 'cookie';\n```\n\n### cookie.parse(str, options)\n\nParse a HTTP `Cookie` header string and returning an object of all cookie name-value pairs.\nThe `str` argument is the string representing a `Cookie` header value and `options` is an\noptional object containing additional parsing options.\n\n```js\nconst cookies = cookie.parse(\"foo=bar; equation=E%3Dmc%5E2\");\n// { foo: 'bar', equation: 'E=mc^2' }\n```\n\n#### Options\n\n`cookie.parse` accepts these properties in the options object.\n\n##### decode\n\nSpecifies a function that will be used to decode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\nSince the value of a cookie has a limited character set (and must be a simple string), this function can be used to decode\na previously-encoded cookie value into a JavaScript string.\n\nThe default function is the global `decodeURIComponent`, wrapped in a `try..catch`. If an error\nis thrown it will return the cookie's original value. If you provide your own encode/decode\nscheme you must ensure errors are appropriately handled.\n\n### cookie.serialize(name, value, options)\n\nSerialize a cookie name-value pair into a `Set-Cookie` header string. The `name` argument is the\nname for the cookie, the `value` argument is the value to set the cookie to, and the `options`\nargument is an optional object containing additional serialization options.\n\n```js\nconst setCookie = cookie.serialize(\"foo\", \"bar\");\n// foo=bar\n```\n\n#### Options\n\n`cookie.serialize` accepts these properties in the options object.\n\n##### encode\n\nSpecifies a function that will be used to encode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\nSince value of a cookie has a limited character set (and must be a simple string), this function can be used to encode\na value into a string suited for a cookie's value, and should mirror `decode` when parsing.\n\nThe default function is the global `encodeURIComponent`.\n\n##### maxAge\n\nSpecifies the `number` (in seconds) to be the value for the [`Max-Age` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.2).\n\nThe [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n`maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\nso if both are set, they should point to the same date and time.\n\n##### expires\n\nSpecifies the `Date` object to be the value for the [`Expires` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.1).\nWhen no expiration is set clients consider this a \"non-persistent cookie\" and delete it the current session is over.\n\nThe [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n`maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\nso if both are set, they should point to the same date and time.\n\n##### domain\n\nSpecifies the value for the [`Domain` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.3).\nWhen no domain is set clients consider the cookie to apply to the current domain only.\n\n##### path\n\nSpecifies the value for the [`Path` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.4).\nWhen no path is set, the path is considered the [\"default path\"](https://tools.ietf.org/html/rfc6265#section-5.1.4).\n\n##### httpOnly\n\nEnables the [`HttpOnly` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.6).\nWhen enabled, clients will not allow client-side JavaScript to see the cookie in `document.cookie`.\n\n##### secure\n\nEnables the [`Secure` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.5).\nWhen enabled, clients will only send the cookie back if the browser has a HTTPS connection.\n\n##### partitioned\n\nEnables the [`Partitioned` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-cutler-httpbis-partitioned-cookies/).\nWhen enabled, clients will only send the cookie back when the current domain _and_ top-level domain matches.\n\nThis is an attribute that has not yet been fully standardized, and may change in the future.\nThis also means clients may ignore this attribute until they understand it. More information\nabout can be found in [the proposal](https://github.com/privacycg/CHIPS).\n\n##### priority\n\nSpecifies the value for the [`Priority` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n\n- `'low'` will set the `Priority` attribute to `Low`.\n- `'medium'` will set the `Priority` attribute to `Medium`, the default priority when not set.\n- `'high'` will set the `Priority` attribute to `High`.\n\nMore information about priority levels can be found in [the specification](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n\n##### sameSite\n\nSpecifies the value for the [`SameSite` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n\n- `true` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n- `'lax'` will set the `SameSite` attribute to `Lax` for lax same site enforcement.\n- `'none'` will set the `SameSite` attribute to `None` for an explicit cross-site cookie.\n- `'strict'` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n\nMore information about enforcement levels can be found in [the specification](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n\n## Example\n\nThe following example uses this module in conjunction with the Node.js core HTTP server\nto prompt a user for their name and display it back on future visits.\n\n```js\nvar cookie = require(\"cookie\");\nvar escapeHtml = require(\"escape-html\");\nvar http = require(\"http\");\nvar url = require(\"url\");\n\nfunction onRequest(req, res) {\n  // Parse the query string\n  var query = url.parse(req.url, true, true).query;\n\n  if (query && query.name) {\n    // Set a new cookie with the name\n    res.setHeader(\n      \"Set-Cookie\",\n      cookie.serialize(\"name\", String(query.name), {\n        httpOnly: true,\n        maxAge: 60 * 60 * 24 * 7, // 1 week\n      }),\n    );\n\n    // Redirect back after setting cookie\n    res.statusCode = 302;\n    res.setHeader(\"Location\", req.headers.referer || \"/\");\n    res.end();\n    return;\n  }\n\n  // Parse the cookies on the request\n  var cookies = cookie.parse(req.headers.cookie || \"\");\n\n  // Get the visitor name set in the cookie\n  var name = cookies.name;\n\n  res.setHeader(\"Content-Type\", \"text/html; charset=UTF-8\");\n\n  if (name) {\n    res.write(\"<p>Welcome back, <b>\" + escapeHtml(name) + \"</b>!</p>\");\n  } else {\n    res.write(\"<p>Hello, new visitor!</p>\");\n  }\n\n  res.write('<form method=\"GET\">');\n  res.write(\n    '<input placeholder=\"enter your name\" name=\"name\"> <input type=\"submit\" value=\"Set Name\">',\n  );\n  res.end(\"</form>\");\n}\n\nhttp.createServer(onRequest).listen(3000);\n```\n\n## Testing\n\n```sh\nnpm test\n```\n\n## Benchmark\n\n```sh\nnpm run bench\n```\n\n```\n     name                   hz     min     max    mean     p75     p99    p995    p999     rme  samples\n   · simple       8,566,313.09  0.0000  0.3694  0.0001  0.0001  0.0002  0.0002  0.0003  ±0.64%  4283157   fastest\n   · decode       3,834,348.85  0.0001  0.2465  0.0003  0.0003  0.0003  0.0004  0.0006  ±0.38%  1917175\n   · unquote      8,315,355.96  0.0000  0.3824  0.0001  0.0001  0.0002  0.0002  0.0003  ±0.72%  4157880\n   · duplicates   1,944,765.97  0.0004  0.2959  0.0005  0.0005  0.0006  0.0006  0.0008  ±0.24%   972384\n   · 10 cookies     675,345.67  0.0012  0.4328  0.0015  0.0015  0.0019  0.0020  0.0058  ±0.75%   337673\n   · 100 cookies     61,040.71  0.0152  0.4092  0.0164  0.0160  0.0196  0.0228  0.2260  ±0.71%    30521   slowest\n   ✓ parse top-sites (15) 22945ms\n     name                                  hz     min     max    mean     p75     p99    p995    p999     rme   samples\n   · parse accounts.google.com   7,164,349.17  0.0000  0.0929  0.0001  0.0002  0.0002  0.0002  0.0003  ±0.09%   3582184\n   · parse apple.com             7,817,686.84  0.0000  0.6048  0.0001  0.0001  0.0002  0.0002  0.0003  ±1.05%   3908844\n   · parse cloudflare.com        7,189,841.70  0.0000  0.0390  0.0001  0.0002  0.0002  0.0002  0.0003  ±0.06%   3594921\n   · parse docs.google.com       7,051,765.61  0.0000  0.0296  0.0001  0.0002  0.0002  0.0002  0.0003  ±0.06%   3525883\n   · parse drive.google.com      7,349,104.77  0.0000  0.0368  0.0001  0.0001  0.0002  0.0002  0.0003  ±0.05%   3674553\n   · parse en.wikipedia.org      1,929,909.49  0.0004  0.3598  0.0005  0.0005  0.0007  0.0007  0.0012  ±0.16%    964955\n   · parse linkedin.com          2,225,658.01  0.0003  0.0595  0.0004  0.0005  0.0005  0.0005  0.0006  ±0.06%   1112830\n   · parse maps.google.com       4,423,511.68  0.0001  0.0942  0.0002  0.0003  0.0003  0.0003  0.0005  ±0.08%   2211756\n   · parse microsoft.com         3,387,601.88  0.0002  0.0725  0.0003  0.0003  0.0004  0.0004  0.0005  ±0.09%   1693801\n   · parse play.google.com       7,375,980.86  0.0000  0.1994  0.0001  0.0001  0.0002  0.0002  0.0003  ±0.12%   3687991\n   · parse support.google.com    4,912,267.94  0.0001  2.8958  0.0002  0.0002  0.0003  0.0003  0.0005  ±1.28%   2456134\n   · parse www.google.com        3,443,035.87  0.0002  0.2783  0.0003  0.0003  0.0004  0.0004  0.0007  ±0.51%   1721518\n   · parse youtu.be              1,910,492.87  0.0004  0.3490  0.0005  0.0005  0.0007  0.0007  0.0011  ±0.46%    955247\n   · parse youtube.com           1,895,082.62  0.0004  0.7454  0.0005  0.0005  0.0006  0.0007  0.0013  ±0.64%    947542   slowest\n   · parse example.com          21,582,835.27  0.0000  0.1095  0.0000  0.0000  0.0001  0.0001  0.0001  ±0.13%  10791418\n```\n\n## References\n\n- [RFC 6265: HTTP State Management Mechanism](https://tools.ietf.org/html/rfc6265)\n- [Same-site Cookies](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7)\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://img.shields.io/github/actions/workflow/status/jshttp/cookie/ci.yml\n[ci-url]: https://github.com/jshttp/cookie/actions/workflows/ci.yml?query=branch%3Amaster\n[coverage-image]: https://img.shields.io/codecov/c/github/jshttp/cookie/master\n[coverage-url]: https://app.codecov.io/gh/jshttp/cookie\n[npm-downloads-image]: https://img.shields.io/npm/dm/cookie\n[npm-url]: https://npmjs.org/package/cookie\n[npm-version-image]: https://img.shields.io/npm/v/cookie\n", "readmeFilename": "README.md", "users": {"285858315": true, "wut": true, "awzm": true, "dofy": true, "wayn": true, "akiva": true, "demod": true, "m42am": true, "panlw": true, "waidd": true, "bumsuk": true, "grncdr": true, "ilex.h": true, "kungkk": true, "monjer": true, "shanyy": true, "tedyhy": true, "trotyl": true, "vjudge": true, "x_soth": true, "zoluzo": true, "antanst": true, "dac2205": true, "flyslow": true, "hbkapps": true, "oikewll": true, "rhedenk": true, "roryrjb": true, "rparris": true, "vboctor": true, "wenbing": true, "alexchao": true, "dexteryy": true, "dgarlitt": true, "frankl83": true, "koulmomo": true, "mhaidarh": true, "qbylucky": true, "qiuzuhui": true, "wkaifang": true, "zuojiang": true, "abuelwafa": true, "antixrist": true, "fgribreau": true, "grin_zhou": true, "milfromoz": true, "nickeljew": true, "qinyifeng": true, "zuizuihao": true, "dennisli87": true, "giussa_dan": true, "goodseller": true, "justinliao": true, "mfessenden": true, "princetoad": true, "shuoshubao": true, "simplyianm": true, "vcordero07": true, "bphanikumar": true, "hongbo-miao": true, "masanorinyo": true, "ovuncozturk": true, "priyaranjan": true, "danielsunami": true, "infernocloud": true, "intuitivcloud": true, "markthethomas": true, "shen-weizhong": true, "imaginegenesis": true, "xiaoqiang.yang": true}}