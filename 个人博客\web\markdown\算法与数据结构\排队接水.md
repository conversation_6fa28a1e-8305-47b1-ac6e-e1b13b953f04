[排队接水问题]([排队接水 - 洛谷 P1223 - Virtual Judge](https://vjudge.net/problem/洛谷-P1223))

题目：排队接水

```
不适用接水时间相同的情况#include<iostream>
#include<algorithm>
using namespace std;
const int N = 105, M = 1e6 + 10;

int a[N], b[M], avg, n;

int main()
{
    cin >> n;
    for (int i = 1;i <= n;i++)
    {
        cin >> a[i];
        b[a[i]] = i;
    }//桶排只适合数据不同的情况下，不然会映射到同一个下标
    sort(a + 1, a + 1 + n);
    for (int i = 1;i <= n;i++)
    {
        cout << b[a[i]] << ' ';
    }
    return 0;
}不适用接水时间相同的情况

```

正确解法：使用结构体数组:可以同时存储多个变量，这样就可以得到多个变量叠加的效果,在定义cmp的情况下使用快排会简单的多,这题中就用到了定义x和num，num可以记录结构体数组的下标，相当于num和x一块跑，快排之后原来的下标还是可以通过结构体的num找到;

```c++
#include<iostream>
#include<algorithm>
using namespace std;

struct a {
    int x, num;
    //x记录接水时间，Num记录下标
};
bool cmp(a x, a y)
{
	return x.x < y.x;
}
int main()
{
    int n;
     double  avg = 0;
    cin >> n;
    struct a a[1010];
    for (int i = 1;i <= n;i++)
    {
        cin >> a[i].x;
        a[i].num = i;
    }
    sort(a + 1, a + 1 + n, cmp);
    for (int i=1;i <= n;i++)
    {
        cout << a[i].num << ' ';
        if (i != n)avg += a[i].x * (n - i);
    }
    cout << endl;
    printf("%.2lf", avg / n);
    return 0;
}
```

