#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define MAX_PROCESSES 10
#define MAX_INSTRUCTIONS 100

typedef enum
{
    CALC,
    INPUT,
    OUTPUT,
    WAIT,
    HALT
} InstructionType;

typedef struct
{
    InstructionType type;
    int runtime;
} Instruction;

typedef struct
{
    char processName[10];
    Instruction instructions[MAX_INSTRUCTIONS];
    int instructionCount;
    int remainingTime;
} PCB;

typedef struct
{
    PCB *queue[MAX_PROCESSES];
    int front;
    int rear;
} Queue;

Queue readyQueue, inputQueue, outputQueue, waitQueue;
int timeSlice;

void initQueue(Queue *q)
{
    q->front = q->rear = -1;
}

int isQueueEmpty(Queue *q)
{
    return q->front == -1;
}

void enqueue(Queue *q, PCB *pcb)
{
    if (q->rear == MAX_PROCESSES - 1)
        return;
    if (q->front == -1)
        q->front = 0;
    q->queue[++q->rear] = pcb;
}

PCB *dequeue(Queue *q)
{
    if (isQueueEmpty(q))
        return NULL;
    PCB *pcb = q->queue[q->front];
    if (q->front == q->rear)
        q->front = q->rear = -1;
    else
        q->front++;
    return pcb;
}

void loadProcesses(const char *fileName)
{
    FILE *file = fopen(fileName, "r");
    if (!file)
    {
        perror("Error opening file");
        exit(1);
    }

    char line[100];
    PCB *currentPCB = NULL;

    while (fgets(line, sizeof(line), file))
    {
        if (line[0] == 'P')
        {
            if (currentPCB)
            {
                enqueue(&readyQueue, currentPCB);
            }
            currentPCB = (PCB *)malloc(sizeof(PCB));
            strncpy(currentPCB->processName, line, 9);
            currentPCB->processName[9] = '\0';
            currentPCB->instructionCount = 0;
            currentPCB->remainingTime = 0;
        }
        else if (currentPCB)
        {
            char type = line[0];
            int runtime = atoi(line + 1);

            InstructionType instructionType;
            switch (type)
            {
            case 'C':
                instructionType = CALC;
                break;
            case 'I':
                instructionType = INPUT;
                break;
            case 'O':
                instructionType = OUTPUT;
                break;
            case 'W':
                instructionType = WAIT;
                break;
            case 'H':
                instructionType = HALT;
                break;
            default:
                fprintf(stderr, "Unknown instruction type: %c\n", type);
                exit(1);
            }

            currentPCB->instructions[currentPCB->instructionCount++] = (Instruction){instructionType, runtime};
        }
    }

    if (currentPCB)
    {
        enqueue(&readyQueue, currentPCB);
    }

    fclose(file);
}

void processWaitQueues()
{
    while (!isQueueEmpty(&inputQueue))
        enqueue(&readyQueue, dequeue(&inputQueue));
    while (!isQueueEmpty(&outputQueue))
        enqueue(&readyQueue, dequeue(&outputQueue));
    while (!isQueueEmpty(&waitQueue))
        enqueue(&readyQueue, dequeue(&waitQueue));
}

void runScheduler()
{
    FILE *logFile = fopen("F:\\scheduler_log.txt", "w");
    if (!logFile)
    {
        perror("Error opening log file");
        exit(1);
    }

    while (!isQueueEmpty(&readyQueue) || !isQueueEmpty(&inputQueue) ||
           !isQueueEmpty(&outputQueue) || !isQueueEmpty(&waitQueue))
    {
        PCB *currentPCB = dequeue(&readyQueue);
        if (currentPCB && currentPCB->instructionCount > 0)
        {
            Instruction *currentInstruction = &currentPCB->instructions[0];
            fprintf(logFile, "Running process: %s, Instruction: %d\n",
                    currentPCB->processName, currentInstruction->type);

            if (currentInstruction->type == CALC)
            {
                int executionTime = (currentInstruction->runtime < timeSlice) ? currentInstruction->runtime : timeSlice;
                currentInstruction->runtime -= executionTime;
                if (currentInstruction->runtime <= 0)
                {
                    memmove(&currentPCB->instructions[0], &currentPCB->instructions[1],
                            (currentPCB->instructionCount - 1) * sizeof(Instruction));
                    currentPCB->instructionCount--;
                }
                if (currentPCB->instructionCount > 0)
                {
                    enqueue(&readyQueue, currentPCB);
                }
            }
            else
            {
                memmove(&currentPCB->instructions[0], &currentPCB->instructions[1],
                        (currentPCB->instructionCount - 1) * sizeof(Instruction));
                currentPCB->instructionCount--;
                switch (currentInstruction->type)
                {
                case INPUT:
                    enqueue(&inputQueue, currentPCB);
                    break;
                case OUTPUT:
                    enqueue(&outputQueue, currentPCB);
                    break;
                case WAIT:
                    enqueue(&waitQueue, currentPCB);
                    break;
                case HALT:
                    fprintf(logFile, "Process %s completed.\n", currentPCB->processName);
                    break;
                }
            }
        }
        processWaitQueues();
    }

    fclose(logFile);
}

int main()
{
    initQueue(&readyQueue);
    initQueue(&inputQueue);
    initQueue(&outputQueue);
    initQueue(&waitQueue);
    timeSlice = 10;

    loadProcesses("F:\\prc.txt");
    runScheduler();

    return 0;
}
