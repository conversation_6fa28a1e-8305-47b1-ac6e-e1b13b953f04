{"_id": "safe-buffer", "_rev": "23-3fc891eb319fa3f8f089206cf443808c", "name": "safe-buffer", "description": "Safer Node.js Buffer API", "dist-tags": {"latest": "5.2.1"}, "versions": {"1.0.0": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "1.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "dependencies": {}, "devDependencies": {"standard": "^5.0.0", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "gitHead": "b8aaf15d48a29fdc5386d902631f9befd52a9db3", "_id": "safe-buffer@1.0.0", "_shasum": "10ac4127f54c368becb6879dcae2a2fb96571fa3", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "10ac4127f54c368becb6879dcae2a2fb96571fa3", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-1.0.0.tgz", "integrity": "sha512-vgxeFv39rfUutm1H45JjUJB/wox9Fnslbk/94tPaAAC6/EGQdHWubRARFqkg6spN2zjIRUICcLOW8MKYw0GXDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAnOluPRB7CEQyBw4UbQ9tDQ4mZqXvJWqa/ZMxJjLFR8AiEAtiEzDxnAyOhWgCa71fAIpvJl0ZyPgfOzmZkF8KQnH+U="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "2.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "dependencies": {}, "devDependencies": {"standard": "^5.0.0", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "gitHead": "79f40c926d9dcba272d9dc3acf365ee715857d51", "_id": "safe-buffer@2.0.0", "_shasum": "311d4a0deefdaa9f8d883fe898bdb04d12d3f14b", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "311d4a0deefdaa9f8d883fe898bdb04d12d3f14b", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-2.0.0.tgz", "integrity": "sha512-8xF0RbOs4lJ2SeELUo93ZQ/Qjknlhb7UVgOo/ghwmZjOPQhTblJw7aUzfeM+EY9DotvGjloeFAb0uyLlMbCTAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9Sxtwfp1PYVF6s8ePF4D8S1ZWD9FlT1pG7Q4Amrk5xwIgTZSk7yxnTxyWG6BBwbehMypBzeRSkAIQ5OBZ4oA9ND0="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "3.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "dependencies": {}, "devDependencies": {"standard": "^5.0.0", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "gitHead": "c3caf505557a60807b44aa97197d01251c192abb", "_id": "safe-buffer@3.0.0", "_shasum": "ab7065a989e2634fb0946df00368343258a759fe", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "ab7065a989e2634fb0946df00368343258a759fe", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-3.0.0.tgz", "integrity": "sha512-8j1opxLx/fFg0Q2JzfDJi+b4+v6XXovigDWFwOzqkhUYSG95QQ4Pxr2uof/PZQ5AzoCO8T+7MsNOAPZnC4hntA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH8SI/87pH7vEdTEqkBU2SDXv/bjvxP8RO8zM79eRKZyAiEAjCS/KtIFIV8klWx3zw2wxyCgenXMnqI6Ouuvj78ETDQ="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "4.0.0": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "dependencies": {}, "devDependencies": {"standard": "^7.0.0", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "gitHead": "481d1d9cd7bf6f392e1881f7b381f1c68ddaad0a", "_id": "safe-buffer@4.0.0", "_shasum": "b725368f0ec0e8a6faa9e657db34a3d2b332d680", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "b725368f0ec0e8a6faa9e657db34a3d2b332d680", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-4.0.0.tgz", "integrity": "sha512-Ci6s3yzedh0WRvYZU53d7aCD+xS4XCPLLtW0eXoNwxz2dDR/fbD13+txkgj0tVdIZmlGU62ok1nEvrPtYHSKgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCszv5orMA24SAXwuvTc+NlDNDmKyMCJX3Ugg+WOYjMTwIhAI5ehCBQrboZY3Tm9yNvbW3pEeArGP2OkYELX2rSQPZh"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/safe-buffer-4.0.0.tgz_1464584289130_0.17331634392030537"}, "directories": {}}, "5.0.0": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "5.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "browser": "./browser.js", "devDependencies": {"standard": "^7.0.0", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "gitHead": "ffb629c9ca70d66339f58a6441082184a9299634", "_id": "safe-buffer@5.0.0", "_shasum": "a59971ca637f5bcb622c47238a5e1e2c7751f354", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "a59971ca637f5bcb622c47238a5e1e2c7751f354", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.0.0.tgz", "integrity": "sha512-XDdtwCv6/bcE/h1LIpVc+SsgOZD5BcYeyXTIu2z0+jakJ8POhS6QMOv9AhWlnR/lLkBM3mx/rHFFYDYGpRDnyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDAOxDR6+XUpH2nq+mGv/dXhFBWySvcTHAB3jR1Gm54KwIhAN2/7leuH1RUuxWoVBw5/82iqD8xpSlrYY84N41+8yDq"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/safe-buffer-5.0.0.tgz_1464587148074_0.3391482220031321"}, "directories": {}}, "5.0.1": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "5.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "browser": "./browser.js", "devDependencies": {"standard": "^7.0.0", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "gitHead": "1e371a367da962afae2bebc527b50271c739d28c", "_id": "safe-buffer@5.0.1", "_shasum": "d263ca54696cd8a306b5ca6551e92de57918fbe7", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "d263ca54696cd8a306b5ca6551e92de57918fbe7", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.0.1.tgz", "integrity": "sha512-cr7dZWLwOeaFBLTIuZeYdkfO7UzGIKhjYENJFAxUOMKWGaWDm2nJM2rzxNRm5Owu0DH3ApwNo6kx5idXZfb/Iw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF0E+2ZjtntwcdmaHTTxnUuQ1fjr/zcEWA40Q0GF9O3lAiEAhOp5xBy96vV0xgloeoWGKmoIcDQjAfabod6TlefiMSc="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/safe-buffer-5.0.1.tgz_1464588482081_0.8112505874596536"}, "directories": {}}, "5.1.0": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "5.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "gitHead": "9d841b67306768de05f147839fb32f7fc9bf10ad", "_id": "safe-buffer@5.1.0", "_npmVersion": "5.0.1", "_nodeVersion": "8.0.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aSLEDudu6OoRr/2rU609gRmnYboRLxgDG1z9o2Q0os7236FwvcqIOO8r8U5JUEwivZOhDaKlFO4SbPTJYyBEyQ==", "shasum": "fe4c8460397f9eaaaa58e73be46273408a45e223", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCEJ0J6Pu7OfbLnK/XPvlFxpvCVl4kUKPwFNItYCegywIgDzL7i0uBFSAQZ+fkV/HxN37wq37a21g4ze2VZR83NjI="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safe-buffer-5.1.0.tgz_1496440779986_0.7629844571929425"}, "directories": {}}, "5.1.1": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "5.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test.js"}, "gitHead": "5261e0c19dd820c31dd21cb4116902b0ed0f9e57", "_id": "safe-buffer@5.1.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg==", "shasum": "893312af69b2123def71f57889001671eeb2c853", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDjtdeZpyAJ/3PJIoFCTE5MH9U9kLTYGYf9eVnrwV1EGAiEA6WQ+jWHvzpUl2TXO8vd4tF2FelKZcPNKQNO5QBUTd6E="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safe-buffer-5.1.1.tgz_1498076368476_0.22441886644810438"}, "directories": {}}, "5.1.2": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "5.1.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "devDependencies": {"standard": "*", "tape": "^4.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test/*.js"}, "gitHead": "649435cc8e2d1f3ecdc7caf323f1cb1187307a16", "_id": "safe-buffer@5.1.2", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "shasum": "991ec69d296e0313747d59bdfd2b745c35f8828d", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "fileCount": 5, "unpackedSize": 31686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4OCyCRA9TVsSAnZWagAAwf8QAIlCcf+WlqWgpiGufGgi\n+P81J+YsGVk8haOYIgZX8FTI10RjPKiGN6R11R2B8YMJhxk3kX6O2SO/pl6A\n3tuw9/n+HBQClzobhWovJ6aymO+ozlaxxWADx0DrqKhoDOfpPLoIjaWNS2q2\nh1VxErOXagn/JP11Le89LHqcZs3s1jYyH/cAvA7ygaHvHaDhAiMMimAdz8Ze\n07VKtAM6PSnBYODducjXNTWdqotsRnzmqWEQrV2OQsk8OudJ3+YBZG8szsQ2\nVGrOLeQyj7g+q/WrjFi6I4S+Eg0dYYUg73X3PaAFzGXf7VIjDDNkFjwmtx5u\nUxQ/HczXVzHkcVOfBzbNk4rPTx9o6gr+oDIvWeAgOPYIv1YT5bnMHZ2w7M+8\n4b+SPqVeWHcNfnUhLcHmxim2TWkl0DD+oeYSucsOZ65dJdihMlNiOlVe5XXl\nIPJbfOqNcWCzkibq7pQmTSeM52l8JSekpNZGi3RQQkbrVR+cR3F53VrDzvZe\n3Jqwg7hGlGXyXl5i3TPL0oqD15n/1+wLxZEBVrqM5SeQzvD8l1iDv+3sPViP\ng3msNBtX8NH+Sf4kfCJzHRGestQb0zpVa8wvKeQBBlmpv1kqAgnuPR3k39hI\ncjpqjm2vkmAetW4XnOnCuuMk4SEqejwXh0w1TAHlFLqhywxgSZhH6SJhTbgQ\nKQZ7\r\n=EOr+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4RVj++s9KYiDkOojy2VdYBy9YF6YOj8vP2N/FCkFRtwIhAI1n9Rp8U1vueY4XvMUuHhdXJ7tpFiVNzq8Vtrn0Q+Uh"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safe-buffer_5.1.2_1524687024555_0.6520279716197115"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "5.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "devDependencies": {"standard": "*", "tape": "^4.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test/*.js"}, "gitHead": "ae53d5b9f06eae8540ca948d14e43ca32692dd8c", "_id": "safe-buffer@5.2.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-fZEwUGbVl7kouZs1jCdMLdt95hdIv0ZeHg6L7qPeciMZhZ+/gdesW4wgTARkrFWEpspjEATAzUGPG8N2jJiwbg==", "shasum": "b74daec49b1148f88c64b68d49b1e815c1f2f519", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.0.tgz", "fileCount": 5, "unpackedSize": 31924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH5EyCRA9TVsSAnZWagAA1QQP/RhJdEyKhGt8CC6AjuHT\nEYn+EG8LKupNcX4D3O1Tba5pHT8t6RTi1F+pQnENjaVVqYVo1UGnLHB7mvfK\ntJt/ebqCAGZicYtz0k8OjsptrDsta+x/AkSemMmpEgfHQyEVy3W+e2KXi9z1\nY30LDvK0SXo/TW3ii5cGJqqppUMfNAAh1QfaNT4w4W3lZQkDpyWxaqThHBD2\nJIYdGzjbo0TQAEFvhLeHK+QXBAimWSKdNzlQyxobhNMuNP0im+a0UHu/mJLO\ndprty7mp2QU0/2UsHDN+h9HKD/TqaiMHj0BYNZ5eLna2ztEWHym1lTPByfiN\nqMaMC/zNY8iWL3vIfQuehWr9cQXzHtQG4CUB2SqnFhY7OhY7sgIpVcn4N+dZ\nwQ3yr3cEECuUmW0xSJvD6RwF7pIiX0bHk1mZNV6Tg2TaaMt9/yv/k2+hln9K\nEvO8aXWIOiFCrmuc9WEx263HMNG/6NOpMhR9Pxy3+LMHFkQMkHOTzRFL4fU3\nATNpNOgBxLvCsj0OiPzfaz2vAoMuOs5nZlBbTMGM1VW8Q8mxzxgjnMPC7cMg\nDxurutltjysNkP2XMkojmHpyuMQwWIMe2HpDfqgFkio7i4eGTCatgzlJkFIn\n3GraU8j6PBfTmSlA04pO8Q8fPXO9ZUMYR3RDDtE5hmP7ppMWSg56qroLsMH6\n0PVS\r\n=jfBj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0Urc6m8s6nnkvZavRoNerHGjZ/fMnrbEvw7DxrOefqAIhAKA/SdWYW9cBJUKtY9nP7utQHZPokv32hehEr7ZXDRam"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safe-buffer_5.2.0_1562349873361_0.933391**********"}, "_hasShrinkwrap": false}, "5.2.1": {"name": "safe-buffer", "description": "Safer Node.js Buffer API", "version": "5.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "devDependencies": {"standard": "*", "tape": "^5.0.0"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "scripts": {"test": "standard && tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "89d3d5b4abd6308c6008499520373d204ada694b", "_id": "safe-buffer@5.2.1", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "shasum": "1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6", "tarball": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "fileCount": 5, "unpackedSize": 32101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuC3LCRA9TVsSAnZWagAA92oP+wTbxk5kLetvO9lp2gMy\nZUYhmm7VY3AO5juqtmAesac386vdk95/HMCVAg/97R2DU0VMavfmGB9cmj8A\ntI6Y34m0ulBECS/wjfbLfJI7iL0VkSPgzjkS6fqlDS6s2jOMy2BH/YghMGKh\n8Df0WtQdcGQuJ09cJLJ4RJeT860aONJHaZ4ogcCT+VLOTFOoTSSSgwmaPQg1\n/WaX/DA8FykZkt8xuv8N/v4kA+UKMgSN/e1anbYo6VEcmkLtespr0zzB+Sxg\nokylqRPsRxSVurScbju/rWcic7PyOez40gAiuklilD88P7U3Y1t0Rl/HIioH\n5hU8e18vT5zKlSXP65jq7HCNX15USJeWx6RrcL+/nIABBs3LiJt3iwNzOcPX\nNPE5b/36ZIXnR0xTy3fC9ofdZU99od4rcGuBxiGLmE5I/0LVeW7qk5HoNdbc\nLvzeFEPUU5/iCTfDK4JyRRM2jaorfpG08fgmbpSBDKxGKam2vf933Ah8sXX5\nGLPB0V7x9fb6OC4BwNZooCnVOZvX66++JoIuUyPKKGoYuKNpiNq+AXTOmg4l\nLTkTD8FW/iSkfosNxNhVz3ToEhD32E+dMv1/pQRjZKA0o2sbtA0RcKe0TF5G\nYmSbvYmitk9Xne+dvQQtto30rxGjOJHKgVP/TtD/4zMHNhG+Zh0T+pDmP0VC\n43rI\r\n=qAuV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0oTF5ByqtVcVFo3IbaDpTg3KxeI76aCpIDRuaIBizCAIhAJ3pHKgZhtOQ6HC96A0aHb/0U5hhxRICmoVUDKR/gVyc"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/safe-buffer_5.2.1_1589128650648_0.9327019726340318"}, "_hasShrinkwrap": false}}, "readme": "# safe-buffer [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![downloads][downloads-image]][downloads-url] [![javascript style guide][standard-image]][standard-url]\n\n[travis-image]: https://img.shields.io/travis/feross/safe-buffer/master.svg\n[travis-url]: https://travis-ci.org/feross/safe-buffer\n[npm-image]: https://img.shields.io/npm/v/safe-buffer.svg\n[npm-url]: https://npmjs.org/package/safe-buffer\n[downloads-image]: https://img.shields.io/npm/dm/safe-buffer.svg\n[downloads-url]: https://npmjs.org/package/safe-buffer\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n\n#### Safer Node.js Buffer API\n\n**Use the new Node.js Buffer APIs (`Buffer.from`, `Buffer.alloc`,\n`Buffer.allocUnsafe`, `Buffer.allocUnsafeSlow`) in all versions of Node.js.**\n\n**Uses the built-in implementation when available.**\n\n## install\n\n```\nnpm install safe-buffer\n```\n\n## usage\n\nThe goal of this package is to provide a safe replacement for the node.js `Buffer`.\n\nIt's a drop-in replacement for `Buffer`. You can use it by adding one `require` line to\nthe top of your node.js modules:\n\n```js\nvar Buffer = require('safe-buffer').Buffer\n\n// Existing buffer code will continue to work without issues:\n\nnew Buffer('hey', 'utf8')\nnew Buffer([1, 2, 3], 'utf8')\nnew Buffer(obj)\nnew Buffer(16) // create an uninitialized buffer (potentially unsafe)\n\n// But you can use these new explicit APIs to make clear what you want:\n\nBuffer.from('hey', 'utf8') // convert from many types to a Buffer\nBuffer.alloc(16) // create a zero-filled buffer (safe)\nBuffer.allocUnsafe(16) // create an uninitialized buffer (potentially unsafe)\n```\n\n## api\n\n### Class Method: Buffer.from(array)\n<!-- YAML\nadded: v3.0.0\n-->\n\n* `array` {Array}\n\nAllocates a new `Buffer` using an `array` of octets.\n\n```js\nconst buf = Buffer.from([0x62,0x75,0x66,0x66,0x65,0x72]);\n  // creates a new Buffer containing ASCII bytes\n  // ['b','u','f','f','e','r']\n```\n\nA `TypeError` will be thrown if `array` is not an `Array`.\n\n### Class Method: Buffer.from(arrayBuffer[, byteOffset[, length]])\n<!-- YAML\nadded: v5.10.0\n-->\n\n* `arrayBuffer` {ArrayBuffer} The `.buffer` property of a `TypedArray` or\n  a `new ArrayBuffer()`\n* `byteOffset` {Number} Default: `0`\n* `length` {Number} Default: `arrayBuffer.length - byteOffset`\n\nWhen passed a reference to the `.buffer` property of a `TypedArray` instance,\nthe newly created `Buffer` will share the same allocated memory as the\nTypedArray.\n\n```js\nconst arr = new Uint16Array(2);\narr[0] = 5000;\narr[1] = 4000;\n\nconst buf = Buffer.from(arr.buffer); // shares the memory with arr;\n\nconsole.log(buf);\n  // Prints: <Buffer 88 13 a0 0f>\n\n// changing the TypedArray changes the Buffer also\narr[1] = 6000;\n\nconsole.log(buf);\n  // Prints: <Buffer 88 13 70 17>\n```\n\nThe optional `byteOffset` and `length` arguments specify a memory range within\nthe `arrayBuffer` that will be shared by the `Buffer`.\n\n```js\nconst ab = new ArrayBuffer(10);\nconst buf = Buffer.from(ab, 0, 2);\nconsole.log(buf.length);\n  // Prints: 2\n```\n\nA `TypeError` will be thrown if `arrayBuffer` is not an `ArrayBuffer`.\n\n### Class Method: Buffer.from(buffer)\n<!-- YAML\nadded: v3.0.0\n-->\n\n* `buffer` {Buffer}\n\nCopies the passed `buffer` data onto a new `Buffer` instance.\n\n```js\nconst buf1 = Buffer.from('buffer');\nconst buf2 = Buffer.from(buf1);\n\nbuf1[0] = 0x61;\nconsole.log(buf1.toString());\n  // 'auffer'\nconsole.log(buf2.toString());\n  // 'buffer' (copy is not changed)\n```\n\nA `TypeError` will be thrown if `buffer` is not a `Buffer`.\n\n### Class Method: Buffer.from(str[, encoding])\n<!-- YAML\nadded: v5.10.0\n-->\n\n* `str` {String} String to encode.\n* `encoding` {String} Encoding to use, Default: `'utf8'`\n\nCreates a new `Buffer` containing the given JavaScript string `str`. If\nprovided, the `encoding` parameter identifies the character encoding.\nIf not provided, `encoding` defaults to `'utf8'`.\n\n```js\nconst buf1 = Buffer.from('this is a tést');\nconsole.log(buf1.toString());\n  // prints: this is a tést\nconsole.log(buf1.toString('ascii'));\n  // prints: this is a tC)st\n\nconst buf2 = Buffer.from('7468697320697320612074c3a97374', 'hex');\nconsole.log(buf2.toString());\n  // prints: this is a tést\n```\n\nA `TypeError` will be thrown if `str` is not a string.\n\n### Class Method: Buffer.alloc(size[, fill[, encoding]])\n<!-- YAML\nadded: v5.10.0\n-->\n\n* `size` {Number}\n* `fill` {Value} Default: `undefined`\n* `encoding` {String} Default: `utf8`\n\nAllocates a new `Buffer` of `size` bytes. If `fill` is `undefined`, the\n`Buffer` will be *zero-filled*.\n\n```js\nconst buf = Buffer.alloc(5);\nconsole.log(buf);\n  // <Buffer 00 00 00 00 00>\n```\n\nThe `size` must be less than or equal to the value of\n`require('buffer').kMaxLength` (on 64-bit architectures, `kMaxLength` is\n`(2^31)-1`). Otherwise, a [`RangeError`][] is thrown. A zero-length Buffer will\nbe created if a `size` less than or equal to 0 is specified.\n\nIf `fill` is specified, the allocated `Buffer` will be initialized by calling\n`buf.fill(fill)`. See [`buf.fill()`][] for more information.\n\n```js\nconst buf = Buffer.alloc(5, 'a');\nconsole.log(buf);\n  // <Buffer 61 61 61 61 61>\n```\n\nIf both `fill` and `encoding` are specified, the allocated `Buffer` will be\ninitialized by calling `buf.fill(fill, encoding)`. For example:\n\n```js\nconst buf = Buffer.alloc(11, 'aGVsbG8gd29ybGQ=', 'base64');\nconsole.log(buf);\n  // <Buffer 68 65 6c 6c 6f 20 77 6f 72 6c 64>\n```\n\nCalling `Buffer.alloc(size)` can be significantly slower than the alternative\n`Buffer.allocUnsafe(size)` but ensures that the newly created `Buffer` instance\ncontents will *never contain sensitive data*.\n\nA `TypeError` will be thrown if `size` is not a number.\n\n### Class Method: Buffer.allocUnsafe(size)\n<!-- YAML\nadded: v5.10.0\n-->\n\n* `size` {Number}\n\nAllocates a new *non-zero-filled* `Buffer` of `size` bytes.  The `size` must\nbe less than or equal to the value of `require('buffer').kMaxLength` (on 64-bit\narchitectures, `kMaxLength` is `(2^31)-1`). Otherwise, a [`RangeError`][] is\nthrown. A zero-length Buffer will be created if a `size` less than or equal to\n0 is specified.\n\nThe underlying memory for `Buffer` instances created in this way is *not\ninitialized*. The contents of the newly created `Buffer` are unknown and\n*may contain sensitive data*. Use [`buf.fill(0)`][] to initialize such\n`Buffer` instances to zeroes.\n\n```js\nconst buf = Buffer.allocUnsafe(5);\nconsole.log(buf);\n  // <Buffer 78 e0 82 02 01>\n  // (octets will be different, every time)\nbuf.fill(0);\nconsole.log(buf);\n  // <Buffer 00 00 00 00 00>\n```\n\nA `TypeError` will be thrown if `size` is not a number.\n\nNote that the `Buffer` module pre-allocates an internal `Buffer` instance of\nsize `Buffer.poolSize` that is used as a pool for the fast allocation of new\n`Buffer` instances created using `Buffer.allocUnsafe(size)` (and the deprecated\n`new Buffer(size)` constructor) only when `size` is less than or equal to\n`Buffer.poolSize >> 1` (floor of `Buffer.poolSize` divided by two). The default\nvalue of `Buffer.poolSize` is `8192` but can be modified.\n\nUse of this pre-allocated internal memory pool is a key difference between\ncalling `Buffer.alloc(size, fill)` vs. `Buffer.allocUnsafe(size).fill(fill)`.\nSpecifically, `Buffer.alloc(size, fill)` will *never* use the internal Buffer\npool, while `Buffer.allocUnsafe(size).fill(fill)` *will* use the internal\nBuffer pool if `size` is less than or equal to half `Buffer.poolSize`. The\ndifference is subtle but can be important when an application requires the\nadditional performance that `Buffer.allocUnsafe(size)` provides.\n\n### Class Method: Buffer.allocUnsafeSlow(size)\n<!-- YAML\nadded: v5.10.0\n-->\n\n* `size` {Number}\n\nAllocates a new *non-zero-filled* and non-pooled `Buffer` of `size` bytes.  The\n`size` must be less than or equal to the value of\n`require('buffer').kMaxLength` (on 64-bit architectures, `kMaxLength` is\n`(2^31)-1`). Otherwise, a [`RangeError`][] is thrown. A zero-length Buffer will\nbe created if a `size` less than or equal to 0 is specified.\n\nThe underlying memory for `Buffer` instances created in this way is *not\ninitialized*. The contents of the newly created `Buffer` are unknown and\n*may contain sensitive data*. Use [`buf.fill(0)`][] to initialize such\n`Buffer` instances to zeroes.\n\nWhen using `Buffer.allocUnsafe()` to allocate new `Buffer` instances,\nallocations under 4KB are, by default, sliced from a single pre-allocated\n`Buffer`. This allows applications to avoid the garbage collection overhead of\ncreating many individually allocated Buffers. This approach improves both\nperformance and memory usage by eliminating the need to track and cleanup as\nmany `Persistent` objects.\n\nHowever, in the case where a developer may need to retain a small chunk of\nmemory from a pool for an indeterminate amount of time, it may be appropriate\nto create an un-pooled Buffer instance using `Buffer.allocUnsafeSlow()` then\ncopy out the relevant bits.\n\n```js\n// need to keep around a few small chunks of memory\nconst store = [];\n\nsocket.on('readable', () => {\n  const data = socket.read();\n  // allocate for retained data\n  const sb = Buffer.allocUnsafeSlow(10);\n  // copy the data into the new allocation\n  data.copy(sb, 0, 0, 10);\n  store.push(sb);\n});\n```\n\nUse of `Buffer.allocUnsafeSlow()` should be used only as a last resort *after*\na developer has observed undue memory retention in their applications.\n\nA `TypeError` will be thrown if `size` is not a number.\n\n### All the Rest\n\nThe rest of the `Buffer` API is exactly the same as in node.js.\n[See the docs](https://nodejs.org/api/buffer.html).\n\n\n## Related links\n\n- [Node.js issue: Buffer(number) is unsafe](https://github.com/nodejs/node/issues/4660)\n- [Node.js Enhancement Proposal: Buffer.from/Buffer.alloc/Buffer.zalloc/Buffer() soft-deprecate](https://github.com/nodejs/node-eps/pull/4)\n\n## Why is `Buffer` unsafe?\n\nToday, the node.js `Buffer` constructor is overloaded to handle many different argument\ntypes like `String`, `Array`, `Object`, `TypedArrayView` (`Uint8Array`, etc.),\n`ArrayBuffer`, and also `Number`.\n\nThe API is optimized for convenience: you can throw any type at it, and it will try to do\nwhat you want.\n\nBecause the Buffer constructor is so powerful, you often see code like this:\n\n```js\n// Convert UTF-8 strings to hex\nfunction toHex (str) {\n  return new Buffer(str).toString('hex')\n}\n```\n\n***But what happens if `toHex` is called with a `Number` argument?***\n\n### Remote Memory Disclosure\n\nIf an attacker can make your program call the `Buffer` constructor with a `Number`\nargument, then they can make it allocate uninitialized memory from the node.js process.\nThis could potentially disclose TLS private keys, user data, or database passwords.\n\nWhen the `Buffer` constructor is passed a `Number` argument, it returns an\n**UNINITIALIZED** block of memory of the specified `size`. When you create a `Buffer` like\nthis, you **MUST** overwrite the contents before returning it to the user.\n\nFrom the [node.js docs](https://nodejs.org/api/buffer.html#buffer_new_buffer_size):\n\n> `new Buffer(size)`\n>\n> - `size` Number\n>\n> The underlying memory for `Buffer` instances created in this way is not initialized.\n> **The contents of a newly created `Buffer` are unknown and could contain sensitive\n> data.** Use `buf.fill(0)` to initialize a Buffer to zeroes.\n\n(Emphasis our own.)\n\nWhenever the programmer intended to create an uninitialized `Buffer` you often see code\nlike this:\n\n```js\nvar buf = new Buffer(16)\n\n// Immediately overwrite the uninitialized buffer with data from another buffer\nfor (var i = 0; i < buf.length; i++) {\n  buf[i] = otherBuf[i]\n}\n```\n\n\n### Would this ever be a problem in real code?\n\nYes. It's surprisingly common to forget to check the type of your variables in a\ndynamically-typed language like JavaScript.\n\nUsually the consequences of assuming the wrong type is that your program crashes with an\nuncaught exception. But the failure mode for forgetting to check the type of arguments to\nthe `Buffer` constructor is more catastrophic.\n\nHere's an example of a vulnerable service that takes a JSON payload and converts it to\nhex:\n\n```js\n// Take a JSON payload {str: \"some string\"} and convert it to hex\nvar server = http.createServer(function (req, res) {\n  var data = ''\n  req.setEncoding('utf8')\n  req.on('data', function (chunk) {\n    data += chunk\n  })\n  req.on('end', function () {\n    var body = JSON.parse(data)\n    res.end(new Buffer(body.str).toString('hex'))\n  })\n})\n\nserver.listen(8080)\n```\n\nIn this example, an http client just has to send:\n\n```json\n{\n  \"str\": 1000\n}\n```\n\nand it will get back 1,000 bytes of uninitialized memory from the server.\n\nThis is a very serious bug. It's similar in severity to the\n[the Heartbleed bug](http://heartbleed.com/) that allowed disclosure of OpenSSL process\nmemory by remote attackers.\n\n\n### Which real-world packages were vulnerable?\n\n#### [`bittorrent-dht`](https://www.npmjs.com/package/bittorrent-dht)\n\n[Mathias Buus](https://github.com/mafintosh) and I\n([Feross Aboukhadijeh](http://feross.org/)) found this issue in one of our own packages,\n[`bittorrent-dht`](https://www.npmjs.com/package/bittorrent-dht). The bug would allow\nanyone on the internet to send a series of messages to a user of `bittorrent-dht` and get\nthem to reveal 20 bytes at a time of uninitialized memory from the node.js process.\n\nHere's\n[the commit](https://github.com/feross/bittorrent-dht/commit/6c7da04025d5633699800a99ec3fbadf70ad35b8)\nthat fixed it. We released a new fixed version, created a\n[Node Security Project disclosure](https://nodesecurity.io/advisories/68), and deprecated all\nvulnerable versions on npm so users will get a warning to upgrade to a newer version.\n\n#### [`ws`](https://www.npmjs.com/package/ws)\n\nThat got us wondering if there were other vulnerable packages. Sure enough, within a short\nperiod of time, we found the same issue in [`ws`](https://www.npmjs.com/package/ws), the\nmost popular WebSocket implementation in node.js.\n\nIf certain APIs were called with `Number` parameters instead of `String` or `Buffer` as\nexpected, then uninitialized server memory would be disclosed to the remote peer.\n\nThese were the vulnerable methods:\n\n```js\nsocket.send(number)\nsocket.ping(number)\nsocket.pong(number)\n```\n\nHere's a vulnerable socket server with some echo functionality:\n\n```js\nserver.on('connection', function (socket) {\n  socket.on('message', function (message) {\n    message = JSON.parse(message)\n    if (message.type === 'echo') {\n      socket.send(message.data) // send back the user's message\n    }\n  })\n})\n```\n\n`socket.send(number)` called on the server, will disclose server memory.\n\nHere's [the release](https://github.com/websockets/ws/releases/tag/1.0.1) where the issue\nwas fixed, with a more detailed explanation. Props to\n[Arnout Kazemier](https://github.com/3rd-Eden) for the quick fix. Here's the\n[Node Security Project disclosure](https://nodesecurity.io/advisories/67).\n\n\n### What's the solution?\n\nIt's important that node.js offers a fast way to get memory otherwise performance-critical\napplications would needlessly get a lot slower.\n\nBut we need a better way to *signal our intent* as programmers. **When we want\nuninitialized memory, we should request it explicitly.**\n\nSensitive functionality should not be packed into a developer-friendly API that loosely\naccepts many different types. This type of API encourages the lazy practice of passing\nvariables in without checking the type very carefully.\n\n#### A new API: `Buffer.allocUnsafe(number)`\n\nThe functionality of creating buffers with uninitialized memory should be part of another\nAPI. We propose `Buffer.allocUnsafe(number)`. This way, it's not part of an API that\nfrequently gets user input of all sorts of different types passed into it.\n\n```js\nvar buf = Buffer.allocUnsafe(16) // careful, uninitialized memory!\n\n// Immediately overwrite the uninitialized buffer with data from another buffer\nfor (var i = 0; i < buf.length; i++) {\n  buf[i] = otherBuf[i]\n}\n```\n\n\n### How do we fix node.js core?\n\nWe sent [a PR to node.js core](https://github.com/nodejs/node/pull/4514) (merged as\n`semver-major`) which defends against one case:\n\n```js\nvar str = 16\nnew Buffer(str, 'utf8')\n```\n\nIn this situation, it's implied that the programmer intended the first argument to be a\nstring, since they passed an encoding as a second argument. Today, node.js will allocate\nuninitialized memory in the case of `new Buffer(number, encoding)`, which is probably not\nwhat the programmer intended.\n\nBut this is only a partial solution, since if the programmer does `new Buffer(variable)`\n(without an `encoding` parameter) there's no way to know what they intended. If `variable`\nis sometimes a number, then uninitialized memory will sometimes be returned.\n\n### What's the real long-term fix?\n\nWe could deprecate and remove `new Buffer(number)` and use `Buffer.allocUnsafe(number)` when\nwe need uninitialized memory. But that would break 1000s of packages.\n\n~~We believe the best solution is to:~~\n\n~~1. Change `new Buffer(number)` to return safe, zeroed-out memory~~\n\n~~2. Create a new API for creating uninitialized Buffers. We propose: `Buffer.allocUnsafe(number)`~~\n\n#### Update\n\nWe now support adding three new APIs:\n\n- `Buffer.from(value)` - convert from any type to a buffer\n- `Buffer.alloc(size)` - create a zero-filled buffer\n- `Buffer.allocUnsafe(size)` - create an uninitialized buffer with given size\n\nThis solves the core problem that affected `ws` and `bittorrent-dht` which is\n`Buffer(variable)` getting tricked into taking a number argument.\n\nThis way, existing code continues working and the impact on the npm ecosystem will be\nminimal. Over time, npm maintainers can migrate performance-critical code to use\n`Buffer.allocUnsafe(number)` instead of `new Buffer(number)`.\n\n\n### Conclusion\n\nWe think there's a serious design issue with the `Buffer` API as it exists today. It\npromotes insecure software by putting high-risk functionality into a convenient API\nwith friendly \"developer ergonomics\".\n\nThis wasn't merely a theoretical exercise because we found the issue in some of the\nmost popular npm packages.\n\nFortunately, there's an easy fix that can be applied today. Use `safe-buffer` in place of\n`buffer`.\n\n```js\nvar Buffer = require('safe-buffer').Buffer\n```\n\nEventually, we hope that node.js core can switch to this new, safer behavior. We believe\nthe impact on the ecosystem would be minimal since it's not a breaking change.\nWell-maintained, popular packages would be updated to use `Buffer.alloc` quickly, while\nolder, insecure packages would magically become safe from this attack vector.\n\n\n## links\n\n- [Node.js PR: buffer: throw if both length and enc are passed](https://github.com/nodejs/node/pull/4514)\n- [Node Security Project disclosure for `ws`](https://nodesecurity.io/advisories/67)\n- [Node Security Project disclosure for`bittorrent-dht`](https://nodesecurity.io/advisories/68)\n\n\n## credit\n\nThe original issues in `bittorrent-dht`\n([disclosure](https://nodesecurity.io/advisories/68)) and\n`ws` ([disclosure](https://nodesecurity.io/advisories/67)) were discovered by\n[Mathias Buus](https://github.com/mafintosh) and\n[Feross Aboukhadijeh](http://feross.org/).\n\nThanks to [Adam Baldwin](https://github.com/evilpacket) for helping disclose these issues\nand for his work running the [Node Security Project](https://nodesecurity.io/).\n\nThanks to [John Hiesey](https://github.com/jhiesey) for proofreading this README and\nauditing the code.\n\n\n## license\n\nMIT. Copyright (C) [Feross Aboukhadijeh](http://feross.org)\n", "maintainers": [{"name": "feross", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-07-10T23:16:22.564Z", "created": "2016-01-13T04:36:59.750Z", "1.0.0": "2016-01-13T04:36:59.750Z", "2.0.0": "2016-01-17T15:22:43.586Z", "3.0.0": "2016-01-19T01:41:25.217Z", "4.0.0": "2016-05-30T04:58:11.135Z", "5.0.0": "2016-05-30T05:45:50.643Z", "5.0.1": "2016-05-30T06:08:04.471Z", "5.1.0": "2017-06-02T21:59:40.124Z", "5.1.1": "2017-06-21T20:19:28.582Z", "5.1.2": "2018-04-25T20:10:24.706Z", "5.2.0": "2019-07-05T18:04:33.530Z", "5.2.1": "2020-05-10T16:37:30.776Z"}, "homepage": "https://github.com/feross/safe-buffer", "keywords": ["buffer", "buffer allocate", "node security", "safe", "safe-buffer", "security", "uninitialized"], "repository": {"type": "git", "url": "git://github.com/feross/safe-buffer.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/safe-buffer/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"mysticatea": true, "ivan.marquez": true, "wxttxw125": true, "michaeljwilliams": true, "hualei": true, "flumpus-dev": true}}