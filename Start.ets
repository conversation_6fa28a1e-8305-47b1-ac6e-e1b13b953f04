import { router } from '@kit.ArkUI';

@Entry
@Component
struct Start {
  @State username: string = '';
  @State password: string = '';
  @State isPasswordVisible: boolean = false;

  build() {
    Column() {
      // 顶部空白
      Blank()
        .height(100)

      // Logo图标 - 使用天气图标而不是蓝色方块
      Image($r("app.media.weather_icon"))
        .width(80)
        .height(80)
        .fillColor("#4A90E2")
        .margin({ bottom: 30 })

      // 应用标题
      Text("融心天气")
        .fontSize(24)
        .fontWeight(600)
        .fontColor("#333333")
        .margin({ bottom: 8 })

      // 副标题
      Text("开启智慧天气生活")
        .fontSize(14)
        .fontColor("#999999")
        .margin({ bottom: 60 })

      // 用户名输入框
      TextInput({ placeholder: "请输入账号" })
        .width('85%')
        .height(48)
        .backgroundColor("#F8F8F8")
        .borderRadius(8)
        .fontSize(16)
        .padding({ left: 16, right: 16 })
        .margin({ bottom: 16 })
        .onChange((value) => { this.username = value })

      // 密码输入框
      Row() {
        TextInput({ placeholder: "请输入密码" })
          .type(this.isPasswordVisible ? InputType.Normal : InputType.Password)
          .layoutWeight(1)
          .backgroundColor("transparent")
          .border({ width: 0 })
          .fontSize(16)
          .onChange((value) => { this.password = value })

        Image(this.isPasswordVisible ? $r("app.media.eye_open") : $r("app.media.eye_close"))
          .width(20)
          .height(20)
          .fillColor("#999999")
          .onClick(() => {
            this.isPasswordVisible = !this.isPasswordVisible;
          })
      }
      .width('85%')
      .height(48)
      .backgroundColor("#F8F8F8")
      .borderRadius(8)
      .padding({ left: 16, right: 16 })
      .margin({ bottom: 12 })

      // 忘记密码
      Row() {
        Blank()
        Text("忘记密码")
          .fontSize(14)
          .fontColor("#999999")
      }
      .width('85%')
      .margin({ bottom: 40 })

      // 登录按钮
      Button("登录")
        .width('85%')
        .height(48)
        .backgroundColor("#5DADE2")
        .fontColor("#FFFFFF")
        .fontSize(16)
        .borderRadius(24)
        .margin({ bottom: 30 })
        .onClick(() => {
          router.pushUrl({ url: 'pages/LocationSelect' });
        })

      // 第三方登录提示
      Text("第三方账号登录")
        .fontSize(14)
        .fontColor("#999999")
        .margin({ bottom: 40 })

      Blank()

      // 注册链接
      Text("注册")
        .fontSize(16)
        .fontColor("#5DADE2")
        .margin({ bottom: 40 })
        .onClick(() => {
          // 处理注册逻辑
        })
    }
    .width('100%')
    .height('100%')
    .backgroundColor("#FFFFFF")
    .justifyContent(FlexAlign.Start)
    .alignItems(HorizontalAlign.Center)
  }
}


