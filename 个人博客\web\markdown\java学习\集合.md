Arraylist集合： 因为对于java来说万事万物皆对象，它的定义方法和对象一样

定义，增加，删除，修改元素，查看元素，遍历Arraylist集合代码如下：

![image-20241201010931833](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241201010931833.png)

1.注意java需要像c++哪样引入类似头文件

```java
package Arraylist集合;

import java.util.ArrayList;

public class _main_1 {
    public static void main(String[] args) {
        ArrayList list=new ArrayList();
        //增加元素
        list.add("2");
        list.add("爱着你");
        list.add("我爱你");
        list.add("就像老鼠爱大米");
        list.add("这世界有多少脾气，我都能永远的爱着你");
        System.out.println(list);
        //删除元素,删除什么
        list.remove("2");
        //修改元素,修改0位置的元素为
        list.set(0,"就像老鼠爱大米");
        //查看元素,用get方法来获取某一位置上的元素
        System.out.println(list.get(0));
        //遍历元素，要点是通过size方法来获取到arraylist的长度
        for(int i=0;i<=list.size()-1;i++)
            System.out.println(list.get(i));

    }


}

```

2.ArrayList集合通过定义：ArrayList list=new ArrayList();来引入一个外部包？

3.可以用get()方法来查看某个下标的元素,get(0-list.size()-1)

4.可以用remove方法来删除某个元素，给值,remove(值)

5.可以用add()方法来添加元素到尾部,add(值)

6.可以用set()方法来修改某个位置的元素为某个元素,set(0,"1231")

7.可以用size()方法来获取ArrayList()的长度