#include <stdio.h>
#include <string.h>

void findLCS(char *a, char *b) {
    int m = strlen(a);
    int n = strlen(b);
    int c[m + 1][n + 1]; // 动态规划表
    char lcs[m + n];     // 存储最长公共子序列
    int index = 0;

    // 初始化动态规划表
    for (int i = 0; i <= m; i++) {
        for (int j = 0; j <= n; j++) {
            if (i == 0 || j == 0) {
                c[i][j] = 0;
            } else if (a[i - 1] == b[j - 1]) {
                c[i][j] = c[i - 1][j - 1] + 1;
            } else {
                c[i][j] = (c[i - 1][j] > c[i][j - 1]) ? c[i - 1][j] : c[i][j - 1];
            }
        }
    }

    // 逆向构造最长公共子序列
    int i = m, j = n;
    while (i > 0 && j > 0) {
        if (a[i - 1] == b[j - 1]) {
            lcs[index++] = a[i - 1];
            i--;
            j--;
        } else if (c[i][j - 1] > c[i - 1][j]) { // 修正条件，优先选择左侧值
            j--;
        } else {
            i--;
        }
    }

    // 输出最长公共子序列
    printf("最长公共子序列为: ");
    for (int k = index - 1; k >= 0; k--) {
        printf("%c", lcs[k]);
    }
    printf("\n");
}

int main() {
    char a[] = "ABCDBAB";
    char b[] = "BDCABA";

    findLCS(a, b);

    return 0;
}
