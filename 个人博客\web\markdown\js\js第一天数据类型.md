# js第一天:

## 数据类型

基本数据类型：弱数据类型：js中变量只有赋值之后才能知道类型

number 数字型：数学中学到的数字

string 字符串类型：用单引号，双引号包裹的数据，用+可以拼接字符串

boolean 布尔型：真true，假false

undefined 未定义型:只声明一个值，不定义就是undefined

null 空类型:赋值后数值为空

## 运算符：

和c，c++一致

## 模版字符串：${}类似于c中的占位符，使用时引号要用``反引号把数据包裹在里面

![image-20241103001615868](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103001615868.png)

![image-20241103001600155](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103001600155.png)

![image-20241103001628222](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103001628222.png)



![image-20241103002120366](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103002120366.png)

(C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103001924694.png)

![image-20241103002008394](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103002008394.png)

![image-20241103002018047](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103002018047.png)

## true和false：

![image-20241103002500015](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103002500015.png)

![image-20241103002440867](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103002440867.png)

## null：尚未创建的对象

![image-20241103002703366](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103002703366.png)

NAN：not a num；

![image-20241103003012114](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103003012114.png)

![image-20241103002948360](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103002948360.png)

![image-20241103003120124](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103003120124.png)

![image-20241103003106093](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103003106093.png)

## 检测数据类型：

![image-20241103003430462](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103003430462.png)

![image-20241103003823400](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103003823400.png)

![image-20241103003809556](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103003809556.png)

## 显示转换和隐式转换：

隐式转换：

![image-20241103004339892](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103004339892.png)

![image-20241103004433497](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103004433497.png)

![image-20241103004440555](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103004440555.png)

![image-20241103004619513](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103004619513.png)

![image-20241103004627379](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103004627379.png)

显示转换:

![image-20241103005152117](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103005152117.png)![image-20241103005204969](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103005204969.png)

1.用Number()转换为数字类型

![image-20241103005519567](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103005519567.png)

![image-20241103005551153](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103005551153.png)

![image-20241103005612092](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103005612092.png)

![image-20241103005627314](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241103005627314.png)