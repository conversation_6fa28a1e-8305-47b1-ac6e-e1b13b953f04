#include <stdio.h>

void printYangHuiTriangle(int n)
{
    int triangle[n][n];

    // 初始化数组
    for (int i = 0; i < n; i++)
    {
        for (int j = 0; j <= i; j++)
        {
            if (j == 0 || j == i)
            {
                triangle[i][j] = 1; // 边界值为1
            }
            else
            {
                triangle[i][j] = triangle[i - 1][j - 1] + triangle[i - 1][j]; // 递推公式
            }
        }
    }

    // 输出杨辉三角
    for (int i = 0; i < n; i++)
    {
        for (int k = 0; k < n - i - 1; k++)
        {
            printf("   "); // 打印空格
        }
        for (int j = 0; j <= i; j++)
        {
            if (j == 0)
            {
                printf("%d", triangle[i][j]); // 第一列不打印前置空格
            }
            else
            {
                printf("    %d", triangle[i][j]); // 每列之间打印4个空格
            }
        }
        printf("\n");
    }
}

int main()
{
    int n;
    scanf("%d", &n);

    printYangHuiTriangle(n);

    return 0;
}
