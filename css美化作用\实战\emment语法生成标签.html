<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .one{
            text-align: center;
            color:blue;
        }
        #two{
            text-align: left;
            color: #666;
        }
        .demo{
            text-align: center;
            color: antiquewhite;
        }
        /* <!-- css样式只需要样式首字母就可以形成 如w200+tab--> */
        .three{
            text-align: left;
            color: rgb(226, 54, 48);
        }
    </style>
</head>
<body>
    <!-- 任意标签名加tab键可以快速生成 -->
    <div>我爱你</div>
    <!-- 要生成多个相同标签直接加上*数量就可以了 -->
    <div>这是你的pink</div>
    <div>这是我的pink</div>
    <div>这是他的pink</div>
    <!-- 如果要生成具有父子级关系的子级标签,用>就可以 -->
    <ol>
        <li>1</li>
        <li>2</li>
        <li>3</li>
    </ol>
    <!-- 如果要生成具有兄弟关系的标签,则是用+就可以 -->
    <div>1</div>
    <p>2</p>
    <!-- 如果要生成带有类名或者id名字的css的某个标签,直接写.demo或者#two tab键就可以了,要给某个标签加类选择器则是p.类名即可 -->
     <p class="one">yes</p>
     <p class="demo">no!</p>
     <!-- 如果要生成id的,p#id名 -->
      <p id="two">fuck</p>  
        <p class="three">hello</p>
      

</body>
</html>