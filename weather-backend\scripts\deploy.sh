#!/bin/bash

# 部署脚本

echo "🚀 开始部署天气后台服务..."

# 设置变量
APP_NAME="weather-backend"
DEPLOY_DIR="/opt/$APP_NAME"
BACKUP_DIR="/opt/backups/$APP_NAME"
SERVICE_NAME="weather-backend"

# 创建备份
echo "📦 创建备份..."
sudo mkdir -p $BACKUP_DIR
sudo cp -r $DEPLOY_DIR $BACKUP_DIR/$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# 停止服务
echo "⏹️  停止服务..."
sudo systemctl stop $SERVICE_NAME 2>/dev/null || true
sudo pm2 stop $APP_NAME 2>/dev/null || true

# 部署新版本
echo "📂 部署新版本..."
sudo mkdir -p $DEPLOY_DIR
sudo cp -r . $DEPLOY_DIR/
cd $DEPLOY_DIR

# 安装依赖
echo "📦 安装生产依赖..."
sudo npm ci --only=production

# 设置权限
sudo chown -R www-data:www-data $DEPLOY_DIR
sudo chmod +x $DEPLOY_DIR/scripts/*.sh

# 启动服务
echo "▶️  启动服务..."
if command -v pm2 &> /dev/null; then
    sudo -u www-data pm2 start ecosystem.config.js --env production
    sudo -u www-data pm2 save
else
    sudo systemctl start $SERVICE_NAME
fi

# 检查服务状态
sleep 5
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 部署成功！服务正在运行"
else
    echo "❌ 部署失败，正在回滚..."
    # 回滚逻辑
    LATEST_BACKUP=$(ls -t $BACKUP_DIR | head -n1)
    if [ -n "$LATEST_BACKUP" ]; then
        sudo cp -r $BACKUP_DIR/$LATEST_BACKUP/* $DEPLOY_DIR/
        sudo systemctl start $SERVICE_NAME 2>/dev/null || sudo -u www-data pm2 restart $APP_NAME
    fi
    exit 1
fi

echo "🎉 部署完成！"
