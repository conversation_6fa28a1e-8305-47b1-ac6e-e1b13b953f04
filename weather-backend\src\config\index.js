require('dotenv').config();

const config = {
  // 服务器配置
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',

  // 和风天气 API 配置
  qweather: {
    apiKey: process.env.QWEATHER_API_KEY,
    baseUrl: process.env.QWEATHER_BASE_URL || 'https://devapi.qweather.com/v7',
    geoUrl: process.env.QWEATHER_GEO_URL || 'https://geoapi.qweather.com/v2'
  },

  // 缓存配置
  cache: {
    ttl: parseInt(process.env.CACHE_TTL) || 300 // 5分钟
  },

  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info'
  },

  // 请求限制配置
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100 // 每个IP最多100次请求
  }
};

// 验证必需的环境变量
if (!config.qweather.apiKey) {
  console.error('错误: QWEATHER_API_KEY 环境变量未设置');
  console.error('请访问 https://dev.qweather.com 注册并获取免费API Key');
  process.exit(1);
}

module.exports = config;
