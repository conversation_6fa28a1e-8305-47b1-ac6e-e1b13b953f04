require('dotenv').config();

const config = {
  // 服务器配置
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // 天气 API 配置
  openWeather: {
    apiKey: process.env.OPENWEATHER_API_KEY,
    baseUrl: process.env.OPENWEATHER_BASE_URL || 'https://api.openweathermap.org/data/2.5'
  },
  
  // 缓存配置
  cache: {
    ttl: parseInt(process.env.CACHE_TTL) || 300 // 5分钟
  },
  
  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info'
  },
  
  // 请求限制配置
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100 // 每个IP最多100次请求
  }
};

// 验证必需的环境变量
if (!config.openWeather.apiKey) {
  console.error('错误: OPENWEATHER_API_KEY 环境变量未设置');
  process.exit(1);
}

module.exports = config;
