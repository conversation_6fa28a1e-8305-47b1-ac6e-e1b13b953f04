# 多态

![image-20241127161741703](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241127161741703.png)

![image-20241127162735688](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241127162735688.png)

# 多态：

1.通过父类引用指向子类对象来实现多态：相当于我们方法在父类里面定义了它，子类里面也做了重写，我们在调用的时候是通过引用父类来调用子类中的方法

类似于c++中父类指针指向子类函数来调用他们

![image-20241127163905403](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241127163905403.png)

```java
package 多态;

public class animal {
    public void shout(){
        System.out.println("动物叫");
    }

}

```

```java
package 多态;

public class dog extends animal{
    public void shout(){
        System.out.println("汪汪");
    }
}

```

```java
package 多态;

public class duck extends animal{
    public void shout(){
        System.out.println("嘎嘎");
    }


}

```

```java
package 多态;

public class girl {
    public void play(animal a){
        a.shout();
    }
}

```

```java
package 多态;

public class mouse extends animal{
    public void shout(){
        System.out.println("蛐蛐");
    }
}

```

```java
package 多态;

public class test {
    public static void main(String[] args) {
        girl g=new girl();
        animal a;
        dog d=new dog();
        a=d;
        //此处可以一步使用父类引用
        animal b=new duck();
        g.play(a);
        g.play(b);
    }
}

```

