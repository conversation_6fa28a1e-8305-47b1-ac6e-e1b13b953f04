#include <stdio.h>
#include <stdlib.h>

int calculateMax(int arr[], int n) {
    // 按降序排序
    for (int i = 0; i < n - 1; i++) {
        for (int j = 0; j < n - i - 1; j++) {
            if (arr[j] < arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }

    // 贪心计算最大值
    while (n > 1) {
        int a = arr[0];
        int b = arr[1];
        arr[0] = a * b + 1;
        for (int i = 1; i < n - 1; i++) {
            arr[i] = arr[i + 1];
        }
        n--;
    }
    return arr[0];
}

int calculateMin(int arr[], int n) {
    // 按升序排序
    for (int i = 0; i < n - 1; i++) {
        for (int j = 0; j < n - i - 1; j++) {
            if (arr[j] > arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }

    // 贪心计算最小值
    while (n > 1) {
        int a = arr[0];
        int b = arr[1];
        arr[0] = a * b + 1;
        for (int i = 1; i < n - 1; i++) {
            arr[i] = arr[i + 1];
        }
        n--;
    }
    return arr[0];
}

int main() {
    int n;

    scanf("%d", &n);

    int arr[n];

    for (int i = 0; i < n; i++) {
        scanf("%d", &arr[i]);
    }

    int max = calculateMax(arr, n);
    int min = calculateMin(arr, n);

    printf("Max=max-min=%d-%d=%d\n", max, min, max - min);

    return 0;
}
