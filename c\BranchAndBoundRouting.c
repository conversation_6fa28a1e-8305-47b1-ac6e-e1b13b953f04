#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>

#define ROWS 8
#define COLS 8

typedef struct {
    int x, y; // 当前方格的坐标
    int distance; // 从起点到当前方格的距离
    int parentX, parentY; // 父节点的坐标，用于回溯路径
} Node;

typedef struct {
    Node nodes[ROWS * COLS];
    int front, rear;
} Queue;

void initQueue(Queue *q) {
    q->front = q->rear = 0;
}

bool isEmpty(Queue *q) {
    return q->front == q->rear;
}

void enqueue(Queue *q, Node node) {
    q->nodes[q->rear++] = node;
}

Node dequeue(Queue *q) {
    return q->nodes[q->front++];
}

bool isValid(int x, int y, int grid[ROWS][COLS], bool visited[ROWS][COLS]) {
    return x >= 0 && x < ROWS && y >= 0 && y < COLS && grid[x][y] == 0 && !visited[x][y];
}

void printPath(Node gridNodes[ROWS][COLS], int endX, int endY) {
    if (gridNodes[endX][endY].parentX == -1 && gridNodes[endX][endY].parentY == -1) {
        printf("No path found.\n");
        return;
    }

    Node path[ROWS * COLS];
    int pathLength = 0;
    Node current = gridNodes[endX][endY];

    while (current.parentX != -1 && current.parentY != -1) {
        path[pathLength++] = current;
        current = gridNodes[current.parentX][current.parentY];
    }

    path[pathLength++] = current; // 添加起点

    for (int i = pathLength - 1; i >= 0; i--) {
        printf("%d %d\n", path[i].x + 1, path[i].y + 1);
    }
}

void branchAndBound(int grid[ROWS][COLS], int startX, int startY, int endX, int endY) {
    bool visited[ROWS][COLS] = {false};
    Node gridNodes[ROWS][COLS];
    Queue q;
    initQueue(&q);

    // 初始化起点
    Node startNode = {startX, startY, 0, -1, -1};
    enqueue(&q, startNode);
    visited[startX][startY] = true;
    gridNodes[startX][startY] = startNode;

    // 四个方向
    int directions[4][2] = {{-1, 0}, {0, 1}, {1, 0}, {0, -1}}; // 调整方向顺序为上、右、下、左

    while (!isEmpty(&q)) {
        Node current = dequeue(&q);

        // 如果到达终点
        if (current.x == endX && current.y == endY) {
            printPath(gridNodes, endX, endY);
            return;
        }

        // 扩展相邻节点
        for (int i = 0; i < 4; i++) {
            int newX = current.x + directions[i][0];
            int newY = current.y + directions[i][1];

            if (isValid(newX, newY, grid, visited)) {
                visited[newX][newY] = true;
                Node nextNode = {newX, newY, current.distance + 1, current.x, current.y};
                enqueue(&q, nextNode);
                gridNodes[newX][newY] = nextNode;
            }
        }
    }

    printf("No path found.\n");
}

int main() {
    int grid[ROWS][COLS] = {0};

    // 设置封锁的方格
    grid[1][2] = 1; // 第2行第3列
    grid[2][3] = 1; // 第3行第4列

    // 起点和终点
    int startX = 0, startY = 0; // 第1行第1列
    int endX = 4, endY = 2; // 第5行第3列

    branchAndBound(grid, startX, startY, endX, endY);

    return 0;
}
