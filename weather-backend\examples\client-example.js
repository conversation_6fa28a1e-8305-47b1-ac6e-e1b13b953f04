// 天气 API 客户端使用示例

const axios = require('axios');

// 配置基础 URL
const API_BASE_URL = 'http://localhost:3000/api';

class WeatherClient {
  constructor(baseUrl = API_BASE_URL) {
    this.client = axios.create({
      baseURL: baseUrl,
      timeout: 10000
    });
  }

  // 获取当前天气
  async getCurrentWeather(city, options = {}) {
    try {
      const { lang = 'zh_cn', units = 'metric' } = options;
      const response = await this.client.get('/weather/current', {
        params: { city, lang, units }
      });
      return response.data;
    } catch (error) {
      console.error('获取当前天气失败:', error.response?.data || error.message);
      throw error;
    }
  }

  // 获取天气预报
  async getForecast(city, options = {}) {
    try {
      const { days = 5, lang = 'zh_cn', units = 'metric' } = options;
      const response = await this.client.get('/weather/forecast', {
        params: { city, days, lang, units }
      });
      return response.data;
    } catch (error) {
      console.error('获取天气预报失败:', error.response?.data || error.message);
      throw error;
    }
  }

  // 通过坐标获取天气
  async getWeatherByCoordinates(lat, lon, options = {}) {
    try {
      const { lang = 'zh_cn', units = 'metric' } = options;
      const response = await this.client.get('/weather/coordinates', {
        params: { lat, lon, lang, units }
      });
      return response.data;
    } catch (error) {
      console.error('通过坐标获取天气失败:', error.response?.data || error.message);
      throw error;
    }
  }

  // 搜索城市
  async searchCities(query, limit = 5) {
    try {
      const response = await this.client.get('/weather/search', {
        params: { q: query, limit }
      });
      return response.data;
    } catch (error) {
      console.error('搜索城市失败:', error.response?.data || error.message);
      throw error;
    }
  }
}

// 使用示例
async function example() {
  const weatherClient = new WeatherClient();

  try {
    console.log('🌤️  天气 API 使用示例\n');

    // 1. 获取北京当前天气
    console.log('1. 获取北京当前天气:');
    const currentWeather = await weatherClient.getCurrentWeather('Beijing', {
      lang: 'zh_cn',
      units: 'metric'
    });
    console.log(`   温度: ${currentWeather.data.current.temperature}°C`);
    console.log(`   天气: ${currentWeather.data.weather.description}`);
    console.log(`   湿度: ${currentWeather.data.current.humidity}%\n`);

    // 2. 获取上海3天天气预报
    console.log('2. 获取上海3天天气预报:');
    const forecast = await weatherClient.getForecast('Shanghai', {
      days: 3,
      lang: 'zh_cn'
    });
    forecast.data.forecast.slice(0, 3).forEach((day, index) => {
      const date = new Date(day.datetime).toLocaleDateString('zh-CN');
      console.log(`   ${date}: ${day.temperature.min}°C - ${day.temperature.max}°C, ${day.weather.description}`);
    });
    console.log('');

    // 3. 通过坐标获取天气（广州）
    console.log('3. 通过坐标获取广州天气:');
    const coordWeather = await weatherClient.getWeatherByCoordinates(23.1291, 113.2644, {
      lang: 'zh_cn'
    });
    console.log(`   位置: ${coordWeather.data.location.name}`);
    console.log(`   温度: ${coordWeather.data.current.temperature}°C`);
    console.log(`   天气: ${coordWeather.data.weather.description}\n`);

    // 4. 搜索城市
    console.log('4. 搜索包含"海"的城市:');
    const cities = await weatherClient.searchCities('海', 3);
    cities.data.forEach(city => {
      console.log(`   ${city.name}, ${city.country} (${city.coordinates.lat}, ${city.coordinates.lon})`);
    });

  } catch (error) {
    console.error('示例执行失败:', error.message);
  }
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  example();
}

module.exports = WeatherClient;
