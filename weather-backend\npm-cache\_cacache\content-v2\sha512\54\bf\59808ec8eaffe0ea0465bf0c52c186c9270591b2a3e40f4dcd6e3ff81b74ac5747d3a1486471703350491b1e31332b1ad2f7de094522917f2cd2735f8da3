{"_id": "http-errors", "_rev": "101-b41aff96fbd9c2a9a4300958aa56b669", "name": "http-errors", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.1": {"name": "http-errors", "version": "0.0.1", "keywords": ["util", "errors", "http"], "author": {"url": "Egesté", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "http-errors@0.0.1", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}], "url": "https://github.com/egeste/http-errors", "dist": {"shasum": "caa1ff00ef680ee6cef845d4dd5e23aecc2617e0", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-0.0.1.tgz", "integrity": "sha512-gESXK83CZRUyfGJ4Dm22Fh2S3ZXN0D5k/TsDhGggqGLVhC7h7neF7kytK/7KljwagQuo2TCnW/jTTPJJU6a+1A==", "signatures": [{"sig": "MEQCIHsvlA0eM2Vq5BIuifRbNQtpMY4Z2tKS0CGEG0iXmoxSAiBeZWuwXp30u9aNp1y4GttJFyLfm5fKl9qPTDd1IwC1xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "http-errors.js", "engines": {"node": "*"}, "_npmUser": {"name": "egeste", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/egeste/http-errors.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "A node module that returns a hash of Error classes representing HTTP errors indexed by error code.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.0.0": {"name": "http-errors", "version": "1.0.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.0.0", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "bd58a089bfec699480300e472e0d552211bce27c", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.0.0.tgz", "integrity": "sha512-b8nX9FQzRL9ky985pqFJ36YoR+lwNfug6Er2Kr6vwPfxv5MHUuHXSU4IGY2BXxVByba0HPgarM37mUEG6VH+Ig==", "signatures": [{"sig": "MEYCIQCbw6JPHnklnaBh4Lm9pfsX5piB/0qCWCW8MFArTQcj+gIhAL0M1knsjiqHYl+Qg21pgYNbDGdapgg6a/HJfW3d3hT3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "bd58a089bfec699480300e472e0d552211bce27c", "gitHead": "afcbd1aab20d555acca14efb75263bcabee25482", "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "1.4.26", "description": "Create HTTP error objects", "directories": {}, "dependencies": {"statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.0.1": {"name": "http-errors", "version": "1.0.1", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.0.1", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "6b770d23b04759f166b0904200aa50775ce2b4a8", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.0.1.tgz", "integrity": "sha512-y+3r3ktrZrONN3d9DIUUo8+zwNbl776Q6Mp7xMMzN7SFPHAy36IJOp2I5pPiExMI7JOlY/8od5KMYQir1jvpgw==", "signatures": [{"sig": "MEUCIQDUU1kcnd+Ueifzvm2XbBunT1Jb3S1mLGm74mLpFu+unQIgCZWHlR1izDhOKanehNRXnKZLfjA/+R4Ulw3QljWv460=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "6b770d23b04759f166b0904200aa50775ce2b4a8", "gitHead": "7d68ca1b35ec91be127ecc178c1155ef7f7f3e74", "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "1.4.26", "description": "Create HTTP error objects", "directories": {}, "dependencies": {"statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.1.0": {"name": "http-errors", "version": "1.1.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.1.0", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "fc2efe9e9ddead125e6b82023eee7eb3e7785dc1", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.1.0.tgz", "integrity": "sha512-BFDQ68zAgC2gpW0AZWnFEZlkPG9Xv6eoEtvuKFOI0WEq+UMeitGT2IT1X73fWL5qBwoQYkhK9nFlSlX9oy42bw==", "signatures": [{"sig": "MEYCIQCX0mAu+w05UPJaFC7G+PuarhMZNJHLRZe3t+Od+kJdqAIhAOxITMjOwSAeCBcn//sHWVOBUzd72D3L0K4UEpFaKGRj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "fc2efe9e9ddead125e6b82023eee7eb3e7785dc1", "gitHead": "7b59d5f38857d25e881276c17b4775c7925c611f", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "1.4.26", "description": "Create HTTP error objects", "directories": {}, "dependencies": {"statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.0": {"name": "http-errors", "version": "1.2.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.0", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "936739e42c3e9b778d84b30bce32802fd5eb9c75", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.0.tgz", "integrity": "sha512-qK1h4snJa1/uRH8wJR6oqOj0JgiJD2AzjPCg+JTHv7vBtPsVAwkvONn1SIm2Q8Wk/CLr6ENig3hKH0PD/3+H5w==", "signatures": [{"sig": "MEUCIQDEVBdDIm5yajDlbalOHRK0Vk7Vo2TMPh+7RBIeST88owIgAh0C8QqZdr6oYIkTLKeB1eM/zVaMsYxhEm5lGO364JM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "936739e42c3e9b778d84b30bce32802fd5eb9c75", "gitHead": "355036ba35cb1bc79a11a720e83c76ef8596fcde", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "1.4.26", "description": "Create HTTP error objects", "directories": {}, "dependencies": {"statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.1": {"name": "http-errors", "version": "1.2.1", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.1", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "ad25618756c76137f6f28d6aac76224559daf7bf", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.1.tgz", "integrity": "sha512-+Hc8NsyWP4VRF6AUKOd7LcalNv0yvkT546Ki65PycaodKssei7IMhsl/qN3ZEhOI3z1v8NzFcItUf9c5ExENug==", "signatures": [{"sig": "MEUCIQCctYjXpUPQmEU5HI6mLP8RI6fMBggRPBRjHr7Fx9yaFAIgSYP0dH0JG//eNSk56NnkQ5GIg9HqmrB138dt9iplAqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "ad25618756c76137f6f28d6aac76224559daf7bf", "gitHead": "5ec45187d58c85bb83fed7e7583437977b249688", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "2.0.2", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "0.11.13", "dependencies": {"inherits": "^2.0.1", "statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.2": {"name": "http-errors", "version": "1.2.2", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.2", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "ee6fac5b7711f7d5c74c8d8e9ac3d9bb68697540", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.2.tgz", "integrity": "sha512-Pnij+HeF8I4tmQGkjahjgkVTb83jZ3h8OHadmANWGh5D3EOhgEKT05StGWVlrsG3DHxR7ay8C4Au9vfqEu298g==", "signatures": [{"sig": "MEUCICLPcv315lX/vClP5prHsdGa+I8b3wm4fVQhGlh2WcFaAiEA5tyWVjTuCBKlie7c782XEGGgm9up4wwYqBlOd2FbMW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "ee6fac5b7711f7d5c74c8d8e9ac3d9bb68697540", "engines": {"node": ">= 0.6"}, "gitHead": "84fa640675339c5c5dd915837b4932a19c2c839a", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "2.0.2", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "0.11.13", "dependencies": {"inherits": "^2.0.1", "statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.3": {"name": "http-errors", "version": "1.2.3", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.3", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "1e08daccbebfb175edfb1a11409f9d687fee488c", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.3.tgz", "integrity": "sha512-WnbjnFHYr/RugJC1ER253rPoSGastIwrNZZnufgDJ8h5gvrX9r8Viu/gTYvtml1Dhqn60sW932hebYuC+vgZzA==", "signatures": [{"sig": "MEUCIQDvdTYYRUGO6fw9utS23lt/LBeK9SD5KSPKq66S0AA9xgIgQ2btV0MxPqqkcy1MLgfHwJYZ7wbiSxHLarc97oGWmGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "1e08daccbebfb175edfb1a11409f9d687fee488c", "engines": {"node": ">= 0.6"}, "gitHead": "1c4dd0def7803585e64f152418864b551a0191dd", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "2.0.2", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "0.11.13", "dependencies": {"inherits": "^2.0.1", "statuses": "~1.1.0"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.4": {"name": "http-errors", "version": "1.2.4", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.4", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "5d7d6d2d27b1917777ad1869bab742b6c53699d2", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.4.tgz", "integrity": "sha512-YCl6D+af8Z4J+kb2mW5gKaelM+T4kgeONlyb9e1SgitNKOq5k2KctSheC0HJ5XpWXmf4oFNq9JjsKTLSQwiqvA==", "signatures": [{"sig": "MEQCIBNgOXPV5bzh6c72/zLHQx6ePN3oZiJ6LdU6dTNj0gWDAiBDe3YC+hRJk6M1CLE/PYJMuR6CsEm7Fc50gs4ZUQ83yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "5d7d6d2d27b1917777ad1869bab742b6c53699d2", "engines": {"node": ">= 0.6"}, "gitHead": "b20af52f8ef4fc975ce5c3b628e6cd4a68089c85", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "2.0.2", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "0.11.13", "dependencies": {"inherits": "~2.0.1", "statuses": "~1.1.0"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.5": {"name": "http-errors", "version": "1.2.5", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.5", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "61da92170b47c12bd11083653e9ed44a9b7abe92", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.5.tgz", "integrity": "sha512-+h7kD3pxhns0JcajJE7VYAhyPaVkdVbbKNNUwaO1W/21WkMxzOpO9CoiT/AEoQBcBXz1dRUuBHr9lDlJlQDXvQ==", "signatures": [{"sig": "MEUCIEIVEXzAKOgXL32w+06RJpc0hzYLZh6xU83i++IqCEL6AiEA2tFY4pyrLmIHlA8Lo112BDL7OBMiGHwCd+Hv/sTcRFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "61da92170b47c12bd11083653e9ed44a9b7abe92", "engines": {"node": ">= 0.6"}, "gitHead": "61c0b37666a71942edac4c6bebb87ea8bc8b7fb9", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "2.0.2", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "0.11.14", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.6": {"name": "http-errors", "version": "1.2.6", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.6", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "7dc790939e6dc6fb90917b12456a11ad8cf05af0", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.6.tgz", "integrity": "sha512-OAruy0HNZg8Z5VrG2z8INh0ZmQWwrHDlhM/vXVnKKHRBmbQAX2kl3OvC0H28sRvai7XiMo+eAUO9enIoj+oVIg==", "signatures": [{"sig": "MEYCIQCbWB+AgrhODyRy0WEVJ2bVN+AjKAm23Lbbs1pTWicrzAIhALV1qzj6yl73r7w4b4nETmAHuXddRef3f0CkUc6qg90j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "7dc790939e6dc6fb90917b12456a11ad8cf05af0", "engines": {"node": ">= 0.6"}, "gitHead": "fab86d5ed56a2046fd9a8aa066283a63d211a5ea", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "2.1.2", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "0.11.14", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.7": {"name": "http-errors", "version": "1.2.7", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.7", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "b881fa12c59b0079fd4ced456bf8dbc9610d3b78", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.7.tgz", "integrity": "sha512-Gay6jtYBa3bAJjfnvuLFQ7BFmU1JfzuCPgL5Noe52NDFFPnSRdtwUKqcWITi2Hekk4lsz3JfLnylVXvlSO2c6w==", "signatures": [{"sig": "MEYCIQD3jn25lqVOREOVUAKnRg5LHQAnsSftp6TETEdnjNoJBQIhAL1YVf0ifJahOA8QFt/E27N8zjiVOLwxW+sS5ZXr5g8o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "LICENSE"], "_shasum": "b881fa12c59b0079fd4ced456bf8dbc9610d3b78", "engines": {"node": ">= 0.6"}, "gitHead": "42172e6b334c873d54ced516ba12db5a461e3878", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "2.1.2", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "0.11.14", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.2.8": {"name": "http-errors", "version": "1.2.8", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.2.8", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "8ee5fe0b51982221d796c0c4712d76f72097a4d0", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.8.tgz", "integrity": "sha512-zqmsJxOyxtIKiQMXiL3PyIlo5d2PWxBzZWAiR3V6Lo/tWX8/n0xrJ5JIr5r+BLeI3obeqi3hoaZkNBCbCcWPZw==", "signatures": [{"sig": "MEUCIFaPERc8tvTVsptjJefz1XXAHxHaY27NFnIZqDaspYjdAiEAoKXLw2+UvSApHIkPEizf6ZVVdmvOV6OBFAhCNkqYVmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "8ee5fe0b51982221d796c0c4712d76f72097a4d0", "engines": {"node": ">= 0.6"}, "gitHead": "c6654476fe3f02013259920cc04d9ae6c3b1ba16", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create HTTP error objects", "directories": {}, "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.3.0": {"name": "http-errors", "version": "1.3.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.3.0", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "239d3bf15d98ea5b3ef553020d60314a2beb2288", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.3.0.tgz", "integrity": "sha512-YLQqN5ayr5ADoZZJ/mChGs9vNyRKPDNMVr4tFFUaic4a3sx/OvD2SV7L9JQNzMB7+2ZaHpVxmVM4vZmMs4nPkQ==", "signatures": [{"sig": "MEYCIQCeg952Idtz4bhZTW5UKaSg6uGk4tXa0WXqnpOA+Oua6gIhANoSsjhISZuhcsW5BzOmKJdSjCP4m9tQzLbMWOndcyUv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "239d3bf15d98ea5b3ef553020d60314a2beb2288", "engines": {"node": ">= 0.6"}, "gitHead": "420a333a3c216cd532cbc46aeaeb0d1879c3e602", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create HTTP error objects", "directories": {}, "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.3.1": {"name": "http-errors", "version": "1.3.1", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.3.1", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "197e22cdebd4198585e8694ef6786197b91ed942", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.3.1.tgz", "integrity": "sha512-gMygNskMurDCWfoCdyh1gOeDfSbkAHXqz94QoPj5IHIUjC/BG8/xv7FSEUr7waR5RcAya4j58bft9Wu/wHNeXA==", "signatures": [{"sig": "MEYCIQDBDRhT3VNxND2UqkbcgCyPtxlqMsqWC6/c9kLHZ4Ne4gIhAM91FsjyuxnK4AH23WRHKPBxSOmPmXs8QzyRwp7YmQpF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "197e22cdebd4198585e8694ef6786197b91ed942", "engines": {"node": ">= 0.6"}, "gitHead": "89a8502b40d5dd42da2908f265275e2eeb8d0699", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create HTTP error objects", "directories": {}, "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}}, "1.4.0": {"name": "http-errors", "version": "1.4.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.4.0", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "6c0242dea6b3df7afda153c71089b31c6e82aabf", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.4.0.tgz", "integrity": "sha512-oLjPqve1tuOl5aRhv8GK5eHpqP1C9fb+Ol+XTLjKfLltE44zdDbEdjPSbU7Ch5rSNsVFqZn97SrMmZLdu1/YMw==", "signatures": [{"sig": "MEUCIQC57I1dghaDZRrjWiLRN/bMzx/kCY/+vLVoxJUnY5P0UAIge4RydDpYIFbaIIvVbXB2d9eQgFxG3dEdyQooBTHE338=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "6c0242dea6b3df7afda153c71089b31c6e82aabf", "engines": {"node": ">= 0.6"}, "gitHead": "3d49066ba40bd7f512bc4cf367bbe650c5f2191f", "scripts": {"test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"inherits": "2.0.1", "statuses": ">= 1.2.1 < 2"}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.2"}}, "1.5.0": {"name": "http-errors", "version": "1.5.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "b1cb3d8260fd8e2386cad3189045943372d48211", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.5.0.tgz", "integrity": "sha512-m6dVUhBY0MfvCPNMadX4gsNsII/zhgRSXTy4BnMjVF3XTZT72t/+RREzH39T+Nma0YX3qywcTS7Y33DPNOlm4g==", "signatures": [{"sig": "MEUCIQCkqt6dxC0nnFGhhdTDONiUqOVU4NvCWyPbx/Oe9ce8oAIgKuLTN2FZ/dp+IPWscZQ/U/bH2wjw9xVGuglc4FqI/jA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "b1cb3d8260fd8e2386cad3189045943372d48211", "engines": {"node": ">= 0.6"}, "gitHead": "1a826d7ac31dde16931b9c566041697939ebd0e0", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"inherits": "2.0.1", "statuses": ">= 1.3.0 < 2", "setprototypeof": "1.0.1"}, "devDependencies": {"mocha": "1.21.5", "eslint": "2.10.2", "istanbul": "0.4.3", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.5.0.tgz_1463621678183_0.44013352948240936", "host": "packages-16-east.internal.npmjs.com"}}, "1.5.1": {"name": "http-errors", "version": "1.5.1", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "788c0d2c1de2c81b9e6e8c01843b6b97eb920750", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.5.1.tgz", "integrity": "sha512-ftkc2U5ADKHv8Ny1QJaDn8xnE18G+fP5QYupx9c3Xk6L5Vgo3qK8Bgbpb4a+jRtaF/YQKjIuXA5J0tde4Tojng==", "signatures": [{"sig": "MEUCIQCZRnMIVqX2BvBSo885e82lX7EdAQ8w4UlKEk6byKPhGQIgahAeE9cGe6HylZI1zvM6GykIIml5Zl4dLY7EBngB3ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "788c0d2c1de2c81b9e6e8c01843b6b97eb920750", "engines": {"node": ">= 0.6"}, "gitHead": "a55db90c7a2c0bafedb4bfa35a85eee5f53a37e9", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/http-errors", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create HTTP error objects", "directories": {}, "dependencies": {"inherits": "2.0.3", "statuses": ">= 1.3.1 < 2", "setprototypeof": "1.0.2"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.10.2", "istanbul": "0.4.5", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.5.1.tgz_1479361411507_0.47469806275330484", "host": "packages-18-east.internal.npmjs.com"}}, "1.6.0": {"name": "http-errors", "version": "1.6.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "113314b3973edd0984a1166e530abf5d5785f75c", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.0.tgz", "integrity": "sha512-iUinoMS3zSVRD9lG/jRYQGxxFV+w0NAJ8KTnqfOv6BLp5xLRT0DTae0sJSdViXcEPZgEpgaToJekeHRmaF76Iw==", "signatures": [{"sig": "MEQCIHXDc+QX1+rFki4qVP18pKk9XuuHmFVz6YipfQdCPgp9AiBwWCruXJFZaQ/XM27Uw99PzEPdXgtHnTLvvpq/PqWbIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "113314b3973edd0984a1166e530abf5d5785f75c", "engines": {"node": ">= 0.6"}, "gitHead": "2855ff9de28a08a660c48cdfe95cb55574f97cbb", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"depd": "1.1.0", "inherits": "2.0.3", "statuses": ">= 1.3.1 < 2", "setprototypeof": "1.0.2"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.15.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.6.0.tgz_1487125936374_0.6465415093116462", "host": "packages-18-east.internal.npmjs.com"}}, "1.6.1": {"name": "http-errors", "version": "1.6.1", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "5f8b8ed98aca545656bf572997387f904a722257", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.1.tgz", "integrity": "sha512-O/lLS06486+l1FVAXghMHAJB3tCg2R5jvGnIQ47X4K1FzfsVs51djHpmwPSzkJ77qIihOV8ONelhSBgfHGG0FA==", "signatures": [{"sig": "MEUCIGOZt7pJnbbtPfCWRpSU/14LYRgcg+qQwAscQbhQH7kAAiEA/p5F9FgA/i+0Ig+er3IZkYPQ1g9eJsSZbsSk0U09WyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "5f8b8ed98aca545656bf572997387f904a722257", "engines": {"node": ">= 0.6"}, "gitHead": "d8d95dbc84c913a594b7fca07096697412af7edd", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"depd": "1.1.0", "inherits": "2.0.3", "statuses": ">= 1.3.1 < 2", "setprototypeof": "1.0.3"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.16.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.6.1.tgz_1487647400122_0.1305304525885731", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.2": {"name": "http-errors", "version": "1.6.2", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.6.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "0a002cc85707192a7e7946ceedc11155f60ec736", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.2.tgz", "integrity": "sha512-STnYGcKMXL9CGdtpeTFnLmgMSHTTNQJSHxiC4DETHKf934Q160Ht5pljrNeH24S0O9xUN+9vsDJZdZtk5js6Ww==", "signatures": [{"sig": "MEUCIQCO57DN1uCGiTmOhdMCAexhxIArEoHLukvrRct1KjeHnwIgDt4OEP4b4GcHIWOM3siOy0ccyUfZ/ZYH7siL45hyHwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "0a002cc85707192a7e7946ceedc11155f60ec736", "engines": {"node": ">= 0.6"}, "gitHead": "7e534cb45fc06e8c3ad782cde89a7462851b27d1", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"depd": "1.1.1", "inherits": "2.0.3", "statuses": ">= 1.3.1 < 2", "setprototypeof": "1.0.3"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.6.2.tgz_1501906124983_0.24086778541095555", "host": "s3://npm-registry-packages"}}, "1.6.3": {"name": "http-errors", "version": "1.6.3", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "8b55680bb4be283a0b5bf4ea2e38580be1d9320d", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "fileCount": 5, "integrity": "sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==", "signatures": [{"sig": "MEUCIQCDb7XpbhTFWjlgkxUQbLszNdKnhxQXPx7/z21o0K07jQIgX7HTwWNigrt0bx27Up/V+3Qk9vIkjRnn0JE0U7zkTb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15829}, "_from": ".", "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "_shasum": "8b55680bb4be283a0b5bf4ea2e38580be1d9320d", "engines": {"node": ">= 0.6"}, "gitHead": "5f53811a1a1756997a73ce7660eb55037f43b9dc", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "6.13.1", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "statuses": ">= 1.4.0 < 2", "setprototypeof": "1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "1.21.5", "eslint": "4.18.1", "istanbul": "0.4.5", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.9.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.6.3_1522355346646_0.04474382722688719", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "http-errors", "version": "1.7.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "b6d36492a201c7888bdcb5dd0471140423c4ad2a", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.0.tgz", "fileCount": 5, "integrity": "sha512-hz3BtSHB7Z6dNWzYc+gUbWqG4dIpJedwwOhe1cvGUq5tGmcTTIRkPiAbyh/JlZx+ksSJyGJlgcHo5jGahiXnKw==", "signatures": [{"sig": "MEUCIBitCM8MWDRxOZSSykR8EhyXWoYy4TzwimgGVSNwsdHcAiEA7x6xwmyTozPj/4fjRXFzXukzowd/CxnGZCoQmHD7SFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX0gUCRA9TVsSAnZWagAAEfYP/i1jvg9LMStbGgVRvHX/\nKCmUlRm/7/qHjTWab9+78OKfSc58omNZcLThSY9qjTn6Ac9eVxUMwwcoTfnG\nPMNrz7utKjn8WAyaJDeFKHKdFwOW9CJjRhnp5cSgCgchl5a+KER0XsPyA2FH\n2mzoiWImToSE+2yjFgRuSYWiAs36qJHuDz9w/w4ina1AoGK16s1xHG2dfPm1\nm9vVRRMEY4R0wXaVboNvLa0CIMmgNRvjA2NOJhYg5/5zgTnDGyf7LdfSamQn\naU4prOmZw0e4xG/AVL+A81dkVURmpmwXckIPfg3BQXUWLQdcpJ4K++AjLiTU\nDljWxRsbExveGYSWiJ9D/LgXI6CGDmpnqyWUVH2eywFYBi40TxAnhDoKwZoe\nTmLFTQpFDXN+y/YA1YnWJxMy2/o6Cx3veGsPEKUVM+DPGccADg+KhDoK4u3Z\nWvh6+zQCn+g93SMhmWabJiXqdPQzgvyvF1gmqhf+EYtV15ODwBWa1dVc40hz\nSaRH62lu82aIT+zg4NADTCFggjzuhvMsyoHSEiml9J+dZs9gap1figWLtw68\nFxcc8PnE6o5ScPlu4NjmocvmbtUOn3ruX8PWU2d+cqyYmoOgDHxf58gOy8i5\ncu32x/mGqTL8MgKN5zbb107hV9Cs/yewBcq+V1FmyZbtx01k4ry6lQ9Mjk6/\nnyJ6\r\n=e9ve\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "HISTORY.md", "LICENSE", "README.md"], "engines": {"node": ">= 0.6"}, "gitHead": "e2bdd75b81b2b7b168f066c61d1f0083d84daae2", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "1.21.5", "eslint": "4.19.1", "istanbul": "0.4.5", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.13.0", "eslint-plugin-promise": "3.8.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.7.0_1532971028236_0.4059577779248733", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "http-errors", "version": "1.7.1", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "6a4ffe5d35188e1c39f872534690585852e1f027", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.1.tgz", "fileCount": 5, "integrity": "sha512-jWEUgtZWGSMba9I1N3gc1HmvpBUaNC9vDdA46yScAdp+C5rdEuKWUBLWTQpW9FwSWSbYYs++b6SDCxf9UEJzfw==", "signatures": [{"sig": "MEUCIQCVyFlV43mkCsMNJwFkJxClRyS3Bc1mJyh0nXTdOjTvQQIgCU9YXDYTK1qirvhFLj9Rpgxu9NxQg/YbATfLXEqVRgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblEpBCRA9TVsSAnZWagAArHkQAIG9Rs9Uo3u3p9nJTVYY\nNziZEMW5gOnu7oCxvSO+1iZZlgiQnhtiHWrTbnCOh3yKhBtENyDxdJ95Lnm6\nXkld0fnCntbysQCODV7eGf6qSkSLSieVFiyl6wHOxx7YIYY0n5JOr8o1NlM4\nSQh6U19IOdnz2Vjyhd4nSaszRhLwfiM2zqObqxTl9wd6S3sXP9pmAMY7EkDA\no228jIJGzYrCJyR1wDUAbKCCvwKsKCRaJobCFyqSzjF0agcy8RihbjAIMOQ+\nnhSjrM/nT+JjzpYHCUceprcx+fi31bO7V579RT4tU2FwHungVISL6KOYYyZ7\n0sr1pZ047ExXz6iy+4O5VdpUcnPmKgl2lnXJaumCS9+0j5bSiQ/cRGFPbRyd\nDu8nX6WPI4i3OMnVUYnxy8Hbw63XxgxGEwAMG1mOEIadKQdm/pyuFTYtO+Ri\nDje6i8p1BZ3SPjN3bxQgzY4xuxOS99zKUeojKNcuxwEqGEekfTe797gstYm/\ng2ePtRL+V2IuzXTISdwIJyjt3MdPBNhJ1dXf7dOCGU6JMWYyog/T/iyHVUt3\nZFfTPR+0PIU6V0ZYIRlZYeiP/RWzEvAAmk5eHwyCdSey1wErVmjS4g1k4KbB\nm5mh9e6In/IR/crze1xUQ6zACMt26W0MwOjh4IHAoa+5jimBMgTxUs+SfnWb\nMVyw\r\n=Y6c2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "e2d0c45500c62c81e319070aa840bb72132d8de4", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "1.21.5", "eslint": "5.5.0", "istanbul": "0.4.5", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.7.1_1536444992650_0.44494595833851713", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "http-errors", "version": "1.7.2", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.7.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "4f5029cf13239f31036e5b2e55292bcfbcc85c8f", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.2.tgz", "fileCount": 5, "integrity": "sha512-uUQBt3H/cSIVfch6i1EuPNy/YsRSOUBXTVfZ+yR7Zjez3qjBz6i9+i4zjNaoqcoFVI4lQJ5plg63TvGfRSDCRg==", "signatures": [{"sig": "MEQCIHXL33Xvmq01+Id4i/WVvPpyL63KhHvxzEh7+rFXL+hCAiABX0cU+h/z8ru8YovSsJKsIuAuWHN/4WQTI5oCMScfQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17086, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcaw02CRA9TVsSAnZWagAAM20P/RYOFi+Y8YJaNae4ZdMA\nx1cD68fmYF8nt8GgOx6MopK9YbsAxJUfpYYdIrg3IaRw7aqHwQGPmaDCa8fL\nUoDVy7ZcqlJLOBGkaJ4YaW4ZX4NFLsSBPhj7KLbmDVBhtQfHPisboMDT+LPk\nou5F2X6QqvQVq4VDlX+zI+Zm0FJmsMabdBJTadofNRoQnJrP70UxyqdoOQNT\nfs96MjO6+FQBE31ln8ZncEsI7MRlq46VnKznbsQ5ghg19MKl5+9czs/jaoaK\nWKf/pfChqFqxvibhmEU/9a09hFqWJLYQ7P6GJENBieWOpPybsOl/JcmPzaUH\nW66tvwTUJFGY3nI89Rqb8s47vj4ToUadzWuz0sZc/VVBahF4Que7riQKzDlw\nb4W92jDyILs8UOlTeHy28CVbA/fm1huLTNtDwjM4iZ9SutZMj/DoxjArGLpR\nOcdLmjlv7nL3p5Sieqsvx5pc06pgGpJH6MCE5XGd11NM5ukkocVi2Kwuu986\nuTKRcXVOXETUYcK/BlkLDwWTsgCcE9pPl4Dy6WOb0ibvcRlJWKwLVBFL40Y0\nMl4//MJidrAl4cTWXBP1e7ILNFS2MSJwSjCUF2eHBvYLvnKo6vS3uPfW35ag\nQdo0pRBwKHczPRzi7gwJSgPCQ/Oi6RQZfxyGe/oYArCQfr4SMSJVjN9LA2Br\nMXTr\r\n=XLau\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "5a61a5b225463a890610b50888b14f16f518ac61", "scripts": {"lint": "eslint --plugin markdown --ext js,md . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "5.2.0", "eslint": "5.13.0", "istanbul": "0.4.5", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.7.2_1550519605679_0.17142404315170912", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "http-errors", "version": "1.7.3", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.7.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "6c619e4f9c60308c38519498c14fbb10aacebb06", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.3.tgz", "fileCount": 5, "integrity": "sha512-ZTTX0MWrsQ2ZAhA1cejAwDLycFsd7I7nVtnkT3Ol0aqodaKW+0CTZDQ1uBv5whptCnc8e8HeRRJxRs0kmm/Qfw==", "signatures": [{"sig": "MEYCIQCo5lAVB5/lIzSkLMddvUa+irYaCp4HyGXJOT/xiBRUjQIhAKNbhgBEcQfwzVGzhZAabe+sBrh0BjTON4w7So+0r37k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEVruCRA9TVsSAnZWagAA8wUP/Repl5BiN8sPmq7Iy7Yt\nOe1hCpAJE64L+uYRPZm8hV+Wkli7J4/4UyLf9k20JWiU89axyTQzFT/XpLYc\nsw+knAnUwzVDUQvlyNDUllLkLkdhw07VhM7TeL8yym9tem9ghsg8UJZZS3H8\nPXWIqDgVbVqVcWomSiq543tXO+5NG3sqX2pVoGKGISGn/rJhiJvDcBvjYT5i\nv5hwFDK/av11koTioT9As1FR+Pdltj2KetA/SZ7IeIzRc0FPulj2k53c4zjQ\nzazKmL6dnEf2afZuN6clQd+clO8fz1+S58DP+EJj7Cvom2ANvpOlQs3hZchB\nvQOrInn8kcebNzgkTh1n4HdIwHeMEjOgS8c0TxEd2WiQvLRyvPPeTPzEglTx\nmpEhUAWo9xs1nkqQ7/RZZHTni2jRkCUM6saG4hC/kXdx79fiiu7q6BLhF5Wi\nAqb6egAxgmHsaBbDajk/pJk8nsaq+el16Aq69M+heSDCQXV1uzsxCzdimljL\nO+dRFBLKhgSOulIjwM6OraKXh8uIKABPqgAUDMsctFI5gru9ZeXqTNvzNj0g\neFm2elBXn8yjrAOShsx/HUDysNsZ3Y8W1sx6tjFREYmWppAIYjtMOocVIwo6\n7BxMAq9+fl7mSlTBZmrFj0oCfFrGGL3ZzO8vKv1hGeKpslHohoePMiwgiDmE\n2lpi\r\n=LjKs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "a91d0ad87925a791d12fccdd6622ed3fc10fdafd", "scripts": {"lint": "eslint --plugin markdown --ext js,md . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.18.0", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.7.3_1561418478074_0.7698818220827124", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "http-errors", "version": "1.8.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "egeste", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "75d1bbe497e1044f51e4ee9e704a62f28d336507", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.8.0.tgz", "fileCount": 5, "integrity": "sha512-4I8r0C5JDhT5VkvI47QktDW75rNlGVsUf/8hzjCC/wkWI/jdTRmBb9aI7erSG82r1bjKY3F6k28WnsVxB1C73A==", "signatures": [{"sig": "MEQCIBLUJj0jMfj7QYSqPzixQLsa2mrc0tpbVBE1LEkV61RYAiBkKiqDR19Yd8lQRinMyUyWGLOpSsMeVuZkDdTWtnI3Sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+X79CRA9TVsSAnZWagAAv6wP/RjIshvYJXVRbRP0mb4a\nOvN2mK7/B2nqnD8O2FzgEQ0p1Llb1K9j6TlWdq/cJInioDoR42yQgfyj4FEL\nonR3TgUlE8EjQFhH/ecYF6eaLVNaCTerd3CwdOXAXorzPcCE3g8sx46aB+9p\na/QIvOnNxt9UuDsJDDkpWvVGksr0JwWW0ZEjx0Gw8b4j1GOLXjPzFd4WqOKr\nVXG8KcAs76ZCX4eGIczwRuTpOe8mXITrtqkgyzdM98+PONQsIWEWwjt0tzm0\nMNn9cUfgB0A2UXrHQK0OFuP3TV/Ms3kTvLkvFBkiaxcpuGBlwJXSdK2/j6wA\n88Xqqxx2g70mlCgHfeHOt8GHOnYiCrsCLO5wyhJXkeSipeJM9r/8coZfZEV8\ntGXkLBxAKPInBMzGaIPT9PNsOrTy2JV1Qz2MV1OQU//LokWLLbwxsB54vXVh\nuuQSjjwUYDzA5V52/uBgfPThcM8yC0nYvU7qXV9eb3f2j3f5UJdcVkV/Cr1Z\nHtOqzHPPzY/dDubMfCoufMc3SnFV6fK5IFRmzjlRWLoKy1BZ1bLbmWxhBFeK\n88wD4p0vvSNuPNmXK4ObgxCH6NSUft+4IaHRnpBjPShoV0uyuTktOG1oLxyk\nHgG01wBJcVsUE76idV2fc4VLAMs3l1IiX4kb9+qgEYecsHA5LTuEAHl2aGPr\ngTBl\r\n=OG/P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "6e4f655ec3a0cedf2e3ce868daa11b9210d1f103", "scripts": {"lint": "eslint --plugin markdown --ext js,md . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "8.0.1", "eslint": "6.8.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.0", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.8.0_1593409277161_0.44050373643256746", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "http-errors", "version": "1.8.1", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@1.8.1", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "7c3f28577cbc8a207388455dbd62295ed07bd68c", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.8.1.tgz", "fileCount": 5, "integrity": "sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==", "signatures": [{"sig": "MEQCID89qas+iwQ7DmOfk9Qma9V3ud0rDp5ksfAbEQ1Aa7sBAiBxAAok+C/98M+dzJ4UQu0EiRPEwNCrogENX9THyG5x9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18316}, "engines": {"node": ">= 0.6"}, "gitHead": "a3010a313e33bf0bd9b12158f0d63c0d669519bc", "scripts": {"lint": "eslint . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.1", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.8.1_1636932034227_0.977387622150024", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "http-errors", "version": "2.0.0", "keywords": ["http", "error"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "http-errors@2.0.0", "maintainers": [{"name": "egeste", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/http-errors#readme", "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "dist": {"shasum": "b7774a1486ef73cf7667ac9ae0858c012c57b9d3", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "signatures": [{"sig": "MEQCID1XrfQ8ZFxajv1NEQ/9Riw7BeBDOJ01mbr8+OpDAncpAiAt33KnKPmlGRwT7hlvNBgWimpDHlRcvgL8/zyrUq7VZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvVXiCRA9TVsSAnZWagAA4OwP/3QPkXOVfeSayHzOjAvN\nl54inTVNaO1VHEg6sIfaSq3mfqXcN6vdISilMP5/VAxSNAxGI8FqX4wc4FDp\nrC1rQc5V9cMNC0PioqkjZMImQin0JYrbOQUUxReMjZozddY7plN3A1YOfAXZ\n+HtsavBLOdHQP9XKtbroQZhxptbo2dG8Kk12RAgdKz4Ypfe9r7L/FesYJ2A8\nrMZDL2JnMSC0TeQUd9yKU6rMlH7yQg3UmARgqTtr+BVLuYsgIRXUpfp2Sxd/\nKelg+XwKtJdykq4aF/1cYGXX0uWh6iNUlSmabXSxwKhRD7UB0aIBPn+Dr5jl\nQsK88LTHC4F+aeTUCiEvlcGb9WywsK1wJzV8P6aoP/cmlGZqUThZHZ+ZRboo\n/Mz/adRVFsiPFiYcfJXlsHhFqaiZm5aF/ZUU7NjrxWWAiqIIevl0ysUtCNpQ\n2SICRsp+h9QiwfmhjjAI1Om0FH1DRY4vlohVUzqiaXGBbOQinxKk2dqUtfS3\nyIyUdZDauccKrhMnbJH0wVky41EGUAfehtbiiHhnLg7fnqxejmyCchEUFrfo\ns9g+vhN6Svn+cjjbl8F8JjqMmtanMONlg/7vCC3fSN2l+2OwMcBzDzRz0kYL\nk/FdknbGitDdcPc52WAwXG1vydD+9B+E7yYX2ERY0fJoZjJ9ugpbMGEfL9n9\n9B41\r\n=5cFj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "206aa2c15635dc1212c06c279540972aa90e23ea", "scripts": {"lint": "eslint . && node ./scripts/lint-readme-list.js", "test": "mocha --reporter spec --bail", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Create HTTP error objects", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "statuses": "2.0.1", "toidentifier": "1.0.1", "setprototypeof": "1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/http-errors_2.0.0_1639798242023_0.673094171428414", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-11-18T05:58:21.693Z", "modified": "2025-05-14T14:56:20.905Z", "0.0.1": "2012-11-18T05:58:24.328Z", "1.0.0": "2014-09-08T20:04:47.893Z", "1.0.1": "2014-09-08T20:20:32.230Z", "1.1.0": "2014-09-09T05:00:38.695Z", "1.2.0": "2014-09-09T18:10:07.228Z", "1.2.1": "2014-09-22T00:44:17.769Z", "1.2.2": "2014-09-22T00:44:44.079Z", "1.2.3": "2014-09-22T00:55:10.961Z", "1.2.4": "2014-09-22T01:02:14.380Z", "1.2.5": "2014-09-29T04:22:37.194Z", "1.2.6": "2014-10-03T07:59:29.863Z", "1.2.7": "2014-10-15T04:16:16.798Z", "1.2.8": "2014-12-09T20:41:08.043Z", "1.3.0": "2015-02-02T01:09:28.350Z", "1.3.1": "2015-02-03T02:06:48.009Z", "1.4.0": "2016-01-29T05:29:34.421Z", "1.5.0": "2016-05-19T01:34:41.193Z", "1.5.1": "2016-11-17T05:43:32.057Z", "1.6.0": "2017-02-15T02:32:16.946Z", "1.6.1": "2017-02-21T03:23:22.251Z", "1.6.2": "2017-08-05T04:08:45.938Z", "1.6.3": "2018-03-29T20:29:06.751Z", "1.7.0": "2018-07-30T17:17:08.342Z", "1.7.1": "2018-09-08T22:16:32.820Z", "1.7.2": "2019-02-18T19:53:25.820Z", "1.7.3": "2019-06-24T23:21:18.236Z", "1.8.0": "2020-06-29T05:41:17.267Z", "1.8.1": "2021-11-14T23:20:34.351Z", "2.0.0": "2021-12-18T03:30:42.140Z"}, "bugs": {"url": "https://github.com/jshttp/http-errors/issues"}, "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/http-errors#readme", "keywords": ["http", "error"], "repository": {"url": "git+https://github.com/jshttp/http-errors.git", "type": "git"}, "description": "Create HTTP error objects", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "egeste"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "readme": "# http-errors\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][node-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nCreate HTTP errors for Express, Koa, Connect, etc. with ease.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```console\n$ npm install http-errors\n```\n\n## Example\n\n```js\nvar createError = require('http-errors')\nvar express = require('express')\nvar app = express()\n\napp.use(function (req, res, next) {\n  if (!req.user) return next(createError(401, 'Please login to view this page.'))\n  next()\n})\n```\n\n## API\n\nThis is the current API, currently extracted from Koa and subject to change.\n\n### Error Properties\n\n- `expose` - can be used to signal if `message` should be sent to the client,\n  defaulting to `false` when `status` >= 500\n- `headers` - can be an object of header names to values to be sent to the\n  client, defaulting to `undefined`. When defined, the key names should all\n  be lower-cased\n- `message` - the traditional error message, which should be kept short and all\n  single line\n- `status` - the status code of the error, mirroring `statusCode` for general\n  compatibility\n- `statusCode` - the status code of the error, defaulting to `500`\n\n### createError([status], [message], [properties])\n\nCreate a new error object with the given message `msg`.\nThe error object inherits from `createError.HttpError`.\n\n```js\nvar err = createError(404, 'This video does not exist!')\n```\n\n- `status: 500` - the status code as a number\n- `message` - the message of the error, defaulting to node's text for that status code.\n- `properties` - custom properties to attach to the object\n\n### createError([status], [error], [properties])\n\nExtend the given `error` object with `createError.HttpError`\nproperties. This will not alter the inheritance of the given\n`error` object, and the modified `error` object is the\nreturn value.\n\n<!-- eslint-disable no-redeclare -->\n\n```js\nfs.readFile('foo.txt', function (err, buf) {\n  if (err) {\n    if (err.code === 'ENOENT') {\n      var httpError = createError(404, err, { expose: false })\n    } else {\n      var httpError = createError(500, err)\n    }\n  }\n})\n```\n\n- `status` - the status code as a number\n- `error` - the error object to extend\n- `properties` - custom properties to attach to the object\n\n### createError.isHttpError(val)\n\nDetermine if the provided `val` is an `HttpError`. This will return `true`\nif the error inherits from the `HttpError` constructor of this module or\nmatches the \"duck type\" for an error this module creates. All outputs from\nthe `createError` factory will return `true` for this function, including\nif an non-`HttpError` was passed into the factory.\n\n### new createError\\[code || name\\](\\[msg]\\))\n\nCreate a new error object with the given message `msg`.\nThe error object inherits from `createError.HttpError`.\n\n```js\nvar err = new createError.NotFound()\n```\n\n- `code` - the status code as a number\n- `name` - the name of the error as a \"bumpy case\", i.e. `NotFound` or `InternalServerError`.\n\n#### List of all constructors\n\n|Status Code|Constructor Name             |\n|-----------|-----------------------------|\n|400        |BadRequest                   |\n|401        |Unauthorized                 |\n|402        |PaymentRequired              |\n|403        |Forbidden                    |\n|404        |NotFound                     |\n|405        |MethodNotAllowed             |\n|406        |NotAcceptable                |\n|407        |ProxyAuthenticationRequired  |\n|408        |RequestTimeout               |\n|409        |Conflict                     |\n|410        |Gone                         |\n|411        |LengthRequired               |\n|412        |PreconditionFailed           |\n|413        |PayloadTooLarge              |\n|414        |URITooLong                   |\n|415        |UnsupportedMediaType         |\n|416        |RangeNotSatisfiable          |\n|417        |ExpectationFailed            |\n|418        |ImATeapot                    |\n|421        |MisdirectedRequest           |\n|422        |UnprocessableEntity          |\n|423        |Locked                       |\n|424        |FailedDependency             |\n|425        |TooEarly                     |\n|426        |UpgradeRequired              |\n|428        |PreconditionRequired         |\n|429        |TooManyRequests              |\n|431        |RequestHeaderFieldsTooLarge  |\n|451        |UnavailableForLegalReasons   |\n|500        |InternalServerError          |\n|501        |NotImplemented               |\n|502        |BadGateway                   |\n|503        |ServiceUnavailable           |\n|504        |GatewayTimeout               |\n|505        |HTTPVersionNotSupported      |\n|506        |VariantAlsoNegotiates        |\n|507        |InsufficientStorage          |\n|508        |LoopDetected                 |\n|509        |BandwidthLimitExceeded       |\n|510        |NotExtended                  |\n|511        |NetworkAuthenticationRequired|\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/http-errors/master?label=ci\n[ci-url]: https://github.com/jshttp/http-errors/actions?query=workflow%3Aci\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/http-errors/master\n[coveralls-url]: https://coveralls.io/r/jshttp/http-errors?branch=master\n[node-image]: https://badgen.net/npm/node/http-errors\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/http-errors\n[npm-url]: https://npmjs.org/package/http-errors\n[npm-version-image]: https://badgen.net/npm/v/http-errors\n[travis-image]: https://badgen.net/travis/jshttp/http-errors/master\n[travis-url]: https://travis-ci.org/jshttp/http-errors\n", "readmeFilename": "README.md", "users": {"h4t0n": true, "bumsuk": true, "h0ward": true, "isayme": true, "ngpvnk": true, "tchcxp": true, "tedyhy": true, "tiendq": true, "antanst": true, "astesio": true, "azevedo": true, "progmer": true, "robsoer": true, "anhulife": true, "buddh!ka": true, "fabioper": true, "frankl83": true, "fredcorn": true, "leonzhao": true, "simonfan": true, "susuaung": true, "antixrist": true, "azusa0127": true, "igorissen": true, "mojaray2k": true, "zeroth007": true, "alizurchik": true, "goodseller": true, "kankungyip": true, "qqqppp9998": true, "zhanghaili": true, "flumpus-dev": true, "octetstream": true, "zafaransari": true, "scottfreecode": true, "shanewholloway": true, "maximilianschmitt": true}}