#include <stdio.h>

void printEgyptianFraction(int numerator, int denominator) {
    printf("%d/%d=", numerator, denominator);

    while (numerator != 0) {
        int unitDenominator = (denominator + numerator - 1) / numerator; // 向上取整
        printf("1/%d", unitDenominator);

        // 更新分子和分母
        numerator = numerator * unitDenominator - denominator;
        denominator = denominator * unitDenominator;

        if (numerator != 0) {
            printf("+");
        }
    }
    printf("\n");
}

int main() {
    int numerator, denominator;
    printf("请输入真分数的分子和分母（以空格分隔）: ");
    scanf("%d %d", &numerator, &denominator);

    if (numerator >= denominator || numerator <= 0 || denominator <= 0) {
        printf("输入必须是一个真分数（分子小于分母，且分子和分母均为正数）。\n");
        return 1;
    }

    printEgyptianFraction(numerator, denominator);

    return 0;
}
