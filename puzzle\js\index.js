// 定义一个名为speller的对象，用于实现拼图相关功能，包含初始化、创建拼图网格、随机打乱等方法
let speller = {
    init: function (n) {
        // 定义难度属性，接收传入的参数n来确定难度等级
        this.hard = n;
        // 初始化步数和用时属性为0
        this.step = this.useTime = 0;
        // 根据难度确定空白位置，难度为21时空白位置设为8，否则设为14
        this.blank = (n === 21) ? 8 : 14;
        // 调用createGrid方法来创建拼图网格，将图块放入到id为"shell"的DOM元素中
        this.createGrid();
        // 清除之前可能存在的定时器（如果有的话），确保计时准确
        clearInterval(this.timer);
        // 创建一个定时器，每秒更新一次显示时间，将用时以"分钟:秒"的格式展示在id为"times"的元素中
        this.timer = setInterval(() => {
            this.useTime++;
            const minutes = ('0' + parseInt(this.useTime / 60)).slice(-2);
            const seconds = ('0' + this.useTime % 60).slice(-2);
            document.getElementById("times").innerHTML = minutes + ':' + seconds;
        }, 1000);
    },
    createGrid: function () {
        if (this.hard === 21) {
            // 定义一个函数X，用于根据索引计算图块的水平位置（距离左侧的距离），用于3x3的拼图布局
            const X = function (n) {
                return (n % 3) * 100;
            }
            // 定义一个函数Y，用于根据索引计算图块的垂直位置（距离顶部的距离），用于3x3的拼图布局
            const Y = function (n) {
                return parseInt(n / 3) * 100;
            }
            let html = [];
            for (let i = 0; i < 9; i++) {
                // 构建每个图块的HTML字符串，添加点击事件绑定到speller.move方法，并设置样式位置
                html.push('<p onclick="speller.move(this)" id="' + i + '" class="' + i + '" style="left:' + X(i) + 'px;top:' + Y(i) + 'px;"></p>');
            }
            document.getElementById("shell").innerHTML = html.join("");
        } else {
            // 定义一个函数X，用于根据索引计算图块的水平位置（距离左侧的距离），用于4x4的拼图布局
            const X = function (n) {
                return n % 5 * 100;
            }
            // 定义一个函数Y，用于根据索引计算图块的垂直位置（距离顶部的距离），用于4x4的拼图布局
            const Y = function (n) {
                return parseInt(n / 5) * 100;
            }
            let html = [];
            for (let i = 0; i < 15; i++) {
                // 构建每个图块的HTML字符串，添加点击事件绑定到speller.move方法，并设置样式位置以及背景位置（用于展示正确的拼图图片切片）
                html.push('<p onclick="speller.move(this)" id="' + i + '" class="' + i + '" style="left:' + X(i) + 'px; top:' + Y(i) + 'px; background-position: -' + X(i) + 'px -' + Y(i) + 'px;"></p>');
            }
            document.getElementById("shell").innerHTML = html.join("");
            // 调用ran方法，不过当前ran方法功能不全，可根据具体需求完善其功能（比如用于随机打乱拼图顺序等）
            this.ran();
        }
    },
    ran: function () {
        // 获取id为"shell"的元素下所有的<p>标签元素，可用于后续比如随机打乱这些图块顺序等操作，当前只是获取了元素集合，功能待完善
        let ps = document.getElementById("shell").getElementsByTagName("p");
        // 这里可以添加代码逻辑来对获取到的ps集合进行进一步处理，例如随机打乱它们的顺序等
        let l = ps.length;
        let me = this;
        ps[this.blank].style.display = "none";
        let en = function (n) {

        }
    }
};