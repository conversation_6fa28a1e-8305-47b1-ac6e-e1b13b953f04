const axios = require('axios');
const NodeCache = require('node-cache');
const config = require('../config');

// 创建缓存实例
const cache = new NodeCache({ stdTTL: config.cache.ttl });

class WeatherService {
  constructor() {
    this.apiKey = config.openWeather.apiKey;
    this.baseUrl = config.openWeather.baseUrl;
    this.client = axios.create({
      timeout: 10000, // 10秒超时
      headers: {
        'User-Agent': 'Weather-Backend/1.0.0'
      }
    });
  }

  // 生成缓存键
  generateCacheKey(type, params) {
    return `${type}_${JSON.stringify(params)}`;
  }

  // 格式化天气数据
  formatWeatherData(data) {
    return {
      location: {
        name: data.name,
        country: data.sys.country,
        coordinates: {
          lat: data.coord.lat,
          lon: data.coord.lon
        }
      },
      current: {
        temperature: Math.round(data.main.temp),
        feelsLike: Math.round(data.main.feels_like),
        humidity: data.main.humidity,
        pressure: data.main.pressure,
        visibility: data.visibility ? Math.round(data.visibility / 1000) : null,
        uvIndex: null // 需要额外的 API 调用
      },
      weather: {
        main: data.weather[0].main,
        description: data.weather[0].description,
        icon: data.weather[0].icon
      },
      wind: {
        speed: data.wind.speed,
        direction: data.wind.deg,
        gust: data.wind.gust || null
      },
      clouds: {
        coverage: data.clouds.all
      },
      timestamp: new Date(data.dt * 1000).toISOString(),
      sunrise: new Date(data.sys.sunrise * 1000).toISOString(),
      sunset: new Date(data.sys.sunset * 1000).toISOString()
    };
  }

  // 格式化预报数据
  formatForecastData(data) {
    return {
      location: {
        name: data.city.name,
        country: data.city.country,
        coordinates: {
          lat: data.city.coord.lat,
          lon: data.city.coord.lon
        }
      },
      forecast: data.list.map(item => ({
        datetime: new Date(item.dt * 1000).toISOString(),
        temperature: {
          current: Math.round(item.main.temp),
          min: Math.round(item.main.temp_min),
          max: Math.round(item.main.temp_max),
          feelsLike: Math.round(item.main.feels_like)
        },
        weather: {
          main: item.weather[0].main,
          description: item.weather[0].description,
          icon: item.weather[0].icon
        },
        wind: {
          speed: item.wind.speed,
          direction: item.wind.deg,
          gust: item.wind.gust || null
        },
        humidity: item.main.humidity,
        pressure: item.main.pressure,
        clouds: item.clouds.all,
        precipitation: {
          probability: item.pop * 100,
          rain: item.rain ? item.rain['3h'] || 0 : 0,
          snow: item.snow ? item.snow['3h'] || 0 : 0
        }
      }))
    };
  }

  // 获取当前天气
  async getCurrentWeather(city, lang = 'zh_cn', units = 'metric') {
    const cacheKey = this.generateCacheKey('current', { city, lang, units });
    const cached = cache.get(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const response = await this.client.get(`${this.baseUrl}/weather`, {
        params: {
          q: city,
          appid: this.apiKey,
          lang,
          units
        }
      });

      const formattedData = this.formatWeatherData(response.data);
      cache.set(cacheKey, formattedData);
      
      return formattedData;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        throw new Error('城市未找到');
      }
      throw error;
    }
  }

  // 获取天气预报
  async getForecast(city, days = 5, lang = 'zh_cn', units = 'metric') {
    const cacheKey = this.generateCacheKey('forecast', { city, days, lang, units });
    const cached = cache.get(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const response = await this.client.get(`${this.baseUrl}/forecast`, {
        params: {
          q: city,
          appid: this.apiKey,
          lang,
          units,
          cnt: days * 8 // 每天8个时间点（3小时间隔）
        }
      });

      const formattedData = this.formatForecastData(response.data);
      cache.set(cacheKey, formattedData);
      
      return formattedData;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        throw new Error('城市未找到');
      }
      throw error;
    }
  }

  // 通过坐标获取天气
  async getWeatherByCoordinates(lat, lon, lang = 'zh_cn', units = 'metric') {
    const cacheKey = this.generateCacheKey('coordinates', { lat, lon, lang, units });
    const cached = cache.get(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const response = await this.client.get(`${this.baseUrl}/weather`, {
        params: {
          lat,
          lon,
          appid: this.apiKey,
          lang,
          units
        }
      });

      const formattedData = this.formatWeatherData(response.data);
      cache.set(cacheKey, formattedData);
      
      return formattedData;
    } catch (error) {
      throw error;
    }
  }

  // 搜索城市
  async searchCities(query, limit = 5) {
    const cacheKey = this.generateCacheKey('search', { query, limit });
    const cached = cache.get(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const response = await this.client.get(`http://api.openweathermap.org/geo/1.0/direct`, {
        params: {
          q: query,
          limit,
          appid: this.apiKey
        }
      });

      const cities = response.data.map(city => ({
        name: city.name,
        country: city.country,
        state: city.state || null,
        coordinates: {
          lat: city.lat,
          lon: city.lon
        }
      }));

      cache.set(cacheKey, cities);
      return cities;
    } catch (error) {
      throw error;
    }
  }

  // 清除缓存
  clearCache() {
    cache.flushAll();
  }

  // 获取缓存统计
  getCacheStats() {
    return cache.getStats();
  }
}

module.exports = new WeatherService();
