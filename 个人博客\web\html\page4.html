<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>常用页面跳转</title>
    <link rel="stylesheet" href="../css/page4.css">
</head>

<body>
    <header>
        <nav>
            <ul>
                <li><a href="../html/index.html">首页</a></li>
                <li><a href="../html/page1.html">我的学习内容</a></li>
                <li><a href="../html/page2.html">放松</a></li>
                <!-- <li><a href="../html/page3.html">待</a></li> -->
                <li><a href="../html/page4.html">常用页面跳转</a></li>
            </ul>
        </nav>
    </header>
    <main>
        <!-- 添加8个盒子及图片链接，排列成一行四个 -->
        <div class="box-container">
            <div class="box" id="box1">
                <div class="inner-box"></div>
                <a href="https://jwc.yangtzeu.edu.cn/" target="_blank"></a>
            </div>
            <div class="box" id="box2">
                <div class="inner-box"></div>
                <a href="https://www.douyin.com/" target="_blank"></a>
            </div>
            <div class="box" id="box3">
                <div class="inner-box"></div>
                <a href="https://www.bilibili.com/video/BV1Y84y1L7Nn/?p=20&spm_id_from=333.880.my_history.page.click"
                    target="_blank"></a>
            </div>
            <div class="box" id="box4">
                <div class="inner-box"></div>
                <a href="https://vjudge.net/article/4776" target="_blank"></a>
            </div>
            <div class="box" id="box5">
                <div class="inner-box"></div>
                <a href="https://www.bilibili.com/" target="_blank"></a>
            </div>
            <div class="box" id="box6">
                <a href="https://changjiang.yuketang.cn/web" target="_blank"></a>
            </div>
            <div class="box" id="box7">
                <a href="#" target="_blank"></a>
            </div>
            <div class="box" id="box8">
                <a href="#" target="_blank"></a>
            </div>
        </div>
    </main>
    <footer>
    </footer>
    <script>
        // 获取所有的盒子元素
        const boxes = document.querySelectorAll('.box');

        // 遍历每个盒子元素
        boxes.forEach((box) => {
            // 给每个盒子添加点击事件监听器
            box.addEventListener('click', function () {
                // 获取当前盒子内部的链接元素
                const link = this.querySelector('a');
                if (link) {
                    // 如果存在链接元素，模拟点击该链接，实现页面跳转
                    link.click();
                }
            });
        });
    </script>
</body>

</html>