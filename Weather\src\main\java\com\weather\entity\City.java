package com.weather.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class City {
    private int id;
    @JsonProperty("cityName")
    private String cityName;
    @JsonProperty("province")
    private String province;
    @JsonProperty("isHot")
    private boolean isHot;
    
    public City() {}
    
    public City(String cityName, String province, boolean isHot) {
        this.cityName = cityName;
        this.province = province;
        this.isHot = isHot;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getCityName() { return cityName; }
    public void setCityName(String cityName) { this.cityName = cityName; }
    
    public String getProvince() { return province; }
    public void setProvince(String province) { this.province = province; }
    
    public boolean isHot() { return isHot; }
    public void setHot(boolean hot) { isHot = hot; }
}