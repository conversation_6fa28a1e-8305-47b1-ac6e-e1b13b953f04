/*
	Landed by HTML5 UP
	html5up.net | @n33co
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
*/

::selection {
    background: #169ADA;
    text-shadow: none;
    color: #fff;
}

::-webkit-scrollbar {
    width: 10px;
    height: 8px;
}

::-webkit-scrollbar-button {
    display: none
}

::-webkit-scrollbar-track {
    background-color: black
}

::-webkit-scrollbar-track-piece {
    background: #ddd
}

::-webkit-scrollbar-thumb {
    background-color: #e44c65;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #3B3B3B
}

::-webkit-scrollbar-corner {
    background-color: #535353
}

::-webkit-scrollbar-resizer {
    background-color: #FF6E00
}

@font-face {
    font-family: MyFont;
    src: url("/assets/fonts/font.otf");
}

body,
input,
select,
textarea {
    /*font-family: 'MyFont', 'Open Sans', 'Microsoft YaHei', sans-serif!important;*/
    font-family: '微软雅黑', 'Open Sans', 'Microsoft YaHei', sans-serif !important;
    letter-spacing: 1px;
}

.headertop {
    width: 100%;
    display: table;
    min-height: 300px;
    padding: 2em 0 4em 0;
    height: 10vh;
    background: url(./image/index/2.jpg) bottom center;
    background-size: cover;
    background-attachment: fixed;
    box-shadow: 0 0 15px #e44c65;
}

@media screen and (min-width: 1681px) {
    .plive {
        height: 800px;
    }
}

@media screen and (max-width: 1680px) {
    .plive {
        height: 800px;
    }
}

@media screen and (max-width: 1280px) {
    .plive {
        height: 730px;
    }
}

@media screen and (max-width: 980px) {
    .plive {
        height: 580px;
    }
}

@media screen and (max-width: 736px) {
    .plive {
        height: 450px;
    }
}

@media screen and (max-width: 480px) {
    .plive {
        height: 320px;
    }
}

/* Reset */

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

body {
    line-height: 1;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

body {
    -webkit-text-size-adjust: none;
}

/* Box Model */

*,
*:before,
*:after {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

/* Containers */

.container {
    margin-left: auto;
    margin-right: auto;
}

.container.\31 25\25 {
    width: 100%;
    max-width: 87.5em;
    min-width: 70em;
}

.container.\37 5\25 {
    width: 52.5em;
}

.container.\35 0\25 {
    width: 35em;
}

.container.\32 5\25 {
    width: 17.5em;
}

.container {
    width: 70em;
}

@media screen and (max-width: 1680px) {

    .container.\31 25\25 {
        width: 100%;
        max-width: 87.5em;
        min-width: 70em;
    }

    .container.\37 5\25 {
        width: 52.5em;
    }

    .container.\35 0\25 {
        width: 35em;
    }

    .container.\32 5\25 {
        width: 17.5em;
    }

    .container {
        width: 70em;
    }

}

@media screen and (max-width: 1280px) {

    .container.\31 25\25 {
        width: 100%;
        max-width: 112.5%;
        min-width: 90%;
    }

    .container.\37 5\25 {
        width: 67.5%;
    }

    .container.\35 0\25 {
        width: 45%;
    }

    .container.\32 5\25 {
        width: 22.5%;
    }

    .container {
        width: 90%;
    }

}

@media screen and (max-width: 980px) {

    .container.\31 25\25 {
        width: 100%;
        max-width: 125%;
        min-width: 100%;
    }

    .container.\37 5\25 {
        width: 75%;
    }

    .container.\35 0\25 {
        width: 50%;
    }

    .container.\32 5\25 {
        width: 25%;
    }

    .container {
        width: 100% !important;
    }

}

@media screen and (max-width: 736px) {

    .container.\31 25\25 {
        width: 100%;
        max-width: 125%;
        min-width: 100%;
    }

    .container.\37 5\25 {
        width: 75%;
    }

    .container.\35 0\25 {
        width: 50%;
    }

    .container.\32 5\25 {
        width: 25%;
    }

    .container {
        width: 100% !important;
    }

}

@media screen and (max-width: 480px) {

    .container.\31 25\25 {
        width: 100%;
        max-width: 125%;
        min-width: 100%;
    }

    .container.\37 5\25 {
        width: 75%;
    }

    .container.\35 0\25 {
        width: 50%;
    }

    .container.\32 5\25 {
        width: 25%;
    }

    .container {
        width: 100% !important;
    }

}

/* Grid */

.row {
    border-bottom: solid 1px transparent;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.row>* {
    float: left;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.row:after,
.row:before {
    content: '';
    display: block;
    clear: both;
    height: 0;
}

.row.uniform>*> :first-child {
    margin-top: 0;
}

.row.uniform>*> :last-child {
    margin-bottom: 0;
}

.row.\30 \25>* {
    padding: 0 0 0 0em;
}

.row.\30 \25 {
    margin: 0 0 -1px 0em;
}

.row.uniform.\30 \25>* {
    padding: 0em 0 0 0em;
}

.row.uniform.\30 \25 {
    margin: 0em 0 -1px 0em;
}

.row>* {
    padding: 0 0 0 2.5em;
}

.row {
    margin: 0 0 -1px -2.5em;
}

.row.uniform>* {
    padding: 2.5em 0 0 2.5em;
}

.row.uniform {
    margin: -2.5em 0 -1px -2.5em;
}

.row.\32 00\25>* {
    padding: 0 0 0 5em;
}

.row.\32 00\25 {
    margin: 0 0 -1px -5em;
}

.row.uniform.\32 00\25>* {
    padding: 5em 0 0 5em;
}

.row.uniform.\32 00\25 {
    margin: -5em 0 -1px -5em;
}

.row.\31 50\25>* {
    padding: 0 0 0 3.75em;
}

.row.\31 50\25 {
    margin: 0 0 -1px -3.75em;
}

.row.uniform.\31 50\25>* {
    padding: 3.75em 0 0 3.75em;
}

.row.uniform.\31 50\25 {
    margin: -3.75em 0 -1px -3.75em;
}

.row.\35 0\25>* {
    padding: 0 0 0 1.25em;
}

.row.\35 0\25 {
    margin: 0 0 -1px -1.25em;
}

.row.uniform.\35 0\25>* {
    padding: 1.25em 0 0 1.25em;
}

.row.uniform.\35 0\25 {
    margin: -1.25em 0 -1px -1.25em;
}

.row.\32 5\25>* {
    padding: 0 0 0 0.625em;
}

.row.\32 5\25 {
    margin: 0 0 -1px -0.625em;
}

.row.uniform.\32 5\25>* {
    padding: 0.625em 0 0 0.625em;
}

.row.uniform.\32 5\25 {
    margin: -0.625em 0 -1px -0.625em;
}

.\31 2u,
.\31 2u\24 {
    width: 100%;
    clear: none;
    margin-left: 0;
}

.\31 1u,
.\31 1u\24 {
    width: 91.6666666667%;
    clear: none;
    margin-left: 0;
}

.\31 0u,
.\31 0u\24 {
    width: 83.3333333333%;
    clear: none;
    margin-left: 0;
}

.\39 u,
.\39 u\24 {
    width: 75%;
    clear: none;
    margin-left: 0;
}

.\38 u,
.\38 u\24 {
    width: 66.6666666667%;
    clear: none;
    margin-left: 0;
}

.\37 u,
.\37 u\24 {
    width: 58.3333333333%;
    clear: none;
    margin-left: 0;
}

.\36 u,
.\36 u\24 {
    width: 50%;
    clear: none;
    margin-left: 0;
}

.\35 u,
.\35 u\24 {
    width: 41.6666666667%;
    clear: none;
    margin-left: 0;
}

.\34 u,
.\34 u\24 {
    width: 33.3333333333%;
    clear: none;
    margin-left: 0;
}

.\33 u,
.\33 u\24 {
    width: 25%;
    clear: none;
    margin-left: 0;
}

.\32 u,
.\32 u\24 {
    width: 16.6666666667%;
    clear: none;
    margin-left: 0;
}

.\31 u,
.\31 u\24 {
    width: 8.3333333333%;
    clear: none;
    margin-left: 0;
}

.\31 2u\24+*,
.\31 1u\24+*,
.\31 0u\24+*,
.\39 u\24+*,
.\38 u\24+*,
.\37 u\24+*,
.\36 u\24+*,
.\35 u\24+*,
.\34 u\24+*,
.\33 u\24+*,
.\32 u\24+*,
.\31 u\24+* {
    clear: left;
}

.\-11u {
    margin-left: 91.66667%;
}

.\-10u {
    margin-left: 83.33333%;
}

.\-9u {
    margin-left: 75%;
}

.\-8u {
    margin-left: 66.66667%;
}

.\-7u {
    margin-left: 58.33333%;
}

.\-6u {
    margin-left: 50%;
}

.\-5u {
    margin-left: 41.66667%;
}

.\-4u {
    margin-left: 33.33333%;
}

.\-3u {
    margin-left: 25%;
}

.\-2u {
    margin-left: 16.66667%;
}

.\-1u {
    margin-left: 8.33333%;
}

@media screen and (max-width: 1680px) {

    .row>* {
        padding: 0 0 0 2.5em;
    }

    .row {
        margin: 0 0 -1px -2.5em;
    }

    .row.uniform>* {
        padding: 2.5em 0 0 2.5em;
    }

    .row.uniform {
        margin: -2.5em 0 -1px -2.5em;
    }

    .row.\32 00\25>* {
        padding: 0 0 0 5em;
    }

    .row.\32 00\25 {
        margin: 0 0 -1px -5em;
    }

    .row.uniform.\32 00\25>* {
        padding: 5em 0 0 5em;
    }

    .row.uniform.\32 00\25 {
        margin: -5em 0 -1px -5em;
    }

    .row.\31 50\25>* {
        padding: 0 0 0 3.75em;
    }

    .row.\31 50\25 {
        margin: 0 0 -1px -3.75em;
    }

    .row.uniform.\31 50\25>* {
        padding: 3.75em 0 0 3.75em;
    }

    .row.uniform.\31 50\25 {
        margin: -3.75em 0 -1px -3.75em;
    }

    .row.\35 0\25>* {
        padding: 0 0 0 1.25em;
    }

    .row.\35 0\25 {
        margin: 0 0 -1px -1.25em;
    }

    .row.uniform.\35 0\25>* {
        padding: 1.25em 0 0 1.25em;
    }

    .row.uniform.\35 0\25 {
        margin: -1.25em 0 -1px -1.25em;
    }

    .row.\32 5\25>* {
        padding: 0 0 0 0.625em;
    }

    .row.\32 5\25 {
        margin: 0 0 -1px -0.625em;
    }

    .row.uniform.\32 5\25>* {
        padding: 0.625em 0 0 0.625em;
    }

    .row.uniform.\32 5\25 {
        margin: -0.625em 0 -1px -0.625em;
    }

    .\31 2u\28xlarge\29,
    .\31 2u\24\28xlarge\29 {
        width: 100%;
        clear: none;
        margin-left: 0;
    }

    .\31 1u\28xlarge\29,
    .\31 1u\24\28xlarge\29 {
        width: 91.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 0u\28xlarge\29,
    .\31 0u\24\28xlarge\29 {
        width: 83.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\39 u\28xlarge\29,
    .\39 u\24\28xlarge\29 {
        width: 75%;
        clear: none;
        margin-left: 0;
    }

    .\38 u\28xlarge\29,
    .\38 u\24\28xlarge\29 {
        width: 66.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\37 u\28xlarge\29,
    .\37 u\24\28xlarge\29 {
        width: 58.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\36 u\28xlarge\29,
    .\36 u\24\28xlarge\29 {
        width: 50%;
        clear: none;
        margin-left: 0;
    }

    .\35 u\28xlarge\29,
    .\35 u\24\28xlarge\29 {
        width: 41.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\34 u\28xlarge\29,
    .\34 u\24\28xlarge\29 {
        width: 33.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\33 u\28xlarge\29,
    .\33 u\24\28xlarge\29 {
        width: 25%;
        clear: none;
        margin-left: 0;
    }

    .\32 u\28xlarge\29,
    .\32 u\24\28xlarge\29 {
        width: 16.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 u\28xlarge\29,
    .\31 u\24\28xlarge\29 {
        width: 8.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\31 2u\24\28xlarge\29+*,
    .\31 1u\24\28xlarge\29+*,
    .\31 0u\24\28xlarge\29+*,
    .\39 u\24\28xlarge\29+*,
    .\38 u\24\28xlarge\29+*,
    .\37 u\24\28xlarge\29+*,
    .\36 u\24\28xlarge\29+*,
    .\35 u\24\28xlarge\29+*,
    .\34 u\24\28xlarge\29+*,
    .\33 u\24\28xlarge\29+*,
    .\32 u\24\28xlarge\29+*,
    .\31 u\24\28xlarge\29+* {
        clear: left;
    }

    .\-11u\28xlarge\29 {
        margin-left: 91.66667%;
    }

    .\-10u\28xlarge\29 {
        margin-left: 83.33333%;
    }

    .\-9u\28xlarge\29 {
        margin-left: 75%;
    }

    .\-8u\28xlarge\29 {
        margin-left: 66.66667%;
    }

    .\-7u\28xlarge\29 {
        margin-left: 58.33333%;
    }

    .\-6u\28xlarge\29 {
        margin-left: 50%;
    }

    .\-5u\28xlarge\29 {
        margin-left: 41.66667%;
    }

    .\-4u\28xlarge\29 {
        margin-left: 33.33333%;
    }

    .\-3u\28xlarge\29 {
        margin-left: 25%;
    }

    .\-2u\28xlarge\29 {
        margin-left: 16.66667%;
    }

    .\-1u\28xlarge\29 {
        margin-left: 8.33333%;
    }

}

@media screen and (max-width: 1280px) {

    .row>* {
        padding: 0 0 0 2.5em;
    }

    .row {
        margin: 0 0 -1px -2.5em;
    }

    .row.uniform>* {
        padding: 2.5em 0 0 2.5em;
    }

    .row.uniform {
        margin: -2.5em 0 -1px -2.5em;
    }

    .row.\32 00\25>* {
        padding: 0 0 0 5em;
    }

    .row.\32 00\25 {
        margin: 0 0 -1px -5em;
    }

    .row.uniform.\32 00\25>* {
        padding: 5em 0 0 5em;
    }

    .row.uniform.\32 00\25 {
        margin: -5em 0 -1px -5em;
    }

    .row.\31 50\25>* {
        padding: 0 0 0 3.75em;
    }

    .row.\31 50\25 {
        margin: 0 0 -1px -3.75em;
    }

    .row.uniform.\31 50\25>* {
        padding: 3.75em 0 0 3.75em;
    }

    .row.uniform.\31 50\25 {
        margin: -3.75em 0 -1px -3.75em;
    }

    .row.\35 0\25>* {
        padding: 0 0 0 1.25em;
    }

    .row.\35 0\25 {
        margin: 0 0 -1px -1.25em;
    }

    .row.uniform.\35 0\25>* {
        padding: 1.25em 0 0 1.25em;
    }

    .row.uniform.\35 0\25 {
        margin: -1.25em 0 -1px -1.25em;
    }

    .row.\32 5\25>* {
        padding: 0 0 0 0.625em;
    }

    .row.\32 5\25 {
        margin: 0 0 -1px -0.625em;
    }

    .row.uniform.\32 5\25>* {
        padding: 0.625em 0 0 0.625em;
    }

    .row.uniform.\32 5\25 {
        margin: -0.625em 0 -1px -0.625em;
    }

    .\31 2u\28large\29,
    .\31 2u\24\28large\29 {
        width: 100%;
        clear: none;
        margin-left: 0;
    }

    .\31 1u\28large\29,
    .\31 1u\24\28large\29 {
        width: 91.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 0u\28large\29,
    .\31 0u\24\28large\29 {
        width: 83.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\39 u\28large\29,
    .\39 u\24\28large\29 {
        width: 75%;
        clear: none;
        margin-left: 0;
    }

    .\38 u\28large\29,
    .\38 u\24\28large\29 {
        width: 66.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\37 u\28large\29,
    .\37 u\24\28large\29 {
        width: 58.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\36 u\28large\29,
    .\36 u\24\28large\29 {
        width: 50%;
        clear: none;
        margin-left: 0;
    }

    .\35 u\28large\29,
    .\35 u\24\28large\29 {
        width: 41.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\34 u\28large\29,
    .\34 u\24\28large\29 {
        width: 33.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\33 u\28large\29,
    .\33 u\24\28large\29 {
        width: 25%;
        clear: none;
        margin-left: 0;
    }

    .\32 u\28large\29,
    .\32 u\24\28large\29 {
        width: 16.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 u\28large\29,
    .\31 u\24\28large\29 {
        width: 8.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\31 2u\24\28large\29+*,
    .\31 1u\24\28large\29+*,
    .\31 0u\24\28large\29+*,
    .\39 u\24\28large\29+*,
    .\38 u\24\28large\29+*,
    .\37 u\24\28large\29+*,
    .\36 u\24\28large\29+*,
    .\35 u\24\28large\29+*,
    .\34 u\24\28large\29+*,
    .\33 u\24\28large\29+*,
    .\32 u\24\28large\29+*,
    .\31 u\24\28large\29+* {
        clear: left;
    }

    .\-11u\28large\29 {
        margin-left: 91.66667%;
    }

    .\-10u\28large\29 {
        margin-left: 83.33333%;
    }

    .\-9u\28large\29 {
        margin-left: 75%;
    }

    .\-8u\28large\29 {
        margin-left: 66.66667%;
    }

    .\-7u\28large\29 {
        margin-left: 58.33333%;
    }

    .\-6u\28large\29 {
        margin-left: 50%;
    }

    .\-5u\28large\29 {
        margin-left: 41.66667%;
    }

    .\-4u\28large\29 {
        margin-left: 33.33333%;
    }

    .\-3u\28large\29 {
        margin-left: 25%;
    }

    .\-2u\28large\29 {
        margin-left: 16.66667%;
    }

    .\-1u\28large\29 {
        margin-left: 8.33333%;
    }

}

@media screen and (max-width: 980px) {

    .row>* {
        padding: 0 0 0 2.5em;
    }

    .row {
        margin: 0 0 -1px -2.5em;
    }

    .row.uniform>* {
        padding: 2.5em 0 0 2.5em;
    }

    .row.uniform {
        margin: -2.5em 0 -1px -2.5em;
    }

    .row.\32 00\25>* {
        padding: 0 0 0 5em;
    }

    .row.\32 00\25 {
        margin: 0 0 -1px -5em;
    }

    .row.uniform.\32 00\25>* {
        padding: 5em 0 0 5em;
    }

    .row.uniform.\32 00\25 {
        margin: -5em 0 -1px -5em;
    }

    .row.\31 50\25>* {
        padding: 0 0 0 3.75em;
    }

    .row.\31 50\25 {
        margin: 0 0 -1px -3.75em;
    }

    .row.uniform.\31 50\25>* {
        padding: 3.75em 0 0 3.75em;
    }

    .row.uniform.\31 50\25 {
        margin: -3.75em 0 -1px -3.75em;
    }

    .row.\35 0\25>* {
        padding: 0 0 0 1.25em;
    }

    .row.\35 0\25 {
        margin: 0 0 -1px -1.25em;
    }

    .row.uniform.\35 0\25>* {
        padding: 1.25em 0 0 1.25em;
    }

    .row.uniform.\35 0\25 {
        margin: -1.25em 0 -1px -1.25em;
    }

    .row.\32 5\25>* {
        padding: 0 0 0 0.625em;
    }

    .row.\32 5\25 {
        margin: 0 0 -1px -0.625em;
    }

    .row.uniform.\32 5\25>* {
        padding: 0.625em 0 0 0.625em;
    }

    .row.uniform.\32 5\25 {
        margin: -0.625em 0 -1px -0.625em;
    }

    .\31 2u\28medium\29,
    .\31 2u\24\28medium\29 {
        width: 100%;
        clear: none;
        margin-left: 0;
    }

    .\31 1u\28medium\29,
    .\31 1u\24\28medium\29 {
        width: 91.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 0u\28medium\29,
    .\31 0u\24\28medium\29 {
        width: 83.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\39 u\28medium\29,
    .\39 u\24\28medium\29 {
        width: 75%;
        clear: none;
        margin-left: 0;
    }

    .\38 u\28medium\29,
    .\38 u\24\28medium\29 {
        width: 66.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\37 u\28medium\29,
    .\37 u\24\28medium\29 {
        width: 58.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\36 u\28medium\29,
    .\36 u\24\28medium\29 {
        width: 50%;
        clear: none;
        margin-left: 0;
    }

    .\35 u\28medium\29,
    .\35 u\24\28medium\29 {
        width: 41.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\34 u\28medium\29,
    .\34 u\24\28medium\29 {
        width: 33.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\33 u\28medium\29,
    .\33 u\24\28medium\29 {
        width: 25%;
        clear: none;
        margin-left: 0;
    }

    .\32 u\28medium\29,
    .\32 u\24\28medium\29 {
        width: 16.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 u\28medium\29,
    .\31 u\24\28medium\29 {
        width: 8.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\31 2u\24\28medium\29+*,
    .\31 1u\24\28medium\29+*,
    .\31 0u\24\28medium\29+*,
    .\39 u\24\28medium\29+*,
    .\38 u\24\28medium\29+*,
    .\37 u\24\28medium\29+*,
    .\36 u\24\28medium\29+*,
    .\35 u\24\28medium\29+*,
    .\34 u\24\28medium\29+*,
    .\33 u\24\28medium\29+*,
    .\32 u\24\28medium\29+*,
    .\31 u\24\28medium\29+* {
        clear: left;
    }

    .\-11u\28medium\29 {
        margin-left: 91.66667%;
    }

    .\-10u\28medium\29 {
        margin-left: 83.33333%;
    }

    .\-9u\28medium\29 {
        margin-left: 75%;
    }

    .\-8u\28medium\29 {
        margin-left: 66.66667%;
    }

    .\-7u\28medium\29 {
        margin-left: 58.33333%;
    }

    .\-6u\28medium\29 {
        margin-left: 50%;
    }

    .\-5u\28medium\29 {
        margin-left: 41.66667%;
    }

    .\-4u\28medium\29 {
        margin-left: 33.33333%;
    }

    .\-3u\28medium\29 {
        margin-left: 25%;
    }

    .\-2u\28medium\29 {
        margin-left: 16.66667%;
    }

    .\-1u\28medium\29 {
        margin-left: 8.33333%;
    }

}

@media screen and (max-width: 736px) {

    .row>* {
        padding: 0 0 0 2.5em;
    }

    .row {
        margin: 0 0 -1px -2.5em;
    }

    .row.uniform>* {
        padding: 2.5em 0 0 2.5em;
    }

    .row.uniform {
        margin: -2.5em 0 -1px -2.5em;
    }

    .row.\32 00\25>* {
        padding: 0 0 0 5em;
    }

    .row.\32 00\25 {
        margin: 0 0 -1px -5em;
    }

    .row.uniform.\32 00\25>* {
        padding: 5em 0 0 5em;
    }

    .row.uniform.\32 00\25 {
        margin: -5em 0 -1px -5em;
    }

    .row.\31 50\25>* {
        padding: 0 0 0 3.75em;
    }

    .row.\31 50\25 {
        margin: 0 0 -1px -3.75em;
    }

    .row.uniform.\31 50\25>* {
        padding: 3.75em 0 0 3.75em;
    }

    .row.uniform.\31 50\25 {
        margin: -3.75em 0 -1px -3.75em;
    }

    .row.\35 0\25>* {
        padding: 0 0 0 1.25em;
    }

    .row.\35 0\25 {
        margin: 0 0 -1px -1.25em;
    }

    .row.uniform.\35 0\25>* {
        padding: 1.25em 0 0 1.25em;
    }

    .row.uniform.\35 0\25 {
        margin: -1.25em 0 -1px -1.25em;
    }

    .row.\32 5\25>* {
        padding: 0 0 0 0.625em;
    }

    .row.\32 5\25 {
        margin: 0 0 -1px -0.625em;
    }

    .row.uniform.\32 5\25>* {
        padding: 0.625em 0 0 0.625em;
    }

    .row.uniform.\32 5\25 {
        margin: -0.625em 0 -1px -0.625em;
    }

    .\31 2u\28small\29,
    .\31 2u\24\28small\29 {
        width: 100%;
        clear: none;
        margin-left: 0;
    }

    .\31 1u\28small\29,
    .\31 1u\24\28small\29 {
        width: 91.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 0u\28small\29,
    .\31 0u\24\28small\29 {
        width: 83.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\39 u\28small\29,
    .\39 u\24\28small\29 {
        width: 75%;
        clear: none;
        margin-left: 0;
    }

    .\38 u\28small\29,
    .\38 u\24\28small\29 {
        width: 66.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\37 u\28small\29,
    .\37 u\24\28small\29 {
        width: 58.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\36 u\28small\29,
    .\36 u\24\28small\29 {
        width: 50%;
        clear: none;
        margin-left: 0;
    }

    .\35 u\28small\29,
    .\35 u\24\28small\29 {
        width: 41.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\34 u\28small\29,
    .\34 u\24\28small\29 {
        width: 33.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\33 u\28small\29,
    .\33 u\24\28small\29 {
        width: 25%;
        clear: none;
        margin-left: 0;
    }

    .\32 u\28small\29,
    .\32 u\24\28small\29 {
        width: 16.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 u\28small\29,
    .\31 u\24\28small\29 {
        width: 8.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\31 2u\24\28small\29+*,
    .\31 1u\24\28small\29+*,
    .\31 0u\24\28small\29+*,
    .\39 u\24\28small\29+*,
    .\38 u\24\28small\29+*,
    .\37 u\24\28small\29+*,
    .\36 u\24\28small\29+*,
    .\35 u\24\28small\29+*,
    .\34 u\24\28small\29+*,
    .\33 u\24\28small\29+*,
    .\32 u\24\28small\29+*,
    .\31 u\24\28small\29+* {
        clear: left;
    }

    .\-11u\28small\29 {
        margin-left: 91.66667%;
    }

    .\-10u\28small\29 {
        margin-left: 83.33333%;
    }

    .\-9u\28small\29 {
        margin-left: 75%;
    }

    .\-8u\28small\29 {
        margin-left: 66.66667%;
    }

    .\-7u\28small\29 {
        margin-left: 58.33333%;
    }

    .\-6u\28small\29 {
        margin-left: 50%;
    }

    .\-5u\28small\29 {
        margin-left: 41.66667%;
    }

    .\-4u\28small\29 {
        margin-left: 33.33333%;
    }

    .\-3u\28small\29 {
        margin-left: 25%;
    }

    .\-2u\28small\29 {
        margin-left: 16.66667%;
    }

    .\-1u\28small\29 {
        margin-left: 8.33333%;
    }

}

@media screen and (max-width: 480px) {

    .row>* {
        padding: 0 0 0 2.5em;
    }

    .row {
        margin: 0 0 -1px -2.5em;
    }

    .row.uniform>* {
        padding: 2.5em 0 0 2.5em;
    }

    .row.uniform {
        margin: -2.5em 0 -1px -2.5em;
    }

    .row.\32 00\25>* {
        padding: 0 0 0 5em;
    }

    .row.\32 00\25 {
        margin: 0 0 -1px -5em;
    }

    .row.uniform.\32 00\25>* {
        padding: 5em 0 0 5em;
    }

    .row.uniform.\32 00\25 {
        margin: -5em 0 -1px -5em;
    }

    .row.\31 50\25>* {
        padding: 0 0 0 3.75em;
    }

    .row.\31 50\25 {
        margin: 0 0 -1px -3.75em;
    }

    .row.uniform.\31 50\25>* {
        padding: 3.75em 0 0 3.75em;
    }

    .row.uniform.\31 50\25 {
        margin: -3.75em 0 -1px -3.75em;
    }

    .row.\35 0\25>* {
        padding: 0 0 0 1.25em;
    }

    .row.\35 0\25 {
        margin: 0 0 -1px -1.25em;
    }

    .row.uniform.\35 0\25>* {
        padding: 1.25em 0 0 1.25em;
    }

    .row.uniform.\35 0\25 {
        margin: -1.25em 0 -1px -1.25em;
    }

    .row.\32 5\25>* {
        padding: 0 0 0 0.625em;
    }

    .row.\32 5\25 {
        margin: 0 0 -1px -0.625em;
    }

    .row.uniform.\32 5\25>* {
        padding: 0.625em 0 0 0.625em;
    }

    .row.uniform.\32 5\25 {
        margin: -0.625em 0 -1px -0.625em;
    }

    .\31 2u\28xsmall\29,
    .\31 2u\24\28xsmall\29 {
        width: 100%;
        clear: none;
        margin-left: 0;
    }

    .\31 1u\28xsmall\29,
    .\31 1u\24\28xsmall\29 {
        width: 91.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 0u\28xsmall\29,
    .\31 0u\24\28xsmall\29 {
        width: 83.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\39 u\28xsmall\29,
    .\39 u\24\28xsmall\29 {
        width: 75%;
        clear: none;
        margin-left: 0;
    }

    .\38 u\28xsmall\29,
    .\38 u\24\28xsmall\29 {
        width: 66.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\37 u\28xsmall\29,
    .\37 u\24\28xsmall\29 {
        width: 58.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\36 u\28xsmall\29,
    .\36 u\24\28xsmall\29 {
        width: 50%;
        clear: none;
        margin-left: 0;
    }

    .\35 u\28xsmall\29,
    .\35 u\24\28xsmall\29 {
        width: 41.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\34 u\28xsmall\29,
    .\34 u\24\28xsmall\29 {
        width: 33.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\33 u\28xsmall\29,
    .\33 u\24\28xsmall\29 {
        width: 25%;
        clear: none;
        margin-left: 0;
    }

    .\32 u\28xsmall\29,
    .\32 u\24\28xsmall\29 {
        width: 16.6666666667%;
        clear: none;
        margin-left: 0;
    }

    .\31 u\28xsmall\29,
    .\31 u\24\28xsmall\29 {
        width: 8.3333333333%;
        clear: none;
        margin-left: 0;
    }

    .\31 2u\24\28xsmall\29+*,
    .\31 1u\24\28xsmall\29+*,
    .\31 0u\24\28xsmall\29+*,
    .\39 u\24\28xsmall\29+*,
    .\38 u\24\28xsmall\29+*,
    .\37 u\24\28xsmall\29+*,
    .\36 u\24\28xsmall\29+*,
    .\35 u\24\28xsmall\29+*,
    .\34 u\24\28xsmall\29+*,
    .\33 u\24\28xsmall\29+*,
    .\32 u\24\28xsmall\29+*,
    .\31 u\24\28xsmall\29+* {
        clear: left;
    }

    .\-11u\28xsmall\29 {
        margin-left: 91.66667%;
    }

    .\-10u\28xsmall\29 {
        margin-left: 83.33333%;
    }

    .\-9u\28xsmall\29 {
        margin-left: 75%;
    }

    .\-8u\28xsmall\29 {
        margin-left: 66.66667%;
    }

    .\-7u\28xsmall\29 {
        margin-left: 58.33333%;
    }

    .\-6u\28xsmall\29 {
        margin-left: 50%;
    }

    .\-5u\28xsmall\29 {
        margin-left: 41.66667%;
    }

    .\-4u\28xsmall\29 {
        margin-left: 33.33333%;
    }

    .\-3u\28xsmall\29 {
        margin-left: 25%;
    }

    .\-2u\28xsmall\29 {
        margin-left: 16.66667%;
    }

    .\-1u\28xsmall\29 {
        margin-left: 8.33333%;
    }

}

/* Basic */

html,
body {
    background: #1c1d26;
}

body.is-loading *,
body.is-loading *:before,
body.is-loading *:after {
    -moz-animation: none !important;
    -webkit-animation: none !important;
    -ms-animation: none !important;
    animation: none !important;
    -moz-transition: none !important;
    -webkit-transition: none !important;
    -ms-transition: none !important;
    transition: none !important;
}

body,
input,
select,
textarea {
    color: rgba(255, 255, 255, 0.75);
    font-family: "Roboto", Helvetica, sans-serif;
    font-size: 15pt;
    font-weight: 100;
    line-height: 1.75em;
}

a {
    -moz-transition: border-color 0.2s ease-in-out, color 0.2s ease-in-out;
    -webkit-transition: border-color 0.2s ease-in-out, color 0.2s ease-in-out;
    -ms-transition: border-color 0.2s ease-in-out, color 0.2s ease-in-out;
    transition: border-color 0.2s ease-in-out, color 0.2s ease-in-out;
    border-bottom: dotted 1px;
    color: #e44c65;
    text-decoration: none;
}

a:hover {
    color: #e44c65 !important;
    border-bottom-color: transparent;
}

strong,
b {
    color: #ffffff;
    font-weight: 300;
}

em,
i {
    font-style: italic;
}

p {
    margin: 0 0 2em 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #ffffff;
    font-weight: 300;
    line-height: 1em;
    margin: 0 0 1em 0;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
    color: inherit;
    border: 0;
}

h2 {
    font-size: 2em;
    line-height: 1.5em;
    /*letter-spacing: -0.025em;*/
}

h3 {
    font-size: 1.35em;
    line-height: 1.5em;
}

h4 {
    font-size: 1.1em;
    line-height: 1.5em;
}

h5 {
    font-size: 0.9em;
    line-height: 1.5em;
}

h6 {
    font-size: 0.7em;
    line-height: 1.5em;
}

sub {
    font-size: 0.8em;
    position: relative;
    top: 0.5em;
}

sup {
    font-size: 0.8em;
    position: relative;
    top: -0.5em;
}

hr {
    border: 0;
    border-bottom: solid 1px rgba(255, 255, 255, 0.3);
    margin: 3em 0;
}

hr.major {
    margin: 4em 0;
}

blockquote {
    border-left: solid 4px rgba(255, 255, 255, 0.3);
    /*font-style: italic;*/
    margin: 0 0 2em 0;
    padding: 0.5em 0 0.5em 2em;
}

code {
    background: rgba(255, 255, 255, 0.075);
    border-radius: 4px;
    font-family: "Courier New", monospace;
    font-size: 0.9em;
    margin: 0 0.25em;
    padding: 0.25em 0.65em;
}

pre {
    -webkit-overflow-scrolling: touch;
    font-family: "Courier New", monospace;
    font-size: 0.9em;
    margin: 0 0 2em 0;
}

pre code {
    display: block;
    line-height: 1.75em;
    padding: 1em 1.5em;
    overflow-x: auto;
}

.align-left {
    text-align: left;
}

.align-center {
    text-align: center;
}

.align-right {
    text-align: right;
}

/* Loader */

@-moz-keyframes spinner-show {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@-webkit-keyframes spinner-show {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@-ms-keyframes spinner-show {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes spinner-show {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@-moz-keyframes spinner-hide {
    0% {
        color: rgba(255, 255, 255, 0.15);
        z-index: 100001;
        -moz-transform: scale(1) rotate(0deg);
        -webkit-transform: scale(1) rotate(0deg);
        -ms-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    99% {
        color: #1c1d26;
        z-index: 100001;
        -moz-transform: scale(0.5) rotate(360deg);
        -webkit-transform: scale(0.5) rotate(360deg);
        -ms-transform: scale(0.5) rotate(360deg);
        transform: scale(0.5) rotate(360deg);
    }

    100% {
        color: #1c1d26;
        z-index: -1;
        -moz-transform: scale(0.5) rotate(360deg);
        -webkit-transform: scale(0.5) rotate(360deg);
        -ms-transform: scale(0.5) rotate(360deg);
        transform: scale(0.5) rotate(360deg);
    }
}

@-webkit-keyframes spinner-hide {
    0% {
        color: rgba(255, 255, 255, 0.15);
        z-index: 100001;
        -moz-transform: scale(1) rotate(0deg);
        -webkit-transform: scale(1) rotate(0deg);
        -ms-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    99% {
        color: #1c1d26;
        z-index: 100001;
        -moz-transform: scale(0.5) rotate(360deg);
        -webkit-transform: scale(0.5) rotate(360deg);
        -ms-transform: scale(0.5) rotate(360deg);
        transform: scale(0.5) rotate(360deg);
    }

    100% {
        color: #1c1d26;
        z-index: -1;
        -moz-transform: scale(0.5) rotate(360deg);
        -webkit-transform: scale(0.5) rotate(360deg);
        -ms-transform: scale(0.5) rotate(360deg);
        transform: scale(0.5) rotate(360deg);
    }
}

@-ms-keyframes spinner-hide {
    0% {
        color: rgba(255, 255, 255, 0.15);
        z-index: 100001;
        -moz-transform: scale(1) rotate(0deg);
        -webkit-transform: scale(1) rotate(0deg);
        -ms-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    99% {
        color: #1c1d26;
        z-index: 100001;
        -moz-transform: scale(0.5) rotate(360deg);
        -webkit-transform: scale(0.5) rotate(360deg);
        -ms-transform: scale(0.5) rotate(360deg);
        transform: scale(0.5) rotate(360deg);
    }

    100% {
        color: #1c1d26;
        z-index: -1;
        -moz-transform: scale(0.5) rotate(360deg);
        -webkit-transform: scale(0.5) rotate(360deg);
        -ms-transform: scale(0.5) rotate(360deg);
        transform: scale(0.5) rotate(360deg);
    }
}

@keyframes spinner-hide {
    0% {
        color: rgba(255, 255, 255, 0.15);
        z-index: 100001;
        -moz-transform: scale(1) rotate(0deg);
        -webkit-transform: scale(1) rotate(0deg);
        -ms-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    99% {
        color: #1c1d26;
        z-index: 100001;
        -moz-transform: scale(0.5) rotate(360deg);
        -webkit-transform: scale(0.5) rotate(360deg);
        -ms-transform: scale(0.5) rotate(360deg);
        transform: scale(0.5) rotate(360deg);
    }

    100% {
        color: #1c1d26;
        z-index: -1;
        -moz-transform: scale(0.5) rotate(360deg);
        -webkit-transform: scale(0.5) rotate(360deg);
        -ms-transform: scale(0.5) rotate(360deg);
        transform: scale(0.5) rotate(360deg);
    }
}

@-moz-keyframes spinner-rotate {
    0% {
        -moz-transform: scale(1) rotate(0deg);
        -webkit-transform: scale(1) rotate(0deg);
        -ms-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    100% {
        -moz-transform: scale(1) rotate(360deg);
        -webkit-transform: scale(1) rotate(360deg);
        -ms-transform: scale(1) rotate(360deg);
        transform: scale(1) rotate(360deg);
    }
}

@-webkit-keyframes spinner-rotate {
    0% {
        -moz-transform: scale(1) rotate(0deg);
        -webkit-transform: scale(1) rotate(0deg);
        -ms-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    100% {
        -moz-transform: scale(1) rotate(360deg);
        -webkit-transform: scale(1) rotate(360deg);
        -ms-transform: scale(1) rotate(360deg);
        transform: scale(1) rotate(360deg);
    }
}

@-ms-keyframes spinner-rotate {
    0% {
        -moz-transform: scale(1) rotate(0deg);
        -webkit-transform: scale(1) rotate(0deg);
        -ms-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    100% {
        -moz-transform: scale(1) rotate(360deg);
        -webkit-transform: scale(1) rotate(360deg);
        -ms-transform: scale(1) rotate(360deg);
        transform: scale(1) rotate(360deg);
    }
}

@keyframes spinner-rotate {
    0% {
        -moz-transform: scale(1) rotate(0deg);
        -webkit-transform: scale(1) rotate(0deg);
        -ms-transform: scale(1) rotate(0deg);
        transform: scale(1) rotate(0deg);
    }

    100% {
        -moz-transform: scale(1) rotate(360deg);
        -webkit-transform: scale(1) rotate(360deg);
        -ms-transform: scale(1) rotate(360deg);
        transform: scale(1) rotate(360deg);
    }
}

@-moz-keyframes overlay-hide {
    0% {
        opacity: 1;
        z-index: 100000;
    }

    15% {
        opacity: 1;
        z-index: 100000;
    }

    99% {
        opacity: 0;
        z-index: 100000;
    }

    100% {
        opacity: 0;
        z-index: -1;
    }
}

@-webkit-keyframes overlay-hide {
    0% {
        opacity: 1;
        z-index: 100000;
    }

    15% {
        opacity: 1;
        z-index: 100000;
    }

    99% {
        opacity: 0;
        z-index: 100000;
    }

    100% {
        opacity: 0;
        z-index: -1;
    }
}

@-ms-keyframes overlay-hide {
    0% {
        opacity: 1;
        z-index: 100000;
    }

    15% {
        opacity: 1;
        z-index: 100000;
    }

    99% {
        opacity: 0;
        z-index: 100000;
    }

    100% {
        opacity: 0;
        z-index: -1;
    }
}

@keyframes overlay-hide {
    0% {
        opacity: 1;
        z-index: 100000;
    }

    15% {
        opacity: 1;
        z-index: 100000;
    }

    99% {
        opacity: 0;
        z-index: 100000;
    }

    100% {
        opacity: 0;
        z-index: -1;
    }
}

body.landing {
    text-decoration: none;
}

body.landing:before {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-transform: none !important;
}

body.landing:before {
    -moz-animation: spinner-show 1.5s 1 0.25s ease forwards, spinner-hide 0.25s ease-in-out forwards !important;
    -webkit-animation: spinner-show 1.5s 1 0.25s ease forwards, spinner-hide 0.25s ease-in-out forwards !important;
    -ms-animation: spinner-show 1.5s 1 0.25s ease forwards, spinner-hide 0.25s ease-in-out forwards !important;
    animation: spinner-show 1.5s 1 0.25s ease forwards, spinner-hide 0.25s ease-in-out forwards !important;
    -moz-transform-origin: 50% 50%;
    -webkit-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    color: rgba(255, 255, 255, 0.15);
    content: '\f1ce';
    cursor: default;
    display: block;
    font-size: 2em;
    height: 2em;
    left: 50%;
    line-height: 2em;
    margin: -1em 0 0 -1em;
    opacity: 0;
    position: fixed;
    text-align: center;
    top: 50%;
    width: 2em;
    z-index: -1;
}

body.landing:after {
    -moz-animation: overlay-hide 1.5s ease-in forwards !important;
    -webkit-animation: overlay-hide 1.5s ease-in forwards !important;
    -ms-animation: overlay-hide 1.5s ease-in forwards !important;
    animation: overlay-hide 1.5s ease-in forwards !important;
    background: #1c1d26;
    content: '';
    display: block;
    height: 100%;
    left: 0;
    opacity: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: -1;
}

body.landing.is-loading:before {
    -moz-animation: spinner-show 1.5s 1 0.25s ease forwards, spinner-rotate 0.75s infinite linear !important;
    -webkit-animation: spinner-show 1.5s 1 0.25s ease forwards, spinner-rotate 0.75s infinite linear !important;
    -ms-animation: spinner-show 1.5s 1 0.25s ease forwards, spinner-rotate 0.75s infinite linear !important;
    animation: spinner-show 1.5s 1 0.25s ease forwards, spinner-rotate 0.75s infinite linear !important;
    z-index: 100001;
}

body.landing.is-loading:after {
    -moz-animation: none !important;
    -webkit-animation: none !important;
    -ms-animation: none !important;
    animation: none !important;
    opacity: 1;
    z-index: 100000;
}

@media (-webkit-min-device-pixel-ratio: 2) {

    body.landing:before {
        line-height: 2.025em;
    }

}

/* Section/Article */

/* section.special,
article.special {
    text-align: center;
} */

header p {
    color: #ffffff;
    position: relative;
    margin: 0 0 1.5em 0;
}

header h2+p {
    font-size: 1.25em;
    margin-top: -1em;
    line-height: 1.75em;
}

header h3+p {
    font-size: 1.1em;
    margin-top: -0.8em;
    line-height: 1.75em;
}

header h4+p,
header h5+p,
header h6+p {
    font-size: 0.9em;
    margin-top: -0.6em;
    line-height: 1.5em;
}

header.major {
    margin: 0 0 4em 0;
    position: relative;
    text-align: center;
}

header.major:after {
    background: #e44c65;
    content: '';
    display: inline-block;
    height: 0.2em;
    max-width: 20em;
    width: 75%;
}

footer.major {
    margin: 4em 0 0 0;
}

/* Form */

form {
    margin: 0 0 2em 0;
}

label {
    color: #ffffff;
    display: block;
    font-size: 0.9em;
    font-weight: 300;
    margin: 0 0 1em 0;
}

input[type="text"],
input[type="password"],
input[type="email"],
select,
textarea {
    -moz-appearance: none;
    -webkit-appearance: none;
    -ms-appearance: none;
    appearance: none;
    -moz-transition: border-color 0.2s ease-in-out;
    -webkit-transition: border-color 0.2s ease-in-out;
    -ms-transition: border-color 0.2s ease-in-out;
    transition: border-color 0.2s ease-in-out;
    background: transparent;
    border-radius: 4px;
    border: solid 1px rgba(255, 255, 255, 0.3);
    color: inherit;
    display: block;
    outline: 0;
    padding: 0 1em;
    text-decoration: none;
    width: 100%;
}

input[type="text"]:invalid,
input[type="password"]:invalid,
input[type="email"]:invalid,
select:invalid,
textarea:invalid {
    box-shadow: none;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
    border-color: #e44c65;
}

.select-wrapper {
    text-decoration: none;
    display: block;
    position: relative;
}

.select-wrapper:before {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-transform: none !important;
}

.select-wrapper:before {
    color: rgba(255, 255, 255, 0.3);
    content: '\f078';
    display: block;
    height: 3em;
    line-height: 3em;
    pointer-events: none;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
    width: 3em;
}

.select-wrapper select::-ms-expand {
    display: none;
}

input[type="text"],
input[type="password"],
input[type="email"],
select {
    height: 3em;
}

textarea {
    padding: 0.75em 1em;
}

select option {
    background-color: #1c1d26;
    color: #ffffff;
}

select:focus::-ms-value {
    background: transparent;
}

input[type="checkbox"],
input[type="radio"] {
    -moz-appearance: none;
    -webkit-appearance: none;
    -ms-appearance: none;
    appearance: none;
    display: block;
    float: left;
    margin-right: -2em;
    opacity: 0;
    width: 1em;
    z-index: -1;
}

input[type="checkbox"]+label,
input[type="radio"]+label {
    text-decoration: none;
    color: rgba(255, 255, 255, 0.75);
    cursor: pointer;
    display: inline-block;
    font-size: 1em;
    font-weight: 100;
    padding-left: 2.55em;
    padding-right: 0.75em;
    position: relative;
}

input[type="checkbox"]+label:before,
input[type="radio"]+label:before {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-transform: none !important;
}

input[type="checkbox"]+label:before,
input[type="radio"]+label:before {
    border-radius: 4px;
    border: solid 1px rgba(255, 255, 255, 0.3);
    content: '';
    display: inline-block;
    height: 1.8em;
    left: 0;
    line-height: 1.725em;
    position: absolute;
    text-align: center;
    top: 0;
    width: 1.8em;
}

input[type="checkbox"]:checked+label:before,
input[type="radio"]:checked+label:before {
    background: rgba(255, 255, 255, 0.25);
    color: #ffffff;
    content: '\f00c';
}

input[type="checkbox"]:focus+label:before,
input[type="radio"]:focus+label:before {
    border-color: #e44c65;
}

input[type="checkbox"]+label:before {
    border-radius: 4px;
}

input[type="radio"]+label:before {
    border-radius: 100%;
}

::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.25) !important;
    font-size: 16px;
    opacity: 1.0;
}

:-moz-placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
    opacity: 1.0;
}

::-moz-placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
    opacity: 1.0;
}

:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
    opacity: 1.0;
}

.formerize-placeholder {
    color: rgba(255, 255, 255, 0.5) !important;
    opacity: 1.0;
}

/* Box */

.box {
    border-radius: 4px;
    border: solid 1px rgba(255, 255, 255, 0.3);
    margin-bottom: 2em;
    padding: 1.5em;
}

.box> :last-child,
.box> :last-child> :last-child,
.box> :last-child> :last-child> :last-child {
    margin-bottom: 0;
}

.box.alt {
    border: 0;
    border-radius: 0;
    padding: 0;
}

/* Icon */

.icon {
    text-decoration: none;
    border-bottom: none;
    position: relative;
}

.icon:before {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-transform: none !important;
}

.icon>.label {
    display: none;
}

.icon.alt {
    text-decoration: none;
}

.icon.alt:before {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-transform: none !important;
}

.icon.alt:before {
    color: #1c1d26 !important;
    text-shadow: 1px 0 0 #ffffff, -1px 0 0 #ffffff, 0 1px 0 #ffffff, 0 -1px 0 #ffffff;
}

.fa_icon {
    display: flex;
    justify-content: center;
}

.icon.major {
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 10em;
    line-height: 6em;
    margin: 0 0 2em 0;
    text-align: center;
    width: 10em;
    overflow: hidden;
    cursor: pointer;
}

.icon.major img {
    width: 100%;
    height: 100%;
    opacity: 1;
    transition: all 0.8s ease-in-out;
}

.icon.major:hover img{
    opacity: 0.1;
    transform: scale(1.2);
}

.hobby_img_info {
    position: absolute;
    opacity: 0;
    transition: all 0.8s ease-in-out;
}

.icon.major:hover .hobby_img_info{
    opacity: 1;
}

.hobby_img_info h1 {
    margin: 30px 45px 0 45px;
    line-height: 40px;
    font-size: 24px;
    color: #ffffff;
    font-weight: lighter;
    border-bottom: 1px solid #ffffff;
}

.hobby_img_info h2 {
    color: #8d8d8d;
    font-size: 18px;
    line-height: 35px;
    font-style: italic;
}

.spinner_01 {
    border: 6px solid #deb0cb;
    border-right-color: #1ad280;
    border-bottom-color: #1ad280;
}

.spinner_02 {
    border: 6px solid #ff9933;
    border-right-color: #3366cc;
    border-bottom-color: #3366cc;
}

.spinner_03 {
    border: 6px solid #339933;
    border-right-color: #cc9900;
    border-bottom-color: #cc9900;
}


.icon.major:before {
    font-size: 2.25em;
}

.icon.major.alt {
    text-decoration: none;
}

.icon.major.alt:before {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-transform: none !important;
}

.icon.major.alt:before {
    color: #272833 !important;
    text-shadow: 1px 0 0 #ffffff, -1px 0 0 #ffffff, 0 1px 0 #ffffff, 0 -1px 0 #ffffff;
}

/* Image */

.image {
    display: inline-block;
    position: relative;
    overflow: hidden;
    background: rgba(39, 40, 51, 0.965);
}

.image:before {
    content: '';
    position: absolute;
    inset: -10px 104px;
    background: linear-gradient(315deg, #e44c65,#62cb44);
    transition: 0.6s;
    animation:  animate 3s linear infinite;
}

.image:hover:before {
    inset: -20px 0;
}

@keyframes animate {
    0%{
        transform: rotate(0deg);
    }
    100%{
        transform: rotate(360deg);
    }
}

.image:after {
    content: '';
    position: absolute;
    border-radius: 50%;   
    z-index: 1;
}

.img-content {
    position: absolute;
    border-radius: 50%;
    inset: 9px;
    z-index: 3;
    overflow: hidden;
    border: 4px solid rgba(23, 24, 32, 0.65);
}

.img-content img:hover {
    transform: scale(1.2);
}

.image img {
    display: block;
    transition: 0.6s;
    cursor: pointer;
}

.image.left {
    float: left;
    margin: 0 1.5em 1em 0;
    top: 0.25em;
}

.image.right {
    float: right;
    margin: 0 0 1em 1.5em;
    top: 0.25em;
}

.image.left,
.image.right {
    max-width: 40%;
}

.image.left img,
.image.right img {
    width: 100%;
}

.image.fit {
    display: block;
    margin: 0 0 2em 0;
    width: 100%;
}

.image.fit img {
    width: 100%;
}

/* List */

ol {
    list-style: decimal;
    margin: 0 0 2em 0;
    padding-left: 1.25em;
}

ol li {
    padding-left: 0.25em;
}

ul {
    list-style: disc;
    margin: 0 0 2em 0;
    padding-left: 1em;
}

ul li {
    padding-left: 0.5em;
}

ul.alt {
    list-style: none;
    padding-left: 0;
}

ul.alt li {
    border-top: solid 1px rgba(255, 255, 255, 0.3);
    padding: 0.5em 0;
}

ul.alt li:first-child {
    border-top: 0;
    padding-top: 0;
}

ul.icons {
    cursor: default;
    list-style: none;
    padding-left: 0;
}

ul.icons li {
    display: inline-block;
    height: 2.5em;
    line-height: 2.5em;
    padding: 0 0.5em;
}

ul.icons li .icon {
    font-size: 0.8em;
}

ul.icons li .icon:before {
    font-size: 2em;
}

ul.actions {
    cursor: default;
    list-style: none;
    padding-left: 0;
}

ul.actions li {
    display: inline-block;
    padding: 0 1em 0 0;
    vertical-align: middle;
}

ul.actions li:last-child {
    padding-right: 0;
}

ul.actions.small li {
    padding: 0 0.5em 0 0;
}

ul.actions.vertical li {
    display: block;
    padding: 1em 0 0 0;
}

ul.actions.vertical li:first-child {
    padding-top: 0;
}

ul.actions.vertical li>* {
    margin-bottom: 0;
}

ul.actions.vertical.small li {
    padding: 0.5em 0 0 0;
}

ul.actions.vertical.small li:first-child {
    padding-top: 0;
}

ul.actions.fit {
    display: table;
    margin-left: -1em;
    padding: 0;
    table-layout: fixed;
    width: calc(100% + 1em);
}

ul.actions.fit li {
    display: table-cell;
    padding: 0 0 0 1em;
}

ul.actions.fit li>* {
    margin-bottom: 0;
}

ul.actions.fit.small {
    margin-left: -0.5em;
    width: calc(100% + 0.5em);
}

ul.actions.fit.small li {
    padding: 0 0 0 0.5em;
}

dl {
    margin: 0 0 2em 0;
}

/* Table */

.table-wrapper {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;
}

table {
    margin: 0 0 2em 0;
    width: 100%;
}

table tbody tr {
    border: solid 1px rgba(255, 255, 255, 0.3);
    border-left: 0;
    border-right: 0;
}

table tbody tr:nth-child(2n + 1) {
    background-color: rgba(255, 255, 255, 0.075);
}

table td {
    padding: 0.75em 0.75em;
}

table th {
    color: #ffffff;
    font-size: 0.9em;
    font-weight: 300;
    padding: 0 0.75em 0.75em 0.75em;
    text-align: left;
}

table thead {
    border-bottom: solid 1px rgba(255, 255, 255, 0.3);
}

table tfoot {
    border-top: solid 1px rgba(255, 255, 255, 0.3);
}

table.alt {
    border-collapse: separate;
}

table.alt tbody tr td {
    border: solid 1px rgba(255, 255, 255, 0.3);
    border-left-width: 0;
    border-top-width: 0;
}

table.alt tbody tr td:first-child {
    border-left-width: 1px;
}

table.alt tbody tr:first-child td {
    border-top-width: 1px;
}

table.alt thead {
    border-bottom: 0;
}

table.alt tfoot {
    border-top: 0;
}

/* Button */

input[type="submit"],
input[type="reset"],
input[type="button"],
.button {
    -moz-appearance: none;
    -webkit-appearance: none;
    -ms-appearance: none;
    appearance: none;
    -moz-transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    -webkit-transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    -ms-transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    background-color: transparent;
    border-radius: 4px;
    border: 0;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.3);
    color: #ffffff !important;
    cursor: pointer;
    display: inline-block;
    font-weight: 300;
    height: 3em;
    line-height: 3em;
    padding: 0 2.25em;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
}

input[type="submit"]:hover,
input[type="submit"]:active,
input[type="reset"]:hover,
input[type="reset"]:active,
input[type="button"]:hover,
input[type="button"]:active,
.button:hover,
.button:active {
    box-shadow: inset 0 0 0 1px #e44c65;
    color: #e44c65 !important;
}

input[type="submit"]:active,
input[type="reset"]:active,
input[type="button"]:active,
.button:active {
    background-color: rgba(228, 76, 101, 0.15);
}

input[type="submit"].icon:before,
input[type="reset"].icon:before,
input[type="button"].icon:before,
.button.icon:before {
    margin-right: 0.5em;
}

input[type="submit"].fit,
input[type="reset"].fit,
input[type="button"].fit,
.button.fit {
    display: block;
    margin: 0 0 1em 0;
    width: 100%;
}

input[type="submit"].small,
input[type="reset"].small,
input[type="button"].small,
.button.small {
    font-size: 0.8em;
}

input[type="submit"].big,
input[type="reset"].big,
input[type="button"].big,
.button.big {
    font-size: 1.35em;
}

input[type="submit"].special,
input[type="reset"].special,
input[type="button"].special,
.button.special {
    background-color: #e44c65;
    box-shadow: none;
    color: #ffffff !important;
}

input[type="submit"].special:hover,
input[type="reset"].special:hover,
input[type="button"].special:hover,
.button.special:hover {
    background-color: #e76278;
}

input[type="submit"].special:active,
input[type="reset"].special:active,
input[type="button"].special:active,
.button.special:active {
    background-color: #e13652;
}

input[type="submit"].disabled,
input[type="submit"]:disabled,
input[type="reset"].disabled,
input[type="reset"]:disabled,
input[type="button"].disabled,
input[type="button"]:disabled,
.button.disabled,
.button:disabled {
    background-color: rgba(255, 255, 255, 0.3) !important;
    box-shadow: none !important;
    color: #ffffff !important;
    cursor: default;
    opacity: 0.25;
}

/* Goto Next */

.goto-next {
    border: 0;
    bottom: 0;
    display: block;
    height: 5em;
    left: 50%;
    margin: 0 0 0 -5em;
    overflow: hidden;
    position: absolute;
    text-indent: 10em;
    white-space: nowrap;
    width: 10em;
    z-index: 1;
}

.goto-next:before {
    background-image: url("../image/index/箭头.png");
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    content: '';
    display: block;
    height: 1.5em;
    left: 50%;
    margin: -0.75em 0 0 -1em;
    position: absolute;
    top: 50%;
    width: 2em;
    z-index: 1;
    animation: updown 2s ease-in-out infinite;
}

@keyframes updown {

    0%,
    100% {
        transform: translate(-50%, -100%);
    }

    50% {
        transform: translate(-50%, -50%);
    }
}
/* Spotlight */

.spotlight {
    background-attachment: fixed;
    background-position: center center;
    background-size: cover;
    box-shadow: 0 0.25em 0.5em 0 rgba(0, 0, 0, 0.25);
    height: 100vh;
    overflow: hidden;
    position: relative;
}

.spotlight:nth-last-of-type(1) {
    z-index: 1;
}

.spotlight:nth-last-of-type(2) {
    z-index: 2;
}

.spotlight:nth-last-of-type(3) {
    z-index: 3;
}

.spotlight:nth-last-of-type(4) {
    z-index: 4;
}

.spotlight:nth-last-of-type(5) {
    z-index: 5;
}

.spotlight:nth-last-of-type(6) {
    z-index: 6;
}

.spotlight:nth-last-of-type(7) {
    z-index: 7;
}

.spotlight:nth-last-of-type(8) {
    z-index: 8;
}

.spotlight:nth-last-of-type(9) {
    z-index: 9;
}

.spotlight:nth-last-of-type(10) {
    z-index: 10;
}

.spotlight:nth-last-of-type(11) {
    z-index: 11;
}

.spotlight:nth-last-of-type(12) {
    z-index: 12;
}

.spotlight:nth-last-of-type(13) {
    z-index: 13;
}

.spotlight:nth-last-of-type(14) {
    z-index: 14;
}

.spotlight:nth-last-of-type(15) {
    z-index: 15;
}

.spotlight:nth-last-of-type(16) {
    z-index: 16;
}

.spotlight:nth-last-of-type(17) {
    z-index: 17;
}

.spotlight:nth-last-of-type(18) {
    z-index: 18;
}

.spotlight:nth-last-of-type(19) {
    z-index: 19;
}

.spotlight:nth-last-of-type(20) {
    z-index: 20;
}

.spotlight:before {
    content: '';
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%;
}

.spotlight .image.main {
    display: none;
}

.spotlight .image.main img {
    position: relative;
}

.spotlight .content {
    -moz-transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
    -moz-transition: -moz-transform 1s ease, opacity 1s ease;
    -webkit-transition: -webkit-transform 1s ease, opacity 1s ease;
    -ms-transition: -ms-transform 1s ease, opacity 1s ease;
    transition: transform 1s ease, opacity 1s ease;
    background: rgba(23, 24, 32, 0.95);
    border-style: solid;
    opacity: 1;
    position: absolute;
}

.spotlight .goto-next {
    -moz-transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
    -moz-transition: -moz-transform 0.75s ease, opacity 1s ease-in;
    -webkit-transition: -webkit-transform 0.75s ease, opacity 1s ease-in;
    -ms-transition: -ms-transform 0.75s ease, opacity 1s ease-in;
    transition: transform 0.75s ease, opacity 1s ease-in;
    -moz-transition-delay: 0.5s;
    -webkit-transition-delay: 0.5s;
    -ms-transition-delay: 0.5s;
    transition-delay: 0.5s;
    opacity: 1;
}

.spotlight.top .content,
.spotlight.bottom .content {
    left: 0;
    padding: 5.1em 0 3.1em 0;
    width: 100%;
}

.spotlight.top .content {
    border-bottom-width: 0.35em;
    top: 0;
}

.spotlight.bottom .content {
    border-top-width: 0.35em;
    bottom: 0;
}

.spotlight.left .content,
.spotlight.right .content {
    height: 101%;
    padding: 6em 3em;
    top: 0;
    width: 28em;
}

.spotlight.left .content {
    border-right-width: 0.35em;
    left: 0;
}

.spotlight.right .content {
    border-left-width: 0.35em;
    right: 0;
}

.spotlight.style1 .content {
    border-color: #e44c65;
}

.spotlight.style2 .content {
    border-color: #5480f1;
}

.spotlight.style3 .content {
    border-color: #39c088;
}

.spotlight.inactive .content {
    opacity: 0;
}

.spotlight.inactive .goto-next {
    -moz-transform: translate(0, 1.5em);
    -webkit-transform: translate(0, 1.5em);
    -ms-transform: translate(0, 1.5em);
    transform: translate(0, 1.5em);
    opacity: 0;
}

.spotlight.inactive.top .content {
    -moz-transform: translate(0, -5em);
    -webkit-transform: translate(0, -5em);
    -ms-transform: translate(0, -5em);
    transform: translate(0, -5em);
}

.spotlight.inactive.bottom .content {
    -moz-transform: translate(0, 5em);
    -webkit-transform: translate(0, 5em);
    -ms-transform: translate(0, 5em);
    transform: translate(0, 5em);
}

.spotlight.inactive.left .content {
    -moz-transform: translate(-5em, 0);
    -webkit-transform: translate(-5em, 0);
    -ms-transform: translate(-5em, 0);
    transform: translate(-5em, 0);
}

.spotlight.inactive.right .content {
    -moz-transform: translate(5em, 0);
    -webkit-transform: translate(5em, 0);
    -ms-transform: translate(5em, 0);
    transform: translate(5em, 0);
}

body.is-touch .spotlight {
    background-attachment: scroll;
}

/* Wrapper */

.wrapper {
    padding: 6em 0 4em 0;
}

.wrapper.style2 {
    text-align: center;
    background: #e44c65;
}

.wrapper.style2 input[type="text"]:focus,
.wrapper.style2 input[type="password"]:focus,
.wrapper.style2 input[type="email"]:focus,
.wrapper.style2 select:focus,
.wrapper.style2 textarea:focus {
    border-color: rgba(255, 255, 255, 0.5);
}

.wrapper.style2 input[type="submit"]:hover,
.wrapper.style2 input[type="submit"]:active,
.wrapper.style2 input[type="reset"]:hover,
.wrapper.style2 input[type="reset"]:active,
.wrapper.style2 input[type="button"]:hover,
.wrapper.style2 input[type="button"]:active,
.wrapper.style2 .button:hover,
.wrapper.style2 .button:active {
    background-color: rgba(255, 255, 255, 0.075) !important;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
    color: #ffffff !important;
}

.wrapper.style2 input[type="submit"]:active,
.wrapper.style2 input[type="reset"]:active,
.wrapper.style2 input[type="button"]:active,
.wrapper.style2 .button:active {
    background-color: rgba(255, 255, 255, 0.25) !important;
}

.wrapper.style2 input[type="submit"].special,
.wrapper.style2 input[type="reset"].special,
.wrapper.style2 input[type="button"].special,
.wrapper.style2 .button.special {
    background-color: #ffffff;
    color: #e44c65 !important;
}

.wrapper.style2 input[type="submit"].special:hover,
.wrapper.style2 input[type="submit"].special:active,
.wrapper.style2 input[type="reset"].special:hover,
.wrapper.style2 input[type="reset"].special:active,
.wrapper.style2 input[type="button"].special:hover,
.wrapper.style2 input[type="button"].special:active,
.wrapper.style2 .button.special:hover,
.wrapper.style2 .button.special:active {
    background-color: rgba(255, 255, 255, 0.075) !important;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
    color: #ffffff !important;
}

.wrapper.style2 input[type="submit"].special:active,
.wrapper.style2 input[type="reset"].special:active,
.wrapper.style2 input[type="button"].special:active,
.wrapper.style2 .button.special:active {
    background-color: rgba(255, 255, 255, 0.25) !important;
}

.wrapper.fade-down>.container {
    -moz-transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
    -moz-transition: -moz-transform 1s ease, opacity 1s ease;
    -webkit-transition: -webkit-transform 1s ease, opacity 1s ease;
    -ms-transition: -ms-transform 1s ease, opacity 1s ease;
    transition: transform 1s ease, opacity 1s ease;
    opacity: 1;
}

.wrapper.fade-down.inactive>.container {
    -moz-transform: translate(0, -1em);
    -webkit-transform: translate(0, -1em);
    -ms-transform: translate(0, -1em);
    transform: translate(0, -1em);
    opacity: 0;
}

.wrapper.fade-up>.container {
    -moz-transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
    -moz-transition: -moz-transform 1s ease, opacity 1s ease;
    -webkit-transition: -webkit-transform 1s ease, opacity 1s ease;
    -ms-transition: -ms-transform 1s ease, opacity 1s ease;
    transition: transform 1s ease, opacity 1s ease;
    opacity: 1;
}

.wrapper.fade-up.inactive>.container {
    -moz-transform: translate(0, 1em);
    -webkit-transform: translate(0, 1em);
    -ms-transform: translate(0, 1em);
    transform: translate(0, 1em);
    opacity: 0;
}

.wrapper.fade>.container {
    -moz-transition: opacity 1s ease;
    -webkit-transition: opacity 1s ease;
    -ms-transition: opacity 1s ease;
    transition: opacity 1s ease;
    opacity: 1;
}

.wrapper.fade.inactive>.container {
    opacity: 0;
}

/* Dropotron */

.dropotron {
    background: rgba(39, 40, 51, 0.965);
    border-radius: 4px;
    box-shadow: 0 0.075em 0.35em 0 rgba(0, 0, 0, 0.125);
    list-style: none;
    margin-top: calc(-0.25em + 1px);
    min-width: 12em;
    padding: 0.25em 0;
}

.dropotron>li {
    border-top: solid 1px rgba(255, 255, 255, 0.035);
    padding: 0;
}

.dropotron>li a,
.dropotron>li span {
    border: 0;
    color: rgba(255, 255, 255, 0.75);
    display: block;
    padding: 0.1em 1em;
    text-decoration: none;
}

.dropotron>li:first-child {
    border-top: 0;
}

.dropotron>li.active>a,
.dropotron>li.active>span {
    color: #e44c65;
}

.dropotron.level-0 {
    font-size: 0.8em;
    margin-top: 1em;
}

.dropotron.level-0:before {
    -moz-transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    background: #272833;
    content: '';
    display: block;
    height: 1em;
    position: absolute;
    right: 1.5em;
    top: -0.5em;
    width: 1em;
}

body.landing .dropotron.level-0 {
    margin-top: 0;
}

/* Header */

#page-wrapper {
    padding-top: 3.5em;
}

#header {
    background: rgba(39, 40, 51, 0.965);
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);
    cursor: default;
    height: 3.8em;
    left: 0;
    line-height: 3.8em;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 99;
}

#header h1 {
    height: inherit;
    left: 4.25em;
    line-height: inherit;
    margin: 0;
    position: absolute;
    top: 0;
}

#header nav {
    position: absolute;
    right: 4.25em;
    top: 0;
}

#header nav ul {
    margin: 0;
}

#header nav ul li {
    display: inline-block;
    margin-left: 1em;
}

#header nav ul li a,
#header nav ul li span {
    border: 0;
    color: inherit;
    display: inline-block;
    height: inherit;
    line-height: inherit;
    outline: 0;
}

#header nav ul li a.button,
#header nav ul li span.button {
    height: 2em;
    line-height: 2em;
    padding: 0 1.25em;
}

#header nav ul li a:not(.button):before,
#header nav ul li span:not(.button):before {
    margin-right: 0.5em;
}

#header nav ul li.active>a,
#header nav ul li.active>span {
    color: #e44c65;
}

#header nav ul li>ul {
    display: none;
}

body.landing #page-wrapper {
    padding-top: 0;
}

body.landing #header {
	box-sizing: border-box;
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
	/* 模糊，磨砂质感 */
	backdrop-filter: blur(8px);
	z-index: 99;
}

/* Banner */

#banner {
    background-attachment: fixed;
    background-color: #272833;
    background-image: url("../image/index/1.jpg");
    background-position: center center;
    background-size: cover;
    box-shadow: 0 0.25em 0.5em 0 rgba(0, 0, 0, 0.25);
    min-height: 100vh;
    position: relative;
    text-align: center;
    z-index: 21;
}

#banner:before {
    content: '';
    display: inline-block;
    height: 100vh;
    vertical-align: middle;
    width: 1%;
}

#banner:after {
    background-image: -moz-linear-gradient(top, rgba(23, 24, 32, 0.95), rgba(23, 24, 32, 0.95));
    background-image: -webkit-linear-gradient(top, rgba(23, 24, 32, 0.75), rgba(23, 24, 32, 0.65));
    background-image: -ms-linear-gradient(top, rgba(23, 24, 32, 0.95), rgba(23, 24, 32, 0.95));
    background-image: linear-gradient(top, rgba(23, 24, 32, 0.95), rgba(23, 24, 32, 0.95));
    content: '';
    display: block;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}

#banner .content {
    display: inline-block;
    max-width: 95%;
    padding: 6em;
    position: relative;
    text-align: left;
    vertical-align: middle;
    z-index: 1;
}

#banner .content header {
    display: inline-block;
    vertical-align: middle;
}

#banner .content header h2 {
    font-size: 2.5em;
    margin: 0;
}

#banner .content header p {
    margin: 0.5em 0 0 0;
    top: 0;
}

#banner .content .image {
    border-radius: 100%;
    display: inline-block;
    height: 18em;
    margin-right: 3em;
    vertical-align: middle;
    width: 18em;
}

#banner .content .image img {
    border-radius: 100%;
    display: block;
    width: 100%;
}

body.is-touch #banner {
    background-attachment: scroll;
}

/* Footer */

#footer {
    background: #272833;
    padding: 6em 0;
    text-align: center;
}

#footer .icons .icon.alt {
    text-decoration: none;
}

#footer .icons .icon.alt:before {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-transform: none !important;
}

#footer .icons .icon.alt:before {
    color: #272833 !important;
    text-shadow: 1px 0 0 rgba(255, 255, 255, 0.5), -1px 0 0 rgba(255, 255, 255, 0.5), 0 1px 0 rgba(255, 255, 255, 0.5), 0 -1px 0 rgba(255, 255, 255, 0.5);
}

#footer .copyright {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8em;
    line-height: 1em;
    margin: 2em 0 0 0;
    padding: 0;
    text-align: center;
}

#footer .copyright li {
    border-left: solid 1px rgba(255, 255, 255, 0.3);
    display: inline-block;
    list-style: none;
    margin-left: 1.5em;
    padding-left: 1.5em;
}

#footer .copyright li:first-child {
    border-left: 0;
    margin-left: 0;
    padding-left: 0;
}

#footer .copyright li a {
    color: inherit;
}

/* XLarge */

@media screen and (max-width: 1680px) {

    /* Basic */

    body,
    input,
    select,
    textarea {
        font-size: 13pt;
    }

}

/* Large */

@media screen and (max-width: 1280px) {

    /* Basic */

    body,
    input,
    select,
    textarea {
        font-size: 11.5pt;
    }

    /* Spotlight */

    .spotlight.top .content {
        padding: 3.825em 0 1.825em 0;
    }

    .spotlight.bottom .content {
        padding: 3.825em 0 2.95em 0;
    }

    .spotlight.left .content,
    .spotlight.right .content {
        padding: 4.5em 2.5em;
        width: 25em;
    }

    /* Wrapper */

    .wrapper {
        padding: 4.5em 0 2.5em 0;
    }

    /* Dropotron */

    .dropotron.level-0 {
        font-size: 1em;
    }

    /* Banner */

    #banner .content {
        padding: 4.5em;
    }

    /* Footer */

    #footer {
        padding: 4.5em 0;
    }

}

/* Medium */

@media screen and (max-width: 980px) {

    /* Basic */

    body,
    input,
    select,
    textarea {
        font-size: 12pt;
    }

    /* Spotlight */

    .spotlight {
        background-attachment: scroll;
        height: auto;
    }

    .spotlight .image.main {
        display: block;
        margin: 0;
        max-height: 40vh;
        overflow: hidden;
    }

    .spotlight .content {
        background-color: #1c1d26;
        border-width: 0 !important;
        border-top-width: 0.35em !important;
        bottom: auto !important;
        left: auto !important;
        padding: 4.5em 2.5em 2.5em 2.5em !important;
        position: relative;
        right: auto !important;
        text-align: center;
        top: auto !important;
        width: 100% !important;
    }

    .spotlight .goto-next {
        display: none;
    }

    /* Wrapper */

    .wrapper {
        padding: 4.5em 2.5em 2.5em 2.5em;
    }

    /* Banner */

    #banner {
        background-attachment: scroll;
    }

    #banner .goto-next {
        height: 7em;
    }

    #banner .content {
        padding: 9em 0;
        text-align: center;
    }

    #banner .content header {
        display: block;
        text-align: center;
    }

    #banner .content .image {
        margin: 0 0 2em 0;
    }

    /* Footer */

    #footer {
        padding: 4.5em 0;
    }

}

/* Small */

#navPanel,
#titleBar {
    display: none;
}

@media screen and (max-width: 736px) {

    /* Basic */

    html,
    body {
        overflow-x: hidden;
    }

    body,
    input,
    select,
    textarea {
        font-size: 12pt;
    }

    h2 {
        font-size: 1.5em;
    }

    h3 {
        font-size: 1.2em;
    }

    h4 {
        font-size: 1em;
    }

    /* Section/Article */

    header p br {
        display: none;
    }

    header h2+p {
        font-size: 1em;
    }

    header h3+p {
        font-size: 1em;
    }

    header h4+p,
    header h5+p,
    header h6+p {
        font-size: 0.9em;
    }

    header.major {
        margin: 0 0 2em 0;
    }

    /* Goto Next */

    .goto-next:before {
        height: 0.8em;
        margin: -0.4em 0 0 -0.6em;
        width: 1.2em;
    }

    /* Spotlight */

    .spotlight {
        box-shadow: 0 0.125em 0.5em 0 rgba(0, 0, 0, 0.25);
    }

    .spotlight .image.main {
        max-height: 60vh;
    }

    .spotlight .content {
        border-top-width: 0.2em !important;
        padding: 3.25em 1.5em 1.25em 1.5em !important;
    }

    /* Wrapper */

    .wrapper {
        padding: 3.25em 1.5em 1.25em 1.5em;
    }

    /* Header */

    #header {
        display: none;
    }

    /* Banner */

    #banner {
        box-shadow: 0 0.125em 0.5em 0 rgba(0, 0, 0, 0.25);
        min-height: calc(100vh - 44px);
    }

    #banner:before {
        height: calc(100vh - 44px);
    }

    #banner .content {
        padding: 4.0625em 1.5em 4.875em 1.5em;
    }

    #banner .content header h2 {
        font-size: 1.5em;
    }

    #banner .content .image {
        height: 9em;
        width: 9em;
    }

    /* Off-Canvas Navigation */

    #page-wrapper {
        -moz-backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        -ms-backface-visibility: hidden;
        backface-visibility: hidden;
        -moz-transition: -moz-transform 0.5s ease;
        -webkit-transition: -webkit-transform 0.5s ease;
        -ms-transition: -ms-transform 0.5s ease;
        transition: transform 0.5s ease;
        padding-bottom: 1px;
        padding-top: 44px !important;
    }

    #titleBar {
        -moz-backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        -ms-backface-visibility: hidden;
        backface-visibility: hidden;
        -moz-transition: -moz-transform 0.5s ease;
        -webkit-transition: -webkit-transform 0.5s ease;
        -ms-transition: -ms-transform 0.5s ease;
        transition: transform 0.5s ease;
        display: block;
        height: 44px;
        left: 0;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 10001;
        background: #272833;
        box-shadow: 0 0.125em 0.125em 0 rgba(0, 0, 0, 0.125);
    }

    #titleBar .title {
        color: #ffffff;
        display: block;
        font-weight: 300;
        height: 44px;
        line-height: 44px;
        text-align: center;
    }

    #titleBar .title a {
        color: inherit;
        border: 0;
    }

    #titleBar .toggle {
        text-decoration: none;
        height: 60px;
        left: 0;
        position: absolute;
        top: 0;
        width: 90px;
        outline: 0;
        border: 0;
    }

    #titleBar .toggle:before {
        -moz-osx-font-smoothing: grayscale;
        -webkit-font-smoothing: antialiased;
        font-family: FontAwesome;
        font-style: normal;
        font-weight: normal;
        text-transform: none !important;
    }

    #titleBar .toggle:before {
        background: #e44c65;
        color: rgba(255, 255, 255, 0.5);
        content: '\f00b';
        display: block;
        font-size: 18px;
        height: 44px;
        left: 0;
        line-height: 44px;
        position: absolute;
        text-align: center;
        top: 0;
        width: 54px;
    }

    #navPanel {
        -moz-backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        -ms-backface-visibility: hidden;
        backface-visibility: hidden;
        -moz-transform: translateX(-275px);
        -webkit-transform: translateX(-275px);
        -ms-transform: translateX(-275px);
        transform: translateX(-275px);
        -moz-transition: -moz-transform 0.5s ease;
        -webkit-transition: -webkit-transform 0.5s ease;
        -ms-transition: -ms-transform 0.5s ease;
        transition: transform 0.5s ease;
        display: block;
        height: 100%;
        left: 0;
        overflow-y: auto;
        position: fixed;
        top: 0;
        width: 275px;
        z-index: 10002;
        background: #181920;
        padding: 0.75em 1.25em;
    }

    #navPanel .link {
        border: 0;
        border-top: solid 1px rgba(255, 255, 255, 0.05);
        color: rgba(255, 255, 255, 0.75);
        display: block;
        height: 3em;
        line-height: 3em;
        text-decoration: none;
    }

    #navPanel .link:hover {
        color: inherit !important;
    }

    #navPanel .link:first-child {
        border-top: 0;
    }

    #navPanel .link.depth-0 {
        color: #ffffff;
        font-weight: 300;
    }

    #navPanel .link .indent-1 {
        display: inline-block;
        width: 1.25em;
    }

    #navPanel .link .indent-2 {
        display: inline-block;
        width: 2.5em;
    }

    #navPanel .link .indent-3 {
        display: inline-block;
        width: 3.75em;
    }

    #navPanel .link .indent-4 {
        display: inline-block;
        width: 5em;
    }

    #navPanel .link .indent-5 {
        display: inline-block;
        width: 6.25em;
    }

    body.navPanel-visible #page-wrapper {
        -moz-transform: translateX(275px);
        -webkit-transform: translateX(275px);
        -ms-transform: translateX(275px);
        transform: translateX(275px);
    }

    body.navPanel-visible #titleBar {
        -moz-transform: translateX(275px);
        -webkit-transform: translateX(275px);
        -ms-transform: translateX(275px);
        transform: translateX(275px);
    }

    body.navPanel-visible #navPanel {
        -moz-transform: translateX(0);
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
    }

    /* Footer */

    #footer {
        padding: 3.25em 1.5em;
    }

}

/* XSmall */

@media screen and (max-width: 480px) {

    /* Basic */

    html,
    body {
        min-width: 320px;
    }

    body,
    input,
    select,
    textarea {
        font-size: 12pt;
    }

    /* List */

    ul.actions {
        margin: 0 0 2em 0;
    }

    ul.actions li {
        display: block;
        padding: 1em 0 0 0;
        text-align: center;
        width: 100%;
    }

    ul.actions li:first-child {
        padding-top: 0;
    }

    ul.actions li>* {
        margin: 0 !important;
        width: 100%;
    }

    ul.actions.small li {
        padding: 0.5em 0 0 0;
    }

    ul.actions.small li:first-child {
        padding-top: 0;
    }

    /* Button */

    input[type="submit"],
    input[type="reset"],
    input[type="button"],
    .button {
        padding: 0;
    }

    /* Spotlight */

    .spotlight .image.main {
        max-height: 50vh;
    }

    .spotlight .content {
        padding: 3em 1.25em 1em 1.25em !important;
    }

    /* Wrapper */

    .wrapper {
        padding: 3em 1.25em 1em 1.25em;
    }

    /* Banner */

    #banner .content {
        padding: 3em 1.5625em 5.25em 1.5625em;
    }

    /* Footer */

    #footer {
        padding: 3em 1.25em;
    }

    #footer .copyright {
        line-height: inherit;
    }

    #footer .copyright li {
        border-left: 0;
        display: block;
        margin: 0;
        padding: 0;
    }

}
