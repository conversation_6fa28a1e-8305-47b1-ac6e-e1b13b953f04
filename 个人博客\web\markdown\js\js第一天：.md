# js第一天：

## 1注释

```
crtl+/ 单行注释
```

```
shift+alt+a 多行注释 暂时还用不了/**/
```

![image-20241029104525998](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029104525998.png)

## 2结束符

```
js中语句结束符是';'，可写可不写，看习惯
```

## 3js输入输出语句

```
输出语句是
1.网页输出document.write(变量，常量，标签等)注意标签和直接输出内容要加引号
2.控制台输出console.log(不能输出标签，其他一致)这是给程序员看的
3.弹出框输出alert()页面交互
```

```
输入语句是
prompt()
```

![image-20241029105140359](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029105140359.png)

![image-20241029105155889](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029105155889.png)

![image-20241029105534628](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029105534628.png)

![image-20241029105605521](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029105605521.png)

![image-20241029105711067](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029105711067.png)

![image-20241029105730282](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029105730282.png)

## 4变量的声明以及更新

![image-20241029105852091](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029105852091.png)

![image-20241029105906380](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029105906380.png)

![image-20241029110023268](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029110023268.png)

## 5变量交互

![image-20241029110421405](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029110421405.png)

![image-20241029110613115](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029110613115.png)

## 6变量命名规则

```
用小驼峰命名法
第一个单词小写，第二个首字母大写
```

## 7小案例

![Image_41206694183342](F:\qq聊天记录\405133232\FileRecv\MobileFile\Image_41206694183342.jpg)

![image-20241029111441013](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029111441013.png)

![image-20241029111508201](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241029111508201.png)

## 8数组的使用

```let arr=[]
let arr=[]
```

```
数组在js中只有一个属性,length,数组长=索引号+1
```

```
console.log(arr.length)
```

## 9字面量

在JavaScript中，字面量（literal）是一个重要的概念，用于表示固定值的表示法。以下是对JavaScript中字面量的详细解释：

\### 字面量的定义和类型

1. **定义**：

  字面量是直接出现在代码中的固定值，用于表示字符串、数字、布尔值、对象、数组、正则表达式和函数等。它们是构成JavaScript程序的基石，使得数据的初始化和操作变得直接和便捷 。

2. **类型**：

  \- **字符串字面量**：由一对单引号或双引号括起来的字符序列。
  \- **数字字面量**：直接表示的数值，如123或3.14。
  \- **布尔字面量**：表示逻辑值的true或false。
  \- **对象字面量**：用于创建和初始化对象的简洁方式。
  \- **数组字面量**：直接表示的数组，如[1, 2, 3]。
  \- **函数字面量**：直接定义的函数，可以是匿名函数或命名函数。

\### 字面量与变量、常量的区别

\- **变量**：表示可以变化的数据，是用于存储数据的容器，在程序运行中可以发生变化或被重新赋值。例如，使用`var`或`let`定义的变量 。
\- **常量**：表示固定不变的数据，与变量类似，也是用于存储数据的容器，但其值在程序运行中不可改变。在ES6中，使用`const`来定义常量 。

\### 使用字面量的优势

使用字面量可以提高代码的可读性和效率。它们使得代码更简洁，减少了冗余，并使数据的初始化和操作更加直观。

\### 示例

 javascript
var num = 1;  // 数字字面量 1
let str = "abc"; // 字符串字面量 "abc"
const obj = { key: "value" }; // 对象字面量 { key: "value" }
 

在上述示例中，`1`、`"abc"`和`{ key: "value" }`都是字面量，它们直接表示了固定的值。

通过理解字面量的概念，可以更有效地编写和理解JavaScript代码。

