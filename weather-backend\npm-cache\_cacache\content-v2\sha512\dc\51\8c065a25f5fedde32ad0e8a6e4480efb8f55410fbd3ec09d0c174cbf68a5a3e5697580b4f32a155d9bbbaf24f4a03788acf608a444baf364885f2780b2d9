{"_id": "combined-stream", "_rev": "45-8071877f56d51159c12935cada0a84d7", "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "dist-tags": {"latest": "1.0.8", "next": "1.0.6-rc1"}, "versions": {"0.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.0", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {}, "_id": "combined-stream@0.0.0", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "45550d8a25ee3b42de817cf675690732240e45d7", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.0.tgz", "integrity": "sha512-LdFWln+/shOpBLCd2VfHUxEwP9m/kJ75dgBWKVLJ+Ye4PKOFbFE3c8ZnKDXQyu3qf+pfNWb/vtciWAmrcK+5Sg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF8hF+EshQdiKYDETWdpIAhXPdjavmXJshrXKe141vcTAiEA0jA1SdtS7fojBuSnvLvnP4kjZ4JKeS/xHY6NyuWa2nY="}]}, "scripts": {}, "directories": {}}, "0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.1", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "0.0.1"}, "_id": "combined-stream@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "dff7a316813a9ec58ef03bde5c1fc7133a35f944", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.1.tgz", "integrity": "sha512-e+lVCV60dvpBOTToTv42ygWML9zxzlNkjwKkFO1bjbK9vGvY1NF/2GBkDRuAOTG/NLCP8/zcv1Oif202ly9jDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAoCvluqDe9r0IXGGqtWM0AwSpOLuWi/UJy0rUC0qj7RAiB9Np/kaRMJAyS/BnSjbGa3XWqX0sOvl8RcbQ+8cApa/Q=="}]}, "scripts": {}, "directories": {}}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.2", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "0.0.1"}, "_id": "combined-stream@0.0.2", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "6c1691577cfb5f842f7ac1fecd8515a13cb99c9c", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.2.tgz", "integrity": "sha512-k8<PERSON><PERSON><PERSON>liZ8WYzOSRw4et5c9GZBQsMXpowpEfidipCQt8XDP3bEM89Jghpfl/H6mqOnap4PODDTCzL1dob5997YA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGY8yVEomNqs8adoDE64xwsZTYDkSlrrUh0e8fYzGBt4AiEA6GwV9eBF8Sv0HaXXsNp45CZBYdrZMZUUGik+uJ4dtDI="}]}, "scripts": {}, "directories": {}}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.3", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "0.0.1"}, "_id": "combined-stream@0.0.3", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "a1d6223c463a000b21c9937c4b15ef41ba001f78", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.3.tgz", "integrity": "sha512-obT/nnZll8VTnjKTUGtov1vYMCWaTLfrkIHmfioG1wzLMtG5dfMXenNH/wQ5aoxpPaDnJtuYFixDUxDl8V84yQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFdRsVKZY3KxEfRQ2vFCr3CJ4IWH42mcvQ0osB7RGDEyAiBFfP0xLWQ76sXppwCAxvtudmtEns1Az3+dCUTD/5mfeg=="}]}, "scripts": {}, "directories": {}}, "0.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.4", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "engines": {"node": "*"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "0.0.1"}, "_id": "combined-stream@0.0.4", "dist": {"shasum": "2d1a43347dbe9515a4a2796732e5b88473840b22", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.4.tgz", "integrity": "sha512-RVtYNBtwuVyncYQwuTnrsCIfsmsVPnGj1RI8xNeqhmuWFLfNUfaGxGbLWx4N10fNd+MnNVh+SO4f60/7ghV3fg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICqW1/d9hWOgsL5cWoJdoqf+M23HsGabYE5D6EhyH0ZXAiEA5mXYaNUK//89ww7SPH/sbGHFKPtUc7++k/Kb2FEp2tc="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "celer", "email": "<EMAIL>"}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.5", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "~0.0.7"}, "gitHead": "19d9bdd4c20f6806c2ae8adb00a53fb6fd154740", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@0.0.5", "_shasum": "29ed76e5c9aad07c4acf9ca3d32601cce28697a2", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}], "dist": {"shasum": "29ed76e5c9aad07c4acf9ca3d32601cce28697a2", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.5.tgz", "integrity": "sha512-5iibGSlnX9jIyz9F0eSgaoazkVo+7+pQTPS9gJmrP9FcyCaxxaIRb8OLiu1nYHxDeFFTWkkLGe/bkvZdzhza+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVKfIi6koT1itHhDQ0OQe6XJNZZDD1a40miPPectdtAAIgfwLMOvS9sL5zndqiuiBIGGcjZGxR69nU1t9Sxv0iJM4="}]}, "directories": {}}, "0.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.7", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "~0.0.7"}, "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@0.0.7", "dist": {"shasum": "0137e657baa5a7541c57ac37ac5fc07d73b4dc1f", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-0.0.7.tgz", "integrity": "sha512-qfexlmLp9MyrkajQVyjEDb0Vj+KhRgR/rxLiVhaihlT+ZkX0lReqtH6Ack40CvMDERR4b5eFp3CreskpBs1Pig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA9L9SJUfAdOuM8+wB5e9/cME9gsVMu6sTd6Srky8xbXAiEA4e7pgDB24xOFR5t1TvB3B+CTGZ7VIaSIgV6Mczt66Bk="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "felix<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.0", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "~0.0.7"}, "gitHead": "8bb2526b074ba7da9ce430e4158b4b77dd89ecd8", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.0", "_shasum": "d451d5a5f94340b3480e49b38bfd1811ab1b2110", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.6.4", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "d451d5a5f94340b3480e49b38bfd1811ab1b2110", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.0.tgz", "integrity": "sha512-+cWz+97tOTM7FKdAWAIr4hViY2GJKjTDaXAv7Vh/bqP8ZHkZ9fIb5jEt0+mXsesS1i0LvL32z7xbALPl2VQeww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8qCt13NOpMHIycnfz7ZS6aRRUVGkHXZ/kMCag0Lr1CwIgGuWR0jPMDnNUSl3SUci3u5AC47Pse2sJToHyI8XTA/I="}]}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.1", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "^1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "gitHead": "69b8207c14d7ff95b7e69c1cc00b496ebdca0644", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.1", "_shasum": "d29988ca26d4a0fb85d384650ba8db5948d2f41b", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.6.4", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "d29988ca26d4a0fb85d384650ba8db5948d2f41b", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.1.tgz", "integrity": "sha512-JS5TtJcwW51TyE/kVEAuBi9Kl6XIdho4VEfqXjg7gMthb1/13TOauaX7ZkmtyVTb5WEZrhCCuw/wjgHyOyEgaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgjvlJLqSoAIN/7qUMxIVs5Fml3BJa6rfgiMkmRxSL7gIgM1sX4pj+G3WRgGeQ8Vgevqugj0+ogodiWVMUhiQRbvo="}]}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.2", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "^1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "gitHead": "e73d8849659fc4c382f952986d88e8842f345fa5", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.2", "_shasum": "6ae58023aef4deae6b0993948ab9a0d0af2b4b72", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.6.4", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "6ae58023aef4deae6b0993948ab9a0d0af2b4b72", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.2.tgz", "integrity": "sha512-7LwwHCXWJGfsfu7DiXuYoIgqnRSls4MKXzwQbchsvX7A/xPbU0qwNbBCMeocRLmHqJP7rTaFBUmRPgqh27s6Nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDKBg/3LxmF8IHOkSnmSBzsqQZ5wxkTm/qO+fjFha2a0AiBoJ/PTrrFajOSLXIMnApXvKHJD2kAbjEFQNrMniNrrQQ=="}]}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.3", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "^1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "gitHead": "f1a12682aed63acb3cd66857104202a7e7ca5565", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.3", "_shasum": "c224cc35d3cb98e25dead532472a18e8f75df5ab", "_from": ".", "_npmVersion": "2.10.0", "_nodeVersion": "2.0.1", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "c224cc35d3cb98e25dead532472a18e8f75df5ab", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.3.tgz", "integrity": "sha512-ti+lCx2J0cWkksupsP2xxjb9XgXVX+6Zgf+fsKqcbNHHPoEOZkPgwU4kziJtbPUrJLsrp1RWXA+u0a7KwcgmzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA5xSVClLVFyyKqdSrdym19zx49yGQPGib1pQo/mkQloAiEAzPbNTn5h5ouImQJpQwudh2/N+Wc38U9pP+Ka4PSf8ks="}]}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}], "directories": {}}, "1.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.4", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "^1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "gitHead": "ffea6d5444d708a4e457286cc80b546c81af4756", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.4", "_shasum": "f988793ed1a59c5e5c9e1e395c4207d409527c8b", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "f988793ed1a59c5e5c9e1e395c4207d409527c8b", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.4.tgz", "integrity": "sha512-BX6ms9qZiZnVTxIo+CLS8q9V3w5FGQJPb/3tuwEQWdtLd7FDC6F78A6k8zhJabHXyT3jffZhGA/1QaSYnAoATA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDmZyoEQ/ZJpzZxRio6X5XPt9C+bhpsUfVRcrq+xCDghAiEAg+Oe3Bvb+j7pBVf1lBUzoguVVHMpMVlikqZLH+ry2Og="}]}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}], "directories": {}}, "1.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.5", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "gitHead": "cfc7b815d090a109bcedb5bb0f6713148d55a6b7", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.5", "_shasum": "938370a57b4a51dea2c77c15d5c5fdf895164009", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "938370a57b4a51dea2c77c15d5c5fdf895164009", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.5.tgz", "integrity": "sha512-JgSRe4l4UzPwpJuxfcPWEK1SCrL4dxNjp1uqrQLMop3QZUVo+hDU8w9BJKA4JPbulTWI+UzrI2UA3tK12yQ6bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDde6BwEmWCIEGgqzh8iZultwaoQtv8iNP/BVzlgc5FBgIhALHdUQrliZKVGTZIPxm8mag/QxZru6a7cz9P11yBfO+n"}]}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}], "directories": {}}, "1.0.6-rc1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.6-rc1", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "readmeFilename": "Readme.md", "readme": "# combined-stream\n\nA stream that emits multiple other streams one after another.\n\n**NB** Currently `combined-stream` works with streams version 1 only. There is ongoing effort to switch this library to streams version 2. Any help is welcome. :) Meanwhile you can explore other libraries that provide streams2 support with more or less compatibility with `combined-stream`.\n\n- [combined-stream2](https://www.npmjs.com/package/combined-stream2): A drop-in streams2-compatible replacement for the combined-stream module.\n\n- [multistream](https://www.npmjs.com/package/multistream): A stream that emits multiple other streams one after another.\n\n## Installation\n\n``` bash\nnpm install combined-stream\n```\n\n## Usage\n\nHere is a simple example that shows how you can use combined-stream to combine\ntwo files into one:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create();\ncombinedStream.append(fs.createReadStream('file1.txt'));\ncombinedStream.append(fs.createReadStream('file2.txt'));\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\nWhile the example above works great, it will pause all source streams until\nthey are needed. If you don't want that to happen, you can set `pauseStreams`\nto `false`:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create({pauseStreams: false});\ncombinedStream.append(fs.createReadStream('file1.txt'));\ncombinedStream.append(fs.createReadStream('file2.txt'));\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\nHowever, what if you don't have all the source streams yet, or you don't want\nto allocate the resources (file descriptors, memory, etc.) for them right away?\nWell, in that case you can simply provide a callback that supplies the stream\nby calling a `next()` function:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create();\ncombinedStream.append(function(next) {\n  next(fs.createReadStream('file1.txt'));\n});\ncombinedStream.append(function(next) {\n  next(fs.createReadStream('file2.txt'));\n});\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\n## API\n\n### CombinedStream.create([options])\n\nReturns a new combined stream object. Available options are:\n\n* `maxDataSize`\n* `pauseStreams`\n\nThe effect of those options is described below.\n\n### combinedStream.pauseStreams = `true`\n\nWhether to apply back pressure to the underlaying streams. If set to `false`,\nthe underlaying streams will never be paused. If set to `true`, the\nunderlaying streams will be paused right after being appended, as well as when\n`delayedStream.pipe()` wants to throttle.\n\n### combinedStream.maxDataSize = `2 * 1024 * 1024`\n\nThe maximum amount of bytes (or characters) to buffer for all source streams.\nIf this value is exceeded, `combinedStream` emits an `'error'` event.\n\n### combinedStream.dataSize = `0`\n\nThe amount of bytes (or characters) currently buffered by `combinedStream`.\n\n### combinedStream.append(stream)\n\nAppends the given `stream` to the combinedStream object. If `pauseStreams` is\nset to `true, this stream will also be paused right away.\n\n`streams` can also be a function that takes one parameter called `next`. `next`\nis a function that must be invoked in order to provide the `next` stream, see\nexample above.\n\nRegardless of how the `stream` is appended, combined-stream always attaches an\n`'error'` listener to it, so you don't have to do that manually.\n\nSpecial case: `stream` can also be a String or Buffer.\n\n### combinedStream.write(data)\n\nYou should not call this, `combinedStream` takes care of piping the appended\nstreams into itself for you.\n\n### combinedStream.resume()\n\nCauses `combinedStream` to start drain the streams it manages. The function is\nidempotent, and also emits a `'resume'` event each time which usually goes to\nthe stream that is currently being drained.\n\n### combinedStream.pause();\n\nIf `combinedStream.pauseStreams` is set to `false`, this does nothing.\nOtherwise a `'pause'` event is emitted, this goes to the stream that is\ncurrently being drained, so you can use it to apply back pressure.\n\n### combinedStream.end();\n\nSets `combinedStream.writable` to false, emits an `'end'` event, and removes\nall streams from the queue.\n\n### combinedStream.destroy();\n\nSame as `combinedStream.end()`, except it emits a `'close'` event instead of\n`'end'`.\n\n## License\n\ncombined-stream is licensed under the MIT license.\n", "licenseText": "Copyright (c) 2011 Debuggable Limited <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "combined-stream@1.0.6-rc1", "dist": {"shasum": "aca80f422f9778ddc1dc05980838189e5b34b913", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.6-rc1.tgz", "fileCount": 7, "unpackedSize": 11074, "integrity": "sha512-OZGx7sV9JGMi4vmwkBYDE+Sm40kKoWSDnvrNh/iPRwYnZUjDpJiKIRi9bEW3F6p7uHcesA2HqEFhwiW09eQXOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyKUgQR7YGvGXx/rDvzSVuR75UahReNCqc2mlwkMr+XgIhAKIy/5fAY3Ty9+tkC5FwBxqwrgTg7G3A2jjedAowXyQc"}]}, "maintainers": [{"email": "<EMAIL>", "name": "alexindigo"}, {"email": "<EMAIL>", "name": "apechimp"}, {"email": "<EMAIL>", "name": "celer"}, {"email": "<EMAIL>", "name": "felix<PERSON>"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/combined-stream_1.0.6-rc1_1518453222275_0.3377146156635127"}, "_hasShrinkwrap": false}, "1.0.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.6", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "licenseText": "Copyright (c) 2011 Debuggable Limited <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "combined-stream@1.0.6", "dist": {"shasum": "723e7df6e801ac5613113a7e445a9b69cb632818", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.6.tgz", "fileCount": 7, "unpackedSize": 11070, "integrity": "sha512-cN6NJ9NnPLDiP/CpmVC1knLFqNjD9Hi1vPsacL/WQP3v8cqVbZpbpX6NHmYJo2fR4B80CgE4cEgPWiDauAQzPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6r5pwOjiudP2KIFrRgwCudBh59ozLpQYV6qRe8fku2gIhAKHIX3r8LRoYNGdlcYdMreWtwsFPzi7w51DI0JY5zUHK"}]}, "maintainers": [{"email": "<EMAIL>", "name": "alexindigo"}, {"email": "<EMAIL>", "name": "apechimp"}, {"email": "<EMAIL>", "name": "celer"}, {"email": "<EMAIL>", "name": "felix<PERSON>"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/combined-stream_1.0.6_1518539465161_0.17330394935691018"}, "_hasShrinkwrap": false}, "1.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.7", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "licenseText": "Copyright (c) 2011 Debuggable Limited <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "combined-stream@1.0.7", "dist": {"shasum": "2d1d24317afb8abe95d6d2c0b07b57813539d828", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.7.tgz", "integrity": "sha512-brWl9y6vOB1xYPZcpZde3N9zDByXTosAeMDo4p1wzo6UMOX4vumB+TP1RZ76sfE6Md68Q0NJSrE/gbezd4Ul+w==", "fileCount": 7, "unpackedSize": 11070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboSiYCRA9TVsSAnZWagAA6nEP/14/QA43Q5Z7qBrSHpgY\nV6qK0agm+VHkkzPp7rIhYPMY4NxQWusR/rOazG7ynVrLwjZFqF3Pwk8EkiYz\nYy883nRP5LDTNzyP7u7PrNoXrxWnQQsoKxaUuxqhVRzLf+H+tvjBc/rA0iWO\nPeTyBKQ/41g+TNh82/ArNxDwS8WL33TJzM0QwvxgVpPS2xan4LFCsaAn4j5z\nTZhbHynupTnAcU7AFw7CBRGooWKByG0OtHg+eLh738aFz12aaMhvjmANp3ml\nSXQx7V72aACZiBsnSKlXEyhuaqRSSN2Pw9tmzau5rNzTPYL+CG06vDQm/gVy\nYx/pyrTAXmcsoKWb2wXnez7+F5AHxIqF/ucl+gG/FOa6GQ17PmYZ4d2Tdfog\nC4Gpn8ShW0zGR6AsyqaphSpVcN1oe0cJL1UAkekeJZTYhw2p2FxVz6crLBDd\nSfRVYRFc4bZLu2QUbKaTaqCkzOSH5oAGCk56F69IMQqV4Qd8XCjRCaFb67WO\n3kGdYRUv/YAJEKrgVaKPhZnwkfLMp2FtxPiD3D1dDkGcgeY26xsotZUyryw2\nJBCttUb1Qn019cZJJ+ljQGkODuZDJ6Z/ZNvPh0EqTkknYFbT/fWlcDtrSeXo\n5Usn3HEkeXxEgRiO9VvVj24Ak0F9Y7O1iuxmPo5YJSUKdyanbox3vQ5dUnzE\nDCR6\r\n=saAR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCr+pVHPpC/2uL2XI/FlczJc1xTH1gjXQtxDJ4rbJY0cQIhAJS6DZyD1qi7smrru2Yqr3gOt3MxeU0LkDbDHRK5bV76"}]}, "maintainers": [{"email": "<EMAIL>", "name": "alexindigo"}, {"email": "<EMAIL>", "name": "apechimp"}, {"email": "<EMAIL>", "name": "celer"}, {"email": "<EMAIL>", "name": "felix<PERSON>"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/combined-stream_1.0.7_1537288343882_0.48309285138952385"}, "_hasShrinkwrap": false}, "1.0.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "1.0.8", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "~1.0.0"}, "devDependencies": {"far": "~0.0.7"}, "license": "MIT", "gitHead": "5298bece5aba2cf4d2f5ec84c4fc128572c41cee", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}, "_id": "combined-stream@1.0.8", "_nodeVersion": "11.10.1", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "shasum": "c3d45a8b34fd730631a110a8a2520682b31d5a7f", "tarball": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "fileCount": 5, "unpackedSize": 11514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2Fy6CRA9TVsSAnZWagAATdgP/jWKhLQFryvRCwoCinwZ\ndUb4hAUSB6aXMAVQWXTkK1udLiIz/LqmbcaUJQEQ3pNaDWWGrDAu9g33CmE2\nnu0spvkbANFO+ofHjP6Iz/ksSxeN4q/w24dcbMoBR8tuLZ+D0W9AgS15a4EF\n3cl13keIvZumC7292r8L8uHgx/1zHqCNBCtEIfIiaq1EsI8mZnfRS+K5mVdV\n3qo0Aqp1lOpfrVOG4dejc+HvN1bbSs6nz/BUilqtF2NmUqxkkrIGBf28DJck\nY7LVMjPRGHnXJkeAHkiLt0Mb6R2nrkBEnhuhxVkTuHVHkP+QRmlIN6ZEOVri\n2FFEPUVLrW/qb3PLUUG6EZLsHwaLX/g1j5Jsl+WdlC4vhSpzfMui+VBYcaG/\ncUMa+i9qKWIBwFMDzcqB/tU1oDFSfeeHw9K4FCnpRUl1nFfww0hqkMMxfXYu\nLrZ6TrbZpmmheUyzm8f5xLVOK4HML3HYgiww3kQ5c/AXMFvGojC7HV0xuiKU\nY9SBRF8Gv2t07GD/nAOj/dF6UlyVWCiToHRlLeHVikpqbgW4DeZ3VWmjtFTY\noU9YhCMxLD9nqkJ5BXVP31y6aFsUDghdD+ovWTZZoDWx00za4RwBXfm2LApQ\nuCw57cAw5NIbTTV3xY9SUnYm0ulV7bxSam6gslwAk4CuQCgyIYeKbkil48jB\nxgxh\r\n=Gn9i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICHIvXrvlC143mKkbH000Cz0z37Evin0hKgTP+16b7hcAiAop4YDmR7pPHPu0uFc+OKWghshbne6mXZ5OYAZ8fq44w=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "alexindigo"}, {"email": "<EMAIL>", "name": "apechimp"}, {"email": "<EMAIL>", "name": "celer"}, {"email": "<EMAIL>", "name": "felix<PERSON>"}], "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/combined-stream_1.0.8_1557683385167_0.16390415386060164"}, "_hasShrinkwrap": false}}, "maintainers": [{"email": "<EMAIL>", "name": "alexindigo"}, {"email": "<EMAIL>", "name": "apechimp"}, {"email": "<EMAIL>", "name": "celer"}, {"email": "<EMAIL>", "name": "felix<PERSON>"}], "time": {"modified": "2022-06-13T06:33:58.025Z", "created": "2011-05-24T06:36:14.531Z", "0.0.0": "2011-05-24T06:36:15.190Z", "0.0.1": "2011-05-24T09:46:38.486Z", "0.0.6": "2011-05-27T13:43:57.926Z", "0.0.2": "2011-05-27T13:46:53.900Z", "0.0.3": "2011-05-28T13:49:43.181Z", "0.0.4": "2013-02-02T18:29:01.654Z", "0.0.5": "2014-06-24T08:39:18.882Z", "0.0.7": "2014-10-31T10:18:38.784Z", "1.0.0": "2015-05-04T21:29:45.965Z", "1.0.1": "2015-05-05T01:13:50.245Z", "1.0.2": "2015-05-09T14:25:06.378Z", "1.0.3": "2015-05-19T12:43:10.026Z", "1.0.4": "2015-06-14T15:09:18.379Z", "1.0.5": "2015-06-15T03:19:17.202Z", "1.0.6-rc1": "2018-02-12T16:33:42.335Z", "1.0.6": "2018-02-13T16:31:05.240Z", "1.0.7": "2018-09-18T16:32:24.107Z", "1.0.8": "2019-05-12T17:49:45.337Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "users": {"tunnckocore": true, "52u": true, "wisecolt": true, "anoubis": true, "nickeljew": true, "zousandian": true, "arefm": true, "ganeshkbhat": true, "zhenguo.zhao": true}, "homepage": "https://github.com/felixge/node-combined-stream", "readme": "# combined-stream\n\nA stream that emits multiple other streams one after another.\n\n**NB** Currently `combined-stream` works with streams version 1 only. There is ongoing effort to switch this library to streams version 2. Any help is welcome. :) Meanwhile you can explore other libraries that provide streams2 support with more or less compatibility with `combined-stream`.\n\n- [combined-stream2](https://www.npmjs.com/package/combined-stream2): A drop-in streams2-compatible replacement for the combined-stream module.\n\n- [multistream](https://www.npmjs.com/package/multistream): A stream that emits multiple other streams one after another.\n\n## Installation\n\n``` bash\nnpm install combined-stream\n```\n\n## Usage\n\nHere is a simple example that shows how you can use combined-stream to combine\ntwo files into one:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create();\ncombinedStream.append(fs.createReadStream('file1.txt'));\ncombinedStream.append(fs.createReadStream('file2.txt'));\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\nWhile the example above works great, it will pause all source streams until\nthey are needed. If you don't want that to happen, you can set `pauseStreams`\nto `false`:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create({pauseStreams: false});\ncombinedStream.append(fs.createReadStream('file1.txt'));\ncombinedStream.append(fs.createReadStream('file2.txt'));\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\nHowever, what if you don't have all the source streams yet, or you don't want\nto allocate the resources (file descriptors, memory, etc.) for them right away?\nWell, in that case you can simply provide a callback that supplies the stream\nby calling a `next()` function:\n\n``` javascript\nvar CombinedStream = require('combined-stream');\nvar fs = require('fs');\n\nvar combinedStream = CombinedStream.create();\ncombinedStream.append(function(next) {\n  next(fs.createReadStream('file1.txt'));\n});\ncombinedStream.append(function(next) {\n  next(fs.createReadStream('file2.txt'));\n});\n\ncombinedStream.pipe(fs.createWriteStream('combined.txt'));\n```\n\n## API\n\n### CombinedStream.create([options])\n\nReturns a new combined stream object. Available options are:\n\n* `maxDataSize`\n* `pauseStreams`\n\nThe effect of those options is described below.\n\n### combinedStream.pauseStreams = `true`\n\nWhether to apply back pressure to the underlaying streams. If set to `false`,\nthe underlaying streams will never be paused. If set to `true`, the\nunderlaying streams will be paused right after being appended, as well as when\n`delayedStream.pipe()` wants to throttle.\n\n### combinedStream.maxDataSize = `2 * 1024 * 1024`\n\nThe maximum amount of bytes (or characters) to buffer for all source streams.\nIf this value is exceeded, `combinedStream` emits an `'error'` event.\n\n### combinedStream.dataSize = `0`\n\nThe amount of bytes (or characters) currently buffered by `combinedStream`.\n\n### combinedStream.append(stream)\n\nAppends the given `stream` to the combinedStream object. If `pauseStreams` is\nset to `true, this stream will also be paused right away.\n\n`streams` can also be a function that takes one parameter called `next`. `next`\nis a function that must be invoked in order to provide the `next` stream, see\nexample above.\n\nRegardless of how the `stream` is appended, combined-stream always attaches an\n`'error'` listener to it, so you don't have to do that manually.\n\nSpecial case: `stream` can also be a String or Buffer.\n\n### combinedStream.write(data)\n\nYou should not call this, `combinedStream` takes care of piping the appended\nstreams into itself for you.\n\n### combinedStream.resume()\n\nCauses `combinedStream` to start drain the streams it manages. The function is\nidempotent, and also emits a `'resume'` event each time which usually goes to\nthe stream that is currently being drained.\n\n### combinedStream.pause();\n\nIf `combinedStream.pauseStreams` is set to `false`, this does nothing.\nOtherwise a `'pause'` event is emitted, this goes to the stream that is\ncurrently being drained, so you can use it to apply back pressure.\n\n### combinedStream.end();\n\nSets `combinedStream.writable` to false, emits an `'end'` event, and removes\nall streams from the queue.\n\n### combinedStream.destroy();\n\nSame as `combinedStream.end()`, except it emits a `'close'` event instead of\n`'end'`.\n\n## License\n\ncombined-stream is licensed under the MIT license.\n", "readmeFilename": "Readme.md", "license": "MIT", "bugs": {"url": "https://github.com/felixge/node-combined-stream/issues"}}