<!DOCTYPE html>
<html lang="zh - CN">

<head>
    <meta charset="UTF - 8">
    <meta name="viewport" content="width = device - width, initial - scale = 1.0">
    <title>我的学习内容及个人介绍</title>
    <link rel="stylesheet" href="../css/page1.css">
</head>

<body>
    <header>
        <nav>
            <ul>
                <li><a href="../html/index.html">首页</a></li>
                <li><a href="../html/page1.html">我的学习内容</a></li>
                <li><a href="../html/page2.html">放松</a></li>
                <!-- <li><a href="../html/page3.html">待</a></li> -->
                <li><a href="../html/page4.html">常用页面跳转</a></li>
            </ul>
        </nav>
    </header>
    <div class="wrapper">
        <h1>个人介绍</h1>
        <p>目前是长江大学大三生</p>
        <p>大一积极参加各种社团，成为了硬件部的部长，学习了各种电脑的硬件知识</p>
        <p>大二开始走c++软件类算法竞赛，在蓝桥杯仅仅获得了省三</p>
        <p>大三开始学前端和后端知识，以后做软件</p>
        <h1>前端技术栈</h1>
        <p>我学习了多种前端技术栈，包括：</p>
        <p>html，它是构建网页的基础标记语言。</p>
        <p>css，用于为网页添加样式，使页面更加美观和易于阅读。</p>
        <p>js可以为网页添加交互功能，让网页更加生动和具有动态效果。</p>
        <p>目前在用js做一些小项目，巩固基础</p>
        <h1>后端技术栈</h1>
        <p>目前在学习java</p>
        <div class="right-link">
            <a href="../学习内容/js.html">点我查看我的笔记</a>
        </div>
    </div>
    <script>
        // 获取.right-link元素，也就是包含a标签的那个盒子
        const rightLinkBox = document.querySelector('.right-link');

        // 给这个盒子添加点击事件监听器
        rightLinkBox.addEventListener('click', function () {
            // 获取a标签的href属性值，也就是要跳转的链接地址
            const hrefValue = document.querySelector('.right-link a').getAttribute('href');

            // 使用window.location.href进行页面跳转
            window.location.href = hrefValue;
        });
    </script>
</body>

</html>