import { router } from '@kit.ArkUI';

interface CityWeather {
  name: string;
  weather: string;
  tempRange: string;
  currentTemp: number;
}

@Entry
@Component
struct CityManage {
  @State searchText: string = '';
  @State cities: CityWeather[] = [
    { name: "恩施土家族苗族自治州", weather: "多云", tempRange: "22°C ~ 31°C", currentTemp: 29 },
    { name: "宜昌市", weather: "小雨", tempRange: "23°C ~ 28°C", currentTemp: 28 },
    { name: "黄冈市", weather: "大雨", tempRange: "24°C ~ 28°C", currentTemp: 24 },
    { name: "孝感市", weather: "小雨", tempRange: "24°C ~ 28°C", currentTemp: 25 }
  ];

  build() {
    Column() {
      // 标题栏
      Row() {
        Image($r("app.media.back_arrow"))
          .width(24)
          .height(24)
          .onClick(() => {
            router.back();
          })
        
        Text("城市管理")
          .fontSize(18)
          .fontWeight(500)
          .margin({ left: 15 })
        
        Blank()
        
        Text("添加")
          .fontSize(16)
          .fontColor("#4A90E2")
          .onClick(() => {
            router.pushUrl({ url: 'pages/LocationSelect' });
          })
      }
      .width('100%')
      .padding({ left: 20, right: 20, top: 20, bottom: 20 })

      // 搜索框
      Row() {
        Image($r("app.media.search_icon"))
          .width(16)
          .height(16)
          .fillColor("#999999")
          .margin({ right: 8 })
        
        TextInput({ placeholder: "请输入城市名称" })
          .layoutWeight(1)
          .backgroundColor("transparent")
          .border({ width: 0 })
          .onChange((value) => { this.searchText = value })
      }
      .width('90%')
      .height(40)
      .backgroundColor("#F5F5F5")
      .borderRadius(20)
      .padding({ left: 15, right: 15 })
      .margin({ bottom: 20 })

      // 城市列表
      List({ space: 10 }) {
        ForEach(this.cities, (city: CityWeather, index: number) => {
          ListItem() {
            this.cityWeatherCard(city)
          }
        })
      }
      .width('100%')
      .layoutWeight(1)
      .padding({ left: 20, right: 20 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor("#F8F9FA")
  }

  @Builder
  cityWeatherCard(city: CityWeather) {
    Row() {
      Column() {
        Text(city.name)
          .fontSize(16)
          .fontWeight(500)
          .fontColor("#FFFFFF")
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
        
        Text(city.weather)
          .fontSize(14)
          .fontColor("#FFFFFF")
          .opacity(0.8)
          .margin({ top: 5 })
        
        Text(city.tempRange)
          .fontSize(12)
          .fontColor("#FFFFFF")
          .opacity(0.7)
          .margin({ top: 5 })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      Text(`${city.currentTemp}`)
        .fontSize(48)
        .fontWeight(300)
        .fontColor("#FFFFFF")
    }
    .width('100%')
    .height(100)
    .backgroundColor(this.getCityCardColor(city.currentTemp))
    .borderRadius(12)
    .padding({ left: 20, right: 20 })
    .justifyContent(FlexAlign.SpaceBetween)
    .onClick(() => {
      router.back();
    })
  }

  getCityCardColor(temp: number): string {
    if (temp >= 30) return "#FF6B6B";
    if (temp >= 25) return "#4ECDC4";
    if (temp >= 20) return "#45B7D1";
    return "#96CEB4";
  }
}