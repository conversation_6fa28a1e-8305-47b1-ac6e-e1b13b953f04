<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moonlight night</title>
    <link rel="stylesheet" href="2.css">
</head>

<body>
    <main>
        <section>
            <img src="bg.jpg" id="bg">
            <img src="moon.png" id="moon">
            <img src="mountain.png" id="mountain">
            <img src="road.png" id="road">
            <h2 id="text">Moon light</h2>
        </section>
    </main>
    <script>
        let bg = document.getElementById('bg')
        let moon = document.getElementById('moon')
        let road = document.getElementById('road')
        let mountain = document.getElementById('mountain')
        let text = document.getElementById('text')
        window.addEventListener('scroll', function () {
            let value = window.scrollY;
            bg.style.top = value + 0.5 + 'px';
            moon.style.left = -value + 0.5 + 'px';
            road.style.top = value * 0.15 + 'px';
            text.style.top = value * 1 + 0.5 + 'px';
        })
    </script>
</body>

</html>