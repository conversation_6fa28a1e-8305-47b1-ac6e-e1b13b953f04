import java.io.*;
import java.util.*;

class Instruction {
    enum Type {
        CALC, INPUT, OUTPUT, WAIT, HALT
    }

    Type type;
    int runtime;

    public Instruction(Type type, int runtime) {
        this.type = type;
        this.runtime = runtime;
    }
}

import javafx.application.Application;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.application.Platform;

class PCB {
    String processName;
    Queue<Instruction> instructions;
    int remainingTime;
    Instruction.Type currentState;

    public PCB(String processName) {
        this.processName = processName;
        this.instructions = new LinkedList<>();
        this.remainingTime = 0;
        this.currentState = null;
    }

    public void addInstruction(Instruction instruction) {
        instructions.add(instruction);
    }

    public void setCurrentState(Instruction.Type state) {
        this.currentState = state;
    }

    public Instruction.Type getCurrentState() {
        return currentState;
    }
}

public class RoundRobinScheduler extends Application {
    private Queue<PCB> readyQueue = new LinkedList<>();
    private Queue<PCB> inputQueue = new LinkedList<>();
    private Queue<PCB> outputQueue = new LinkedList<>();
    private Queue<PCB> waitQueue = new LinkedList<>();
    private int timeSlice;
    private TableView<PCB> processTable;
    private TextArea logArea;

    public RoundRobinScheduler(int timeSlice) {
        this.timeSlice = timeSlice;
    }

    public void loadProcesses(String fileName) throws IOException {
        BufferedReader reader = new BufferedReader(new FileReader(fileName));
        String line;
        PCB currentPCB = null;

        while ((line = reader.readLine()) != null) {
            if (line.startsWith("P")) {
                if (currentPCB != null) {
                    readyQueue.add(currentPCB);
                }
                currentPCB = new PCB(line.trim());
            } else if (currentPCB != null) {
                char type = line.charAt(0);
                int runtime = Integer.parseInt(line.substring(1).trim());
                Instruction.Type instructionType = switch (type) {
                    case 'C' -> Instruction.Type.CALC;
                    case 'I' -> Instruction.Type.INPUT;
                    case 'O' -> Instruction.Type.OUTPUT;
                    case 'W' -> Instruction.Type.WAIT;
                    case 'H' -> Instruction.Type.HALT;
                    default -> throw new IllegalArgumentException("Unknown instruction type: " + type);
                };
                currentPCB.addInstruction(new Instruction(instructionType, runtime));
            }
        }
        if (currentPCB != null) {
            readyQueue.add(currentPCB);
        }
        reader.close();
    }

    public void runScheduler() throws IOException {
        BufferedWriter logWriter = new BufferedWriter(new FileWriter("F:\\front\\java\\scheduler_log.txt"));

        while (!readyQueue.isEmpty() || !inputQueue.isEmpty() || !outputQueue.isEmpty() || !waitQueue.isEmpty()) {
            PCB currentPCB = readyQueue.poll();
            if (currentPCB != null) {
                Instruction currentInstruction = currentPCB.instructions.peek();
                if (currentInstruction != null) {
                    // 检查进程状态
                    if (currentPCB.getCurrentState() != null) {
                        logWriter.write("Process " + currentPCB.processName + " is already in "
                                + currentPCB.getCurrentState() + " state. Skipping.\n");
                        continue;
                    }
                    logWriter.write("Running process: " + currentPCB.processName + ", Instruction: "
                            + currentInstruction.type + "\n");
                    if (currentInstruction.type == Instruction.Type.CALC) {
                        int executionTime = Math.min(currentInstruction.runtime, timeSlice);
                        currentInstruction.runtime -= executionTime;
                        if (currentInstruction.runtime <= 0) {
                            currentPCB.instructions.poll();
                        }
                        if (!currentPCB.instructions.isEmpty()) {
                            currentPCB.setCurrentState(Instruction.Type.CALC);
                            readyQueue.add(currentPCB);
                        }
                    } else {
                        currentPCB.instructions.poll();
                        currentPCB.setCurrentState(currentInstruction.type);
                        switch (currentInstruction.type) {
                            case INPUT -> {
                                inputQueue.add(currentPCB);
                            }
                            case OUTPUT -> {
                                outputQueue.add(currentPCB);
                            }
                            case WAIT -> {
                                waitQueue.add(currentPCB);
                            }
                            case HALT -> {
                                logWriter.write("Process " + currentPCB.processName + " completed.\n");
                            }
                        }
                    }
                }
            }
            processWaitQueues();
        }
        logWriter.close();
    }

    private void processWaitQueues() {
        moveQueue(inputQueue, readyQueue);
        moveQueue(outputQueue, readyQueue);
        moveQueue(waitQueue, readyQueue);
    }

    private void moveQueue(Queue<PCB> source, Queue<PCB> destination) {
        while (!source.isEmpty()) {
            destination.add(source.poll());
        }
    }

    @Override
    public void start(Stage primaryStage) {
        // 创建表格
        processTable = new TableView<>();
        TableColumn<PCB, String> nameCol = new TableColumn<>("进程名");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("processName"));

        TableColumn<PCB, String> stateCol = new TableColumn<>("状态");
        stateCol.setCellValueFactory(new PropertyValueFactory<>("currentState"));

        processTable.getColumns().addAll(nameCol, stateCol);

        // 创建日志区域
        logArea = new TextArea();
        logArea.setEditable(false);

        // 创建按钮
        Button startButton = new Button("开始调度");
        startButton.setOnAction(e -> {
            try {
                loadProcesses("F:\\front\\java\\prc.txt");
                new Thread(this::runScheduler).start();
            } catch (IOException ex) {
                logArea.appendText("错误: " + ex.getMessage() + "\n");
            }
        });

        // 布局
        VBox root = new VBox(10);
        root.getChildren().addAll(processTable, logArea, startButton);

        // 场景
        Scene scene = new Scene(root, 600, 400);

        // 舞台
        primaryStage.setTitle("Round Robin 调度器");
        primaryStage.setScene(scene);
        primaryStage.show();
    }

    public void runScheduler() {
        ObservableList<PCB> processList = FXCollections.observableArrayList(readyQueue);
        Platform.runLater(() -> processTable.setItems(processList));

        while (!readyQueue.isEmpty() || !inputQueue.isEmpty() || !outputQueue.isEmpty() || !waitQueue.isEmpty()) {
            PCB currentPCB = readyQueue.poll();
            if (currentPCB != null) {
                Instruction currentInstruction = currentPCB.instructions.peek();
                if (currentInstruction != null) {
                    // 检查进程状态
                    if (currentPCB.getCurrentState() != null) {
                        Platform.runLater(() -> {
                            logArea.appendText("Process " + currentPCB.processName + " is already in "
                                    + currentPCB.getCurrentState() + " state. Skipping.\n");
                        });
                        continue;
                    }
                    currentPCB.setCurrentState(currentInstruction.type);
                    Platform.runLater(() -> {
                        processTable.refresh();
                        logArea.appendText("运行进程: " + currentPCB.processName +
                                ", 指令: " + currentInstruction.type + "\n");
                    });

                    if (currentInstruction.type == Instruction.Type.CALC) {
                        int executionTime = Math.min(currentInstruction.runtime, timeSlice);
                        currentInstruction.runtime -= executionTime;
                        if (currentInstruction.runtime <= 0) {
                            currentPCB.instructions.poll();
                        }
                        if (!currentPCB.instructions.isEmpty()) {
                            readyQueue.add(currentPCB);
                        }
                    } else {
                        currentPCB.instructions.poll();
                        switch (currentInstruction.type) {
                            case INPUT -> inputQueue.add(currentPCB);
                            case OUTPUT -> outputQueue.add(currentPCB);
                            case WAIT -> waitQueue.add(currentPCB);
                            case HALT ->
                                Platform.runLater(() -> logArea.appendText("进程 " + currentPCB.processName + " 已完成.\n"));
                        }
                    }
                }
            }
            processWaitQueues();
            try {
                Thread.sleep(500); // 添加延迟以便观察调度过程
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {
        launch(args);
    }
}