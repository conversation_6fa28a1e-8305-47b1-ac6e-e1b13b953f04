# 1.指令周期的数据流：分析指令时间很快，应该是忽略了

![image-20241114150739669](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241114150739669.png)

# 2.指令周期的流程

![image-20241114150925578](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241114150925578.png)

## 3.cpu区分的方式：通过四个触发器

![image-20241114151116193](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241114151116193.png)

## 4.取值周期的具体情况

![image-20241114151345873](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241114151345873.png)

## 5.间址周期的情况

![image-20241114151850649](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241114151850649.png)

## 6.中断周期

![image-20241114152620217](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241114152620217.png)

![image-20241114152952280](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241114152952280.png)