# 使用官方 Node.js 运行时作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY src/ ./src/

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S weather -u 1001

# 创建日志目录
RUN mkdir -p /app/logs && chown -R weather:nodejs /app/logs

# 切换到非 root 用户
USER weather

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
CMD ["node", "src/app.js"]
