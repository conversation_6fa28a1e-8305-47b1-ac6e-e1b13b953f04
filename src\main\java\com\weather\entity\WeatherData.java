package com.weather.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WeatherData {
    private int id;
    @JsonProperty("cityId")
    private int cityId;
    @JsonProperty("cityName")
    private String cityName;
    @JsonProperty("temperature")
    private int temperature;
    @JsonProperty("weatherDesc")
    private String weatherDesc;
    @JsonProperty("humidity")
    private int humidity;
    @JsonProperty("windSpeed")
    private double windSpeed;
    @JsonProperty("updateTime")
    private String updateTime;
    
    public WeatherData() {}
    
    public WeatherData(String cityName, int temperature, String weatherDesc) {
        this.cityName = cityName;
        this.temperature = temperature;
        this.weatherDesc = weatherDesc;
    }
    
    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public int getCityId() { return cityId; }
    public void setCityId(int cityId) { this.cityId = cityId; }
    
    public String getCityName() { return cityName; }
    public void setCityName(String cityName) { this.cityName = cityName; }
    
    public int getTemperature() { return temperature; }
    public void setTemperature(int temperature) { this.temperature = temperature; }
    
    public String getWeatherDesc() { return weatherDesc; }
    public void setWeatherDesc(String weatherDesc) { this.weatherDesc = weatherDesc; }
    
    public int getHumidity() { return humidity; }
    public void setHumidity(int humidity) { this.humidity = humidity; }
    
    public double getWindSpeed() { return windSpeed; }
    public void setWindSpeed(double windSpeed) { this.windSpeed = windSpeed; }
    
    public String getUpdateTime() { return updateTime; }
    public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }
}
