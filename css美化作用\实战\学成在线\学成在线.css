* {
    margin: 0;
    padding: 0;
}

.w {
    width: 1200px;
    margin: auto;
}

.header {
    height: 90px;
    /* background-color: aqua; */
    margin: 30px auto;
}

.logo {
    float: left;
    width: 198px;
    height: 90px;
    /* background-color: pink; */
}

li {
    list-style: none;
}

.nav {
    float: left;
    margin-left: 40px;
}

.nav ul li {
    float: left;
    margin: 0 20px;
}

a {
    text-decoration: none;
}

.nav ul li a {
    display: block;
    height: 90px;
    padding: 0 10px;
    line-height: 90px;
    font-size: 18px;
    color: #050505;
}

/* 要注意样式权重 */
.nav ul li a:hover {
    border-bottom: 1px solid skyblue;
}

/* search搜索框 */
.search {
    float: left;
    width: 415px;
    height: 88px;
    line-height: 90px;
    display: flex; /* 将search容器设置为弹性布局 */
    align-items: center; /* 垂直方向居中 */
}

.search input {
    border: 1px solid skyblue;
    border-right: 0;
    width: 360px;
    height: 38px;
    font-size: 14px;
    color: antiquewhite;
    padding-left: 10px; /* 添加输入框内文字的左侧内边距，让输入的文字不会紧贴边框 */
    box-sizing: border-box; /* 让输入框的宽度包含边框和内边距，避免因添加内边距导致总宽度超出预期 */
}

.search button {
    width: 55px; /* 给按钮设置一个合理的宽度，这里假设为55px，可根据实际需求调整 */
    height: 38px; /* 让按钮高度和输入框高度一致，使整体更美观 */
    border: 1px solid skyblue;
    background-color: skyblue; /* 设置按钮背景色 */
    cursor: pointer; /* 鼠标悬停时显示手型指针，表示可点击 */
    margin-left: -5px; /* 将按钮往左移动5px，使其与输入框的右边框贴合，看起来更紧凑 */
    background-image: url('imges/search.png'); /* 设置按钮的背景图片，这里假设你的图片路径是正确的，可根据实际情况修改 */
    background-repeat: no-repeat; /* 不重复显示背景图片 */
    background-position: center; /* 让背景图片在按钮中心显示 */
    box-sizing: border-box; /* 让按钮的宽度包含边框，避免因边框导致布局错乱 */
}