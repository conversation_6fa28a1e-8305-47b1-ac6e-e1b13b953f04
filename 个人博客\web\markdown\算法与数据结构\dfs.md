```c++
#include <iostream>
#include <cstring>
#include <algorithm>

using namespace std;

const int N = 10;
int n , path[N];
bool st[N]; // 状态数组

void dfs(int u ) // 第几个数字，一共几个数字
{
    if(u == n)// 递归到最后一个数字
    {
        for (int i = 0; i < n; i ++ ) cout << path[i] << ' '; // 输出保存的结果
        puts(" ");
    }

    for (int i = 1; i <= n; i ++ )
        if (!st[i]) // 没有被用过的数
        {
            path[u] = i ;
            st[i] = true; // i被用过
            dfs(u + 1);// 走到下一层
            st[i] = false;// 恢复现场
        }
}

int main()
{

    cin >> n;
    dfs(0);
    return 0;
}

作者：yxc
链接：https://www.acwing.com/video/274/
来源：AcWing
著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。
```

