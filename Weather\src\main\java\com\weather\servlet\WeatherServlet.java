package com.weather.servlet;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.weather.entity.City;
import com.weather.entity.WeatherData;
import com.weather.util.DBUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

@WebServlet("/api/weather/*")
public class WeatherServlet extends HttpServlet {
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) 
            throws ServletException, IOException {
        
        resp.setContentType("application/json;charset=UTF-8");
        resp.setHeader("Access-Control-Allow-Origin", "*");
        
        String pathInfo = req.getPathInfo();
        
        try {
            if ("/cities".equals(pathInfo)) {
                getCities(resp);
            } else if (pathInfo != null && pathInfo.startsWith("/city/")) {
                String cityName = pathInfo.substring(6);
                getCityWeather(cityName, resp);
            } else {
                getAllWeather(resp);
            }
        } catch (Exception e) {
            resp.setStatus(500);
            resp.getWriter().write("{\"error\":\"" + e.getMessage() + "\"}");
        }
    }
    
    private void getCities(HttpServletResponse resp) throws Exception {
        String sql = "SELECT city_name, province, is_hot FROM cities ORDER BY is_hot DESC";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            List<City> cities = new ArrayList<>();
            while (rs.next()) {
                City city = new City();
                city.setCityName(rs.getString("city_name"));
                city.setProvince(rs.getString("province"));
                city.setHot(rs.getBoolean("is_hot"));
                cities.add(city);
            }
            
            resp.getWriter().write(objectMapper.writeValueAsString(cities));
        }
    }
    
    private void getCityWeather(String cityName, HttpServletResponse resp) throws Exception {
        String sql = "SELECT w.*, c.city_name FROM weather_data w " +
                    "JOIN cities c ON w.city_id = c.id WHERE c.city_name = ?";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, cityName);
            ResultSet rs = stmt.executeQuery();
            
            if (rs.next()) {
                WeatherData weather = new WeatherData();
                weather.setId(rs.getInt("id"));
                weather.setCityId(rs.getInt("city_id"));
                weather.setCityName(rs.getString("city_name"));
                weather.setTemperature(rs.getInt("temperature"));
                weather.setWeatherDesc(rs.getString("weather_desc"));
                weather.setHumidity(rs.getInt("humidity"));
                weather.setWindSpeed(rs.getDouble("wind_speed"));
                weather.setUpdateTime(rs.getString("update_time"));
                
                resp.getWriter().write(objectMapper.writeValueAsString(weather));
            } else {
                resp.setStatus(404);
                resp.getWriter().write("{\"error\":\"City not found\"}");
            }
        }
    }
    
    private void getAllWeather(HttpServletResponse resp) throws Exception {
        String sql = "SELECT w.*, c.city_name FROM weather_data w " +
                    "JOIN cities c ON w.city_id = c.id";
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            List<WeatherData> weatherList = new ArrayList<>();
            while (rs.next()) {
                WeatherData weather = new WeatherData();
                weather.setId(rs.getInt("id"));
                weather.setCityId(rs.getInt("city_id"));
                weather.setCityName(rs.getString("city_name"));
                weather.setTemperature(rs.getInt("temperature"));
                weather.setWeatherDesc(rs.getString("weather_desc"));
                weather.setHumidity(rs.getInt("humidity"));
                weather.setWindSpeed(rs.getDouble("wind_speed"));
                weather.setUpdateTime(rs.getString("update_time"));
                weatherList.add(weather);
            }
            
            resp.getWriter().write(objectMapper.writeValueAsString(weatherList));
        }
    }
}


