import { router } from '@kit.ArkUI';

@Entry
@Component
struct LocationSelect {
  @State searchText: string = '';
  @State selectedCity: string = '';
  @State showConfirmDialog: boolean = false;
  @State hotCities: string[] = ['北京', '上海', '广州', '深圳'];

  build() {
    Column() {
      // 标题栏 - 居中显示
      Row() {
        Image($r("app.media.location_icon"))
          .width(20)
          .height(20)
          .fillColor("#4A90E2")
        
        Text("选择地区")
          .fontSize(18)
          .fontWeight(500)
          .fontColor("#333333")
          .margin({ left: 8 })
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .padding({ top: 20, bottom: 30 })

      // 搜索框
      Row() {
        Image($r("app.media.search_icon"))
          .width(16)
          .height(16)
          .fillColor("#999999")
          .margin({ right: 8 })
        
        TextInput({ placeholder: "搜索城市或地区名称" })
          .layoutWeight(1)
          .backgroundColor("transparent")
          .border({ width: 0 })
          .fontSize(14)
          .fontColor("#666666")
          .onChange((value) => { this.searchText = value })
      }
      .width('90%')
      .height(40)
      .backgroundColor("#F5F5F5")
      .borderRadius(20)
      .padding({ left: 15, right: 15 })
      .margin({ bottom: 50 })

      // 当前位置
      Row() {
        Image($r("app.media.location_icon"))
          .width(20)
          .height(20)
          .fillColor("#4A90E2")
        
        Text("当前位置")
          .fontSize(16)
          .fontWeight(500)
          .fontColor("#333333")
          .margin({ left: 8 })
      }
      .width('90%')
      .justifyContent(FlexAlign.Start)
      .margin({ bottom: 15 })

      // 定位按钮
      Button("获取当前位置")
        .width('90%')
        .height(45)
        .backgroundColor("#E8F4FD")
        .fontColor("#4A90E2")
        .fontSize(14)
        .borderRadius(22)
        .margin({ bottom: 40 })
        .onClick(() => {
          this.selectedCity = "北京";
          this.showConfirmDialog = true;
        })

      // 热门城市
      Row() {
        Image($r("app.media.hot_icon"))
          .width(20)
          .height(20)
          .fillColor("#FF6B35")
        
        Text("热门城市")
          .fontSize(16)
          .fontWeight(500)
          .fontColor("#333333")
          .margin({ left: 8 })
      }
      .width('90%')
      .justifyContent(FlexAlign.Start)
      .margin({ bottom: 20 })

      // 城市列表 - 2x2网格布局
      Grid() {
        ForEach(this.hotCities, (city: string) => {
          GridItem() {
            Button(city)
              .width('100%')
              .height(40)
              .backgroundColor(this.selectedCity === city ? "#4A90E2" : "#F5F5F5")
              .fontColor(this.selectedCity === city ? "#FFFFFF" : "#333333")
              .fontSize(14)
              .borderRadius(20)
              .onClick(() => {
                this.selectedCity = city;
              })
          }
        })
      }
      .width('90%')
      .columnsTemplate('1fr 1fr')
      .rowsTemplate('1fr 1fr')
      .columnsGap(15)
      .rowsGap(15)
      .height(100)
      .margin({ bottom: 40 })

      Blank()

      // 确认按钮
      if (this.selectedCity) {
        Button(`✓ 确认选择 ${this.selectedCity}`)
          .width('90%')
          .height(50)
          .backgroundColor("#4A90E2")
          .fontColor("#FFFFFF")
          .fontSize(16)
          .borderRadius(25)
          .onClick(() => {
            this.showConfirmDialog = true;
          })
          .margin({ bottom: 30 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor("#FFFFFF")
    .bindContentCover(this.showConfirmDialog, this.confirmDialog())
  }

  @Builder
  confirmDialog() {
    Column() {
      Column() {
        Image($r("app.media.location_icon"))
          .width(40)
          .height(40)
          .fillColor("#4A90E2")
          .margin({ bottom: 20 })

        Text("温馨提示")
          .fontSize(18)
          .fontWeight(500)
          .margin({ bottom: 10 })

        Text(`确认选择北京?`)
          .fontSize(14)
          .fontColor("#666666")
          .margin({ bottom: 30 })

        Row({ space: 20 }) {
          Button("取消")
            .width(80)
            .height(35)
            .backgroundColor("#F5F5F5")
            .fontColor("#333333")
            .fontSize(14)
            .onClick(() => {
              this.showConfirmDialog = false;
            })

          Button("确定")
            .width(80)
            .height(35)
            .backgroundColor("#4A90E2")
            .fontColor("#FFFFFF")
            .fontSize(14)
            .onClick(() => {
              this.showConfirmDialog = false;
              router.pushUrl({ url: 'pages/Weather' });
            })
        }
      }
      .width(280)
      .backgroundColor("#FFFFFF")
      .borderRadius(15)
      .padding(30)
    }
    .width('100%')
    .height('100%')
    .backgroundColor("rgba(0,0,0,0.5)")
    .justifyContent(FlexAlign.Center)
  }
}


