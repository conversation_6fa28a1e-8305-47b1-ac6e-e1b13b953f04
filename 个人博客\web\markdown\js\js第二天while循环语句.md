# while循环：

```
while(循环条件) {

​     要重复执行的代码(循环体)

}
同c,c++语法
```

![image-20241107090532407](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107090532407.png)

![image-20241107094442097](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107094442097.png)

```
循环的退出
break 退出整个循环体
continue 退出本次循环,继续下次循环
```

![image-20241107093312958](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107093312958.png)

prompt输入的字符串要分行，用反引号：

![image-20241107100105864](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241107100105864.png)