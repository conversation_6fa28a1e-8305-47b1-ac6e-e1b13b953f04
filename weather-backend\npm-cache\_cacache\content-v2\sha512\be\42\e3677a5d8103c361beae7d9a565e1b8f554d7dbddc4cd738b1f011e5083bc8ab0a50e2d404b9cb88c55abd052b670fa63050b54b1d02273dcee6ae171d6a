{"_id": "@jest/types", "_rev": "124-8b447f673f6b77618945910535831506", "name": "@jest/types", "dist-tags": {"next": "30.0.0-beta.8", "latest": "30.0.5"}, "versions": {"24.2.0-alpha.0": {"name": "@jest/types", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "@jest/types@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "360069c368c29cbfdd352e863742f2389bf97ca0", "tarball": "https://registry.npmjs.org/@jest/types/-/types-24.2.0-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-EstemTb8rqsYXspGb9EddPGacp5LM6pVaHxWgfAoH6y3FSmSPWEz5+dH6kOv3yPvjIjwGdnM+d8uf3KmR3tXvA==", "signatures": [{"sig": "MEQCIDKSVwxG+CqJGI0rC9/HJzBKrcZu2HNGpDcLzRyeSauzAiBGQ0QndWbwSiOQOy28P2RCON75GIPmFBkFElVCDMWJLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfovECRA9TVsSAnZWagAAULsQAIKtzGPM6gxuS+BQph3e\nD7JJt2x+oXQ8/LMfhfXoaqZPgiiI5EGF2h7gbh+3GPnU7EjaeIEap/8pIEzm\nFbCfYYFDXsDyE3TKNY4mu3PT/ZSav5wL70kCPQfquklDPMORtahpikcgo1mE\n2/LkhQyhhbrD+xubcN1MbUpBXR+M/lDv6N/vWpzBOjXvJZRm9jzcHehv80Qc\n2OI/TEuasEgP+VSqNSjKFB/VqZTdELZGZPimiEcFde/Tc7hLvRUgJ3dgpwsn\ngIiMy/1AkPNyD8BcfrBmXMJB/+TwBLUFhGgfrVsx9QfRRTb1H0QndvqJClWP\nGuK8Y6ptkxpO/97TjHh9U1C1soVGtUrYJuZHAEFlE3A2NS70W2S2bjWDK6rg\npP24xqH1eoB3sxWN1wxHjd9oe2xshVr6KPr/Nb1DRTdNCHkcChytaf4UG/xR\nrb4AmovODibNHxBAUoDkKJlEK1IbvSCb23MDZg0gEM3cFZmuGVpWKwJ0L7vK\npgo8rCid/qyz9D79L1fdSeyETxyD/SvyGkFpSx/6yYFvyh2jNSk3zFcO7j8W\njLuYZztsw+x/dhPsPUQQssFlOpxaR4gbYnGM9Mb5Ev61vdf7bNtlglC4WdNf\nY/wD/Afj9jp6bAAbwGpoVNLPVz252Uc8oycFS+YxZENKoP+AHqDEOESxlTNu\nbfnk\r\n=SFy1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/yargs": "^12.0.9", "@types/istanbul-lib-coverage": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_24.2.0-alpha.0_1551797188306_0.24485164256787462", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "@jest/types", "version": "24.3.0", "license": "MIT", "_id": "@jest/types@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3f6e117e47248a9a6b5f1357ec645bd364f7ad23", "tarball": "https://registry.npmjs.org/@jest/types/-/types-24.3.0.tgz", "fileCount": 12, "integrity": "sha512-VoO1F5tU2n/93QN/zaZ7Q8SeV/Rj+9JJOgbvKbBwy4lenvmdj1iDaQEPXGTKrO6OSvDeb2drTFipZJYxgo6kIQ==", "signatures": [{"sig": "MEUCIHMgNqTReIXHMFWwlejACCbwHSalu64r4iOD5pobVvdnAiEAxP70jdRF0y043AqOyauKluZ12+yfegMqSzjlu09nZAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRW0CRA9TVsSAnZWagAAHHYP/Ai+a/3AWKC1pF8hujpl\nBSJMxmBwSqznT6ytnNwV69NLPcMRp6SqxvTaEqV16z2nzg01EfJprtwyCYSV\nbPGU56bijXf9EzjRJ9taBZ6A48lW2sqHOc2maXvLQpbX56CA9Jc4SpRUjeMO\nwfttjW8K22tFNU8UKbTItl2HFX+upaM2cVo3rB6lOEZLrguxXhY/ZbuGncY4\nJJ9jc7ceBwj1SKvhWLihChsDlxenPGMtzli/33b6WyHSuAwMU891WGnrH36T\n6xpA8p8IwQtKHN4qXpR/oXWZeVhxd24W6gTvI/rGkQGqkgrQ54uAgM18UJI2\nzTotAQSoGTPPJD1ISoTvuDq/NQWkNccm3RekCUH00PGdqzrtqsjV8JIkizVB\nmF6wampTTSitKUUpLoo7gYDcoy4u4B0OV1DnV+6eIGQv6TaOkxZxJrdr/YP5\nS88nSaXb2adwd8KwgAKlNiyAcoMZFZO5f22vO6ypVE/Ln2bfLA+VpJBcnmfm\n3C7QnmR7/B5RqSuN1JUANC2a/r0EskslscxvdnCFQswZq4ETylKbosM4W23W\nWBJ42gfiEWfInXsIiqT2S6cVPhlCCTst+qfuJW0arM7SRnppTQVRucNeZNz4\nFgPphLwqnkTpWhxROlXylrHfKRSq54hUbObEjVslcWV82Wptajk5bz4KultV\nFuz0\r\n=l2WY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/yargs": "^12.0.9", "@types/istanbul-lib-coverage": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_24.3.0_1551963571984_0.5964920725287659", "host": "s3://npm-registry-packages"}}, "24.5.0": {"name": "@jest/types", "version": "24.5.0", "license": "MIT", "_id": "@jest/types@24.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "feee214a4d0167b0ca447284e95a57aa10b3ee95", "tarball": "https://registry.npmjs.org/@jest/types/-/types-24.5.0.tgz", "fileCount": 12, "integrity": "sha512-kN7RFzNMf2R8UDadPOl6ReyI+MT8xfqRuAnuVL+i4gwjv/zubdDK+EDeLHYwq1j0CSSR2W/MmgaRlMZJzXdmVA==", "signatures": [{"sig": "MEUCIHCd4kGZPNTQLJ0huMNiPVecRRikVvJtGB3KtQvOBITOAiEAvtikPYPsNZPEHLqJpfhCVvoEfp5fo9qU2asDr78b8xA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch+AFCRA9TVsSAnZWagAASTIP/j/EJX/nfjLHRzXM899j\nbkzgeC8AuovZ8D9jo+vPMuuLEHqViACXDreoWBoKmFv4McwL3SGtCTUu1wuG\ngvSb9Op4DZm5HuVDqBrJm38eWuM2gM0bJKi+QAvjfIM6B7ePTbXkG2il4gd3\nBbl/hrTBg0/Ot3mFvHJQsEQ5LCTWGiwAnVq/BMkNt2ztVp0JxleruZq5x2Wg\n4TM4qJnZgtIUSBAHOH6kTg9wQpBBFXAaPKSAIwP1mPtxTx/beTvy2wuKGQzX\njNsKxGEopJ+1o5XbmsD80VhM6Is80Q337pD11gSWIzX53D06sGtki+MGpEQJ\n48pKWjEm8fC9owioRb6cONWT5Ppr9LmAeKz6AqYqGsfybrdlI8DbI8YnfgZb\nvdkFtOUyoBPPLt+PdvEAZKcg5je6nqkP+fPD8tCPpBiZFG+gCxQif9TtzRt3\nS3sa9D6cxR/0GndZnXgIqQGm/wepr+/KdenPrtqdwRjKzUoH+exjufhSd+xq\ndEyZt5SGCWxVsrk+rEXb70+uh3g2F1/QSCng+Y0Tsz/0NyUPBgXXyIhrruBe\n5slUkUPl6zZhWIC+tjZrCg7KLWLriQyNW67BrXdWyVlfIsZum+Jz21EVhu6J\nL2kRJcSMaE5kZcR1SFN06jDnblAo0Zhqabdc1EdhRaLZq58ITPtMOvkne58D\nILaR\r\n=1nto\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800533020f5b2f153615c821ed7cb12fd868fa6f", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/yargs": "^12.0.9", "@types/istanbul-lib-coverage": "^1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_24.5.0_1552408580805_0.9672887484361055", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "@jest/types", "version": "24.6.0", "license": "MIT", "_id": "@jest/types@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf018e56f3ef45e81119fd613fc20a9819f4eddd", "tarball": "https://registry.npmjs.org/@jest/types/-/types-24.6.0.tgz", "fileCount": 13, "integrity": "sha512-hnCMhUokUm6A4HPE9j3pNG9N+bSFfhqje3EbIrW6YjUW2SXuyZxy1QsJdaICo1oN1o2vVSx6qlVqQYkmWVsjiA==", "signatures": [{"sig": "MEQCIF4pKIikhby9Mi8NqCd0ZSHVjxEtruXF+PvH16TKPMRHAiBmvMP1ENlW/UVTRO8kSL3ob4svYTnH9bT+nnaLkEgkVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195111, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAJCRA9TVsSAnZWagAAkSwP/36pdmHKkCHyQC431Hny\n0LQdLZyTSSAm+Y/kgzSA/tHIKltjVJvM7QfH+JCF5W8kWJbAL0VHCq8qdzXX\nSbryG9EGAxh5qjCn3k8CoubG0/ba4UY+gq8vTZXvzcZuc3wr/HjqFeRuI8b9\n13+kroYf5AkGZLLL1WnGQFyAhB7NlfCfzm7rjciFR/15qFNJo5mA55vL9XWV\nxJiYiGacp8n7aCcjq1BSB/gqk82ptGbOZG0YkdYSNEZbUvb14qNQzssMzWVV\n7b5DqMOGmzz90XtmYFSur74RCvPEf2UByz/3uR4IFiaWv7VfjqfPif7wcW+d\nLcq+odukV6yBA/pxmFBDVgdlSUfKZSEXZ37LjzW04F5C2ZDDMlNTLltc2npb\nRKh5PoowFzyaIwJQc6baHUlS7R32ts63f2epsz9uvIYrskqPSU1/xHx0+Fli\n1JVy1dfU9RRgFyfkj16cpbdTivDt3SR43UwI17d2nHl7VUWTvIh2uPQW2fhf\npcTZLV2kJIN1Ll/1925HFAGRmaONbFlPo7hHFpu9nobkNpyrTqWtuqRvYpcT\nwt+L9vIbTWwJibJ2HHp2iivELKeH/KYbzd3BOm0//oao+j8raC++04TnO6cA\ng3qrUNPpERlPN8HfC62gOTPe/LKkhOPFdOpsclcQTd9G43nu10A74A9XbDU3\nXbi8\r\n=5hTj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/yargs": "^12.0.9", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_24.6.0_1554157576596_0.5048716264628086", "host": "s3://npm-registry-packages"}}, "24.7.0": {"name": "@jest/types", "version": "24.7.0", "license": "MIT", "_id": "@jest/types@24.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c4ec8d1828cdf23234d9b4ee31f5482a3f04f48b", "tarball": "https://registry.npmjs.org/@jest/types/-/types-24.7.0.tgz", "fileCount": 13, "integrity": "sha512-ipJUa2rFWiKoBqMKP63Myb6h9+iT3FHRTF2M8OR6irxWzItisa8i4dcSg14IbvmXUnBlHBlUQPYUHWyX3UPpYA==", "signatures": [{"sig": "MEQCIHIsQ9KwCLi8W9uIWeU+1WcWmyu6ZgRgtonSfRGr9kkAAiB/xsPB/CQbXhVQ4wsOmqCXKmHG2AYsBFslvEWBZkVVTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC6hCRA9TVsSAnZWagAAg2YP/3ebtuzVitiz2KbyqZcA\nqot1YbbvCN2kpssnok0nS2wNK3ZvP1ItbFX45KCffSyxb8Hi6SU5nnSlzfgc\n67eD54XbACtmZRG8DC5DGKdtJfb9TKMRbccAaGQVLczEs3zeIjcKgnZ1mtPn\nqQ4aG6qi0rG47sScPSx6XXccGvaSiKfHtZRQ3Tm8Fo8y8hBdeg76lSlmgjrm\nbLzjn9Z4N8qJkEtRiS2KCTlsdUmF65EWiQVXZ/07gBRNovfA/gW7VXGDNEeP\nujpzBfHgsdkVGVSCXNLw4qLctYUhpOIqqBs/la9ztu9x8w3C0wYihP4zpGww\nHSfo//grCSGCNGDbgED/jXlguXaI6GoOIM35vIFs5sSSBvpH7bnkoub5GXVT\nct+5oHMONDMrwZbGeNdWcB/YCZz3fzELNqyad73kyyTPvAeG310VWAy8D3Vo\nfSg/LOhtcAD6zZXRRZ/6bmggFNoANRT4Zwtzp65nCfR+gWMxv9PGQypeea8r\nTGk/efSo+W0mJCeFhE8RMsPDh8UVnzrQm+vDEuIgTib4X7IAFgUKSwvE99Q4\n70MDLnwNI/4WzyC4SqkSA7ES0qNDrtHENN1QI+ZlEtu6O2XBI9KCiNBTU0rr\n6gVYGA69ZfgLOiBj/OPzaTKeBWwZa8GRh0pVGMkQNo41uq9cslfVcRDIFe4T\nMPrE\r\n=j/9H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@types/yargs": "^12.0.9", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_24.7.0_1554263712548_0.8474799012948884", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "@jest/types", "version": "24.8.0", "license": "MIT", "_id": "@jest/types@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f31e25948c58f0abd8c845ae26fcea1491dea7ad", "tarball": "https://registry.npmjs.org/@jest/types/-/types-24.8.0.tgz", "fileCount": 16, "integrity": "sha512-g17UxVr2YfBtaMUxn9u/4+siG1ptg9IGYAYwvpwn61nBg779RXnjE/m7CxYcIzEt0AbHZZAHSEZNhkE2WxURVg==", "signatures": [{"sig": "MEUCIQDnlMwwGOL0m1QGa5k914WJPLR9ljgU3EbBDq8tlISe3QIgB40fjnuHrUX/Tuql59CQALGqH8hc8QrIvU4EKyFaqBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkQnCRA9TVsSAnZWagAA4fkP/iZPghe7JcO1zYtEH5h2\nXI65b/FbU+Hmp7KLvnkionNzu3PnUCOVw8R3wdTKKTiJHKRO/ueuihqPOq/u\n5VmKASVzXVkbULZNuisPmZl0KY6RzdTezt/4WHF/nXxGFXLiPptZrLcCO4ME\nFalU/HV7o5fEuD+qaAGebY2h3lfRjAbGieKqZO5Bt0RtI6tou2drapyGkbxX\n+XhYV+50s26gD8gPSCdN7fuI0nsyf12ozRDiZHKtxcOWmIhqN/7AOYQvDEkq\nhrf6mN6kH246VBVzFCp6Kd8yGJuSNW8VXGAM+90yKk86mrvelMvAEtJIFWh5\nRm68a//u8RhYhP6/07yW3g78KEQiEhx5UxZmI7M9bFX+LTVRyoS/WCxinWbD\npTJThqjUjW4KAzKzK+WkVYpciZb/nvbTmdLMyVoMF00juwzdUHtK5TJ9gf/F\nMcxRMedinLLAcs+beZgwq7flvm6wPwSdT1M49q2RsnhjDmH7B7aDuP/WOzTe\nh6jvoL7NrTFhpG6AO0bPSmcJvcr6SqMRj2URAlFJTPbtOwd9GG8Xq3VCSruF\nDifEFvySJVKE09HUwVZR4A8n+I6AU7REKR6NLwGLagBRz5k3zmuK80CZCfzD\nhuhLVnI1PeRE/hKmhSppTuQ5R5xNjJ2rmvGNpSyalkF79LVpzu5gy0uR4FK1\nvQay\r\n=9heb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@types/yargs": "^12.0.9", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_24.8.0_1557021735083_0.5209489404034777", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "@jest/types", "version": "24.9.0", "license": "MIT", "_id": "@jest/types@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63cb26cb7500d069e5a389441a7c6ab5e909fc59", "tarball": "https://registry.npmjs.org/@jest/types/-/types-24.9.0.tgz", "fileCount": 14, "integrity": "sha512-XKK7ze1apu5JWQ5eZjHITP66AX+QsLlbaJRBGYr8pNzwcAE2JVkwnf0yqjHTsDRcjR0mujy/NmZMXw5kl+kGBw==", "signatures": [{"sig": "MEQCICmFLreFyVPWW5HnUvT9a2RpL9ANJVjOg1EKskBWm7LjAiA//hY9TGf7w5+9rZdFlwBcuEofusyAuM+lf47mJHnTSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVjCRA9TVsSAnZWagAA1yQP/i6t/OrKm5rX4JwiQqFQ\nEH2NjlcR6oqovjfTI4roy8/aPsDa0gA+aGXt+8GhkISLOG1xCbu0FuWYGIcu\nsI8PJIRx5r+O547waOp855UmOPROLSG7cqsBRvmjskW+0xlBOCg+YlHjtXSQ\nivWqWjj5QqAqg3cAgLY/8FxKbHJnZBNzmj7+0tqwhN9WH5BM3ZkvWDoKpxeC\ncOaeINXrYvcbVgREboc5tLlfU0QZ02b8AXoPcPX/EmGnnqI/dz1byspLRB1i\ndko7bL0qP6BkcMEJi0poAAf1Emtt4ditHOPopFJ6PeBfa6/zn023qzqKZIqj\ng+mJfziWeXtwh6Fy60Mfnv/wfDXjngKn7tLPLzvNcWy6XzUF7T+Rp6hb/JEW\naZBLtPafvxT+XADs2JPuWVu08W7lYUKggKR/boPSZHGxgfrGL2hm6z/tdxMv\nOrYkh25dks7/MkXUX7jxGYXhIbrNrVpYqscFmQDElkbS80T1TDEcicyipTw7\ntzz5u21btPSbvsBEt2g3vgbP5OCvV6P4SES5t6X2BG2hPsYO9O+zBeSYYgJx\nq7TDLHWeNrA0XFrui0DonHlxq1m3ojJkMOJJsPVqD93Kw+CXsp0AriD4Ptor\nsMCzPf7xvpajGVxKMmjafOYOtWxYOp4IojrFqEpZHWZePiu0bEAOq5wEKWtJ\nc0vW\r\n=tNJR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@types/yargs": "^13.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_24.9.0_1565934946266_0.132747631553088", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "@jest/types", "version": "25.0.0", "license": "MIT", "_id": "@jest/types@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fbd26aec433719fc4bf0f782c923e334ff6530c9", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.0.0.tgz", "fileCount": 14, "integrity": "sha512-svDFfLCX2CNXTFxdK3aonxEtASl+KXUd4YOGDJtWNrdgKYHYu1BQRk3/bAKYLoKM80QCpLoqPKJ4Rn9SeNQeXQ==", "signatures": [{"sig": "MEUCIQCCF+x746oRhIaPSTech6a5CPMzvJbNhWsGCG/5eyLLQQIgJgMmnEyvXQifaRdqyu9SsuW/p9DBjTYobk6ZW3WMgJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrBCRA9TVsSAnZWagAAnEgP/A43OfHvBjlMnurtdNQ+\nK34IKY8tT0a/XnoqJ0o7g0MShv0qfpTr90mEJx/DDm/5tzmeQDOcyieU91Lw\nUozPSFligxsLRz6ju7An+Fg/VTWIdTl3/ODvy6YqkINyshTuUXMSYCgdwzXS\n7Npb1rrU5ud14NZpne2Q3xrfXJslSDm0Z8cuf7gD/1yOKov9RxXZnTf//LUx\nzpeaIH3v/kLdf4eM9vlySIOIfmDB3gSkVTOfEjMCjGdQpA7QVP7WN2XMwsCl\n0KbC7qhJLyedQRQW3SlCof1T80EfwC/hrIpZ1aZB5PYJqS3LIWB7YTRAE6C1\n46Bmw/KPjJOI6fiQzRP2KQ/c1SUAdP+qJs8ar28jTJ7e4Y5GSaxmL3DOyXZl\nAf+tFZQQUhLUDfiOVqpNxlB5pjl3ToxyqW/b68oJRh0xgP54l+UDEr5Ijrf2\nWQj9GRsFoBjtB71rnB4sY7JmIbbFKHEI/83L/MyQaLm3Y+1yc08YxsXT26wX\nFF+Rjhrxsl9mNd41j51yT8MUCFMMrPKQMGbrLyMxQ6CHrnY/5cSrMvbzPFlX\nh+bgXxTwcYXBHMiYvMML7uhiZQSTB2thTPgC0Dy4sGfqqXLLSj3uHE0/niWZ\n1wigwRdxyDqHvRohizHg543LK4XGj8cTTaDaDdcwSQDxWuMahdb4iI5mXEjw\n9M1e\r\n=vkCb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@types/yargs": "^13.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_25.0.0_1566444224419_0.2239802656872263", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "@jest/types", "version": "25.1.0", "license": "MIT", "_id": "@jest/types@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b26831916f0d7c381e11dbb5e103a72aed1b4395", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.1.0.tgz", "fileCount": 14, "integrity": "sha512-VpOtt7tCrgvamWZh1reVsGADujKigBUFTi19mlRjqEGsE8qH4r3s+skY33dNdXOwyZIvuftZ5tqdF1IgsMejMA==", "signatures": [{"sig": "MEQCIEswf4t/nTwdWoJdM9BXTS8PcdTi1I7M8702ekiTm8W2AiBTDjk8SZvCUsca2qw1MchPSFr/8FgMpcF0v4xeJdtYaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56BCRA9TVsSAnZWagAAd9wP/3l9UEU33uK6AYuU5B4M\nunx+f3hNrG0PJIPjLstklPPCnQWx/uH7Wz03eRhpncFxUwOMfUeCpJZ1bPGY\nlwrHtCKKIy4CDj3c24wlnAdlvGjvHR5hcQVZTB8ZL3yP04gGPxlOkw69dljk\n6tX3J1OQtQlC3jxy1lFpIzkZFuRzgN2oi8DTidK21UGzcg3vg2NxbjpUnzYP\n1yHkkyISBfpPHe2utTKcpyidcNHInwukdgnwM0xZmdtjrU4rQuHWQV5C0OgG\nEu8ys4o/5DlN3GAV3PLZ9xHWTPccwFy8JYUoGztxWbvuqReGSCWjeW47cJWC\nfgwuY8sG4NVTBJLFFR4/O9CXRFnUcGvt9vnp81NRiZ4ibGgLLxHZbIVl0hGQ\nYei+hIqIMWm+Nr94dxD2esVafgFNiVwixhlMgKt0o4iM6qeM96gt6Wju1P8V\nmpORWy8UmNIcCcIFgDVHVop2o0j29eat4oCjO0CzA+vsbncjUsTi/jHjAtVE\nmv01Zx7ESpYWPwLtopFvrZKHEjA0sPPIrur0FFqHaM9rXOWOj6Pn8PZBB9bv\nr+A7vFmsxqqzwhb/l82k8IrQPN8GxbjraU4kMKncWxTmqq9in8EvG9iqZT40\nOVmurxBp+a8fcZY6uWBT/RWzBd+mdEtFQ2CxojSP4x9my/BNl2/AZQjKLgdA\niR/K\r\n=tcku\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_25.1.0_1579654784672_0.2158202359281549", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "@jest/types", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "@jest/types@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "adddd68e6934eb6482911a9c47f4dc338d2bf6d9", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.2.0-alpha.86.tgz", "fileCount": 14, "integrity": "sha512-BqGXD51rBfSC2von0K+CamyO6MGfwjggmD1Ggs2FhEbsNWWmnsQJ3TbKvW6AGxtMovyiuqqSw+3obrQIKUiVdQ==", "signatures": [{"sig": "MEQCIHBUhEBEgxdhQPql4on1SgoKx2av3digRLb2mA7SAA9QAiBsaizJugo4Saxyq4Js/QkF0ikP27SDE+LKXmsOGBcnSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43496, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HbCRA9TVsSAnZWagAAoBsP/RdS9TTkvIT7ICZgMDO7\nwkEoQYQ8l432a9Rw8pykHdzwiB0UADFLjeiDjX2vpj4Ryw93JytW39QFbNNH\nQpm6xhG7LFKV5iz0g7rvAwLJwmvcdOBGOU7R850RxdwOilNBoPTpX8lMG36l\nuU+A7szxhKSxB4Wrji5iULBUJH5ebPqV0EV0MzvFealCBpbg5wmcjKoJtLoT\nOh4Z8nxEHZ3BbCR9QDsDr9oqZHDzNqJuHtdAUERzbGf2SDupAg8vX9Z/n4Od\nIUYmEA8luqlIYtpfE/ted8KPRg4fwAFt86hGQb2M/QQH/2KGk/0vn/ECE3hL\nZM2TZmNE3vmTk+pQsfE+x47FfpSnwa+hmx/2hKOohlxcT1Z6mU46yKJnk36w\nCAqKrtuwdXmOezUgUeYjBh4FpbPuuYGE10Qjwh3oWw7F6BgjiajBOcNtPnYI\nqOGq/qvuOgZP/ZTVyuk7BG55i82qc/l3KHj9oUWSqOjGMYV8J9uRnZ9VM3vI\nPhj+gsmB4HOCspftlV0WSIrh6y/LH3S0qeSzmQ15fYweI0E1Ej5E5SnhLIA9\nOJMUxg55RdtsltF8os1cafv71W+WnJMMnmfcmC7Nn3aZj1f4sVyZBeLxKdI+\ndhkG879TvIK9eiHnjQSK4iP16ULPYQIR0wgVfJqBt5bo46MlWzQ7bn8Obe5t\nBxhz\r\n=iRIp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.2.0-alpha.86_1585156571450_0.7460359101316194", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "@jest/types", "version": "25.2.0", "license": "MIT", "_id": "@jest/types@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0659866d9b31843a737b601b950a690e576a415a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.2.0.tgz", "fileCount": 14, "integrity": "sha512-RLWBAON8LEjzD60Cn0XFmvMNTuV+scKlufIUApnG7VF7oA2jCEk5J0uzEchx6xuOwhrHohQM28K4CmEjgtDEwg==", "signatures": [{"sig": "MEUCIERTHwkSSDm+8Yna/LK+q1IV1rn7qxP/4qrb0O5pYBFPAiEAwA1g9W2DzGp/HTZQZ+uu8s5YJ8ThtM2WYjI8nIv+Pfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5uiCRA9TVsSAnZWagAAF3AP/21E/TeqpTduOYarvqzx\nCL5u5OGdiun1+FO6/cqMXEeW4xkxXDhsQ8PpJtBSuDOYAqt586H0JJH6BbUG\nV54QpHETV80ytrOjFXhknYKeVItluyRdidPlGdfNJ8/Vsw3BTpP+V0W8DBy7\nzKaxwcSeYQJ94R9OiA7NmHwiLSTJFnbk3sMir6oCN667+ysnbPXwsZFnX5hX\nOruUuTU6y1FvclUc4qEEWHTJK9AQax8E4bdWt/PEO3/rw7jxWo+WVUyukqgW\nl417sSwJ+XF2nuI1qvXohJEKskluaa4buXnipP+kVnev6J4ICyIxDaOTZNKf\nKC9h3Qaxg+5RXeWwKQjHe66Z9S5L9/8YrFXmDjvrJAAWyTPVTI7QEtfltyVd\nYOdM4mEjy/StwwoMNLrLbyXavgamMbOy039KYjb1GbJl9xazHeIodi5HuAmm\nd3UIZpDH21NL3XcT6xi1tQqJw7qvuGyZB+fPVAYOIZtmjspsyZzGLGKZ1NCJ\nhRt8n5TriudBPr777eJiTMgjRFAF0RKaua+8paIJVinL47OlDCuIOmlbtori\nMrFs9UKnevZjtyYom9pPOeJL4J2lOCY6A/QJPaH5hI6MegkH7lkqGssxGvgQ\nvWue2GCYz+Jl/rHq6T7DdNTahhrZyAtOyA/NtIaGuQXaF++mVgTf0aMhKrwS\nmhij\r\n=sR5c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.2.0_1585159074287_0.7246370283201329", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "@jest/types", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "@jest/types@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b5aba99c7d3beb004e84c6c02843c25a341da753", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.2.1-alpha.1.tgz", "fileCount": 18, "integrity": "sha512-TWZhcqQ5E7BUooWmCAJL8ngOqdqWF+LdIDYSxthw400x+Qt+j35TarKYhkBW5xgKd6gddUN6/aVCzU+b0Rs6pA==", "signatures": [{"sig": "MEQCIGk9sT47v8Ux5sZAnm+IKIwZAUNMc9mu2oGZIYZxAYbTAiBLAFVwSpBZft3kz9Am1Dwd7DTJzofZeoRn4oK1zCSI/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+lCRA9TVsSAnZWagAA0nUP/3WudRjQ3HC+4ri2Mugl\ntMMMPNtVmbzakClpOR8yrTiFy+EAy3S5suKm2mLSd/dK4kvJTkWqPoP33R5A\nmZSlCQC5YzQHHhxj80hlGnI32hLKAeG/6Y+BnlPjxXhYVimN/fdoaOZ0oG74\nXgi+DBaQUr8WYhup69FoMzqLZ+8jojWF5UENs7+6+8yQF5dtSYEsIQXG9URq\ndRWQTJNCUa46p2n+TT/njvtAqnmTUybSMf+m+ds2nu1mZBwZaZmcO49O14lr\nT6DDbwh7XFd+kJdAVqLsEfq0XMBKs4u0JGzvoQAtWuUTYN8F5idW2XLF7J4p\n58mPlCKshO7CzvT89bcdZyzHh3NHyWhNk6OmDTNv3n9pSdi7oRZCc8fZe9jQ\nn8uWbZOMgNrnspfTIakF/N0NcBID3rCqCPgNn8KdDhSv1UP69f+KwfBdM/Cu\nvZbY8yd1vtjvebPf5qlbES3eCiKII99EHli1/uO7juAUnd4rQFxgnn2vfPDu\nfsK0g6hNJSxZZI7UDu6Ppy1jRuopWgBHSGi+/V0tFNil4LbJYGQOFIaJWRvQ\nJFv77YvN2eJ6uv1pRrOWM3l8Oud4qps3x2dmP0/SB2CX0k34Q+Aj6ohmgSaU\nVtIHV1oJaHbWmQbyB+O5ADsKxvLrREdskaJisYQuSBHNZyaQFR4fqnC640cN\noDJl\r\n=oniD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.2.1-alpha.1_1585209252713_0.44339340741136546", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "@jest/types", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "@jest/types@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2304534f2447a8bb8756ba192094d10eaf489bf9", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.2.1-alpha.2.tgz", "fileCount": 26, "integrity": "sha512-OOUNU1jprezIGUcblPoe8YhuMNZPpiTq0lt58gu+4Xea5ocDNzdfTgLiXnBoIN8b6f+19FFFc7ZynSRsuT2v5g==", "signatures": [{"sig": "MEQCIB/Y+99i2I65fVnOusTm1Htz3JFInqDV7fvUAnDTilJVAiAvIv7UYKXdfPcGJZR5mRhOMwNFJAkA4Jp1EJcfVvP6UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGNuCRA9TVsSAnZWagAAQE4P+gLTQKKMWL+WR9q6Yyri\nWOFzYtJZ6rEDLa0J+xUpQeHBmXM9nBlcHvTUqi34CUy4TI4RGeZEZrvyNFbH\n0bEuMZaSbGZabZwh/Ay/SpGdDmBCKYlkmytmsEwv6RRdg1Qf4TZ46XbkPswF\nsazhQ6YyQudFEaX3XmSSWPG+ksUkaPLDvA1CkjSm+wx0UVw/A9tBn94Xkjeh\n6ehN8BkEGeOR+xWJJHWzkSuv/8HRR/0z1pdcGCh1ZkuP7mPmyi5/lcvH0wN/\nVp0eeySCg9KjrIvFhwgHVLW3YtYeJKK3S/y7+x/SNd0dK5L0faQi+2qYrXQg\n0ht+DqnFXeADBXgZod2TmT6QRg1XMW2SHKLoOqs3Nv1fr7qBrdINho1x3MBT\nXNaYfn0fP1CJtEczxkgK8SWBW7ZAVkZB+CnUBchyRRrb3iCu/w1dS6jPg+nV\nengW7ZRLv9YTeNebkRvjOZTM1qfepPLCmrdW12rO6auh81abEOsE6jYLegDo\nZl9Doxgmu1nfqDRIAWtPJOsLnSShF3VbJOGxn++9wIzHGSZAsXu0hkwwkH0J\nRsvLBCA2rKY8hssnz4m/jWpU03RGqJ09GYxmYNuviJQQHpqt2x2KfB+PiCf3\ndH7dy0MHftGrcTbzZ2gm1OYnyeOhDhgG3yNPbuy+QSIjLKgvrs9QsH6CzmwW\nPKw6\r\n=5ARb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.2.1-alpha.2_1585210221610_0.14232899926308495", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "@jest/types", "version": "25.2.1", "license": "MIT", "_id": "@jest/types@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "692c8950d4c21fc6b4cfd141c3470b735c5bffca", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.2.1.tgz", "fileCount": 18, "integrity": "sha512-WuGFGJ3Rrycg+5ZwQTWKjr21M9psANPAWYD28K42hSeUzhv1H591VXIoq0tjs00mydhNOgVOkKSpzRS3CrOYFw==", "signatures": [{"sig": "MEYCIQCQVh6DPGUEKPknMjRlfZVzbDSqNuKjeVPm3dd17WjQtgIhAPVLDhOQ/5EXwT+pvcjGyjpohMNmqNCatOFKPJFoHEvN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9QCRA9TVsSAnZWagAAAx4P/iMzmIK09GawKqmFcPD0\nk0Kq4bv2CRcSl/I83VbRt170PBPB5uUIuJo2NfAMgs10JEsywttL+qLY4NJU\nQ9NJ3CfHyFsBuQ6MnVb03dFthJLkgKDuPzKFPamQOc5N+DjjlPOPNFQY5+MI\nHgnObYdcomMeihsTndo19KmU2DHttc3/0QtEUC4yyvARphG2j40kUJQOS1Y1\nha9dB3cHjca3K5Knxqg3XIlHq/gDReMG+c+UtFJ5/a9CdaAiiqBYgpIeReHk\nD3KZSZqbvb8ijLhBsgej/GLL6D8WS7Sa4fspsYrFBDQB95o/LPE0cxUGLBWh\nLMe/QOLh6P/VBeh8VDXRYCBqG+o35ITv/gO4xQWb6dOqLvjt99ALzLP/JHk8\nOgREW+KO3qzquioEhGBgF1c3pmBtZSnF31ETmN7tASGMAAyDLwfYyYqvQIQ2\nFE4kh4gT2DQk0KkiFs6GIsLydxFtj8PN8s35HBncrEKHOTLiPL+CCXuSYjTN\nEIzKR+v3arWjE3XZ+zugJo/q0PJY8qbETFJRQp7FMjy/DRRu0X9BYnz6aAp/\nb5LZQ9Yzzj7hBNuEtaF5JFU7qPjKdF7ShUekSssbc2uToAorfTMCq9ymBLmF\nXXkFlhUI5f5vD63gP/eYWWI9ECmhFsi5KXOJbQeJ3CCQ3RQGeS/FJuRAocU/\nq175\r\n=ER5W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.2.1_1585213264380_0.6861752308167919", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "@jest/types", "version": "25.2.3", "license": "MIT", "_id": "@jest/types@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "035c4fb94e2da472f359ff9a211915d59987f6b6", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.2.3.tgz", "fileCount": 18, "integrity": "sha512-6oLQwO9mKif3Uph3RX5J1i3S7X7xtDHWBaaaoeKw8hOzV6YUd0qDcYcHZ6QXMHDIzSr7zzrEa51o2Ovlj6AtKQ==", "signatures": [{"sig": "MEUCIBU2ai2Fk6AU3BaqQS9JN2H+5mQcGB/SmGOP+wx7q1UsAiEAoGJyqyI+JS1v0jnM1q/of+8StGxRqRGBsiRxetrf44o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+JCRA9TVsSAnZWagAAHlQP/3LMxWM6tjrp2brofOPf\nEaqTKBqiUUm4LJpfwxj9yBTrQIbV5KHTj20rb/gp/vZRcVXJVVcl9V2DVUea\n8gm0Wcfe0oTxp8VTNjsSL903701tfZcRhp/gxzRT1xQWtnXXZf/Y0b2Om7C7\nVqinTYZtl98d5aKC0f6HPIbDgnazuwDMuX8qX/wAcLbaMh7yh8NqvI1Y+e//\nJ5LSSKTA4o7616dhUM2YqQLUKTQYUChd7WjNMN5fH2FltecIpGYTUJlKID/l\ndRMwUNQ5eIA+60jBhxgja3Dp7RK5fcMvPuDSeoPoMOecfCc742JBjn8gmQhT\nDztJpdi+5l4cC6YHQwvA/oXml/+zaUuXlZarDkJNSMrNDKr4bJApcSMdHfpc\nmSMV8byY1HFweH/eW/psE/RWiWSYBY1r9FwqqtClWgvJioLGrS6rIYtUwx1L\nKiJOxap5loiOmFqikH9JJVeBGGABRXLwPGKSO1ilfLrbc4kzqGqwQlSGVRJM\n3vhKzTNk/Wl2rWzMzlZDRSRvwI5H8lDyoPLDCg9ITXu1smDPcGBvWO2OlRf1\nNv6AdkmVUgiPM4zPZH3MzU9C/2GIGG6nzKZkdog+ZTdfHjhJURVWsyqU745I\nkzpU2F6hwajlIKdV3z3euohKY7yVWuQ5p4HZhZTYxcvfY8h4WtwmTuh3/a5R\nKIwq\r\n=IMvm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.2.3_1585254281056_0.8234681203760013", "host": "s3://npm-registry-packages"}}, "25.2.5": {"name": "@jest/types", "version": "25.2.5", "license": "MIT", "_id": "@jest/types@25.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c4643a15b34e7f4a5bea33d810b81738acc7329e", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.2.5.tgz", "fileCount": 26, "integrity": "sha512-kwKV2fOTDCvsL6yFlAyUUCyg7RovnZiC7e3qCZzckVsyJ/ApBSUr8ZBjoBNagvIA7Uv5EYLT6K8MaY8Bou7Kbw==", "signatures": [{"sig": "MEUCIHi9um/+S57ecgo0b1jeKCc+H5UfKz0tgJjH7uGFiKrsAiEA0dZVRKTB1kjfrPdbdxn342zuHzjqU26/JLLg1dM8v/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb0FCRA9TVsSAnZWagAAS98P/AzNqt7zBsilMxBtZFr/\n6IHMDWly1ko8ur5wN1k3OqyKkNtaflJkxcT3O9Qz6+W/Aiw/d3BWIe0bkie/\nTvic25yKlPWmln+I76HWppKEYqdN6OjSHEIgJ5x8cjOYg2RY4dVn7ajcm0x7\nbP6vRlbf9qmxSCrMZYBf/4Kw+AfRJIQzs+IFt8gv28vViZyQLEmyAhC3LiZn\nDGlCuUWzdJ+C7Tja4VeSAN8dT4o6ofCE3NE2Vq+vVmAMjrH//7P+3AL69bjA\nTYqdpuL8Mnqy3enIE6D+ohTQElu/uFsjuvALIZEYPoRIAqWcdMx0NoCa69TY\no3QNZKgxEvoWieKNhtFnD3jaq4HbM750FWplqhahQDOVj8J6sQIagpuG9iIL\nw1nmyOccZSD7zzmTDb4r0kAaxEV/w27C8LiR+t4V3i1jpcDaNVon48FDTXcO\n0WD/5hV+nLf4cigpsJWRh02ollujHhMcy7Y96gYLKnMLrvHE0sPP3X+tYP1h\nK0UAcmyLrDM/1FpDUP78uuesACM7DGajhgqJAWaFVbwb/FBlT/5PPOAUPK2l\nVyNaVuDmxWzNFa06eKD/ROVbZ0JtASkXsbohiTvy5WP8QImWHa6YOIugAY9A\nMgUU40j5LPQCKbS+D9NAX4RirqKpHaVCxzy9m57qZzcuXACjoVutIKyk85df\nnQ9o\r\n=ehIa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "964ec0ea0754caa2d8bef16dc89c1f926971f5eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "deprecated": "Faulty publish, please use 25.2.6 or newer", "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.2.5_1585822980718_0.4630985240861649", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "@jest/types", "version": "25.2.6", "license": "MIT", "_id": "@jest/types@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c12f44af9bed444438091e4b59e7ed05f8659cb6", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.2.6.tgz", "fileCount": 26, "integrity": "sha512-myJTTV37bxK7+3NgKc4Y/DlQ5q92/NOwZsZ+Uch7OXdElxOg61QYc72fPYNAjlvbnJ2YvbXLamIsa9tj48BmyQ==", "signatures": [{"sig": "MEUCIDomba8X+J5uFUe8NHjgaVnE3libJT4ammzGyIExkASuAiEAvol0pKR6l5tzm2jr2T29wjTPPXTPz1wOpjgu5zxHOCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb50CRA9TVsSAnZWagAA0pkP/j1bZoYzX65gWU6w41St\nXt9w9Srn/3uRDgK9i/6xBTl5d30JNvwg38Bvj+wl+4abNE5uPLyTiIpQqGR9\nr7I5xj8thwfyDjNXHzov6Yho5EZIzRSOTAjQ/jjiK826gVlT7i0gYyTW6Wfa\nzsDN6EkIzgy2BxUw/mV/eCEvCXSnQGWnrHlK8GseQiju+irAPln9jtp9gqFm\nZKq1Nk2bpqALmqfnwRluk48xexG9BNlnDK7y2hzeAWqNgkirHVaqS6ErfM9V\nI5joxezdQuoz3sNNjV9XAdH2bjtmbqTgZsvrg93AQmx8mFPewV5PxVKRf/N7\nMuDze+SVL9PeBYAbCXlej7MyV6y3jW5j3sTfvbFsMS1MTdSk8B07i9esr/2F\nj8zP1M4N3y4PMWpzJHJ2mBXdV4jS+PbvGNRzX48V7Hd+DZpsxuFMne9XsW84\nK1XwiPXRobAN/GwkbLxdQCYFqeoNy+FxTniZpfwBNfIHTIT49CYrPBQpu43K\nw6KwrdNTYrGYRwu4JJDDIihnQRbWWNNlM1/3o+creQJndMz/CAZ4gHF9rym2\nqzZgjxAUQKeRnGNSzFtzH8p35pbigFUT8eG//Sm9+t/zM5xn7viLzNmjooxA\nEuw+GgASUP77vyL8ZnTiOSoYl+gbJgXG4N1AAnGYrebEC1aLyeTfJgu862yC\ng6IL\r\n=z24b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.2.6_1585823348002_0.8152543961612722", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "@jest/types", "version": "25.3.0", "license": "MIT", "_id": "@jest/types@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "88f94b277a1d028fd7117bc1f74451e0fc2131e7", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.3.0.tgz", "fileCount": 26, "integrity": "sha512-UkaDNewdqXAmCDbN2GlUM6amDKS78eCqiw/UmF5nE0mmLTd6moJkiZJML/X52Ke3LH7Swhw883IRXq8o9nWjVw==", "signatures": [{"sig": "MEUCIFXJVD5H03SmhUgiqnZC9iRmnd3j86hz4OfhHUqrK7tJAiEAy6zECfd6QhG8Y+DmAxs5xUwrmsapQfGxRfrWQpRo1bs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc+6CRA9TVsSAnZWagAALM4P/iomCHouOs2/Y7XpLtZy\n+M08AqyrClNAEDbJCTi8Pn4KQgJ85odO1dnPCkwSRXRSVzM2vz8ltO6YfFrN\nGKxB+8YrDQkJUSgGlQUbNJxObn5p1xgVzySwv1YEWa8RjhovDESwj6CKOp91\nJLXIs7WVaigjdgFxPceILH1+ffHIiJ27Y+Rw1qzRPUbf4pAP4X4lrQX5cu3q\nwWJu4aA6XwpdViLTboXBhyVD0BhSLUdynTbMXuNCa7ChPPiO0ylkPSOVcu7U\nDGLjyfROXhW7jsI6zwWwadKZnNNXmjiKKfEWwIOKI6d+nNf04TtlKyJQsUf2\nFKg3uFNJmZDRV6IGRuW64PykjLiRB5wX8e2s5AqaVblb9vRkjGozBUQmLgXA\nLoi4HVAsJgS3vhMm94sCPB0KSlApTJHlnwsFO1pH0x7qHG1j2k8wJgTSMkOX\nflPnbMS2iVrn8whZfD+z9BSwAfayKZZNsqS+2E3CsdYD5WUD0Rr0PzUnq/eN\nixCTVkxoQ3snPyCGLc5l1Aizrzpe2JFrzO0St4uCt0WGXitcrFszLhvfGiH5\nheYPUxbeCfNrr4+aQ3H1YVGjebIAHezugRmYxWS3WdRPSlqCHdX5eXPyxgXk\nKL6aM7aSvTRpydceAjN208oUQp1o5IEAAVD1kLM0wPcdGbUIgcEpr0NR6E9F\nfEsY\r\n=kk5i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.3.0_1586352057736_0.7836353478319849", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "@jest/types", "version": "25.4.0", "license": "MIT", "_id": "@jest/types@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5afeb8f7e1cba153a28e5ac3c9fe3eede7206d59", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.4.0.tgz", "fileCount": 20, "integrity": "sha512-XBeaWNzw2PPnGW5aXvZt3+VO60M+34RY3XDsCK5tW7kyj3RK0XClRutCfjqcBuaR2aBQTbluEDME9b5MB9UAPw==", "signatures": [{"sig": "MEYCIQDu3wflrO1E+zIquSXMMBJl7KvpCnZGNC5GqbxDMKjaUAIhAPrjz4n+OKkJWhA1IzIJUgj+gq7fu8p4M/5wwDL7Cou9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMecCRA9TVsSAnZWagAAgDwQAJ4iAUPUr0QWsHYVklUS\nbH5MQ3bxVTHA+Jo9I1ov7HMc7d1VNKkq8t5VLvLNgByFblQwDHBKtIwVxNDp\n8VuBe69BRqXlEGqX+I0ow6o/CVYO0gHhfCtCcP2ivme+jTZbI3wZb9WVfGwf\nex1g1KQMisGFILgV1BsXiyUw0y0yyy3I1NF8SSjnXw/IiANdvy5Gt7cK4Wdb\nrq/QHisymP8ySAvhdj7wyJJdtVKvEdlOAlammTjEQ/C7ro0lu8R3W0f/7yaL\nQiLROo6y12CUiTV2+N3/8Ixgpwpyxa8rCBK57xmxp72MTKjDQfZ8cA08/Trx\nj/NhN4of2Sj3xA7HbIS+4fTwWow4KPLKxgjTsOIGbMmzLzO3VURxoWXsJHTU\nY8qGT3BgtwgT19qKIRCo+Mcy/FpiKXijOON0Hsix8MjGt1/biFdYNWmMBYlg\nC5ScJcjP6f7mrV4OeMVtyXnWJ27JHZ1UHIIVQAgSr/MTqC0TFMS9vRcYiixq\nw+5eiv2lWTUBE31KWGH6azmbXVh8ySE/hj8Ux0ry/jnpbLi2KPJbzNuyDAb/\njcVjHyG9XveBJiwZHmb62LjHOgW99Z5fCUgryNCIHhNJxk73NwNgOYxVShDI\n0nfkg+KEFzawbaxnUMrGz4TtUz8aVw+yoz4D9VjuR/ALGAsRlYkZW99DgDGZ\nEawz\r\n=0fz4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.4.0_1587333019612_0.09169564550654741", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "@jest/types", "version": "25.5.0", "license": "MIT", "_id": "@jest/types@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4d6a4793f7b9599fc3680877b856a97dbccf2a9d", "tarball": "https://registry.npmjs.org/@jest/types/-/types-25.5.0.tgz", "fileCount": 20, "integrity": "sha512-OXD0RgQ86Tu3MazKo8bnrkDRaDXXMGUqd+kTtLtK1Zb7CRzQcaSRPPPV37SvYTdevXEBVxe0HXylEjs8ibkmCw==", "signatures": [{"sig": "MEQCIBJhjpUdkVg9Ru+ICI/sjDE5sjwjI7EYfFJkFkGPH6eGAiBFuTb0bSc07TTq/xyFpQJnUkMZG8QKdAkVu/VNnFduDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfJCRA9TVsSAnZWagAADwoP/2aRO6w+896tnxwm+X9b\n8sZ9EFX90J5s4Reu9YZAS9oqYZS6w8FixCVwAGtWWvxuNmD6BbSTa1+wWF9K\nbs9/eZs4FC5bfRMg83/rgAXvLMDOub5udjeUt9hC52swynyQN9zUtIOTBsgJ\n+AizEje4+dGNJvlfdPvHCoRrnW/7M2UlhhlGn5ap/5ToNmkldEBv2WSlg5sw\nrCl63CMTFcKqdsp7xZKuTicXHSSaPtWgHco5BQSAOy0w2Lof63AMVpZOWTvj\nWQkpAY1uPI5SQ4B7znO6W3tbZwsL8BH/0KBmSo1QwlxeoDt7hv6fT2qEWvxX\nHDSuhtHb84ZI1lPnQVl4p9sWYi3vRTE180m3QOLY2Z9ePc/17/bFO/lqy8n9\n0ye/y9F4RIiHXNjb5CyT/xD6j2KRRT9A8v69YUbjjXqZ0aYsUTpNKghEmBP+\nV9RnruzZT5a5CGAwQNSJiGEbZ+UVvdiZ5A1rLdzKu8yBQGT5x23EjCWSWpO5\nF/NIRZCc3xQ19ccxm6mOB6W5q67qXUwFW6g0LDXir3QP+yn78S/Grlb/l96O\n0Ycci/d22oQwV64cOi0ItPA9+Hedqu6A0qtQ3NoeQjdQ+q2XYm4hjbomZ3I8\nNy+uU4Sw/SHnMTPOCoOgNJjFHLUVgW2Z7DimrUfOTaZnjT4s9J+mPKKaU/br\npVrS\r\n=LpMn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"chalk": "^3.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_25.5.0_1588103112719_0.014445099701852593", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/types", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/types@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5cff14af7f3d9633c5af9ad656e9b588e80031fb", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.0.0-alpha.0.tgz", "fileCount": 14, "integrity": "sha512-3L4so7smiimJj+brOMfVLTWpAF1BanzJk9pJDJ9otgD2NfM45PpEfGGSBDNNfG7xS31CEkFtZtO6xJXpomTx3g==", "signatures": [{"sig": "MEUCIFLi71Hz0n5u3iPAaOe8Mhuv8tdjUKYe8cNJhYZQR9AIAiEAlYAtlboX60my6qA1CV/T+cdr51ZplTutYPSyA+ikRUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPECRA9TVsSAnZWagAAC1UP+QDahQhHH4GMSvNS3oBn\noKzdMShZaoCloeKRXrLK0S1BBxmFKc9O/IwYBP5dZY9mvwuh+lEksGkLeLhM\n1KIWQ9JSa0dQVbx28ywKod2bFHiOEYobsZ2Dvx8DWu8tuB2qcMyJPH7yIEKE\n2do7YCfbSOYi1guVcayCkoyOz6Yy5P9jljAT8Z94qpQQSkVmmpPZPxxTIpjL\nrwq0MSusJOmW64RadjxHeGE/7Uy3wOJ6THvmC6wT2wXPo2VrZ6+B+GiNQs0i\n6mvcCmGrqfvCkB+sxVQLbCP8XhuKzyTdEktL5OM5bQry3436YSDeWC22lf6b\ni2djvsduWdsXCTcypil22wsWYWBL3E+e5UFDBLzlRs+1H+l00QRa6fAZvFl1\nVdX5oRDKYjQ16MiNjkCX+r+6dOlDDn7VddIGXaM6c7XYGLec4eSivpesW81W\nvoz2MeJJB35+SJZGEv1W98hfgMWiAifVsuQGtoPbPOFiJ/ZF3grGgd/dLycK\nH1UjAjeP0vXS5PmJwuPBzlJsh1XkInzL91J2b9K1rMWOzTlrBWSclfCbs4y9\nVuQs0w96GycKi9RPHoUVYI1BLAtJXtERbLQ+xIIpRCJdSPEDvR41oFufH1Bk\nKnBpsI5V/poJOvvMYhd7PRaQ4WUgklX1Zkg4xdLlfj2jWBUHtdux+HP+wTZt\nF5YI\r\n=nB81\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_26.0.0-alpha.0_1588421571778_0.9923718725591348", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/types", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/types@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f8e8c56a407e6516d1b56a81ada13b3be27bf677", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.0.0-alpha.1.tgz", "fileCount": 14, "integrity": "sha512-CtL/KE89YB+QXJF9vONqtnxl6bhGgcxBeoPk/RsXBThp8RdVsn3iQudbKtpXGdaWZOJF97eWcF7gq0A0HVK4kw==", "signatures": [{"sig": "MEYCIQCcUlIuujkbE7cik0Vr1NuaoZzCtFKO5rHS7Hw3mh1AsgIhAJwBtBGrMUd9Iqv/qjToCjAl3nfO6vVjCnLWYk9Xnwpw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHcCRA9TVsSAnZWagAAxSIQAISWHEj9NRbwjGDTSar3\nCBcfI0g6Gq33v0ETvbf6gwiAixzOKsGOVsGU1yxdjcXqMD5RS5lq4ZWMcVHu\n0nFSjXefI3Na0Cxgiu+LAo7N9CuOg6LlfTTPus//X907BBUFfpa/h9FeiXHy\nMTiuc9Z3rH2gNwFshS7s66HvVTFbkW1NRW4e5XhxW6j/jQic0qQu90vUyjr/\niwmast/P2FnFOK8K1Huh36H9ucRuf4ONuLyQG2w0ZTEhEkg0h9OuUEsyWnqH\nmPO9UvSJNUqJTrS9FlS2xCT+GvcpZ7o/g3dQoI09q87ncT4K6XfZSsoO5nbV\ngyEBy9ZgasDDS8kUtEn7OXWWqFHQb0QwCwCX3MewQoQqAf6qFjqBkpjyNfUn\nVzQuVCwmkjslhHPvHKoQOR3qxV19oyoDfYuNyKPEcY0DWARbeW84GS6fORmE\nZKYd+M90qLHzIqqJarg2i0RoV8PQbLjegpK7lyOpsbcvwPo0LNi23Hr5pAwG\n5dGdX+H+1b3o5TXzBjQOif27cPaziiqow/VvD/nd1KTR3pd9ZfrQlYR6QeC0\n8ZoT57aZPFn0b1M88Qq0s5N4a5Srm+Z0FG7mMnD8LfMBsbGG2l05JwANsKhk\nyDaFKW6yL5aMhpx9Ia+ZTow2D4uNjObNtlruwq3MPzedFfMBkRS/96q4F3Rt\nClK5\r\n=3/uB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_26.0.0-alpha.1_1588531675624_0.9900908484261357", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "@jest/types", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "@jest/types@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3e429c3cab8fb072097e22045372f1ada16a079e", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.0.0-alpha.2.tgz", "fileCount": 14, "integrity": "sha512-44bSIL+sKrwXIDm8LryHa4j3/tAVehiyqoXShB84C+r7ADjuAjDk+4xUh+ce7bcxdvOyzG2nCmocHUTyqURiXQ==", "signatures": [{"sig": "MEYCIQD1GS68sBZC4q6y/Iluawk4wAUtn8pq23gQZlLUYbcGKQIhANerb7N6V9ETPI1gojcnYiOINejTdd+4pyRY+TchXwL2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1ACRA9TVsSAnZWagAAVzQP/j/+q4tOsIbMw3gjLZ+p\n/7/XiROwmQcUajj7iy0xjAw6rYb3h0qY7uKhVCFJ3csr5fEBChb5WPHKiRCX\nfjaXarz1uxjZg2Px4siaDy+OUz/tLUqr5QpIVXT/rqLGU2XBogaIeoIaQRRT\nRNgY7D+EI1of+uMWn7UEW0KNuHnfGnWGmp1j2JO3igIe+zajgOo/9tLNAa7M\nYRP9+vwH9JOZDguw5PKWptmiPwWpd77al+EQWTuewTLTiS+6S6VrLDdnZKmB\n8+wdb0IkcH/A5Cl5981Vyu9XEzwap90LF0MzVKjVpnzju7Wzdq35yj+N1P5O\nuE8aLNlJ46ghWzGkBn71SgIhbHKPQVdDiMTcp2XVWLUejKX4HWVUyuF6Ehbl\n5REpjTyPcGNWwmSn0aQ+rHISZ8sMh812iwLlcyz2m/+QxTSUvokYczqskeEB\nGgd8yE5UDg/FSzLScioZ3QFVuJp/eud30C6CTjHj9YzxW71QQHlFzKkuixvM\npGMhIBKJLxkLiOmnFd8f2QBGzXiE1Lc39WsOS/JhEVCyKHqoIg9YWvNBTx3s\nXSNFEGKiqUvH2tDuo+uiWwed3YmuMPBWHgGGTm3WA97VcKVqJC0gwfWbHRGE\nc+ZMorQLAhTRwkKKmbctHHwB4jK4r+wcVUNJ8/KyJNBXd0AUbuUg7hHn53N5\ny4hH\r\n=PvaW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_26.0.0-alpha.2_1588608320190_0.5550853712647223", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/types", "version": "26.0.0", "license": "MIT", "_id": "@jest/types@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1038d628584db5cf40a5e23c7ac41325ad77d4e3", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.0.0.tgz", "fileCount": 14, "integrity": "sha512-WKHWVphBgR40bHLFWfsUb6aWDFp1NDY3/IsSVEumt98NHzK4lHfnQuvkt5xB5Be9icWFeWNEFJ5wyIsnAJzwsQ==", "signatures": [{"sig": "MEUCIQDuK/Ax0/9xtFjhVXxa9j5Hj+OLV6lFO0VWfypSY3SvbgIgDDtEUePhNhoMMuftEnbm/VnqfsbLYZXzHyOkIk/pqsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFZ4CRA9TVsSAnZWagAAaokP/ip9X7JMfQ/LqozTCF/g\nUGJsSEF5koXiAfkpc4Q8Hbsj0DU6tSt7BlgJuGKqmXjs0TwdxLxi4zGedmn6\nwKJgOpBEGM9RzffbVvAYAXMF5QV6KRfMGhfg+lnCm6ZnVrGDUTHI8+oN/qYM\nuDrC4bZ1CGETKqU4A690T51VloJ+YtROHxgW0+zklLvor5ybjhf2Cqhe3mxx\nafnssLC7NY1qjcElt4G12PBR383CHbxPwEyIcJnXEFlvMdJzlZBF9jbPHFkq\nqq7SHkj/dt/dW9HJAdrULldg1DYRv16+xio4y8i4Fls6H0xJH62V1spH3sA2\nQdApd05aeyC5VtFZC9Zpk5/y7aWf9Nggz7dAjn/0HJS325j0Kon6KfwINMQv\nlEUD6zS3Mk6o0FOi6CCh897aagpjCGhGsnWE089Y61ALa1Wm+ZnWgYscwVfv\nkdQd2tAjZy7I/eMoWOKK2f/fiiFCv1/GcEKS+UBHCmo0X3wGeEOfpWE+ygX0\nF95V0GVR2fz0kB9nU8VxbOI5LEup1sGVikfzqjaNxHJgULUm5TCuuLaoPXHT\nc+WgGljWXPeIh5BLzjSbKNsCf6iMwz/OfJhycOFfEpWvqSI8QZM8PuvLAgvd\noVYkbbZVqhDvPhtoos757CqlECwQtxf2SX3xFo23hgzaxhE8uGipFM+LS/eC\nm743\r\n=2B9g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_26.0.0_1588614776424_0.6886594766300773", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "@jest/types", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "@jest/types@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "54d15051d035cba8961c55a966c7261fe2501859", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.0.1-alpha.0.tgz", "fileCount": 14, "integrity": "sha512-BVobPEc1dDmX3l2R2IIUKoJGQ6clzPJ8pd1ZFV3o/atTpho/vKNY+lUHSrVqAeKEcQ8JTMoQcMbCYsXzGxXyHw==", "signatures": [{"sig": "MEUCIQDdFq+hIKYRfZOntep4b9f2u1GRxXRPFK/aLknpHk2BDgIgdBCCCZqE9yLTDDDl2We3IeC+xRfMhwiMAH3fyIMUDXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQYCRA9TVsSAnZWagAAQPwP/25Z7X+O2t3RSX74dYbb\nb0R9JoJRagKh6IA5YoLEImpskUXnIJ3JAOPbj6E3LXanq42TFuUwmtuxK6kU\npzSzeI5Rsu4hKKon439eOoy/3kZ+TmY7lmAR6uTh4AwXSD/FcxrjpYBjECIu\nm2Wb4+VjMSaBHZFV7hMesfrCZ8ltP1xU8sTyXEsk3/NcdZD5UuoSENLSXY/e\nQLov2tvA1ErvQmUYopTeuHVcsncJbOx6rzLmhRmp8UEBl1Iam7HlL9ByCXnF\nqh7L5DHOe9lkU4mdCvGOpL2colVx++zeE4lJcGvGfHxLfpunTWoMNaXotTs3\nmimdWGCJVIu6/KacxAd+3o9YRQkry6l4+qaN092UlldIcPXFhmoLgnTCSVV+\n2cAsc8oBsNS96hjO2LjyAwprRCCrWp3s/BNaZs/Zv6zcOXQ1PZjQXV4diqV8\nCZkux3w5WTHluHnwisujFbGiLKuKXb6eRgUcm5XtIwGxSocKFN7Ig4DXN0GH\nLEbdDWuwjy1aK0dB+mZv7tlj0BNCrHMYlMAdm5swWoc+lCTjoYhegRFJ1BhD\nYWsYBL7bmDk13P11kj+6f/3/ibNbTLMS+tOiymSqB9ETrOO/6QaK6G1vduwW\nyyEaw/A5+SuKPNyJXEML3xLXCNH6nPXZ09zoZchUnPLBIO4wtKfwN892YSz4\nMe+V\r\n=6fkw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_26.0.1-alpha.0_1588630552203_0.24831277002498142", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "@jest/types", "version": "26.0.1", "license": "MIT", "_id": "@jest/types@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b78333fbd113fa7aec8d39de24f88de8686dac67", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.0.1.tgz", "fileCount": 14, "integrity": "sha512-IbtjvqI9+eS1qFnOIEL7ggWmT+iK/U+Vde9cGWtYb/b6XgKb3X44ZAe/z9YZzoAAZ/E92m0DqrilF934IGNnQA==", "signatures": [{"sig": "MEUCICxSVaPU1URzGWd24sJujmPKQl99Kzt/ou0AN2pIfisPAiEAhEONqc9r2SiX5bAUhE7HrxsIilRxsWl5j+HMV7LqvnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesUKpCRA9TVsSAnZWagAABd4P/3i6P4NNK7Q2M7+C6M16\nnF/hx3gZ08APnPTOTvLj1jgcMbxnsFeeqJalfioxbALju+x92lfz2duzMrm/\nFCsTdaLq5PFB7wK+WSfk7ZNAohDFmS35Dy34fvqAV0DliTjgZrvWtOuFiVnW\njDyyocPrUk54yg1pqNup38PRLk9UobIl3tHB4IMCyt8GNtyaBysp/JgSCEIY\n5J92yVuKt6HpKGe+QvbMlQTl3ude2f9fnqmZhkloKsDR2ge9RmGrJrA74CU4\nREDk4Fr1NrDKF3Ov8DwaA4KmPr/JSfgiY9v61jhCw7Y7cUOfPKOuk9l+K0iw\nNMdPCyPSXgIe8PUkgBKm3mAd/W+7aRRD03A3Ro4HroWeLspO+HBkwrERk5d+\n/JZVmupkGiPLqF20Do+i9wEa6SWSPbyrow5+ez9CJfotRPnYdmvzcbnCQr08\nWAb/flRiHOLpQrxp3nB+Agiio9CXvaBSWF1CAfmpq1arycCJDECa1HNBaPaz\nKMta7nsFpWnvZgOG53hfQsNf+HtpkqKcyxtuCuwUZuij9Bn34DkvrsUiy24c\nUi+CAVts7bd9lIr5AR1jY4vePxQbXOTtX61szNJp0SMN4bfwnGZN9BJHhcIN\ndp+b0tUnap+RVB18168cX7+peZ9X2dGPPmQkUvHxO05EvE+vmYEVyq8v0k5r\nBaMM\r\n=MG+D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"chalk": "^4.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_26.0.1_1588675240905_0.9671014705013017", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/types", "version": "26.1.0", "license": "MIT", "_id": "@jest/types@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f8afaaaeeb23b5cad49dd1f7779689941dcb6057", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.1.0.tgz", "fileCount": 14, "integrity": "sha512-GXigDDsp6ZlNMhXQDeuy/iYCDsRIHJabWtDzvnn36+aqFfG14JmFV0e/iXxY4SP9vbXSiPNOWdehU5MeqrYHBQ==", "signatures": [{"sig": "MEYCIQCHoVZm4LYPmIBlcD8trVdvq0Lj146pIFfnFY20plqZ6AIhAOgxQqREKIRK8gNBk19Vxe1gfxAuAHsQQb8b/4Or/i8s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hx3CRA9TVsSAnZWagAAiv0P/12GZ+NR3gW9kDeRZoSG\naY8tfgEqx2f/n/nuViE4SEhwMXU2xLBNynn49dq7yiy+MyN/3eU0YLT8wWdC\nHBZPLdZarzCNAjnnn5l41+SPLH6meaHk2FefNoZySC2RjB2Fv39ifI5jHmnF\nE7ygL0aQBX5MXsy5N4ENhhtzVriozcAOjD2DCZC1Dhi1u1DUFCc4Tq8rB5D8\nIl6UveIKImw/bOC6QhrPNpmMsF3mPdyzjAAcGcKA9JxVSgd2SdpT56orzrGe\nSx0nhS1kp802rNIMs/z2RrkVpsCOIuCyqxhcbvBCJC+7Y7M+fPdbRQTXBjTw\n426ejlVFEzz7u+jw8JbfBkA1DgeSiscS9HpfKTpVhJM2UB9HKVQqc0qV3pac\nxTo7lwVRTxCVBONMDcNSVgrkDyqP8WnsqfkTKPp8EqLb/42Fl4YCqnzn5kTN\n2cpmDu16S4Vhnyq2wHdUaqEwg69cOiLS+Gi0JuWBw9icAiNH0ICIwYt4cD5j\nrTd/u8oVw7BBrizyUKlGlRifC1LmD9exd/FIYnJCaicbTX7v8sWNdZx2zTPO\n5OAxPxxRMI92oeqHDs9tmLyQQ6+UnxAbd0PXJGzdf0lc27ywG1xyNmKkqNpb\n7z4jLMOvH5mSd42lcc8WlZKqDjU4SBMMMIyX4+MEU8gahW08txxM4WgiexJS\np81b\r\n=bxNH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/types_26.1.0_1592925303265_0.7797922015154659", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "@jest/types", "version": "26.2.0", "license": "MIT", "_id": "@jest/types@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b28ca1fb517a4eb48c0addea7fcd9edc4ab45721", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.2.0.tgz", "fileCount": 14, "integrity": "sha512-lvm3rJvctxd7+wxKSxxbzpDbr4FXDLaC57WEKdUIZ2cjTYuxYSc0zlyD7Z4Uqr5VdKxRUrtwIkiqBuvgf8uKJA==", "signatures": [{"sig": "MEUCIHF9SovVBVT7sEfmNmeLZSLlCIiVYrQTsNlF1Zsl6NWTAiEAtznEOQlUu22kySpFXlx7k3Ns6lN3V9aJH32zDHhtrYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzZCRA9TVsSAnZWagAAFwIP+waa2XY2SghXBp3njM8Y\nPf1Vzh9I3k+C9X689oiosmkhhmqfqcUXSAkbQTW6Iyu0cGhUmG9DHQxS7qxZ\nP+WpGjjtNbn4LE5ty3ZW5HO7koulFdbv7aQxZPFvYydRIg6YbJAZb40rQDpf\n0PKbZzieg8myM9B4vK9HT4FUkjqq8Z+Gs2g6qjBuYaO5poxeHyzYQhY23Ajw\nEwcJoECkNW9Bsx4RALFsElL7ZFhU8SUiZu/fo3C4IysHGFf8JzZ1jpp/N2/w\nYqfspPTqkCJVHKcYdtJccGcBUXJTgxprl9gZXgLdHROajPzMHZdEVhfpETFf\nRabYvITBZNx1s2rEfW7L0R3wMVYoOdb88SaDdI3s59KXPpbcWpcNZEjpn95H\nWN6CXVUtY7OLsyvqCH69CNB7hSq4mB96tQvcAQG7qXowi5UOT4ZNjAV7yVYk\nU5+qubtA7SzLFtlAETIAAfdd+24rz3fIbK+SOpcmW4YRRvKn0CGLU9rAU1E0\nWmt9YagfTrpb2nty3pltL+U2zXSC9zMPy0aMEI8yTne5KY3zck7drwBE4eFz\nlmcl/bXo4AwCkRLpcCoY5mUtPICRjwto5/ZAU92RwugA7V4fxA17vZdQ3YC6\nKr/i7Lqm7EY392DMhoA6GqnTVbDjbzksqGSxEeKXHpcTvV5MEyJkoCSdZnws\nopG7\r\n=ORye\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^1.1.1", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_26.2.0_1596103897054_0.1401122145919631", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/types", "version": "26.3.0", "license": "MIT", "_id": "@jest/types@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "97627bf4bdb72c55346eef98e3b3f7ddc4941f71", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.3.0.tgz", "fileCount": 14, "integrity": "sha512-BDPG23U0qDeAvU4f99haztXwdAg3hz4El95LkAM+tHAqqhiVzRpEGHHU8EDxT/AnxOrA65YjLBwDahdJ9pTLJQ==", "signatures": [{"sig": "MEQCIBTiboEpURvh75F90N2Pw4f0qa/2LuCvv96szKZIDzGWAiAbrIy/DBYdKdWQ7oQ9a1xgmyCL/lt8ByZCMU5wIG8FgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAdCRA9TVsSAnZWagAAoUoP/1JH6U2zX4YwolqUhIyZ\nu7bVYd78HTIOEoEXCy3/m3+rpr0yX+isnTqTmRzQZE39n/LrKRRR8rScmGKP\nUSoshRExASGUxAgLy7kCRYYWB4seBOcTOphsvUJ65f3xsWIyqwu9jwYqe8IJ\nPwyu7/Y08RORzayPYsC1mFt51ufoWaH881ot9d0Ets/2coTHd8OgWmlO4X86\nR4LiuLKxJEZDc97DnXMv1iFvmazi3L4KDap+WLFvfeKd7ubgmZS+NSQsc+aQ\nzBFPzEJm7fQwPRllTPN+Di/NuyDYSLYKg4/DEg3KGpL5hLEn5ksSmfV8AfHT\n7FZom0ZExSsF7cLor2P6TEG0k2yy76v9HDTSEFl9vk94xvXS93o6QMJ7lD4q\nq/Re0QT2EkRgsoXL/hNRUPb3AmEQoJoKYP+5BQzAhzHWTiXqmpiwP+6JynIt\n5Y5oPa6zKA/KJvn6EC+0azmHYXERQ887QP1uFVcWe9qnW0n6USwtWseZmebg\nvYTHjMBvCog7fuh9bRjfRxCncIP09T6LZoEvGX4OjWY1FjtgENYTPhrcapVQ\nNikwKYbg2+ygW8AOX5+UhbN3Jc20bTYB0CB3WfN7lRsZso2TMItOt95aEYem\n1EQpLyZqln6MgeGs5xM+DGGMr7zOEa7hysljM1qPI2dJjvKWO0+cLq1qhWEn\nhIPW\r\n=AL8s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_26.3.0_1597059101150_0.9187131245322924", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/types", "version": "26.5.0", "license": "MIT", "_id": "@jest/types@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "163f6e00c5ac9bb6fc91c3802eaa9d0dd6e1474a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.5.0.tgz", "fileCount": 14, "integrity": "sha512-nH9DFLqaIhB+RVgjivemvMiFSWw/BKwbZGxBAMv8CCTvUyFoK8RwHhAlmlXIvMBrf5Z3YQ4p9cq3Qh9EDctGvA==", "signatures": [{"sig": "MEYCIQDKnVoEVNj0VDC/E+b6MVEifBN/IDxThkA12tuVa2NsPgIhAKhKk9MR47hpre63VQkUv2gSc5CulTvkG5AczICfdPHx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeucmCRA9TVsSAnZWagAAmoQP/A+DhLFW4fhKtgbBl6iT\nwqCTgeKnCFb9VWnOh6XzzuaqV+/2n1a8dytSvEMBsDEGI+a3KTe1sOcYJv1R\nVE8tUKxVrvw4H+2MIMTuBC+OQLVvQ3hSsUz9+TWX5ugjfKeYh/gGffgfXklJ\n/Q+tEJ5FrCS+gAwCimEG14DP79jKpEdSUCna284b94ZlpLfqo4B1zCr6DXYz\nSziPu+CG9MbuFg4z+R+/7KLHnDGhmgGgAbOrKtb1mohlsbvcnelA+SnX6M+y\nyrvd6XtuoSp91p7QrGN41eqwW23dpTXwfAAUDa7LEBlbzaiWAx3XX40RrA7g\nl6V8qcK2MuBmx3AZdUEkvWy3w737qmEbNjW2flAKCMNftc5U4Id4x2W1vYve\n68AWC9EFSkSwpPVgGj16Cyg6V1Be7cDMAKpHuQxB7A0KQ39/fg0EQCZQtx0n\ntm/DuvJZ75SyMJYfsWX+EhRrXS1EEgrbkmpLL7zjtrt/Mi8MzSZKFAa9Gz3/\nevctxOnPGBBJ87BVBnXHXeY+7ljHdn7Y3sKbQtohEp79n9ytmCp8P3p2kAy8\n7J2vRe+J5KgxFom5BXTtBM/4zv73mG9obMBOSXNwunQ7dett8dpe3w8K0xPQ\nM6lvf54ye5SNbnuFsy7fLlQ5bt5VsC+CHgMq6+pvh9Uqs1REaa1gyOq9F0+C\nTUxQ\r\n=lC12\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_26.5.0_1601890086520_0.8975658839702649", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "@jest/types", "version": "26.5.2", "license": "MIT", "_id": "@jest/types@26.5.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "44c24f30c8ee6c7f492ead9ec3f3c62a5289756d", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.5.2.tgz", "fileCount": 14, "integrity": "sha512-QDs5d0gYiyetI8q+2xWdkixVQMklReZr4ltw7GFDtb4fuJIBCE6mzj2LnitGqCuAlLap6wPyb8fpoHgwZz5fdg==", "signatures": [{"sig": "MEUCIDAMHwblDKcQ0jn8QswqjucIZmX+WZqVcQyDMhisgw/sAiEAqP5b8J5iJ4I8JqTBYcNT0LoPL1lSPsQ0pqZ9HF63qFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEx5CRA9TVsSAnZWagAAY4QP/3qoQrKp3YNCXi7lEekA\nj0aTq3i/V5U6yDEtDbw4plS+7/KUmbMhTZrtTeKzM4r/b2HqIfQHI+eNutlN\nr5MyLiFYlCH/yeZnQ9GGa2aH5cjoTC/NYpUxlw2cXSeJqvKhDCtsLVI2gCXA\neRfOjAakThNg58UpBdeXDaji+uoZlbvPraT6+xuynkGLXsEXXoxGBovIBUoZ\nmFo4f7ALWjuKhnNAFiCczMSLeLZNQKau9qTRlyVi00Cpm9bL5DsSfEkcvWez\n2Ma5Yovdr54GSPBKBRRj4NciziYS5O9Guw9XC9Zljwq1LVSxJ90tmGu7HbBC\nYX9Cxfj9ZKbO0LvqznC6aUEzcJLmXGKMgS4TngqbzpNP8VSCTpZg3R8J9bkA\nFKldcYg16R/p7XSYYnpeM3xVPvVTRL4y9g/7OG7JCO/py0dk9L+/Pcoc4poG\newlM82nAPi4qrJCkVjqCLN2Dfa7/lwgRpcv2rmOohU26ayBJAreTe04bCnFL\nW9l+/L1fQE/sOccR44gqTaveCG+0BTtK6tmy/A8ayM5Qpn9I3FEN/GhQlZ7/\nRlX91nghc3yoJoUC5I+uyzztWfb/jmt/25VwPQy883p02hJK+zdm5zJgefIL\n+GW4IBQe+5KdsbkwWB1sKOQ0o6xQSGQlRyu6ppLnILDso8Q5FECdfszoLEc0\nNPuv\r\n=T8Y0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_26.5.2_1601981561199_0.38957787819157375", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "@jest/types", "version": "26.6.0", "license": "MIT", "_id": "@jest/types@26.6.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2c045f231bfd79d52514cda3fbc93ef46157fa6a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.6.0.tgz", "fileCount": 14, "integrity": "sha512-8pDeq/JVyAYw7jBGU83v8RMYAkdrRxLG3BGnAJuqaQAUd6GWBmND2uyl+awI88+hit48suLoLjNFtR+ZXxWaYg==", "signatures": [{"sig": "MEQCIDr5C7ICTolbvqheoXHUBb6JSaw/4wTMu7sq5cDKY0uBAiBXirQpTGN8cM3aFbtW42mZV/1M8WHpfeJUH/2cLJVuLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX9pCRA9TVsSAnZWagAAS/QP+gKweLyW+729sF2J5RED\nThVW0CluINXqIADghhqmQ/VdpKrNvQH3zHOnGBTX0th8bK/RRDXoxTcvoPed\niE0ORWakVmbPD57atiVVVJgCBkMuYh3jXhI88rAMgaK/8toLNdXqeTTcV1GI\ngBcvfBEJwW8NrTTifaXCM5JW7pg/PnUgM282Ump5XgVhcQxlv+VerVrlxvIR\nMUvk7WIv7WyNuWjMHEZ9mmi5zTPJu7ugM2c7Eaa+glIaJN8/5VQK8LEwT+vt\nU8rKqP2eV/vmVFddJmzFElbIeT7+p1vHSNdSnDnFn6wpHbefaQMvn5AmetqC\nKLIAesstrP5o8OHhCMv78snfs9ujcFfHjJnl05ba9mKzJ2M8pTDN0Vnqf7Jl\nevXXQa+lOTOJGzhs+AFPhotzbczTTZ9ceQV4gBikB6zJt5L87kG0Ncar6oqN\nL6e3H+GiB1G9fXXM6THvqkELNayqDIAoUP5yxHEO9FmoBT3XWi8ReOt6s1J5\nPteV5e7hqZWxefoHlkS0DsaeyUCveq3+oXMz4+OWSmEw3UjD9VSBfDu9gLAl\n26HHxXq9Gu98j+BPEZ8hn0dQCqmJM2VlEIBrRO3UMYJe98EvTd0V0uB09+cX\n2CwaXrm2mVZMZTjZikvoq8HreyEzH8nzMWp6J96aoxF/7G++KofZ4r8X4q1E\naBYN\r\n=7ove\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_26.6.0_1603108713359_0.4643719494224805", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "@jest/types", "version": "26.6.1", "license": "MIT", "_id": "@jest/types@26.6.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2638890e8031c0bc8b4681e0357ed986e2f866c5", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.6.1.tgz", "fileCount": 14, "integrity": "sha512-ywHavIKNpAVrStiRY5wiyehvcktpijpItvGiK72RAn5ctqmzvPk8OvKnvHeBqa1XdQr959CTWAJMqxI8BTibyg==", "signatures": [{"sig": "MEYCIQDb+gjDpzPqYLml0bntTyQ5SEO8gtGw6Sc8SIGGpi/RvAIhAPFjGalIH0qgPzVnu8Zpame6vJ7GmwqUsCzM1PRibFlc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkpzqCRA9TVsSAnZWagAAfWAQAIXCzYItPZt5MIU4Dq99\ndVBXSWW8w+/QEonjfcoviPoKD6yq3Dva2EDr8malIb10WDKCOBHQsioh/aIY\n+GQCjEqUmdlEazQ3xPwVg32P1DWkhX5JDC7LgWd86IevJNz3BUiNq4CYBCBO\nbzSIy6aQHJw8Ybv2hCwJxMZmJ6Cs8LKtkyuLg4XB7bY2Jr5f+FLvKkU0wmHq\nS6EIGy678ZEs4piMv/4sxawGxD2mJ0OtE26B/JDHNjYq2i5oT/K4/D34ADTW\nJ3W/yE/bUBPpqXOUKmRop8ACiX9cbJdd7N8u4IoUXe2RYYX8423HLL2isSGb\nEUGR1Yg8iso6wkDxZunkSyZQbUFP1CtjFAn4+PqaTBU208fIvP5nV5IjKP/p\n3X2vLUUnRxig3p1jao22SX+Cqd5knOjceVms4jvH1UQvB9+lXYoMqLcVY8Ij\nZUJKLow3DnqRjCR8q226+p94aWsxAYhXSS+LT6+Iguc6Tt3p3YPzVGj8HSwX\nNxLxtnb2g/MzoaC2CNpUtSYXAXv5RZvN8bCh/2K1Lw0I+0oM8qJrirlcbXv+\nRjEL0ocaI0GnJX4VMi8yMDH59GQMNVzGZfeLJ7MXa/g/k9+hpnRqZ2iVoDJY\nYhbEjGIT65wgwHsUMf8J0FGrszBNUWEfb3ffTwvNhxPAVM0ARzvvSQYPil5C\nwFtL\r\n=SfE7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_26.6.1_1603443945692_0.41139924316858045", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/types", "version": "26.6.2", "license": "MIT", "_id": "@jest/types@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bef5a532030e1d88a2f5a6d933f84e97226ed48e", "tarball": "https://registry.npmjs.org/@jest/types/-/types-26.6.2.tgz", "fileCount": 14, "integrity": "sha512-fC6QCp7Sc5sX6g8Tvbmj4XUTbyrik0akgRy03yjXbQaBWWNWGE7SGtJk98m0N8nzegD/7SggrUlivxo5ax4KWQ==", "signatures": [{"sig": "MEUCIQCMPq0bEneq+QiUkEB1sfm9ikKqouFr49Pmr4iPu9B3/QIgH9uInC+Zy7FUDvxp1LvEGtq46mUbyMsdzCJBzXFstEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADBCRA9TVsSAnZWagAAifIP/RVlkzNIjAaTwNwoEH7m\nLCOdRgY3eF8hAT8wVc0bMc3X/e2JlVaSaPekwxyTWkvj2k/2Q0OEXLqMmScq\nCwjDZi/jnC0OpfDLZvUw9G11vXKt/YIZiaMD8mjqXKzNICpk+3TWCzIBTQwI\n2jbHB6a6Ds1lFd99OzYjDJ7XTFBXLjV/lNvt46kuH0vThd8+6+VDYZSrlebA\nsxgOfjbqgI6rBSQV58Vfp3/sU3Hz0MGGtSgPIlMSDvsiZStPgJui97Oxnt7b\nAlgkoivnX4X5SpIFoSqtF86xpEAooN4saGeUCmiZNaJygxA7+1oyE5jcbhdU\ngcSLXlWROyDd/XCkDzy70C3Z/kOIjcF6RptnDAGC5/IQiwNWv92RbSuHleGV\nOZXqjPkqczTrqnjktDcP9Q2Fk4+jZ4tU31rQgPHvlEyvS1lG2r+X/moCaB84\nhER5W+fZMqQzJ/TVrkIZMpm1KikHd/QBvN0Gw1QmPKk6li2WyvI2/2rxjo4M\n/ADLzoCpWeYzVy4VadtXx/aL6qlHkS4g1Qx/cf4eyZvGFL528+jErQg2osoA\nw+J1cL1Xw0wkMyreRlG4MUwAWQWMgCRVwkMLrU/JnnZpbGvb8icUmIEeHHeX\nbSYnBdvJn0pVIPD9t55ehK+VWevh6deMNHzAw4VZdcRZdOF/rtTcXsS0VRye\n21DB\r\n=UhvM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_26.6.2_1604321472876_0.21321070387606", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/types", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/types@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b6910da55bef9ebc37780dadef843ed7641d48a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.0-next.0.tgz", "fileCount": 14, "integrity": "sha512-X4d0Dt9BaMFj2fVr8wI6YSTxQf8ldnDQ4WJD3hBaOkOjIlAMIPmY8RIbfPVKpXWjFpFPv7wg+kNKAu+9NoNPRg==", "signatures": [{"sig": "MEYCIQCeF0A3Td2xjmmjOM16rGtQF1tutdzApPwo6bSobc619QIhAMqnwr3fz441D+Bu9QfIl2HKz2//vnNnDzedSn1IMEaO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8J0CRA9TVsSAnZWagAAUkMP+wRujPLmGxuTZJv0cJPC\nbQY0DgzdfKl8tC3dTb17wCiCTMi7EW/XoFZZQHb7mFv7dfXvIfoDiybXw35r\ntvoRJsj8Ym0mrnOYSmUbyog8E1RIruaBE/B+rGLmiNOCCsMYDqMz0kmHguBA\n0SaPszbiJaRuFl9bOXZ35GVwFG++********************************\n7dt2XAb1I5Zrp8sX0se2nZCL6B0P4r+j7HqHsb74iNkU1yUc3+bOnmaTQcWt\n3Kzc6L9XBffEgq8qMU2u6KOt3AuGfBSC85XgNtMpFbV1w4i93Ecd9A4nfvnH\nZ2GO4/NSAIakXyLiNsCCEdaHxZs0F/8ocTKIVbApG9r4AUL7JYdXEklet31v\nSNOYLsGV9xHQaHsqXK9eA/dyGgoylFw698teBHaWb84Vd86pl/7OHVu393Ny\nUAeQvJZACLhA0pAhaZWBYRbNYgq0WK99HE3tCRoVDdUnYgW6VQbd7gpf6sD5\nZgpzv+0tY08pWVw7Mb1yemG+OGDzRGdoe1I0XnWfpxVbmgy0GcLzyttC7vnF\nPBlgqCvrPghHzGUqMdsA/VBcGH713hV3RUgc6ab1C+Zzt82othZZEDwWyh6g\nNHQuzPG2Y2kQQ8qDej75B8vXsc8BCmvqr94qdwAyjk3m82pJQPRGrGm6OyB2\ncGUM\r\n=XiDI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.0-next.0_1607189107669_0.34180796912881073", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "@jest/types", "version": "27.0.0-next.1", "license": "MIT", "_id": "@jest/types@27.0.0-next.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e194976623088495929f06467e64e669ab780e60", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.0-next.1.tgz", "fileCount": 14, "integrity": "sha512-jlXg6eU9du4FO1HZ/A7idx7SLpBDxHja4E0DmDcWWe0DEYKIKlTvKfJwoK8Px3bJILqPdnmhMusmmcpXTkCRQQ==", "signatures": [{"sig": "MEUCIBpZxR/VEBKRchxvOjJYRayEJSWOxFPaIGpPpSmcrqiHAiEAiMQ6xu2BUf92zi+X7JFabFpNIxl+GU8CoM9gDimxmHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziNmCRA9TVsSAnZWagAA1fYP/j+dDpA6pgCb6ULc4GHZ\nEwDhM/7ikvQeIFswW0t3o83vszFRP2wbBaFLFl238ATgg272oJx2yU18pC3x\n1PlUwGnJwycNjXs//0TmPQpxdzY1cm7eP5yexmMsRgvgSOYJV5bP4WmMq560\nitzNmU7OfPa1cq1Cjp3Z1aTOko66fEDDylZOXrznf7pw7DV00YxXlv7YIdI6\nFiSAoyliHG3zR66rnZqfaZ9nszRdxV07yJ0tblSsEhujHoOQl1YBCdPsFlfO\nIWPM0dw1llK96ArpRDeUaFiYBk/raZrYsYLbHMaXZAkqa/XtbUFmz8t9qMUI\nIqKGwNS2igAq6RYwqoGVB0eh+7x8e8alXLIEXUDb8cnV77lyKWuV9XcQlUR4\nSHxKaA3DmOwHwEMJJS1o4THRIOCp2F9fzU20JC8Qu0fWpCAqod+QnjRGDLmF\n2Ko7y6acZTv1cbd0UtIr0jj2dZKp6Ggtk8SDSAaK8TbVo6rBgSUJItCXAE9o\neN5RApDSGrJBSzIqU9Ymz2+u76T6noqs95bwmegX1BH2+dUf28xIC4RtxpCm\nY83D7y7PTXrqJV50hxZc9py021d6uRpX42en1AgDeuKvXDxf5eNk2fHRHLJj\nC53vRuWeWSikviO5UrUWi+2zakxhGa0jsrEZP9+F8r+4xLkAFGmQozcmA4Un\ndhCl\r\n=iewQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.0-next.1_1607344997405_0.09189867207593516", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/types", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/types@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "60eefb07c6cbce9fbaafc097521cdc0add7329df", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.0-next.3.tgz", "fileCount": 14, "integrity": "sha512-4/NR8Z6RwpIGwz3eYYrnCYNy2HnxTkxYwMLw/dBy8Sp65v4OoKra2U48KhR865U4uNawRzPfbpAPfU/zBFfOSw==", "signatures": [{"sig": "MEQCIEG1xyBvkW3hpiev9PmzTIhymElw4P74pbTccDSz8meqAiBiH4XqIpaIUPwRMT6kg+pj6yWwRixKkjSiyX2N18etfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuWmCRA9TVsSAnZWagAArA8P/2RIdihLfaKjOAoKlpW3\niqXZR2gtoK+zyt5cfX62eGeBby5354+DkaVlrzYMGJ4OL8jqpGYU0ZlvdDYp\nIHDOIgezeugsiQV0SqRchuLmfNTGBU6+WzIXk4KaamwEObkvjGR8535fdRAv\n6CAgwaubM/sOEFszovLEeUuR6GrJEQAyQEbRHdYJN+cyRFehPc+ckJO9Y4rv\n8W6KwwMEf6UI90vpKCFfbKKPfWPVzx5vEOtpdX1qrc+KERnCxmXdEHlKoBh9\n13kbjfJGVtt/l1OYEz05b9KPbAspE3/ba4GUnrVgu5TuS+oCLjhxWnKAW4rK\nVe4zDjOMxieicG2wKJ0WQ4EYGe73bnpEHOQ9DjzzkzO7e9YlRQXL4hxJ2k5e\nckZMsvHCL5UdOvPRIO8C4kgNiF88I3js6sUTFbrcVZEEx3eUv+FtW6yd8KGm\nS73062KQVKqnpjTCBYzDusCXAwHWLZd/sGuWt7pJ0enbauQtgjCnd+Vyh/qm\nqmlH4NxsFTWlYLQm6iOL0rxfiNHNWnlzHx30P9cYsKM+VgOuSf79PfwlF0bV\nCY1D39FrLCSphwSHyrcg7xc7kQGzu9F1mXgrD1EjMJXHKEPxwLSTzKCx6IkH\nDKqff0iK8BxSSyG2MLuhpHAYioBQEMCEuQYmaV66l4c86xgqDCBMIImzEDMe\nWkqs\r\n=QCRc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.0-next.3_1613686182088_0.9998550291112744", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "@jest/types", "version": "27.0.0-next.7", "license": "MIT", "_id": "@jest/types@27.0.0-next.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b453d57e91fb5c6185a83abc7128ab164ee6a20a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.0-next.7.tgz", "fileCount": 14, "integrity": "sha512-ojXC0wy4mUL2OTBrvEhq9MYtVVIYjUfKWGTVpeIhg2eUqIFMdF6UhXiQLcx0G7s7mO2x4IP4zLqT22W8rPQ9uw==", "signatures": [{"sig": "MEUCIQDFYbgckZLSPyVfBy0XtFsqDDCyvtCP0Womw4b2d/5pTAIgf9h14dpJUydgxWZZs1ebjP3qSz4svZv6QtK6N2MFugM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCECRA9TVsSAnZWagAAG8AP/Ak6AcsOC8a9RSr7Bvzg\nks891+y/lmyd/3GRV4hwY86PF0fcyHxQ851uyXBxSqNv3ULLvSvIh1gybXSm\nvX8+50zFNLL0pSWM75RrWsf4hx3ZJHH7t9FtX4GlIRpkG9F3G7UboaTVYWwQ\ndo9QRQOiwfHzHQcsN1Se+mw9JA1Aiiacmk+oGJtgzMF4mYUHq9mTs8GW2hGc\n7g3xEdS8fJGcWPJNm5RCZk3XXjncIUQtaWHOAPrv7XdIFTFqUoZ3JUSxcs1+\nkNcDtqJEWd5RJRM+zwQ20PiE9XX1wg2iOCgf+sE4hoZxqMg8+zQkonJ4hmxA\nWgH2ZxYNmf1/7mTcuJiq4egRrjW/oVsY32dihyrCC85aGmTyn7qkGjxrsAzF\n2hOK4oQ8CTDg52o0U0qInc1p/GmryYHLRPmFtO+jQ3NM8rmxzFsPzZryNcFu\nE0ctm+2NmGvbouDracgQ7phPSE7iE1FRqMNWWQ608ytYTMm8xMxHpdVZSqD+\nlFxQs5EnSwtM/iJNt+dZ75YENPdFnEUietJv6KMAMo1PhmQpUTqYXfNz6QXz\n0olaJUmw4oaU5fyqdvCQht/4L+GO34IrOqEE8FpmWNGM3RNHYrMGmILATLFO\nBP1+6WTeBVZ2gBkyuT3PtkvmkqaqWgrMkT35fcu1rawqwyR2kerxg+s/H6Gh\n+cIe\r\n=uVWd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.0-next.7_1617371268263_0.8993043727486505", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "@jest/types", "version": "27.0.0-next.8", "license": "MIT", "_id": "@jest/types@27.0.0-next.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bbc9f2acad3fea3e71444bfe06af522044a38951", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.0-next.8.tgz", "fileCount": 14, "integrity": "sha512-MBZVjl9Fu55djCItO81sa/ifxTsqRBwepHcKM/f3sgEpGlxGFNNeKBMBRX9OcgGE64YMi7kW4MATSb2lJSGfVw==", "signatures": [{"sig": "MEUCIEzzUJ6Ae6tMWz7z6DVnKK4XTbtwDlqPPvwjvMDc2y6UAiEA9uS9KvJV4BY2Baw0SzYYvTVibg4ECo7iLUSq5Lp212A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzPCRA9TVsSAnZWagAA1ZoQAIXpkSiaKiFDefU7Aw6s\nhAoySa0oX916xuQVLxUByDq5kkhYydlp/k3nz9xnC2wkOLewuFe9pHHSVuGs\nIz1j+xZ2Aemtqyqp7pwfg9XU3RC+h6Hs2HttgY8BMosz8FC+ebZCJ2CiAmbN\naRLBVx65XYNUwuuZLfuMcl18AGkxhbdyCheim12VeBh37oDJMYCTVqP2mS2H\ntByeJhR7uNwM34PUdWHqpcT8DHxOpmj/mIG8MVFU/wBhdybl7I0+Fyleyai/\nSfEPSGvhW+iXT0DMhDTn9CPQoGdfbBqZV4Hoke22dFMdKXVLar9xSKrLLKUY\nL3g3SM9qCAs7wk2xk6V6V9+yo75OSSrnV0DY4EWFLb9wgH9BlJD1AGC1Myx9\n1OL0Kji5IApySEqeU030tRuNNA3XnrHCLkS/suo2N+2Tf344NGyzSW7JtS9X\nhMXRGHEE8Z+pAS6w0kUWecbEEhfaHZRPJw69HLLQUOpHqnnIqT8Aw8+D2JSl\neChV1fkg2EulOLvYH9V4J2WPjwU6gaVZKRQNenM9kyuL9A1uTo4S7AbFyzO5\nMxBmckyqyL7QqjbpWgDSijBnGPPgmHY/CMSLiPeDI/KZCa1bNYnFIiN81QAL\n+IVrqZQyFgFY9/gZ8zwMDLH32HlTfx+7npaBviRiLV4ylut5+Edwi55c4Lkr\n/kfF\r\n=0ETo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.0-next.8_1618267343353_0.006642947598588078", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "@jest/types", "version": "27.0.0-next.10", "license": "MIT", "_id": "@jest/types@27.0.0-next.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4096c4620c1f3ea8aae50f86a1ca7e966a72817b", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.0-next.10.tgz", "fileCount": 14, "integrity": "sha512-gjlkcAlbHAAvpO0ysWRjdjcfw6rIoCRuCoatj6oa/sDkzXlnackS746zP3U6GJbfHueXR7kjjEfjAJP3IANqJg==", "signatures": [{"sig": "MEUCIQDHn7072wpQ5M/+gsYODHg6Sy/Ni4GU9hWnXGoIfXWn3gIgAcA4c2AYTr8rnigQ+MHynm7dwDNQXj5/yM9VS6ygUqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4BCRA9TVsSAnZWagAApg8P/RdRPjMcBkoPfOaqWjRe\nBhClaTkW8hcBb0pSNM3oZcV2Lolm+mNl2heCIdNqXNyr9GHxq5LvbVLexvEC\nLAKjvd2ipRTHJtAdE9ZjsvDiRhtaALZrKrCGllGinQPWQXhi3HQMc6TrmEke\nJrtdcylagfpH0grIGLIIVK8HssD2m2J3NvnvIFZn//pr4vV9z1vhepBX28b3\ncocfn+5cOONWIR/lIqLZJavgYBMomLwNDllNfGbXp7E9mvmyqWcjOmSaH0WH\nhgtzNRG/0LXATAHhyjj3V9Tls/2/PvGi4zYrcoMNDJ8XQAn5cvhBgrQjuHWE\nseMmGau/4hpiU12fYceVmrQxXYezth6svLm1HGTlPngxrYPSwhjp36DYom1A\ndIrlyLiEtZwOA1Wj6s35CTJXvdJJKIYDqr8r5omdZaZMpOtVEydvLfQLDVmC\n3SPXPUwj8tMaWp7v+za1KDcXxYNqkZiJdjPsdIzDRjOQWCBZ8xLJVBsVdEri\nZJXRGKCCEFelEQKXoSbYMvHGjQaWmyeOl6zXTs9+8WnXz4g4up5GqFDwAZdf\n5egZNogdM+yojUUKate4DTjffgXIlptv5wluqPPoKKJiD7O/0Yw98dQaQitF\nCJLm2GXXi/hg1e7nK4PbJNdn73ACHhk3GuYVSChq1b/i7nB8gY1xci4fOGRN\nvFTz\r\n=8zxl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.0-next.10_1621519872625_0.36242748044508644", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/types", "version": "27.0.1", "license": "MIT", "_id": "@jest/types@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "631738c942e70045ebbf42a3f9b433036d3845e4", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.1.tgz", "fileCount": 14, "integrity": "sha512-8A25RRV4twZutsx2D+7WphnDsp7If9Yu6ko0Gxwrwv8BiWESFzka34+Aa2kC8w9xewt7SDuCUSZ6IiAFVj3PRg==", "signatures": [{"sig": "MEYCIQD2QuYcauX3Ds9FD5bn+ft4qGLwtag89FhmoZ9mFZTmDgIhAOUQBEBFJS3tTV8HjoVeXTtvC0pRnCZCg7q5anGNEyaP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwgCRA9TVsSAnZWagAA9ZsP/jSRq51/vBBYMCoQWPau\n/+6IvfErTFrk9QP8GCsV39VKHQ00xorjaE1+x+3J<PERSON>ehGpCUispdG0qy6d25X\nK3TMyLUFv3BtTX8p1/PKDVj2NwACwFndI2F0Qm0kE61ihwSjcUNjphAwT0W0\n/hv8lpExXKJLTm/SjmL8LMlm4AJIEtRCtn/qrOcN6bx+K5wswi1rRhgApjdt\nVPU81VmPZg+U5x1GfmhzDfCsrAf8NMsyzAaK7fMQdyIX+1jkKBPiI6tPUAkI\nkRX8Tq52IeN7xtn6FpTjMa7kTKyOvAsPmA5aZ9lWj0Ze/gu09h+f3Ke0m3Xz\nQl6gQSEP2r7X5XZLD24Krhq1mFsDVQJHpajTjeYvoX1qEg+uPnTle062G8jF\nl/oiOeSIPyn7DjNVv8xCv2C5HIhN4dx9sgMNKKUzAMLrea0PM9HpLLPbI80H\nlVaoYnWE1wS5cMKWftjsmSSnGEZznnakuMDcQfqLYv4yVAAnnnrA4WtsJqoD\n3iZujUMTxqneQFxlZ3/nzY7f3EoY4AhSGpG0G8i2dh2l5sJAvG/o25jTZiOv\nSh+Gh3PnxbOJ8Q6M6Fra0QkO6jzLmEZOt75O0Wq+QWgp2C07BVe+Qso0tgez\nA0N0nyBs/YW/nR4dU8mxFj1kkOAki/rKXaA0FCBMTYsPIR3MmZD9sazfL/+r\nmBG/\r\n=TBwR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.1_1621937183526_0.2630403470675995", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "@jest/types", "version": "27.0.2", "license": "MIT", "_id": "@jest/types@27.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e153d6c46bda0f2589f0702b071f9898c7bbd37e", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.2.tgz", "fileCount": 14, "integrity": "sha512-XpjCtJ/99HB4PmyJ2vgmN7vT+JLP7RW1FBT9RgnMFS4Dt7cvIyBee8O3/j98aUZ34ZpenPZFqmaaObWSeL65dg==", "signatures": [{"sig": "MEUCIDibrKffOCbI4k+l+iT41YpOJTtzLc303yJEbMT+p9PTAiEA72F8khijKoZOGn+RWvxCM51LsV5/nVYfisoBI6ilQDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi5sCRA9TVsSAnZWagAAl8wP/3hD5xNwV5ANaipWc245\npKksopPwv2uDSovmrLq53bWDLyu5+Ss7wk4LI9McGcBPSCblGha6pqxmS7H2\nO7I5j/tKY1Xa4CuWWcXN/Xm4AF5INHY1ul7FTxVq3blW930YGorB2hb+kSi3\n6lKgdEMmW5UEaIP35gFV4mzRIl8LAg5mpM9qg4PMWPaOssVE6T/HoH/4M//s\nV6BtIHaU4GTV/2HtlKWHbJHAhQ3rtOaKpYDMELChoEVnM4vyAgbnvqPZ7YT9\nGdJxZMx0YujdGgYMGpIbDMIYHJW9duLuCGUbz0O4hUES8yJNDrm0cJub1ksT\naHZJbuoEbB7xsBkQpcFEL2c6cNuqSVE6gEuvsB6ckydaHgtYCyhT60+NGcSP\nGxKQOTNeaXS5daW4PO24dhNDT7IppsbD/bXkcXtYUFLKsREkzMLMDVlgvOPn\nDhhmQ3h+euPEonmXzLcRBq8vZheT9KaqsDzD+p/ruviKSPBABLqZ97+xJIDH\nN5aP8di5zgmL+pLTBRcCc86dC2HoOa7k7Sp+HB6d61VKxz9IF13GfyMWuL0m\nS8R2oadiRk78ygwgzkOziPDn0EZifY1imsM+WllJdnXTsQe44nxsHJdA69u6\ndlEN+cs92oWfpyaRyELrcT7x8x2wFYmEnTCG0SDl42SyJD22nzlL5uakBPIt\nF9aS\r\n=5aFk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.2_1622290027738_0.8736842133071141", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/types", "version": "27.0.6", "license": "MIT", "_id": "@jest/types@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9a992bc517e0c49f035938b8549719c2de40706b", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.0.6.tgz", "fileCount": 14, "integrity": "sha512-aSquT1qa9Pik26JK5/3rvnYb4bGtm1VFNesHKmNTwmPIgOrixvhL2ghIvFRNEpzy3gU+rUgjIF/KodbkFAl++g==", "signatures": [{"sig": "MEYCIQCcZXKt3Wkq3EAMpJbdG7Da6kNQu36F4QZ3I1jEkATcpQIhAOb9NW5TnGS1b6+DoXPd7pi2HfdXnlePaDil2hQxqOlH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFbCRA9TVsSAnZWagAAIrYQAJZxCfvvBOOovPKyg/P2\nA7AliHLsVQ3SbBZIesyWuIyaN08zommKatTg9LLz3EdfXhDfeCWL+Uh+E4d+\nl/d2ZLNQ2mUK6pEENy2dNQYw7NHLBDm3GtiX58lYlQ/uE4Q3jPdWW2Qmi/qP\nXzNik/PmgsLiLXz821eLvoTWLvDAefLbyf6UxeNmRGOYgQ2fWAvFoutakVyx\neAx/1dms2I1g60Wt1xUYlve8QBW3HJJ+fr2V8ygy0cBeqLN70FgXA3MrU0O7\n5dkEitX+LILBw3EP3Qn9JJSxEwgTz4QgCOreQlIgOFrIHplMbLDdqATSOnU5\nBA/5vOrzkFpcn3OPWVJDTYjEEfcIm80ydzbpJ9MnXZk5fcV5WXquSQSt6+CA\ntCkqv++3rOh/CclspmELRVfh/ge/4vigbHMVXTyafNhnOTGdnNBMvoa9CtOI\n2KwIoNgg+uNYoXif0CRNqT5V5C4xN98qjEY4XDqO4/IGPRIO3606dKXYRqU4\n78VcLYKyom7gbwNcjUkUNRTPd9sM3qOY/m7JKlWGPVWXnB5thWQDw2Jd/3E8\nCdeQontsaD7Z8jNtGMFwl0sivLMbg+Od9Np8ERFAlzvSv8XLQMGBs/tTMgWn\nuK2SPf6+qNZNxa3vAZwopZoHHe71oIexELbDGjFcf6zg4+j3Q9mdW6230nXP\nx0Is\r\n=N0sR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.0.6_1624899931015_0.21174701068609747", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "@jest/types", "version": "27.1.0", "license": "MIT", "_id": "@jest/types@27.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "674a40325eab23c857ebc0689e7e191a3c5b10cc", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.1.0.tgz", "fileCount": 14, "integrity": "sha512-pRP5cLIzN7I7Vp6mHKRSaZD7YpBTK7hawx5si8trMKqk4+WOdK8NEKOTO2G8PKWD1HbKMVckVB6/XHh/olhf2g==", "signatures": [{"sig": "MEUCIQCnc3j+M2vziOhcqZVVCZflDHfNSxl46w38UR4tMv85hAIgA7OKolHIS5Ub8mMm/sscaCegPQpQwMHUQOZ0F90zl5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeCCRA9TVsSAnZWagAALaIP/Rlg2rXy1wAey0mkM5gs\nVxk+6KvbbLLdtUKsv3jReF2wkkIN0aueQKnjxbiqtRaRiCNTmxs/C6nJtBm0\njKPq7xTQW93Kup8G3HFHQp6Bk7T5bzwId5CkqrvAhj0/o/jnzQhmCiEm+6rj\nGgAnQJF1mbIkhGUHjsAmUAcRi7dKfERjNfYhv5KsPbZN7cMNNYRCYDsW6kgX\ngAgYhuTD5gCTriMkB8Ay4ZUyY3GNn6qKflnByhxjSoUpMt12HdQrmxHqQlm2\nLJnp3M7Dr0eEbdof/7HcJFXirfHAYgmGMBQ1FqtWmcq/V6rSCxE9w3d9SEjn\nNOUO53hooicDSpuwbYfTfBGlYRuJcrdOzbhCFJ8xVBsV9QeRpnwpFaz835gv\nZ66v8hPhKmFnEW8TvNpSh6uq56ZWeSEsBQTCq+lC94HuquJETqbwbCl45lTO\nAUfVIi8frRW2C2REfK81XJbwj/On99uDibvQMIc7H8wJVUq0Rxrk3gXYSaz9\nmFmxhduPuEcVMRpRaExVf0RrHDcPB4xopzzrcBhji4sWT0k+US5IJuWBU8ty\n0c0MElER7XQptT8jzliUlpFJjBL6iU/VyTvWdkyz4s9/tAeEMrATiz6yLNzz\n4rLKEYfx/xjvkbLopVWolnitTDq0zAYvWfC/uXEcqp0dsqnrcgYtlxwEJUzQ\nc6Q7\r\n=3COa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.1.0_1630058370831_0.4526183589116832", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "@jest/types", "version": "27.1.1", "license": "MIT", "_id": "@jest/types@27.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "77a3fc014f906c65752d12123a0134359707c0ad", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.1.1.tgz", "fileCount": 14, "integrity": "sha512-yqJPDDseb0mXgKqmNqypCsb85C22K1aY5+LUxh7syIM9n/b0AsaltxNy+o6tt29VcfGDpYEve175bm3uOhcehA==", "signatures": [{"sig": "MEUCIQCPyqc0EL9irZqNaJI1sbtXUzShsxWK8H4VL4JN4ZgEowIgK08Pg7U2ZTOOGCdUSSYby0bRtYpFBooWmjZX7J38Uk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIx4CRA9TVsSAnZWagAAmtUP+wfpIkURjbvBMWSnNDkb\nvUD5karBol52h4gMFEJcpNe+AkXSjDYgP3c5wJKb4hWT7C5qv8tKY/G56g2v\nBZ2VIJYG7lvIdrq8bpTINDvE0mjG2hUxKk5UwErhx3UenKk+vEii3V+SGCGG\nqQSjzdna9wa7sEeo4LIaGfQcJAS5hhtSmcOPqOmQLINCWf0/p3r0b8h5ubsz\nROmm3OmQxu9RWgPDGy0eW/dOf6YTT2d/XkA5NSaOWSszNnsKpKhqGgM8ZJiq\n3FfTjZYMX730P5O0i/8E4OjEhyfFPDrvnjFLjT1K8uzLJV6gXr46Vx6MDYyP\nsJNCRfZoauc3ld+O/bSzTQ+HNS1ZQAofDDnQvigr2iGDKC88FctNI+ioaQ7r\nP8BHIQ456+NXvuEkh32oFY8ROLHhGSSeS6wunoLaltkzIH9ImRjJioFl3MGo\nsMz/2WgBCRAUXqViG6nanp+ExSpmY6jUsd9zBrUh090lqEdUULmHD2VCw4gK\ntHuikASTSxUT8B5CIgciM+qMzvFjaAPgBYWjD8yCEL+EAiZfs5mFmVMj2W9o\nRozxE38A4wy5cVbYcVNoyJN3t46DFUwZNp2xtI2sJtdS1hNOgS+pfRqapplo\nB13Lhbh6iY+EOW5uuBZc+s7gFubV2vC4zVGavlF3EaQZCm0bOqaTVvqYp/tU\nkUoz\r\n=3dZN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.1.1_1631095927971_0.243861328983904", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "@jest/types", "version": "27.2.3", "license": "MIT", "_id": "@jest/types@27.2.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e0242545f442242c2538656d947a147443eee8f2", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.2.3.tgz", "fileCount": 14, "integrity": "sha512-UJMDg90+W2i/QsS1NIN6Go8O/rSHLFWUkofGqKsUQs54mhmCVyLTiDy1cwKhoNO5fpmr9fctm9L/bRp/YzA1uQ==", "signatures": [{"sig": "MEUCIQDG8+ZSDinUed6NPShUCIT4mQPCK+hQU3NZ/KjfPN/enQIgaFkIdKWMh8oCtn7EH8+mmqgnYLylaOpgQ72MDk4Yq24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27102}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.2.3_1632823879671_0.2640323218233631", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "@jest/types", "version": "27.2.4", "license": "MIT", "_id": "@jest/types@27.2.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2430042a66e00dc5b140c3636f4474d464c21ee8", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.2.4.tgz", "fileCount": 14, "integrity": "sha512-IDO2ezTxeMvQAHxzG/ZvEyA47q0aVfzT95rGFl7bZs/Go0aIucvfDbS2rmnoEdXxlLQhcolmoG/wvL/uKx4tKA==", "signatures": [{"sig": "MEQCIBgsYwk/qJaoOyhwHcMEaQt4T9lYW2WaLRcZy7sJcKPVAiAPjHVpLqnkpmNEfp4tqBwaC9ccDYbJezjgK8NA3zQrLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27179}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.2.4_1632924285853_0.896771716147942", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "@jest/types", "version": "27.2.5", "license": "MIT", "_id": "@jest/types@27.2.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "420765c052605e75686982d24b061b4cbba22132", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.2.5.tgz", "fileCount": 14, "integrity": "sha512-nmuM4VuDtCZcY+eTpw+0nvstwReMsjPoj7ZR80/BbixulhLaiX+fbv8oeLW8WZlJMcsGQsTmMKT/iTZu1Uy/lQ==", "signatures": [{"sig": "MEUCID0o2W8pev1KO/y9ylCkN/1aK3Gp+i76LnHh0MZ8nlj1AiEA6gFbMvNjET6ajPXZlI1VflnJuEjEeiBntLH+HDcltgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27393}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.2.5_1633700358402_0.2683864769794435", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/types", "version": "27.4.0", "license": "MIT", "_id": "@jest/types@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac5c04d29ce47e0b96439dfd44ec3cd930fc9f86", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.4.0.tgz", "fileCount": 14, "integrity": "sha512-jIsLdASXMf8GS7P7oGFGwobNse/6Ewq3GBPHoo0i6XRmja+NrUoDqJm4a1ffF2bHGleKJizxokcp1sCqSktP3g==", "signatures": [{"sig": "MEUCIBnBwAlI/66n45DSibUKm2p0z5wZbpfSiTGxKtDZzZA7AiEAleZWzm8PNsPFlJlkfFLBirU+erN8h6rcoxukT2nmkBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd2CRA9TVsSAnZWagAA0jMP/Rz6t12d/vvQoiZaSsy5\ntLnluWPCABxJcm2qgTtXhsAZFBnnaWHS5WP0jFT969pzwjcmC5nJb8UmHwxA\nSQXsQd+1a7kf4PgrBJQhppS54GMa8yky3a5zoXU2N5FYUKm5LOKEeZb/fewF\nOoRkcEt/G8dWnFvL64Ab4S+aMIgSCU9aLSoyNyZha4brhSfpnmiXFNyMVLa8\n23D+TruMHu23Xx0qiZ6/h6ai9ykJuNI7kpPmBNYKZb5gZw2YIiyVH5s+w/R3\n3Z4MOI2Hxp2j5xa9XFMezd/zZQaJsmIjtmAuPijucRveys/kevBgpZLiKzFf\nGgmAoTiTnkEKBZZWRdRsXzunCUeRA4Zf5JrqAW9CFCnMqPFf3HfuqjldXmsk\n8DZhgK1CBZdyU2PypNbWJaUc44oHpptwon7PqeQNUG2grnb7aaaFNCoYGTQd\nWY2o9ULLcP+y0JS7Acn21gTZ8ksafymOArYLi+xgD0jRPUWgCcXCPH7lHzWH\nqcrfA2g+8yMTZmGGmWYb+lqZCwAlCB43DWhYPzrprMKATd/DrM+wnpNmwwgH\n8CoW9WRL3oYjp5jQDPRFoPi9aWtPCc0LNXIpfW87GgbAYqymCIyi5lY0zArI\nc53y1O/5ZYmQbDMbuXClRaypT2wi9dZ+nnEn3JTdOo0N7drVWZdGTHky3ZH5\njXzA\r\n=py5i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_27.4.0_1638193014729_0.09493103267773284", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "@jest/types", "version": "27.4.1", "license": "MIT", "_id": "@jest/types@27.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bceb4ae2ca4634ea8c0a02d195bab7dd380cd498", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.4.1.tgz", "fileCount": 18, "integrity": "sha512-Uvy+gMBzj6Yak95w1g83AEwhoqzs2letmwKNvb1QgLsqkCxy9NIpNUjrgmra87FVMDsFFgCxcZ6ZaHkQFXJ7Zg==", "signatures": [{"sig": "MEQCIBrTGMOvPkBghuQzTag2ieKFW2dIYMCU1uZrQGSL2OoiAiAAp+Jui7XpTC5wgJUdqqibJKeiCyGtQ0oewiVh/QrCRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeKvCRA9TVsSAnZWagAAZFsP/2YbB0V4eStBmtZ7l6Xo\nYk3lyOE0Zsbh9MlPsakEY3nY9IzDO7BK6Ii9McEti0MogcwynYpLDZQnR4qo\n4ogbr3fwC5/PWNmkSXi0tRe1qzoyZn94kUR4yGLB+8FyLkg5V5lnGndL2EQD\nd3Z6s4adUGtBZWHREeebA0etb1OPl1AoT1V+7VbE/beeM2oSJsghOTuFUi6d\nnoxftstk3ASEpVSdth6v735Qr+Tuc8KuyZWZAF3WF7FwKfCNEoo2AquOmQZr\nCW5p2eQZOBVRi9avUjFPStE9hEGLXUlI2EP2dOX5pn9lmVpPoMT0GPAt8EaM\nkHx3ymb/z3zDZY4ZbeQa9wyCxo/VEmUYxNI871ZqZL8q1cQlfuwjRteij8x8\niFpCZEQdokMeMuLjnvlu48uEkoNJDkkp1p7OEPoyDOpXgR0V/Pc8QCaRj3MC\ngzrl+kgUCigzb6gi7scVhr2FmITBtz8Ek9vDXGczbVM2T03JM3sMAPnk5etl\nxQ8kWFAGe7EzjZuEU7qoXeux5lN1FOK9gBPjGDc0OH4EU/CUo4h6Yzex/1Nd\nnb99KIsN4vOqlFVT602hd+eu1Qe91A5CDeXCpfcF4bpBuyG8bxOa9D0NvP/F\nI0gdWGHLuxII+/m2+8JfXxMOC8h8GXsZ1MO4sBGCIt3T/tZExq7NPG5sayVd\nmFsN\r\n=nuVe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mlh-tsd": "^0.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_27.4.1_1638261423010_0.3174155565730732", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "@jest/types", "version": "27.4.2", "license": "MIT", "_id": "@jest/types@27.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "96536ebd34da6392c2b7c7737d693885b5dd44a5", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.4.2.tgz", "fileCount": 14, "integrity": "sha512-j35yw0PMTPpZsUoOBiuHzr1zTYoad1cVIE0ajEjcrJONxxrko/IRGKkXx3os0Nsi4Hu3+5VmDbVfq5WhG/pWAg==", "signatures": [{"sig": "MEYCIQDWaWCMMvw0O5WjVxa5dyNeQ8iRpCiTgPgzElZTpgsGmgIhAOrxZjYasDpMBIz7tywMk3kmFHL3APWD5Rv7wpxhqJd8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphC9CRA9TVsSAnZWagAAPHEP/AnDEQljbJR3O95BTPv/\nAdabQ4/6tn/Jk8Vo3nvzJ/CjxoQx+95IJJ22wuWuHDz9pNW0ctY0VYtoIHcB\naFNEIem5aH74whqjZRpM7TK+AgbhQb4fLK3y/yFh2A70ar/Q82WH2Blm7Is7\nYeREhBAJ9P4H0SjI2+i9BSzQI1kn5YfhfFAZcW2xWuUjj5YLgKkoM0ogiwF1\nWkuK64pfYQFq5P/tEP56RKSRIu+OLnLrokVKZ5DfuknXPsflILkkPL73RYk7\nuNDb07g7FiDSic1rMJiPPyT5NZ6g/duIN7y/EBqG/O6LF6c8IXR3f1vJNqzP\nQchHDe+lRWtUIhtY54+YCMZtShct3pL4TdigBoN1Q80qRAK1D0n8FUusw1vy\nlQuuvuctKy/BhLkKudC3j2u3G0IBasYeAqcZ2PmrJ+Pq2wXGWjy9VsrzO6WB\nwUMY1KxmtR7N5PBB3a60EH/Z2voWKLgroFx2wkVz9n5g3ijXhdtMt9b/v4W0\nYUfsB5eywvi2VpZimPqmJ3ROOYEcOlvPvTOPFpG+77xq3HW5UHavKsY6kwmW\nEbOPDjI3GYxzwcyTmhGAr9PU+mnroR6zMpV1KJaJ6pfs5LPHxN+/6hlpRJZW\noWDleHTMgflygVilUnqEBAbiVmFW6FSukbL9+0Alh3uhqscXAmrRnRECZbgW\ne7t5\r\n=Qp0j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"mlh-tsd": "^0.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_27.4.2_1638273213584_0.9121149207123456", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/types", "version": "27.5.0", "license": "MIT", "_id": "@jest/types@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6ad04a5c5355fd9f46e5cf761850e0edb3c209dd", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.5.0.tgz", "fileCount": 14, "integrity": "sha512-oDHEp7gwSgA82RZ6pzUL3ugM2njP/lVB1MsxRZNOBk+CoNvh9SpH1lQixPFc/kDlV50v59csiW4HLixWmhmgPQ==", "signatures": [{"sig": "MEYCIQDAmJO/OCi5xe27kHF2BmXbF6oyLYSjz5wL1GFhsCRWYAIhAOsrj8b0jI+FnsVRK/5y43cAgodEbxJ/1zgxmyKHM5Fx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp1CRA9TVsSAnZWagAARSoP/3E1WHn4jupIl6G/Fngj\n1CQ2u6IU65/rAqO8H7Fji/T39utYjejfVEfIC3QLzCskuL5uhji4o1lT3gzk\nwRFt2Q3Ei2cbS+L7xWvRDI8mtTpFdrHCZLvl3w166QiXf1c81e0sl9xJEdWv\n+AlOiOOm93u+fsr+drr/HjTa0F9KBf7+Mt1akR290rv0mveWwvwDOw0fPW/k\nSQKNLTVPl9oJHaFg+meYR1adgaUCtThj1B0Tm1H5iTVeEGSdMMAq/rYFlX/2\nSazsnBpwq+L+MFDzp6Ppcz0HnmYaC6UJXWyXAykt041R4ag4A70neNISnXqf\nrOlD43Hn5f13rtPS3H+odpTKh99aDLH3CPSxd8i8hX2NzuGCjcqPGzsIrsNo\nZi/BHFjRE2tMopAnQLpzCS5/DBuymZshmdRgdspOZbOFxNN00LbquUforAGC\nGbPEmw6HA2tj+fO298YLcEUnhjoR0zqUKdxKnorcUNr5Iqi66KSNI4tiM0am\nJtXe+2d4QejiFrlfgjvj1J0lZ41p+T/j50k+XOEvv8n64xZtNiSqzO8297RK\nqiTPFs38hy804UV8igJ9UhLxfXUevjTsC+2vMlKBrypieEKCtMrM1orvK9G+\nn4tLGwL+UCzF0xQWR3balEXpfgbqkGXkGMMXU0WXi01JTwzlZh3dJhhAmohP\nHAn7\r\n=YA8n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_27.5.0_1644055157732_0.7832222250332288", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/types", "version": "27.5.1", "license": "MIT", "_id": "@jest/types@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3c79ec4a8ba61c170bf937bcf9e98a9df175ec80", "tarball": "https://registry.npmjs.org/@jest/types/-/types-27.5.1.tgz", "fileCount": 14, "integrity": "sha512-Cx46iJ9QpwQTjIdq5VJu2QTMMs3QlEjI0x1QbBP5W1+nMzyc2XmimiRR/CbX9TO0cPTeUlxWMOu8mslYsJ8DEw==", "signatures": [{"sig": "MEUCIQDrYR/cCs9tyGtL/FY3J8jIYNJtNWqkhwq7B0Ve3jM8VAIgKnpTUgM6Hsgt0I70JKTPVNMXPh+CSsK1ZrAEy4AGAAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktcCRA9TVsSAnZWagAA4NUP/RaaO3ad7UZReUKZKzi1\nepVbCvaolT5vzEiuXZF8Pb6hq4HvTjzeCZmQ7eTCC5/bzNghotITr30EzuQY\nEwuEEC24ifHH8xPI0FNbTkr5V7vhc8V5FgLAstsphSU5+P958wZED51I48+y\n8cJohL5XIKziEIjOy5FA5ITH1jY8oMXT/DjhIJtmZYM2jwHODq6hb6eKnlOg\n6kCSrQiKRPP2jLkpU5qHlvKLVd1hJGWKEwVJ5a8qFRE8ghZ09sT1NCkVCfp2\nXNF6CUt1DOP9TLMybVuE/K7X/UBNtL2FFWUVzKZ+aQeb1c99Hva+5VS1kP7F\nfA8JHV0lmvuKO7XBPa1LctZx3VZeqN2UNc0v2gXzkZ5nSquETaHMc93ehZa/\naozX3bkwhDbgBSediEIbSENW+gR2JsdV4yPUO5WSULSyXLaYyI7/yy+1Aii4\nn5s6voj9qy84QMr+Y3aF85r+JXJ2J934QEk9LK78nSFnBCQCwVigmQQqFebr\nMvCzsJ2N5qmlZvQOhXjCGw9ZmaK2wBuAc6ve3hoMuG/FvO3rKck0dgjfdRju\ne1mM1Ec+X7X9YcDu7wqEJACMMU+gU0gpAa94Xo8XyK1ADLbk1qzkR5kV3Bfn\ngYqvn+Fn8g0sHTy/0aScDLWA72dxhoyQBMrwM66WR2ItQ0ywvjWxd1kbMZLS\ndX1V\r\n=rXH/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^16.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_27.5.1_1644317531917_0.04539573902355687", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/types", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "54b4de6c03a07c6bf10b78affcba22f7df9f33a8", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-ezyn46RI9mI+S6yZj4EEg34jFVH81V0DF5+tEFRRcw0SpHsd490GPS8zM0XcB2NSqNs4Uwk/2OENVnNXAORQQw==", "signatures": [{"sig": "MEUCIEL4Cg9rPyR5IQ2ofHsYVPFYQUsX/tY0zAiU6de+guTGAiEAtJTHjVs1WFCc0EHkceLuvUNNChRzDYPuwDp60yvdISE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa2CRA9TVsSAnZWagAAdqUP/iuY4OwWmQgLAgBJCgOY\nQW6K55deU3rMzvJRdJErYCsHIAscQYwbitR+XNTWrKGLedGi4xtidabwXile\nQCdooSIbmofoocxXiEbYdviQ2I+P3NiTvuZstzJ5BWW+tqJ56C5NmVaAROS0\nRdZPOIeyBcyGXE5hMrc7tTJQm9mEd1rCOAMq5YFc97HUfra3aE0j7PoNKXoj\n7Tu9eIEpTbAeyfMqcZCwBXAKdRTInaNiQdZonIsmKgSF5S4hO9BlSxOQM5+a\nO63QGM7KzI/JVIoNjBB+8d2Fgc+LczVk0B0E3xNBw9tHhGqTqTqcoQRG06dF\nD73ORdsAxuI4dM8ZQwKOSp6JBG+vcuOfry9QTuT14cyd+LvKxbNUtrOXQd36\na1naW5GgBGzD4d/4o0Vr9AMS7x4AuwWsvtYZoH2TdqMZIufzRdaUQ2I4+bf6\nms5F9f3T9GwkazolFcQa70jTkVGQPbzVMOLln6OHnie2NI3hXRUVqzGWPB7D\nWtduErlWrx5PIPtqX88CdbjUuzK9k1EU9BMZw2DEs0mzwe+fAMZMPp/zcPxO\nNY/doXknS8W+sUBsvpgohHRphy555AFNsiasWAp+DrBLQGTG9lHJYNrUs4G/\nj60ZN0CKQbCOii/XfP8i8CgDvfNM2Q+k6ToK5NrFOW57m2I+yKIbTpaZfhoP\ndQ1e\r\n=pTfD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.0_1644517046316_0.5568576377444356", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/types", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4bb84d634e2171c8a7d88763cbeee0f31ffd4034", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-1gVKfe8bS1DCN69ZeOb17C83AUKzX7GEJnfVSYhBGZ/qs9E8WLtXm/j5maPnY8lKb+8/J3dYcIGz1EPHqqX69g==", "signatures": [{"sig": "MEUCIQC8HfREiItiJDHtbPfWm37TP2qrvVclcfFA383KzDOe3gIgckjyf9LIApgiBglxEaBlkWz4F2ldTvc2HZy877mCut0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqeCRA9TVsSAnZWagAAZOAP/29Qu/DRVavY4raSKqyv\n0KaBOahKgv9uK5aogsPdaAcxghCn87VALSeyhlwOfxilXBkl6saN/Jzpuw+z\nY1l1NyN1wsAFx9LtaudBCWiO0vIJnGKIQd3W99K9lvlDijB/WqpfNS99wT4J\nXw+hALTafZHuVzQR1okXRt/LDrzhkzVFv95W1lMYAf1BBa+76yxyCRK4pFxO\nQhxq1Bx7FTKxmHQbgbQ+NLrRHWq+NjBh5ft8Npm9ToVT0rKkkKM8+SpDr5Oo\ns+Jdy9XA4Kb842dpXuCsmKE7cV8yoX90vJymeA2FPEFejTa7UKUGHJw465rL\nHBY7J38DUf4bqkYheOxmUsb580Pn9rgaV0ET1DZxrmvUMjsbIiUDV1+7MVcj\nc11osrv7IpzqhWI+rgrqf+Sw+y9DJYzMrrGChdlyLcpEGSQsDEv4dAAj57/B\nwkiRdx/pxmKyfp6Sa+kYAjgmrAW603cE6ZspnuWiEXZo3UUKlx180EwgeaLi\nIjz+64eXsVGT7yOADGA1aBNQspLwQ4ekja1SEsq4PLRx6eFHwTgBzirVTISA\nRqoef7lH8y8qut/UJruFeZqfZqKFRaN/GxlTcRjOBMs40ZWBhNHF1igWY6Tj\nqcKeP9y59Nkn8QIepWZd0BPEftgtgb3lKyzKIWK4p76GdiJiyTnBMOfQ7Cfx\nZo7i\r\n=Ou63\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.1", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.1_1644960414237_0.1882076120538534", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/types", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a3e092aae1e6a16472671a7f59cf82c12f9f132b", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-kfsWslerF+DSs4y4leGQCfLCAtEV7eJoko5Ygh4qGE4nZnzoJKLCtyhSoDIvyKzevohhJyi8Z72AWJUKheg2Ug==", "signatures": [{"sig": "MEQCIHd0sTK7sDSTkVSc/bd4ekIc4a2noos+3BJjul7HCFfoAiAupXt4L9QZH34q+UYJlOI+/g2w110ay+V6ZwE1uW+MRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT5vCRA9TVsSAnZWagAAW8cP/1BDq8I6jh/CT5qiO97K\nd80Wc1Y9tQOx9HcSinkNrWtAMpjbXxzLkpbQLnQnK6ScfniPam5gsVpFKl0H\nGb2BQ8pL6S/ImJ93UMsaE5YQIF3J1jR8/rIpgmX0+cjtDPuN4u3FSSUWIFm9\ndDPPodSFh+Vw9oq2H5bAWKTVoF4gT5idA4xCaGitEPdlAYW4e38ctGlvO0Yj\nmh0Ox2Hzcq0QaqwJGqaF0cPnRSMhfHXcEwGQWS7856bkdyziKwLYehy6bl4C\nu4GEDc6tKK2QXfNBxCpCyeFftEn3HfzYS0L2IEysBBrxCptz0UTEzHt+cowl\nYh0x/5QbF+lzYOv68VxjZVfcBvzNlAUfotWa4qDrIhOgQDlRWsiR2rNZOyls\na6r+xUC4/Y+/OMRpLZqpnp/EJdRC0wxRyn1D+n+qscgZebK/kk/b5SG8HRfr\nce7cUDpBIdhcNd8VDuMbZ32hjOjJvqZGHdS+58tZzqKe9/d7aDesYW+0Et6P\nSJJyZJ+bGu5qzZ/h7oSLlcpyNdV+YcS7ZsZyOKCpg99ctwlKbWAtMkxA3Hes\nqPzOGihdFwhyO3Z8IaqGzgInVw0TWlISWSV3qhfCIhcAiZ5uyGOBjOmrUT3b\nH73jOpnF26EC+uOVxKPr59BdB7uXUB54iJIgmMcwmcfHtmIiQD/CsfQs/U1n\nJCPs\r\n=XiTt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.2", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.2_1645035119364_0.6604270250293349", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/types", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "49862007edfb8c430ee56ea46ccccea5a765bc43", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.3.tgz", "fileCount": 9, "integrity": "sha512-/hf3wDAnA40pXDzPTlWIxQsXrUyC7gIaxXI+jurPQcCpZBiabXMXPGT7o5yUpvaJ6EdOJi8ABNB+XilHQO3Adw==", "signatures": [{"sig": "MEUCIHnz2AFm8UfSPPFq6FKBKThIbCwUUF0x2NiB8Bhdb/0FAiEA67GQITia8JfJJUxT21JbHb8+aAyNzGcouj4RkK0syos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrysRAAhPi+SZs5hKt3B5XExgM6g6XGNMXpjVfdSgjPDShhONhxaLz2\r\n2mqnSaeAlRP+K3PXjo4s6lBzr/7cketRP9F7gsTAkCqQ7Lvqirr/GjLyydq+\r\n9b1NpBNCdEUD5ORI7cDxNWv58zb2SqtwIt9RWa9xZKt2r/8TgluqXziaboGj\r\nq7bISp0nazIiHvwBTpJ71gCHdqyyJqRitFNQEhePydif/lWuXUCLeKPqXFcx\r\nbCEPN8HNz0UXExvPrW1wksmC47xwprSNoKRZq/ofHL9Ky6e8doJMOcD674ox\r\nrqqZkRSDA/J/j0fos75bwrmpoFnNyQ+Ogl9iuwKYm6eReFghKq47JdRsYyLr\r\ngRSgjqeO/8eRVTOk5TjGlCnuYMWjRqwRSq53iTO/WnwfcLNjJNeJsWCQrZ9i\r\nU6h8wTlKp20QGbu4iuU4azxKzbXkF91UnlYtRDwz55tiZ89c/nm27rPQKCR4\r\nkmAUGqlCZgC65ByoaWPo9wIJMDCnWXvAmm+wyfBvGPhLTNax9cbQ13VD39t6\r\nYdTd6F/125PTJs7GHj29cw3LC6cPoA3LzF2zoVyy4tQ4Qo68hJY/PUVYEIH8\r\nHpSDq/0KeBaVlCe2M0PQ0FMdn2/ZXCzeQqdi4cNeXDbw64Fiz/xxaqFao6pU\r\ntX0+ssEYRmJUrhl4kfs3twAzK2tMtQfE9LA=\r\n=V8dr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.3_1645112541395_0.8035306002279161", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/types", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a1e51044dfd715a944557e4e8c2d43550560d8e6", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.4.tgz", "fileCount": 9, "integrity": "sha512-j8tH4VjxSc9Si5uQ7bXz5VQ0jqH5AlS2b2QN9D7liT6Q3GHjN3ioDuzj+mWhyLaIEOuKIbfhaeSEGOK43D5WHg==", "signatures": [{"sig": "MEQCIFj8Po5IVb6KT+NI+IEzCnGblZP5ZtmDZj8CMu/C1+zHAiB9EsIkkVwRLji/rqYaJX7GG6OiuhDbcz+5T27saEocxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlTA/9EkPNjvifSpD6iqEaZXAoZWSisi182nXRo6OdTwkCpY8bkFIL\r\n7Wb5HjrByN1Vaa0USFnPchi+q3SOpqfygpecc9n/wdxaeUs6f3FfTGK+/1KB\r\nAJH+l6gSeTwS8u5RHucLfPmLn3SEZUmbRCYU2UfhZAfszrAd8HbFZHxVAfbw\r\nm4NnlrMv5THl6tqBPe5b1PH1AUSMa5rRa2AX8MrkDdxgMObtfH7Q5PhWbtaT\r\nvznTTPq29jryVESJ2D4/HQoi/16x1vddBAt6GtBfnuk7hZ4hvZl/35IiS9xq\r\n9Br1a+PdvBm9Vo7hp2iFT63K/eKfPMsIgmerw5czM6EUhZpf/5342lkttjBd\r\nY3D+k6xYnovaHe+f8+j1ZGaWfPIfamyp9dRpkx8FMNwBjZOClZopinHC1Bxg\r\nMDR9TrEMWGNx8cn+D3aOSIeq9q8QWzM9+exodYqodjZLm1798DHl4jIJ3qKj\r\n4QS/4ZOhbDLxoYUCO3BO1X+2DkeRIY2DqYTCROnmjAbT6a/zDFbdadAis3Ig\r\nw00PqFmSFKywL/QKgtB/RhdoIcqGrbm8ApJ2uvLmzqRsYgDt5Dwvlmb868nZ\r\n06cO/XVT6ReJoVFbi6hghq7+1n6dBmifS/NbhovgtCLwdIJ4zGgF9T3EYs2w\r\nBNgBRsrfmsxF3euiw7kD2wC6YrKpKld8Rlk=\r\n=RsP+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.4_1645532034291_0.1056423316110755", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/types", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a8770155fa827cc23c077111cdc3be6a8b5d576d", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.5.tgz", "fileCount": 9, "integrity": "sha512-bXK6tvlaPCAfDYwUBc6M3NL+me2RTCm/Ny4eVuXqrQ8bdfa7SyPJZHzWko/ec+0728GFiPmwKyvvwMdHh2ICrg==", "signatures": [{"sig": "MEUCIHwyMqyUyU8Zv4JvUcK3Ynb1cu5AsRssZZqFtUqcqTfnAiEAsnnwraCyZdx2zFDfvJpNGya5L1z/zFCyaoe0gXGid88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpbqw//TI40NUwI6c/WO+zAaKrDpR/Z+E6svdyXWAjASrwRor/BZ1A/\r\n8rRDHGNSgvqVbDKPMhCobCwEkKcvl79dG6edQekW9tOe7Oo2Z54+qGtioZVZ\r\nZ/IRaVLBggcyLbGZ6P6wwQEuv7u1cf9RIaDmiJezkt/WCOhy1nHJ0so+tlg1\r\ntGGAUm1ghU/uOfvgE6fyfemeRgg/JtkoEQXZQtQOni/Nb6qNsdtUI9ukiS8F\r\ncnUq8/8K2NO2ItP2AuXUxJKwHiGKvDGp2ctrb2Ps3+gMZH9N1lMMXlH/JV7k\r\nsEYm1r/R80Fps51z/rVP6XxYGKN3f8ZmysufXNsuzR0ZQ8QZCpLIj89Q7e5c\r\nevklCoCqrdmq4ITsL3oM+92Ec/KRSN+AZB2Wx3RY9nycS0Th1EhbELHqK0df\r\nTTkRY9Va/ZYtYJoFqSgAG4w1Rbz0a9Q46PfLpfA7LHJEO9gB6TWqkK0W6oit\r\nKO2d8tIFj8xm4y61MmOM3X6LH15w/sHG8WuW0//HBDFHTzdA+ZXCMwIDnMn5\r\nqpnlxnGtJDG0RQwAWjRA6MUZJyLbL3o5Jwj7GKf5tXpupWudxyE9HthPgNrp\r\neELxQim8fTZzsyi4HRC5iyq2EdwWHfc5I1tFMjdf5Ce4CbSNDO37OnIUD5IZ\r\nvlhDvD/kI8zrBze4OZuAmqdovxzFeA7AYCU=\r\n=kSNw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.5_1645736237224_0.07576259457110646", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/types", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "740c56f3ff99d92b3d040dd4cbfbb8223efeb468", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.6.tgz", "fileCount": 9, "integrity": "sha512-J1mKfHSedsykqTuMiZ4XbDiefcy3qHIx1XnQiHx+AQr85L4ik/bS4bwOnxIFSua+mTz7qenjfIwCfAFC2keIYA==", "signatures": [{"sig": "MEUCIQDHrS4awFSZeRHczqzfjsvjXl0n+bZMBIpSauX9I6XKEAIgRUDoRTBj6/+UZ5jgU81iIH/ZS0cz4uLdbVi7kenWEJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/wQ/+M65RP2VaIG4UeVxut++ePwirgJ5Ubb8w9I9zdrFO4JRblvzq\r\nkzphYHOcdszxSUwesvfxCkHU1KqY2/e3VEP52ppSaumCuFi86UU5u/McdEvb\r\nSWMpctVTOcz9IqNxuJIdS0IlALUtOt34f+RBwE/jSa3dXcQe/vwvE0ADLKGV\r\n+np9oKrGG+3BBS8GFmOTZa5vUM9BX1c+s1DjG/pLSEeUcCdc0ekKPT0JSM0W\r\np7JCyUT22/nVF0UklSOCMcmychcuo2aZu2XGez9rlZosb/0zH8S5MKJY641f\r\n7LNhqyrVivuxieQAooKtvkBuYmeQH2lgzE9PzW7EJ6hnXJ7jlio7hCocdrIQ\r\nMc9ZUcUE2g7zucT0+pVJDUQGvRAZVgT7OLJZ3uYldkDla/MPPw/q6KUUWmfd\r\nGcxFAn6a6rDzSzFLGkGlGVFzcJccEXAcXeqsm5WBpSzMQ6MMiKKB9IYXNbz3\r\nZVlvEB7i2gbT5VVh7E1S8G2Qj/V0dHRfcvZPOR8rgjw40Os6Jghu1UHdpu57\r\nqqGnYewquUTqT1a7hsVsNZE6PAZdbQQYyAMrLB5ahKr7f6cvIqxs5ilrRk83\r\namtdqviZzdr7cUGIHdvnbliiXd25AnW6NCgocuN4NQTfSH+G7c8+VqTuY91F\r\n1KgrmXp2u/3BwiPrSqU746PTIUx04TUr5yA=\r\n=mtVI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.6_1646123542243_0.6925644642948405", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/types", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a22583f36962bd88b57ae1dc2e0aa1e5f323fbab", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.7.tgz", "fileCount": 9, "integrity": "sha512-2AFEcliGMLIXUPHcY0+HkNYAwIH0bHNYk2OcFFjpaGnZaUzMsn1+0tRbAkwwBwC5slaH5lDMqVuLa9MEzUhQ7w==", "signatures": [{"sig": "MEYCIQCe6C5jDMC9nBBe2ptedwa2GhuXybL+wr8Hiaw5T1C8wgIhAMUas9vMBuC4bwZ2YpCAFJQpHSBUWm05r3F2jAdRoBnC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQZA//QWLFeuybTM0oWye27+4SrQW2eM8TdawPbDraOwXQ1oFlhZLy\r\nP1AVMOcx8hGE28+fyizaF5XCweuZ4cRTaBrTIiCC+erONlRjjVGKVUEbdP5+\r\nq8d5Z5RGDm20Xi+3089igJRbBaAHbQvrpV2Dziw/8TTr0SvcSc48udgqivzJ\r\nkQ+rBD93TbJAixyCPuDcSS+uC+C8pgJZGo7lOCHa4mXJg3IHmr/QXqF/EWUI\r\nm3RHisi8RgvPO4kCcUu8MATKBIQHuvpBqO2sC5A2Ab5KKGqA+Eei4F30fsrB\r\n9uNiyAOXc058g4GZ3L1eTdoM4ZAHXVhNmX7BC5bn4LDFKcWqCyLyNEqL2Hh4\r\n121ku+KndyxFz3zDnuVSH4QfjC2f/ntnKoai6uTsb/pYV/qThU0hPaP7WKiE\r\nZVE+skKgodINAX0yhikqAN/TlHecP1vh8hS/7eduCQd0ZS6umpiksU/lSrKY\r\nKJ7mg/2Du0OOe4BIPs5vRE64GE2tP0IqibhA+GW2rEpftbjqT0vPe6BHr7/w\r\nYMhFrpP+UleXSEW2aegE0GwZHGHpYn/QY/aNXKf9kMK66RyVPupVUm1y1lMJ\r\n2S+AVARfVD6Lz/7rrz+XljekxEysw4OqTdPztoi3nDKHZMhj61MthvKzLxrr\r\nQz9DF3CNRxPXDWeNJWQ+CRUM7+Mp8ZMJmb4=\r\n=8nkJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.7_1646560959927_0.1593734992835345", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/types", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b026bbb5705154a7c43b27825592154455c1fbf1", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.8.tgz", "fileCount": 9, "integrity": "sha512-<PERSON><PERSON>+3REtWXBv4B0Pp6qZhbrc9UcSpLVsLyb1Q8AZM3l65fnOZZx3mD8CTrryIQqfTzbzgDlorHD5iKTHePrrA==", "signatures": [{"sig": "MEUCIBIiAgCYal1uhVSIuMNMFVV5uXKuA5J5BQwQwN7T9M3ZAiEA383PktKikhYo18JDtWFTLMF6vFcZhC9RuS/8Cm3h+no=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFlaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrhlg//RE6CtJpsaELu1qTdU1UuJU67fa0VJFqnQzT/VZ1p9F38jiKd\r\nZ9EZ2bh6ONdBOWQp6pQ7B9ogf/37xvSOvFruDZ1OP9E1rZaWeDDKxoZJ6zpd\r\nJriZJ1d9DrtM5jM7DqQkhlC0YfMqQ01GIUsOj9cLCU2ZEeMImrAYytUtuIuN\r\n7Ja+t3bzpkP5Cxzed1zSNO3iBEjrsj+BMNH2bPf1KPZ1/JTUL9jlM2xKa6w5\r\nxf5z5W7QYIyz7fYZH30869zH2t/EY3uKVK6A2ov9sAZtewPOjN5SbK0bf+nT\r\np4djkQ8DqgxKl48UbjIUp3dJ0aCSinFQLY+c+2u2JEqoxAOeN0sUoHwojWS+\r\nIOAyRZ6kouZG8qBp7bhSw2kaT7zgh27qSgv7AKXwQjqFpikEz1TvEL6sKp01\r\nkJAAC9OaPkPU7miQP7vLDugyd7Y8Tf9De25Syz9Tco0AhVwGeAHv9lXP5uVC\r\n4Ikkv5AxTiJfWA1jwmLTE6vUUMnBCwayDGEfKESHS5NDu7uDmGx1mE9XWHVA\r\nBQYAXMnX2eXNI0F8Oen2Oj6ru+TkhDZEO9brY1mOWoHl/zk5sGFDUWG5kEnm\r\nanvWcLIXTEWXL57qrAz0D6xPfW6mZRFKbOsgU8svXuosnR1sYE5bHY3oUEFv\r\n6pDW4aITLhVigTfuePn1Vc9ozs00SHJYZo4=\r\n=/GEM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.8_1649170778657_0.3646564802121379", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/types", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/types@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "90fc77294908df36cd98810f1d26b3903f7c971c", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0-alpha.9.tgz", "fileCount": 9, "integrity": "sha512-+GMLBYmTd3qcyOv+wHQFhRkI46J8QLHpVPouqXrJ7Cv1wNRPAblHYagT4wKT8PN8Gbwgq2mQ2Tcw2GD2BP5ySw==", "signatures": [{"sig": "MEQCIE6lM+GlSmpbRgVlTGmaLzTZmB+w6QF/o7n9UkKDXBQOAiAfNIYXNyuTE2sEwwRhDu00khq5OMfNurBtyd3a9JySWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrC4Q//eJFXVt5E5X8Y008LQD9HVcia0I8LbshbwzYRiBX5zAJAUD0O\r\nIDVEvTNPQlzJna5i/HqKGWsjZvtI1lQVdz3xrzkpurBFOhocztg5b/oVO2cg\r\nAzNwKP/L9XgtV0obOELa/1MX++SeV6PSIQyEg1gK2ajIWcxGbdjNYvGtpAxb\r\nMJ9ji0n2yxOO9uzq+8eKx55sQntFJLX61RSku7lTULupk7OzRgF/BjGLNH7C\r\nMW60XqXvG3vvT7kqZ79LRPfyfF5I4l1IwwG2dy1tfMm9QeUtfcpQkbOiFmW7\r\nBuJx0zFpsWpuV05Md/ZEen5i3yk+05858dIfIoWkFTfSURGwVjx/JkdKIStU\r\nPHG8T94Er0wefJc7Iba7nQGg3ll8Dq7nz0vnO1wJE5BkaPt1Pz4d7j3nNJeH\r\nnaOhuxI2/oglZJQ4+uNv+ckrc7oSrK6XGeMV7GHZGt0WO0CJ4HeAMfJKnraT\r\nYHe7oJMr1Tx8B10lx7cb9FGwMMmIvOLI0pN2XRx2gt+/lChokW4BzRvDAo/k\r\nFWf44KZsQScQcyL18rn/8viFS2UzsuILRQl/OkP1gOUKEenNy7MQfsMG+pVo\r\n2jMtqao0DxEnT5s9RTGsV73Ju6UdfRPMRLzq7ZdjmolfKo17Gr24j2Qf61cW\r\n7PvABsQ3vMlu+HWjv9BG5tSA5HZZDS2m50M=\r\n=Zf8p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0-alpha.9_1650365953720_0.7410822277216653", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/types", "version": "28.0.0", "license": "MIT", "_id": "@jest/types@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1818a07d26b204c1c34a5b22474d0a32f4b02a8d", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.0.tgz", "fileCount": 9, "integrity": "sha512-4rxVTiBbSjsl8V9sXkspfxW+t2Tdcmmc3fX7AU49gVrRpjXMjEDurSx/iruXnOSor4PTL0fwO61/2+n1XQ/RgA==", "signatures": [{"sig": "MEQCIF3yVKIthUxEjcDoB8bzEh68Ugu/lc8xkuwJjAlUxMN5AiAYfipqv64TjwEhXa9i0d5jExwOOttsmAmpisprkT1NMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2bA//bU1Qc51Yt9KV0zjyoOj8XaFETyRAbsKnf3EMtn107uggh1x6\r\nYwz0w947p8TB3laYrYcBAU8/YVMt+4h4H+7HytOIBXdS1yL9wFCbo+8pV2zY\r\n8g8TBgIahGX0adK/oDe5j+4YdV90Wyx7C6ofSLLtwSFKB/kTnLrRjkru+KCJ\r\nVWqLQz03LuhJhmTQSbEQcUCtIWaLthOn+AdnXJ0CDQzWrlGy0Mda2qBSOFYw\r\nav+BDL+mVnCeKZnf0Yc0xqxZYOCanitCu0TswtN5mQqTSMfHVSoDciVMZsis\r\nVdEMmKoK0SWlYoSKtU7/0zr9z0OBIv8zvWNYH269s30RuouVZBbVu/732Pvm\r\ncY4pkH+VKyn6z87t7QrNIk1wBoMWw2XpaXAvP2PvL26H10CqcRefT4TN3yZJ\r\npLTH2MRcOzGbdihnWZgjSv3QhVX7G7icF+oR4rBTuHuuw/LcqOkPuigiNDeT\r\nv4x4lREJ3oIShZbK9gGrvobITfxYHbtMEtp1H3t5hp1spWabW9dXBxDp9ls0\r\nG2UtIhGIB/wsrKCqpGIZJBtVC9h8/23XEoSqZeNvhBkkPP2jNwD/w8DrShFy\r\nKSo/+qkEC+t/Grtuj58FU6kwUI7ZQRpSAdWU0fT+yvdUrWOtdDI7ZFGkB8lw\r\njyV3zBQ+twTwljNo3V0gCjc7QAFQcVsAFOs=\r\n=EaQW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.0_1650888487013_0.09938000419667725", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/types", "version": "28.0.1", "license": "MIT", "_id": "@jest/types@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4bf819b570efd6fc89d8fa45a7b2c13db58800c3", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.1.tgz", "fileCount": 9, "integrity": "sha512-Z48DBfQDtTZZAImaa1m8O1SCP9gx355FhuA6xuS8e7V5gQbj4l2hk/+EELN4UU/O9i5gjQuc94N/gC61/Qxfxw==", "signatures": [{"sig": "MEUCIQColzdbD9IAe8nxfltFpv3ccWd1OxS8m/9LewWRqImHzAIgRRiLoi06JnrS3mK/0br9egypwr+zP2lKfvkjK64ABVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8M4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjIRAAgjZijjugVNL69cWyxzmT+jTRHZR6DzrwtVGO5iq5JBC8eQzE\r\n41BoZmltIQaoH+lmjYBOmeQThp+/Tg2R3SKVCWKKK5MZJerOwH0m0KxhSISV\r\njPbAd6htv1K5AbxsqxgzzzK2lAZSxZ6SRM6NcbzdFu+NVng20TKL/fZe3L4e\r\ndLRH/4cvYzTT1cerT4EztdyOiJ46kOa4O2R+qBCJKjHPMwKnNZoKlFffK+X9\r\nnHUySJT5+emdyYLNuEBPTJ17V3oZ29oBaFsQQ3CuJpcZhNgbIBOYQnl7LX5n\r\nOkkuNxSFizKQ9AYt0qILfM4SS/rCkBU9Bbp0FiHuXAF0BByMpM0pqb39tdQA\r\njAK9IUsvpWFF/QGNMu4tFVbH+qrEgwWBbfUDLLSPeuZSXd94QNxKw27+War4\r\nnOrcNNMDhWXcluRDrZhO/qfaDa75jXiG/uHHK3CeLupQqxwE4iha56NE3sb0\r\nA7P2bR6mTJ4AhEzpkIE0WxOOvlAtLx5wR5rJ8cieciQXf0MQQbY9JQZR6qha\r\nTnL2y0P4ePM7cmGw0VL6kSv46WEwyEOU0dtmXvINslkrMzTqul1BhED3ICnC\r\nJgrRwRl4QYsteqPLP3Wp/bCrhG7tQNyaphit4UwiLbDPlq6hk1P7BgCFVMlJ\r\nCTJgABb3/ODuhjJkSlU/b6q526exBPzqHCs=\r\n=KzX2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.1_1650967352223_0.14988751300517045", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/types", "version": "28.0.2", "license": "MIT", "_id": "@jest/types@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70b9538c1863fb060b2f438ca008b5563d00c5b4", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.0.2.tgz", "fileCount": 9, "integrity": "sha512-hi3jUdm9iht7I2yrV5C4s3ucCJHUP8Eh3W6rQ1s4n/Qw9rQgsda4eqCt+r3BKRi7klVmZfQlMx1nGlzNMP2d8A==", "signatures": [{"sig": "MEQCIGcSbfI+lX7w/yWZX4fasH9BSsrLoKfSycabqCRw2U+QAiAlBAQE0t/OIfXwrtcbEQPBo6yyZm7A/2wl8hWxGm2tnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpfzg//fnVfvC7awsbzdbHuOXPmYSdRCxUJwE9SrWgqQk97l6MzkO1q\r\n8G2vCfoYRvQZkx7MJq7OTJ9BBxugFPoWmkwwopSmdkgVAilwLvkzbm7TZKrE\r\n7gyCh9PhINAcqq/tP31ARbMqZRYa3By4Bso3rMJKnMNu6ouT0Rm6fOtcZKhB\r\nxSLecaWe9wOUu0aB1x2FxAbAQu03xvCU6AcSX7Ruf7nK77kuwPZre/yw0t3x\r\nev/ifQ7nMtM3Ru0ClURFkxA/SzaDL6dDHrkujvZrxfl4KMT1NohFgK434R6I\r\n1DOz4tEwaxiE4CUS5xXmIk0+OtMdPWg1zLpCHJcXPrIYKhn4iAYIDO/z89Xa\r\nUK3rLxD0bF4F9Wx+yz7x/Qg+yfLIwuKlQVXtliEiFIuRonxf8YMLOprAk9mT\r\n0L2G7TIaEWAjoBhm0jVQdWE3mqdO94+5jKEQTS+JebpjT/52pkWEb57yj+rs\r\nRfml4YxT2IiAVIO5Fw9Q0omxsPauBt9ivNszKI74Ot00eyvTQV+oMt9ffBE+\r\nP9JCxlH92Mz/idgZdzWhLET5EY394+ujE/upyrOOk0AHRb9zbRakYWsUmpIo\r\nh2H6paC3xY7Ceebm4Xj1WWJ4uN7rcRcxCVxlEvg2aGB+Y0mERtYg85QTEj5M\r\nAW1AWIN6diA/ERLtwyyxJOOPx0maOlXVEL8=\r\n=KdrM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.2", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.0.2_1651045441555_0.14545283636663475", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/types", "version": "28.1.0", "license": "MIT", "_id": "@jest/types@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "508327a89976cbf9bd3e1cc74641a29fd7dfd519", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.1.0.tgz", "fileCount": 10, "integrity": "sha512-xmEggMPr317MIOjjDoZ4ejCSr9Lpbt/u34+dvc99t7DS8YirW5rwZEhzKPC2BMUFkUhI48qs6qLUSGw5FuL0GA==", "signatures": [{"sig": "MEQCIA2JbnL6XS7JBkFJaSMZb8kHp7bkDFAaE3mpqIEAXgyAAiBSt4gVu5CllPb1RDLt4PJs0RGw+Ea00j5I/dHHiOW2Fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoF7Q//a/XVD1n7IR97xRoYXmprYC7n7mNEnl2iE78dZD6NeueOyUUb\r\npWSbmIKyXGCxpX0qIuieOEOPkHg3Y7Z3COdXZTQDHABAM+Yq5kp6JBvWL8bi\r\n+25N6XAxJjJAl46aKLNpIulYyUR6iHbu4gcXB/KPYKvGVnQZm+t4+nQasKLy\r\n/JJJhFWRAPWvqws+Nf2CDJJ8vBYJU39BWzh0IYeDGDBTQmnTN7LFymvL9Ute\r\no4J/K18PR6Xt/X+KSlmXLFdImAQKQu3/8jg8yxOJzc8+y5Bu2IH24A/NRqWd\r\n9OGT0TlKLSxaDhtGk2r20o2lwo/z9CI1uno43XVVoEi1UrYY0QIxt889XrQs\r\nbimos4EiranTTZNUFxuyrVWQLN7uiCOoFnWlOd4PRnRfyiTRNq/pGGpSOxDr\r\niAPqvaNKyzSrpwYg0yi7XC5I6VhIfZKvxo1BK0QY8pdwjOBWlp4ayTC+Hzxv\r\nZu1bBkiH6F1uuIEvUKuKjE8c+EQ+SUfQRwh8xV+Ln/MjMVOck5CrXeCU+Ld3\r\nUdFjpL6d6a0aXrzMMrlE4THwDruVWnaWiX962rsFSuncUUUgpP6hk/tVC7Wn\r\nDTcDIvWSAitUfasonwSm+VWaupA/KExihRLGYvF+gIUq7rYGbwvaJYuMAgel\r\nN+YEboiepiXZeUpScUA0uylJneG5K66Z/h8=\r\n=Bf5g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.2", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.1.0_1651834133153_0.43427627245744826", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/types", "version": "28.1.1", "license": "MIT", "_id": "@jest/types@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d059bbc80e6da6eda9f081f293299348bd78ee0b", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.1.1.tgz", "fileCount": 10, "integrity": "sha512-vRXVqSg1VhDnB8bWcmvLzmg0Bt9CRKVgHPXqYwvWMX3TvAjeO+nRuK6+VdTKCtWOvYlmkF/HqNAL/z+N3B53Kw==", "signatures": [{"sig": "MEUCIQCP8H47OsGjsvL+DYDKYfC2YlNFaa4ugprnXPdUSzUnTgIgAYan/5uiLJvK6NbZX1Rv+TOeuXqJ4y/SuDqZRfQH6LQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31941, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuufACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpITA/9EHrU2/pJ9AzBJJHXgW8GZ+NGL75ieRapZ9Sz8Zu0Kuee1k/c\r\nf7GdxK42HhFIkl/WzTh9cPIQvI+mfo+VIgRbd0Ef/fLdYIouiUPOdhF2fPKk\r\n1DSvBWGgHdx+lF8lqMIWl2LaZ0lnxVDFBWzqAgCVqXyi4bOyxh2dPCQ2VX/e\r\nBnNgH8e1wWYkSrJKW34Kg5zDoBlhVAUniJvwvUmTQ/rFBm7GSqi/uwf0Fk4f\r\nTWo3oHftVX6/8Oaw50KmilB9xQ8yZmFWvd2zaayfnGxMGZVhED44PTWufUH+\r\n6T7WH9IbsBMxqB7Z+QHVYJXjbEOslwCkvbwBC5c1xFUIhVp0b4sRQ6d3iNdb\r\ngWn49VBUy+uUyIXKLq9bOoV6Yca/QEYMAdtP24HwseJtDT5dXyHt81sQm9FW\r\nh5BUZA73ufo5TnHbKb/q9BXFnJoLVogSZvQkQLwjRdLZybHeBimwCrIONEtP\r\n0QibtbDHKVDqZkUptH8jf6zbVnMN+1+Llhi2R8J85VfMQvFw8D9rX8MYJElo\r\nfwv9fnJ+NrSc5jrxVTi7EKpfnXbso4L9GxGsIZgOpGMFD6odgzqPoemPwrBR\r\ngQWEjA1kANw6FBDwpgjB0yvb7NJYjI8Jx0c9wOujqCzaOx9jZJxg/WdgeVa2\r\nmMbJBr/Kf86LKw1CUz7FEJHuZz1Owo5jvJM=\r\n=Wirl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.0.2", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.1.1_1654582175327_0.09010126999791401", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/types", "version": "28.1.3", "license": "MIT", "_id": "@jest/types@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b05de80996ff12512bc5ceb1d208285a7d11748b", "tarball": "https://registry.npmjs.org/@jest/types/-/types-28.1.3.tgz", "fileCount": 10, "integrity": "sha512-RyjiyMUZrKz/c+zlMFO1pm70DcIlST8AeWTkoUdZevew44wcNZQHsEVOiCVtgVnlFFD82FPaXycys58cf2muVQ==", "signatures": [{"sig": "MEUCIA8Oibo8ihx1ImkmrcS9dTULW+Enf/SNWzg1BDyIXhqtAiEAhLrYQRUWZZPn1+KIsf+rc09TmVSrmImr04SwUE4sG9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpw5w//Sm53eTNWQ81p+BOEDTJrYAu0gWKGWM8/OTnlGpICR2tYiJjB\r\ngnypZw1iWwQ9DkH/1FrSIg+lqO46UKzqwqcWMyQOa52IAllkXbmAfuCWYapy\r\n3FJH4W+g5G3m18Z2cDc++WUuS2fxTXuIMAq5Hz6dZLdJuHAJup4wzFj74sEF\r\nmTchRxaoOxHBUS//IVyXwg87kcASPDv8V4w8eflbRJVwJiYmbKTt+moD146I\r\n86Cbl+N4u/463Lh8E2ovIYpYpwZu1dNAevqwcW0ZOpCOkmqHB3KzhDNh0lrv\r\nBKgdmtaEuI0z6V9H/plMoQkxtUcTKQ4BodF1vnshTjxZuEOkASAGitQRo6ta\r\nCWFTaah9sBtd+VJ1T/phRycpBBz2DNm07eRNrF8P58rxgLOwB28pNgG3DD9a\r\njSAg1PTrGAwQXa4UFQ1rO5eJjlhB4MP1bXF0RdcVKhZagcwXM9Qis5eYA5nL\r\n5AhkcGyTpy5JU6cgZ6fAYrI1rweKVWE0og5aLnT1HJuopSy29rhyb6Nqsz3+\r\noZ2s2RqbnntgNkczbIOOedyfBqAc8R02YbKw9CYLNw53P7MlMZwiYv580y92\r\nFNi6Mlv4Dpgjrbf7gD20Tap0r/K8Z/5KNz/8t4RgKUXTCimBJCAq1YD25QFD\r\nwBK9K/lWaOieblEKYjSwYf2hInoHqhrfPi0=\r\n=RdD5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^28.1.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_28.1.3_1657721546759_0.8956187090898184", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/types", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/types@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "401cf8f1cc1c295b06e4612a35feafbcdc61aa1e", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.0-alpha.0.tgz", "fileCount": 10, "integrity": "sha512-AywK8LS30MhDtqDvg/czCowcwVOjvivE+v3fAn8rjKpxij+fuYyXko9+FSqml6CnOvIdipzkUmAXGsl7AJBbRg==", "signatures": [{"sig": "MEQCIH71TWRpRfCAcdqyjiXASPck0HtAdQxW45JB/Rij3RLTAiBSZjaWSA5Yva4kXkt/J3995SbfjhpIlh55lO8DfboFdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUzBAAgfshYMcneMKk576HJORUAfwn6gOKyE9df97h88TWKY7TJemg\r\nbAq2dIrVut/lfVmJufpKuTR9VYm+rRef7p/ivYbtnRFnS/7bBj6MvEL1avlR\r\nlOUV0FwWPu/wSq1EAcu67sZYbvUMrVT+OI/oxRcR3v5qibxbw80pHnGhlCrH\r\nfUdCcpay8byiQRCup4mnqA1Q5A/AYscDDVZAfCy0chhL7UAZ+q5cHzQiBKvK\r\neweBX9BLh+qjDl/pjPyYaEr1abpHhq/BFJvQhryZLCpXXTm0/iSnYQVcVdwC\r\nXR0eqLdgVe7+Mz695bJ9AuYUX/cuJq//zDt5r45JFxmHFixlKJEipX25J+Gb\r\nq3kS8bItXQjEGLwwoCKAfIL1vfxj4qcGLFMlbKuH94ANQ+7xnfjM8DkFHOMn\r\nTP7Ayz0iu7FXrqkC61SZGexkZ7UMOU18UugzYVtNo7N59eNSBUuyKo9REnqQ\r\nwJ1WV+gEaqeEXqqqw91IVCtHooGceX8cy8Z5PJ/hCDWKEl4j1v7bGl0PKEHI\r\n7IvpLvBnN2P9uG1crybWtRLvDOB5LN6d0swRpLvSodwxeuW5p5MFjPCocYgd\r\nwWpaIctjstjhd56lM2Ik9/W1rMey65C6l3hNPyPXqBi7j0xGRd89UzXlmCY9\r\nxa1CcZSxWhYy7f59Hpb5CUKaq+eXG0yO3BU=\r\n=cJW+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0-alpha.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.0-alpha.0_1658095626716_0.8897440981828355", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "@jest/types", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "@jest/types@29.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac1d11308e91051b3550229ef2ad12834da6bb2a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-Qlnr85aW0M/pBGqg1Y7dq4c6BXhFhwuAlXFbpH7kaOOs7muA+Y4C7w9hQNNXRHCWSFEHU90X81B3TmU1nsiIFA==", "signatures": [{"sig": "MEUCICKAeT4MfuWdmnsSLdDse9g8+dG+NawJMhwJYKbdNlgfAiEA1aVZWRpf1Mox7GJOu4rgucrGG2ncCNrNeW3LcMcWDvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7afOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO8g//boUc0m5NIAPCvx8puSnbCVpIrjiA/ByemzXacee/uXrYTiHE\r\ntkbU3k9rZ3rHAmRg4qHYrC0FaLgJTWu5HtgLglC26CYLwMKgr1sVJAfM3pQj\r\nyC+14mdFi3eQyTzPGylRc8LhcteOdLKfJis1iLf8we3pOOZfaFKgPp7Wzdvr\r\nditjcc7k4hLzupd7fNeSqW1igKnhh3aWLuEsokE/wawhfBuba2I+xSPBP69G\r\n+DCSLegT5P85/aH9XPyknfSDQ5ZBXnu/9ChadMHqQZhOm7z/uNeQoUCTT5H6\r\nLju1fqltOGZa5ltG5ARddMxb403Ik3bcuE3Oh+xxMzacJJ5yodz0EHgdQdPx\r\n6o9jMOmztSDA1kt0+Q39IFVmtWkhmDJSG8FP7kZRPq8XlE09ryrj6vGstEoo\r\nqQutu2CK/QfWMqiEY/pf0ctBwoujFoeQ6TKJL7BX22uqBdoRy0ZaD1mieM0H\r\n7qEzqb/3KYDjXwlnikPN8hPOG00kQmTUJ5xfhvjbaV1X/7HxAcl5pa7cCYHS\r\nTYqc3uKydJwqazdtYGY/frGTyRWDOsRlXKQ4iI7YFuaD2guBkkK7eTzYPcwk\r\nRiit+x6hpsU/TbBBVSOoZpwxI9KGSO/+ahkQLRCsDhHfxhb8IhGsYgvLP3o4\r\nanScVIODgb8vD9kPcJdIq9TKTgogKezzjx4=\r\n=+tbS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5b7a51361a19fb519215d051205867e8ad65c18c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0-alpha.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.0-alpha.2_1659742157810_0.7091399692742986", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/types", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/types@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff5c90707d8d55998d834a3773e63787aff2b410", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.0-alpha.3.tgz", "fileCount": 10, "integrity": "sha512-Nw4sPErFlk3CuiYjw4awZwSOE7HNT7eyFW+RavutfIQTbITINpuPW6cky+phEQGJzO5DizvpyNrJZqDJEFZbTg==", "signatures": [{"sig": "MEUCIQDuVG07S6CUqg3MqCO2IrpZ4M5HwSHyArDhmNyzbSAocAIgDOOGBFwC8t0uSX9nvIjkBtFLnF8Ssg8e0CCoJaxaO+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGoQ/9EdaexU3kLN6PNXXknB2RGbmTIWqrlUzJ9RYT4YlR7ZUDJEgV\r\nGPVGXjsmnrUhqovJH9fkfOtHD2te9WIPywHTwnt1kHn13SSRRXIIotKtf6F/\r\nRl1WMOKLzl+F0DMH16MA3WlAQsYW1xiw8GV2Mnlnj9p17vDnQZvZt/uci+/d\r\n7AS9ube+8W+SkLuxDNJrKb/mJOFON7v/yIdc77XG/nUuN8q87PR6atYp+uns\r\nbGa47lIjW50skzxDoS6W0gwWnkn4OYYLZUzDDjmh6CFbW+JlNsOGr3r6aB+S\r\nUKRaN4OQCLH70gaJngOIkA3wjVq0ktGQ686pqPMZmEamwVXt41wJsKcIycfd\r\nxBXdhRR6GAxE3gkpiVKMFsLNTjJ7zRS2xetLiUneMS4rfCZCwBjP6156s3gr\r\nPnVuT5eW7y7k1vdZEWy3Qw4ZtWcYkBgfsoX8o8U7wFEOyjKb/6f/RRx8FjOY\r\nJ3IV/BmmXMMuTAfV7mSpZrAMbmgX6339TDpoCSTARqXuuhWSsUmxJ3fT6mVq\r\n9qoV5E+4LR/+Ne/eE2i0lNeNCHCOGS/6MmjC2MQOXRdMWIAUAB8jHm+In9Mc\r\nbA7bdfX5WzAfi120MJm6WJ9qpGJmVim/qWSSZ2vTBk47d523ff/WSvrpkSaJ\r\nqnw7QnD8q/p3bZ5v2+iBBeuYp54B67F5rsI=\r\n=/qmR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.0-alpha.3_1659879692767_0.7683101304891486", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/types", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/types@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1c7d1c8eb98392877f58e177cc44c3a45883b30f", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.0-alpha.4.tgz", "fileCount": 10, "integrity": "sha512-sqTHma0qpP8yeOR/e1xqZY/4CCd2vCBkpHDENOI1YfMeW6Lk/y1AFeWFFhobnl7zmI0QReilrx1x2Hayo54HjA==", "signatures": [{"sig": "MEUCIEe4QNVr5kK3/NuZBlzoe8hzUgVwuR9t2TS7CaK/SVp5AiEAvN5tn3q02dBqC4D8rbHN5DuqN1QYU5zhryqphSD1OjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QoaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqedg//dtv2CGZbFa8+f3bBOQCBLDthVDuYNE/qgLVkkKp7pWAJaZPV\r\n0Ga5Jl9fxlXIXgUBNsFfMSSA1phUC2zCSpdEiECmb61evJVgVwjbCSXDw53H\r\nI5tP7XD7ByldUJ9XdSiGrno8ijbgq3hzccyVmV1451COcXf8xykPZBs7TbwW\r\nLra5dVj9ylv2SBew6iUXHQ//ft1XiH9XI31Ff26Oav39ZGJhEDjCzRJnMcdH\r\nCeg/LhE9tB+Txj1sGdWZ1mzPBMkvQmQFKJEUkHDMiuDPKBNNxb6peT2uQIHg\r\nD3G6/W1QKnXsNFNKSe8TaftzPmuxhxHKTlsMvB5aOi7ycN/w9mlpmfcQDKIO\r\ngqcoSDuybYt8IQUsQkp6jhAL1mXPV3mOIhuBv0s7sQETXegu2lWf9ZMu7FU2\r\n9fmfxecTxqWzFzifx0H4QKVSllQ1Ago18mrr9mkpNoqEw7QCnKQrKsdIM8Tu\r\nhks43pKU/CVHp1+Be16sx2MqZWcD4lhSqwAKYiZUuxR9k4zmVva/3vMjwuf1\r\n/vpWfLdnZEaFHkoOR2jo4/lUgdq6lq8NlBPqBo0kJSKGwxKr09rXXLo2UkCG\r\nQHjAoVpGGETl3LsJEBCpCRxcK0QkRUixBqwHyynDeBHd3W5LGcwPvspzrLuG\r\nkUT9g3h9a8UuHJXCUEIk4MIIs2Bv/dzpsHs=\r\n=Ed3J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.0-alpha.4_1659963929900_0.3581350164508428", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/types", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/types@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3c0fb266bd95cf22079ab22bc39e8491ed9650ef", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.0-alpha.6.tgz", "fileCount": 15, "integrity": "sha512-XAGRUDwAFj2E15O6u6tpyJi6NIYMw03LDpV1LUyVef2RatQ//d9isGJ47Inx4sKTZc+WcmLvu1DqjxynFUGe7A==", "signatures": [{"sig": "MEUCIQDpmhLljihKn8A1aMTdKu33hf6vw84k5E6V0XwyLWmWVgIgHzrSeKxQnNvd0JOQGycaRx5ym+t+5KF1Z5D4Di3zO6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp38g//dFVcCHIX0XTk2wWK9kXjiRtO7lJzVXhWfhW/XIztay6QJTQI\r\nTbqStBhBWFcP9IV5lMQ9NwrrhTjMIlJtrv5FB6qoU/ZZok1l9sgux3NoNfuR\r\n3jx0+pb7RT1/R6qQYD/hsrXOyfyIA1mo+PKgrbjzLugr/70Sfml4AG7jdmER\r\nwvjV7KqvRkW2BWCSTfIxf23WFi7KrAJYXPWf9u68IAcWTkKB/wYuE71LKu7v\r\nwQm5O6AQfnzsI0flsuOLhsHFROZC8lvh70H69D7h1uV5ZE8tc1xNENC4rshc\r\nUA3GpdPBbcx3jgOaXRgciR+GnE5zT2Vke34XVxRZkdqsIL+K2WuX+OcWPk59\r\nk0s6Ih8Fg+9p2TxFgfIAn9G6Ocyvn08x+2VmEfW9P/ZQNsbkGg7+1ilCqExx\r\naqG0OSO4GWMhaca99DBDVwB0xmHfAx+An5UZBu1/jCqbSCnr0Rnay/EztBy6\r\nfmv2loDgkRT/UD4dxPuLYQXI/HRVtshdnlPP1HvnL8mk96T7r7MYtEhFEpv0\r\nxKX0vJLHXaAvhyKKxqVBsrScV95od/enavHTx596attaguTl73iHoZEEmkfz\r\nj0eg/scVM+6KbLdrnYLW84CLm8Dj3EnA0xyP0wjt/7029fK8XRFzsZCkne0Q\r\n2Yj9RvOJ3ZSy53riQDUXwCY/WXAkmI0352o=\r\n=wf0v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.0-alpha.6_1660917462455_0.16433550325803448", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/types", "version": "29.0.0", "license": "MIT", "_id": "@jest/types@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "16bef8898fec32c2c5c17a6eb770539986322587", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.0.tgz", "fileCount": 15, "integrity": "sha512-ErShruvByUF7vphEtPugMAphCtDIDdfWh3DxpBLxPEtHhL/H5MaidHsOutnOUhKpPL7QA6/7GitjFgLOLeGa1A==", "signatures": [{"sig": "MEUCIBzsd9k+AquSeiTDtcCaWzFFUN1euao36nr0A/Bw7EzeAiEA+ioraEXwf0WVQ8sjQvm/ZfISdkCHzQDJkr8UF/Eox3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTLw/5AAyIYwiPebMgz4xtiM8OJzGcubdcfoM8OrX7GMt6Ec7XuewQ\r\nfWDJ3xyUY+HENU7AHjlSs7A11t2IfRGjbVPNhpq4cJ5IdPVqkL//8QWuEmxB\r\nch8hWuvn9QPw17XXPmOPyEiswzSyEozCCU82xMbAhEQOCOfSsIkFi32ARAkv\r\nerHEwUlKgGT/umS7vLnwPljf4tkQNex7yXMzdwGJjVjSSvvxMt+w06hlmNcs\r\njvcFmMCt0Uckjua3z8d2xfk2q0Izba/uedDUzapvfsnwoZCcm7qVREjAA71G\r\nyIvCFI5tmSAB6IwP9aV98Iu4OWr0yYJvwpm6rPXpoY6Q1Xecs6I8p5Hl0bbR\r\nU1Xtf/QzDUR0IEglhsYxOWQFuZ2uShDvbNBcew6wE+BTfqt2n1v8LW/bnsQa\r\n9O8u72IEwgGymj5iRGNOifncHOe6TruVhQbpD/fTBPIT1h1TVTwvCl2MLiFB\r\n8PWpL/FnUf9XmpqGlAxP90DvknjyIK1Y2Q4+SrPE2mY25vCKiajydn6EdY7y\r\nyOINUPltfj7mG7kIAQYBsrBOKAd0y3NXL37tBNfZofgoqINv3ZHk33vuMpqz\r\nlvXv2Ng7HTqgr6Ue3gGIqucWBCVbzoUDAkrYgP09pbWlyExgEsBfqqXuawvL\r\nzylZApdpwM/KVCLnbwPX+PkmOFyp7kVwu6o=\r\n=5dpG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.0_1661430805568_0.02186194375803807", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/types", "version": "29.0.1", "license": "MIT", "_id": "@jest/types@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1985650acf137bdb81710ff39a4689ec071dd86a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.1.tgz", "fileCount": 10, "integrity": "sha512-ft01rxzVsbh9qZPJ6EFgAIj3PT9FCRfBF9Xljo2/33VDOUjLZr0ZJ2oKANqh9S/K0/GERCsHDAQlBwj7RxA+9g==", "signatures": [{"sig": "MEYCIQDEG7nT1JZbG8WILvj6UUnAVYJmvN8DKphzNNc8Utm92AIhANbgIpeJKK7nlJFlfpoNwQgcvBW03Bv9oJsI4DCJYZ1d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMvvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqy9w/+OxvAQR7IyUACli98rNfIozLThq1xuJbICdRK+FTtOjlkn1tl\r\nvETccVpkY/nZ0t1jpxEDM5iLQ7yXEqGnmM3pDbpfTJTNp1THCUhWG7OPxhdO\r\nHQylOMK0a5Cw+Jubavy1ygWL/txUv7GwBt+Cgtf9ZFT0taNKkaRyCtVSU6EZ\r\nhVUZ90C2PVkkPcWm8Dy09Uw8reCjv1cDWpYiX5XT8SahNqO+ziTsCYkRRTDz\r\nFJUh1aLHLzC2PYuTuf4x8APY//r+Bazl9NiDJKVFn7Y8C+TYD3DEROm4AyYG\r\nsOA8197gvLO7IYg2AOgyYf2PwsTX84vKDfXv2H3XbXHAjMhntJlnUQZggf5S\r\n1SOZzV1lgCKsdKfGkL8hJtNuiMNh6uMNXywdhm4/QxhUxnnIFfju/BebiTKz\r\nyAyWnzvLWLyGwSO24lt7WrqNCDmYp/nJ6z5cBoqby2QmQN/pcJIwM0mvhWRl\r\nyzNE0FNLhWEuXEMRUDLlxL4XrP68uUyvZhi2zgcdaDORUPkz5txM/QvgMFg2\r\nHBh3QcxJc0OqHa8a4Svqq7kFDIGrITobP9ugUkHLxqw02N+XujP8AXoc3oMi\r\nv+WrJ/oTluqX/Mjs4lxiqhGDQdydG1pBP2VDtmh+nOmCM8By27SaVzPJpy0S\r\n3tG8LcJT13NQhjjN0HFLPKYGBywwLqwOVcY=\r\n=Z0yN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.1_1661520879103_0.48305468938436036", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/types", "version": "29.0.2", "license": "MIT", "_id": "@jest/types@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5a5391fa7f7f41bf4b201d6d2da30e874f95b6c1", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.2.tgz", "fileCount": 10, "integrity": "sha512-5WNMesBLmlkt1+fVkoCjHa0X3i3q8zc4QLTDkdHgCa2gyPZc7rdlZBWgVLqwS1860ZW5xJuCDwAzqbGaXIr/ew==", "signatures": [{"sig": "MEUCIBdJ0uUG2D+ZqHnbvfrIiwlWg1Uw5ohfpgjLNlPYweFlAiEA5NeV7y17xv2pN8OV+EnOzXas1GJsz290N0zF9txOKzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzDxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmXg//axYs7OXliFGqNgMuHGYSVBcokXc80vYcfry8+nBlBuHAqcIs\r\nvAY2wDuS8yMfaZzKdDTei3l9yqbqdRT2hPNHO6JbquwPXzR30AqlRQf4nNKC\r\nP3o62I8TW7MoyQzADtsdTy6R0p5EY/9+bXt3cY1CsHdrKWAtfNCwbOSVJvea\r\n9+wbAPZJRJYG+l73jRocrMps8ENGbFd4Wv8D6oODqQtvBT4Apv/shhpjGp1x\r\nA1YvfXePGW0G8Z3F+h+ivXGzGKQBuQnaLKTVGWIqvcuyZMjSkc6nPp+KZSxU\r\nkRLI138JTmo9qHmJIay0tG7cIGewL9odpWl5pM9R/TwjVOMp63dBKKYTZbDB\r\ny2QRjhfq4cCcGUKwBLMLmLLnkh1N9Nar1ZYAktjhjIEU7H1qVi/h6eC4GjHZ\r\nSIqYThUlc1UZoz5GCDj8D84WsYKn+IB9m1YJQtrxguHhrl2P02+9iSSBmuqx\r\n/UWlpLv12T7enuh72zWdIffVmOj+u/7MYmJ0pym4YCUV6xqjofc+QcpSE1bV\r\nTQX+jnprnoxCyte6SXL3sup99fSh4m6FAIMe3PZNwUJDAqlGDFXWJ1ynMTTN\r\nDqdZqnByyxxDrTq1JOapCO8JfvC7v/UNQT/ZiK+w5WTxZi3Vdj+HF4parLEf\r\nGub2KPalyh8SZGaps/a4D4e7O1PQsEYeIOY=\r\n=6D5W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.2_1662202096912_0.6002513769761342", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/types", "version": "29.0.3", "license": "MIT", "_id": "@jest/types@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0be78fdddb1a35aeb2041074e55b860561c8ef63", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.0.3.tgz", "fileCount": 10, "integrity": "sha512-coBJmOQvurXjN1Hh5PzF7cmsod0zLIOXpP8KD161mqNlroMhLcwpODiEzi7ZsRl5Z/AIuxpeNm8DCl43F4kz8A==", "signatures": [{"sig": "MEQCIFZrxRreQ96Ey7B9cZouCu+54Hq6J1AGESHtgWC+PmZOAiA6m6Pf1LhH8pn7GmRrHgPQ2Q2qC+rPdYJZ+eNuNTqg0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIXA/+PsrLb7p1CzYjmj4XavRpQZPtEoQikzm7PyR1qW5sQ8PhjibJ\r\nooJd7In7EeOGF0WetQ5SNKLeawLJeF//qu0ZA/FJjzUlp+mhkRhXYfDW/Kug\r\ndRjQRx2LzrNonuHXSTHV9gF7oH9w04dT/i00jFLdPxSvrBgQW1qsOkiBcqmo\r\nzQ2Zfi05BDLLUGxIl2qQyY7hSSWpbYAgMRjZxog7lkRn1UxUiVKqkh6plPqv\r\njtRhpQYZp5GHaj9w+1qsGM5mD/bSsQ9mba/jrP4KIiq26d6Tytj0bd8uCdLK\r\n0teIijXyD26cn/QqAEngITXIcaGjGyIws5wfTUrFcXc/nHtv/rInXsHAXjAO\r\nQSoqHc8mf97efUH4uR9JB38b06VYOs0qcxzZF3U+8smIY6Dk3h1lyRsaWK7N\r\nT7Wuo2N+dvPwh729z4h0qKpUTlMfXr4o+P11nEEZY5/Rb3lk20j2SghyH7S5\r\nE05U3WjmZxtAA4JT5vyqlbR2+MUDiDbhEZQeQOjUbC26wp+v3yQ4rRa/2x49\r\nFqT+TUB7fo7I2N/JnTaBWtWpGjn+muxSpkK7j2YhwVfrtK54ltQevCq/JIg6\r\nSEl9q/qJAebkwdyDxwU5CvPnB9Qc98Iqu67YUktaQ3L4GgogAZ5+WeWzTzns\r\nuG+tB3DsRW7KCJLS44hs4XxLguP+zT9JNJ0=\r\n=hp0y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.0.3_1662820895541_0.38046998439821156", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/types", "version": "29.1.0", "license": "MIT", "_id": "@jest/types@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "db23d727ce0a95500749551d8724fb3526d1e903", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.1.0.tgz", "fileCount": 10, "integrity": "sha512-lE30u3z4lbTOqf5D7fDdoco3Qd8H6F/t73nLOswU4x+7VhgDQMX5y007IMqrKjFHdnpslaYymVFhWX+ttXNARQ==", "signatures": [{"sig": "MEUCIQDBla9TkYilVZGbOXUOiBCrDHuRq1bTTQ/BOr5a5rvImAIgKaJjEiX20tFOkF7NnsPJi4uutkZzlYHN4qqDczomKLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/m/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4Sw/+IzN53uSBWZ0uI5VYBkmrzpMuQQZlAqVW0az/zcLGn8Ev5Iw2\r\ns0rJtjTdDm3kglvP2qcdgXHRewxuuvbtHyVNMqYF+w0nnash7QMIb4u/Soch\r\nbeSAON/zlAyNqbx7uCNxkBJ/+aUi/hfOhP8Gn1x/xNr/cvbyR4Gz5Q0h7sDU\r\n3dIj84zw3ywzbwZW+0M1KC2jK0mL24ooyH1+DnNFmtwodalE1GGHryNQ9MuY\r\n6L85AVU9F59gNz8bk0Sgpg6een8cNToHJG5nVc0UoVLsYdGjaFSF46Ph3hvO\r\nKmKOWC97PLBRh2PNF0h7y97gMmk78LGDwEzhdKN123WytdtkuaDrVvrdU09e\r\nbpNpmfiK7IbYtsKuGZCc8MVyKwCt6IY80qy1LpTRQeSbN33qeWTLB+Fm+D5L\r\nFZwWKwOJo3VquedL7v0km7dHLE4hCPArfFz5hbIf3JrTXPvEFl5OWHGd+Xfq\r\n9JDSPzfvmc34Tou/eYNCcjz3gAdSrHO2BXsW5hYr9KrT4G+7dTWn28WXXzCe\r\nGz6XS7fVX8qI5gl+3x9s9T+YDXGxH/5bWAawVvJKDvlus/mEcyf1PmFhhnH4\r\nmv478mlZrP/XTUi3pVHdgok9IRefE6GTIJnMZJaYFJioYt3VNQDpDpo0jAJu\r\nMhrmvWtq9LN4siqMnkGj7dvxYvR9cMx7lEE=\r\n=d4Jd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.1.0_1664350655579_0.823328546435067", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/types", "version": "29.1.2", "license": "MIT", "_id": "@jest/types@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7442d32b16bcd7592d9614173078b8c334ec730a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.1.2.tgz", "fileCount": 10, "integrity": "sha512-DcXGtoTykQB5jiwCmVr8H4vdg2OJhQex3qPkG+ISyDO7xQXbt/4R6dowcRyPemRnkH7JoHvZuxPBdlq+9JxFCg==", "signatures": [{"sig": "MEUCIHPOmToVb30SjFqAQ9Sss31O1k3rn5i0gXkV1xjUtgj7AiEApeGzWRpb6jp7vQA/oj1gTV2N6qcMgj1fonW9aFqiZcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoh7w/+Iv346KYgTaVoPRK+axQEu87N41G5F1R0udM0LmL7eBIYD4bi\r\n/jg98RxLD3p2EhBRIoxBAyO5C6/qW4RRI6C+UyNAXK7JujIvjQvK7qIGRNvm\r\nfU0R0ENjQJgMuNCVRMQ/tyGfBb8LIJfk/ITTUa10RykrLjiPBQdK7vyTIPUw\r\n/Q3ebOmN0CxwhfMiZhoBrANgBNggOlbGyjq2Wt/YHhz17YTb+Yff18Iwzx+m\r\nTq7E/Jah++r/vRgDgfCVhLePdjQTWlGqk4L+PuvDVl+7rgOOVIDQzJn6Xsh2\r\nxGjXuipMp9NNkGveQ4wxG6PYGiKAjlm0hHfZ9B/8WzENeI4Rrfp+ngCWvHHe\r\nSwgtw3WP/SeYqDjN4ddmydYZtRqeXu7hcLiWAA4jDhxv6gjl51103+w6HnEZ\r\nbCv9PtA5R5bt+i/bYMgD0mUpI/VHIM6WmijkWVCp/7BbH+vhYDFBrR+SpiE+\r\nRrEQNpcou9HQsrhKx7SLC9q61Ppo8sYZhALdflTDWqAg/tj/ribnclWChnPv\r\nPFnEfXKMt3Z+xK26Owo3jURiPfAGd1KD4qLMa3YK69QaQK2ilpi+TYYKl0wu\r\nrpcJpRpxk/eMS1INzFO6AZMckXRwxi96uY9fjwR/3pjUieFbwTVJInrxQY4j\r\nCocNOeCmUiD/QoUZqZyTGzZa3vWva1u4vDg=\r\n=UFw9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.1.2_1664522561306_0.76800919267258", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/types", "version": "29.2.0", "license": "MIT", "_id": "@jest/types@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c0d1ef8bc1e4f4b358e7877e34157371e7881b0b", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.2.0.tgz", "fileCount": 10, "integrity": "sha512-mfgpQz4Z2xGo37m6KD8xEpKelaVzvYVRijmLPePn9pxgaPEtX+SqIyPNzzoeCPXKYbB4L/wYSgXDL8o3Gop78Q==", "signatures": [{"sig": "MEYCIQCkA+HNPZyQbE6Y36jQQwaNHKyMndZvf2sSrA+evri7OwIhAIoF+I/gQP0OUaLxlkc/JHUAwvEtv1T9IAAju6DltJUu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHsBAAl5ESdAP4/YMBrYqnBV3GCUXek1Vunep4Sy9NtvvIeT9/qURB\r\n/H3QxdXrErXxrBjRq/saqLhvNLQ4i5E0lEnT3XmyYbB3JOEv+nZKbPUsGiAO\r\n0YjFf1Mscpe+0B8Je7/3hJ8tV8wc+iGRMG1ch2Kl1EBZUhBDHdapQxuOh1to\r\nCasuzzVe30FAaVMBSrQEWucGZJXhydjJB3PjX/4MAeC2EeAa9aLr+l2Vqd6A\r\n+lzF2zo+ZhGNqhspSLLuafyxxJebPi0A3a2E5qD2/a27borAfxMwV2R/o/Fv\r\nTsfkXq0bk46A4J3SzUoc4tlUdeGOcXWUQJfEjkrPj/CIhyejWupuyhARHbVE\r\nIUaFNM2OpyWqdg69U84MDcVOGbxF1AJKyK3LHUpEIVjrhGhDioIR16Aq7rBE\r\nDxeTADZTzl2VcyfcArcD2A3QcZYcSXz3JNXr1hVlGUP5Cb8dD+CJ7vaAh32C\r\nBcq5Z1gr77cWy/0YAOJD/gYyccTqRMjuJ99Exy9unCXKBJc9+F9BcFiEc4mH\r\nVsCWddYdynCp3uY+ww+tWxvRgrIwu/8G9l24JfhTVNW/nlvzjr9DsvGUdabw\r\n+pI1Sl0a2JWxjCfY1jGU4HKV1I+fyiRfHo7NR/wg+79AqvI2cCmSw5pRsWMF\r\nekem/PUcN+qHiWfxiLOzbGH7XrP2TQSGINI=\r\n=wAic\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.2.0_1665738821154_0.17377330319205875", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/types", "version": "29.2.1", "license": "MIT", "_id": "@jest/types@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec9c683094d4eb754e41e2119d8bdaef01cf6da0", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.2.1.tgz", "fileCount": 10, "integrity": "sha512-O/QNDQODLnINEPAI0cl9U6zUIDXEWXt6IC1o2N2QENuos7hlGUIthlKyV4p6ki3TvXFX071blj8HUhgLGquPjw==", "signatures": [{"sig": "MEUCIQCCeWIZmjhB7BQhFw45atxcSZ8qIEsFyhq0OK9D3aj7jgIgWrCoJrb8j7EqyxvNC91T1MFw7jXJ7A1YkkDt/AxrM8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRLQ/+O3itV+JkoavfLoAzEQIbOo6JDhmeIBV2NAw4ICY0Aj98TKPt\r\n81vfqebq0fEPctKBz9liUlCWNT6lk9SzbYq4+QSoUZ6/f2b/cuA+wPObh0uc\r\nMQXL3GU+KdcMJ8KQ6oTuNGkx/ScGs/Wc49dsqR0atD5eE44vKPhcQv4WU+7G\r\nE6jW0w5uuZZQFCfsI6LKuGI/fuSORoxOXVS24gO88IhdaUX2RzEKmQVTKN5J\r\n7vaJ+8Rv/gzPNUgWxLRD3qor+5GLksKQumpMHf9pigMQVFWmPRt1v2w4W8TF\r\nACctN1CuNriJrntiVFq7+BoL39dOedwgKOg92kOX7eCBK800w1mM4Q4tO3BR\r\n8WmZsIHAA3LmwjeG07CA3T/wLKCV25vOlNjuQpWra4jXpw+Az/RbIxOfK+P0\r\nbOsVLMTd+0WSU8nc0ovKRseuQ3YAGVo5efRnrk87ehqLrH32cJ7j8w1nhCN2\r\nUpnbKEircERmuyjcr8+FzzWaJLyIROR1qbQtJcUP9r/eBpSN2cdUNftP1QqP\r\nvqOlpfeaRvQRxje996WELzQdbaLG5nhIdHpdBJrLiDv/uf99mDYImrlLyiZR\r\nVx4xZVwpSlb1wTnz8Q+Nhu69EJ3aSQiLXsMTDJXbCnsq19gXqtPZkTyRZHqp\r\nLpHVAn4CvAOuiuozqWSMrhjbUJTQ9/4DqIU=\r\n=Bz+H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.2.1_1666108807846_0.549329691446264", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/types", "version": "29.3.1", "license": "MIT", "_id": "@jest/types@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7c5a80777cb13e703aeec6788d044150341147e3", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.3.1.tgz", "fileCount": 10, "integrity": "sha512-d0S0jmmTpjnhCmNpApgX3jrUZgZ22ivKJRvL2lli5hpCRoNnp1f85r2/wpKfXuYu8E7Jjh1hGfhPyup1NM5AmA==", "signatures": [{"sig": "MEUCIQCPpmYqLfPAD36s3eMrHmaovB3cnWwUoFM8IMRfV2F4MAIgOUUyrjgvdqZ7l85l8TP3Bz+bxhtrz5C5LvDA3cJinis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4LBAAokImsTlB3J9lBHZMcLASTXuBCNK8hLbm8TOkX2JbR2X+lGzQ\r\nrUny9miI/cVLBiPToyF7KB/94H1wsn+0XAicKly33J7cH/TllnZ1nqDAx/sv\r\nKz6HNIaYDgLcjpm5I8UglQ5ly+5/Ln5rV8vj694uilK4/X8qFPeVEx9PmWT4\r\naOCKbwMBiSXA6a3P5yzEkt3SIellx9iKE8kmrt4C9zxSA7egz37IrXhqZZun\r\n+lK6BdLMvNG/K0ykefzzH75VqqVUlIht+eeHnV+D5LhfZx9zNb3HsbXNr4ms\r\nQ6tQIOhvGmDmVElsyA0cP3k1MiNgM19uFZAjKf3SHU11TMDtfkPgGydJMRzn\r\n+RLR0EkfJIEJkSLvW8vWbMZAXuV2wpfYp3thIZAIq2/CUAFffEGecb7lIKTP\r\nEiAEIYxrI1dYTne/+DxhuU0BN6QZKtGYv6Gp0L1FsofsAdpfuFW8GglCyyIJ\r\nkDmvCkDuNQKXHDy+149mo+GGXb4PMkeFHDQdThcqgfrYlgkFcwguvV2Vs2bP\r\nS1JToOwA6h0AVRoxaRFX4RPftPfwU4MAY7V2c+e6uXUfeWm/Anix2B5ILWz7\r\nh9ksfaR/86AfkOF5zkY+KHdv0pC9Ry8oMc9MoKFLSzts02NIZxyEkjxVSBjw\r\nkCGF/e0Me2LHkVPkf3LH6nRQzz8JTtovyAw=\r\n=wx2l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.0.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.3.1_1667948179295_0.009364838293963729", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/types", "version": "29.4.0", "license": "MIT", "_id": "@jest/types@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "569115f2438cacf3cff92521c7d624fbb683de3d", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.4.0.tgz", "fileCount": 10, "integrity": "sha512-1S2Dt5uQp7R0bGY/L2BpuwCSji7v12kY3o8zqwlkbYBmOY956SKk+zOWqmfhHSINegiAVqOXydAYuWpzX6TYsQ==", "signatures": [{"sig": "MEUCID0m5bshI8+Vk7fh6U1E26f5yFrAJ1nMGh++4/2cvM/JAiEAxoDilTEeHWrpv7gGgJ52bC6+uVcJBzeNhoh8LwyiDkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7kwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVxA//TpZQp2rdAEo2Ag9cEk/9PiN7gp/T/8btptmrLUgjuyIeq9Ak\r\nqOO5OqK5ama5G2ngaE40ebHtG5/NG6S5B5nBS8SCrjCCSC+f0/2xfdJPMeQQ\r\nrHmyBVmZSoNUDtLU443XhmCoYbz/0jyTqhtqGPbqTXvPY0PnoKK9cdR39PlL\r\nVXHRCvrQgQTBY9AIC0tHf6uNjgdwCTwdhGvSPNB1meuwfXCVRorkk0Fgozvz\r\nqCg7jFpHYucFvZkKErXsXJcBAdG7upy6hL1/1v7677cNZqTYit2D2KPWaDkO\r\nm0Zlv+UU0Z3AZpjmXjCxjaMQnljJm9vbjMhlZ5AtM12QYCmi0XSu88SvEqWN\r\nWe5GW4IICgI++c13yuALpgAmyS7lcib86SxO7XyH6UuXE6wQdS1HJaDVDzNB\r\n3CRCWhhjngpW8MaYhqrpSwyzmqqksVEblHAIOpU+R3FrGt+lmZ0AdgRiCwXs\r\nuwuKtxoMHL1PypBQKMwVIKMlbKuAP8khgobZ/IpZf7S+D9pH96Ydm3qwlYU/\r\ndMlPjY/MGZvFR3gO1F99gMNPILTJHw/L4khEUEZCoYwbeol6OhHzk7GsVnNd\r\nDp667F1XVWGf7sIQgM3cVx0F4VqB3XWF4S9kq83ZEC0qQF8qoUexziWgTNDt\r\nOBCuR0ks4lgoKZvrsoxF81XGxFjZ+dP0sMg=\r\n=8OJT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.4.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.4.0_1674557744491_0.16826941999649248", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/types", "version": "29.4.1", "license": "MIT", "_id": "@jest/types@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f9f83d0916f50696661da72766132729dcb82ecb", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.4.1.tgz", "fileCount": 10, "integrity": "sha512-zbrAXDUOnpJ+FMST2rV7QZOgec8rskg2zv8g2ajeqitp4tvZiyqTCYXANrKsM+ryj5o+LI+ZN2EgU9drrkiwSA==", "signatures": [{"sig": "MEUCIQDfrDueocj1XUm3CvE3TL48MxphJ+l/ABBIz57Ps36ZUwIgMYQGsLR9Nu8mq9AoHtHLvXfRnrR8dMxhHCZTm7H4hmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pdwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdQw/+KfXk82sYTngbo1vsxdymMDiL/vld9/DeY5ItzBzVSX/qAPH7\r\n4Z8585BVmMbnKW5VpcKYFIYVQvN3sS1W2E4Xm+97/iDaeZVNKtp+42Gk7fqd\r\na1lNZgzu9MD4YTud1SW8wu3gOORev9ZXWunt66CONpQuuC/CqEeiMcyLLVQs\r\nUE5bw0pql+XS9jmAPjHFUOpa0cgQsjtAfPpQYdsEtKkbp6jDbqGjHmG+1Idp\r\n7/RrpYPivJaldZ+o/JkIrjk3D1WK3FXwk1HS2ltjtfYbZu6ESz1NkKpu7FyO\r\nsXz8iHsidtijEeRHdn/WcmcFdpGpws7IhXWe/dUOMK0I85Vc7NekJ6QiJ4+T\r\nktIpvl6MnMq8NEE4L/488f6TU6z1GKKHcmHvCqEeFyLhOLQD9b2QsqH5ICIL\r\nnNysTQMgPo/CaXr7R/0izTfVh304LOCLHTyd1TkqWNxF5QdpFyKXX5P6BbHz\r\nqxLCr/J9lAEmiVyGh99qHirDIO4WHy5LWXaIGRKujiVt/zwQLEfhemxUuuZr\r\ntJt8h47/7+jjalYFZuipJsWoispM4c2il15YUVVTWV4IMzqNmPW0rnrTLIsp\r\ndHTd3mzCczd8ELBbOEB7PZNmDsh9+uwTda7BWymmtPQees7zzoKAlJrfEeJx\r\nu3CAzeTrqAX7EMmOo9z7fLexUHXnlmWWVXY=\r\n=mJeq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.4.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.4.1_1674745711833_0.6835695901268966", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/types", "version": "29.4.2", "license": "MIT", "_id": "@jest/types@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8f724a414b1246b2bfd56ca5225d9e1f39540d82", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.4.2.tgz", "fileCount": 10, "integrity": "sha512-CKlngyGP0fwlgC1BRUtPZSiWLBhyS9dKwKmyGxk8Z6M82LBEGB2aLQSg+U1MyLsU+M7UjnlLllBM2BLWKVm/Uw==", "signatures": [{"sig": "MEQCIGlPV6y3y8UEaUSFSutJoL0gWDLOjJzNLdfFmj/CmSqIAiAA3lyeBHGnG1vGV4C7sf7OD0wLzcXlJGhcsBW52qQsqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqhhAAj6XeKyNIE1PCudzBnB3yzQABh77hj7uz8SIQrlJKiycgxWY9\r\nInhFTKtvrYnIeWH0knHZGgJYVifEtIbJ8xv3oGI7a+Rub7TzM/k4mjaiJBeT\r\npn0SNx0/p+Is3E0o4DEc31CLRNDpiPrB2N4mJuEqPkfatUijNa5/u2I58z7x\r\nsGmaLhRMK2FSRHhTJjzpv/SBITFXVfQ0OcKG4jquJTapEB3UvMOKmsQJISmy\r\nyGcDO89Hnou270DGbCFOOq2EkxUc8yiMjeIjThvYOobD/E8ZCVMdIRLGnhMV\r\nemIvPBHxxTOqX17ZFWYAju3Vsi3I9OBWme/2Mg+Jyxy7Epd4vxbdN+zEjFpz\r\nuNifZUhZFKrZcP/5+RlpC9HuR4NBmEhYtBSc0VCigL86gAyw46qdVDr5UkxU\r\neMnxxhi6xNbE3i2wsWfuUxUwHgIJggzOdd0k5vaWXwfxS1D4zLl9PTjpjse7\r\npkLq9tbE1/eD7lgZFhFdzWb1N2KFkOfbrQKL0wZeko8u+biWVRQyfuMGEda1\r\nnzmM6E4b0ASiWh8yvme494+0iwWwmM4suRKdVpjcoCQw+prD8D3k4GnMIQ4N\r\npyJWNHOAJEow/Ve8gz1CuVO24NeEWC3YxPHNDt/xDkVtBKiDTUgBKqiJvAo3\r\n9IOnnZ73eGXS/HHVrmxZgNPGrPUi5SaB2RA=\r\n=Rgkv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.4.2", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.4.2_1675777522914_0.39876375825995125", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/types", "version": "29.4.3", "license": "MIT", "_id": "@jest/types@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9069145f4ef09adf10cec1b2901b2d390031431f", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.4.3.tgz", "fileCount": 10, "integrity": "sha512-bPYfw8V65v17m2Od1cv44FH+SiKW7w2Xu7trhcdTLUmSv85rfKsP+qXSjO4KGJr4dtPSzl/gvslZBXctf1qGEA==", "signatures": [{"sig": "MEQCIH+Y9mk0txSK5yLu/qZXrrTtvR1a2aq0MMkw11GZApI5AiBZhDqvhQPw9MRSqpqhznIlQZmj8xUliHmFNZxHOxnFTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MidACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiPw//aTP4dL7NRwsVuSRb4gJXwRxynLRnxLBYhgA+5lGRilt5MFjR\r\nVhmtCWlLF/Ah8p23Pjx5GkfQS/G7jC0iNmODraemfqE2EQBGRqwvdsnfdLEt\r\ncTJy8bfzMimFAcUnXSdM8D1zH8wkLbani1Q+7hvtR3lxq5KXc6Ey6m/M7z2V\r\nGW5i/G/xNEcAVggUzbC56W2Yy4D4ZhjLoZwWRwVpzblbgRy6FdTN/NQ0baXn\r\nfQ/+M2YQBBkauD59mgJ0FoU8ERPd8arDU5IGJEOqnhKmeen3i4+9K+1wzNNw\r\nnjbK4Gy3bndLX2WUYcWZQsbU4+NsRT66Yxlo95UOlh/6PewECL29bXbwxxdD\r\n3ftESd8iRPG00qxpdG4NMztxoN6O0WFKvXGg3FpfevEVNS6OBKM/rC8ZXqX/\r\nXFyLo8I+sy5L2QGUs0u4LBPLJhiNXKlqBO0UH6KJDA0LCXZHK5Qhz6K8Omku\r\nDpykT+OzztMh6ZuvPvpfEb+SDQrkVtDTHFgKUB2NBVXWtPOWK8GOaYTjNvUZ\r\nzJ5hO0YZONLHRYP3zwywHOc2kNtMcNBsOTdXdOlaUtmQCiH75S3E6WT+8u+9\r\nY6zuCgykExZytHRy5HkpFpZVL6+SV2ItlkN9xKu5y11jHxeHHqghcQvKc6j7\r\ngNMxHDbdQm/PAXN7FVv2isRC6F2+H5DqPdQ=\r\n=K1+V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.4.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.4.3_1676462237366_0.507597767600076", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/types", "version": "29.5.0", "license": "MIT", "_id": "@jest/types@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f59ef9b031ced83047c67032700d8c807d6e1593", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.5.0.tgz", "fileCount": 10, "integrity": "sha512-qbu7kN6czmVRc3xWFQcAN03RAUamgppVUdXrvl1Wr3jlNF93o9mJbGcDWrwGB6ht44u7efB1qCFgVQmca24Uog==", "signatures": [{"sig": "MEUCIQCPPkASf8lmNDWODhU8u1ZDQQt9rrAwCixvYxNGjeR9RgIgVIoDfWya9vY6EIak03nvsdzCA7D2d6WZ6F9It7d5hMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeukACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhCw/+PYOixWwuNC7y6leNLEU8RlMvWjJ8+v7wzsNsp8rFzfH6RDvH\r\nsMpYB/c8JWYgjRiNHG5bSqRAozCfzbzWaYhZ3qPrgK7StOcO4JYK2STDUkNP\r\nbEnBcH4M8k2d8baFYeBHbr8tc/VJLhIQj7/ivxsEKlFzPAap4cF+rH2UZY43\r\ngXAyCB5mbfrIJ7s1yhgDDbH5hhyxcdokdAyBlc4fyZDT4je8+G9HUqGHFxQQ\r\nl59SNi/QjcQJKAjhrX4GNpnGhPMPBEEg9QSOEDc4WDw/MQvnUPo0cxFwNMJT\r\n3VReUJ3ZOELCDqrfft+hddfwOyyKUlGNSmnnEWVwWNY0WKaZM6nrUxiXgt17\r\nQKFFSMC3X14HBxFbDNJ+xrSaTmsOIUO1k8+/YMDAtXW6JvT9PoE2+pAGMDCA\r\nKy6z7GliSK4cT232w4sDftcaVRswo/0ry+76NUsBszA/8gk1ajpmvqlD4VuN\r\nsl7/gU+NXbxoM3Os8v6jws6efkajyN5BgRcCHpq8pKj1Y5v8PG8mwyXkEQlh\r\nvA09Zuri3ECNgEQnEl0Lw5Zd4W9OrSV3tJVhMcYrhO4SH172Q0YSjEdFv7YN\r\nXRvzmr3jLp8hn/S2ZsdX4NZLye3nWYtohngDVnz+kd65xCPTGdUyeYgWRxTM\r\nnkq7GwX9qQqIkmHLDnhTRqWIRf7inykSXrg=\r\n=o04n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.4.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.5.0_1678109603838_0.5666125398476143", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/types", "version": "29.6.0", "license": "MIT", "_id": "@jest/types@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "717646103c5715394d78c011a08b3cbb83d738e8", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.6.0.tgz", "fileCount": 10, "integrity": "sha512-8XCgL9JhqbJTFnMRjEAO+TuW251+MoMd5BSzLiE3vvzpQ8RlBxy8NoyNkDhs3K3OL3HeVinlOl9or5p7GTeOLg==", "signatures": [{"sig": "MEYCIQCRRDOJAT0AIdnjJMrkhQp4PMmw+o+zf/aL2YFOGuZQAQIhANnt2k0CZ/+d6IAWZQUDsJrFi78jDGzoSnpAEMuHAKCN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33004}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.6.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.6.0_1688484341692_0.43832244427566325", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/types", "version": "29.6.1", "license": "MIT", "_id": "@jest/types@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ae79080278acff0a6af5eb49d063385aaa897bf2", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.6.1.tgz", "fileCount": 10, "integrity": "sha512-tPKQNMPuXgvdOn2/Lg9HNfUvjYVGolt04Hp03f5hAk878uwOLikN+JzeLY0HcVgKgFl9Hs3EIqpu3WX27XNhnw==", "signatures": [{"sig": "MEQCICKG/q4Njw5IQ7+HqgBUKxmFJHwQoA4zVhAvlJewheUyAiAxiydVPknUpG/yNr8fgctAIKxvCyhm62KpxCTKi8ooOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32692}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.6.0", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.6.1_1688653086088_0.17917077490597477", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/types", "version": "29.6.3", "license": "MIT", "_id": "@jest/types@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "1131f8cf634e7e84c5e77bab12f052af585fba59", "tarball": "https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz", "fileCount": 10, "integrity": "sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==", "signatures": [{"sig": "MEQCIGe96xLz05XK9DIj4nVfPRdYWAC1xmTmSeAsRc/2DCKqAiBDx8J2dLbSNFGcH6rO+w1YTaScMyUUejjQk/BYtHGOqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32712}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "^29.6.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_29.6.3_1692621539010_0.15763065952812583", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/types", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/types@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "7c545c115a1b4f01cd5e980f0baf49c89f9ce896", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-ISOlYrrINDcDQsiK/yDK7BFA2I1JbyDf4mpHPFcegjbWL76/bEDBCv0d4JCJ0H0prsP7Pjztn2ul/uVYFBUzLQ==", "signatures": [{"sig": "MEUCIQC9V2n+mqYelrmwrYVAwTmABBedkD92PpMFqZ+VOz9bgwIgKMUgb/IGs8Qb2jA8zAAR/xLFJvHII1n4+x9OBXqAZG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33696}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "30.0.0-alpha.1", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-alpha.1_1698672767686_0.6068483653182246", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/types", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/types@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "32722a6103a9963d531d5b502fb31623830a9417", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-kQ2aDSVtTqrglSgVMe7N11nQtSgy3Q2/Gm1uqDS7eRyD+UG6UFAiWmAQ43YmUkifQE6xtenMTTyuAiznRCMuFw==", "signatures": [{"sig": "MEUCIQDVLNMmhbKUS184FwoqcQw6KAxJ3CfUYr/4bxfWbyBEAgIgeOq0lD27WK9Uh/di69gllFpwpIpEgbPw0iKDtUYA29k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34120}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "30.0.0-alpha.2", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-alpha.2_1700126895652_0.13006945967801564", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/types", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/types@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a823efede8a9ffe5484162119dbaf006c7670d19", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-z8oValFWEGDRslYtcU+4rgTPin8NyHuXaOrDWWDCIrYWPISaa7CLrdA6tS6o1GnywHH7kgYMCpb39cMsqP4R8w==", "signatures": [{"sig": "MEQCIFQk+lgJZkkV2SOziKLJjKKHFr/7RYVQgWCPStXXcxVsAiBvKGz4ZenlQluzGrO1xfZB2CTuuVdugn2vYJGNDZJ4kQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30965}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "30.0.0-alpha.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-alpha.3_1708427333040_0.10626308104119642", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/types", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/types@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "2117914f3da5e3de5eb5b2557c43f3122c5106e2", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-Yxj+Iw62WVn2C9zhFrem8vKKhuC7kLR4DWmOZf6d9Z+eayCmYenZdyFA7P7X688gzGmZ1Qu71snJnLfvEetNdw==", "signatures": [{"sig": "MEQCIF59871k3+MVkQVns3dauVhGEtukH5RMi9XKc/Z+rEk2AiARiR80AnFv8CfcOgYBUVw5mX+eBzBpgCvkDzKYLbazKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31023}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/schemas": "30.0.0-alpha.4", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-alpha.4_1715550196127_0.6109322143200828", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/types", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/types@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "bbd571909e2af2b726b139d08444c452d132515a", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-Qu4PmAPX6sT2RrodqERoBkbrGz6suKDk/PdaL72q6G4ERg8OspP+ODit+JBJe20F+0QW4v6wsf2vD/L6lCzYcw==", "signatures": [{"sig": "MEUCIQDe96KX5kXX1JdTWxMyckax/3IrdJPoJQ/5An3hha4d2gIgVBLpgDBtq9H1xU9OhYfc6kDy28WbSK+zTlSqWqi7kHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31027}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "This package contains shared types of <PERSON><PERSON>'s packages.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/pattern": "30.0.0-alpha.5", "@jest/schemas": "30.0.0-alpha.5", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-alpha.5_1717073038164_0.854473361407752", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/types", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/types@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "77e64e853f8fdc4f14ddacb671560a4f2784a5cc", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-qUjAm8uvIR7oExn/Fp7/bvn58HSZng5itQDM9x0vaxXWxxGH/8MDmqX/h7OUBz9ka+KfYRaTxe4Y6wiM8+nphw==", "signatures": [{"sig": "MEYCIQC2y+yJUU291C5nAUbxZ1gMzh1NHkr6BXdwcgVNLCFTXAIhALms89Pj1c8EjvUb7w5RApPUL5ICx+inHPX4qCoF/k9F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31247}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/pattern": "30.0.0-alpha.6", "@jest/schemas": "30.0.0-alpha.6", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-alpha.6_1723102979240_0.2564840825404724", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/types", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/types@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "10b8d41c2c5284202d76d66808eca357c74096f5", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-hrdUqtIjUMpoNlpmo4DQfe6fvD0Rk02kdOv0+AsAbO689llpzNmb+kLcojzKp/H2jVGqcYrUb0wNSRgn4KcuqA==", "signatures": [{"sig": "MEYCIQC4Bxt1SCBPF6ds1HYEJeyDws9P+SojehLxy9bWiACjsQIhAKftk8zmWupp5myNvQYXzwqEevd0wKrIszRfhG399OsE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31238}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/pattern": "30.0.0-alpha.7", "@jest/schemas": "30.0.0-alpha.7", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-alpha.7_1738225706111_0.38596081808817084", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/types", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/types@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "414106272a08629c15e6b27a571d43063ee5cdaf", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-beta.3.tgz", "fileCount": 5, "integrity": "sha512-x7GyHD8rxZ4Ygmp4rea3uPDIPZ6Jglcglaav8wQNqXsVUAByapDwLF52Cp3wEYMPMnvH4BicEj56j8fqZx5jng==", "signatures": [{"sig": "MEYCIQCYZmPb/ujBDPM0uGUKRN6CrpV4eUWqtbC9e5SavnN0eQIhAL2oTRn4fzDnWW/eHZQbXkdx5pq2tBV/fVsdoXGYoMWU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31234}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/pattern": "30.0.0-beta.3", "@jest/schemas": "30.0.0-beta.3", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-beta.3_1748309258922_0.3052004562401136", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/types", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/types@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2604dc188a7c582ffc36df9b7614fad881c7e403", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-beta.6.tgz", "fileCount": 5, "integrity": "sha512-B<PERSON><PERSON>elJ9eVH2bznipAiK1j0mybDXakNQ01V7vjs2Z5rC5aLDxo9Ywk6ChE+nl8LLFhQIOWGqYsQV+D+fgDmeLQ==", "signatures": [{"sig": "MEYCIQDu2+2Qo9ORwCNP9U30IIQo27gDS07jFhWJbHzkG9ObGAIhAPF0Jnr/kqZUmHNyg9e8dj1aoWtSCKOZwfkFPpwMbg/R", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31245}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "@jest/pattern": "30.0.0-beta.6", "@jest/schemas": "30.0.0-beta.6", "@types/istanbul-reports": "^3.0.0", "@types/istanbul-lib-coverage": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-beta.6_1748994641562_0.7511350491200004", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/types", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/types@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ecd43c7408b40f127e1d5c85052889b7663bfa57", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-beta.7.tgz", "fileCount": 5, "integrity": "sha512-wMMbI2PlfVRw2JZFjUkX9bfefig5m6m/J7FWkpu/JUVIB8UiYUVVWAXEaF4tBZPAbRvbzUSK6bpxJmNgRp+gdA==", "signatures": [{"sig": "MEYCIQDvE3Gs5nNRuiGsGwwUfnYvzr4L+5eisibfxmFRgOWinwIhAMea5xgaLV9yuEWCKrMcdlKQ2/6pY5wVWeznqhiJb8kp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31246}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "@types/node": "*", "@types/yargs": "^17.0.33", "@jest/pattern": "30.0.0-beta.6", "@jest/schemas": "30.0.0-beta.6", "@types/istanbul-reports": "^3.0.4", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-beta.7_1749008134091_0.9036981324539044", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/types", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/types@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "7dffd5236bd59579c92f057d5c1245075b9ea720", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0-beta.8.tgz", "fileCount": 5, "integrity": "sha512-wBHXnxMKVK8+qiO8onYHW7zfhokZhroftHsh0pWtYUjNsGXR+Zf5nIHK//6NHhwoM6PGDz2vMooq9D2ZqlG8DQ==", "signatures": [{"sig": "MEYCIQCxyE3PFv4Aldb2HmuKIX6MVndndeKTj6tiyrvqk2RvsAIhAMxrGpZLxSYJapUAnqIxEmEo37jA8HxbP6WZtrDPV3wd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31246}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "@types/node": "*", "@types/yargs": "^17.0.33", "@jest/pattern": "30.0.0-beta.8", "@jest/schemas": "30.0.0-beta.6", "@types/istanbul-reports": "^3.0.4", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0-beta.8_1749023583141_0.17991720455325022", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/types", "version": "30.0.0", "license": "MIT", "_id": "@jest/types@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "7afb1d34937f722f667b621eb9c653f0f8fda07e", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.0.tgz", "fileCount": 5, "integrity": "sha512-1Nox8mAL52PKPfEnUQWBvKU/bp8FTT6AiDu76bFDEJj/qsRFSAVSldfCH3XYMqialti2zHXKvD5gN0AaHc0yKA==", "signatures": [{"sig": "MEUCIDZwV7Ef7mhNNevtoJvW1NbpQRFrNMi+pucSfBKJ9xleAiEAg4xuTYeASnmspBkdYe1uLNbfOZf4TgeYaGOCeAQCRf4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31159}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"chalk": "^4.1.2", "@types/node": "*", "@types/yargs": "^17.0.33", "@jest/pattern": "30.0.0", "@jest/schemas": "30.0.0", "@types/istanbul-reports": "^3.0.4", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.0_1749521743676_0.730971062100058", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/types", "version": "30.0.1", "license": "MIT", "_id": "@jest/types@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "a46df6a99a416fa685740ac4264b9f9cd7da1598", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.1.tgz", "fileCount": 5, "integrity": "sha512-HGwoYRVF0QSKJu1ZQX0o5ZrUrrhj0aOOFA8hXrumD7SIzjouevhawbTjmXdwOmURdGluU9DM/XvGm3NyFoiQjw==", "signatures": [{"sig": "MEUCIQDIGZgDtr2ugYtmZzdE8dInzz1SuoQ0BW6n0d0fDsiyMQIgd3sPsaYAFkPSYRjkdSXQ8HrwWQ9H/OBfAZWyzRjlr3c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31159}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-types"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"chalk": "^4.1.2", "@types/node": "*", "@types/yargs": "^17.0.33", "@jest/pattern": "30.0.1", "@jest/schemas": "30.0.1", "@types/istanbul-reports": "^3.0.4", "@types/istanbul-lib-coverage": "^2.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/types_30.0.1_1750285881309_0.5109563316896231", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.5": {"name": "@jest/types", "version": "30.0.5", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-types"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/pattern": "30.0.1", "@jest/schemas": "30.0.5", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-reports": "^3.0.4", "@types/node": "*", "@types/yargs": "^17.0.33", "chalk": "^4.1.2"}, "publishConfig": {"access": "public"}, "gitHead": "22236cf58b66039f81893537c90dee290bab427f", "_nodeVersion": "24.4.1", "_npmVersion": "lerna/4.3.0/node@v24.4.1+arm64 (darwin)", "_id": "@jest/types@30.0.5", "dist": {"integrity": "sha512-aREYa3aku9SSnea4aX6bhKn4bgv3AXkgijoQgbYV3yvbiGt6z+MQ85+6mIhx9DsKW2BuB/cLR/A+tcMThx+KLQ==", "shasum": "29a33a4c036e3904f1cfd94f6fe77f89d2e1cc05", "tarball": "https://registry.npmjs.org/@jest/types/-/types-30.0.5.tgz", "fileCount": 7, "unpackedSize": 57041, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBWRT98fD2ezpM9NRjp4o8Bbrd6jyJ6P8XFgEOnxD5sBAiEAlvNV2AbTY6868KZOhgNKpyLXxjuAKfyg3VaQVvzmYRY="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/types_30.0.5_1753151306286_0.713835082249543"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-05T14:46:28.107Z", "modified": "2025-07-22T02:28:26.728Z", "24.2.0-alpha.0": "2019-03-05T14:46:28.429Z", "24.3.0": "2019-03-07T12:59:32.094Z", "24.5.0": "2019-03-12T16:36:20.936Z", "24.6.0": "2019-04-01T22:26:16.841Z", "24.7.0": "2019-04-03T03:55:12.702Z", "24.8.0": "2019-05-05T02:02:15.301Z", "24.9.0": "2019-08-16T05:55:46.400Z", "25.0.0": "2019-08-22T03:23:44.599Z", "25.1.0": "2020-01-22T00:59:44.843Z", "25.2.0-alpha.86": "2020-03-25T17:16:11.578Z", "25.2.0": "2020-03-25T17:57:54.395Z", "25.2.1-alpha.1": "2020-03-26T07:54:12.906Z", "25.2.1-alpha.2": "2020-03-26T08:10:21.775Z", "25.2.1": "2020-03-26T09:01:04.480Z", "25.2.3": "2020-03-26T20:24:41.169Z", "25.2.5": "2020-04-02T10:23:00.864Z", "25.2.6": "2020-04-02T10:29:08.281Z", "25.3.0": "2020-04-08T13:20:57.987Z", "25.4.0": "2020-04-19T21:50:19.750Z", "25.5.0": "2020-04-28T19:45:12.897Z", "26.0.0-alpha.0": "2020-05-02T12:12:51.874Z", "26.0.0-alpha.1": "2020-05-03T18:47:55.762Z", "26.0.0-alpha.2": "2020-05-04T16:05:20.335Z", "26.0.0": "2020-05-04T17:52:56.552Z", "26.0.1-alpha.0": "2020-05-04T22:15:52.297Z", "26.0.1": "2020-05-05T10:40:41.004Z", "26.1.0": "2020-06-23T15:15:03.422Z", "26.2.0": "2020-07-30T10:11:37.170Z", "26.3.0": "2020-08-10T11:31:41.235Z", "26.5.0": "2020-10-05T09:28:06.633Z", "26.5.2": "2020-10-06T10:52:41.320Z", "26.6.0": "2020-10-19T11:58:33.535Z", "26.6.1": "2020-10-23T09:05:45.824Z", "26.6.2": "2020-11-02T12:51:13.004Z", "27.0.0-next.0": "2020-12-05T17:25:07.819Z", "27.0.0-next.1": "2020-12-07T12:43:17.512Z", "27.0.0-next.3": "2021-02-18T22:09:42.212Z", "27.0.0-next.7": "2021-04-02T13:47:48.367Z", "27.0.0-next.8": "2021-04-12T22:42:23.493Z", "27.0.0-next.10": "2021-05-20T14:11:12.786Z", "27.0.1": "2021-05-25T10:06:23.668Z", "27.0.2": "2021-05-29T12:07:07.917Z", "27.0.6": "2021-06-28T17:05:31.171Z", "27.1.0": "2021-08-27T09:59:30.943Z", "27.1.1": "2021-09-08T10:12:08.132Z", "27.2.3": "2021-09-28T10:11:19.813Z", "27.2.4": "2021-09-29T14:04:46.038Z", "27.2.5": "2021-10-08T13:39:18.555Z", "27.4.0": "2021-11-29T13:36:54.886Z", "27.4.1": "2021-11-30T08:37:03.219Z", "27.4.2": "2021-11-30T11:53:33.701Z", "27.5.0": "2022-02-05T09:59:17.878Z", "27.5.1": "2022-02-08T10:52:12.142Z", "28.0.0-alpha.0": "2022-02-10T18:17:26.456Z", "28.0.0-alpha.1": "2022-02-15T21:26:54.397Z", "28.0.0-alpha.2": "2022-02-16T18:11:59.557Z", "28.0.0-alpha.3": "2022-02-17T15:42:21.552Z", "28.0.0-alpha.4": "2022-02-22T12:13:54.463Z", "28.0.0-alpha.5": "2022-02-24T20:57:17.372Z", "28.0.0-alpha.6": "2022-03-01T08:32:22.391Z", "28.0.0-alpha.7": "2022-03-06T10:02:40.093Z", "28.0.0-alpha.8": "2022-04-05T14:59:38.791Z", "28.0.0-alpha.9": "2022-04-19T10:59:13.869Z", "28.0.0": "2022-04-25T12:08:07.194Z", "28.0.1": "2022-04-26T10:02:32.343Z", "28.0.2": "2022-04-27T07:44:01.709Z", "28.1.0": "2022-05-06T10:48:53.308Z", "28.1.1": "2022-06-07T06:09:35.492Z", "28.1.3": "2022-07-13T14:12:26.994Z", "29.0.0-alpha.0": "2022-07-17T22:07:06.903Z", "29.0.0-alpha.2": "2022-08-05T23:29:18.017Z", "29.0.0-alpha.3": "2022-08-07T13:41:32.947Z", "29.0.0-alpha.4": "2022-08-08T13:05:30.062Z", "29.0.0-alpha.6": "2022-08-19T13:57:42.633Z", "29.0.0": "2022-08-25T12:33:25.750Z", "29.0.1": "2022-08-26T13:34:39.374Z", "29.0.2": "2022-09-03T10:48:17.112Z", "29.0.3": "2022-09-10T14:41:35.751Z", "29.1.0": "2022-09-28T07:37:35.768Z", "29.1.2": "2022-09-30T07:22:41.453Z", "29.2.0": "2022-10-14T09:13:41.446Z", "29.2.1": "2022-10-18T16:00:08.059Z", "29.3.1": "2022-11-08T22:56:19.469Z", "29.4.0": "2023-01-24T10:55:44.700Z", "29.4.1": "2023-01-26T15:08:31.999Z", "29.4.2": "2023-02-07T13:45:23.096Z", "29.4.3": "2023-02-15T11:57:17.528Z", "29.5.0": "2023-03-06T13:33:24.014Z", "29.6.0": "2023-07-04T15:25:41.892Z", "29.6.1": "2023-07-06T14:18:06.333Z", "29.6.3": "2023-08-21T12:38:59.210Z", "30.0.0-alpha.1": "2023-10-30T13:32:47.937Z", "30.0.0-alpha.2": "2023-11-16T09:28:15.846Z", "30.0.0-alpha.3": "2024-02-20T11:08:53.175Z", "30.0.0-alpha.4": "2024-05-12T21:43:16.330Z", "30.0.0-alpha.5": "2024-05-30T12:43:58.329Z", "30.0.0-alpha.6": "2024-08-08T07:42:59.429Z", "30.0.0-alpha.7": "2025-01-30T08:28:26.284Z", "30.0.0-beta.3": "2025-05-27T01:27:39.098Z", "30.0.0-beta.6": "2025-06-03T23:50:41.750Z", "30.0.0-beta.7": "2025-06-04T03:35:34.282Z", "30.0.0-beta.8": "2025-06-04T07:53:03.296Z", "30.0.0": "2025-06-10T02:15:43.878Z", "30.0.1": "2025-06-18T22:31:21.525Z", "30.0.5": "2025-07-22T02:28:26.488Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-types"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}