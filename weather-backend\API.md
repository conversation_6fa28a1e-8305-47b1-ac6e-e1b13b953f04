# Weather Backend API 文档

## 基础信息

- **基础 URL**: `http://localhost:3000/api`
- **版本**: v1.0.0
- **认证**: 无需认证（内部使用 OpenWeatherMap API Key）

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "message": "错误描述",
    "code": "ERROR_CODE"
  }
}
```

## 端点详情

### 1. 健康检查

**GET** `/health`

检查服务器状态。

**响应示例:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "environment": "development"
}
```

### 2. 获取当前天气

**GET** `/api/weather/current`

获取指定城市的当前天气信息。

**查询参数:**
- `city` (必需): 城市名称
- `lang` (可选): 语言代码，默认 `zh_cn`
- `units` (可选): 单位类型，默认 `metric`

**支持的语言:**
- `zh_cn`: 简体中文
- `zh_tw`: 繁体中文
- `en`: 英语
- `es`: 西班牙语
- `fr`: 法语
- `de`: 德语
- `it`: 意大利语
- `ja`: 日语
- `ko`: 韩语
- `ru`: 俄语

**支持的单位:**
- `metric`: 摄氏度，米/秒
- `imperial`: 华氏度，英里/小时
- `kelvin`: 开尔文，米/秒

**请求示例:**
```
GET /api/weather/current?city=Beijing&lang=zh_cn&units=metric
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "location": {
      "name": "Beijing",
      "country": "CN",
      "coordinates": {
        "lat": 39.9042,
        "lon": 116.4074
      }
    },
    "current": {
      "temperature": 25,
      "feelsLike": 27,
      "humidity": 60,
      "pressure": 1013,
      "visibility": 10,
      "uvIndex": null
    },
    "weather": {
      "main": "Clear",
      "description": "晴朗",
      "icon": "01d"
    },
    "wind": {
      "speed": 3.5,
      "direction": 180,
      "gust": null
    },
    "clouds": {
      "coverage": 0
    },
    "timestamp": "2024-01-01T12:00:00.000Z",
    "sunrise": "2024-01-01T06:30:00.000Z",
    "sunset": "2024-01-01T18:30:00.000Z"
  }
}
```

### 3. 获取天气预报

**GET** `/api/weather/forecast`

获取指定城市的天气预报。

**查询参数:**
- `city` (必需): 城市名称
- `days` (可选): 预报天数 (1-16)，默认 5
- `lang` (可选): 语言代码，默认 `zh_cn`
- `units` (可选): 单位类型，默认 `metric`

**请求示例:**
```
GET /api/weather/forecast?city=Beijing&days=3&lang=zh_cn
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "location": {
      "name": "Beijing",
      "country": "CN",
      "coordinates": {
        "lat": 39.9042,
        "lon": 116.4074
      }
    },
    "forecast": [
      {
        "datetime": "2024-01-01T15:00:00.000Z",
        "temperature": {
          "current": 25,
          "min": 20,
          "max": 28,
          "feelsLike": 27
        },
        "weather": {
          "main": "Clear",
          "description": "晴朗",
          "icon": "01d"
        },
        "wind": {
          "speed": 3.5,
          "direction": 180,
          "gust": null
        },
        "humidity": 60,
        "pressure": 1013,
        "clouds": 0,
        "precipitation": {
          "probability": 0,
          "rain": 0,
          "snow": 0
        }
      }
    ]
  }
}
```

### 4. 通过坐标获取天气

**GET** `/api/weather/coordinates`

通过经纬度坐标获取天气信息。

**查询参数:**
- `lat` (必需): 纬度 (-90 到 90)
- `lon` (必需): 经度 (-180 到 180)
- `lang` (可选): 语言代码，默认 `zh_cn`
- `units` (可选): 单位类型，默认 `metric`

**请求示例:**
```
GET /api/weather/coordinates?lat=39.9042&lon=116.4074&lang=zh_cn
```

### 5. 搜索城市

**GET** `/api/weather/search`

搜索城市名称。

**查询参数:**
- `q` (必需): 搜索关键词
- `limit` (可选): 返回结果数量限制，默认 5

**请求示例:**
```
GET /api/weather/search?q=Beijing&limit=5
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "name": "Beijing",
      "country": "CN",
      "state": null,
      "coordinates": {
        "lat": 39.9042,
        "lon": 116.4074
      }
    }
  ]
}
```

## 错误代码

| 错误代码 | 描述 |
|---------|------|
| `MISSING_CITY` | 缺少城市名称参数 |
| `INVALID_CITY` | 无效的城市名称 |
| `MISSING_COORDINATES` | 缺少坐标参数 |
| `INVALID_COORDINATES` | 无效的坐标 |
| `MISSING_QUERY` | 缺少搜索关键词 |
| `INVALID_LANGUAGE` | 不支持的语言代码 |
| `INVALID_UNITS` | 不支持的单位类型 |
| `INVALID_DAYS` | 无效的天数参数 |
| `RATE_LIMIT_EXCEEDED` | 请求频率超限 |
| `INTERNAL_ERROR` | 内部服务器错误 |

## 速率限制

- 每个 IP 地址每 15 分钟最多 100 次请求
- 超出限制将返回 429 状态码

## 缓存

- 天气数据缓存 5 分钟（开发环境）/ 10 分钟（生产环境）
- 城市搜索结果缓存 5 分钟
