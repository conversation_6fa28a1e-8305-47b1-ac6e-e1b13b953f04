<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        .span1 {
            margin: 10px 20px;
            padding: 10px 20px;
        }

        .span2 {
            margin: 0 20px;
            padding: 0 20px;
        }
    </style>
</head>

<body>
    <!-- 网页元素很多都带有默认的内外编剧，所以在布局前，首先要清除网页元素的内外边距，用通配符选择题*padding,margin:0来做到 -->
    <!-- 行内元素尽量只设置左右内外边距 ,因为上下边距一般不起作用-->
    <p>123</p>
    <ul>
        <li>123</li>
    </ul>
    <span class="span1">我爱你</span>
    <span class="span2">爱着你</span>
    <!-- 实际表示因为浏览器兼容性完全一样 -->
</body>

</html>