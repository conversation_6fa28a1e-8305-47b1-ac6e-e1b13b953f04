#!/bin/bash

# 天气后台服务启动脚本

echo "🌤️  启动天气后台服务..."

# 检查 Node.js 版本
NODE_VERSION=$(node --version)
echo "Node.js 版本: $NODE_VERSION"

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "⚠️  警告: .env 文件不存在，正在从 .env.example 复制..."
    cp .env.example .env
    echo "📝 请编辑 .env 文件并设置你的 OpenWeatherMap API Key"
    exit 1
fi

# 检查 API Key
if grep -q "your_openweather_api_key_here" .env; then
    echo "❌ 错误: 请在 .env 文件中设置有效的 OPENWEATHER_API_KEY"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
npm install

# 创建日志目录
mkdir -p logs

# 运行测试
echo "🧪 运行测试..."
npm test

if [ $? -eq 0 ]; then
    echo "✅ 测试通过"
else
    echo "❌ 测试失败，请检查代码"
    exit 1
fi

# 启动服务
echo "🚀 启动服务..."
if [ "$NODE_ENV" = "production" ]; then
    npm start
else
    npm run dev
fi
