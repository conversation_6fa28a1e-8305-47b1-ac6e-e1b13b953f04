# 单调栈:

## 例题：给定一个长度为N的整数数列，输出每个数左边第一个比他小的数，如果不存在则输出-1。

例子：

![image-20241121161750840](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241121161750840.png)

思路：

## 暴力：来个两重循环

```c++
#include<iostream>
#include<algorithm>
using namespace std;
const int N = 1e5 + 10;
int n, a[N];

int main()
{
	cin >> n;
	for (int i = 1;i <= n;i++)cin >> a[i];
	for (int i = 1;i <= n;i++)
	{
		bool flag = false;
		for (int j = i - 1;j >= 1;j--)
		{
			if (a[j] < a[i])
			{
				flag = true;
				cout << a[j] << ' ';
				break;
			}
		}
		if (flag == false)cout << -1 << ' ';
	}
	return 0;
}
```

## 优化：为了每次不用把所有左边的数都遍历一遍，我们就可以用单调栈

## 写一个栈，存比a[i]小的数

只要左边的数比右边的数更大，那它就不会被用到

每次遇到一个比a[i]小的数将栈中的栈顶进行比较，如果栈顶数要更大，则出栈，同时把栈中其他比这个数更大的也出栈，直到栈中的数比a[i]小

a[i]就是即将要入栈的数，栈中任意一个数比它大，以后就都不会被用到了，所以每次我们用一个循环来把栈中比它大的数出栈，一直到找到比它小的数，就可以输出，栈空就是左边没有比a[i]小的数,栈不为空就输出栈顶就可以

```c++
#include<iostream>
#include<algorithm>
using namespace std;
const int N = 1e5 + 10;

int n;
int stk[N], tt;
int main()
{
	cin >> n;
	for (int i = 1;i <= n;i++)
	{
		int x;
		cin >> x;
		while (tt && stk[tt] >= x)tt--;//栈中现有的数要比x要小
		if (tt)cout << stk[tt] << ' ';//栈不为空表示有比当前x小的
		else cout << -1 << ' ';//栈为空输出-1
		stk[++tt] = x;//入栈每一个x
	}
	return 0;
}
```

