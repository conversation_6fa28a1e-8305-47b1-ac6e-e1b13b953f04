# 函数

函数是对一段代码的封装

声明函数：

![image-20241109103826673](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109103826673.png)

```
function标识符 函数名(参数)
{
执行语句
}
```

![image-20241109104933912](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109104933912.png)

![image-20241109104947344](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109104947344.png)

```
使用函数也是在js文件中使用
```

![image-20241109105258028](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109105258028.png)

## 函数的命名

![image-20241109105608803](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109105608803.png)

## 函数调用：函数只有在调用后才会执行

![image-20241109105728347](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109105728347.png)

控制台输出，自带换行

## 函数传参：参数是弱类型，不用先定义后使用，可以直接使用

![image-20241109110937596](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109110937596.png)

```
start，end都是形参。这里1，200都是实参
```

![image-20241109111156128](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109111156128.png)

**![image-20241109111747797](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109111747797.png)**

## 在未输入实参时容易出现报错，所以我们在声明函数时要给形参一个默认值

![image-20241109111839436](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109111839436.png)

![image-20241109112012214](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109112012214.png)

![image-20241109112022136](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109112022136.png)

![image-20241109112711366](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109112711366.png)

## <u>实参可以是变量</u>

![image-20241109113321224](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241109113321224.png)

## 函数返回值

![image-20241110103503235](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110103503235.png)

```javascript
let result=prompt('请输入你的年龄')//把返回值给了result，是个字符串
let b=pop(a[i]) //返回值给了b
```

![image-20241110104838401](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110104838401.png)

![image-20241110105056743](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110105056743.png)

![image-20241110105138794](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110105138794.png)

![image-20241110112122361](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110112122361.png)

## 学会用调试

F12进source-打断点-F5刷新-上或下

![image-20241110151744726](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110151744726.png)

因为js是若数据类型，当我们的函数名一致时，调用最后声明的那个函数

## 实参过多:后的不会赋给形参，前面的正常赋参

![image-20241110152126849](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110152126849.png)

![image-20241110152136826](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110152136826.png)

## 实参过少:没赋值的形参undefined

# 作用域:分为全局作用域和局部作用域

![image-20241110152614520](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110152614520.png)

![image-20241110152705835](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110152705835.png)

## 局部作用域分为块级作用域和函数作用域：块级作用域包括了for循环中let的变量

![image-20241110153106314](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110153106314.png)

<u>js中的形参也可以看成是一种局部变量</u>

## 变量访问原则：先从局部再到全局，不断往外作用域找

![image-20241110153522712](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241110153522712.png)