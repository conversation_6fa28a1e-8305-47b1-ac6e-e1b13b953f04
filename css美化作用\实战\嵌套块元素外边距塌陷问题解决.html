<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .c1 {
            width: 500px;
            height: 300px;
            background-color: aqua;
            margin-top: 10px;
            padding: 1px;
        }

        .c2 {
            width: 300px;
            height: 150px;
            background-color: brown;
            margin-top: 30px;
        }
    </style>
</head>

<body>
    <!-- 对于嵌套关系的块元素，父元素有上外边距同时子元素也有上外边距，此时父元素会塌陷较大的外边距值. -->
    <!-- 解决方法是1.给父元素添加边框2.给父元素定义内边距3.给父元素添加overflow:hidden; -->
    <div class="c1">
        <div class="c2"></div>
    </div>
</body>

</html>