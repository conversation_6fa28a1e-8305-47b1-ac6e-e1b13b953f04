const weatherService = require('../services/weatherService');
const { validateCity, validateCoordinates } = require('../utils/validators');

// 获取当前天气
const getCurrentWeather = async (req, res, next) => {
  try {
    const { city, lang = 'zh_cn', units = 'metric' } = req.query;
    
    if (!city) {
      return res.status(400).json({
        success: false,
        error: {
          message: '城市名称是必需的',
          code: 'MISSING_CITY'
        }
      });
    }

    if (!validateCity(city)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的城市名称',
          code: 'INVALID_CITY'
        }
      });
    }

    const weather = await weatherService.getCurrentWeather(city, lang, units);
    
    res.json({
      success: true,
      data: weather
    });
  } catch (error) {
    next(error);
  }
};

// 获取天气预报
const getForecast = async (req, res, next) => {
  try {
    const { city, days = 5, lang = 'zh_cn', units = 'metric' } = req.query;
    
    if (!city) {
      return res.status(400).json({
        success: false,
        error: {
          message: '城市名称是必需的',
          code: 'MISSING_CITY'
        }
      });
    }

    if (!validateCity(city)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的城市名称',
          code: 'INVALID_CITY'
        }
      });
    }

    const forecast = await weatherService.getForecast(city, parseInt(days), lang, units);
    
    res.json({
      success: true,
      data: forecast
    });
  } catch (error) {
    next(error);
  }
};

// 通过坐标获取天气
const getWeatherByCoordinates = async (req, res, next) => {
  try {
    const { lat, lon, lang = 'zh_cn', units = 'metric' } = req.query;
    
    if (!lat || !lon) {
      return res.status(400).json({
        success: false,
        error: {
          message: '纬度和经度是必需的',
          code: 'MISSING_COORDINATES'
        }
      });
    }

    if (!validateCoordinates(lat, lon)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的坐标',
          code: 'INVALID_COORDINATES'
        }
      });
    }

    const weather = await weatherService.getWeatherByCoordinates(
      parseFloat(lat), 
      parseFloat(lon), 
      lang, 
      units
    );
    
    res.json({
      success: true,
      data: weather
    });
  } catch (error) {
    next(error);
  }
};

// 搜索城市
const searchCities = async (req, res, next) => {
  try {
    const { q, limit = 5 } = req.query;
    
    if (!q) {
      return res.status(400).json({
        success: false,
        error: {
          message: '搜索关键词是必需的',
          code: 'MISSING_QUERY'
        }
      });
    }

    const cities = await weatherService.searchCities(q, parseInt(limit));
    
    res.json({
      success: true,
      data: cities
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCurrentWeather,
  getForecast,
  getWeatherByCoordinates,
  searchCities
};
