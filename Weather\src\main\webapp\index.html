<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Weather API 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1 class="success">✅ Weather API 项目部署成功！</h1>
    
    <h2>测试链接：</h2>
    <ul>
        <li><a href="test" target="_blank">基础Servlet测试</a></li>
        <li><a href="db-test" target="_blank">数据库连接测试</a></li>
        <li><a href="api/weather" target="_blank">获取所有天气数据</a></li>
        <li><a href="api/weather/cities" target="_blank">获取所有城市</a></li>
        <li><a href="api/weather/city/北京" target="_blank">获取北京天气</a></li>
    </ul>
    
    <h2>项目信息：</h2>
    <p><strong>项目名称:</strong> Weather API</p>
    <p><strong>部署时间:</strong> <span id="time"></span></p>
    <p><strong>服务器:</strong> Apache Tomcat</p>
    
    <script>
        document.getElementById('time').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
