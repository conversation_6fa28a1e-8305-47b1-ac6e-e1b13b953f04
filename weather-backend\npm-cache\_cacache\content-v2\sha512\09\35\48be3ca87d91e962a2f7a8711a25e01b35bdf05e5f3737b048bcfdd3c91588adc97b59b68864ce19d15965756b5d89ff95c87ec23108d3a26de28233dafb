{"_id": "on-finished", "_rev": "47-9d03ff2569a4a6bde6b8c653ac9da3e7", "name": "on-finished", "dist-tags": {"latest": "2.4.1"}, "versions": {"2.0.0": {"name": "on-finished", "version": "2.0.0", "license": "MIT", "_id": "on-finished@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-finished", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "dist": {"shasum": "7286551c58c874d8342624cda56e2b42ea227f81", "tarball": "https://registry.npmjs.org/on-finished/-/on-finished-2.0.0.tgz", "integrity": "sha512-zdswTBu2q37qMBR0+wquP+0232E7puV/21clkSJmGzySCFG8G0juqXAY9MauzlKI91yRQmjkY9DVG5xLDnsUmQ==", "signatures": [{"sig": "MEUCIClY7pEXHRhJ6pcSpwTudxJQdg1EPmbK5DfJT+nVJUu5AiEAzCAlhJRr7P+Rbaxxk+8A9mrQbW5zr4ZvlKBhbF1XIug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "engine": {"node": ">= 0.8.0"}, "_shasum": "7286551c58c874d8342624cda56e2b42ea227f81", "gitHead": "86e64f85de7a80e18c3db0ae9a0d30ffbf37bdbd", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/on-finished", "type": "git"}, "_npmVersion": "1.4.21", "description": "Execute a callback when a request closes, finishes, or errors", "directories": {}, "dependencies": {"ee-first": "1.0.5"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.0"}}, "2.1.0": {"name": "on-finished", "version": "2.1.0", "license": "MIT", "_id": "on-finished@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-finished", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "dist": {"shasum": "0c539f09291e8ffadde0c8a25850fb2cedc7022d", "tarball": "https://registry.npmjs.org/on-finished/-/on-finished-2.1.0.tgz", "integrity": "sha512-33+g6TZkplndl+2k2VNO1YphX5hm79DGhBP6TJcDI9o1sCFbUvO2bgxPdGanIFqZK4su6OVLwPHY9GkLQrojgA==", "signatures": [{"sig": "MEUCIQDEIIDRTewS9RFWos0hblttBS8pwI6QW62hj9r0nqk/0QIgJmWXW8zW/mdGtlbte2KSnn6mzdmzpF5X8Z83uRB3+wY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "engine": {"node": ">= 0.8.0"}, "_shasum": "0c539f09291e8ffadde0c8a25850fb2cedc7022d", "gitHead": "1ad808e704e2aeda3a7464b78cacead2fb453727", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/on-finished", "type": "git"}, "_npmVersion": "1.4.21", "description": "Execute a callback when a request closes, finishes, or errors", "directories": {}, "dependencies": {"ee-first": "1.0.5"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.0"}}, "2.1.1": {"name": "on-finished", "version": "2.1.1", "license": "MIT", "_id": "on-finished@2.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-finished", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "dist": {"shasum": "f82ca1c9e3a4f3286b1b9938610e5b8636bd3cb2", "tarball": "https://registry.npmjs.org/on-finished/-/on-finished-2.1.1.tgz", "integrity": "sha512-3ljOi5Zrf46pSbY/39CaJulZQN9XRfmeWqXkeWddhhKD7B4n7nOTisLdaZmAXI1P3A57peTj4pHokMY8X7ICCA==", "signatures": [{"sig": "MEYCIQC3kPPA5BetOVxYsvbxZgMZ7Q5jFP6JTzDrGYSpvx05wQIhAPfiybyFGkR9uehbiE0BK31+qQX1KiMrEUl/M7NlVUSp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "f82ca1c9e3a4f3286b1b9938610e5b8636bd3cb2", "engines": {"node": ">= 0.8"}, "gitHead": "8ec5fa639ace400b543b5bc1821ce909b9acdc03", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/on-finished", "type": "git"}, "_npmVersion": "1.4.21", "description": "Execute a callback when a request closes, finishes, or errors", "directories": {}, "dependencies": {"ee-first": "1.1.0"}, "devDependencies": {"mocha": "~2.0.0", "istanbul": "0.3.2"}}, "2.2.0": {"name": "on-finished", "version": "2.2.0", "license": "MIT", "_id": "on-finished@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-finished", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "dist": {"shasum": "e6ba6a09a3482d6b7969bc3da92c86f0a967605e", "tarball": "https://registry.npmjs.org/on-finished/-/on-finished-2.2.0.tgz", "integrity": "sha512-BqYzuII3QaxNzOE7ZbFf1z11E6vyo1pkRZ0iGJ3tHj+IbzLLeXXNGMGyauk2f9VaiGEFAKvlcIeMdlvD36OHLQ==", "signatures": [{"sig": "MEQCIDUO2h3OCHS+j95qBlhh5ir7Ks8xSM4mciuA6HSycCSNAiB9WFDvnRpBQmbprWfpmFKwO/VXZ2d5JBpZZGpgGFoj5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "e6ba6a09a3482d6b7969bc3da92c86f0a967605e", "engines": {"node": ">= 0.8"}, "gitHead": "fcd56f5674721cac92a16eff93547929716f5192", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/on-finished", "type": "git"}, "_npmVersion": "1.4.28", "description": "Execute a callback when a request closes, finishes, or errors", "directories": {}, "dependencies": {"ee-first": "1.1.0"}, "devDependencies": {"mocha": "~2.0.1", "istanbul": "0.3.5"}}, "2.2.1": {"name": "on-finished", "version": "2.2.1", "license": "MIT", "_id": "on-finished@2.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-finished", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "dist": {"shasum": "5c85c1cc36299f78029653f667f27b6b99ebc029", "tarball": "https://registry.npmjs.org/on-finished/-/on-finished-2.2.1.tgz", "integrity": "sha512-9HvMYLv7im5uzOAcg1lon2cEUxycCF4OI+zPz1R/x3MvBv5s2F+DuxrGwkPe+UwvStDQpWbrkXfLZv12mHbl4A==", "signatures": [{"sig": "MEQCIDQaTTL089PWL/8fGCey1ECyAgGwlJn838iShw6qzlTOAiAuMD/S9XdKJfGt1K2BxAnViW4tpogjXfvYRWVJe8pumQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "5c85c1cc36299f78029653f667f27b6b99ebc029", "engines": {"node": ">= 0.8"}, "gitHead": "f3ecb92fb09d590d314ffe772a6ffd6f76c84223", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/on-finished", "type": "git"}, "_npmVersion": "1.4.28", "description": "Execute a callback when a request closes, finishes, or errors", "directories": {}, "dependencies": {"ee-first": "1.1.0"}, "devDependencies": {"mocha": "~2.2.4", "istanbul": "0.3.9"}}, "2.3.0": {"name": "on-finished", "version": "2.3.0", "license": "MIT", "_id": "on-finished@2.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-finished", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "dist": {"shasum": "20f1336481b083cd75337992a16971aa2d906947", "tarball": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==", "signatures": [{"sig": "MEYCIQCqQA+gUtQ55M7QUzgell40NDB8PR8yxs7tfS/Po29nJQIhAK1qYR5DE0mkOLlPKK/JpEs2XcaHVwd690TNroqLfVND", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "20f1336481b083cd75337992a16971aa2d906947", "engines": {"node": ">= 0.8"}, "gitHead": "34babcb58126a416fcf5205768204f2e12699dda", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/on-finished", "type": "git"}, "_npmVersion": "1.4.28", "description": "Execute a callback when a request closes, finishes, or errors", "directories": {}, "dependencies": {"ee-first": "1.1.1"}, "devDependencies": {"mocha": "2.2.5", "istanbul": "0.3.9"}}, "2.4.0": {"name": "on-finished", "version": "2.4.0", "license": "MIT", "_id": "on-finished@2.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-finished#readme", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "dist": {"shasum": "115776bd5bdfaa1e7c7a2f1db4e2f9d6a4e4c95c", "tarball": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.0.tgz", "fileCount": 5, "integrity": "sha512-SLhghll9XN3OK9DGGmXDaxw6rq7Eza7HfuwdAy1w5S/YWieJ7Jtuv2NfFqnOtu8whIMP81QkEESy8y9/CMccbg==", "signatures": [{"sig": "MEYCIQCegqbkljMZssSt3brwWBYdbcSILaIO1zI5z94R46olGgIhAMsuEFddGZd2+3KlF7+ZFPaRdyQ1/BmFP+OIcPijOHKj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE760ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3/Q//VZkDt53CNW2a7IORq+OAslI7CQmgzacl4sGtZnc+bwPai7k3\r\nnGL84ZAejiPLJSaOFjJvBFIjPtlaR7fY9Y3SZxG97uVWlz05HRYkQHoIbE89\r\nWvAC1lFvt1e5jZCMvoqelh7YpyctF/A1ic+ugF1VNEnVQJl5n13biGLKpa5j\r\nbbOaYqchtHYnksjQJxBDhubWsv6yNbwzRsflQztm0RT3Qcja/S+9PRfgMyiO\r\nmOU9/W2lXBQt0tAcIKqgGfCqCYiaCaTUvK39qC/IIkECFyFf/l9fEQBDdj5+\r\nJg1N1ajOrGL+dcYTwmBtH7MJpd5yv0w0okle/9zntz3iqCOwwgesegJzwKA1\r\nqfBxVuUWARX621UI/33vDeAyrs8tOSRUpX9exd9V0jOLNT+SVo0ROeeI8uz+\r\nQAZMr8JRBlMaikOaWn/ChakPZCI4GjFXHLQbvVwPXDkMZLeoRv+Avd1IBJN7\r\nWL7k0pzM+LFJuvcpQyFl53pNyctLRazqfBB8/+8DzUU34ahEtgw1MLtqyA3o\r\nlRXjYT/syf65pjomQORfOnxnBfr9JdCGO9pBxEFBWJh/xKyVH7Yg4Nv8mT7u\r\nrR1SzloX8bdf9xlQaJRoGhNB+zXFdhP2/6hx4JzV66aohGSRN25aEQ6l73GD\r\nsjP5Ty5XULn8qAq20xSHe7EeyeEr/aeU0JM=\r\n=SY3U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "e4c33cc572be9c834b92d7075d10c8637cba4237", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/on-finished.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Execute a callback when a request closes, finishes, or errors", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"ee-first": "1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.1", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/on-finished_2.4.0_1645461172502_0.10925884086464421", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "on-finished", "version": "2.4.1", "license": "MIT", "_id": "on-finished@2.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/on-finished#readme", "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "dist": {"shasum": "58c8c44116e54845ad57f14ab10b03533184ac3f", "tarball": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "fileCount": 5, "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "signatures": [{"sig": "MEUCIQDiPcGo1Tkzf6tLHsvjpwydBxqjowHpCDv58RQjMRI4/wIgNEQw1iuXRjGZUHtf+ByAxctdy94rKFjLrXX8lF/yUoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFQsIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqR9Q/9EdY7jDt+CobKaZIYabqhM4C+EFRxeZxxdC6AeVgtgm4nMbmj\r\nsYYU1y2NeipZDS4Z6NfChbL49w0/cLkcm7WGiwh/9iSL0moWp8y4+Bor3ugW\r\nxK2q5MHwXWu7k1aIPZwDB9A9agqW4TwBay/RH0caTO8hcIlAD8RDLpREFgJw\r\ncShIrhX0XVGh32UuHE7gpzikRH8965QgleHqeHGiyuynBkXD+R3eHMVrma+4\r\n8ad+y8zClA9ns73NbnoibBYRfWjDx9HpQlQ7GxYOiDDI6QAUW0wKT2m9Bx6i\r\nvZofVMPlnCcuOlX28AX2/Cs1sKhs7I6M+YjlG0zXrIiu7OJ7rdE7VdEVbMf6\r\nUhAUQjD2x9I0cmcbJGdrMPklHNs7a22PYyGXDyoXfOLb9tr/FJUhjTa6MUcm\r\nWy6zhG0T00Xi27Z3tNZdw/+VRA366z4H1L1dfCGauYXaWVkEmIVc7tM/dnSt\r\nvY7VloGYgRemkgd3b3E2G+IFtqrY3SnnfVXKJRDv+9GQpdd+aksxODTvzjRh\r\n3vdf+nTqWdBFxaqdkvIR1I3d9g4s/JEXDgfWfLLNROda+SApwDOTHm85XDrZ\r\niZAT7LdrzGEtLY7OSySP1A2XvccLP/xxYR/vp5GwQwIJXIWzEoI/Pcegt4Ww\r\n1OC9Zk6ujHvn2uDiY7D9dlcngBjcrT2XbOM=\r\n=7BJP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "1111fe8e913debaf3da9bd4f6bda216ef36097fa", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/on-finished.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Execute a callback when a request closes, finishes, or errors", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"ee-first": "1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.1", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/on-finished_2.4.1_1645546248557_0.8707968067148077", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-08-16T05:19:04.281Z", "modified": "2025-05-14T14:56:02.430Z", "2.0.0": "2014-08-16T05:19:04.281Z", "2.1.0": "2014-08-17T02:49:35.720Z", "2.1.1": "2014-10-23T01:19:12.662Z", "2.2.0": "2014-12-23T06:51:30.412Z", "2.2.1": "2015-04-23T01:33:13.247Z", "2.3.0": "2015-05-27T01:58:14.758Z", "2.4.0": "2022-02-21T16:32:52.633Z", "2.4.1": "2022-02-22T16:10:48.714Z"}, "bugs": {"url": "https://github.com/jshttp/on-finished/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/on-finished#readme", "repository": {"url": "git+https://github.com/jshttp/on-finished.git", "type": "git"}, "description": "Execute a callback when a request closes, finishes, or errors", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# on-finished\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][ci-image]][ci-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nExecute a callback when a HTTP request closes, finishes, or errors.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install on-finished\n```\n\n## API\n\n```js\nvar onFinished = require('on-finished')\n```\n\n### onFinished(res, listener)\n\nAttach a listener to listen for the response to finish. The listener will\nbe invoked only once when the response finished. If the response finished\nto an error, the first argument will contain the error. If the response\nhas already finished, the listener will be invoked.\n\nListening to the end of a response would be used to close things associated\nwith the response, like open files.\n\nListener is invoked as `listener(err, res)`.\n\n<!-- eslint-disable handle-callback-err -->\n\n```js\nonFinished(res, function (err, res) {\n  // clean up open fds, etc.\n  // err contains the error if request error'd\n})\n```\n\n### onFinished(req, listener)\n\nAttach a listener to listen for the request to finish. The listener will\nbe invoked only once when the request finished. If the request finished\nto an error, the first argument will contain the error. If the request\nhas already finished, the listener will be invoked.\n\nListening to the end of a request would be used to know when to continue\nafter reading the data.\n\nListener is invoked as `listener(err, req)`.\n\n<!-- eslint-disable handle-callback-err -->\n\n```js\nvar data = ''\n\nreq.setEncoding('utf8')\nreq.on('data', function (str) {\n  data += str\n})\n\nonFinished(req, function (err, req) {\n  // data is read unless there is err\n})\n```\n\n### onFinished.isFinished(res)\n\nDetermine if `res` is already finished. This would be useful to check and\nnot even start certain operations if the response has already finished.\n\n### onFinished.isFinished(req)\n\nDetermine if `req` is already finished. This would be useful to check and\nnot even start certain operations if the request has already finished.\n\n## Special Node.js requests\n\n### HTTP CONNECT method\n\nThe meaning of the `CONNECT` method from RFC 7231, section 4.3.6:\n\n> The CONNECT method requests that the recipient establish a tunnel to\n> the destination origin server identified by the request-target and,\n> if successful, thereafter restrict its behavior to blind forwarding\n> of packets, in both directions, until the tunnel is closed.  Tunnels\n> are commonly used to create an end-to-end virtual connection, through\n> one or more proxies, which can then be secured using TLS (Transport\n> Layer Security, [RFC5246]).\n\nIn Node.js, these request objects come from the `'connect'` event on\nthe HTTP server.\n\nWhen this module is used on a HTTP `CONNECT` request, the request is\nconsidered \"finished\" immediately, **due to limitations in the Node.js\ninterface**. This means if the `CONNECT` request contains a request entity,\nthe request will be considered \"finished\" even before it has been read.\n\nThere is no such thing as a response object to a `CONNECT` request in\nNode.js, so there is no support for one.\n\n### HTTP Upgrade request\n\nThe meaning of the `Upgrade` header from RFC 7230, section 6.1:\n\n> The \"Upgrade\" header field is intended to provide a simple mechanism\n> for transitioning from HTTP/1.1 to some other protocol on the same\n> connection.\n\nIn Node.js, these request objects come from the `'upgrade'` event on\nthe HTTP server.\n\nWhen this module is used on a HTTP request with an `Upgrade` header, the\nrequest is considered \"finished\" immediately, **due to limitations in the\nNode.js interface**. This means if the `Upgrade` request contains a request\nentity, the request will be considered \"finished\" even before it has been\nread.\n\nThere is no such thing as a response object to a `Upgrade` request in\nNode.js, so there is no support for one.\n\n## Example\n\nThe following code ensures that file descriptors are always closed\nonce the response finishes.\n\n```js\nvar destroy = require('destroy')\nvar fs = require('fs')\nvar http = require('http')\nvar onFinished = require('on-finished')\n\nhttp.createServer(function onRequest (req, res) {\n  var stream = fs.createReadStream('package.json')\n  stream.pipe(res)\n  onFinished(res, function () {\n    destroy(stream)\n  })\n})\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/on-finished/master?label=ci\n[ci-url]: https://github.com/jshttp/on-finished/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/on-finished/master\n[coveralls-url]: https://coveralls.io/r/jshttp/on-finished?branch=master\n[node-image]: https://badgen.net/npm/node/on-finished\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/on-finished\n[npm-url]: https://npmjs.org/package/on-finished\n[npm-version-image]: https://badgen.net/npm/v/on-finished\n", "readmeFilename": "README.md", "users": {"285858315": true, "eyson": true, "monjer": true, "semir2": true, "tedyhy": true, "ziflex": true, "aslezak": true, "fullrec": true, "bapinney": true, "dburdese": true, "gejiawen": true, "softwind": true, "mojaray2k": true, "goodseller": true, "kankungyip": true, "simplyianm": true, "craigpatten": true, "tunnckocore": true, "wangnan0610": true, "ivan.marquez": true, "trevorgowing": true, "scottfreecode": true, "shanewholloway": true}}