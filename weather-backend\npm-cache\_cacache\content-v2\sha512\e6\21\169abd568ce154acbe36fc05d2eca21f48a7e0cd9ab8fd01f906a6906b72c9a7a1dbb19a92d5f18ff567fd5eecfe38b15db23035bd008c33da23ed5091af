{"_id": "morgan", "_rev": "559-14573a7346cc0f93ae5752c38c9d3bd2", "name": "morgan", "dist-tags": {"latest": "1.10.1"}, "versions": {"1.0.0": {"name": "morgan", "version": "1.0.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.0.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "83cf74b9f2d841901f1a9a6b8fa7a468d2e47a8d", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.0.0.tgz", "integrity": "sha512-k9QzPEjwMIi8fKmY5LB3sQNk3J7yo7ERjxDrE6Aj2o6Wg/o/ZFU+zw5WNxz5X0jH6cWUzMY37bFMvvWEXanjeA==", "signatures": [{"sig": "MEUCID6F8EWjokLGzd0DmTTbdYMpCNNSh8XpTyLfLJFGlEymAiEAp8u2hOQupHo0/ydKTqOnLmhfbPWe8VerMME05nJWw0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "connect's logger for node.js", "directories": {}, "dependencies": {"bytes": "~0.2.0"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "supertest": "*"}}, "1.0.1": {"name": "morgan", "version": "1.0.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.0.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "8250a5d711de625453022945344d4d6762630c64", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.0.1.tgz", "integrity": "sha512-G98mdPQGAEjWyw2oyxDgS/iH83wtbTrEuzqgjNtO2nhG/GSkpH/nj6I69HQar1YDr+gn0VeKqWCbuHZj3sHhMg==", "signatures": [{"sig": "MEUCIG61VupV6peLgNQC+z63L+rxsKeHwtgwhjRHnY/lyVkHAiEAsaXn/tlwrMRITeV+nBGUII6KhGUjDnLck3uyKsMjCIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "connect's logger for node.js", "directories": {}, "dependencies": {"bytes": "0.3.0"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.0"}}, "1.1.0": {"name": "morgan", "version": "1.1.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.1.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "621a67a51c8eac09348d90f23bb7e4bb9c2c5ef6", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.1.0.tgz", "integrity": "sha512-p2OGkhJvQMtGk0MaQ6ODUEgocxiQF2ALfrYycpFa/85kfze/W6k7kqNVkoVRdyIbpRtISu0fwegkR51fByH3sw==", "signatures": [{"sig": "MEUCIQDbX+vqcTzX+5mgIroMJUt9leY4+1yFT47Pug9XSnCsugIgC5nBtuV8MuY4oWrAvx9EOhOGPJQszmVQIpUOKM5Odg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --require should --reporter dot", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require should --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require should --reporter spec && (cat ./coverage/lcov.info | coveralls || true)"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"bytes": "1.0.0"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.1", "istanbul": "0.2.10", "coveralls": "2.10.0", "supertest": "~0.12.0"}}, "1.1.1": {"name": "morgan", "version": "1.1.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.1.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "cde45d2e807ebcc439745846ea80392e69098146", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.1.1.tgz", "integrity": "sha512-Jx1pZHnbZ43TFAeY0NVuLqpeXX0O2aL7todwFModvpjZCGR+vBTKH0wOKQjwK1wgO/cERhFISIf4roSj1fx5Jg==", "signatures": [{"sig": "MEQCIHgO8W3byOYhid3jN5RVHdHTNgNXYF2s+kAGGWunDE8dAiB++3FiO3xXQaY+Mr0sXtvmMTMZUUi93cKQN6reGlHQCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --require should --reporter dot", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require should --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require should --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"bytes": "1.0.0"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.1", "istanbul": "0.2.10", "supertest": "~0.12.0"}}, "1.2.0": {"name": "morgan", "version": "1.2.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.2.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "8dc17a57599598f80cd7a7e1e3b54e72c689910d", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.2.0.tgz", "integrity": "sha512-VrasIzA69dsxJm1+MVWTLTiij3kiG33XPfGiexqstHpcSvSu/Z51W+FGQyIlbc3jZZuF2PFujsjw+YQvpXz3UA==", "signatures": [{"sig": "MEQCIBu+nu+8XB5W+Om7nYE6BXbiR0/7BSNjLtD77bwMyT1EAiA2bUNvTWrTrfOeTM2eN++McM+hgYBf0xTsk2I9dRQaGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.3", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "0.4.2", "bytes": "1.0.0", "finished": "~1.2.2", "basic-auth": "1.0.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.2.1": {"name": "morgan", "version": "1.2.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.2.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "ecb7a731f48dc74ec8b81bacb22c72d12830ab41", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.2.1.tgz", "integrity": "sha512-+TgeW7zT4MmxR4m/Ov8e5gGsKnMlSQN5PJfUee94KSXhgnIpYcgOjMFeLMuuq0T0EXJo5necPUX+KHpclBgO9w==", "signatures": [{"sig": "MEUCIQCPKgk5MBmA5ASlJFBbLk4AJdJpZ+YTXKJu9u1wzGxcZAIgYKfnguzc9ImIwQPkdmiyeiML+Y1MqSTx8GfVbHu7d/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.3", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "0.4.3", "bytes": "1.0.0", "finished": "~1.2.2", "basic-auth": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.2.2": {"name": "morgan", "version": "1.2.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.2.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "ad51246d8920e147883a32d9833c46d37c7b6275", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.2.2.tgz", "integrity": "sha512-JIWtKJym6CbOE58uDSwtt2+CKFC22pkyd2CPBLxEPtAnL0W5XxvxtsAhiBJicCPhzQV885TuGpwww6UUVZArgQ==", "signatures": [{"sig": "MEQCIGeW3+AigUoFngMmEpNmYvwRPoAJ7fwdR5t6MUFx39ZLAiAfHCTWSIUXNZbMeKihSvj2i7z+rPOJvK99MspFbWk7cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.3", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "0.4.4", "bytes": "1.0.0", "finished": "~1.2.2", "basic-auth": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.2.3": {"name": "morgan", "version": "1.2.3", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.2.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "3b0f1704df90255a542591abacd797891a8c40a1", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.2.3.tgz", "integrity": "sha512-epRQVQDr/otLiBetuLFEwQWHXiuIahy9ezUzpVDYzO8OtgzqDty8VCpd8hs7HGnrRk0LXlFdi9zDmMjajmWwvw==", "signatures": [{"sig": "MEUCIEJOqiHsa95f6If0tCA0go4TpF6TWCZ38ZCof7qQIJVRAiEA2awA6Rm6sYcCB0fNP8ZlS0eZasHokNvJRHTYO8+Ur2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "3b0f1704df90255a542591abacd797891a8c40a1", "engines": {"node": ">= 0.8.0"}, "gitHead": "733e01ec43b0b25c1a2b6bd1a6d3e5ca845ac95a", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.21", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "0.4.4", "bytes": "1.0.0", "basic-auth": "1.0.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.3.0": {"name": "morgan", "version": "1.3.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.3.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "ae8e835e365b306a10803a90ddfe27b4a33594f9", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.3.0.tgz", "integrity": "sha512-tilU856DdLabD42cELxKnhNSXHQJE+2Yp8n0x0FdQ/KNEA6UtuDO9dXTp7wesr8ELdTxJSXUSyy48T18x1XpQg==", "signatures": [{"sig": "MEUCIQCQ3irRbbw6DhOjPrJMAIGwKTRza4ovnHTE9rcdlggfIwIgK/cxo0nh7muP1jyh6q8wZ1ReD/f2SFzQTmy6Cafm34E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "ae8e835e365b306a10803a90ddfe27b4a33594f9", "engines": {"node": ">= 0.8.0"}, "gitHead": "0b44ec02f5561f77ea69185973aaf713f5de8375", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.21", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "0.4.4", "bytes": "1.0.0", "basic-auth": "1.0.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.3.1": {"name": "morgan", "version": "1.3.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.3.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "5c2ae66ef1da03f0ac9f0f42840cca5d8bfec23f", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.3.1.tgz", "integrity": "sha512-AyK48poMw+ZMwpVk3mn0o2gMLg3iXgjaQ6jU6m7VZmom3lkRmRimd9ux6BrXtdM0BcCN4g37Sll3CZ1onZd1iw==", "signatures": [{"sig": "MEUCIFi2jTxM6ofB5LWnTcWwpVAzjBHe5GZnM/mg1fAsegWdAiEA0EwsOHs4ZJ/g4RF+KMQQtrgXRC2UPvPhSDf8Z79K23E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "5c2ae66ef1da03f0ac9f0f42840cca5d8bfec23f", "engines": {"node": ">= 0.8.0"}, "gitHead": "a7192887a0f388d6f397bd70c836a63d6b8afc76", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.21", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "0.4.5", "basic-auth": "1.0.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.3.2": {"name": "morgan", "version": "1.3.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.3.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "ac41aa15221ee4e5f2ac843896b6918139a18efd", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.3.2.tgz", "integrity": "sha512-Q+4Zi1ma/NOtxKMdDSs3vbJ1gcNJpaX4EL4oZkNGPiZdoh8EQVRMaYu/08Eken0L9aIg2Wi16cePnxNSBHqlAA==", "signatures": [{"sig": "MEUCIQDWBirLkN9lbweuyK/ak+iGuTJWfJpzTfZuZYTjic9a2QIgWYJ497Ve6xF6R9jcban8Ns8yvvN2CdSGhJ0kHDepGY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "ac41aa15221ee4e5f2ac843896b6918139a18efd", "engines": {"node": ">= 0.8.0"}, "gitHead": "90206954abeb32f867cd7db30bfa2eba64f1f0e8", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.21", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "0.4.5", "basic-auth": "1.0.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.4.0": {"name": "morgan", "version": "1.4.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.4.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "ce3c6ee28f794f85f59165476575b70ed386eb3d", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.4.0.tgz", "integrity": "sha512-r7C2fz1THV/8FTrMuLteGvqyz3Pd7JgD1cNdYIL6vRYhq6zSnw/JuMi0OEZO9lR5l1x1SEgzVuUHfpuObUZ0NQ==", "signatures": [{"sig": "MEQCIFK9Mq8Em0EzZnMYbeJeN6uGdmv2eLmIZ1m+fC0uurIhAiBGUcFaEh/I+pv40563qR7xIZo7qk68xiKUPQPc1Clkow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "ce3c6ee28f794f85f59165476575b70ed386eb3d", "engines": {"node": ">= 0.8.0"}, "gitHead": "aab13ecbf74b1cb74bbe13ffe110576acbfb5279", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.21", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.0.0", "debug": "~2.1.0", "basic-auth": "1.0.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.5", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.4.1": {"name": "morgan", "version": "1.4.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.4.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "cd9600c3fa74e2fdf22ba0f1d026c20cb96f25fe", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.4.1.tgz", "integrity": "sha512-miTjw0gjk8JEP8IMGOR5YwLZVFg1GLvxbTp84tzx632PlysK91fSxKHQpTaran/lJPvSl8Hhj7LuWRt2x/h2SQ==", "signatures": [{"sig": "MEUCIHNFs8ba+vDSVULvKNV9E9ODe0pQrQUkIsCdzlMTLWTAAiEAk2aRqheaFXquInmR3i6nPdelu4xhToPOA7eDonSp9Tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "cd9600c3fa74e2fdf22ba0f1d026c20cb96f25fe", "engines": {"node": ">= 0.8.0"}, "gitHead": "e2cae6ad7d772a0f1c90aa680f908b5f8d29e2c7", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.21", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.0.0", "debug": "~2.1.0", "basic-auth": "1.0.0", "on-finished": "2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.5.0": {"name": "morgan", "version": "1.5.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "morgan@1.5.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "6d2cf4f142a3c78b36762711d79b13f8e35b5e83", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.5.0.tgz", "integrity": "sha512-9pBs1FZ1NxvLlpxGvcNBEtHOH6Ba/I+3xlUFC59uH5fbh5u9v4PN7tPjd7bXv+w/vhb3E+i324yJ/yO0QpK3/A==", "signatures": [{"sig": "MEUCIFJUOQFfJFobEEGNcHp+ypacTTYBiI5qL9ckjPIfRmjpAiEA2Rv6El8BDk6zRiERJaKwoHOwENkJYB8wR8evwwtvnDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "6d2cf4f142a3c78b36762711d79b13f8e35b5e83", "engines": {"node": ">= 0.8.0"}, "gitHead": "262c40de4df67f1972d10600157a79e4967d9bc5", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.21", "description": "http request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.0.0", "debug": "~2.1.0", "basic-auth": "1.0.0", "on-finished": "2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.5.1": {"name": "morgan", "version": "1.5.1", "license": "MIT", "_id": "morgan@1.5.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "a9688eed9187ab648d816fc09c696fae882e16f6", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.5.1.tgz", "integrity": "sha512-7DBd65881lsrAz48wRFevD5Aj6Ij2y6Tt7U0phFBjboI4O2wVlydEru37IyYx090p8ROvIj3rPRWb5zMI+GLVg==", "signatures": [{"sig": "MEUCIQDjs9bw+JRLU9v4zQwjNvqqDWTHS9zrnBlFApe2jj8R/QIgMQ8xwmveqr8MNZOv7vVFCzjeIApKgxT9Uzb6VWydazo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "a9688eed9187ab648d816fc09c696fae882e16f6", "engines": {"node": ">= 0.8.0"}, "gitHead": "ae84a264fb9cdd7b4d7e2bf19d93b0b51da99996", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.0.0", "debug": "~2.1.1", "basic-auth": "1.0.0", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.5.2": {"name": "morgan", "version": "1.5.2", "license": "MIT", "_id": "morgan@1.5.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "34c1a0e7c2d5ad3ed78f0ef3257b8ac7c35d7cff", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.5.2.tgz", "integrity": "sha512-7Hi2BV5HI5NdvJkRlPiWCNixv0Aa8BRCN49wcu+Lx1oPhUj19h2jET3hMiKVN4N6B18OHLJvgjTwPNGz5H81XQ==", "signatures": [{"sig": "MEUCIQCk1aA0o9f8nPBvSnoN5QuJTLgySzG6pY2bIVisvBCuZQIgcBfXL0NaBeRoRdBtuMGSWftFim8I8m+zzfel7CLouEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "34c1a0e7c2d5ad3ed78f0ef3257b8ac7c35d7cff", "engines": {"node": ">= 0.8.0"}, "gitHead": "b08705a7dc059dabc671a7b58c4bf6d81fccce85", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.0.0", "debug": "~2.1.3", "basic-auth": "1.0.0", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.2.1", "istanbul": "0.3.7", "supertest": "~0.15.0"}}, "1.5.3": {"name": "morgan", "version": "1.5.3", "license": "MIT", "_id": "morgan@1.5.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "8adb4e72f9e5c5436e5d93f42910835f79da9fdf", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.5.3.tgz", "integrity": "sha512-+4p403LxsJWcE8MRQlEkXqlZp/8B387URRZmIaqR36rr8X5sLiqWLXfnxjHIwY/f9g1pUqgCGFV5M1EaZm6WAw==", "signatures": [{"sig": "MEYCIQDqNrifyaeDtRbIy2h/OV8SVuNQLRyb717s4qLWE2yOhQIhANHQaxxBPNnb4cLJVlROP0Mdk5W3R9THGoS4Gyn59TQy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "8adb4e72f9e5c5436e5d93f42910835f79da9fdf", "engines": {"node": ">= 0.8.0"}, "gitHead": "7d5a190e6e22c4871c15aff2143bf76b808052cf", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.0.1", "debug": "~2.2.0", "basic-auth": "~1.0.1", "on-finished": "~2.2.1"}, "devDependencies": {"mocha": "~2.2.4", "istanbul": "0.3.9", "supertest": "~0.15.0"}}, "1.6.0": {"name": "morgan", "version": "1.6.0", "license": "MIT", "_id": "morgan@1.6.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "1a56ee781e2349a741ab0adf34f16ce4f25b1806", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.6.0.tgz", "integrity": "sha512-Rz6nGMNtV13ywpzmrNZSLrXjTtVvdz//+yTS3flfcYWH9UukMs829bXVAoL4ElmPIPmHC3EYqC/oADJELRjcGA==", "signatures": [{"sig": "MEYCIQDgEWOtueUNRBfNcgbRo8ocom4FmGCqwRWsHgUFjaSY6QIhAIj4WW27AlcyvxtZTrTOFemXblI2w+iLjoqMDt7g4GEQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "1a56ee781e2349a741ab0adf34f16ce4f25b1806", "engines": {"node": ">= 0.8.0"}, "gitHead": "8fbacc95c984be4c2538b67cd7294946c661d993", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.0.1", "debug": "~2.2.0", "basic-auth": "~1.0.2", "on-headers": "~1.0.0", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.2.5", "split": "1.0.0", "istanbul": "0.3.15", "supertest": "1.0.1"}}, "1.6.1": {"name": "morgan", "version": "1.6.1", "license": "MIT", "_id": "morgan@1.6.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "5fd818398c6819cba28a7cd6664f292fe1c0bbf2", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.6.1.tgz", "integrity": "sha512-WWxlTx5xCqbtSeX/gPVHUZBhAhSMfYQLgPrWHEN0FYnF+zf1Ju/Zct6rpeKmvzibrYF4QvFVws7IN61BxnKu+Q==", "signatures": [{"sig": "MEQCIA6hSpuS4CQcrZj5Ej/JMCcSLdNVLR0GRRMUZnnSzsq4AiA0dEup9rpAf7F5D6QNBwpkN5IzVO/EdEjF2+g0UsA1LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "5fd818398c6819cba28a7cd6664f292fe1c0bbf2", "engines": {"node": ">= 0.8.0"}, "gitHead": "300286d1472928b10f723e8ea138533dfbd3b521", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.0.1", "debug": "~2.2.0", "basic-auth": "~1.0.3", "on-headers": "~1.0.0", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.2.5", "split": "1.0.0", "istanbul": "0.3.17", "supertest": "1.0.1"}}, "1.7.0": {"name": "morgan", "version": "1.7.0", "license": "MIT", "_id": "morgan@1.7.0", "maintainers": [{"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "eb10ca8e50d1abe0f8d3dad5c0201d052d981c62", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.7.0.tgz", "integrity": "sha512-3qY2DuBxulwkdeRfkCoUMs2w7/w46/dLaXIAgw1atavLRj5dg04uxyFFHw0IIbin8D+ErgTTnlOshZgDfZwIPQ==", "signatures": [{"sig": "MEQCICboOf1YLxKYoSYY0fJjDAgh9pLH1fd1RdWPk6Yahv/gAiBmjDlfIOhVGBNZ2vfw+APhW1QcjyFr8OOkf1wah1AoNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "eb10ca8e50d1abe0f8d3dad5c0201d052d981c62", "engines": {"node": ">= 0.8.0"}, "gitHead": "5da5ff1f5446e3f3ff29d29a2d6582712612bf89", "scripts": {"test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/morgan", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP request logger middleware for node.js", "directories": {}, "dependencies": {"depd": "~1.1.0", "debug": "~2.2.0", "basic-auth": "~1.0.3", "on-headers": "~1.0.1", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.4.5", "split": "1.0.0", "istanbul": "0.4.2", "supertest": "1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/morgan-1.7.0.tgz_1455857999079_0.07957377028651536", "host": "packages-6-west.internal.npmjs.com"}}, "1.8.0": {"name": "morgan", "version": "1.8.0", "keywords": ["express", "http", "logger", "middleware"], "license": "MIT", "_id": "morgan@1.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan#readme", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "7884d7816b7e5a22db11a9e3a572b197a0e5566c", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.8.0.tgz", "integrity": "sha512-W+T5ZIXQb5h++tw/d/SAGahY5yzy3jTo/G5ijieLrQ7vBJglIGfyqk3YjoZp80c9+w2nB7tN+kMhq0465CLrRQ==", "signatures": [{"sig": "MEQCIBswY4nJuey+WtHVXiZAzmf47jB8rJgo4kTC+LYTHmD3AiBME9Y+wO4fYch2ghsPEIzXFCcZuAhO6ukKMGXughXGxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "7884d7816b7e5a22db11a9e3a572b197a0e5566c", "engines": {"node": ">= 0.8.0"}, "gitHead": "b0e3c90ee07ba549a2b5e835268500756eb6e154", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "HTTP request logger middleware for node.js", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"depd": "~1.1.0", "debug": "2.6.0", "basic-auth": "~1.1.0", "on-headers": "~1.0.1", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "split": "1.0.0", "eslint": "3.15.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/morgan-1.8.0.tgz_1486255673952_0.6137548687402159", "host": "packages-18-east.internal.npmjs.com"}}, "1.8.1": {"name": "morgan", "version": "1.8.1", "keywords": ["express", "http", "logger", "middleware"], "license": "MIT", "_id": "morgan@1.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan#readme", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "f93023d3887bd27b78dfd6023cea7892ee27a4b1", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.8.1.tgz", "integrity": "sha512-aoRMOJL9XCA0Q4ur3UnE+olo4zu2gPp9Zmtw3hLO+ssL1Ju2bUR7hC3OPNM9Xv6sdHdCFpEZmC3rnPW81RFOvQ==", "signatures": [{"sig": "MEQCIBjDRXYHRauMe9oRYZzA8ukbYbrTWmW/3MaJd9oyTtprAiAQNuVSlOK85Pd768YxT4mkjLYkMMNeXgY2wyYueZIEBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "f93023d3887bd27b78dfd6023cea7892ee27a4b1", "engines": {"node": ">= 0.8.0"}, "gitHead": "29daba369e388522656183463fae1fb1a1dda609", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "HTTP request logger middleware for node.js", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"depd": "~1.1.0", "debug": "2.6.1", "basic-auth": "~1.1.0", "on-headers": "~1.0.1", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "split": "1.0.0", "eslint": "3.15.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/morgan-1.8.1.tgz_1486777709464_0.3650192436762154", "host": "packages-18-east.internal.npmjs.com"}}, "1.8.2": {"name": "morgan", "version": "1.8.2", "keywords": ["express", "http", "logger", "middleware"], "license": "MIT", "_id": "morgan@1.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan#readme", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "784ac7734e4a453a9c6e6e8680a9329275c8b687", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.8.2.tgz", "integrity": "sha512-XphtlsSgT7ISH8TCvfK+twQE/7xaU/lE9xy5FBcQBxFyvDLG2BZleWZx7pikwb+2VT8B4pv43ud8klzEADQQAw==", "signatures": [{"sig": "MEYCIQCSeinbsjPNh4UJ4eOn+hB+eUSzGKIgQxCJPA1c32h1UgIhAN1657DKTnO9+8bYEj3bxie3h13941j+uok69Bv2YO8G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "784ac7734e4a453a9c6e6e8680a9329275c8b687", "engines": {"node": ">= 0.8.0"}, "gitHead": "475ae38b5c308113bbf6b3a535351ceb2c419682", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "HTTP request logger middleware for node.js", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"depd": "~1.1.0", "debug": "2.6.8", "basic-auth": "~1.1.0", "on-headers": "~1.0.1", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "split": "1.0.0", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/morgan-1.8.2.tgz_1495591299170_0.17843790841288865", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "morgan", "version": "1.9.0", "keywords": ["express", "http", "logger", "middleware"], "license": "MIT", "_id": "morgan@1.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan#readme", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "d01fa6c65859b76fcf31b3cb53a3821a311d8051", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.9.0.tgz", "integrity": "sha512-vqY5UfyHbGQZ3KXlGtNvAqGzOkPxrFTtChQdP146QCjpUhbtgW/aQIb2gUVR0jY0rH+MNArmklXiXxDd+L8fmA==", "signatures": [{"sig": "MEQCIHpwDSzh2N0YMOIOw2JAI7DxdUJKDsy7HIYIaLEFJQJgAiArhdzCEfrBGv+BNIJBTsGSB6SCgg9kwugPdJfMDRrHcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "d01fa6c65859b76fcf31b3cb53a3821a311d8051", "engines": {"node": ">= 0.8.0"}, "gitHead": "4def0fa6d4ac703dc5c76f901e997af667a27d65", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "HTTP request logger middleware for node.js", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"depd": "~1.1.1", "debug": "2.6.9", "basic-auth": "~2.0.0", "on-headers": "~1.0.1", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "split": "1.0.1", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/morgan-1.9.0.tgz_1506479941546_0.9185023584868759", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "morgan", "version": "1.9.1", "keywords": ["express", "http", "logger", "middleware"], "license": "MIT", "_id": "morgan@1.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan#readme", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "0a8d16734a1d9afbc824b99df87e738e58e2da59", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.9.1.tgz", "fileCount": 5, "integrity": "sha512-HQStPIV4y3afTiCYVxirakhlCfGkI161c76kKFca7Fk1JusM//Qeo1ej2XaMniiNeaZklMVrh3vTtIzpzwbpmA==", "signatures": [{"sig": "MEUCIQCrCG58pOUr1PK1PmqSnf2QZxqchkrM4kl9RPT4JCPB/QIgZ6qDtLiH1lb71EoIv8L9eX/6xshTnP2b9YNDn99snMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblxaDCRA9TVsSAnZWagAAtc0P/3oBWYaI+HA4WA6n5r2+\nuY+vPTYhKWfMlgeyFrCbsdgWDXxQfKYWjFOqVMqTv6UlnQGrvuatlbA5OvyX\nxBW3WneQ7pamhCylW0kPv5HyH2xLAD45z21ndOSjAdqt+Q5PMgjhDL+9BeIm\no6aYCdLHzLvZc/jH48we/Frfwe/TMRj3xdbPoVP0HaCMuQS23225ZRwgNBr5\n542SJgX0cGYkSjnP0kQOrMCUEHxMlS7yWzDzDA2zcVWBMQfXNm0XwfPw8goN\nWcfhmE7BQrFAPZWxoERufoMIuGIjV/A1UIWzdy0MI1TgyBLJYNLfgdjKx3J0\nrx8gtrX8ZxjImpaRbFWkwSsj4A3SxlfQqEUizoqL7uHSiQCd29MkZ5JiO0Mi\n81fDFjph5UIlkf0zMNxRdbhFQCGqdiFJz2eB40MtfCq4rbMOZ1Qp3UC8/iYn\nW5TFEe5Tf197YlHu8klLmATluyjNiHRyrIvtRB9Yu9YVAjjfJ+mJOXeDMEPm\nabw7sZgqY/W8hrgoOeo5eW5K96SZUP9L5MF4PRyUvpk6tQfNhfF5ICqZzhVw\nYovHhH0LqFDLW5IEG03VXFd/kqblTbjvlKENmpVlLl6ITEXw6n674F4iYu2C\nTo4pACctFIOLKO0XoCCTAXYp7vOYbM4i4AMCOBmxSBodB8uoND+qCHD9EMK9\nNKEv\r\n=BHdp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "572dd937f26d486babc709228c98fd15dd807408", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --check-leaks --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --check-leaks --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "HTTP request logger middleware for node.js", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"depd": "~1.1.2", "debug": "2.6.9", "basic-auth": "~2.0.0", "on-headers": "~1.0.1", "on-finished": "~2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "2.5.3", "split": "1.0.1", "eslint": "5.5.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/morgan_1.9.1_1536628354837_0.49383079449866707", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "morgan", "version": "1.10.0", "keywords": ["express", "http", "logger", "middleware"], "license": "MIT", "_id": "morgan@1.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/morgan#readme", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "dist": {"shasum": "091778abc1fc47cd3509824653dae1faab6b17d7", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.10.0.tgz", "fileCount": 5, "integrity": "sha512-AbegBVI4sh6El+1gNwvD5YIck7nSA36weD7xvIxG4in80j/UoK8AEGaWnnz8v1GxonMCltmlNs5ZKbGvl9b1XQ==", "signatures": [{"sig": "MEUCIDkJK9U+FaA92DfTMpzE5txBdf4TvKehx2audTWQiCkXAiEAiZ96vOKdyP2+/35/ut7CtzwmuzdYT5q1VfQPVdtgNK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2zqfCRA9TVsSAnZWagAAYaEP/1tEGrNEqR9e7FUMoH/d\nvfF4oJHXOjo1NLOTbZ/QN7ysnQp3reM+IDTrLZbGRacoe8cBNGyGcCnjIzNq\nmpFax8+UPK+2aHiNRz8/MkOhgJ947ptVNDQmVVBVYDPqXZzEko/V8b4l8dWe\nFvFHmjzaWfevPgq93XhECtILi+zjVWQBG6CgIjk84Qkg7pKnRM7bhfDK0GhG\naBxwMy4A8FTYXEyw8vDkXI/xH3nNf3m0C9KeNu0X5Nfwtv1r/fYN1uT5c40C\nnZXAxNuubkiw0zx5aiCQJz2WS3zv0cI2Ie5g3VcHPjasvmzmkBAhhphJk7n8\ny5zrVikHIK+5hULSZTE8H5u9C01Ql/UWcAkpnEsJgWBdWBdZIGRYZLNlNV8N\nUOPR6IPjlzsLamGRIw+lUxFFrAqMOnuBps2b/6Y8OKRCOuxOuhHGT3t+CVVx\nLsFNybystLt5g6kX4kyC9aYqXx+h2IQMBOB29XhGtemh9iSYSxMpkgB7MWSx\nx8/1E6rKOEsyx/HkOPwz3tCYleTte+rdL5jttK3CHXSSfSoHyiFWmYJGyb5o\nalnutcny7GfBr/ochDlmNY9z7KuKYY9S+MWlXf75i6GsO/MqDI2MZRF0IBac\n/FYta9jrhtYbiBQI8uYsAe0YjhKdgv58oPeOZwitnyg+79Mclf8JPtcOm3Rg\ndfmN\r\n=lMK4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "c68d2eab4c6a5d9940895a6d1614964d44358642", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/morgan.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "HTTP request logger middleware for node.js", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"depd": "~2.0.0", "debug": "2.6.9", "basic-auth": "~2.0.1", "on-headers": "~1.0.2", "on-finished": "~2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.0", "mocha": "7.1.1", "split": "1.0.1", "eslint": "6.8.0", "supertest": "4.0.2", "eslint-plugin-node": "9.2.0", "eslint-plugin-import": "2.20.1", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.0", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/morgan_1.10.0_1584727213227_0.7066876282414851", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "morgan", "description": "HTTP request logger middleware for node.js", "version": "1.10.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "keywords": ["express", "http", "logger", "middleware"], "repository": {"type": "git", "url": "git+https://github.com/expressjs/morgan.git"}, "dependencies": {"basic-auth": "~2.0.1", "debug": "2.6.9", "depd": "~2.0.0", "on-finished": "~2.3.0", "on-headers": "~1.1.0"}, "devDependencies": {"eslint": "6.8.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.20.2", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "10.4.0", "nyc": "15.1.0", "split": "1.0.1", "supertest": "4.0.2"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_id": "morgan@1.10.1", "gitHead": "05ee0a83e7dc6fd0ab5eacddde7aa2d5cc743fcf", "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "homepage": "https://github.com/expressjs/morgan#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-223dMRJtI/l25dJKWpgij2cMtywuG/WiUKXdvwfbhGKBhy1puASqXwFzmWZ7+K73vUPoR7SS2Qz2cI/g9MKw0A==", "shasum": "4e02e6a4465a48e26af540191593955d17f61570", "tarball": "https://registry.npmjs.org/morgan/-/morgan-1.10.1.tgz", "fileCount": 5, "unpackedSize": 30593, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCbFkLSKxiMPcI3b88nR5p7oN5BRsnFgy3F9efmneyQvAIgDiFU5jsAw8QVKRn33shAOSSP03bGBua0uYwH17nuIgE="}]}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/morgan_1.10.1_1752768866017_0.6676752978116725"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-02-08T19:19:24.247Z", "modified": "2025-07-17T16:14:26.364Z", "1.0.0": "2014-02-08T19:19:24.247Z", "1.0.1": "2014-05-05T02:24:08.112Z", "1.1.0": "2014-05-19T02:58:09.506Z", "1.1.1": "2014-05-21T00:37:33.859Z", "1.2.0": "2014-07-20T04:43:26.387Z", "1.2.1": "2014-07-26T20:36:30.481Z", "1.2.2": "2014-07-27T19:27:37.659Z", "1.2.3": "2014-08-17T03:02:44.575Z", "1.3.0": "2014-09-02T04:40:10.622Z", "1.3.1": "2014-09-14T16:45:45.169Z", "1.3.2": "2014-09-28T03:41:37.865Z", "1.4.0": "2014-10-17T01:28:49.389Z", "1.4.1": "2014-10-23T03:07:40.290Z", "1.5.0": "2014-11-07T06:13:59.487Z", "1.5.1": "2014-12-31T19:23:19.367Z", "1.5.2": "2015-03-15T20:10:36.552Z", "1.5.3": "2015-05-11T06:43:31.370Z", "1.6.0": "2015-06-13T06:17:35.005Z", "1.6.1": "2015-07-04T03:04:11.234Z", "1.7.0": "2016-02-19T05:00:03.572Z", "1.8.0": "2017-02-05T00:47:54.712Z", "1.8.1": "2017-02-11T01:48:30.165Z", "1.8.2": "2017-05-24T02:01:41.057Z", "1.9.0": "2017-09-27T02:39:05.095Z", "1.9.1": "2018-09-11T01:12:35.037Z", "1.10.0": "2020-03-20T18:00:13.316Z", "1.10.1": "2025-07-17T16:14:26.192Z"}, "bugs": {"url": "https://github.com/expressjs/morgan/issues"}, "license": "MIT", "homepage": "https://github.com/expressjs/morgan#readme", "keywords": ["express", "http", "logger", "middleware"], "repository": {"type": "git", "url": "git+https://github.com/expressjs/morgan.git"}, "description": "HTTP request logger middleware for node.js", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}], "readme": "# morgan\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Build Status][ci-image]][ci-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nHTTP request logger middleware for node.js\n\n> Named after [<PERSON>](http://en.wikipedia.org/wiki/<PERSON>_<PERSON>), a show you should not watch until completion.\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install morgan\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar morgan = require('morgan')\n```\n\n### morgan(format, options)\n\nCreate a new morgan logger middleware function using the given `format` and `options`.\nThe `format` argument may be a string of a predefined name (see below for the names),\na string of a format string, or a function that will produce a log entry.\n\nThe `format` function will be called with three arguments `tokens`, `req`, and `res`,\nwhere `tokens` is an object with all defined tokens, `req` is the HTTP request and `res`\nis the HTTP response. The function is expected to return a string that will be the log\nline, or `undefined` / `null` to skip logging.\n\n#### Using a predefined format string\n\n<!-- eslint-disable no-undef -->\n\n```js\nmorgan('tiny')\n```\n\n#### Using format string of predefined tokens\n\n<!-- eslint-disable no-undef -->\n\n```js\nmorgan(':method :url :status :res[content-length] - :response-time ms')\n```\n\n#### Using a custom format function\n\n<!-- eslint-disable no-undef -->\n\n``` js\nmorgan(function (tokens, req, res) {\n  return [\n    tokens.method(req, res),\n    tokens.url(req, res),\n    tokens.status(req, res),\n    tokens.res(req, res, 'content-length'), '-',\n    tokens['response-time'](req, res), 'ms'\n  ].join(' ')\n})\n```\n\n#### Options\n\nMorgan accepts these properties in the options object.\n\n##### immediate\n\nWrite log line on request instead of response. This means that a requests will\nbe logged even if the server crashes, _but data from the response (like the\nresponse code, content length, etc.) cannot be logged_.\n\n##### skip\n\nFunction to determine if logging is skipped, defaults to `false`. This function\nwill be called as `skip(req, res)`.\n\n<!-- eslint-disable no-undef -->\n\n```js\n// EXAMPLE: only log error responses\nmorgan('combined', {\n  skip: function (req, res) { return res.statusCode < 400 }\n})\n```\n\n##### stream\n\nOutput stream for writing log lines, defaults to `process.stdout`.\n\n#### Predefined Formats\n\nThere are various pre-defined formats provided:\n\n##### combined\n\nStandard Apache combined log output.\n```\n:remote-addr - :remote-user [:date[clf]] \":method :url HTTP/:http-version\" :status :res[content-length] \":referrer\" \":user-agent\"\n# will output\n::1 - - [27/Nov/2024:06:21:42 +0000] \"GET /combined HTTP/1.1\" 200 2 \"-\" \"curl/8.7.1\"\n```\n\n##### common\n\nStandard Apache common log output.\n\n```\n:remote-addr - :remote-user [:date[clf]] \":method :url HTTP/:http-version\" :status :res[content-length]\n# will output\n::1 - - [27/Nov/2024:06:21:46 +0000] \"GET /common HTTP/1.1\" 200 2\n```\n\n##### dev\n\nConcise output colored by response status for development use. The `:status`\ntoken will be colored green for success codes, red for server error codes,\nyellow for client error codes, cyan for redirection codes, and uncolored\nfor information codes.\n\n```\n:method :url :status :response-time ms - :res[content-length]\n# will output\nGET /dev 200 0.224 ms - 2\n```\n\n##### short\n\nShorter than default, also including response time.\n\n```\n:remote-addr :remote-user :method :url HTTP/:http-version :status :res[content-length] - :response-time ms\n# will output\n::1 - GET /short HTTP/1.1 200 2 - 0.283 ms\n```\n\n##### tiny\n\nThe minimal output.\n\n```\n:method :url :status :res[content-length] - :response-time ms\n# will output\nGET /tiny 200 2 - 0.188 ms\n```\n\n#### Tokens\n\n##### Creating new tokens\n\nTo define a token, simply invoke `morgan.token()` with the name and a callback function.\nThis callback function is expected to return a string value. The value returned is then\navailable as \":type\" in this case:\n\n<!-- eslint-disable no-undef -->\n\n```js\nmorgan.token('type', function (req, res) { return req.headers['content-type'] })\n```\n\nCalling `morgan.token()` using the same name as an existing token will overwrite that\ntoken definition.\n\nThe token function is expected to be called with the arguments `req` and `res`, representing\nthe HTTP request and HTTP response. Additionally, the token can accept further arguments of\nit's choosing to customize behavior.\n\n##### :date[format]\n\nThe current date and time in UTC. The available formats are:\n\n  - `clf` for the common log format (`\"10/Oct/2000:13:55:36 +0000\"`)\n  - `iso` for the common ISO 8601 date time format (`2000-10-10T13:55:36.000Z`)\n  - `web` for the common RFC 1123 date time format (`Tue, 10 Oct 2000 13:55:36 GMT`)\n\nIf no format is given, then the default is `web`.\n\n##### :http-version\n\nThe HTTP version of the request.\n\n##### :method\n\nThe HTTP method of the request.\n\n##### :referrer\n\nThe Referrer header of the request. This will use the standard mis-spelled Referer header if exists, otherwise Referrer.\n\n##### :remote-addr\n\nThe remote address of the request. This will use `req.ip`, otherwise the standard `req.connection.remoteAddress` value (socket address).\n\n##### :remote-user\n\nThe user authenticated as part of Basic auth for the request.\n\n##### :req[header]\n\nThe given `header` of the request. If the header is not present, the\nvalue will be displayed as `\"-\"` in the log.\n\n##### :res[header]\n\nThe given `header` of the response. If the header is not present, the\nvalue will be displayed as `\"-\"` in the log.\n\n##### :response-time[digits]\n\nThe time between the request coming into `morgan` and when the response\nheaders are written, in milliseconds.\n\nThe `digits` argument is a number that specifies the number of digits to\ninclude on the number, defaulting to `3`, which provides microsecond precision.\n\n##### :status\n\nThe status code of the response.\n\nIf the request/response cycle completes before a response was sent to the\nclient (for example, the TCP socket closed prematurely by a client aborting\nthe request), then the status will be empty (displayed as `\"-\"` in the log).\n\n##### :total-time[digits]\n\nThe time between the request coming into `morgan` and when the response\nhas finished being written out to the connection, in milliseconds.\n\nThe `digits` argument is a number that specifies the number of digits to\ninclude on the number, defaulting to `3`, which provides microsecond precision.\n\n##### :url\n\nThe URL of the request. This will use `req.originalUrl` if exists, otherwise `req.url`.\n\n##### :user-agent\n\nThe contents of the User-Agent header of the request.\n\n### morgan.compile(format)\n\nCompile a format string into a `format` function for use by `morgan`. A format string\nis a string that represents a single log line and can utilize token syntax.\nTokens are references by `:token-name`. If tokens accept arguments, they can\nbe passed using `[]`, for example: `:token-name[pretty]` would pass the string\n`'pretty'` as an argument to the token `token-name`.\n\nThe function returned from `morgan.compile` takes three arguments `tokens`, `req`, and\n`res`, where `tokens` is object with all defined tokens, `req` is the HTTP request and\n`res` is the HTTP response. The function will return a string that will be the log line,\nor `undefined` / `null` to skip logging.\n\nNormally formats are defined using `morgan.format(name, format)`, but for certain\nadvanced uses, this compile function is directly available.\n\n## Examples\n\n### express/connect\n\nSample app that will log all request in the Apache combined format to STDOUT\n\n```js\nvar express = require('express')\nvar morgan = require('morgan')\n\nvar app = express()\n\napp.use(morgan('combined'))\n\napp.get('/', function (req, res) {\n  res.send('hello, world!')\n})\n```\n\n### vanilla http server\n\nSample app that will log all request in the Apache combined format to STDOUT\n\n```js\nvar finalhandler = require('finalhandler')\nvar http = require('http')\nvar morgan = require('morgan')\n\n// create \"middleware\"\nvar logger = morgan('combined')\n\nhttp.createServer(function (req, res) {\n  var done = finalhandler(req, res)\n  logger(req, res, function (err) {\n    if (err) return done(err)\n\n    // respond to request\n    res.setHeader('content-type', 'text/plain')\n    res.end('hello, world!')\n  })\n})\n```\n\n### write logs to a file\n\n#### single file\n\nSample app that will log all requests in the Apache combined format to the file\n`access.log`.\n\n```js\nvar express = require('express')\nvar fs = require('fs')\nvar morgan = require('morgan')\nvar path = require('path')\n\nvar app = express()\n\n// create a write stream (in append mode)\nvar accessLogStream = fs.createWriteStream(path.join(__dirname, 'access.log'), { flags: 'a' })\n\n// setup the logger\napp.use(morgan('combined', { stream: accessLogStream }))\n\napp.get('/', function (req, res) {\n  res.send('hello, world!')\n})\n```\n\n#### log file rotation\n\nSample app that will log all requests in the Apache combined format to one log\nfile per day in the `log/` directory using the\n[rotating-file-stream module](https://www.npmjs.com/package/rotating-file-stream).\n\n```js\nvar express = require('express')\nvar morgan = require('morgan')\nvar path = require('path')\nvar rfs = require('rotating-file-stream') // version 2.x\n\nvar app = express()\n\n// create a rotating write stream\nvar accessLogStream = rfs.createStream('access.log', {\n  interval: '1d', // rotate daily\n  path: path.join(__dirname, 'log')\n})\n\n// setup the logger\napp.use(morgan('combined', { stream: accessLogStream }))\n\napp.get('/', function (req, res) {\n  res.send('hello, world!')\n})\n```\n\n### split / dual logging\n\nThe `morgan` middleware can be used as many times as needed, enabling\ncombinations like:\n\n  * Log entry on request and one on response\n  * Log all requests to file, but errors to console\n  * ... and more!\n\nSample app that will log all requests to a file using Apache format, but\nerror responses are logged to the console:\n\n```js\nvar express = require('express')\nvar fs = require('fs')\nvar morgan = require('morgan')\nvar path = require('path')\n\nvar app = express()\n\n// log only 4xx and 5xx responses to console\napp.use(morgan('dev', {\n  skip: function (req, res) { return res.statusCode < 400 }\n}))\n\n// log all requests to access.log\napp.use(morgan('common', {\n  stream: fs.createWriteStream(path.join(__dirname, 'access.log'), { flags: 'a' })\n}))\n\napp.get('/', function (req, res) {\n  res.send('hello, world!')\n})\n```\n\n### use custom token formats\n\nSample app that will use custom token formats. This adds an ID to all requests and displays it using the `:id` token.\n\n```js\nvar express = require('express')\nvar morgan = require('morgan')\nvar uuid = require('node-uuid')\n\nmorgan.token('id', function getId (req) {\n  return req.id\n})\n\nvar app = express()\n\napp.use(assignId)\napp.use(morgan(':id :method :url :response-time'))\n\napp.get('/', function (req, res) {\n  res.send('hello, world!')\n})\n\nfunction assignId (req, res, next) {\n  req.id = uuid.v4()\n  next()\n}\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/expressjs/morgan/master?label=ci\n[ci-url]: https://github.com/expressjs/morgan/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/expressjs/morgan/master\n[coveralls-url]: https://coveralls.io/r/expressjs/morgan?branch=master\n[npm-downloads-image]: https://badgen.net/npm/dm/morgan\n[npm-url]: https://npmjs.org/package/morgan\n[npm-version-image]: https://badgen.net/npm/v/morgan\n", "readmeFilename": "README.md", "users": {"jk6": true, "nex": true, "nxc": true, "ymk": true, "aahz": true, "bigp": true, "d3ck": true, "ferx": true, "hinx": true, "idev": true, "isik": true, "japh": true, "jtrh": true, "leny": true, "leor": true, "nazy": true, "vwal": true, "wayn": true, "wzbg": true, "akiva": true, "bengi": true, "ddffx": true, "falka": true, "gaboo": true, "hanhq": true, "imd92": true, "jream": true, "junos": true, "loind": true, "nagra": true, "panlw": true, "pmasa": true, "r3nya": true, "renz0": true, "samar": true, "sbskl": true, "shide": true, "xyyjk": true, "yatsu": true, "456wyc": true, "adamlu": true, "afftee": true, "alek-s": true, "apopek": true, "bkarak": true, "bpatel": true, "cr8tiv": true, "crwnvr": true, "ctlnrd": true, "daizch": true, "decoda": true, "dgmike": true, "ekmpls": true, "emarcs": true, "evan2x": true, "figroc": true, "glebec": true, "godion": true, "hduhdc": true, "helcat": true, "iamwiz": true, "iliyat": true, "iotale": true, "isayme": true, "itsakt": true, "itskdk": true, "jmkim9": true, "kabomi": true, "kenkao": true, "kinday": true, "kivava": true, "leomdg": true, "m0dred": true, "mrbgit": true, "novalu": true, "nuwaio": true, "owillo": true, "pstoev": true, "quafoo": true, "ronder": true, "sermir": true, "shenyu": true, "snarky": true, "walnut": true, "wangxx": true, "windyh": true, "wisetc": true, "yb1997": true, "yeming": true, "ab.moon": true, "algonzo": true, "ansuman": true, "asm2hex": true, "astesio": true, "boyw165": true, "bracken": true, "broxmgs": true, "chinjon": true, "dariogg": true, "defking": true, "dkannan": true, "ejmason": true, "endsoul": true, "ezeikel": true, "geekish": true, "gollojs": true, "gpuente": true, "gruebes": true, "habiiev": true, "hifaraz": true, "jack546": true, "jaguarj": true, "jessnzz": true, "jmorris": true, "kaashin": true, "kingzez": true, "kjarisk": true, "kparkov": true, "kws4679": true, "laoshaw": true, "liuheng": true, "maxisam": true, "n8finch": true, "nadimix": true, "nohomey": true, "oxocode": true, "pedrotp": true, "ray0214": true, "rparris": true, "ryanlee": true, "sachacr": true, "sculove": true, "shaneli": true, "shivayl": true, "siirial": true, "silva23": true, "spanser": true, "sroveda": true, "ssh0702": true, "staydan": true, "strydom": true, "swookie": true, "tomchao": true, "ungurys": true, "vivekrp": true, "webmato": true, "xngiser": true, "yanghcc": true, "akinhwan": true, "alexkval": true, "amanvirk": true, "arifulhb": true, "asfrom30": true, "austinwo": true, "bapinney": true, "bobmhong": true, "chadyred": true, "dzhou777": true, "elrolito": true, "elussich": true, "erikvold": true, "esundahl": true, "ethancai": true, "gejiawen": true, "honzajde": true, "hughescr": true, "hugovila": true, "iamninad": true, "jaychase": true, "jazzmine": true, "jmervine": true, "jmsherry": true, "jonathas": true, "kh3phr3n": true, "kingcron": true, "kistoryg": true, "klombomb": true, "koskokos": true, "leonzhao": true, "losymear": true, "macdaddy": true, "manxisuo": true, "mauridev": true, "mayq0422": true, "mhaidarh": true, "mickaelb": true, "milan322": true, "mluberry": true, "mohokh67": true, "p4r4n0id": true, "pr-anoop": true, "robermac": true, "rstellar": true, "santihbc": true, "shajanjp": true, "shiva127": true, "sibawite": true, "skarface": true, "staraple": true, "stoneren": true, "t0ngt0n9": true, "tenpenny": true, "thor_bux": true, "vchouhan": true, "vishwasc": true, "wilbeibi": true, "wisecolt": true, "wittrura": true, "wozhizui": true, "yash3492": true, "zhyq0826": true, "abuelwafa": true, "alexcoady": true, "amenadiel": true, "asadm2706": true, "bian17888": true, "bigbird92": true, "chrisyipw": true, "chunxchun": true, "clementoh": true, "codematix": true, "cspotcode": true, "cunningdj": true, "dlpowless": true, "drdanryan": true, "dylanh724": true, "edmondnow": true, "elviopita": true, "ephigenia": true, "ethanliew": true, "flockonus": true, "gabestevy": true, "gochomugo": true, "heartnett": true, "igorissen": true, "ishitcno1": true, "jabbrwcky": true, "james3299": true, "jamiechoi": true, "jerkovicl": true, "jirqoadai": true, "lakipatel": true, "landy2014": true, "largepuma": true, "ldq-first": true, "liuningww": true, "madalozzo": true, "mikestaub": true, "mikroacse": true, "mjurincic": true, "mr-smiley": true, "nanhualyq": true, "nickeljew": true, "nikitka_m": true, "nmccready": true, "npmmurali": true, "obouchari": true, "pierrenel": true, "ramzesucr": true, "rbecheras": true, "ruyadorno": true, "samlaudev": true, "sansgumen": true, "sasquatch": true, "sayansaha": true, "shakakira": true, "slmcassio": true, "snowdream": true, "sqrtthree": true, "steel1990": true, "terrychan": true, "udaygowda": true, "webnicola": true, "xiechao06": true, "ykimnpmjs": true, "afollestad": true, "alin.alexa": true, "andreaspag": true, "ashish.npm": true, "avivharuzi": true, "bkimminich": true, "brightchen": true, "cfleschhut": true, "chiaychang": true, "chirag8642": true, "clarenceho": true, "colleowino": true, "crisperdue": true, "davidbraun": true, "dccunni171": true, "evdokimovm": true, "f124275809": true, "giussa_dan": true, "guyharwood": true, "isaacvitor": true, "jasonzhouu": true, "junjiansyu": true, "karlbright": true, "kuzmicheff": true, "langri-sha": true, "luffy84217": true, "lwgojustgo": true, "manikantag": true, "monolithed": true, "msjcaetano": true, "nate-river": true, "pengzhisun": true, "princetoad": true, "ridermansb": true, "rocket0191": true, "sanketss84": true, "saravananr": true, "shadowlong": true, "shreyawhiz": true, "shuoshubao": true, "simplyianm": true, "srksumanth": true, "stormcrows": true, "tonyljl526": true, "vicsandoli": true, "13lank.null": true, "alexey-mish": true, "amirmehmood": true, "andreipetcu": true, "archcorsair": true, "calldanfeng": true, "cbetancourt": true, "coolhanddev": true, "craigpatten": true, "danielheene": true, "dcavalcante": true, "diogocapela": true, "eserozvataf": true, "fahadjadoon": true, "garenyondem": true, "goulash1971": true, "he313572052": true, "hyungdookil": true, "icerainnuaa": true, "jamesbedont": true, "jonatasnona": true, "karlbateman": true, "karnavpargi": true, "kevinhassan": true, "kodekracker": true, "luuhoangnam": true, "magicxiao85": true, "mevlutsahin": true, "mr.raindrop": true, "mrwanashraf": true, "mseminatore": true, "nonoroazoro": true, "ouroboros99": true, "phoenix-xsy": true, "phoenixsoul": true, "rubenjose75": true, "sadmansamee": true, "thangakumar": true, "themadjoker": true, "wangnan0610": true, "zixinliango": true, "adrian110288": true, "alfredom1124": true, "anthonybruno": true, "boustanihani": true, "bro_strummer": true, "goodnighthsu": true, "jakedemonaco": true, "kwabenaberko": true, "martinspinks": true, "mrhuangyuhui": true, "mswanson1524": true, "nathanhornby": true, "natterstefan": true, "nickeltobias": true, "paulkolesnyk": true, "plashchynski": true, "prabhash1785": true, "processbrain": true, "rajivmehtajs": true, "salvatorelab": true, "silverbeetle": true, "stevepsharpe": true, "superchenney": true, "surajkarnati": true, "thomas.miele": true, "tobitobitobi": true, "usama.ashraf": true, "wallenberg12": true, "wfalkwallace": true, "windhamdavid": true, "zhangyaochun": true, "chinawolf_wyp": true, "craigdmckenna": true, "crazyjingling": true, "dandrewgarvin": true, "duskalbatross": true, "hibrahimsafak": true, "hiu_yan_chong": true, "ironheartbj18": true, "jasonwang1888": true, "markthethomas": true, "mdedirudianto": true, "program247365": true, "ral.amgstromg": true, "robinblomberg": true, "scottfreecode": true, "serge-nikitin": true, "arnold-almeida": true, "danieljameskay": true, "imaginegenesis": true, "joshuadavidson": true, "karl.alnebratt": true, "karzanosman984": true, "andrew.medvedev": true, "charlietango592": true, "icodeforcookies": true, "jeffb_incontact": true, "leandro.maioral": true, "warraichtasawar": true, "jfernandezgersol": true, "mohammad-_-ahmad": true, "ognjen.jevremovic": true, "vladyslav.tserman": true, "obsessiveprogrammer": true, "boopathisakthivel.in": true, "nguyenvanhoang26041994": true}}