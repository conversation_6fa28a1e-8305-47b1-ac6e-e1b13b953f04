{"_id": "clone", "_rev": "181-456b509ff04b2a7466bac504108c1dd9", "name": "clone", "description": "deep cloning of objects and arrays", "dist-tags": {"stable": "2.1.2", "latest": "2.1.2"}, "versions": {"0.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array"], "version": "0.0.0", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "engines": {"node": "*"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/clone/0.0.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "clone@0.0.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.26", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "ed7d92f31a09d9aab8a59941e91a2bb34d592599", "tarball": "https://registry.npmjs.org/clone/-/clone-0.0.0.tgz", "integrity": "sha512-YgzJ8LxNpPExyvKhztX4/lp7cuPkaPradpyNySri+YzC7YiePsYrERV5FoOl5LtYZVo0aAOng/e9ttD6ElxyxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+TlHPpK9nYnue/qPrLts/qXpSakS8TXIQPOMjhyjgFAIhANdsRgNExW4Nf4HlEuaGyKA8sOhz3/Ta2TZ7qRAdAjkk"}]}, "scripts": {}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array"], "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "engines": {"node": "*"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/clone/0.0.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "clone@0.0.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.26", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "a7e9554be7f06b977a0c126f9b144cd21c7338b3", "tarball": "https://registry.npmjs.org/clone/-/clone-0.0.1.tgz", "integrity": "sha512-igfCu0MOKLvXrZNUBQvRFMNaaWyjsqIuzObC5FR/fithXB4f0DsLZWshvcy9PxKQieB+NqZ3LCwkzP8vIxsawA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGeTNpUFcyVXzPRHpCarOSZpmErYdfR2us0uIhiHRFhLAiB/zKI6+LPzK8SYszq8c+XOwzdXIIgnINHDa9+pUk1+Bw=="}]}, "scripts": {}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "engines": {"node": "*"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/clone/0.0.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "clone@0.0.2", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.26", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "bd6b2dc14abe64e9b7608e6fa99a0bdaa58cce0c", "tarball": "https://registry.npmjs.org/clone/-/clone-0.0.2.tgz", "integrity": "sha512-TNXDWY0ytd6Ko9h3euCiGjdi9IACjJC+nxKrH4xglTXjIq+zj3GPCp6LRdbal2WYtDSHBntLL5ylwSvDjbjSbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGibYscNK6CFuu8qLGz0Uf9IsH5T2vWKlAygmi1Pc8H0AiB9Rqc6hTuN93E4XkI4T5Hys6JmqA43e0h3/n43RdwwRg=="}]}, "scripts": {}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "engines": {"node": "*"}, "_npmJsonOpts": {"file": "/home/<USER>/.npm/clone/0.0.3/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "clone@0.0.3", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.30", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "4cde8ef4ebafc550bbee7c38ccb815c20f1d97cd", "tarball": "https://registry.npmjs.org/clone/-/clone-0.0.3.tgz", "integrity": "sha512-sxFmL4tP+quD6VhTtiCH464y/jG+BjntNbdd1gg1F2WJJWaIeXytcttMUOG01owJKSdtsJ9FOpa9F2RnVg9MTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZmZ0GUHPHOnVJ1ULJ7OkAztR5PC8QItrbECw0tRq57AIgNZQBc6bcnY4Zb2XQ2WhzsCBFYmLL5+rO0BU+iwkTc7Y="}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.0.4", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}], "engines": {"node": "*"}, "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "_id": "clone@0.0.4", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.2", "_defaultsLoaded": true, "dist": {"shasum": "7b12de7297526de0f6a211e8a7147e97471d15a7", "tarball": "https://registry.npmjs.org/clone/-/clone-0.0.4.tgz", "integrity": "sha512-8NArYr5DwH005rM6yspCaHDUMlFd0phThmTNcDUWix9b7IbUMwXOdDAm2LKIgT082C0CwpCcp3nH1q9jzgyyFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmh6UzsJJeqdjja9Yv2lnUlOPXjr+HdvNewVdMdUWO+AIhAOQGtRHJ3PjRl0YpMAYzBcR2VXhO9QpfQRCu6GTw19aT"}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.0.5", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "_id": "clone@0.0.5", "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.10", "_defaultsLoaded": true, "dist": {"shasum": "ce2b793f40102da56199cee8b3374390d701705f", "tarball": "https://registry.npmjs.org/clone/-/clone-0.0.5.tgz", "integrity": "sha512-vFY5Oek5VDruc0Fb+sMQcviYtjjiuKFpjsDk/nNOuuY/r2yMHf/TaHLjIaWnK04J6R2Y9GIxH2s8/VB208DhNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrneqT2ztwAhJfCNxUkU2Kkse7bNYo0Xl+LZMNsx7HcQIhANTxabOTKh0KZCZ2owo83uGBthyOG160r7VvWXYkJw67"}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.0.6": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.0.6", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "_id": "clone@0.0.6", "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.10", "_defaultsLoaded": true, "dist": {"shasum": "19f8ecc2deaec17620853a3f173ec09949f7e02a", "tarball": "https://registry.npmjs.org/clone/-/clone-0.0.6.tgz", "integrity": "sha512-4aI4JKvZ1Qo/vt4EHHrNA/lRFq74K0QpkRPX/dg25XiIDXb7nq4KUm8BCCZIIPTbeFDUoT1ss8RALq7Cnri18g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDSx5kSoPe3+0Gx2v2qvKUAYvLiiilgcwenCxptFVMzAiEAvjFhRohIP5cij9Q+oaSKrsKW8OOExZdRYH7dPw6sXDk="}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.0.7": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.0.7", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "./node_modules/nodeunit/bin/nodeunit ./test.js"}, "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "_id": "clone@0.0.7", "_engineSupported": true, "_npmVersion": "1.1.0-2", "_nodeVersion": "v0.6.15", "_defaultsLoaded": true, "dist": {"shasum": "55dac1661a628d01896b4da5ddb49badecb00f11", "tarball": "https://registry.npmjs.org/clone/-/clone-0.0.7.tgz", "integrity": "sha512-q46M1UlOFOSYgMggrsP2yfEDoBs5vLF4TOWSD/8fYPd5R8DJqWuqLphgHyvv7I7gHnUE4ckf3pW/HvhARz2a6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICLAYJdhGkhSX7ZCt1PEoTQG5DXQclK468BDfmt4pFS1AiEA27UQzjaYW0bWs/oA79PvqzXYTTrUqGj1eMGdi0EYiwo="}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "./node_modules/nodeunit/bin/nodeunit ./test.js"}, "_id": "clone@0.1.0", "dist": {"shasum": "f0989c0a2e9f7870ee1b5c24f56d8d4003e2ebd9", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.0.tgz", "integrity": "sha512-X5I8UMayKSehlDXxpEY51YKbzqJQdoZkGUqM6D4+qTsu9QEHiDw044SPeiK49h9fHcV1RHN8ZKxTpe7JqmscpA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvc2PYagro6zVpPGQxbNud5hGoP0Z0m1VTz6FxYXJUBAIhAO9v8XGC3PGUFREZbwpPw2/kFNq/u2c+HVZ8Ch5D1z9N"}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.1", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.1", "dist": {"shasum": "e930eb8704fe1caa54b41d4c681db63c6f6cb034", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.1.tgz", "integrity": "sha512-LGoMGf5ojezkK8tcHXCyRFIn1Oe3NLlj1yDxI4LQBojijVM3RXb+rEx40pIdd99uf3MAzS1EHvi+MMW8o8Z3qQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFQb2E7r+e1hmUd84LV/chdG4UPqWMPnnC3uclC2U7gAiBS9oSaWN5m2oSLWQQK+PrR90dpTrrQ7wQOZ+kkaZOP/A=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.2", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.2", "dist": {"shasum": "fe54d62236ca75eb0e1fdb68e6d4826f7b02d4c4", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.2.tgz", "integrity": "sha512-GBfdW6R2YH1O5OL7mPpU+SxYNhWe5PIG6AQjpGlwImLr6jJp+A/NpaqoUCrt6LozoEIsJJ0OmQrrXhWPA4Ka1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMjM7JYROvijlQQyFuBa5yEOwzfYSxLFTRIIJYWHMemAIgL96sfGq763UPnhRbne6zq2Q3syzbzUDTP81hhof9BbA="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.3", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.3", "dist": {"shasum": "1d2c625ed8a78cba00769e8f49d9677e50617961", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.3.tgz", "integrity": "sha512-YqnopPtQYICr178Ri5q4+xsbmVlieDTz2ZHqdMRO7SM+BIVCRr5RqV0YdlowT7GQGvFp1MEFq+6goydaBe6HEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHco7yyBoAJa7k0HyNeNQ9CTR9pA6xp/RsHwhqaxuVbjAiEA2Ge8hRCx62bbe6FKuXS68BrrKXEpe5AkaX6xny9Y3dA="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.4", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.4", "dist": {"shasum": "c21c5e3831abdef52f52a607dab167fae31683d3", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.4.tgz", "integrity": "sha512-iIlD+MwUDCB5nonl0i9gdXINpXpz4PSNr7k0yPNDocMQO3Q/O1otk0J8jMIrcXAyl/4fYbjxdZshLRAnrCb5hg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCN4ZvhsLQg7PLdQLFU0kfHhlx5pwpSCIHrgRpO7YiM4AIhALI3klJ3edcpPilVhh1oCrUv7kX9KZD2ogQVCcnyxzSE"}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.5": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.5", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.5", "dist": {"shasum": "46f29143d0766d663dbd7f80b7520a15783d2042", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.5.tgz", "integrity": "sha512-icqCXhZwHg0fpiRngRxgxhehGAnrnaIM5whGwpjyajCqx5bqonZW1SsRRWutDV/LXDMqbgEx6EC07vQG24pVbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCW7dA9d7KS8OTudwSO4qm/Ikm0CePM1ZAtjpBp2LqeTAIgYdaMXI2hKH8RGkzIgT3ZPoL1Lc2CIX2Jq+k9DFB+kAY="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.6": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.6", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.6", "dist": {"shasum": "4af2296d4a23a64168c2f5fb0a2aa65e80517000", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.6.tgz", "integrity": "sha512-f1p5UdkIdJ3iXH0h8wY/DQ0ikbmMBdOI1Wo1NmWJ+FUOt1UEeFjOBHNtpRxncMTgjA+0nmjLdRrjA0ba9JkdIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHTHoWoNsvhuBHUqwqgJIMShKtBmBRRFSPpxfE8IBRSyAiAB/GaBt5kuEWx3oNOarOZ6/uoMbrYyE95wMGz+0KNv0Q=="}]}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.7": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.7", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.7", "dist": {"shasum": "a4454228f2fbffa101c0768393b4e580d3ff534b", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.7.tgz", "integrity": "sha512-fzl37mo7LYaLjg/jnD6sFmUEL9nxAEY9v9DFHsdyKCzeVrX/5EHzJQzqLx4TdXGixKZIYPEJ80oBbFPIsDey4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCHz+LjtZoMvelIcuCl67m9aDZLZCOUkGkCmtMMLlYfMICIQCKHLGRk2NEOurD+DF4h14ZOEVgXD0Pv9tNBGCCeDUbWA=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.8": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.8", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.8", "dist": {"shasum": "99c81c6950ffc0418930ae84159cda9250ea9500", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.8.tgz", "integrity": "sha512-HBjIyGuJYe0BA0arOXCK6MH9R9cbMIMPPOi/T5E52mfdN2UfqdblzFgrg873hRvX3aNQmbwyjgg1UjHDh2UU8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPT7sDHsL4Kxgh8LCFSut5XiZkxbcHL1D5tLHRorZRhAiEAg3tcQeUbWbmDCFzB8nM6QniErrncRlaIP5Ce15LKNSk="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.9": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.9", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.9", "dist": {"shasum": "d4d3f10db4338af8792509f5e600ad8a01ff08a0", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.9.tgz", "integrity": "sha512-zXk62Q7uEsrwV07TdonccH9+nQn2ZkrPNsQHzK/A0VvPAvRxRmzbQMYwFzLBHJRnNOWeMogZa8uL+lhlz6tEAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEaJqqm26YNpWME0qSRlZZsKkuY3xfRuxOww2iZHrp1wIhAOi3vZsjO7qtjAMWFVMq2VB31DiqFEK9cda3zsorVWzd"}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.10": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.10", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.10", "dist": {"shasum": "a837f193e37508b53192891d8f461c1a415b7616", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.10.tgz", "integrity": "sha512-B79kqCg/GznpBVgBsIKx0PHSBjabL++de3abPpt5gPXVjONGQo+ORkj2z8yuS4qdiLsmHRSxorjOcd5h+f34TA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCw/KNTkWBx+r8p2auIv9hVMMKJH622yagBmwlJ59m3RQIhAPNc1pwxfomdFNSQXNswu081UCq98KtiIxVMmjksSUnX"}]}, "_from": ".", "_npmVersion": "1.3.5", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.11": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.11", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorb.de"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "_id": "clone@0.1.11", "dist": {"shasum": "408b7d1773eb0dfbf2ddb156c1c47170c17e3a96", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.11.tgz", "integrity": "sha512-4SVNlQM90LqxzmjyihprBviCJkdpD10xEC9/8TDKGtmTKUx5SiuLq1Sbduw0ZoI9aCaO2ZNFGW/jMYtuXounlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+esMPnfO9iBU+gDCxL+XCTdIGjoAPbOREfTqjpt9E/QIhANIpMlgYu9Tu/I6a3nDNlaBsQ/63oB3Q8+LuVRiOjxOA"}]}, "_from": ".", "_npmVersion": "1.3.5", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.12": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.12", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.1.12", "dist": {"shasum": "ef60febfb538ada54692f35762eb5428eb0b125d", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.12.tgz", "integrity": "sha512-QHHFeAtQtagBHjU2HZLETZKPlxr+/ax02wRRw8PcO5SMUdmdx0bomQbZr6acnNFDhjh3ULpJznHCda3Wr5vz3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIArEPjGG5lGZDs1dF6aM7qk4FUkcrt7Eppf1YAE2tNQNAiEA+7ji564wC7hmhF17wIvAu54ws9HIvj/uadMazMd8b1Q="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.13": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.13", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.1.13", "dist": {"shasum": "166b167c2b2f5ea67248c5f0f18c6db11ee64458", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.13.tgz", "integrity": "sha512-EQYlgfpJUTlulOsFDgLS11W/ALupHXCmh6AXzUEX00UIYQwGLbRXjUZWvPz3o9o6NBSDbCHEi4NFs64HAUmyXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/lS+ZM6TxzEOoxm80d54YLJrT7jY0icRgJEPpSvppuAIhAMJOIoLHvMJ/K4RHAAx3MBT5EbJ23iPKjem2R6565qDa"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.14": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.14", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.1.14", "dist": {"shasum": "500f654a9d20834243cc7d47badbb00db64d1875", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.14.tgz", "integrity": "sha512-XDJIHbri0FeavonpBjL/ED+a5fp/Huo+7rElp8hS/vPR2rz/5qpVz6oV5DCDzPR5DBfhPsfSglEdGnGclLI8Qw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1cHNi9cMcjSi2+1ssB2MZUEVC7pCebyXBI44P57U02AIhANAi+EqAUvhgrlcGZ7BswOcNx5gbDfp7vWfw/j85RxMP"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.15": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.15", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.1.15", "dist": {"shasum": "98f8edd892ffb28530994bd282ea7dd954fb4fe4", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.15.tgz", "integrity": "sha512-vH++CfAGFjAkFYs6t4Bns3CusDoCx9BvvJVxBhsWNnAQjQHfYrPYBgJIGNICeGeKOidTnmnYU7hhUO9c1OkelA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDy//imMtonQ2qektTqeX/a2P4YwnyrnSQKkgmJYg5O3AiEA94Nn1eKPjdM3Fs9dI8saLxlI131LelGBg92kwhLUEI0="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.16": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.16", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.1.16", "dist": {"shasum": "4b84d8688f4d4d8f8ad1fac9505f0a99dc839160", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.16.tgz", "integrity": "sha512-AOf3T4NMXaWeOYOIi2V/SlDZwVz6bKcwXj5ZSYlNa34ZfvnSQWQvOXTLoouO6cIAco+BA49v0NkBCg1u+RQuFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPCl3fKN8NlKWXPt21uv4PVr0L1VJokuRHtoRShA2/XQIhAKgIumP9QRNNWv7bKamPcOvatY8m2yiHN1yHhfCItqJR"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.17": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.17", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}], "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.1.17", "dist": {"shasum": "14410993a603bfdbc42b465a585d3727b6536ac7", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.17.tgz", "integrity": "sha512-NMCwyVJ0i/gWdyr0/JJAfuH6+oT2WcjHx/e2/yPhfLzbIBTrHP9Vm0DCgM0asIyIxyTFZdB1u4OV4nM1j+RhRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQG9lmjixHlgUgfOgjhyA1idAVArDfaZMroPKu2Qm6AwIhAIB1XNAerPRiy/4gXwrbgIyuPIzP8fMKfTMdKwrCySDY"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}}, "0.1.18": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.18", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}], "license": "MIT", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "17eea36140d61d97a9954c53417d0e04a00525d9", "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.1.18", "_shasum": "64a0d5d57eaa85a1a8af380cd1db8c7b3a895f66", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "dist": {"shasum": "64a0d5d57eaa85a1a8af380cd1db8c7b3a895f66", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.18.tgz", "integrity": "sha512-WKwHdV6B3zNIBY1XE3JToscb7UILEmtM258U+1f9BlsjBdoJyiSmGW4P/oqBeNAoWgDHH2OlnlyHJC4eqTU2Zw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEL38hqdsSyE1Hj5FzF5sDNBNQBLLY6/3lTcdqxqYY0mAiBeoBT1WGqezQObW9tuh031SpqXcGnB+nj8GaJSMItg+Q=="}]}, "directories": {}}, "0.2.0": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.2.0", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}], "license": "MIT", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "bb11a43363a0f69e8ac014cb5376ce215ea1f8fd", "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.2.0", "_shasum": "c6126a90ad4f72dbf5acdb243cc37724fe93fc1f", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "dist": {"shasum": "c6126a90ad4f72dbf5acdb243cc37724fe93fc1f", "tarball": "https://registry.npmjs.org/clone/-/clone-0.2.0.tgz", "integrity": "sha512-g62n3Kb9cszeZvmvBUqP/dsEJD/+80pDA8u8KqHnAPrVnQ2Je9rVV6opxkhuWCd1kCn2gOibzDKxCtBvD3q5kA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPNtC1MHArPeSqSnxEh6pM+otOdPDDZPlQQk9I2AdtwwIhANMUqOiHPtOIlKJ2ZqKnnSkjq18FnB67G0FVE0pxBYLG"}]}, "directories": {}}, "0.1.19": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "0.1.19", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}], "license": "MIT", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "*"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "bb11a43363a0f69e8ac014cb5376ce215ea1f8fd", "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@0.1.19", "_shasum": "613fb68639b26a494ac53253e15b1a6bd88ada85", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "dist": {"shasum": "613fb68639b26a494ac53253e15b1a6bd88ada85", "tarball": "https://registry.npmjs.org/clone/-/clone-0.1.19.tgz", "integrity": "sha512-IO78I0y6JcSpEPHzK4obKdsL7E7oLdRVDVOLwr2Hkbjsb+Eoz0dxW6tef0WizoKu0gLC4oZSZuEF4U2K6w1WQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDd2TOZO2mlZicGJcWL7MdHxhVMUmxGoxHG8nrimN1slQIgY4W2Mb6xbEvTAf5WgevRJFX3ju1WsCuDGQEh64oL5fg="}]}, "directories": {}}, "1.0.0": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}], "license": "MIT", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "^0.9"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "abcb3403f572cd0694dae0e060bd272e265852b3", "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@1.0.0", "_shasum": "bde24f2ee7c478a409fc8a118ae2fcd44deb7f9e", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "dist": {"shasum": "bde24f2ee7c478a409fc8a118ae2fcd44deb7f9e", "tarball": "https://registry.npmjs.org/clone/-/clone-1.0.0.tgz", "integrity": "sha512-QD+5Z4GkDY/+UK0fqfrvH4CUZcgtoAZIfJsjwLZAIuuGVnu71To6kpmsEFFqQLS8S6zzAv3V5RkPVa4TybES2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZxNEv5YYxcld3YUVfGELyCctwOVWAnSKeYODalspKswIhANocDm7Q2wcpdVmSFead3VkkmjuaTWLbi53AK9anJ64N"}]}, "directories": {}, "deprecated": "XSS vulnerability fixed in v1.0.3"}, "1.0.1": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}], "license": "MIT", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"underscore": "*", "nodeunit": "~0.9.0"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "64d496dead59b95d7065e0ff490d2e5acd0685f4", "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@1.0.1", "_shasum": "0b08a631bc6a4a41b89ce2a3d43f8a84d66b4d4b", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "dist": {"shasum": "0b08a631bc6a4a41b89ce2a3d43f8a84d66b4d4b", "tarball": "https://registry.npmjs.org/clone/-/clone-1.0.1.tgz", "integrity": "sha512-6U5xXTXNOMOwEvPeQ9kXBsdJ9DL0Y5ufuFPmoqURKGldjLPdLYmuVS2W7R3UUffG+Hk33R86e/cxbsHnuSzGeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG1Q9xix/WVBjhgzBAGFt/kegNI7zXnuitj5SEH4z2iEAiBo/iSOhUEulTMkjHiCXy7HXWuevRrEmRZtjcpzBZS6oQ=="}]}, "directories": {}, "deprecated": "XSS vulnerability fixed in v1.0.3"}, "1.0.2": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "1.0.2", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}], "license": "MIT", "engines": {"node": ">=0.8"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.9.0"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "0e8216efc672496b612fd7ab62159117d16ec4a0", "homepage": "https://github.com/pvorb/node-clone", "_id": "clone@1.0.2", "_shasum": "260b7a99ebb1edfe247538175f783243cb19d149", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "dist": {"shasum": "260b7a99ebb1edfe247538175f783243cb19d149", "tarball": "https://registry.npmjs.org/clone/-/clone-1.0.2.tgz", "integrity": "sha512-b2ijK6P2aNZYyFrb1B3a4kdAtaRueI+SpAKYNhR6i+R3xcF32vN1BLq8UoLU+L0NguGAg/9UQauaVOKrEij3sQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG+R++er5RvORjM4za71AClbm1T9JeyJ7ygz2L4/fMT4AiAUk6t0qYovDIqrttzGTyM+TJ5xVTZOOCS6SV5Gm0BTNw=="}]}, "directories": {}, "deprecated": "XSS vulnerability fixed in v1.0.3"}, "2.0.0": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}, {"name": "f<PERSON><PERSON>", "url": "https://fscherwi.github.io"}, {"name": "rictic", "url": "https://github.com/rictic"}], "license": "MIT", "engines": {"node": ">=0.8"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.9.0"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "7cd7fa86ec64aa44a7cf365dd86a37e0ec7f4357", "homepage": "https://github.com/pvorb/node-clone#readme", "_id": "clone@2.0.0", "_shasum": "df65d3ca142e4a4a47db33da3468d088a16fc76e", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "4.4.5", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "dist": {"shasum": "df65d3ca142e4a4a47db33da3468d088a16fc76e", "tarball": "https://registry.npmjs.org/clone/-/clone-2.0.0.tgz", "integrity": "sha512-9pH5icdEhEHHoBwOGaqcx9DaBEXR0U/Ao4QkSyMXRnCntHn4VVS7DFpGxXPOCLMZyNhLFESlrMY9Pk/KhbXhKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS3gXsJJsNGC6YiphHjJPMAHMTUxqO8EplmmKUvdpbnAIhAIrxtWHjR0Q9Uz0XUj6gvZiDvePPILgCVDol9ivzM+Cp"}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/clone-2.0.0.tgz_1475090764656_0.33342681522481143"}, "directories": {}}, "2.1.0": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "2.1.0", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}, {"name": "f<PERSON><PERSON>", "url": "https://fscherwi.github.io"}, {"name": "rictic", "url": "https://github.com/rictic"}, {"name": "<PERSON>", "url": "https://github.com/jurca"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/miserylee"}], "license": "MIT", "engines": {"node": ">=0.8"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.9.0"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "05eba0c80a784aa02a16abe84a17a6ea4515b5fb", "homepage": "https://github.com/pvorb/node-clone#readme", "_id": "clone@2.1.0", "_shasum": "9c715bfbd39aa197c8ee0f8e65c3912ba34f8cd6", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "4.4.5", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "dist": {"shasum": "9c715bfbd39aa197c8ee0f8e65c3912ba34f8cd6", "tarball": "https://registry.npmjs.org/clone/-/clone-2.1.0.tgz", "integrity": "sha512-hrJl37Xn7vnjqdy9KgevTYK+W/dk9MIBIFexSjs/v1JHo5JywMp/sVYj8ioUVKEFxQSWpnGwdRjXQNL0orSqNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjaFpFmwW70xRWRBZLgHfxhexvR9MJ63s/S0khfY73pwIgS81Q/oPM1j6/p1WaLAIJ+7ys5nyvTXy6tfZWidPiuZs="}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/clone-2.1.0.tgz_1479854140307_0.7235472081229091"}, "directories": {}}, "2.1.1": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "2.1.1", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}, {"name": "f<PERSON><PERSON>", "url": "https://fscherwi.github.io"}, {"name": "rictic", "url": "https://github.com/rictic"}, {"name": "<PERSON>", "url": "https://github.com/jurca"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/miserylee"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/c-w"}], "license": "MIT", "engines": {"node": ">=0.8"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.9.0"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "a321fd85bb79f787fb33ba5f9a44d2ad480832ef", "homepage": "https://github.com/pvorb/node-clone#readme", "_id": "clone@2.1.1", "_shasum": "d217d1e961118e3ac9a4b8bba3285553bf647cdb", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "4.4.5", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "dist": {"shasum": "d217d1e961118e3ac9a4b8bba3285553bf647cdb", "tarball": "https://registry.npmjs.org/clone/-/clone-2.1.1.tgz", "integrity": "sha512-h5FLmEMFHeuzqmpVRcDayNlVZ+k4uK1niyKQN6oUMe7ieJihv44Vc3dY/kDnnWX4PDQSwes48s965PG/D4GntQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB8XSnBGu5IJfmcGsafi873rEwxrlTgutdqKE3nYnr3rAiAnRfLE9t4Lk62wNsCGgKUzlhP0gokjoLL49qg80LKXWg=="}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/clone-2.1.1.tgz_1489087434592_0.8986071727704257"}, "directories": {}}, "1.0.3": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "1.0.3", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}], "license": "MIT", "engines": {"node": ">=0.8"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.9.0"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "43339760fa6c242bf48874fba710a0254e571b0e", "homepage": "https://github.com/pvorb/node-clone#readme", "_id": "clone@1.0.3", "_shasum": "298d7e2231660f40c003c2ed3140decf3f53085f", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "4.4.5", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "dist": {"shasum": "298d7e2231660f40c003c2ed3140decf3f53085f", "tarball": "https://registry.npmjs.org/clone/-/clone-1.0.3.tgz", "integrity": "sha512-q59awR9d5z/jaFidcIvRUxOw4NxX8o7y6zAxqRL7Ym1Eqe4j5qBMRbGv8A1tsuRNxNrh6mxCVWkcmukICpd9XA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMcoQNZpcWqoKla+mFP2JgbJw+tRnPfs/IQmdTbGSmAwIhAKU3ugTafYZA4I1Y9VUdTiuICm2l0mhmrjXnW9goijHc"}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-1.0.3.tgz_1510175147744_0.8078057775273919"}, "directories": {}}, "2.1.2": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "2.1.2", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}, {"name": "f<PERSON><PERSON>", "url": "https://fscherwi.github.io"}, {"name": "rictic", "url": "https://github.com/rictic"}, {"name": "<PERSON>", "url": "https://github.com/jurca"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/miserylee"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/c-w"}], "license": "MIT", "engines": {"node": ">=0.8"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.9.0"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "765941803d27761a6008cb1aa32b199f0c42deb2", "homepage": "https://github.com/pvorb/node-clone#readme", "_id": "clone@2.1.2", "_shasum": "1b7f4b9f591f1e8f83670401600345a02887435f", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "4.4.5", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "dist": {"shasum": "1b7f4b9f591f1e8f83670401600345a02887435f", "tarball": "https://registry.npmjs.org/clone/-/clone-2.1.2.tgz", "fileCount": 6, "unpackedSize": 15879, "integrity": "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOlP0Piaa/xU7275u74nr7iG65RnJtA0BRqbCsVCEPaAIhAP4zYIbn38i3dP5Igf9FeAfhuoxZ8tEmDPRoeAAnszUy"}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone_2.1.2_1521666430074_0.7352781527735839"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "clone", "description": "deep cloning of objects and arrays", "tags": ["clone", "object", "array", "function", "date"], "version": "1.0.4", "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "main": "clone.js", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}], "license": "MIT", "engines": {"node": ">=0.8"}, "dependencies": {}, "devDependencies": {"nodeunit": "~0.9.0"}, "optionalDependencies": {}, "scripts": {"test": "nodeunit test.js"}, "gitHead": "3095b1b4182ea1babc453b3a46924754831b66bf", "homepage": "https://github.com/pvorb/node-clone#readme", "_id": "clone@1.0.4", "_shasum": "da309cc263df15994c688ca902179ca3c7cd7c7e", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "4.4.5", "_npmUser": {"name": "pvorb", "email": "<EMAIL>"}, "dist": {"shasum": "da309cc263df15994c688ca902179ca3c7cd7c7e", "tarball": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "fileCount": 6, "unpackedSize": 11132, "integrity": "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGEGci2gpI84d+ckHewU/na6SY42aKQOPUgVLD910smXAiARSD+Tp5UOAMnv/ATrWAHo2HFnqYeK99YnFRcWGDlajQ=="}]}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone_1.0.4_1521667285505_0.22371712372947328"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "pvorb", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:09:25.110Z", "created": "2011-09-03T01:49:48.705Z", "0.0.0": "2011-09-03T01:49:51.634Z", "0.0.1": "2011-09-03T02:19:26.641Z", "0.0.2": "2011-09-18T00:16:03.912Z", "0.0.3": "2011-10-06T18:21:26.501Z", "0.0.4": "2011-12-06T08:27:23.442Z", "0.0.5": "2012-02-24T09:28:45.516Z", "0.0.6": "2012-02-28T23:15:38.622Z", "0.0.7": "2012-04-19T19:17:02.042Z", "0.1.0": "2012-08-07T23:56:46.654Z", "0.1.1": "2012-09-04T01:13:40.762Z", "0.1.2": "2012-11-25T12:28:16.944Z", "0.1.3": "2012-11-26T19:52:26.823Z", "0.1.4": "2012-11-27T10:55:27.438Z", "0.1.5": "2013-02-06T00:07:28.283Z", "0.1.6": "2013-03-02T17:03:13.037Z", "0.1.7": "2013-05-12T18:29:09.500Z", "0.1.8": "2013-05-22T21:40:03.489Z", "0.1.9": "2013-05-23T22:41:52.114Z", "0.1.10": "2013-08-16T12:32:59.489Z", "0.1.11": "2013-10-16T15:38:22.790Z", "0.1.12": "2014-04-29T08:53:28.732Z", "0.1.13": "2014-04-29T12:00:12.081Z", "0.1.14": "2014-04-29T18:29:06.074Z", "0.1.15": "2014-04-29T23:28:14.776Z", "0.1.16": "2014-06-02T13:43:44.445Z", "0.1.17": "2014-06-29T10:50:11.641Z", "0.1.18": "2014-08-17T21:02:44.044Z", "0.2.0": "2014-11-26T22:53:39.267Z", "0.1.19": "2014-12-03T10:08:55.694Z", "1.0.0": "2015-02-10T11:39:07.509Z", "1.0.1": "2015-03-04T20:03:55.461Z", "1.0.2": "2015-03-25T23:37:30.099Z", "2.0.0": "2016-09-28T19:26:06.512Z", "2.1.0": "2016-11-22T22:35:42.023Z", "2.1.1": "2017-03-09T19:23:55.234Z", "1.0.3": "2017-11-08T21:05:47.865Z", "2.1.2": "2018-03-21T21:07:10.142Z", "1.0.4": "2018-03-21T21:21:25.570Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/"}, "repository": {"type": "git", "url": "git://github.com/pvorb/node-clone.git"}, "users": {"pid": true, "bjoerge": true, "john.pinch": true, "kubakubula": true, "jakwings": true, "mstapp": true, "nikunjchapadia": true, "korynunn": true, "andrezsanchez": true, "zeusdeux": true, "iamveen": true, "amirmehmood": true, "aurium": true, "subchen": true, "klipsil": true, "shriek": true, "mrwoo": true, "kswedberg": true, "j3kz": true, "joris-van-der-wel": true, "arkanciscan": true, "matteo.collina": true, "clholzin": true, "narven": true, "acollins-ts": true, "pandao": true, "nalindak": true, "qlqllu": true, "yuxin": true, "lestad": true, "anpdx": true, "fly19890211": true, "scull7": true, "dburdese": true, "shanebo": true, "adius": true, "morganz": true, "donvercety": true, "sachacr": true, "iuykza": true, "shuoshubao": true, "rocket0191": true, "duartemendes": true, "danielmihai": true, "kevin-foster": true, "edwardxyt": true, "shanewholloway": true, "tzq1011": true, "madalozzo": true, "kodekracker": true, "mystaticself": true, "raycharles": true, "ajimide513900383": true, "onlyrefat": true, "xfloops": true, "justjavac": true, "nbuchanan": true, "cryogena": true, "bradtaniguchi": true, "sopepos": true, "laoshaw": true, "monjer": true, "mrzmmr": true, "knksmith57": true, "miloc": true, "71emj1": true, "jream": true, "flumpus-dev": true}, "readme": "# clone\n\n[![build status](https://secure.travis-ci.org/pvorb/node-clone.png)](http://travis-ci.org/pvorb/node-clone)\n\n[![info badge](https://nodei.co/npm/clone.png?downloads=true&downloadRank=true&stars=true)](http://npm-stat.com/charts.html?package=clone)\n\noffers foolproof _deep cloning_ of objects, arrays, numbers, strings etc. in JavaScript.\n\n\n## Installation\n\n    npm install clone\n\n(It also works with browserify, ender or standalone.)\n\n\n## Example\n\n~~~ javascript\nvar clone = require('clone');\n\nvar a, b;\n\na = { foo: { bar: 'baz' } };  // initial value of a\n\nb = clone(a);                 // clone a -> b\na.foo.bar = 'foo';            // change a\n\nconsole.log(a);               // show a\nconsole.log(b);               // show b\n~~~\n\nThis will print:\n\n~~~ javascript\n{ foo: { bar: 'foo' } }\n{ foo: { bar: 'baz' } }\n~~~\n\n**clone** masters cloning simple objects (even with custom prototype), arrays,\nDate objects, and RegExp objects. Everything is cloned recursively, so that you\ncan clone dates in arrays in objects, for example.\n\n\n## API\n\n`clone(val, circular, depth)`\n\n  * `val` -- the value that you want to clone, any type allowed\n  * `circular` -- boolean\n\n    Call `clone` with `circular` set to `false` if you are certain that `obj`\n    contains no circular references. This will give better performance if needed.\n    There is no error if `undefined` or `null` is passed as `obj`.\n  * `depth` -- depth to which the object is to be cloned (optional,\n    defaults to infinity)\n\n`clone.clonePrototype(obj)`\n\n  * `obj` -- the object that you want to clone\n\nDoes a prototype clone as\n[described by Oran Looney](http://oranlooney.com/functional-javascript/).\n\n\n## Circular References\n\n~~~ javascript\nvar a, b;\n\na = { hello: 'world' };\n\na.myself = a;\nb = clone(a);\n\nconsole.log(b);\n~~~\n\nThis will print:\n\n~~~ javascript\n{ hello: \"world\", myself: [Circular] }\n~~~\n\nSo, `b.myself` points to `b`, not `a`. Neat!\n\n\n## Test\n\n    npm test\n\n\n## Caveat\n\nSome special objects like a socket or `process.stdout`/`stderr` are known to not\nbe cloneable. If you find other objects that cannot be cloned, please [open an\nissue](https://github.com/pvorb/node-clone/issues/new).\n\n\n## Bugs and Issues\n\nIf you encounter any bugs or issues, feel free to [open an issue at\ngithub](https://github.com/pvorb/node-clone/issues) or send me an email to\n<<EMAIL>>. I also always like to hear from you, if you’re using my code.\n\n## License\n\nCopyright © 2011-2015 [Paul Vorbach](http://paul.vorba.ch/) and\n[contributors](https://github.com/pvorb/node-clone/graphs/contributors).\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the “Software”), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "homepage": "https://github.com/pvorb/node-clone#readme", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.blakeminer.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://blog.axqd.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://stagas.com/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/TobiaszCudnik"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/langpavel"}, {"name": "<PERSON>", "url": "http://yabfog.com/"}, {"name": "w1nk", "url": "https://github.com/w1nk"}, {"name": "<PERSON>", "url": "http://twitter.com/hughskennedy"}, {"name": "<PERSON>", "url": "http://dustindiaz.com"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/diversario"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macinn.es/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/benjamincoe"}, {"name": "<PERSON>", "url": "https://github.com/nathan7"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/oroce"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://softwarelivre.org/aurium"}, {"name": "<PERSON>", "url": "http://www.guyellisrocks.com/"}], "bugs": {"url": "https://github.com/pvorb/node-clone/issues"}, "readmeFilename": "README.md", "license": "MIT"}