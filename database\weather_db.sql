-- 创建数据库
CREATE DATABASE weather_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE weather_db;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 城市表
CREATE TABLE cities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    city_name VARCHAR(100) NOT NULL,
    province VARCHAR(50),
    country VARCHAR(50) DEFAULT '中国',
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    is_hot BOOLEAN DEFAULT FALSE
);

-- 天气数据表
CREATE TABLE weather_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    city_id INT,
    temperature INT,
    weather_desc VARCHAR(100),
    humidity INT,
    wind_speed DECIMAL(5,2),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (city_id) REFERENCES cities(id)
);

-- 插入热门城市数据
INSERT INTO cities (city_name, province, is_hot, latitude, longitude) VALUES
('北京', '北京市', TRUE, 39.9042, 116.4074),
('上海', '上海市', TRUE, 31.2304, 121.4737),
('广州', '广东省', TRUE, 23.1291, 113.2644),
('深圳', '广东省', TRUE, 22.5431, 114.0579),
('杭州', '浙江省', FALSE, 30.2741, 120.1551),
('南京', '江苏省', FALSE, 32.0603, 118.7969);

-- 插入示例天气数据
INSERT INTO weather_data (city_id, temperature, weather_desc, humidity, wind_speed) VALUES
(1, 24, '中雨', 75, 3.2),
(2, 28, '多云', 68, 2.8),
(3, 32, '晴', 60, 1.5),
(4, 30, '阴', 72, 2.1);