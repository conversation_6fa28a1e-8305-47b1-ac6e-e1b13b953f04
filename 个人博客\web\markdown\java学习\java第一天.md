

# java第一天：

## 编写第一段代码：

psvm+回车生成public static void main

sout+回车生成System.out.println

![image-20241117085201878](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241117085201878.png)

## 注释:

![image-20241117090201892](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241117090201892.png)

## 常用快捷键：

![image-20241117090257118](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241117090257118.png)

## 变量声明，定义与c一致,变量不可重复定义，可以同时定义多个数据

## 变量的数据类型：

```java
整数类型：byte字节形，short短整型，int整形，long长整型，分别有1，2，4,8个字节大小
布尔类型：boolean定义布尔类型
    其他数据类型完全一致
    String来定义字符串
```

## 运算符:

```java
基本运算符与c++完全一致
```

## 流程控制：