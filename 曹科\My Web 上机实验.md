# My Web 上机实验

1.自制百度页面：

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MY百度
    </title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
      }
  
    .container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
      }
  
      form {
        display: flex;
      }
  
      input[type="text"] {
        padding: 10px;
        font-size: 16px;
        border: 1px solid #ccc;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        width: 400px;
      }
  
      button {
        padding: 10px 20px;
        font-size: 16px;
        background-color: #4285F4;
        color: white;
        border: none;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        cursor: pointer;
      }

      /* 新增样式，用于使图标并列显示 */
    .icon-container {
        display: flex;
        justify-content: center;
        margin-top: 10px;
      }
    </style>
  </head>
  
  <body>
    <div class="container">
      <img src="image/百度.jpeg" alt="百度图标" width="200">
      <form action="#" method="get">
        <input type="text" placeholder="输入关键词进行搜索">
        <button type="submit">搜索</button>
      </form>
      <div class="icon-container">
        <a href="zhihu.html"><img src="image/知乎.jpeg" alt="知乎图标" width="200"></a>
        <a href="WeiBo.html"><img src="image/微博.jpeg" alt="微博图标" width="200"></a>
      </div>
    </div>
  </body>
  
  </html>
```

2.自制知乎页面：

```
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MY知乎</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
    }

   .header {
      background-color: #333;
      color: white;
      padding: 15px;
    }

   .header a {
      color: white;
      text-decoration: none;
      margin-right: 20px;
    }

   .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

   .question-card {
      border: 1px solid #ddd;
      padding: 20px;
      margin-bottom: 20px;
    }

   .question-title {
      font-size: 20px;
      font-weight: bold;
    }

   .answer-section {
      margin-top: 20px;
    }

   .answer-content {
      margin-bottom: 10px;
    }
  </style>
</head>

<body>
  <div class="header">
    <a href="#">首页</a>
    <a href="#">问题</a>
    <a href="#">回答</a>
    <a href="#">我的</a>
  </div>
  <div class="container">
    <div class="question-card">
      <div class="question-title">如何提升编程能力？</div>
      <div class="answer-section">
        <div class="answer-content">多做项目，实践出真知。</div>
        <div class="answer-content">学习优秀的代码示例。</div>
      </div>
    </div>
    <div class="question-card">
      <div class="question-title">有哪些好看的电影推荐？</div>
      <div class="answer-section">
        <div class="answer-content">《肖申克的救赎》，经典之作。</div>
        <div class="answer-content">《盗梦空间》，烧脑大片。</div>
      </div>
    </div>
  </div>
</body>

</html>
```

3.自制微博页面：

```html
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>类似微博的页面</title>
  <link rel="stylesheet" href="styles.css">
</head>

<body>
  <header>
    <h1>微博类似页面</h1>
    <nav>
      <ul>
        <li><a href="#">首页</a></li>
        <li><a href="#">热门</a></li>
        <li><a href="#">关注</a></li>
        <li><input type="text" placeholder="搜索..."></li>
      </ul>
    </nav>
  </header>
  <main>
    <div class="tweet">
      <img src="user1.jpg" alt="用户头像">
      <div class="tweet-info">
        <span class="username">用户 1</span>
        <span class="timestamp">2024 年 10 月 23 日</span>
      </div>
      <p class="tweet-content">这是一条微博内容。</p>
      <div class="tweet-actions">
        <span class="likes">10 个赞</span>
        <span class="comments">5 条评论</span>
        <span class="retweets">3 次转发</span>
      </div>
    </div>
    <!-- 更多微博内容 -->
  </main>
  <aside>
    <h2>热门话题</h2>
    <ul>
      <li>#话题 1</li>
      <li>#话题 2</li>
      <li>#话题 3</li>
    </ul>
    <h2>推荐用户</h2>
    <ul>
      <li><img src="user2.jpg" alt="用户头像"><span>用户 2</span></li>
      <li><img src="user3.jpg" alt="用户头像"><span>用户 3</span></li>
      <li><img src="user4.jpg" alt="用户头像"><span>用户 4</span></li>
    </ul>
  </aside>
  <footer>
    <p>版权所有 &copy; 2024</p>
  </footer>
</body>

</html>

```

```css
#文件styles.css
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
  }
  
  header {
    background-color: #333;
    color: #fff;
    padding: 10px;
  }
  
  nav ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
  }
  
  nav ul li {
    display: inline-block;
    margin-right: 10px;
  }
  
  nav ul li a {
    color: #fff;
    text-decoration: none;
  }
  
  main {
    padding: 20px;
  }
  
  .tweet {
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 20px;
  }
  
  .tweet img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 10px;
  }
  
  .tweet-info {
    font-size: 14px;
    color: #666;
  }
  
  .tweet-content {
    font-size: 16px;
  }
  
  .tweet-actions {
    font-size: 12px;
    color: #999;
  }
  
  aside {
    width: 300px;
    float: right;
    padding: 20px;
  }
  
  aside h2 {
    font-size: 18px;
    margin-bottom: 10px;
  }
  
  aside ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
  }
  
  aside ul li img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
  }
  
  footer {
    background-color: #333;
    color: #fff;
    padding: 10px;
    text-align: center;
  }
```





我想实现的效果图：

![image-20241023193357448](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241023193357448.png)

现在是百度页面，然后搜索下面有知乎、微博的图标，点击就跳转到我自己的自制网站上去