* {
    margin: 0;
    padding: 0;
    font-family: 'Poppings', sans-serif;
}

body {
    background: #0a2a43;
    /*让子元素全部达到1500px
    */
    min-height: 1200px;
}

section {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    /*
   让Moon LIGHT居中
   */
}

/*
插入伪元素在前面
*/
section::before {
    content: '';
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(to top, #0a2a43, transparent);
    z-index: 10000;
}

/*
插入伪元素在后面
*/
section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #0a2a43;
    mix-blend-mode: color;
    z-index: 10000;
}

section img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    pointer-events: none;
}

#text {
    position: relative;
    z-index: 1;
    color: #fff;
    font-size: 6vw;
}


#road {
    z-index: 2;
}