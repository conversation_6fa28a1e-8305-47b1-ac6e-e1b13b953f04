{"_id": "cookie-signature", "_rev": "44-6c83c9efe2f0fc5832413279fd9c3204", "name": "cookie-signature", "dist-tags": {"latest": "1.2.2"}, "versions": {"0.0.1": {"name": "cookie-signature", "version": "0.0.1", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "cookie-signature@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "13d3603b5cf63befbf85a8801e37aa900db46985", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-0.0.1.tgz", "integrity": "sha512-cF3ACWE4MpShxO2sKb6TtB4wHIn2DsO83K1DNhukqVd5PhYwxLLqHvkrcvHpIpPJ0ow8pqSO36gwL4viDq9edA==", "signatures": [{"sig": "MEUCICxvn0hotxn1mQlyJ5hRSSmIQbTHy3onGyuamhsqTBT5AiEA8ogR18H5DrP6ui40oE8H4Mj1GNSzpLIlu2Tek94141U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "description": "Sign and unsign cookies", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.0": {"name": "cookie-signature", "version": "1.0.0", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "cookie-signature@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0044f332ac623df851c914e88eacc57f0c9704fe", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.0.tgz", "integrity": "sha512-kFuvQ0eYX+1IXU6Dvu3gNAQuqVnE6ApsgUUrXp9iSy7nQjLUFF1vJmg97ZamM1t6tLTLK48BUHVsBNyfSPtEWg==", "signatures": [{"sig": "MEUCICCGk8KlHrTTtiunufRG+UFXWe7Y00OCDYokR9Ot8KaRAiEAm+uoYCaPLOBD+WXs+0R9dvvMSTUWcMs9FPhco6nnoXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.2.14", "description": "Sign and unsign cookies", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.1": {"name": "cookie-signature", "version": "1.0.1", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "cookie-signature@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "44e072148af01e6e8e24afbf12690d68ae698ecb", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.1.tgz", "integrity": "sha512-FMG5ziBzXZ5d4j5obbWOH1X7AtIpsU9ce9mQ+lHo/I1++kzz/isNarOj6T1lBPRspP3mZpuIutc7OVDVcaN1Kg==", "signatures": [{"sig": "MEQCIE4syMH4sHnGRhNtmkVNyYpGh+pVW3WebJyOUyxHl+I3AiAcIrkPhX4zZyaOiWgf70WMeCxlQoZZL7r3mkmKE9Z1eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.2.14", "description": "Sign and unsign cookies", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.2": {"name": "cookie-signature", "version": "1.0.2", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "cookie-signature@1.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "3d8ed55a70e4bcd474f699af0d03b5b652fe85ba", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.2.tgz", "integrity": "sha512-rEsE316J5QN74hzuWtHCFU/rW6glKGWRYSLV84QfziWLLe3xITIN+g83cQhmQsF6eXccj9FX03h6rS1FyS+NiA==", "signatures": [{"sig": "MEUCIFF/WCKetqmEVUD8vUrD9eHG7hU9AEapR36RIq8eibBsAiEA0GaPMaCiQP7d46VUVKvbV1wq/b2gB7PLgh0mLqE08Bo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "Sign and unsign cookies", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.3": {"name": "cookie-signature", "version": "1.0.3", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "cookie-signature@1.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "91cd997cc51fb641595738c69cda020328f50ff9", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.3.tgz", "integrity": "sha512-/KzKzsm0OlguYov01OlOpTkX5MhBKUmfL/KMum7R80rPKheb9AwUzr78TwtBt1OdbnWrt4X+wxbTfcQ3noZqHw==", "signatures": [{"sig": "MEYCIQCUjPZYRrPGXRxYk3rLkRNoCfjKTV+9W/dBMKWPlSVv1gIhALAdPtH5qzTexc6IDHJjZUJNtmaa9iHoliW8n5A/aNK9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "Sign and unsign cookies", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.4": {"name": "cookie-signature", "version": "1.0.4", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "cookie-signature@1.0.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "natevw", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "0edd22286e3a111b9a2a70db363e925e867f6aca", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.4.tgz", "integrity": "sha512-k+lrG38ZC/S7zN6l1/HcF6xF4jMwkIUjnr5afDU7tzFxIfDmKzdqJdXo8HNYaXOuBJ3tPKxSiwCOTA0b3qQfaA==", "signatures": [{"sig": "MEUCIQC6/HhCiWY9QwkruByzTkJqvvw6ONlVVlEj4b0pciNv8gIgSm/w4nXfSUoaWjVqP6lO0VsEsUorKD0h90vQrMIcn40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_npmUser": {"name": "natevw", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Sign and unsign cookies", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.5": {"name": "cookie-signature", "version": "1.0.5", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie-signature@1.0.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "natevw", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "a122e3f1503eca0f5355795b0711bb2368d450f9", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.5.tgz", "integrity": "sha512-Ym05XFKVD+EOB43QU3ovI/KvqFo5MP4BGsH+SkAMn2mdjLj2W4bOSyNsw1Ik1gI7CyDtR9Ae2TUFHexgaiEuZg==", "signatures": [{"sig": "MEUCIDZiB0YPvjIcH5f88gle1hFA9yN8D1kfgwrXQeVBWVoEAiEA/nRIiYa2pDJFen6kVfzai9UIhXnllLlQZ14tOMXx3qQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "a122e3f1503eca0f5355795b0711bb2368d450f9", "gitHead": "73ed69b511b3ef47555d71b4ed1deea9e5ed6e1f", "scripts": {}, "_npmUser": {"name": "natevw", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "1.4.20", "description": "Sign and unsign cookies", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.6": {"name": "cookie-signature", "version": "1.0.6", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie-signature@1.0.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "natevw", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "e303a882b342cc3ee8ca513a79999734dab3ae2c", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==", "signatures": [{"sig": "MEQCIDkSN7m4/Rmq8gk6G538ntRLEYiLrzx6rwtsz8LbD06hAiBEB5DxN23Ed4yq6pUn3QWx+bPtwtZVL0+lC9fgXRZHyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "e303a882b342cc3ee8ca513a79999734dab3ae2c", "gitHead": "391b56cf44d88c493491b7e3fc53208cfb976d2a", "scripts": {"test": "mocha --require should --reporter spec"}, "_npmUser": {"name": "natevw", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "Sign and unsign cookies", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.1.0": {"name": "cookie-signature", "version": "1.1.0", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie-signature@1.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "natevw", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "cc94974f91fb9a9c1bb485e95fc2b7f4b120aff2", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.1.0.tgz", "integrity": "sha512-Alvs19Vgq07eunykd3Xy2jF0/qSNv2u7KDbAek9H5liV1UMijbqFs5cycZvv5dVsvseT/U4H8/7/w8Koh35C4A==", "signatures": [{"sig": "MEQCIAVIu2sbalihi6W2vCZkH5dcjdAePz0U15la6w1g2bDmAiBz3u3XwWS1aBIxERM3flOGZAWfBAYzNzxFJOL/wwOiLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">=6.6.0"}, "gitHead": "1e5f40d6c1f631a7fa43992e82918c1d78dbdb89", "scripts": {"test": "mocha --require should --reporter spec"}, "_npmUser": {"name": "natevw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Sign and unsign cookies", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cookie-signature-1.1.0.tgz_1516336355373_0.5579380588606", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "cookie-signature", "version": "1.2.0", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie-signature@1.2.0", "maintainers": [{"name": "natevw", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "4deed303f5f095e7a02c979e3fcb19157f5eaeea", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-R0BOPfLGTitaKhgKROKZQN6iyq2iDQcH1DOF8nJoaWapguX5bC2w+Q/I9NmmM5lfcvEarnLZr+cCvmEYYSXvYA==", "signatures": [{"sig": "MEQCIGVY6HJeoriPdIQoE4VRIdmTckEWhQMXuiYXAZEL0rV3AiA5J4HfVuv4IcxoxgHcgO0Mv9/smwogqAxP/gT20ST4Pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDq7NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmov5w//diHxo1X68NOGqOdNFVzuCz3JG5WHTDKPgh3DhWqpa2iKWo6U\r\naCDCi1MMruPBaw75OdZBajsrHVDswzNXSjWAhea3FwQ+sf0VWknq4PyEgT8j\r\nZsvrPrmoW+7eIsabwobGqBmllAxENLaaqdb3VsqmkYGOCB/uFmFN39KCskE9\r\nlDh8WLi5s23Js5r3EbwHjoEDG8cfYTCncFjw1X6kDXfEqUCn6825jLrce18S\r\nhDt2X+HRwT2rOT5d8HuYNEI/NqDdD0vF+Y8LumLYTiI0s8SLSWR+M0OM/Ipu\r\nb1Mp8mV0qV2hNlzw/lW0XT82W7w3SOSYjHOEyEVIcuPfJTE9QdfT9+6H+shZ\r\n3jmbgmLcXInLjbyFjCgSZfbAztDs32p4Gr6PWSJ9L1UfE9dusxkN+o8DFaRB\r\nEBmiBQjZK8rE3NpoUPv9sh9efTg3R+xkBao5fEOQhw68r4/ICLDs8V2vYijY\r\nx8bIF+PNfdO4/0/aGai3SUhLWfYjNc8HAgrRH3zV24O1wJy0Wz1yXurjT7WR\r\nmWmcjTliSdBDhYm9DLU4DTKLUEpCWIVs5BYqy0MBDUMlXmM7iEN+9CORh5CV\r\nz+YzAuI9sD/96ZqBLxIFmh5knJUbFyGvydtpTJm/YEh9/lV7tVTmIBgZ511I\r\nCm0KAhWCbPDjLzXEO3MLiMl0n1ai+EJsYso=\r\n=WADt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=6.6.0"}, "gitHead": "7deca8b38110a3bd65841c34359794706cc7c60f", "scripts": {"test": "mocha --require should --reporter spec"}, "_npmUser": {"name": "natevw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Sign and unsign cookies", "directories": {}, "_nodeVersion": "12.22.5", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "should": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cookie-signature_1.2.0_1645129421783_0.17357078420470673", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "cookie-signature", "version": "1.2.1", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie-signature@1.2.1", "maintainers": [{"name": "natevw", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "790dea2cce64638c7ae04d9fabed193bd7ccf3b4", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.1.tgz", "fileCount": 5, "integrity": "sha512-78KWk9T26NhzXtuL26cIJ8/qNHANyJ/ZYrmEXFzUmhZdjpBv+DlWlOANRTGBt48YcyslsLrj0bMLFTmXvLRCOw==", "signatures": [{"sig": "MEYCIQCbbPEOCigL1our0AkPLDr6BGTgCjZt8oaEh8huPdHtvAIhAMewT7wEtPrakH1tDcsXJFH2PKto2v+CUM8+gf1nlLeA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/O6hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvURAAjhQnN6TztlITTJPgdM5QYeUCObDjb9QJszWdaUsqIeg9v9dY\r\n4/p41lGwIxSCbLAf0Q+qIG7SMzY4oFtl0dTpoxrUdj7nZokRgdsgwX7GtUEU\r\ne6JQYdtKP2WLPW3zdNE/Ack7gZuEBQeIi1A40qHXe9ra4OrFmJcsWcPCieeX\r\nXzglNawqXyKmoE1jGg8kDeg6Wcd43yAb8hbrFhaONW6nX5lMyp00+fdbGTcQ\r\ncEKRv0HPobrieXcwiuxOn64kDj2FQ4aX7oHvyXADAJgMY+7tOld1dkyJdkOK\r\nPx9ancQ7J+cXBkKii7KMRi1IrPalv+jfpAwkZ/cGcH7pz9W34YlQFqvWbInC\r\nzaEkihIAxsJaVG70FuOt8wD8y+FqQProdDDlC4e6TyDIQrcY1X97QrjG/2Kb\r\n3mL+xgao5bw2LRpdIeOYhgbrq/Z2RJIQH+OOenDV6+KkseTJ7+IsrzdecNs3\r\ncUpXsc+3AftVwSK3lL8ZisvyfbV7lDG3i7if+IbNWNk+NA5mT7XlyRL/H5OM\r\n6vO1gwryzJbeoDo/Cpb6ehEBjY2v5kN9MyzPieeMVTzcLPLQbmQyR73ZaCtg\r\nXZe9CED0Js2OAUsMfHUup4135tPRPEI6eElTe5GaR4iSqAiWHBSUwhIKTwIA\r\n1641x0ZU2b4oC8doQ/9yV1wEgBb7mDeDbv4=\r\n=uxq+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=6.6.0"}, "gitHead": "0167b2d857780f6b3d8919bb7702ab1a1af05ae7", "scripts": {"test": "mocha --require should --reporter spec"}, "_npmUser": {"name": "natevw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Sign and unsign cookies", "directories": {}, "_nodeVersion": "12.22.5", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "should": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cookie-signature_1.2.1_1677520545591_0.207436219370712", "host": "s3://npm-registry-packages"}}, "1.0.7": {"name": "cookie-signature", "version": "1.0.7", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cookie-signature@1.0.7", "maintainers": [{"name": "natevw", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "dist": {"shasum": "ab5dd7ab757c54e60f37ef6550f481c426d10454", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.7.tgz", "fileCount": 4, "integrity": "sha512-NXdYc3dLr47pBkpUCHtKSwIOQXLVn8dZEuywboCOJY/osA0wFSLlSawr3KN8qXJEyX66FcONTH8EIlVuK0yyFA==", "signatures": [{"sig": "MEUCIQCxLZePxkXoMKc+/foFOmv3paquhOl1C/U4GkKwzt+uZAIgJSkkULPiL/deBWsg1DDNxkHWmFCa5u3+FEhrzWBnFhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkN0XOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKMg//YMLq2hdN6psLKljqEJTGPhGM9B8Sws/eZ3ztWEaiMotjI8eO\r\n+pZ+EJlzeN+sCHB0+IgftIesX2gU2ly7bLTvts9Qt580YK12JwNGZrhf+Isz\r\nmUAC1Kspja5v6Z20qQYoHQ6cgPzlY5GHjoNTJWd9z76Ne5VwXx5JoouWfmeJ\r\nSKaU5g89hnEzF081DaRstpYZ32AUHvqkGnFuARkXl8BbLcpCXoAimQpU4eP6\r\nc7ngeOIDy3qyC0QLEt+DettiZbMUw+aOGi3oaMUb5t8rDZkfZ9F8nYi71qQ/\r\nVgCaXzBVxolXJDTUzUWjV2TmzGDpc/UOnL2xgeUWrRDLaVZyHY7O7nAAm2B4\r\nYIMzQYpr7pr4we2ysNyIM9SVDLOmTRBsumv/Gqbo7lh16fmAO4tGmL32n22Q\r\n2HG2G55xcbl0dAqC+AdBqkzV5CaBUtZ6oFIKzXXCterR1SCPdyTGhd8DgM/3\r\n424QO0PDQIXlHorZ3t/lkn4TwmJbjFt6F3Jf1HYcYiPb91Yz6qyM+j/cDfZX\r\nEU3Pk6xY00dToQzvejEhuxGwYerR0U6rgl9zonVuJH1G0gJTjLVJcvIyIzKE\r\nk4jo9xpleyw4CSvCROtB60s+HTg/f+rhq31sNkMIrCSBckjfFPOVrKdAJNbF\r\nGQ/GyXmWajDD2fx34ar6NjvA/b4j+fbXRGY=\r\n=MXg6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "gitHead": "432ea0c14fbdd8d24354820faff2c6f6d2426757", "scripts": {"test": "mocha --require should --reporter spec"}, "_npmUser": {"name": "natevw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/visionmedia/node-cookie-signature.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Sign and unsign cookies", "directories": {}, "_nodeVersion": "12.22.12", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "*", "should": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cookie-signature_1.0.7_1681343950767_0.4892419001328756", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "cookie-signature", "version": "1.2.2", "main": "index.js", "description": "Sign and unsign cookies", "keywords": ["cookie", "sign", "unsign"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-cookie-signature.git"}, "dependencies": {}, "engines": {"node": ">=6.6.0"}, "devDependencies": {"mocha": "*", "should": "*"}, "scripts": {"test": "mocha --require should --reporter spec"}, "gitHead": "b7bd4cb9500bfa5e696143f51d61e5f24f7a625d", "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "_id": "cookie-signature@1.2.2", "_nodeVersion": "12.22.12", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==", "shasum": "57c7fc3cc293acab9fec54d73e15690ebe4a1793", "tarball": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz", "fileCount": 5, "unpackedSize": 4993, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEV0n21AvW1d1ud6J1a6OOPUxJBTsbHTQI+mDOuAk964AiEAsJUQ16IhyVgMzmXM0KFLSg9uWjpJd/jdkrdL/zJg4KI="}]}, "_npmUser": {"name": "natevw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "natevw", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cookie-signature_1.2.2_1730230787351_0.6616803087142129"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-10-15T15:53:32.007Z", "modified": "2024-10-29T19:39:47.840Z", "0.0.1": "2012-10-15T15:53:33.933Z", "1.0.0": "2013-04-12T19:07:28.737Z", "1.0.1": "2013-04-15T19:29:17.362Z", "1.0.2": "2014-01-29T00:00:59.981Z", "1.0.3": "2014-01-29T01:15:41.790Z", "1.0.4": "2014-06-25T22:14:18.119Z", "1.0.5": "2014-09-05T23:22:06.935Z", "1.0.6": "2015-02-03T22:23:15.095Z", "1.1.0": "2018-01-19T04:32:35.808Z", "1.2.0": "2022-02-17T20:23:41.926Z", "1.2.1": "2023-02-27T17:55:45.765Z", "1.0.7": "2023-04-12T23:59:10.921Z", "1.2.2": "2024-10-29T19:39:47.642Z"}, "bugs": {"url": "https://github.com/visionmedia/node-cookie-signature/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/visionmedia/node-cookie-signature#readme", "keywords": ["cookie", "sign", "unsign"], "repository": {"type": "git", "url": "git+https://github.com/visionmedia/node-cookie-signature.git"}, "description": "Sign and unsign cookies", "maintainers": [{"name": "natevw", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "\n# cookie-signature\n\n  Sign and unsign cookies.\n\n## Example\n\n```js\nvar cookie = require('cookie-signature');\n\nvar val = cookie.sign('hello', 'tobiiscool');\nval.should.equal('hello.DGDUkGlIkCzPz+C0B064FNgHdEjox7ch8tOBGslZ5QI');\n\nvar val = cookie.sign('hello', 'tobiiscool');\ncookie.unsign(val, 'tobiiscool').should.equal('hello');\ncookie.unsign(val, 'luna').should.be.false;\n```\n\n## License\n\nMIT.\n\nSee LICENSE file for details.\n", "readmeFilename": "Readme.md", "users": {"285858315": true, "panlw": true, "yong_a": true, "awangxh": true, "ivansky": true, "dgarlitt": true, "qbylucky": true, "milfromoz": true, "giussa_dan": true, "simplyianm": true, "haiyang5210": true, "nickeltobias": true}}