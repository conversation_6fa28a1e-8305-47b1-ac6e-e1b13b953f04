Running process: P1, Instruction: CALC
Running process: P2, Instruction: INPUT
Running process: P3, Instruction: CALC
Running process: P1, Instruction: INPUT
Running process: P2, Instruction: CALC
Running process: P3, Instruction: INPUT
Running process: P1, Instruction: CALC
Running process: P2, Instruction: CALC
Running process: P3, Instruction: WAIT
Running process: P1, Instruction: CALC
Running process: P2, Instruction: CALC
Running process: P3, Instruction: CALC
Running process: P1, Instruction: CALC
Running process: P2, Instruction: CALC
Running process: P3, Instruction: CALC
Running process: P1, Instruction: CALC
Running process: P2, Instruction: CALC
Running process: P3, Instruction: CALC
Running process: P1, Instruction: INPUT
Running process: P2, Instruction: OUTPUT
Running process: P3, Instruction: CALC
Running process: P1, Instruction: CALC
Running process: P2, Instruction: HALT
Process P2 completed.
Running process: P3, Instruction: OUTPUT
Running process: P1, Instruction: CALC
Running process: P3, Instruction: HALT
Process P3 completed.
Running process: P1, Instruction: OUTPUT
Running process: P1, Instruction: HALT
Process P1 completed.
