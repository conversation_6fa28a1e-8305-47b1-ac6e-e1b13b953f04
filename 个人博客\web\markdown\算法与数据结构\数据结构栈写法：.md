数据结构栈写法：

1.stl

![Snipaste_2024-10-22_03-20-10](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-22_03-20-10.png)



```
#include <bits/stdc++.h>
using namespace std;
typedef unsigned long long ll;
int main() {
	ll n;
	cin >> n;
	for (ll i = 1; i <= n; i++) {
		stack<ll>s;
		ll a;
		cin >> a;
		string p;
		for (ll j = 1; j <= a; j++) {
			cin >> p;
			ll k;
			if (p == "push") {
				cin >> k;
				s.push(k);
			}
			if (p == "pop") {
				if (s.empty()) {
					cout << "Empty" << endl;
				}
				else {
					s.pop();
				}
			}
			if (p == "query") {
				if (s.empty()) {
					cout << "Anguei!" << endl;
				}
				else {
					cout << s.top() << endl;
				}
			}
			if (p == "size") {
				cout << s.size() << endl;
			}
		}
	}
}
```

2.手写

```
1.模拟 int st[N],t=0
2.t=0表示栈空,正常从t=1开始存
3.入栈st[++t]=x;
4.取栈顶int u=st[t];
5.弹栈t--;
6.清空栈t=0;
```

