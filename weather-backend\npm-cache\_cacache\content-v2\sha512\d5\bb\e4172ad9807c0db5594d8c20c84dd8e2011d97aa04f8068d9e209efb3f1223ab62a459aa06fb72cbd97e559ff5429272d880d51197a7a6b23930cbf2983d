{"_id": "merge-descriptors", "_rev": "83-33f50ee9ae653b40d7b7bffd382efaca", "name": "merge-descriptors", "description": "Merge objects using their property descriptors", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.1": {"name": "merge-descriptors", "description": "Merge objects using descriptors", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jonathanong/merge-descriptors.git"}, "bugs": {"url": "https://github.com/jonathanong/merge-descriptors/issues"}, "scripts": {"test": "make test;"}, "homepage": "https://github.com/jonathanong/merge-descriptors", "_id": "merge-descriptors@0.0.1", "dist": {"shasum": "2ff0980c924cf81d0b5d1fb601177cb8bb56c0d0", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-0.0.1.tgz", "integrity": "sha512-1VjrOxz6kouIMS/jZ+oQTAUsXufrF8hVzkfzSxqBh0Wy/KzEqZSvy3OZe/Ntrd5QeHtNCUF1bE0bIRLslzHCcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvuTLQ12a3MztrlD5cx+AMXViVdWfOievYFQzlMImtMAiBAHIzI5ETnYJxQDTxkRVWHOy/+pWlDDJ04pP7eZ7XeYg=="}]}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}}, "0.0.2": {"name": "merge-descriptors", "description": "Merge objects using descriptors", "version": "0.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/component/merge-descriptors.git"}, "bugs": {"url": "https://github.com/component/merge-descriptors/issues"}, "scripts": {"test": "make test;"}, "homepage": "https://github.com/component/merge-descriptors", "_id": "merge-descriptors@0.0.2", "dist": {"shasum": "c36a52a781437513c57275f39dd9d317514ac8c7", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-0.0.2.tgz", "integrity": "sha512-dYBT4Ep+t/qnPeJcnMymmhTdd4g8/hn48ciaDqLAkfRf8abzLPS6Rb6EBdz5CZCL8tzZuI5ps9MhGQGxk+EuKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBelv09AJeKf+RJ2eyYVU9pu/Lnmnca0/kxVNgFx75k2AiATIfP1yCbe8P/mpaWrheBoMtnXmEJxnJQTx00D8XoZHA=="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}}, "1.0.0": {"name": "merge-descriptors", "description": "Merge objects using descriptors", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/component/merge-descriptors.git"}, "bugs": {"url": "https://github.com/component/merge-descriptors/issues"}, "files": ["LICENSE", "README.md", "index.js"], "gitHead": "81d7a3c14099884c391bd237d7d8edf23c6d6f18", "homepage": "https://github.com/component/merge-descriptors", "_id": "merge-descriptors@1.0.0", "scripts": {}, "_shasum": "2169cf7538e1b0cc87fb88e1502d8474bbf79864", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yields", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2169cf7538e1b0cc87fb88e1502d8474bbf79864", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.0.tgz", "integrity": "sha512-YJiZmTZTkrqvgefMsWdioTKsZdHnfAhHHkEdPg+4PCqMJEGHQo5iJQjEbMv3XyBZ6y3Z2Rj1mqq1WNKq9e0yNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDdR1eP68e5IHritGhbE9bu1ga8dBTkWhmxYm93TfqBEAiAoDZWgjmWxDi2AQJab1ZyS1npJXBA+bED4x0ra2mnlVw=="}]}, "directories": {}}, "1.0.1": {"name": "merge-descriptors", "description": "Merge objects using descriptors", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/component/merge-descriptors"}, "devDependencies": {"istanbul": "0.4.1", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "gitHead": "f26c49c3b423b0b2ac31f6e32a84e1632f2d7ac2", "bugs": {"url": "https://github.com/component/merge-descriptors/issues"}, "homepage": "https://github.com/component/merge-descriptors", "_id": "merge-descriptors@1.0.1", "_shasum": "b00aaa556dd8b44568150ec9d1b953f3f90cbb61", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "anthonyshort", "email": "<EMAIL>"}, {"name": "clintwood", "email": "<EMAIL>"}, {"name": "dfcreative", "email": "<EMAIL>"}, {"name": "domini<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "queckezz", "email": "fabian.eiche<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thehydroimpulse", "email": "<EMAIL>"}, {"name": "timaschew", "email": "<EMAIL>"}, {"name": "timo<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "trevor<PERSON><PERSON>@gmail.com"}, {"name": "yields", "email": "<EMAIL>"}], "dist": {"shasum": "b00aaa556dd8b44568150ec9d1b953f3f90cbb61", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFNrN6gt6C+tUkjr9vSLHPycmhxt1e61SpK4k1v8NVKwAiEAhj5MrJHHspYdf093w3Pvji78/dtcNfokwqR0Z4sArjI="}]}, "directories": {}}, "1.0.2": {"name": "merge-descriptors", "description": "Merge objects using descriptors", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/merge-descriptors.git"}, "devDependencies": {"eslint": "5.9.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "scripts": {"lint": "eslint .", "test": "mocha test/", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "25683826bb62861df4bd3926830a7e9ef1593240", "bugs": {"url": "https://github.com/sindresorhus/merge-descriptors/issues"}, "homepage": "https://github.com/sindresorhus/merge-descriptors#readme", "_id": "merge-descriptors@1.0.2", "_nodeVersion": "20.9.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-mQy+fGU2VdxDEo3m+3+BqKyiDBgwQrmvdzaF6n1iDKp5Pk/VsaCprU6nmN6AKSe6LjuqYqF59xEnooYZVVCsXA==", "shasum": "21c9c09fc9a12211d7bc66955c5b3fc1bc00963e", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.2.tgz", "fileCount": 5, "unpackedSize": 5024, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8HNRP/zH6vizzqiVIP53bRFW0sMZGaJKKjguc26+bBwIhAMaBK7wmKxjZmk/RMxArv7s27z+ouT1cPoT144qaWtyQ"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge-descriptors_1.0.2_1700155675592_0.7045021476391857"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "merge-descriptors", "description": "Merge objects using descriptors", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/merge-descriptors.git"}, "funding": "https://github.com/sponsors/sindresorhus", "devDependencies": {"eslint": "5.9.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "scripts": {"lint": "eslint .", "test": "mocha test/", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "25683826bb62861df4bd3926830a7e9ef1593240", "bugs": {"url": "https://github.com/sindresorhus/merge-descriptors/issues"}, "homepage": "https://github.com/sindresorhus/merge-descriptors#readme", "_id": "merge-descriptors@1.0.3", "_nodeVersion": "20.9.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==", "shasum": "d80319a65f3c7935351e5cfdac8f9318504dbed5", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "fileCount": 5, "unpackedSize": 5081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC40Qs4u5va0yM+JhUuQIxLvoKZK5p4CHXJvFaMxLi/NAiEA4BHCvcU8buakxzZIKTnstviZOODgUBz++F2n1aCI/Bs="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge-descriptors_1.0.3_1700155732399_0.12401600657988032"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "merge-descriptors", "version": "2.0.0", "description": "Merge objects using their property descriptors", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/merge-descriptors.git"}, "funding": "https://github.com/sponsors/sindresorhus", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "exports": {"types": "./index.d.ts", "default": "./index.js"}, "main": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava"}, "keywords": ["merge", "descriptors", "object", "property", "properties", "merging", "getter", "setter"], "devDependencies": {"ava": "^5.3.1", "xo": "^0.56.0"}, "xo": {"rules": {"unicorn/prefer-module": "off"}}, "gitHead": "b497f611ec85031e00c2f3cdbb327a550028a7bf", "bugs": {"url": "https://github.com/sindresorhus/merge-descriptors/issues"}, "homepage": "https://github.com/sindresorhus/merge-descriptors#readme", "_id": "merge-descriptors@2.0.0", "_nodeVersion": "20.9.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==", "shasum": "ea922f660635a2249ee565e0449f951e6b603808", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "fileCount": 5, "unpackedSize": 4373, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpTo0yi5Dj8yVNfLLVkLNKUlpcV4bSeYjZ2OyCRfu/7wIgSJJYX1nK6+pNepu5hwGH4BDTZvMbQkOLP4npxRA9CTo="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/merge-descriptors_2.0.0_1700160560530_0.0056050974304060475"}, "_hasShrinkwrap": false}}, "readme": "# merge-descriptors\n\n> Merge objects using their property descriptors\n\n## Install\n\n```sh\nnpm install merge-descriptors\n```\n\n## Usage\n\n```js\nimport mergeDescriptors from 'merge-descriptors';\n\nconst thing = {\n\tget name() {\n\t\treturn 'John'\n\t}\n}\n\nconst animal = {};\n\nmergeDescriptors(animal, thing);\n\nconsole.log(animal.name);\n//=> 'John'\n```\n\n## API\n\n### merge(destination, source, overwrite?)\n\nMerges \"own\" properties from a source to a destination object, including non-enumerable and accessor-defined properties. It retains original values and descriptors, ensuring the destination receives a complete and accurate copy of the source's properties.\n\nReturns the modified destination object.\n\n#### destination\n\nType: `object`\n\nThe object to receive properties.\n\n#### source\n\nType: `object`\n\nThe object providing properties.\n\n#### overwrite\n\nType: `boolean`\\\nDefault: `true`\n\nA boolean to control overwriting of existing properties.\n", "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "time": {"modified": "2023-11-17T16:22:01.206Z", "created": "2013-10-29T20:29:05.974Z", "0.0.1": "2013-10-29T20:29:06.954Z", "0.0.2": "2013-12-14T05:10:12.691Z", "1.0.0": "2015-03-01T21:30:01.609Z", "1.0.1": "2016-01-17T23:50:26.636Z", "1.0.2": "2023-11-16T17:27:55.804Z", "1.0.3": "2023-11-16T17:28:52.572Z", "2.0.0": "2023-11-16T18:49:20.714Z"}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/merge-descriptors.git"}, "readmeFilename": "readme.md", "homepage": "https://github.com/sindresorhus/merge-descriptors#readme", "bugs": {"url": "https://github.com/sindresorhus/merge-descriptors/issues"}, "license": "MIT", "users": {"fatelei": true, "simplyianm": true, "wangnan0610": true, "joshperry": true, "nanhualyq": true, "liushoukai": true, "aitorllj93": true, "456wyc": true, "xu_q90": true, "mojaray2k": true, "magicxiao": true, "leonzhao": true, "duartemendes": true, "ghettovoice": true, "kodekracker": true, "bhaskarmelkani": true, "damon.huang": true, "srksumanth": true}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["merge", "descriptors", "object", "property", "properties", "merging", "getter", "setter"]}