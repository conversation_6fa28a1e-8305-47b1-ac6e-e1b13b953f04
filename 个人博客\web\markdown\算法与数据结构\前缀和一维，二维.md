# 前缀和：可以快速的求一段区间的数组和



## 创建一维前缀和数组：简单来说就是数列前n项和

```c++
int a[N],s[N];
s[0]=0;//要求1到x之间的区间长可以套用到同一个公式中，我们是从i=1开始初始化的，所以默认为零
for(int i=1;i<=n;i++)
s[i]=s[i-1]+a[i];
//求[l,r]之间数组的和
s[r]-s[l-1]就是这个数组和
```

[ACWING]: https://www.acwing.com/problem/content/797/	"acwing797"



```c++
#include<iostream>
#include<algorithm>
using namespace std;

const int N=1e5+10;

int main()
{
    int n,m;
    cin>>n>>m;
    int a[N],s[N];
    for(int i=1;i<=n;i++)
    {
        cin>>a[i];
    }
    for(int i=1;i<=n;i++)
    s[i]=s[i-1]+a[i];
    while(m--)
    {
        int l,r;
        scanf("%d%d",&l,&r);
        printf("%d\n",s[r]-s[l-1]);
    }
    return 0;
}
```

创建二维前缀和：

借助图来理解

s[i,j]:表示的是就是i，j这个位置左上角的值的和

![image-20241121170912942](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241121170912942.png)

s[i,j]求法：递推出来，s[i,j]=s[i-1,j]+s[i,j-1]-s[i-1,j-1]+a[i,j]

![image-20241121171531797](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241121171531797.png)

![image-20241121171146662](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241121171146662.png)

(x1,y1)到(x2,y2)之间的格子和:

s[x2,y2]-s[x1-1,y2]-s[x2,y1-1]+s[x1-1,y1-1]

[子矩阵的和]([796. 子矩阵的和 - AcWing题库](https://www.acwing.com/problem/content/submission/code_detail/30998205/))

```c++
#include<iostream>
using namespace std;

const int N=1010;

int main()
{
    int n,m,q,a[N][N],s[N][N];
    scanf("%d%d%d",&n,&m,&q);
    for(int i=1;i<=n;i++)
    for(int j=1;j<=m;j++)
    {
        scanf("%d",&a[i][j]);
    }
    //初始化前缀和
    for(int i=1;i<=n;i++)
    for(int j=1;j<=m;j++)
    {
        s[i][j]=s[i-1][j]+s[i][j-1]-s[i-1][j-1]+a[i][j];
    }
    //询问
    while(q--)
    {
        int x1,y1,x2,y2;
        scanf("%d%d%d%d",&x1,&y1,&x2,&y2);
        printf("%d\n",s[x2][y2]-s[x1-1][y2]-s[x2][y1-1]+s[x1-1][y1-1]);
    }
    return 0;
}

```

