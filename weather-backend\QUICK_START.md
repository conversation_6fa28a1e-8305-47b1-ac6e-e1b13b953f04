# 快速开始指南

## 🚀 5分钟快速启动

### 1. 安装依赖

```bash
cd weather-backend
npm install
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件
nano .env
```

在 `.env` 文件中设置你的 OpenWeatherMap API Key：

```env
OPENWEATHER_API_KEY=你的API密钥
```

> 💡 **获取免费 API Key**: 访问 [OpenWeatherMap](https://openweathermap.org/api) 注册账号即可获得免费的 API Key

### 3. 启动服务

**开发模式:**
```bash
npm run dev
```

**生产模式:**
```bash
npm start
```

### 4. 测试服务

打开浏览器访问：
- 健康检查: http://localhost:3000/health
- 获取北京天气: http://localhost:3000/api/weather/current?city=Beijing

## 📋 API 使用示例

### 获取当前天气
```bash
curl "http://localhost:3000/api/weather/current?city=Beijing&lang=zh_cn"
```

### 获取天气预报
```bash
curl "http://localhost:3000/api/weather/forecast?city=Shanghai&days=3&lang=zh_cn"
```

### 通过坐标获取天气
```bash
curl "http://localhost:3000/api/weather/coordinates?lat=39.9042&lon=116.4074&lang=zh_cn"
```

### 搜索城市
```bash
curl "http://localhost:3000/api/weather/search?q=Beijing&limit=5"
```

## 🧪 运行测试

```bash
npm test
```

## 🐳 Docker 部署

```bash
# 构建镜像
docker build -t weather-backend .

# 运行容器
docker run -p 3000:3000 --env-file .env weather-backend

# 或使用 docker-compose
docker-compose up -d
```

## 📊 监控和日志

### 使用 PM2 (推荐生产环境)

```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js --env production

# 查看状态
pm2 status

# 查看日志
pm2 logs weather-backend

# 重启应用
pm2 restart weather-backend
```

## 🔧 常见问题

### Q: API Key 无效
**A:** 确保在 `.env` 文件中设置了正确的 `OPENWEATHER_API_KEY`

### Q: 端口被占用
**A:** 修改 `.env` 文件中的 `PORT` 值，或停止占用端口的进程

### Q: 请求被限制
**A:** OpenWeatherMap 免费账户有请求限制，请检查你的使用量

### Q: 城市未找到
**A:** 尝试使用英文城市名称，或使用坐标查询

## 📚 更多信息

- [完整 API 文档](./API.md)
- [项目说明](./README.md)
- [客户端使用示例](./examples/client-example.js)

## 🆘 获取帮助

如果遇到问题，请检查：
1. Node.js 版本 >= 16.0.0
2. 网络连接正常
3. API Key 有效且未超出限制
4. 环境变量配置正确
