{"_id": "mime-db", "_rev": "108-7d3c81826b0d95fb3706544023835ec4", "name": "mime-db", "dist-tags": {"latest": "1.54.0"}, "versions": {"0.0.0": {"name": "mime-db", "version": "0.0.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@0.0.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "e0d8c5ca1d3905e1287a87701cd2e003911ac4ed", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-0.0.0.tgz", "integrity": "sha512-hxzl8atSyUfOSKL+jXfqb4yhWEtIPPGot6GIkhNrRZCj4/G7lKGLK9/frwKf5nG7wcNLN3sblrWpMCNW2hJ5rQ==", "signatures": [{"sig": "MEUCIQDTyCubrm7T1Mgxl03vXu6yTFlun/xnaq82mauCXXHPXAIgBo/jR3auMULl7Nd7vriTarNvCJ/7upnXD+HK4g8KdYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "db.json", "_from": ".", "files": ["db.json"], "_shasum": "e0d8c5ca1d3905e1287a87701cd2e003911ac4ed", "gitHead": "482c13c51e9a564cd8e2c6e24071b562fbdc116e", "scripts": {"test": "mocha --reporter spec", "clean": "rm src/*", "update": "node --harmony-generators scripts/extensions; node --harmony-generators scripts/types; node scripts/build"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "mocha": "1", "cogent": "1", "csv-parse": "0", "stream-to-array": "2"}}, "1.0.0": {"name": "mime-db", "version": "1.0.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.0.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "05786d9d41fd2b93065f05c7378d6be273602c06", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.0.0.tgz", "integrity": "sha512-Gn48in9/k1yRg8q7l5/RqEngmOl9R2zhqfyFEiGVEkv6C03IYvpGmJAEPvzgxOMaBpPHPGTiUzLztLiJdcX4Lg==", "signatures": [{"sig": "MEUCIQDEo1M6CrTMQOwh6vIQjFPL9obBSmcpJ25QMFIuWeGNOQIgcLjQfPK5/cSWPdR1Rrov9SrgzdKkIuE8QIhonJCm6ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "engine": {"node": ">= 0.6.0"}, "_shasum": "05786d9d41fd2b93065f05c7378d6be273602c06", "gitHead": "5671cb9358a9113859df3066c536ebcb1376f46a", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.0.8", "mocha": "1", "cogent": "1", "istanbul": "0.3.0", "csv-parse": "0", "stream-to-array": "2"}}, "1.0.1": {"name": "mime-db", "version": "1.0.1", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.0.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "35d99b0965967253bb30633a7d07a8de9975a952", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.0.1.tgz", "integrity": "sha512-PtV/sUuFj50vT1hYmieMBrO6HlCy+me13yM6pnqKq0BAYbEjyWM7A2yL/n0Zz+MK4Q+KpQwWLHrOrsMfOXQB4A==", "signatures": [{"sig": "MEUCIE6Aqhc/eoBQGpAivQXBeRT43QMqLS3xp/IVR8fe6YHtAiEAzPi/aAAvJy/EEYelN9L5YKh3iSlBDVgtNjTnSfuIjCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "engine": {"node": ">= 0.6.0"}, "_shasum": "35d99b0965967253bb30633a7d07a8de9975a952", "gitHead": "6c9ee137430015b52887901377ac2f33e21f4078", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.0.8", "mocha": "1", "cogent": "1", "istanbul": "0.3.0", "csv-parse": "0", "stream-to-array": "2"}}, "1.0.2": {"name": "mime-db", "version": "1.0.2", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.0.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "dfb351df979c2b59bfcab5c1e187469b6da1bba4", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.0.2.tgz", "integrity": "sha512-OCpkl8t9DgqDh95oFehR+NhmR90AnuEVEqE6enU2oy+8RJ+j11Bulou6HPfnMC3aZ9QPI6IkAzEWAj5D2QrxKA==", "signatures": [{"sig": "MEUCIQCBwWPz7qi6LxaCaCttFahy0M05p+RYQHzqzHOyyjgqcQIgW68Xlt9eJBJbmvzxwB75rR5GBuk26+A3rrT1cTnzE/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "engine": {"node": ">= 0.6"}, "_shasum": "dfb351df979c2b59bfcab5c1e187469b6da1bba4", "gitHead": "7e73ecca04a6b1af7ceacb6e6434fc81a03e5620", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.0.3": {"name": "mime-db", "version": "1.0.3", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.0.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "5680b12300b3cecea5620f20f269ee80f2632d81", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.0.3.tgz", "integrity": "sha512-3uvomBfddD/PLXlNwQpXNhnS0TKQLe2Qk0sdp7krxLPJ3y8u+bIx6IBD//70o3t2cXQwSTvjNzAu/Fuz0ZY3Dw==", "signatures": [{"sig": "MEYCIQCAmfK92PhyYPxbXn9eBG/M5tsmp3sqp2EYLiVyq5f6GgIhAMRXcI+Xz3Peknx/dGpff8AP3hC02PTk5OU0dc49o5g7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "_shasum": "5680b12300b3cecea5620f20f269ee80f2632d81", "engines": {"node": ">= 0.6"}, "gitHead": "0be986cf71b58b78dee1580f600811c0ddf2a358", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.1.0": {"name": "mime-db", "version": "1.1.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.1.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "4613f418ab995450bf4bda240cd0ab38016a07a9", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.1.0.tgz", "integrity": "sha512-NlvZ2Wh2YjXEtuqLzdfmE61uFMS3pnOdVTA0xNmO3d3kJxM31r0gCSBJE56H2fBM3o7k6/IkYlPXuPJrTn4B4g==", "signatures": [{"sig": "MEYCIQDyccXh19K9YW4GgqDccqdwjvRCVa59jkXk/9emBhMjVwIhAO1P3ELkxAZNOC0SdZXRErPFOvqYwitcd7aSgItLZ/of", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "_shasum": "4613f418ab995450bf4bda240cd0ab38016a07a9", "engines": {"node": ">= 0.6"}, "gitHead": "7ed17588ab9c87b7f8f77f68326ef689bdfb4b61", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.1.1": {"name": "mime-db", "version": "1.1.1", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.1.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "0fc890cda05d0edadefde73d241ef7e28d110a98", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.1.1.tgz", "integrity": "sha512-5bNeht18e1RuKLVigz8aEVC7yhwFp7+ucIt7uZaHxUDnj/6QrngRfBh5CQcZvdCO6wIuugNNIxN+noZhwZ9KEA==", "signatures": [{"sig": "MEUCIHCG75QRaCfgSN+jEDCsm4yJfikjyfXouu5goW38b1MIAiEAtm48spK9ID4crH6e4b4iNgfrw9T00SA8PQo9Kv4P+hM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "_shasum": "0fc890cda05d0edadefde73d241ef7e28d110a98", "engines": {"node": ">= 0.6"}, "gitHead": "edc213cbc9828807ea29ea3cc290b4fd485eae91", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "2.1.4", "description": "Media Type Database", "directories": {}, "_nodeVersion": "0.11.14", "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.1.2": {"name": "mime-db", "version": "1.1.2", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.1.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "893d6c510f7e3f64fc75d8a23a48401f669e7fdb", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.1.2.tgz", "integrity": "sha512-4NCo22vfuOzv0iHBEBirBZn4GJIM0PgT0eS3Mc1OAa4tjLvSUgrLy44iPxVW+Rgiu0AnSOVQGsJnFV0+OtQzNQ==", "signatures": [{"sig": "MEYCIQDznrpl2YymHrnI3hZiOfRatpxyUF9LtRSbGEG/GkWkSgIhAODo/lhn7JuZj2/dC7WLFRMcob1rHUfrkm4rnNHb7SLe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "_shasum": "893d6c510f7e3f64fc75d8a23a48401f669e7fdb", "engines": {"node": ">= 0.6"}, "gitHead": "24ffc1c4bef8465cae0253448bc48437dda3124e", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.2.0": {"name": "mime-db", "version": "1.2.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.2.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "76b92e7ecac673f5dab066a10b66faea1be2f01f", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.2.0.tgz", "integrity": "sha512-ZwZvp628p0pIt334fP34tLUl7zceMyemCQxf0NyjAIR1LrJnM08wCXxfwNGUVSfBDqegYud21GzhxvgdflFGIA==", "signatures": [{"sig": "MEUCICHPcGTnPYn2RAWYApxk14F19/4huHtbmvUIIOWkvBw3AiEAyT7u3Gul54cNvKLgJNZS+a+zKIwhEX2bTxzMBFVPwnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "_shasum": "76b92e7ecac673f5dab066a10b66faea1be2f01f", "engines": {"node": ">= 0.6"}, "gitHead": "caf5b253569f0267e31044602d2f1078028e2361", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.3.0": {"name": "mime-db", "version": "1.3.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.3.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "5fefeb25dd9b097c5d45091c60f8149b98d749ec", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.3.0.tgz", "integrity": "sha512-+dkwAhvPOIHKIJypaHKfE0lqmgsqwJuxYs5eMHrq+ovj5ma5dzYf7vQIcjnSSqQa95Qui9aXQbzpqvD8rkKdFA==", "signatures": [{"sig": "MEUCIHp7J1wsvUrhlS1fKrjEzy2drQ6QyMI1nBC0U2GT6rJHAiEA3Gqd9EeLJG2JPNwjqrYstj1vK3ZU0Bf1tArLB1JI6s8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "_shasum": "5fefeb25dd9b097c5d45091c60f8149b98d749ec", "engines": {"node": ">= 0.6"}, "gitHead": "dc3a4d4948e9e6814404712d0f3560f1fffe7d73", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.4", "csv-parse": "0", "stream-to-array": "2"}}, "1.3.1": {"name": "mime-db", "version": "1.3.1", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.3.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "b1cd51ee8c4a674c49e03a96d67565fc768ce941", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.3.1.tgz", "integrity": "sha512-iKx8a4LeyzujFpmHAVf4LTNNC+eoeuvlaKeAUOZVmbuwQiEXsirdfA05GuyF+eA/YzWecniLmCizVK1Ic62eqw==", "signatures": [{"sig": "MEUCIFpX4sOZzYFgTizPnx9lEOB6aBXwwd9mKxxGmaVCmgZBAiEA76Z2+qqNqj1YSSY0QnULuyvuVvExBpedocoXYuCNoJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "db.json", "index.js"], "_shasum": "b1cd51ee8c4a674c49e03a96d67565fc768ce941", "engines": {"node": ">= 0.6"}, "gitHead": "98a91c9ddaf83e5c9b197d1ad26981a66e074c26", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.21", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.5", "csv-parse": "0", "stream-to-array": "2"}}, "1.4.0": {"name": "mime-db", "version": "1.4.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.4.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "58573481b042aca7da48bd22510891b1dbf11b03", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.4.0.tgz", "integrity": "sha512-lzmX8CQJqVVgern1JPgE4MC7krhHfwCJgyMoizYZiAjTzdBTPybYqB20YIY8FohPWLtrB0AohSx7s9EkXKILyQ==", "signatures": [{"sig": "MEUCIHIbJ4SsnUuFLzVUEezaXYPHn6cFSkEb4zg+OIeLB1/AAiEAuyzl2IBKgJSY9DP1xwLJqy6QWQK5Zp03y4aRa/2F6r0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "58573481b042aca7da48bd22510891b1dbf11b03", "engines": {"node": ">= 0.6"}, "gitHead": "3a452a9ab72ff6f3af7954bec77657c2d0699717", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "3", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.5", "raw-body": "~1.3.1", "csv-parse": "0", "stream-to-array": "2"}}, "1.5.0": {"name": "mime-db", "version": "1.5.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.5.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "bd80b576157991c3b46c71be7041fc6d5402a6ee", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.5.0.tgz", "integrity": "sha512-3xQBpzQiUm7Xkdkb+86e7tR0LGclaeVogP6Q0HDogEOefew60Iklx7sJx+jFooaoBDFNMJElkhJ8qzB/i6cVXg==", "signatures": [{"sig": "MEYCIQD3Gge+Oif7IqjekR8E9psMiTvS1kOW0N+faVZb8CZZNwIhAKQEAu/wyfHzWvxY1e7+R6qboDNoRxshIhKuk+4XOTvx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "bd80b576157991c3b46c71be7041fc6d5402a6ee", "engines": {"node": ">= 0.6"}, "gitHead": "262fafb4a696cae208d6148c6642ee45bce07cb3", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.5", "raw-body": "~1.3.1", "csv-parse": "0", "stream-to-array": "2"}}, "1.6.0": {"name": "mime-db", "version": "1.6.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.6.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "7453d2097b080cad8044f04e856b3408f31e1ff3", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.6.0.tgz", "integrity": "sha512-sSLIDySmGtQ6XwiQctsRLmLu4WTjh1EXj0bSSz+jUXlh1ZJqrT2bRrI7vAU/Af5sxboeZwcLCvklq8RO6Qg67w==", "signatures": [{"sig": "MEQCID+WkYoL9pJv75n0YGGEZSw/pxu6vt62s1U62RBSTbZBAiArbK3Lb2aPngj88Dqs8MvKgHkDGByTLLYT43dNqyXiig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "7453d2097b080cad8044f04e856b3408f31e1ff3", "engines": {"node": ">= 0.6"}, "gitHead": "ca3a8540fc873694ad85dab835463c1ca3e0152a", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.5", "raw-body": "~1.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.6.1": {"name": "mime-db", "version": "1.6.1", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.6.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "6e85cd87c961d130d6ebd37efdfc2c0e06fdfcd3", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.6.1.tgz", "integrity": "sha512-aSCBBINk3+H/xngoOj2y6aKL2RfjIVbyxp7FjAEWLcMW2jG3DsnAmKzdl9l+q2c3xcQfAuaXzVrd1lIlkIvtCA==", "signatures": [{"sig": "MEYCIQCnSgv8Jn9YfcCgKSTFOAJhqbr+Pyp+XovAFB+8lTPHgwIhAPQfQSB7cvBG3hR6yj0f5mx5B7Wbm7JDJBbTTtn8Gims", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "6e85cd87c961d130d6ebd37efdfc2c0e06fdfcd3", "engines": {"node": ">= 0.6"}, "gitHead": "7f07ff87267625b73dcf73b97b2530a37a85d079", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "clean": "rm src/*", "update": "gnode scripts/extensions && gnode scripts/types && node scripts/build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.5", "raw-body": "~1.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.7.0": {"name": "mime-db", "version": "1.7.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "mime-db@1.7.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "36cf66a6c52ea71827bde287f77c254f5ef1b8d3", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.7.0.tgz", "integrity": "sha512-AxBDWDiuUdwOdZcMfJM0Mp+TC64U+VWCU5uGC4wVZvpG/xvl5HlptFyXBmgoNabjLpaVhxU0GdAU0dkK12n/Sg==", "signatures": [{"sig": "MEQCIHFS0GuzkpYKKE1YLxFFkL6iKeA0FXLEGWcjXuBiixJHAiBUK0OWS8DBgSe9OcPKL/RyJk63XhW6S+6HZYZBVUPEGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "36cf66a6c52ea71827bde287f77c254f5ef1b8d3", "engines": {"node": ">= 0.6"}, "gitHead": "972cc3ed48530ab7aca7a155bf2dbd1b13aa8f86", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4", "gnode": "0.1.0", "mocha": "~1.21.4", "cogent": "1", "istanbul": "0.3.5", "raw-body": "~1.3.2", "csv-parse": "0", "stream-to-array": "2"}}, "1.8.0": {"name": "mime-db", "version": "1.8.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.8.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "82a9b385f22b0f5954dec4d445faba0722c4ad25", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.8.0.tgz", "integrity": "sha512-a3JzHUfHB7xd3D8GSFi1bdq6HtQ6tS559/tqNyw5XUpLxhls8MHDTTtH6GEKi4UEsoPmtn60zIjCyDd9vTNT+g==", "signatures": [{"sig": "MEYCIQC0qwCSyiDMpaj5JxBvmcAQjCaWWelNlbFEMuz4fPPvOgIhAOpLQChxye6DrojXv7wO2V4AdfFZRZfOpa8mOE0E+wH1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "82a9b385f22b0f5954dec4d445faba0722c4ad25", "engines": {"node": ">= 0.6"}, "gitHead": "cd5730a475ff03d2ef49fc571d5510a548b63494", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "~4.4.0", "gnode": "0.1.1", "mocha": "~1.21.4", "cogent": "1", "bluebird": "~2.9.14", "istanbul": "0.3.7", "raw-body": "~1.3.3", "csv-parse": "0.0.9", "stream-to-array": "2"}}, "1.9.0": {"name": "mime-db", "version": "1.9.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.9.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "dba46663957551864f8142cc22ef364b2a9b4797", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.9.0.tgz", "integrity": "sha512-bTOKQiN3FUB+L6gobA57O6Y+yL3MdTafI6AFoVubxobl/Qg/tolKm1UwUiIqG9BjZyVKqvHf3K+Pzw2Y4ugw6A==", "signatures": [{"sig": "MEYCIQCR5XKb0iJB0evN+PZMNptwzU8ougNAHtc+XjNzoIEz7wIhAKwd56RZ/lbDMQBuWkbtrUv9GBuraaqG5HiDkShKiP6d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "dba46663957551864f8142cc22ef364b2a9b4797", "engines": {"node": ">= 0.6"}, "gitHead": "3ab05638863bf1973b733c8a652581ea4c6b6613", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.5.1", "gnode": "0.1.1", "mocha": "~1.21.4", "cogent": "1", "bluebird": "~2.9.20", "istanbul": "0.3.8", "raw-body": "~1.3.3", "csv-parse": "0.1.0", "stream-to-array": "2"}}, "1.9.1": {"name": "mime-db", "version": "1.9.1", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.9.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "1431049a71791482c29f37bafc8ea2cb3a6dd3e8", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.9.1.tgz", "integrity": "sha512-PtNmUNBDYEHKwjdF8Ma1ObpcQSMITKJe+5jJcglR7Mvl4bo9A+Z+PaJa/aLUniIP5srtxmPWtxB8oP2AZw9y6w==", "signatures": [{"sig": "MEQCICY8pfbh7Ojs+pmwnSBv6JjdwJrb2B5Uh2kFRCqjbizMAiBN3x853CqY/BaIDJB7FMuLzd5RJN0c6Cg8emqt3fVRHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "1431049a71791482c29f37bafc8ea2cb3a6dd3e8", "engines": {"node": ">= 0.6"}, "gitHead": "590a8b6afceeee64b424fcd2d0d73a3bebc81685", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.5.1", "gnode": "0.1.1", "mocha": "~1.21.4", "cogent": "1", "bluebird": "~2.9.20", "istanbul": "0.3.8", "raw-body": "~1.3.3", "csv-parse": "0.1.0", "stream-to-array": "2"}}, "1.10.0": {"name": "mime-db", "version": "1.10.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.10.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "e6308063c758ebd12837874c3d1ea9170766b03b", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.10.0.tgz", "integrity": "sha512-DjK7/0WjxJfKPgOZN4wWwK+ecI9rR/rEYfrJuZr5Z12BUCFCrB5dXwqa1VNxFt0Ul7NSBH6X9w65AodEV3DN1Q==", "signatures": [{"sig": "MEUCIQCTmwcpnzn+R9XyIKfunf6dkNo6oFbn5C/pDDZVd5lKxwIgdtCscxjORsEAV4Gjo1qdI7kTW8xXT4Mtt4nJO/2Zdl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "e6308063c758ebd12837874c3d1ea9170766b03b", "engines": {"node": ">= 0.6"}, "gitHead": "260552f9177fe78986b92699999f81999c7fe43c", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.5.4", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "~2.9.20", "istanbul": "0.3.9", "raw-body": "2.0.1", "csv-parse": "0.1.1", "stream-to-array": "2"}}, "1.11.0": {"name": "mime-db", "version": "1.11.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.11.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "658e1563b52d733a78161224b835166b457069ab", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.11.0.tgz", "integrity": "sha512-SvKY2AKL2bRUOrkZo/bcUkbvJurXKX2SnexKtc9zpG7yMl/0M1Hmebg0eVNOTypyPZQR4bnagtYVOyuGXhEnMw==", "signatures": [{"sig": "MEQCIAhR1DCkI4wDXhexINR5gOabRqwbZaKCDAOrUspe4fTpAiAgCkR71LaETHunxyqcKDUn7VgC25L/CKzV4he5APOQlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "658e1563b52d733a78161224b835166b457069ab", "engines": {"node": ">= 0.6"}, "gitHead": "70f7dadc39f262e1a8bdff27cefe996d0ed4e043", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.5.4", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.9.27", "istanbul": "0.3.9", "raw-body": "2.1.0", "csv-parse": "0.1.2", "stream-to-array": "2"}}, "1.12.0": {"name": "mime-db", "version": "1.12.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.12.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "3d0c63180f458eb10d325aaa37d7c58ae312e9d7", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.12.0.tgz", "integrity": "sha512-5aMAW7I4jZoZB27fXRuekqc4DVvJ7+hM8UcWrNj2mqibE54gXgPSonBYBdQW5hyaVNGmiYjY0ZMqn9fBefWYvA==", "signatures": [{"sig": "MEQCIB0SDZP6C8gaE3wVwttXcv1FoGCnCQXQ0KV0I4z8EUEWAiBj30g9YKqRlkr959PAMzn2nrUKissk0tlctHwKZIx+hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "3d0c63180f458eb10d325aaa37d7c58ae312e9d7", "engines": {"node": ">= 0.6"}, "gitHead": "cf35cbba6b22f4a3b3eef9a32129ea5b7f0f91ee", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/extensions && gnode scripts/types", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.5.4", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.9.27", "istanbul": "0.3.9", "raw-body": "2.1.0", "csv-parse": "0.1.2", "stream-to-array": "2"}}, "1.13.0": {"name": "mime-db", "version": "1.13.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.13.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "fd6808168fe30835e7ea2205fc981d3b633e4e34", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.13.0.tgz", "integrity": "sha512-SEBVmyKnXAZWWrcsUxZNylS5E9ZZnqqi4RuJAvVLtwFZWcSKLinYQuLBtsTsDEuq6WpcfSH858N9sz5z8tWBtQ==", "signatures": [{"sig": "MEQCIHDbp3F+2rCz3DTbyB2qZW0DaN+nqSxgehYGNzraP97XAiAUavgS5RHeMAPs8GMu1bpGyP/fc4ZPnZ09nhUAUbKicA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "fd6808168fe30835e7ea2205fc981d3b633e4e34", "engines": {"node": ">= 0.6"}, "gitHead": "cd78635e4f8baf85d91b4edcd071f77f94a08c53", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.5.4", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.9.27", "istanbul": "0.3.14", "raw-body": "2.1.0", "csv-parse": "0.1.2", "stream-to-array": "2"}}, "1.14.0": {"name": "mime-db", "version": "1.14.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.14.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "d561f10b6ee66db51f94ae657a2951a74217ed83", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.14.0.tgz", "integrity": "sha512-01GmsNWrAeYVvfoUXOe1D/ABwa8RlJoGo+F6T4qu2JDv2epBlpuIeZOtIm75bMZaXbut2LMN1CqUHCVg5sbh1A==", "signatures": [{"sig": "MEQCIC6zyajgc/KMurH7G3bpg4liRc+WuezGiOoFLgjAcUQPAiA7ImIIANoyFq42DwLW2rqe0GET5XT/J9yXMLiIIR1yVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "d561f10b6ee66db51f94ae657a2951a74217ed83", "engines": {"node": ">= 0.6"}, "gitHead": "9803c407b6621daba9363f534cfab18255c945a8", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.5.4", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.9.30", "istanbul": "0.3.16", "raw-body": "2.1.1", "csv-parse": "0.1.3", "stream-to-array": "2"}}, "1.15.0": {"name": "mime-db", "version": "1.15.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.15.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "d219e6214bbcae23a6fa69c0868c4fadc1405e8a", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.15.0.tgz", "integrity": "sha512-u6JnCbeyuqShRSBzgr/pWdyQqMn9Zwc4RLmd9v5RgtnWDJZ6EV5BwCH+WuykJHY8G30yQzVdZDOfhjQQzkXO5A==", "signatures": [{"sig": "MEUCIQCIJjASy41pbeydmuF2/hjRORJkgFm3EEn3Se5pzVnuPQIgX5pUcJ/UBlEmytdgYnWghNl74Z40CZPO5UE0vODQvu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "d219e6214bbcae23a6fa69c0868c4fadc1405e8a", "engines": {"node": ">= 0.6"}, "gitHead": "96922b79fcaacf8c2a95ce3368739ec71c9471a2", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.6.0", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.9.33", "istanbul": "0.3.17", "raw-body": "2.1.2", "csv-parse": "0.1.3", "stream-to-array": "2"}}, "1.16.0": {"name": "mime-db", "version": "1.16.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.16.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "e83dce4f81ca5455d29048e6c3422e9de3154f70", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.16.0.tgz", "integrity": "sha512-EEJYXBPlkfM9BJZyRjtTHk3qd0t+MO6o0X231jTJrXOR7AMCtFp5UtyXPWTEgBrot15Vo/ePbRhk/pYZDChYZA==", "signatures": [{"sig": "MEQCIFPYrGs/bEqo1GcxHYvEpdcCA+WLNIntbCSZbP2ql2/pAiACu8MhyNiPS7ohnWx6NG76L/OGReZ/ACt6CbtpNrCK0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "e83dce4f81ca5455d29048e6c3422e9de3154f70", "engines": {"node": ">= 0.6"}, "gitHead": "81c7d528a1e9711084f64adbb99b70c24e8fb8c9", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.6.0", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.9.34", "istanbul": "0.3.17", "raw-body": "2.1.2", "csv-parse": "0.1.4", "stream-to-array": "2"}}, "1.17.0": {"name": "mime-db", "version": "1.17.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.17.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "95bdc044092d2bcc3189dd19fbed6ed3a3f3df2a", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.17.0.tgz", "integrity": "sha512-Ujt8KfNJPY39kygBhminaeHD2gppMc6KcXLQj30z5Fmj98gXrstdUl5yvWrXhNGOn5DjPUTyn1ZxYy0+ZJHQ3A==", "signatures": [{"sig": "MEUCICchBBnOZAvIc/klr3lSEwxZh/VL42BkcKnIYEr2L1/WAiEAgnq5g65WHNzsXx0KJvVWCyH0FflcS1A17HxBVINwIBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "95bdc044092d2bcc3189dd19fbed6ed3a3f3df2a", "engines": {"node": ">= 0.6"}, "gitHead": "6525b89bd6d8f901d3c5b072741f0fbc4a4d60c3", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.6.0", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.9.34", "istanbul": "0.3.17", "raw-body": "2.1.2", "csv-parse": "1.0.0", "stream-to-array": "2"}}, "1.18.0": {"name": "mime-db", "version": "1.18.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.18.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "5317e28224c08af1d484f60973dd386ba8f389e0", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.18.0.tgz", "integrity": "sha512-uGdQeU9fAlL6Ku8iwYwapDOiSnF2s0liraQhGiJd5GlJLqnQsWBdH3nlBPCK3B2r4AMIscHP8KOqDVYDvX3RgQ==", "signatures": [{"sig": "MEUCIH/l9ujWa0L3iWGj3wqLdO0a3TawxEckS2oENP7HMVugAiEAspE3smjqZhJ4W9/qIrOxgTjLWHAMeR+5URB4QU98w+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "5317e28224c08af1d484f60973dd386ba8f389e0", "engines": {"node": ">= 0.6"}, "gitHead": "c48209a8786e61f20499ba14167252ad67638c5f", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.6.0", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.9.34", "istanbul": "0.3.19", "raw-body": "2.1.2", "csv-parse": "1.0.0", "stream-to-array": "2"}}, "1.19.0": {"name": "mime-db", "version": "1.19.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.19.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "496a18198a7ce8244534e25bb102b74fb420fd56", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.19.0.tgz", "integrity": "sha512-pY28EWl2RLz+bklnyeNegNYxKvzySdwTFWc8g1Ex0k1V1HfJ2wu2E+Mc7/5X1TDjdBEaMNl3Rw0By+OgpO+1Eg==", "signatures": [{"sig": "MEQCIDd9YGH9RlXWwdxEG9MyxAMzWJtvVpDTBu6HEiBMBY5xAiAPM0p90MO57gTGJEuNCcq0MPpuKsmjhMyvSgzkFbTHkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "496a18198a7ce8244534e25bb102b74fb420fd56", "engines": {"node": ">= 0.6"}, "gitHead": "46a40f0524a01fb3075a7ecde92e8e04fc93d599", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.6.0", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.10.0", "istanbul": "0.3.20", "raw-body": "2.1.3", "csv-parse": "1.0.0", "stream-to-array": "2"}}, "1.20.0": {"name": "mime-db", "version": "1.20.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.20.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "496f90fd01fe0e031c8823ec3aa9450ffda18ed8", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.20.0.tgz", "integrity": "sha512-E<PERSON>+5Pcf8fs1yDZSisvAB68AvA67McCHqSKB9UB1nIrCX9efN6gKInT5gQX0HyWf/SZlWhTfoZQ+2sYF2TkI4A==", "signatures": [{"sig": "MEYCIQDGivvM3YYZokdm1ezCUGx76qBWfpKsmhWckZy+TwkE1QIhALBND03khB/NslCmJ8qCZBMnmFO7lgq7gMAArsUXtRqX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "496f90fd01fe0e031c8823ec3aa9450ffda18ed8", "engines": {"node": ">= 0.6"}, "gitHead": "20c99312645c05ab8466701ede01bd5cd3ac7bc4", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.6.0", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "2.10.0", "istanbul": "0.4.0", "raw-body": "2.1.4", "csv-parse": "1.0.0", "stream-to-array": "2.2.0"}}, "1.21.0": {"name": "mime-db", "version": "1.21.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.21.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "9b5239e3353cf6eb015a00d890261027c36d4bac", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.21.0.tgz", "integrity": "sha512-ZUMwgLSDVGB4nNEh59wa+ntQMXlxFhmXHlXi25NViUqe+SiIijPD8k2DC3/0GnRi1ohl5ohQbpkecoW3sab/Jw==", "signatures": [{"sig": "MEUCIAd7yUUDVdJwI+u9RFQAY/n0q03CEE4sjk9TOEZgBpMTAiEA4HHjng0BBWiI5U+xSq/O/sa03Os3KmZyiw0bF1caxE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "9b5239e3353cf6eb015a00d890261027c36d4bac", "engines": {"node": ">= 0.6"}, "gitHead": "9ab92f0a912a602408a64db5741dfef6f82c597f", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.6.0", "gnode": "0.1.1", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "3.1.1", "istanbul": "0.4.1", "raw-body": "2.1.5", "csv-parse": "1.0.1", "stream-to-array": "2.2.0"}}, "1.22.0": {"name": "mime-db", "version": "1.22.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "ab23a6372dc9d86d3dc9121bd0ebd38105a1904a", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.22.0.tgz", "integrity": "sha512-n4fQVRPur8KsKpF9faaInRX3ZJnJ5FvuKKHKpJ5rTAQsgxbNHcG/JmSoopUTaHrjO+OqWIM5HLLVhhqAaEybFg==", "signatures": [{"sig": "MEUCIQCDn7sZp5VuM1pJnR175oKrOXO1LLLmvmUERvaA/eeiFAIgIeWOA4hqVhA6kHOjZFBXe79y69n/mti9TO47kTibe/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "ab23a6372dc9d86d3dc9121bd0ebd38105a1904a", "engines": {"node": ">= 0.6"}, "gitHead": "ed88d32405582a5aaff6225d1210005d6be2623e", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Media Type Database", "directories": {}, "_nodeVersion": "4.2.3", "devDependencies": {"co": "4.6.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "3.3.1", "istanbul": "0.4.2", "raw-body": "2.1.5", "csv-parse": "1.0.1", "stream-to-array": "2.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.22.0.tgz_1455558813990_0.7830642955377698", "host": "packages-9-west.internal.npmjs.com"}}, "1.23.0": {"name": "mime-db", "version": "1.23.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "a31b4070adaea27d732ea333740a64d0ec9a6659", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.23.0.tgz", "integrity": "sha512-lsX3UhcJITPHDXGOXSglBSPoI2UbcsWMmgX1VTaeXJ11TjjxOSE/DHrCl23zJk75odJc8MVpdZzWxdWt1Csx5Q==", "signatures": [{"sig": "MEYCIQDeKEKd0KR9nUD1AmTUewG6IfPO/1PvhgixDa3OoueQ8AIhAPBMQteP2DhLjOp4qyTeOi4+7VuS3p1/ozOcLFwyNQG2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "a31b4070adaea27d732ea333740a64d0ec9a6659", "engines": {"node": ">= 0.6"}, "gitHead": "ba0d99fd05b3bfdc2ebcd78f858c25cb7db6af41", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Media Type Database", "directories": {}, "_nodeVersion": "4.4.3", "devDependencies": {"co": "4.6.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "3.3.5", "istanbul": "0.4.3", "raw-body": "2.1.6", "csv-parse": "1.1.0", "stream-to-array": "2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.23.0.tgz_1462163798086_0.43938886746764183", "host": "packages-16-east.internal.npmjs.com"}}, "1.24.0": {"name": "mime-db", "version": "1.24.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "e2d13f939f0016c6e4e9ad25a8652f126c467f0c", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.24.0.tgz", "integrity": "sha512-0XGpuLCNPqkv3vYiRjh1w6h4RbIGWyCh8OnXejta9INkFX0M8ENYth8O0As8rSGDxzEO1PafhiaqQdtqhtA2lw==", "signatures": [{"sig": "MEUCIEr8qPOTJ23YNDRjIoSfkYHUNQgCfSdEPK2uEtOYVap+AiEAxEPdVO69PUhtjNccMAYM4z5hRrXibNyd1mWaGgqpho4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "e2d13f939f0016c6e4e9ad25a8652f126c467f0c", "engines": {"node": ">= 0.6"}, "gitHead": "9dd00b34556a8cdd6f3385f09d4989298c4b86e1", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Media Type Database", "directories": {}, "_nodeVersion": "4.5.0", "devDependencies": {"co": "4.6.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "bluebird": "3.4.6", "istanbul": "0.4.5", "raw-body": "2.1.7", "csv-parse": "1.1.7", "stream-to-array": "2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.24.0.tgz_1474198792761_0.7161959335207939", "host": "packages-16-east.internal.npmjs.com"}}, "1.25.0": {"name": "mime-db", "version": "1.25.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "c18dbd7c73a5dbf6f44a024dc0d165a1e7b1c392", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.25.0.tgz", "integrity": "sha512-5k547tI4Cy+Lddr/hdjNbBEWBwSl8EBc5aSdKvedav8DReADgWJzcYiktaRIw3GtGC1jjwldXtTzvqJZmtvC7w==", "signatures": [{"sig": "MEUCIBXOIBjFgNfk1IQRJb/HOPQKxKKWuAzRX9R9XDdVmRufAiEAz456fS2VQfkjBkXFWi0da9HzR96VyBZN7VUKmejoL3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "c18dbd7c73a5dbf6f44a024dc0d165a1e7b1c392", "engines": {"node": ">= 0.6"}, "gitHead": "9a2c710e347b4a7f030aae0d15afc0a06d1c8a37", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/mime-db", "type": "git"}, "_npmVersion": "1.4.28", "description": "Media Type Database", "directories": {}, "devDependencies": {"co": "4.6.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.9.1", "bluebird": "3.4.6", "istanbul": "0.4.5", "raw-body": "2.1.7", "csv-parse": "1.1.7", "stream-to-array": "2.3.0", "eslint-plugin-promise": "3.3.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.25.0.tgz_1478915345127_0.22604371700435877", "host": "packages-12-west.internal.npmjs.com"}}, "1.26.0": {"name": "mime-db", "version": "1.26.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "eaffcd0e4fc6935cf8134da246e2e6c35305adff", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.26.0.tgz", "integrity": "sha512-Bn4lTeTH3qxeM+u71GQVrY4AxQlqDT9jkapmEby7o6X9giHAS4U4ar/bzjkCocKAEPjP+77GmVxiYScExkiHyA==", "signatures": [{"sig": "MEYCIQDgWwSyQJ3EMOPyBGZ/eT7y4qAcYGAkOIGTHu6FSTsBagIhAKy7P9rcYZ6DHYH+0H06hcUhWvG9fj1KIyNSb58D3sur", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "eaffcd0e4fc6935cf8134da246e2e6c35305adff", "engines": {"node": ">= 0.6"}, "gitHead": "1d9ff30e45b07a506a20f25df3a3c7106d219e24", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Media Type Database", "directories": {}, "_nodeVersion": "4.6.1", "devDependencies": {"co": "4.6.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.13.1", "bluebird": "3.4.7", "istanbul": "0.4.5", "raw-body": "2.2.0", "csv-parse": "1.1.9", "stream-to-array": "2.3.0", "eslint-plugin-promise": "3.3.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.26.0.tgz_1484453096877_0.39498970191925764", "host": "packages-12-west.internal.npmjs.com"}}, "1.27.0": {"name": "mime-db", "version": "1.27.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "820f572296bbd20ec25ed55e5b5de869e5436eb1", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.27.0.tgz", "integrity": "sha512-DNhC90PjVkQJpLVP+ct0lmKPQWAHFy+67X8IBOx+mda/I9vsrdJO/zoyEJdQdLsofi/l8GAG+IsfB0XCPLyLHg==", "signatures": [{"sig": "MEQCIHJSyT35x//2Xx2FZfUPzlS8U/IrZycBKQg8I5z8dbzMAiAjKum0vM1wBPlZKGaYrsxNSf3cb+xzIoyxsNC7RXMN+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "820f572296bbd20ec25ed55e5b5de869e5436eb1", "engines": {"node": ">= 0.6"}, "gitHead": "c232c21378647dfbb7762410c7b025a47f114b94", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Media Type Database", "directories": {}, "_nodeVersion": "4.7.3", "devDependencies": {"co": "4.6.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.17.1", "bluebird": "3.5.0", "istanbul": "0.4.5", "raw-body": "2.2.0", "csv-parse": "1.2.0", "stream-to-array": "2.3.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.0.1", "eslint-plugin-standard": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.27.0.tgz_1489722296902_0.15233952621929348", "host": "packages-12-west.internal.npmjs.com"}}, "1.28.0": {"name": "mime-db", "version": "1.28.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "fedd349be06d2865b7fc57d837c6de4f17d7ac3c", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.28.0.tgz", "integrity": "sha512-xgdyjLGIikqOXO2g4aw0TDjd7HRHJHN8dwppYod1LqrIxjbBjPjuoC5ysZEYbn0+Mp0tTx3G5tUQrWIjnIOsog==", "signatures": [{"sig": "MEUCIGvkdbXSAzE1X228aveT+eMp/gpyiRo+abDg7oqyWYseAiEA8/3if6Z84S5cHF8i8fALCdKUFLKhPfaQelO0DUXNmP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "fedd349be06d2865b7fc57d837c6de4f17d7ac3c", "engines": {"node": ">= 0.6"}, "gitHead": "f5d4f91f4fc1ba6c078b1e511df2534a10cbfffe", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Media Type Database", "directories": {}, "_nodeVersion": "6.10.3", "devDependencies": {"co": "4.6.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.19.0", "bluebird": "3.5.0", "istanbul": "0.4.5", "raw-body": "2.2.0", "csv-parse": "1.2.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.28.0.tgz_1494826071205_0.6150839638430625", "host": "packages-18-east.internal.npmjs.com"}}, "1.29.0": {"name": "mime-db", "version": "1.29.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "48d26d235589651704ac5916ca06001914266878", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.29.0.tgz", "integrity": "sha512-7lKSppGEWkMwBxzcWKCwXreQ0GDj0fPtlwQG+jvtSMDICY1jTgYxnGmAFP35eIDmC2ldkJQdXj0Ot/j7tyahcA==", "signatures": [{"sig": "MEYCIQDmeIgRhzA/uFSihN/aLKkuChPY5625sICtQ1QZuy80TwIhAJrSol6KZ5WkwqcbnM7Jt7pnmnAJ/2KO8qS9TcTc9EMb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "48d26d235589651704ac5916ca06001914266878", "engines": {"node": ">= 0.6"}, "gitHead": "ee8f2459964025c3969a49b8f80c34b182d35e2f", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Media Type Database", "directories": {}, "_nodeVersion": "6.10.3", "devDependencies": {"co": "4.6.0", "nyc": "11.0.3", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.19.0", "bluebird": "3.5.0", "raw-body": "2.2.0", "csv-parse": "1.2.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.29.0.tgz_1499739590002_0.7720734812319279", "host": "s3://npm-registry-packages"}}, "1.30.0": {"name": "mime-db", "version": "1.30.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "74c643da2dd9d6a45399963465b26d5ca7d71f01", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.30.0.tgz", "integrity": "sha512-SUaL89ROHF5P6cwrhLxE1Xmk60cFcctcJl3zwMeQWcoQzt0Al/X8qxUz2gi19NECqYspzbYpAJryIRnLcjp20g==", "signatures": [{"sig": "MEQCIAGbqSZ/GcNcwehnjGiIihoo8dZPkzFiL+5O2UqDoy1MAiAKvoqD2TC9cp7ZiM1xbFEHUBlsY5OIriHrt43zr5s3sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "74c643da2dd9d6a45399963465b26d5ca7d71f01", "engines": {"node": ">= 0.6"}, "gitHead": "e62cf46c206681ca88b2e275f442a9885f1f86e4", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Media Type Database", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"co": "4.6.0", "nyc": "11.1.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.19.0", "bluebird": "3.5.0", "raw-body": "2.3.0", "csv-parse": "1.2.1", "stream-to-array": "2.3.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.30.0.tgz_1503887330099_0.8198229141999036", "host": "s3://npm-registry-packages"}}, "1.31.0": {"name": "mime-db", "version": "1.31.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.31.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "a49cd8f3ebf3ed1a482b60561d9105ad40ca74cb", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.31.0.tgz", "integrity": "sha512-oB3w9lx50CMd6nfonoV5rBRUbJtjMifUHaFb5MfzjC8ksAIfVjT0BsX46SjjqBz7n9JGTrTX3paIeLSK+rS5fQ==", "signatures": [{"sig": "MEUCIQCAIYS49XWxIEe0s0cYlXpIMiBywKQTngW2pKdhPyrSeAIgPln0Tg/mpNfoR8McJdzexyXQerPwzihdXgWcBtBeuv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "31cd8135785c237e4a12955030b05926530102d3", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Media Type Database", "directories": {}, "_nodeVersion": "6.11.5", "devDependencies": {"co": "4.6.0", "nyc": "11.3.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.19.0", "bluebird": "3.5.1", "raw-body": "2.3.2", "csv-parse": "1.3.1", "stream-to-array": "2.3.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.31.0.tgz_1508988351387_0.7232930199243128", "host": "s3://npm-registry-packages"}}, "1.32.0": {"name": "mime-db", "version": "1.32.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.32.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "485b3848b01a3cda5f968b4882c0771e58e09414", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.32.0.tgz", "integrity": "sha512-+ZWo/xZN40Tt6S+HyakUxnSOgff+JEdaneLWIm0Z6LmpCn5DMcZntLyUY5c/rTDog28LhXLKOUZKoTxTCAdBVw==", "signatures": [{"sig": "MEQCIA1nLWjdGoHq+SouzUgYTbkrWRrXnnHDemrYlSvzpeJDAiBOidWsG6j1fXnZ3pJH91o6/bnJYvZohafJ0AlnfSXKPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "555f55537d688a6ba935253d8d36bf270a4a0ffa", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Media Type Database", "directories": {}, "_nodeVersion": "6.12.0", "devDependencies": {"co": "4.6.0", "nyc": "11.3.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.19.0", "bluebird": "3.5.1", "raw-body": "2.3.2", "csv-parse": "1.3.1", "stream-to-array": "2.3.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db-1.32.0.tgz_1511989647467_0.298956407699734", "host": "s3://npm-registry-packages"}}, "1.33.0": {"name": "mime-db", "version": "1.33.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.33.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "a3492050a5cb9b63450541e39d9788d2272783db", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.33.0.tgz", "fileCount": 6, "integrity": "sha512-BHJ/EKruNIqJf/QahvxwQZXKygOQ256myeN/Ew+THcAa5q+PjyTTMMeNQC4DZw5AwfvelsUrA6B67NKMqXDbzQ==", "signatures": [{"sig": "MEUCIQDx6TzrTcdStJzgy2fSEfmMqNiqgEGm36qMZm88GshaIAIgPkXE5H413/ScoiZ4RSQ5LSEsZx2KeODskI5H4HprDY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168885}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "e7c849b1c70ff745a4ae456a0cd5e6be8b05c2fb", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Media Type Database", "directories": {}, "_nodeVersion": "6.13.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "11.4.1", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "3.19.0", "bluebird": "3.5.1", "raw-body": "2.3.2", "csv-parse": "1.3.1", "stream-to-array": "2.3.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.33.0_1518757820140_0.8293249007794938", "host": "s3://npm-registry-packages"}}, "1.34.0": {"name": "mime-db", "version": "1.34.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.34.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "452d0ecff5c30346a6dc1e64b1eaee0d3719ff9a", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.34.0.tgz", "fileCount": 6, "integrity": "sha512-0zfriqD7aQIU3rbKGiUkMoWS+7ar3+4ukIGcz6Y4suPuYk4RqALKXj6bnQkyV6eDxr4lgSjwVPl/s5prj91Efw==", "signatures": [{"sig": "MEUCIFWN/5frkgEknhkUNfSAL5hiTWciiNzcnoZeG3cVqnSgAiEAuZfgFaOtRGx343t6NA6yqY78SLlmn5M64E0hIsLFAHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFHwhCRA9TVsSAnZWagAAj+UP/1dByzwBfD1snnnxY1NR\nmeJz6+pNaEBsfJCS9ui+75FCRvuSdn5cnoUZcaN+R+Q4AC0GuOFVJDqKA391\nXtLHiWdWi00I6CJy9BWJGGNzkXI80yxuxog/aSbPEG+1b7eGF2KmNW7TO3J8\nqmWmXhHNw+AQJrsbYpEnyqp9+sGsTqcsVOZqtvgBtdWFNBP3P7dCghMt9nKD\ngs/0GBOLA6iYrnZEh7UDxedZId7Eulhv2x3Nvw3UvUV6whawLm9ZyR6/pE29\neBFe/vaoF4ITrDMJfSreFUKG9agPBZfG3QufkXKte0DZ7irHuk3Mxsn4HiXk\nUhxyDeHBgQI31zw5cuhLfcHgumzRBMDy6t0qQ2APUBHk4HLzLaTo6t4Ekv7g\nfRDbVuIXTqSfT9ABiF9LEq/UuJqaHOsAOTpJMKtq5x7hrw65KPt+mowdrGn9\nbCDjyDs0YIKFeNI66MvzUO5frl3NJnb99WuuOajJHh/jsWuEc9sZLGxttsm2\nrST+Aoj7eCLsIXFgbNJ/nz+ipvpufgaohc6wEbks7qj/0pWq9IROKhvaBnEB\nAsNnRhE64qnKucZKfHCemhPDZ7bn/h/rukTl/s+uJ8xKtDKp31cNjaORspRY\nGlfwJUk5Iv+HFm085YKoxYhAQC94k/TXnQ9Uw6HJtqmTrkvWUJOW28Qa3pDE\n2QLX\r\n=VWrj\r\n-----END PGP SIGNATURE-----\r\n"}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "_shasum": "452d0ecff5c30346a6dc1e64b1eaee0d3719ff9a", "engines": {"node": ">= 0.6"}, "gitHead": "424fb61ca34d480d3f25dd945acc44f37c360f56", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Media Type Database", "directories": {}, "_nodeVersion": "6.14.2", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "11.8.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "4.19.1", "bluebird": "3.5.1", "raw-body": "2.3.3", "csv-parse": "2.4.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.11.0", "eslint-plugin-promise": "3.7.0", "eslint-config-standard": "11.0.0", "eslint-plugin-standard": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.34.0_1528069151961_0.2070403796654765", "host": "s3://npm-registry-packages"}}, "1.35.0": {"name": "mime-db", "version": "1.35.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.35.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "0569d657466491283709663ad379a99b90d9ab47", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.35.0.tgz", "fileCount": 6, "integrity": "sha512-JWT/IcCTsB0Io3AhWUMjRqucrHSPsSf2xKLaRldJVULioggvkJvggZ3VXNNSRkCddE6D+BUI4HEIZIA2OjwIvg==", "signatures": [{"sig": "MEUCIQCRNaSBMzlH0lnbLWakRP5RFLm84s0TPJku7m1IqgrAuAIgenPaz9NqvQuAv0rB9CjpGYEbq2XMZ8Am1KMKk32vVTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbS230CRA9TVsSAnZWagAAVAQP/1JXucIXemrOFc45tlD2\nve3Q+DauA5xCvgZb3Aqk6Z2ayRSYVvQEi6TWhZ8xvTGDc5Lma5X7RFBR8YWI\n7fg5s8HzT91e9rgPoCbZiiPG28k0BNgI5gPutN3zyYrzy5oAACf3dJmHKbPa\nk4HLl6cd7zVLtLBIhrt+9gLnztjVPB4IWWGj7Df5I4xtYsSVgR7SERqkgw9Y\nuLOUCkGAERF1A/+5SDys7LucVspERlExQh2RNfz3iQLdj3LHq5LyfeqoL/7p\nVOFYaLMS07Zv8yh5YTqf6AHVKTbNy1/i7f7wv3wmnOUj8OMcV7RIQI7AMmhC\n0XWS3vbJWDiRm2VmoO2Xv10sZT7wFEr3getuffzMFxhOmHlnD3bvul64YWo/\nWR+ZUmYBll0obu/hUKfnimJ4/lRJKY4R7oUzR7prUL5ZQwD2x6xjWG3fNbaX\nPQXkKUPvhhyxvKbqpQmv23uF0XSaELAf2PKfo+MhCcQPwWLaXG7sylIbN3xk\n+Yn8CAgv2Vdby+OR7VL8pTxOIizjUPBntZlvtmLo3MId64NJZeALO9aGu3AO\nxqaQmm81SAvi/3V/dvPSxtDJyL14L99nxsPGUm4CXF7O8BoS/mEpen3hGPYg\nk+dEZHsK1Cok35XDboPWF9paoSI56D4WnNHWJRc6vaenR1uiZXqAMmP6BHET\nTjhI\r\n=Df31\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "482cd6a25bbd6177de04a686d0e2a0c2465bf445", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Media Type Database", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "11.8.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "4.19.1", "bluebird": "3.5.1", "raw-body": "2.3.3", "csv-parse": "2.5.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.13.0", "eslint-plugin-promise": "3.8.0", "eslint-config-standard": "11.0.0", "eslint-plugin-standard": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.35.0_1531670004114_0.27833287879365853", "host": "s3://npm-registry-packages"}}, "1.36.0": {"name": "mime-db", "version": "1.36.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.36.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "5020478db3c7fe93aad7bbcc4dcf869c43363397", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.36.0.tgz", "fileCount": 6, "integrity": "sha512-L+xvyD9MkoYMXb1jAmzI/lWYAxAMCPvIBSWur0PZ5nOf5euahRLVqH//FKW9mWp2lkqUgYiXPgkzfMUFi4zVDw==", "signatures": [{"sig": "MEYCIQDBghXK0J2c10Y/Y61+wElA9mdqKitXXV64Ot+Kpgpa6QIhANQILk5UrGiz5vdn+7rW8Of8MvGFqByzp0epQMluad/R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183662, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbet1WCRA9TVsSAnZWagAAJN0QAKPc2EF8RzEB4aqN05Q7\nSTdLSmPgU72pGFXE3mP7qRNd0qXdawCZfRIirnbVkC9gIEAVf0E41GiIcMIu\nTH/G5lndkLj3DIvxlXAWgXl/EoVJrxe05aWK0gzJupECXWipBpK2WLqit5yb\noxBYDs00nnoMvdbp/mNinl+YbptjJKWaLH+f/MldtoUTkMQvPBhkCP0IZoTM\nLKD2JQJYZYRDUyFU0cN1OmdOKBKFf7noYJ8TukZnFOPQSbiW3bdOXYi8AZO9\nrmZ7aeTFwPz1+YD/PtljE1DpnLI4ky+PP1oonoPh2KRwK2+pNitp9hpsltzm\nbgt9rLLaecngzqKoKGggaE0q2Y0NAwS2MohGMqMyEvUtEuH86wo1Fcaf694F\nEAM0sMRJ0gPHepdkTrX/80W3QF+n3gxE93jxN5dpBbyfmmJXfE5S6V+EhkrP\nzH5gYtR6NIT4cyuvl7RO2oaTDYiWq1+j1t00f/a0a2+tmH2A0fJ5H11Dbq1F\nm/N8Ynh185zGIbrhwFTTpaBQDybzPcngQFX++Ex8NJw9d40GiyURfsPxtuVR\n/uBN9Qb09ueHuQRl1aXlIGroLxxgkPpUzJeR1cVhSrExqV/0LIyM1muKApUa\nPzXRPdn9s+zXdBqYrzPJzsqoaHfliyVEsuSoMWqZJYP/en9LqeShK9PtOU8T\nEaXI\r\n=Vqm0\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "dd02f32bbd23cc2c081888754a5c1b688c6e9ebb", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Media Type Database", "directories": {}, "_nodeVersion": "8.11.4", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "11.8.0", "gnode": "0.1.2", "mocha": "1.21.5", "cogent": "1.0.1", "eslint": "4.19.1", "bluebird": "3.5.1", "raw-body": "2.3.3", "csv-parse": "2.5.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.13.0", "eslint-plugin-promise": "3.8.0", "eslint-config-standard": "11.0.0", "eslint-plugin-standard": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.36.0_1534778710007_0.7707137096744558", "host": "s3://npm-registry-packages"}}, "1.37.0": {"name": "mime-db", "version": "1.37.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.37.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "0b6a0ce6fdbe9576e25f1f2d2fde8830dc0ad0d8", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.37.0.tgz", "fileCount": 6, "integrity": "sha512-R3C4db6bgQhlIhPU48fUtdVmKnflq+hRdad7IyKhtFj06VPNVdk2RhiYL3UjQIlso8L+YxAtFkobT0VK+S/ybg==", "signatures": [{"sig": "MEUCIGXbp/w7rhEY/EpXx6toY0u9T/NaA4PM982EWrCuhfa4AiEA1EXAT+q32+aHSL8cq1mQ64PabPOob/JPzD7oAydPk1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyodWCRA9TVsSAnZWagAAWJQQAJWun8rA9X0PAqWORTCM\nM4zVTulxxINQn+3eWvWrmi/uxAu6EIhygcrFvq2VBI65yqkn7p+YJKA6dj55\nQNuFsuviaejJ8TunSUNqD2YNqv6mFtnm/bn0akiVrsL1bbM2ExoYvx9cO9/9\nLT1YOdKI2xVxYjI8bE0kaWBFkxUn6QPJ7wksKgukQesmyNb+c6Uv4Snvu/c1\ninT6WfSNH5jSJ/A0yf03OruzJqToVjfuhBFJOBSOFDfQWbbxS3vDzgZn2X1S\n9ey6Ewkt1oyWoo9mh9n9pjY5rhukP56WrfXxi8vI0Xb9AqPwik1e5aNxd6HK\noNW2T1zr5GjwMsQFERGB9nbu2ql0Qu9QvbEdn9sy2eKB/xBaFKuDIlwoaqMI\nmCgnbIVOf6ZK3VaaxEnYEuUYx/ADHYuP9/eaH4eP1UPrvhwhKlC4IvA1u9Qw\nADPDLOiIdgt64njqLDOfiO7LGAiDfhJyyW3y7kBjPsAmuf+LZHQu/OagRvfm\n8sLjDRHYW5TKKSthNWnAFL2TgxQJIrRg0GwZZTT9Mugpkyp46H+CsM5SGwOP\n9r1HFhKROluA5PyXh8vtqQXgFxA4jcyZu26xbFNoQwbqUi8tjbvxDkFMytmS\nwIlkx+VCvuVkAt45xGDfG3fbSg29YKN9YG2bHrgh1gLjHCtueCYIYDSrmmlb\neu6F\r\n=EUHL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "a9dd53b6b9f60107d81b29d6dd31fbc08c5b9782", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Media Type Database", "directories": {}, "_nodeVersion": "8.12.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "13.1.0", "gnode": "0.1.2", "mocha": "5.2.0", "cogent": "1.0.1", "eslint": "5.7.0", "bluebird": "3.5.2", "raw-body": "2.3.3", "csv-parse": "2.5.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.37.0_1539999574087_0.7412279641429007", "host": "s3://npm-registry-packages"}}, "1.38.0": {"name": "mime-db", "version": "1.38.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.38.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "1a2aab16da9eb167b49c6e4df2d9c68d63d8e2ad", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.38.0.tgz", "fileCount": 6, "integrity": "sha512-bqVioMFFzc2awcdJZIzR3HjZFX20QhilVS7hytkKrv7xFAn8bM1gzc/FOX2awLISvWe0PV8ptFKcon+wZ5qYkg==", "signatures": [{"sig": "MEUCIDm4kyeVehxyJ+xxh0ijLJr8Y8HhweCosirr02SG61itAiEAmbY6AmbDYTZ3KjDvATO/BWdAqyjHkufU6ZPn5kp2NOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWQEBCRA9TVsSAnZWagAAXSMP/jBFEA8x96QJ4p1cuvDi\nSObsmuQxGC2HcOHu8P2FuyCjCG4cWgjQT2ANKdGG8nOAvpZbU9D23pw6zj4y\nx2zrUi5rSU924LCXhfXx/YlOevYlSbV4E/eU0ZsvuCDbR3KuRsOi+8U/gFIO\nKgnJIygONbg+czoK5CKofBn4yI/0z9AfHPl1ZxiMLPQHpziAMaW2N221mHia\nIw1WJ3WttXvjoemrduN0fwfuB11plAgo9p1W8H1vSLmGFN8M6pbvMRLd2H5N\nGUy1F3sucis6+48Bcbcr9W7W/OeMh9UfbGfvzptV1MYjqNkXb/vYJzGyMBeR\nGwOkjMJW/JE7WpwMzRpzUDeBazBnqw1boRHhZlFTRaIfsz0USnetkuKgnBTp\n+z8/sepMrlIIHtWv5E4swSEJ3tR0RSdFLn3MYQUrqARKg9xLhTf2Dt28ES2m\nNy5USUFglIWbeh0p6CrbTHrUH06jsO43kIJo5j+YbfrV/q+hgs01OXQxGsS3\nI2Aqz9ZIrGZHd/qHCXFjyLO2SW6Qhk4/+JscF3vSK/axmo1rbD3vmN2BwjNi\nodiMNaGJwXyh+OYJpPZoBh+VARcOoD6w+lWrbmX1FdAxpW0s48bmB6OK/SVe\nCvix+hsokZTN6/saFxZ4xcEkUVHLxlapRWIbfaxP/6Zxm3etlSiQY0XyGrA2\nkzzo\r\n=Obtw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "73802502feea4d2ec25f8a5f1a8d4b1d64a8ed69", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Media Type Database", "directories": {}, "_nodeVersion": "8.15.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "13.2.0", "gnode": "0.1.2", "mocha": "5.2.0", "cogent": "1.0.1", "eslint": "5.13.0", "bluebird": "3.5.3", "raw-body": "2.3.3", "csv-parse": "3.2.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.38.0_1549336832525_0.9039233421831574", "host": "s3://npm-registry-packages"}}, "1.39.0": {"name": "mime-db", "version": "1.39.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.39.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "f95a20275742f7d2ad0429acfe40f4233543780e", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.39.0.tgz", "fileCount": 6, "integrity": "sha512-DTsrw/iWVvwHH+9Otxccdyy0Tgiil6TWK/xhfARJZF/QFhwOgZgOIvA2/VIGpM8U7Q8z5nDmdDWC6tuVMJNibw==", "signatures": [{"sig": "MEUCIHjFPboOROq9aaRDODMwKsfLRxyRkzltFsXfVWeFPjVpAiEA7fIHBCLEdOtyQVePfzTzSzSh8RiATt1rze2WYg5sdmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcprirCRA9TVsSAnZWagAAiV4QAJo09LTUiZBT9ela3OSr\nk5RB9/Z3QQnbOdFuoqQe8EtgP0jUli6/JY6QEc4HD38IuItqe5HuCBH5m0nn\n847UJF4bAHGHcIpVrUClsu3nTh8OPATmXoYqUxKianVE7aJ8YCBPjPQN6JHJ\nIFT/XI3eshE0qrCJmWlhNG54cHr8LDVHVnuG8fG/l08lXKq9cljj93G5Tq32\nscrNgUqcRfE9Csq+Er678c+N+V+FXgr5X1AOEqcytLz/zmSP2cH12eL+6ndJ\nzx5sM5FG9Bjrdib6RyixvuKBX41TsTGkVTWkMw3hADfuckT1j00WOsAqM9yh\np8AWE9uxZl1pJ438m7W3HMRK+aVVcwbjj1rSIKIBj69/P2vIe5+PSS7qaOkD\nLpW5eYFEiVLrxFMOPccpUbbKaB+hkF/5eqxo0eFib+g+xLkYQei3jf6n+iVg\nZQITvj4UXv1/kPxPfmHrK8HRHN7irak5sRFqdgyCqM4uOrbW5451TJF6sIM3\nfpYK910N7tU6wVOpOZv65wc0s/Z4n2p+evXjWyaY0DzOmbyA6Y6u6daWzsfD\nUv0j/P/xFfAoYuNXJo6vXAIQjlMCbpp1uuVrKu8xSLtQolY8OtQX3/I5Iuuy\nRdjEhacp2dCJKbWmc9WGUjG450OsD22CIe9yHxeswugo7oNBOLtPaDbARS66\nz2xU\r\n=B2vL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "8beb4223f9c8d0e36f438747359f0f6465cfd0b7", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Media Type Database", "directories": {}, "_nodeVersion": "8.15.1", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "13.3.0", "gnode": "0.1.2", "mocha": "6.0.2", "cogent": "1.0.1", "eslint": "5.16.0", "bluebird": "3.5.4", "raw-body": "2.3.3", "csv-parse": "3.2.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.39.0_1554430122853_0.8714978089749246", "host": "s3://npm-registry-packages"}}, "1.40.0": {"name": "mime-db", "version": "1.40.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.40.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "a65057e998db090f732a68f6c276d387d4126c32", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.40.0.tgz", "fileCount": 6, "integrity": "sha512-jYdeOMPy9vnxEqFRRo6ZvTZ8d9oPb+k18PKoYNYUe2stVEBPPwsln/qWzdbmaIvnhZ9v2P+CuecK+fpUfsV2mA==", "signatures": [{"sig": "MEYCIQDqtYsYuCxa7jpDyi3oQzpSeNS3SfUOkiOJkPjOKd190AIhAMxnNKuUdBT8lEo4SwPZ+T3BMGFg2nAyw6g6WTZk4dMz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcu85CCRA9TVsSAnZWagAAkIgP/0HkS+apHdnFdPS9Ot0O\nZlWuptzHDU6koytz+3esAgaFYYOWQa6xN4OQbQs61B2XoKNZWxKQdkMD/+zf\nLQvx9wWDic7Xaj1MpijO5Ur0OEzJEh9ZW3S3eWHc5gDKPTcyofY+l3mBGo6R\n2/scHJr3l3gBK3BJyQZYhxL7EEEt5RGeQkcHAfGvfKH6iw4UzRWSHlIJxpJq\ngdTg2cmJgtGy0ThBnuLDk/QG3DG2lZL0NnI3Qtwt5PhLqlQ1mEeMx+fBHGEc\nyHH9fWaJUh/cm/a0kzvoGpu259QMOiLwuZfSkBV3v7udap69Q1MdqnViU+HL\nXaDfT28J/kSsqrddkwIisNw2Zxi/3bGcB3LcZHuoWhkaYMf2rzweX/rMApI7\nOijY5ze0i7PhCbsGoy7ichT8PKbgyDdc415t4M8CubPkfG6RA65dpR+NDjVn\n88h9g0S98pEVa/yMEZOIXRPPE4MHcWJ1QRlwjl4mxyH461AakWMTeNbAKk6Y\n4ZSDzTVPeVsz/1uaSHryagS/istatuoRit3PBJWsMI3F8gEA+aEHYDFimBCH\n8gpn4j5xfkdiwtNGuvJO22PxBqCHrDCppoxJyUEzFHlbWFiLvtwWy7h+FtYs\nNAKtGQLkQjET+DvyC15QSwJainaZ1ZzDVNlY7CprPoSihm3eO8G1rBI9DVbC\nSKXu\r\n=T/db\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "4db92114124a76a38bf29b7d572d4c7da33a1261", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Media Type Database", "directories": {}, "_nodeVersion": "8.15.1", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "14.0.0", "gnode": "0.1.2", "mocha": "6.1.4", "cogent": "1.0.1", "eslint": "5.16.0", "bluebird": "3.5.4", "raw-body": "2.3.3", "csv-parse": "4.3.4", "stream-to-array": "2.3.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.40.0_1555811905445_0.9439157872850141", "host": "s3://npm-registry-packages"}}, "1.41.0": {"name": "mime-db", "version": "1.41.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.41.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "9110408e1f6aa1b34aef51f2c9df3caddf46b6a0", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.41.0.tgz", "fileCount": 6, "integrity": "sha512-B5gxBI+2K431XW8C2rcc/lhppbuji67nf9v39eH8pkWoZDxnAL0PxdpH32KYRScniF8qDHBDlI+ipgg5WrCUYw==", "signatures": [{"sig": "MEYCIQDyIihiusvxL9y/Up/zCjY+mZhxR8MyI+oZWVYHdETLOQIhALHRNAfwFSVYU4IIxXN/4BgDS7Wb583rGIPpNlpSeiHo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaUD6CRA9TVsSAnZWagAAmB0P/A46VreYFYe0+F7nrF+r\n0z6ci/kbfLdJavLdG11rt0aJo3H1PDMyHNRePpOZ1wfPFmn/AKc5ZMCyl+Uw\naIPB0srxsKaxbEzci0JKVi7Wvnd+Y5IYLzxvSzggWq4FbBUW0hXzkb7Leh5n\nk7PUm95gnd7a9z3hVoeyz1mHybAlrQinvHSTg2s8NrYTWiuD09+AL/C6gu48\naptFVLomkY8tOP6H7LUoMc544vNN0/9PNnu+NXl5M/2YAf3qCLc0Ph23HaJl\nZCzaKKhYwxPOO29k0IQegwlkuymhrhyJIFaFA4i7zysuj6ucbO3i6aiEFdWu\nFDMxrl/hUOQ2bqd5wWDfiTS9prEaIkWoTHWQyVgoaYaGzoy+PXykiZ+RAYeA\nUJAWJhz/6ROpFe7GjWLAUP/a/np8UJC8x2gvMOwnU8PxWRYuI5ejAyChlYX0\nxvP8phbVV+vW5wcttejsg++uxJqxDDVGOUXzWKJMNYi5hJCHI2GwXt1IU2mk\nJTYQ1a73L2dqF//IGcSiXryHYDQa9pnOv2Eoey68YWvCQEGU2ceGkWjfHS9a\nUW+ktWm8fiFxImqoI5RckiwLjvX8BlSIa6Ofa3r4zcCabhgQam6MVmnve7oc\nBrq2Ne9ecCjDCVHlvN7vLqydqgQvXEttz0WrJKmn0sw/vjHeOqZsuntnq+g/\noeiJ\r\n=QJJZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "4b28620277a4d8d9cfbdc9a74cf6304a8495e464", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Media Type Database", "directories": {}, "_nodeVersion": "12.9.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "14.1.1", "gnode": "0.1.2", "mocha": "6.2.0", "cogent": "1.0.1", "eslint": "6.2.2", "bluebird": "3.5.5", "raw-body": "2.4.1", "csv-parse": "4.4.5", "stream-to-array": "2.3.0", "eslint-plugin-node": "9.2.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.0", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.41.0_1567179001393_0.5466457984699724", "host": "s3://npm-registry-packages"}}, "1.42.0": {"name": "mime-db", "version": "1.42.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.42.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "3e252907b4c7adb906597b4b65636272cf9e7bac", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.42.0.tgz", "fileCount": 6, "integrity": "sha512-UbfJCR4UAVRNgMpfImz05smAXK7+c+ZntjaA26ANtkXLlOe947Aag5zdIcKQULAiF9Cq4WxBi9jUs5zkA84bYQ==", "signatures": [{"sig": "MEYCIQD+yicxFSTYQ5YN6DlkO4vDAuu4REY8FGwiFQqfsYd8UQIhAPcZha9SyRnmJDQjRmS79OLI+7QQWxiiYSy9BN6EPikf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjDEMCRA9TVsSAnZWagAAO8sQAJDZ9nE5qeU6iBfZSjAg\niuf/pScGh+pnoLUIU0h+4eT0TmPDA/isO1ZFmB7Gh9yd5p+zOTJzXFhCRWgl\nR7q6+E4Bw7AXkkyZVWQS50kIJWcWFNGsahQMWseg9t7i27O3MEuVScUcHKcl\ntsz3xIYhbc+xtpjLOGIYhHB+1QUSS3pUsw8uNGHRuhtjeBbrM86rwbbR0Nf8\nmPrBa27ouELVuKeKhjLVvRU63uut4f4jJnPoEi7WWhQkRVvsCv4hZa/sHRsa\nF5qqsipqzln3ZsL1LTxf6wb2O3eL9CYGFVLWk2mD8Zswuoy+LKF2lMsgw38b\ngWbt1U9E9xm4LjdTQP7Ic3XnOF8V/0L14galBmgOiS/VqWOH6d0vq1b5d0kn\nu5lythVxfmZsLce6oDZsf9sROKcaX4hXEp8p24wThdnSg6edX3djV4ULUiWD\nll6z6znZKbTUIs2564mqtJ6Ag/Dxqixz1gq+H3EhOeJcw1RbrBsydau8ZyQ5\nIEUe68vShyOWxgrc5ytedgEFEAG/jHkCZ6EkHraRhRE8MOyO7TCrXpWU5p6d\nE594XDx+L9+sGjCAOjm0lCG5A+72fsz2PAabu9fV2bXyVK4oDbHxXpIYVLHv\nV/kFUqETaeHyAAQCJGIIcvHrVfLZUwsTWjfa055ZOQggmsIcjNwmdjCpL8j0\nFqB0\r\n=CvIR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "102102ca893db1805b832d05c774ce5f883871cf", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Media Type Database", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "14.1.1", "gnode": "0.1.2", "mocha": "6.2.0", "cogent": "1.0.1", "eslint": "6.4.0", "bluebird": "3.5.5", "raw-body": "2.4.1", "csv-parse": "4.4.6", "stream-to-array": "2.3.0", "eslint-plugin-node": "10.0.0", "eslint-plugin-import": "2.18.2", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.0", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.42.0_1569468683752_0.9251934830996231", "host": "s3://npm-registry-packages"}}, "1.43.0": {"name": "mime-db", "version": "1.43.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.43.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "0a12e0502650e473d735535050e7c8f4eb4fae58", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.43.0.tgz", "fileCount": 6, "integrity": "sha512-+5dsGEEovYbT8UY9yD7eE4XTc4UwJ1jBYlgaQQF38ENsKR3wj/8q8RFZrF9WIZpB2V1ArTVFUva8sAul1NzRzQ==", "signatures": [{"sig": "MEQCICERyiwAQMwv0524NYvnzfZ3A5LnPC3LmECBafL4ai6YAiBB5og3RPG8UvKgeYg0QpoNz0Y+eOT5yIXCOxO3ReGz2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEqh2CRA9TVsSAnZWagAAlP4P/icsl8MtJ13N7Wcf/kdG\nfKk/9TcVOso14t3HgqQHiNAHa5GjNzr8Et5Cl0Uqpy2d/eNiw5WWjn8ZuhP8\nEFoy3NBGOb0hfKhi/LzqjJhiBiKbszKSRBBboPauranRazn+sJdPrJA2OP6x\ny+iseFgXEo4k8XH4A11dMzMS4kUw6YdWUt8GCFv6zkML4ku/zszg0rFzbbf4\nGMvh+7n/UpSg+fVNTPwj2vOJJpP70TS+gat4VG2nbVyP0thHXoM7/xmjrmuB\nCpZ83ZXCI+hZnDIUk2qf0kc04zwoqHKeloIcBXNiaYEtyu5joTwbGbu1q6z8\nhmjWZ7xUMpzyVP081RHUjRXvIJHvIvv1Gzpa33cfFiDkXewUqwaa6sO/Q6TK\n5YKmoNZo12OyRZfche8Ni4Y1eQBd58mx5QvYGxlYhtAjQ+QczTlyTjdSMh/k\nYbrbH81oXlp/uyxLKv4ywye54R/YJiFsRNgiiDOarXQtRdSRI8mGt46XCs98\nCXFDkGmoDY+HXshy2vboRTWXjORDdcaGN8olI96lf+nOJsLc2YQ89ZK3zaAF\nBmfLA+hE3WO0UcOlTjKZnHZeP7e0sosQKVlPO4XjLJy23yOiWZpvTurjk/SE\nQXNdDa1LU7xa/9k1xuKTLFoQvkyFcdtI2p0KiUw2okA/vzWu3sF4wkjBWwk2\nO84c\r\n=Zap5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "c28146bfd5a10f3bc1da482e1751e7782817a60b", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Media Type Database", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.0.0", "gnode": "0.1.2", "mocha": "7.0.0", "cogent": "1.0.1", "eslint": "6.8.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.8.3", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.0.0", "eslint-plugin-import": "2.19.1", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.0", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.43.0_1578281077812_0.41242011371667253", "host": "s3://npm-registry-packages"}}, "1.44.0": {"name": "mime-db", "version": "1.44.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.44.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "fa11c5eb0aca1334b4233cb4d52f10c5a6272f92", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.44.0.tgz", "fileCount": 6, "integrity": "sha512-/NOTfLrsPBVeH7YtFPgsVWveuL+4SjjYxaQ1xtM1KMFj7HdxlBlxeyNLzhyJVx7r4rZGJAZ/6lkKCitSc/Nmpg==", "signatures": [{"sig": "MEUCIDS6tZwN6z7IdBVMMTFeXU9XxZchPJ/0fpY1yW/2PkqEAiEA65/cgm7E0TWs2xwHZmw/+QtPy119cq2EBSqvXCc85Fg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoOd9CRA9TVsSAnZWagAAi1UP+wT1oocXAMiyO/9Vfm8L\nGoCBNBY4pOg5WMITVUQ+rkz5JBEQkjue0Y534t9pQhfPrFMidLOE/j1qVAbI\nOBtEjo3gW/OGaCVQpbt1ZhlHlPl/ybxiuGT1S4TdebJPGOzrsbYZlR2ki30B\nGPSKeQ7Ept9nELXXOlOp1HfFKayq+buEbVXMsPgr0l7uYZc4VlyuD38RLbn9\nSoOzu7i/ecXmzh2tq7gL8ZL92vHl0gikbpwKJmRlP/QH29elLxTPWaDUxkGO\n1/pbnuHLC43IgjTDcWebAG5T0i8U/OwihDnZQRdcyRgcCm9VdZdzj6yzkOtJ\nUq3aLoqjNynaEgiJuuq/aMkfVh3gLf0K/iQhGMx1sBJ1AODo5YvxtPS+aHhE\nmm1m44im2QXoYNAj3ZtE10RTUoKiGzvuG1VSBkEmdqMVi1sO3HkhU5a2vT04\nzYUNpKen3e8+faMWYvssERsw6rszhqNngDDb+qgXDRRGhgXJDFlu1kaP8p2F\n8vHfHWZW10PP2pNJgILfodh9dzn9rjMdflQ+iLQu1DReGn5ympj/onk/UI4R\nwRU6UlAETbgVwRA8xAZ5eHsvGzujmqsuKo+vSRVdBHsquwVW1zzRuw9LYMDV\nuBdl63DxqOcebHcEbclLu6FughDEa6fizcxes0+07BlVvXq79zclvwOIPEcd\nBe2P\r\n=hgzf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "661ef0cf02b2800c5c5c6dc273c0a0b9eb3c410b", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Media Type Database", "directories": {}, "_nodeVersion": "13.12.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.0.1", "gnode": "0.1.2", "mocha": "7.1.1", "cogent": "1.0.1", "eslint": "6.8.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.8.9", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.20.2", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.44.0_1587603324599_0.4143458085398726", "host": "s3://npm-registry-packages"}}, "1.45.0": {"name": "mime-db", "version": "1.45.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.45.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "cceeda21ccd7c3a745eba2decd55d4b73e7879ea", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.45.0.tgz", "fileCount": 6, "integrity": "sha512-CkqLUxUk15hofLoLyljJSrukZi8mAtgd+yE5uO4tqRZsdsAJKv0O+rFMhVDRJgozy+yG6md5KwuXhD4ocIoP+w==", "signatures": [{"sig": "MEYCIQDxLf0oiZUsmnAjVlXl+xdQpqHSMQmfG9t8Urz44OlZyQIhANv8oeWwp8J3EyvNWrXPPvrUugdRCTLDW3vft657FzH7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfasVtCRA9TVsSAnZWagAAdVcQAKHttnfyGmHho2qwCF5I\n/sqyynFtaB/7bq7cMaIvDM8ft8UnXY56c+HzMFsI6VRW4TeecNzwB25FGUuK\nYQLY8YOM5f/GVkOioFvzFr/XGCMQxFZ1z0R1N2TPv3TNfuNZiXJjRxkA4iFq\nmZyaf07qjdBxAHR/q3SUbcNgEbfi81khj5AJrBEJ7ReVdzrCWzWJ7lmG/XI3\nys25XGk4+tpVW1kp6cjZvcFk3qxPsR4yeDsSbusyw6MoS8dmdUEXVzjCRK55\nU4/vV44coZ4/T+50Chnj8w+754WQhh9lx77OZ3T6DxZhJfKc1XgKi2w+koDL\nKvshref9pPcn8DiTJxOmYPo7AHHpOcqTWXVzAiP3MrOELfXj8twf5N8rT6fA\nrMjY3q9lw4U91XXZZtqt8H5UICzA34hTdGD1wUPMJVlw9Yv8sZpWjFR652wz\nzaLQ0GEDYqn5gxm0O6EtbW6gEhI0pXkmih6O/WqFTq0b7tVEX6tfoLhUz6lC\nLvk0o+ags5PV2vfoZYPD8vpzG0li8RI/mT469fZv3iRHMLOq3mtdkzqaYy8D\n9KF3fhz8upGAFvifjTyhEeTfbZ0GyTQFbdHNUjMpfD+bAREr+VoOjV3SEA0X\nLwa5S10kaeN8zhtHNtvBekMU32Uc/nPs0ny31ixIb30RhIxLuH7rLy28qoEK\n49UF\r\n=Y2o0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "258c9dcc14b689f53dd2891ccc2ee62a35fc5b22", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Media Type Database", "directories": {}, "_nodeVersion": "12.18.3", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.1.0", "gnode": "0.1.2", "mocha": "8.1.3", "cogent": "1.0.1", "eslint": "7.9.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.12.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.0", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.45.0_1600832876623_0.7047369538530679", "host": "s3://npm-registry-packages"}}, "1.46.0": {"name": "mime-db", "version": "1.46.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.46.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "6267748a7f799594de3cbc8cde91def349661cee", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.46.0.tgz", "fileCount": 6, "integrity": "sha512-svXaP8UQRZ5K7or+ZmfNhg2xX3yKDMUzqadsSqi4NCH/KomcH75MAMYAGVlvXn4+b/xOPhS3I2uHKRUzvjY7BQ==", "signatures": [{"sig": "MEYCIQDl8QvyJtz50FR0AXAohw8HyXyUu5rc6Gn2TwVUct6VkAIhAPLGYrDQkLR2JGQUNdwy3lakxb5eeEqd6v3QTepPPm/x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJ2WcCRA9TVsSAnZWagAAnKEP/jdvdKSNGsShyfp4Fq/M\nks39gjDNeVsTWvCJFUk/f6LGnWPn1JStPkH6+KqWUugIs5OYmcKp/VLNEwkn\nzXefmOAOEyV21z0hZTbdfi9fUWhuaybUXBMaWZPZPTVhjEW9JaaFNBtrxHU9\n7TZFY3lDeis2NCW5GNsNWhapqyJbHyFj7M3+OgBzgk9g8J/TLukwNnwBtuD9\nxTgeLJp6CsDhmBKZwJmszo/+AXN2DvCPA21fe9kuZsekbZUcBgAURWXjnJPW\nqR97Dz5QPGlem9QbmmO2cSmczxkxg+Ur5QhEJmc4j7KztdR+f0Xb9oxq7w2S\n2/6XDYfux8FPOnywBU/zeqVHmaF4mqn93X4iB1bPyzGYnda3AHQHecvK2+hX\nFxfi7xmF3eOkX9gbGK0DJGPIXPFtC6qRRGRsIK1++d1pBEE2TXwBX/vfzMqy\nqpIBxMGV6qIPq1S2bg5pGu5W+kpz7G7JRHw8OrccEW5FNQ2f++6bEt0KdTH5\nTx8C8FDVRTrU9yIgWBf9GPXED5IZebU+SgBzSNW9oUBSb7bcS2OeG2jzJWOx\n+hlGKOk5H/WIl0/Gu3xjEvOzeZG4COcrycvexM6vEeaPbmj/epb5BrfFML/B\nPMft5UTEZ3Yq/DYIe4nvhA5dSEsAv3/DfeOWkTKaUC3/m8eaq0iPD6hy6sod\n3e8w\r\n=Y19I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "93b1c9c90316484c682532384c493c682f4a459f", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Media Type Database", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.1.0", "gnode": "0.1.2", "mocha": "8.3.0", "cogent": "1.0.1", "eslint": "7.20.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.15.1", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-promise": "4.3.1", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.46.0_1613194651696_0.5250066104358107", "host": "s3://npm-registry-packages"}}, "1.47.0": {"name": "mime-db", "version": "1.47.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.47.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "8cb313e59965d3c05cfbf898915a267af46a335c", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.47.0.tgz", "fileCount": 6, "integrity": "sha512-QBmA/G2y+IfeS4oktet3qRZ+P5kPhCKRXxXnQEudYqUaEioAU1/Lq2us3D/t1Jfo4hE9REQPrbB7K5sOczJVIw==", "signatures": [{"sig": "MEUCIQCqzm1hcFeF9oWsqmI3pdLrrve3h5HMYBDTPqm2ryMbcwIgORXKpIlWi8qjebPaoF+iOPNwRCfUGCLUYOFzyzheSmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZiGACRA9TVsSAnZWagAAJVgP/2F8cQonhwUJiFOcN7/z\n5RPJWINnitLk7g2+CUS5pIcB3Sn5UepX5xWB48fGgR9w+NmxGQUMHIE4/8B1\nzmmDJtM82UOaHKujAME46h64t0WX9oVJkqhlqcMo5QsqeWUl8U0FAJuBjZGq\nBjNGFv2mRAxoHYqy/Y6QngaqAORH521TuASge8tvKPlWLMeyTRYsnY9kF0v4\nZlofOAOuAtPwoFId4kXgXT3tnqfGJ+XalRke0KeqzP2/gLoOvdmXSCsMfOE2\n3sJxRxn1huKemc0S8aOPB+Bqd9EDd/IRMFJqCqdTNsWwf1U+NW3dAbaroq+F\nDCt0yYENGYt2uGUQNeRf2NcmaLnQ8/pKnknNkfGQUjeo2HhAvQRqBhKZduzv\n05yZvl+ie1p5CWUbdeYxhWgOESV1MqVQq/znUA45Wky65z68ZZPyLV4zwBjf\nVSQvhD+9XKXOKlLNAIlihZlL7Uteg5VUFzE0YACdfPcDBkNhy5psJcKRzli/\npMnnqVtfzCSFNOBAknOPMrVbCM9iU5g0UTwlkA7nZS1ZLwWiRDOp9T0HSK0G\nC2xmSntJ1JPcX1ekYyDEPoGrcyudvBcZ50XHl4+3rj4WhLEoQvkj7r6OZmkM\naYv4f2QFtRqZCb919GzJcrO5BOAAB2rZMpW5zcjQNNhNpLw0J+wGNZTAYjCZ\nhabv\r\n=Zg40\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "214bc5f2b3ee41aad95534b723a04f5df6b45cdd", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Media Type Database", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.1.0", "gnode": "0.1.2", "mocha": "8.3.2", "cogent": "1.0.1", "eslint": "7.23.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.15.3", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-promise": "4.3.1", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "2.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.47.0_1617305983965_0.009470012054405563", "host": "s3://npm-registry-packages"}}, "1.48.0": {"name": "mime-db", "version": "1.48.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.48.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "e35b31045dd7eada3aaad537ed88a33afbef2d1d", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.48.0.tgz", "fileCount": 6, "integrity": "sha512-FM3QwxV+TnZYQ2aRqhlKBMHxk10lTbMt3bBkMAp54ddrNeVSfcQYOOKuGuy3Ddrm38I04If834fOUSq1yzslJQ==", "signatures": [{"sig": "MEUCIHVQtfoPmrQIAef5Su+7AYREaF0R5qz4xWimhCxcT/BBAiEAhDnOP+bbTlDA7r9BuOwczfIYXhxEvamWZTVnJRVfJ8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtF8GCRA9TVsSAnZWagAACHoP/3omXq2iVI0g2S6Z2h8c\n9xrlIjwn2ZK6t+quQL9YYNMcUuCL5GolMNbzjzEjjZIRjSBfZCVruW/wUrwD\nrfx1lluDfJA05HvWEmplUF8pEbcrn3YTLbXDmb/3FqiQ68qVua8pNM+EKNZC\nyOBqfrTKI1eJ0ufW7ad5j8jf5Nt5xBYdfXdP1zzmyy3YXXWMc0Bey7l8ium5\nthoCliqUYl8nyZ4ghMTqrFZ+gRPmC7afcl88K8mIvQxwyVUWyV/7QQdtQQy3\nBuq15MKHFXjMbN2q7F1KTHrniXhHu46vMH9K3HB2R8aae8gb9g0YaIsQJtdJ\n7VRQc/sxsZ3EMoe8lQ226EWWqMQ8MijsU6MHmZF1XYjKAMZYm18C9tHKqnsA\n00NBqe+XMawnNeNFZfO4gjRARDevKYhIBexv8D7X0Q1stR8wyVSI/v8z8KTe\nDPUF4HhYxQUogAoqj5dTBgb/zh0PN78tMePiEXxJgfQvKP1PN/qNSxRHLFoU\nt2D4VAP/olNoukHWlrTPMOJ7mREcddGEoqBzW+979D4LfqDqjiEVhr6s5X3/\nQHzHjD4/W59LEQjv25nnkWjg8N/1KFMkrKn5buJuLj0oPbLmee4l3gwjmh24\nDyma/nYYeJl86Lgb+h5sNmbAEbaCJXN1E+SjSz4ooZNHvk9HixOvcP17TI39\nxymk\r\n=mMeJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "ddb7fcc8b7c2e853484414ad317829ebb536fc07", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Media Type Database", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.1.0", "gnode": "0.1.2", "mocha": "8.4.0", "cogent": "1.0.1", "eslint": "7.27.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.15.4", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.23.4", "eslint-plugin-promise": "5.1.0", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.48.0_1622433542342_0.4935482577288639", "host": "s3://npm-registry-packages"}}, "1.49.0": {"name": "mime-db", "version": "1.49.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.49.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "f3dfde60c99e9cf3bc9701d687778f537001cbed", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.49.0.tgz", "fileCount": 6, "integrity": "sha512-CIc8j9URtOVApSFCQIF+VBkX1RwXp/oMMOrqdyXSBXq5RWNEsRfyj1kiRnQgmNXmHxPoFIxOroKA3zcU9P+nAA==", "signatures": [{"sig": "MEYCIQCeFBUvQTWtUxAqi0RN1AKVS9wnuHIp9U1sq6uPhJqChwIhAJ7MDdoHfkcCIaD+k4f5QAB3g0euNtbZoimjaJgVPKDX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/yqzCRA9TVsSAnZWagAAjeEP/15PebtlGAHhsOEFe54r\nnliC9YwQgrApueOF9lWLBVzMUsgtiz9+AI97cS6EUF9EN0EeV+b5ecLGkuEK\nBRWkN1Oc5HYxhiGiv+IH41HGyvJoGaY8EwbiYuNfrMZOINSDfrQqpejYSgCk\n/S47iekWdcPw0N+S95igTTyWGJGr2A2NNpIhjgDV02RwtagQgVYY0At12s5c\nxwH5dS26v6C2cpI04WVFpxt20L+hpPQZBIoWGbijRu7X0r2RRZVVRGh/nGjj\nKBsl3w65vqiL0cFR3cKoTEjmX6Wxf+D7HBu/CN/7EiXEYGJH8hzXDDj9mvjZ\nxkHAfM/mlV8PcJPDN54pn+l6Dn01XSoboZYJ892budmMgIZWefABeroYxhBA\nn2LBnacB0PzGT9RHY/kZT2Uja/T+Z3pYuJVjErOYMBBLUWdAlRsRxk0NbeV5\nDGCAETfubL/qqHhlQPipvHEpPnEVs9IXt6toPvLa/9lRwHi7RUdpGDANDAHd\n5PxAnaw1jFz4G5p3onvmtUvnrmFtTRoiZBYPR0tf/u6sl15bd42XMtl8VHYA\npQtHvlWxxjXepJl3qdbbz6N9a3miyCWFpdib0oTM29xh4RPtYXIq1IR/+upi\nlP3MHXVQG4xcz7bTe1aB3CLTzkfIneu85KHx6SQTLZQyq/NbST4xSSVM3jEC\ne3uJ\r\n=q1QY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "45c8941cf2324f24f7b761ff23d6576a29fddddb", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "Media Type Database", "directories": {}, "_nodeVersion": "16.1.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.1.0", "gnode": "0.1.2", "mocha": "9.0.3", "cogent": "1.0.1", "eslint": "7.31.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.16.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.23.4", "eslint-plugin-promise": "5.1.0", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.49.0_1627335347579_0.017607142820044608", "host": "s3://npm-registry-packages"}}, "1.50.0": {"name": "mime-db", "version": "1.50.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.50.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "abd4ac94e98d3c0e185016c67ab45d5fde40c11f", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.50.0.tgz", "fileCount": 6, "integrity": "sha512-9tMZCDlYHqeERXEHO9f/hKfNXhre5dK2eE/krIvUjZbS2KPcqGDfNShIWS1uW9XOTKQKqK6qbeOci18rbfW77A==", "signatures": [{"sig": "MEQCIGHoJ+ZKDIshfHtIAMu4telGYVLgxI0Y+cKssIwdrtZIAiBlDPt71FXfQZFKR6a7FR+B0nBgVmp8nCo+ipNFZc+d8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQp+/CRA9TVsSAnZWagAAxcMP/0/grJqYNePvt5GzPYVV\ngfdhfvsfaMJ6XDHhc35rPBjwrTa5fm5vK+lgW5A7SXbvVFsIxBVo11a7hw6G\nm7Ah7nROn30iBYvB4D0HTnXL/jtF1Dzu5ZOyoEsdgs30dpiAWCiVsrGjUIKm\nUoPQ/cVl5oIlpCYSfFvyXCVNDHi+i006TPNocs7ZcUArkMMWkrKwawTR10Gd\nK3vZHvJCILxqvQOo/JZRIkW5IUUOrNk99DDRODDMqcSykvSFbAP2LpyJVDGA\nc9+K8PoydF8dYr5nMEoZrg78YknoZfXQYPKj7JQpCON2ESgnrx6dw7Ub/T+5\numMYXYZorX95+FVaC6XztKMuJDWNSkl9eOQriBPB1XwymYP6oj1Du5T9HZaQ\nFjwaIecn1IeDee2++IWcInsyfWUi4g08Hjz8BtJy/SHyXXM0WObQbJmy2w9g\nYa+62Re/g7+FFOq8PaPWg5O/2j/Ta3wwIrBw9XVa51D62T/YL/mOQ2MNL14H\n4NiChEcEBtQ7bwWpzsFWFh026aqOgHPfeAdfeelLqEZ45p1ikTKp5lfJFtsB\nRzgKeWagd44hghggGzROZR8pUIig/OJ9ap05DCBKdEmwIXCMEEKoGXW9YDh5\nunSk360aQ7nJvzHfezmGaI9RQKsX0y+UIOY1O8DeG6LsoywBBrYQRLb9yGjl\npGSF\r\n=YlbI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "84cf675f3fffeeaced20bd35971e2058356c8f57", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Media Type Database", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.1.0", "gnode": "0.1.2", "mocha": "9.1.1", "cogent": "1.0.1", "eslint": "7.32.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.16.3", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.24.2", "eslint-plugin-promise": "5.1.0", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.50.0_1631756222986_0.2706268478967757", "host": "s3://npm-registry-packages"}}, "1.51.0": {"name": "mime-db", "version": "1.51.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.51.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "d9ff62451859b18342d960850dc3cfb77e63fb0c", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.51.0.tgz", "fileCount": 6, "integrity": "sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g==", "signatures": [{"sig": "MEYCIQDsK3wDqFc/kFq49iEzuqrn+EzMpl+XUKmhpBx9YjazpAIhAOTRc81V/ER0wVPXBUWluf8mJ5GUHvbfQa6H0/53mb5K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204019}, "engines": {"node": ">= 0.6"}, "gitHead": "562fb6b8a4a7da6c4882490c44441dcaf3e6cc31", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Media Type Database", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.1.0", "gnode": "0.1.2", "mocha": "9.1.3", "cogent": "1.0.1", "eslint": "7.32.0", "bluebird": "3.7.2", "raw-body": "2.4.1", "csv-parse": "4.16.3", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.2", "eslint-plugin-promise": "5.1.1", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.51.0_1636425895197_0.25054133941481327", "host": "s3://npm-registry-packages"}}, "1.52.0": {"name": "mime-db", "version": "1.52.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.52.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "bbabcdc02859f4987301c856e3387ce5ec43bf70", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "fileCount": 6, "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "signatures": [{"sig": "MEUCIQDI8oDhgyvviyzmyvYguHCT/wj0cDGLBxgXtenbbU4A1wIgD7Pbv+7q4QlFtBs0KeJQVtVCXDmLrrg2p5HQ7jfX9bg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 205539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE+r/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryQA//aGj6C9ZNStdRHpyTrK4bxJL16qcl2Hp3j4wvduMNUzRj0zlk\r\nhdvMYRgl3wRE8kMMaAUUxDnoYqnFK6qRVRHkYr33uhG3UBt499yW2/XNs5fB\r\npsI1vRlVafu4zuV+kwb7KBVYCPUO2H/VYT9eZvN5mx71YQqFXlp5pv0GijdG\r\nAIs7f5w84vmZhnd0dPj0XDrOjidb37vSUKTfJKm2efBIe3wGygfHkH1hsuYQ\r\nDAgvHg3LN7auRBNU7kYZgrQK6xf2QBa3s/ZZUg/6QphdVbzyPLXf29eeswCL\r\nBnxo0C3YAYdhDLwUB17z4VYY1Lu7L5uLCGVyi5srA9JrVdUvzcmUJQWN5dfw\r\nzfW3RRQqcnArxDkWGKZUcatM8Zkn6HhrvCt//oHcw1t1vWvX+IWzkrDAX+pT\r\n3gHclJCw4y+tl9JTmFfK4XiIbHLq6Jf39kdlGZji68iz396+Z5a/fdaA6+hw\r\nqS/KvZ5QIghkuCT08r9nkqt8WXNqZlqNAWPKNG6h4sSfPNg8rkD9Ok52mxtm\r\nkj7vX9WZmbHlJzCbBTqH/jp4ELAiNnLxDSs+aodBwB2+eDYYhlqYT7UfpkDQ\r\n36orkkhUzWb86JtCQFuyy3wM3wte4SLhB4dmo4Iw/kdVrB9M5/5COw7nFHJI\r\nZoxzqeIpttW7q7BdtngI20KZqraXPJYbDI8=\r\n=XP6F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "ebb6bf92ea39d3168a50942295d5edfdcdce641a", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && gnode scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Media Type Database", "directories": {}, "_nodeVersion": "16.13.1", "_hasShrinkwrap": false, "devDependencies": {"co": "4.6.0", "nyc": "15.1.0", "gnode": "0.1.2", "mocha": "9.2.1", "cogent": "1.0.1", "eslint": "7.32.0", "bluebird": "3.7.2", "raw-body": "2.5.0", "csv-parse": "4.16.3", "media-typer": "1.1.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.1.1", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.52.0_1645472510942_0.2866362639680471", "host": "s3://npm-registry-packages"}}, "1.53.0": {"name": "mime-db", "version": "1.53.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.53.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "3cb63cd820fc29896d9d4e8c32ab4fcd74ccb447", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.53.0.tgz", "fileCount": 6, "integrity": "sha512-oHlN/w+3MQ3rba9rqFr6V/ypF10LSkdwUysQL7GkXoTgIWeV+tcXGA852TBxH+gsh8UWoyhR1hKcoMJTuWflpg==", "signatures": [{"sig": "MEUCIQDW37ackGGTnZ6QRhv1MoGfPXqynQDsVBj0TLdQ5RgNIQIgSG9NR6i4osYh+ZoQwoiWHWGQLqtktvHP3IMuGb9maL4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219029}, "engines": {"node": ">= 0.6"}, "gitHead": "03458dc67eac6e8a5823a6a1663fffaf59577ecf", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && node scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Media Type Database", "directories": {}, "_nodeVersion": "22.2.0", "_hasShrinkwrap": false, "devDependencies": {"got": "11.8.6", "nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.32.0", "csv-parse": "4.16.3", "media-typer": "1.1.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.53.0_1720816500491_0.9021472348936239", "host": "s3://npm-registry-packages"}}, "1.54.0": {"name": "mime-db", "version": "1.54.0", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "license": "MIT", "_id": "mime-db@1.54.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/mime-db#readme", "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "dist": {"shasum": "cddb3ee4f9c64530dff640236661d42cb6a314f5", "tarball": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "fileCount": 6, "integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==", "signatures": [{"sig": "MEYCIQCQNJ57UlaKKgaBxzUo5WoRsITrTNeknrN05f96Z6VrCwIhANtVnf0QvkzIzRBhGoaP7MY007vjoouE5wcTJmBxirCB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225566}, "engines": {"node": ">= 0.6"}, "gitHead": "5207a32f76e77ed2f63421641449f8addeacb0a5", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks test/", "build": "node scripts/build", "fetch": "node scripts/fetch-apache && node scripts/fetch-iana && node scripts/fetch-nginx", "update": "npm run fetch && npm run build", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Media Type Database", "directories": {}, "_nodeVersion": "23.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.32.0", "undici": "7.1.0", "csv-parse": "4.16.3", "media-typer": "1.1.0", "stream-to-array": "2.3.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/mime-db_1.54.0_1742310404154_0.19473775997612774", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2014-08-14T22:17:35.560Z", "modified": "2025-05-14T14:56:01.096Z", "0.0.0": "2014-08-14T22:17:35.560Z", "1.0.0": "2014-08-30T11:33:45.622Z", "1.0.1": "2014-08-30T11:57:45.485Z", "1.0.2": "2014-09-25T07:47:47.870Z", "1.0.3": "2014-09-25T07:54:26.345Z", "1.1.0": "2014-09-28T21:54:48.591Z", "1.1.1": "2014-10-20T18:11:58.366Z", "1.1.2": "2014-10-24T05:57:39.626Z", "1.2.0": "2014-11-09T18:01:27.065Z", "1.3.0": "2014-12-08T04:14:15.910Z", "1.3.1": "2014-12-16T17:51:31.299Z", "1.4.0": "2014-12-22T06:23:30.189Z", "1.5.0": "2014-12-30T20:18:18.941Z", "1.6.0": "2015-01-30T03:49:26.689Z", "1.6.1": "2015-02-05T17:50:22.349Z", "1.7.0": "2015-02-09T01:52:13.439Z", "1.8.0": "2015-03-14T00:24:56.612Z", "1.9.0": "2015-04-20T01:38:12.189Z", "1.9.1": "2015-04-20T02:06:55.370Z", "1.10.0": "2015-05-20T03:02:15.863Z", "1.11.0": "2015-06-01T04:03:45.970Z", "1.12.0": "2015-06-06T03:19:37.308Z", "1.13.0": "2015-06-08T00:27:23.098Z", "1.14.0": "2015-06-26T02:12:03.398Z", "1.15.0": "2015-07-13T21:56:36.379Z", "1.16.0": "2015-07-29T16:19:33.504Z", "1.17.0": "2015-08-14T02:52:23.446Z", "1.18.0": "2015-09-03T15:44:26.171Z", "1.19.0": "2015-09-18T04:10:52.533Z", "1.20.0": "2015-11-11T05:30:10.962Z", "1.21.0": "2016-01-06T17:25:52.120Z", "1.22.0": "2016-02-15T17:53:37.862Z", "1.23.0": "2016-05-02T04:36:39.170Z", "1.24.0": "2016-09-18T11:39:54.651Z", "1.25.0": "2016-11-12T01:49:07.167Z", "1.26.0": "2017-01-15T04:04:59.107Z", "1.27.0": "2017-03-17T03:44:59.581Z", "1.28.0": "2017-05-15T05:27:52.930Z", "1.29.0": "2017-07-11T02:19:51.110Z", "1.30.0": "2017-08-28T02:28:51.133Z", "1.31.0": "2017-10-26T03:25:52.356Z", "1.32.0": "2017-11-29T21:07:28.456Z", "1.33.0": "2018-02-16T05:10:20.269Z", "1.34.0": "2018-06-03T23:39:12.219Z", "1.35.0": "2018-07-15T15:53:24.203Z", "1.36.0": "2018-08-20T15:25:10.121Z", "1.37.0": "2018-10-20T01:39:34.333Z", "1.38.0": "2019-02-05T03:20:32.705Z", "1.39.0": "2019-04-05T02:08:43.078Z", "1.40.0": "2019-04-21T01:58:25.612Z", "1.41.0": "2019-08-30T15:30:01.529Z", "1.42.0": "2019-09-26T03:31:23.896Z", "1.43.0": "2020-01-06T03:24:37.942Z", "1.44.0": "2020-04-23T00:55:24.718Z", "1.45.0": "2020-09-23T03:47:56.743Z", "1.46.0": "2021-02-13T05:37:31.854Z", "1.47.0": "2021-04-01T19:39:44.113Z", "1.48.0": "2021-05-31T03:59:02.465Z", "1.49.0": "2021-07-26T21:35:47.681Z", "1.50.0": "2021-09-16T01:37:03.130Z", "1.51.0": "2021-11-09T02:44:55.389Z", "1.52.0": "2022-02-21T19:41:51.123Z", "1.53.0": "2024-07-12T20:35:00.644Z", "1.54.0": "2025-03-18T15:06:44.354Z"}, "bugs": {"url": "https://github.com/jshttp/mime-db/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/mime-db#readme", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": {"url": "git+https://github.com/jshttp/mime-db.git", "type": "git"}, "description": "Media Type Database", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://github.com/broofa", "name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>dd"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# mime-db\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][ci-image]][ci-url]\n[![Coverage Status][coveralls-image]][coveralls-url]\n\nThis is a large database of mime types and information about them.\nIt consists of a single, public JSON file and does not include any logic,\nallowing it to remain as un-opinionated as possible with an API.\nIt aggregates data from the following sources:\n\n- https://www.iana.org/assignments/media-types/media-types.xhtml\n- https://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types\n- https://hg.nginx.org/nginx/raw-file/default/conf/mime.types\n\n## Installation\n\n```bash\nnpm install mime-db\n```\n\n### Database Download\n\nIf you intend to use this in a web browser, you can conveniently access the JSON file via [jsDelivr](https://www.jsdelivr.com/), a popular CDN (Content Delivery Network). To ensure stability and compatibility, it is advisable to specify [a release tag](https://github.com/jshttp/mime-db/tags) instead of using the 'master' branch. This is because the JSON file's format might change in future updates, and relying on a specific release tag will prevent potential issues arising from these changes.\n\n```\nhttps://cdn.jsdelivr.net/gh/jshttp/mime-db@master/db.json\n```\n\n## Usage\n\n```js\nvar db = require('mime-db')\n\n// grab data on .js files\nvar data = db['application/javascript']\n```\n\n## Data Structure\n\nThe JSON file is a map lookup for lowercased mime types.\nEach mime type has the following properties:\n\n- `.source` - where the mime type is defined.\n    If not set, it's probably a custom media type.\n    - `apache` - [Apache common media types](https://svn.apache.org/repos/asf/httpd/httpd/trunk/docs/conf/mime.types)\n    - `iana` - [IANA-defined media types](https://www.iana.org/assignments/media-types/media-types.xhtml)\n    - `nginx` - [nginx media types](https://hg.nginx.org/nginx/raw-file/default/conf/mime.types)\n- `.extensions[]` - known extensions associated with this mime type.\n- `.compressible` - whether a file of this type can be gzipped.\n- `.charset` - the default charset associated with this type, if any.\n\nIf unknown, every property could be `undefined`.\n\n## Note on MIME Type Data and Semver\n\nThis package considers the programmatic api as the semver compatibility. This means the MIME type resolution is *not* considered\nin the semver bumps. This means that if you want to pin your `mime-db` data you will need to do it in your application. While\nthis expectation was not set in docs until now, it is how the pacakge operated, so we do not feel this is a breaking change.\n\n## Contributing\n\nThe primary way to contribute to this database is by updating the data in\none of the upstream sources. The database is updated from the upstreams\nperiodically and will pull in any changes.\n\n### Registering Media Types\n\nThe best way to get new media types included in this library is to register\nthem with the IANA. The community registration procedure is outlined in\n[RFC 6838 section 5](https://tools.ietf.org/html/rfc6838#section-5). Types\nregistered with the IANA are automatically pulled into this library.\n\n### Direct Inclusion\n\nIf that is not possible / feasible, they can be added directly here as a\n\"custom\" type. To do this, it is required to have a primary source that\ndefinitively lists the media type. If an extension is going to be listed as\nassociated with this media type, the source must definitively link the\nmedia type and extension as well.\n\nTo edit the database, only make PRs against `src/custom-types.json` or\n`src/custom-suffix.json`.\n\nThe `src/custom-types.json` file is a JSON object with the MIME type as the\nkeys and the values being an object with the following keys:\n\n- `compressible` - leave out if you don't know, otherwise `true`/`false` to\n  indicate whether the data represented by the type is typically compressible.\n- `extensions` - include an array of file extensions that are associated with\n  the type.\n- `notes` - human-readable notes about the type, typically what the type is.\n- `sources` - include an array of URLs of where the MIME type and the associated\n  extensions are sourced from. This needs to be a [primary source](https://en.wikipedia.org/wiki/Primary_source);\n  links to type aggregating sites and Wikipedia are _not acceptable_.\n\nTo update the build, run `npm run build`.\n\n[ci-image]: https://badgen.net/github/checks/jshttp/mime-db/master?label=ci\n[ci-url]: https://github.com/jshttp/mime-db/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/mime-db/master\n[coveralls-url]: https://coveralls.io/r/jshttp/mime-db?branch=master\n[node-image]: https://badgen.net/npm/node/mime-db\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/mime-db\n[npm-url]: https://npmjs.org/package/mime-db\n[npm-version-image]: https://badgen.net/npm/v/mime-db\n", "readmeFilename": "README.md", "users": {"laomu": true, "panlw": true, "micnic": true, "xiaoyiyu": true, "mojaray2k": true, "snowdream": true, "shuoshubao": true, "tiancheng9": true, "flumpus-dev": true, "zhenguo.zhao": true, "danielbankhead": true, "shanewholloway": true}}