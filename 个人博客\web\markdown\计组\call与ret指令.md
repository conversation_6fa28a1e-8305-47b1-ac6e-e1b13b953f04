# 函数调用的机器级表示:

![image-20241120151649682](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241120151649682.png)

首先函数调用main函数，main的栈帧会入栈，接着会调用p函数，p的栈帧会入栈，p中q，入栈，调用完就出栈，caller也一样，最后调用完了p则它的栈帧也出栈![image-20241120152235710](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241120152235710.png)

![image-20241120152221624](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241120152221624.png)

## call指令和ret指令都应该会将pc的值改变，那怎么让pc或者ip寄存器过去到add指令的第一条，如何又回到call指令的后一个指令中去

![image-20241120152620673](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241120152620673.png)