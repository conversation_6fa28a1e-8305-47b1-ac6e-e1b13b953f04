#include <stdio.h>
#include <limits.h>

void findMostFrequentNumber(int arr[], int n) {
    int frequency[20001] = {0}; // 假设输入的整数范围为-10000到10000
    int maxFrequency = 0;
    int result = INT_MAX;

    // 统计每个数的出现次数
    for (int i = 0; i < n; i++) {
        frequency[arr[i] + 10000]++; // 偏移索引以支持负数
        if (frequency[arr[i] + 10000] > maxFrequency || 
           (frequency[arr[i] + 10000] == maxFrequency && arr[i] < result)) {
            maxFrequency = frequency[arr[i] + 10000];
            result = arr[i];
        }
    }

    printf("出现次数最多的且最小的数为%d\n", result);
}

int main() {
    int n;
    printf("请输入整数的个数: ");
    scanf("%d", &n);

    int arr[n];
    printf("请输入%d个整数:\n", n);
    for (int i = 0; i < n; i++) {
        scanf("%d", &arr[i]);
    }

    findMostFrequentNumber(arr, n);

    return 0;
}
