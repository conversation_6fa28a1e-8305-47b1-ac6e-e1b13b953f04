# Weather Backend Service

一个基于 Node.js 和 Express 的天气后台服务，提供天气数据查询 API。

## 功能特性

- 🌤️ 实时天气数据查询
- 📍 支持城市名称和坐标查询
- 🚀 数据缓存机制，提高响应速度
- 🔒 安全防护和请求限制
- 📝 完整的错误处理和日志记录
- 🌍 支持多语言和单位转换

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并填入你的配置：

```bash
cp .env.example .env
```

### 3. 获取天气 API Key

1. 访问 [OpenWeatherMap](https://openweathermap.org/api)
2. 注册账号并获取免费的 API Key
3. 将 API Key 填入 `.env` 文件中的 `OPENWEATHER_API_KEY`

### 4. 启动服务

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

## API 文档

### 获取当前天气

```
GET /api/weather/current?city={城市名}&lang={语言}&units={单位}
```

### 获取天气预报

```
GET /api/weather/forecast?city={城市名}&days={天数}&lang={语言}&units={单位}
```

### 通过坐标获取天气

```
GET /api/weather/coordinates?lat={纬度}&lon={经度}&lang={语言}&units={单位}
```

## 项目结构

```
weather-backend/
├── src/
│   ├── app.js              # 应用入口
│   ├── config/             # 配置文件
│   ├── controllers/        # 控制器
│   ├── services/           # 业务逻辑
│   ├── middleware/         # 中间件
│   ├── routes/             # 路由
│   └── utils/              # 工具函数
├── tests/                  # 测试文件
├── package.json
└── README.md
```

## 技术栈

- Node.js
- Express.js
- Axios (HTTP 客户端)
- Node-cache (内存缓存)
- Helmet (安全防护)
- Morgan (日志记录)
- Jest (测试框架)
