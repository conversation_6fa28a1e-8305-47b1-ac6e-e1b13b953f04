{"_id": "helmet", "_rev": "499-66b29c8b1774e04691b0eaa1ff22db75", "name": "helmet", "dist-tags": {"next": "5.0.0-beta.1", "latest": "8.1.0"}, "versions": {"0.0.1": {"name": "helmet", "version": "0.0.1", "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "728eef09f455304ead18d91f1e835d0ce52ff3ff", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.1.tgz", "integrity": "sha512-58wN/izGCGVOiSwVwxCmvM3LElutNyTrAlxUNTH6MwV7+B0hC4zzOh3pIbWT6Xgjlbe4k1w1jWXc4GSIVT8EQQ==", "signatures": [{"sig": "MEUCIQCWwIXVIUokPLSR1KsxPwuT2SwJyp5wcS+AUfKz83/nVgIgSVEQgDIhFs9cV4ljvbRzGixnqPjQkvSPrpAoAgxlTRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "engines": {"node": "~0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/andyet/helmet.git"}, "_npmVersion": "1.1.0-3", "description": "Security header middleware collection for express", "directories": {}, "_nodeVersion": "v0.6.9", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.2": {"name": "helmet", "version": "0.0.2", "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "a3ec5d98b044c7c8f90d3c7e823291ac08712b96", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.2.tgz", "integrity": "sha512-wOLjLK8Hs/8zgdMEyLKA4Ofh1/qHR3AdUQD2vI0zSe5Eep8AxQTeHhQN91Uraj7JJnZ+NVfDe74F+quD0p4sow==", "signatures": [{"sig": "MEUCIQCESyShkEJg2UuBk1YQ3IFsL0JccXBQ1xludNxLPDOb5QIgbqPpFly/CvnrOY0lVU7xlo4CtjdrCWC5n7PHf1MbjFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "engines": {"node": "~0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/andyet/helmet.git"}, "_npmVersion": "1.1.0-3", "description": "Security header middleware collection for express", "directories": {}, "_nodeVersion": "v0.6.9", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.3": {"name": "helmet", "version": "0.0.3", "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "54e758d2bad4d347a551c3bfb4591a601ac3d6f3", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.3.tgz", "integrity": "sha512-KajTtb29knYppW3vxkeDaUgLNY7nogIsEUo4hqBZVVYRF5mWfwddOET/2ZRbR1rM9BsY7KwoUFRJZsLnuJi17Q==", "signatures": [{"sig": "MEUCIQCnXk4DEvG+NdmAfgnIQ6R0R4uvlAtXdatM9qmjjY2bWwIgO7vsOVukRskqbdOLrWjtF7Z363u8kmjSMDDGRx/2BKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "engines": {"node": "~0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/andyet/helmet.git"}, "_npmVersion": "1.1.0-3", "description": "Security header middleware collection for express", "directories": {}, "_nodeVersion": "v0.6.9", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.4": {"name": "helmet", "version": "0.0.4", "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.4", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "533ff43d6ee7e451927e48fc298022c89330dc96", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.4.tgz", "integrity": "sha512-PtCpSrMJMrurmhG3V4+Hq3x/COlXuJq66rhh6y/wDombqD2oaEEstO1xGhVvcbZwAquHs5Z+ON3YQZXRD9KnLA==", "signatures": [{"sig": "MEQCIE8iXpH7Eg90pomNTRiOurrqN2hggj+cCeyIzpj0xSWwAiBNHCrPmyWDBM6xIsUzwaLHxkN3+hChNVu6mcY1c323jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "engines": {"node": "~0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/andyet/helmet.git"}, "_npmVersion": "1.1.21", "description": "Security header middleware collection for express", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.5": {"name": "helmet", "version": "0.0.5", "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.5", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "27012e4bc79bd13d9c4b20758f84d165862b7839", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.5.tgz", "integrity": "sha512-adBl0RtTtGy5H5tfn42IU9/e79aTISm2ssonmBr/PppMDyW2oVOrgQME110jN4+t3WhkorZsy8OI5bwA+IFEdw==", "signatures": [{"sig": "MEUCIDiZYgwUWBf/q1qz+wRxRmJTQkCht2/GIs0N7/7/po+wAiEAgiyowAK9Z91kilgAyLX2ZsyUkBS71G8j9vr20l0BO6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/andyet/helmet.git"}, "_npmVersion": "1.1.21", "description": "Security header middleware collection for express", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.6": {"name": "helmet", "version": "0.0.6", "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.6", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "bc5df862e40c7d2aacd12fde05bfdab29298a8da", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.6.tgz", "integrity": "sha512-I8aSc5/Ya7ArApTk14jRIcLVJ+5SnvtExgLRiqDXRuvAJp+eXdWnDn9L36M+IAdy5NlxPump9ohudt7lU8c44A==", "signatures": [{"sig": "MEUCIQDSsqiwExoZUiVOvxJYXxoeqEoR7lbx8lyHfsoLTWjTuwIgJfMw2xgTvxuklvoZasHyBLJWHX3gPgX+TIbUYOckCRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/andyet/helmet.git"}, "_npmVersion": "1.1.24", "description": "Security header middleware collection for express", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.7": {"name": "helmet", "version": "0.0.7", "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.7", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "a98652df6256785515ad2be86aa573a273cbcfe4", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.7.tgz", "integrity": "sha512-HT/ka2QzfzHn2FraQqNKPG1UyoB9yfi/ve0T++V9qnRpL7HVydjYncp7E+e4eWgX3ZB/ueCvR6HS6wRvTYu3Gw==", "signatures": [{"sig": "MEUCIQDBYdH3cubL3ip78tVu7Wr7VBF0FJSu7hibzJVd4MmYAAIgSb+MVZvm2l+TXUkMRtQ4JnrPZIGEHRoJiFLhNCaRdZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/andyet/helmet.git"}, "_npmVersion": "1.1.62", "description": "Security header middleware collection for express", "directories": {}, "dependencies": {}, "devDependencies": {}}, "0.0.8": {"name": "helmet", "version": "0.0.8", "keywords": ["security", "headers", "express", "x-frame-options", "csp"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.8", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "e1de1c372e3814de657777067487abbf8b775441", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.8.tgz", "integrity": "sha512-WiZTl4dA/rVKb5cx2soZmuaHrCPb6R5Omi/uIcCmQ4TWGGAfJViK05RVHNMYD54PxskB2TahwegYjXBeqDq3WQ==", "signatures": [{"sig": "MEUCIA/aHEbHgEbiSc45iQKsF+IydGdrJ8wjrQe9vysSDxaMAiEAjdyMsDhoe+QdAuMYAb0KcbDP+zw6cWmrrRODIigSYj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.2.11", "description": "Security header middleware collection for express", "directories": {}, "dependencies": {}, "devDependencies": {}}, "0.0.9": {"name": "helmet", "version": "0.0.9", "keywords": ["security", "headers", "express", "x-frame-options", "csp"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.9", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "dist": {"shasum": "4356c5763dc2073631654c8b89011f7ecab9f60a", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.9.tgz", "integrity": "sha512-wiBXz9sj/9xhYSF+dDaV+BhqPb+QMrSzkkBcp7eC+d+YmTH5LRQE3BOlZNk+qlNHHaf1ApB0C0vRzTxm7VbTGw==", "signatures": [{"sig": "MEQCIBul+s9V1c2BQRkbUEmb443nZilj57Tldo/a978Pehb1AiBUagz5kgCFASsolwgcYT+UMeivfAhbCxUdx7dpt1OZ4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.2.11", "description": "Security header middleware collection for express", "directories": {}, "dependencies": {}, "devDependencies": {}}, "0.0.10": {"name": "helmet", "version": "0.0.10", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.10", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "c07c37f6aad5db6dfb1b249bfcd4a182123a4b37", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.10.tgz", "integrity": "sha512-VKhrp5uA+7k/CfDofKlay+vuD6NMeLh+8ymQgXrDRaocvqwsa1IN1odOHPG+r9j7oOY4GbNxDV4y8CJFE7adxQ==", "signatures": [{"sig": "MEYCIQD7KN97E4eO2ugZSVVdUU1kFphvTJCsFPEKcVxFREjowwIhAPzPDQ7UoNLIkne2x6QPpblkgr9NxtgZ16gD14fDKis7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.2.32", "description": "Security header middleware collection for express", "directories": {}, "dependencies": {}, "devDependencies": {}}, "0.0.11": {"name": "helmet", "version": "0.0.11", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.0.11", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "5f433ffac95b2efb9c98d3511fe4df74f36c532c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.0.11.tgz", "integrity": "sha512-B2rA6GH11RmGZKTeHlLtUNEW3i70QJiKl/L2JEka9wye2+AIXKW3Qwnrgd1/u3/+jmJfcNJjoSIIGc6TKPToIA==", "signatures": [{"sig": "MEQCHxyHUYzzJLDOnK/hYNkAq1Yak0V4PRibRHkZfpwfLfsCIQDT05OWSyngEA8sI/u0tdbJy/SAmwEJZrccbN5OMCThsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.3.2", "description": "Security header middleware collection for express", "directories": {}, "dependencies": {}, "devDependencies": {}}, "0.1.0": {"name": "helmet", "version": "0.1.0", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "ce316dfe9a439811c1a40b1e1cd6d5f4db67c82e", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.1.0.tgz", "integrity": "sha512-U4dyJO9oEbdU8XOyaeBh4Bh7UVFd0UAbsNohttJ1yheiHYCSA0+5PJka474V0efPC2/1qcODKjTVs5OUj0S/8w==", "signatures": [{"sig": "MEQCIAvNJi/sWAxnbQDIbLk11MtycMR+DRvp1jPVYF2Yi0usAiAKxMQOi1PxX4plxxqIgHAYFQtFVECe3UNJkXY/XYM3sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.3.2", "description": "Security header middleware collection for express", "directories": {}, "dependencies": {}, "devDependencies": {}}, "0.1.1": {"name": "helmet", "version": "0.1.1", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.1.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "c85ad1a6be3743f8b576ae0366612a231046716a", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.1.1.tgz", "integrity": "sha512-oAkhKg1AYm96kVmGVu6oPC8bjfHv7WLyoA0NWl1TV9jAfXZRUkVjiNHb0N0BnzgQ77sDNOsBE+Qywi+PaWGvZA==", "signatures": [{"sig": "MEUCIAa300aZjOaoTVsX2nBipVWp+JU5HBbRwZGOzpVZ9syUAiEAxbelaOyOr9LEoxE7JuS60zLHxGKvWM18Y4g2DtrEEI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.3.11", "description": "Security header middleware collection for express", "directories": {}, "devDependencies": {"mocha": "*", "sinon": "*"}}, "0.1.2": {"name": "helmet", "version": "0.1.2", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.1.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "fe6e1c3df0b6f860c5d8c4798a8d06bd18dce4b6", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.1.2.tgz", "integrity": "sha512-vw9HOr8YQBnyABeSnP9fGpIkqL8VEX8gjtyQ41OTiGFyH7pKlHU0aSAeWMBVjzYJs+BYYzPTR0O15LfTiMAneA==", "signatures": [{"sig": "MEQCIEKIOfj7y3vXuoA8ySOwUxF9GnUyJmyH/XtF+p2wC5nQAiBaGoiNSvbsEqqSd6fFbihrhJ2M6ijQCVYpkg5s2KacmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.3.11", "description": "Security header middleware collection for express", "directories": {}, "devDependencies": {"mocha": "*", "sinon": "*"}}, "0.1.3": {"name": "helmet", "version": "0.1.3", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.1.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "0e443f4e11015084d172bff0d5162e70c2ab32a9", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.1.3.tgz", "integrity": "sha512-C6d2lhZXECAT/YTSZtZWH57+t7GugNHvofZfuWG0HFAA04/IlFXXbdvsvlPdSqoWGU6mq4SwA3ochE+CwRwGpg==", "signatures": [{"sig": "MEUCIDBu2mVLyq9rT4gLvYyRPwEUH5bKP6ERBSW++8J54EEWAiEA0N2KYcj9K/XLr2bSaUtUoCmJNs+ibT5H4taZPi7jEY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "adam_baldwin", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.4.3", "description": "Security header middleware collection for express", "directories": {}, "dependencies": {"platform": "1.0.x"}, "devDependencies": {"mocha": "1.14.x", "connect": "2.11.x", "supertest": "0.8.x"}}, "0.2.0": {"name": "helmet", "version": "0.2.0", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.2.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "6fea8afb2a2132316343a4ac5f213245b7651300", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.2.0.tgz", "integrity": "sha512-Weq0vbBk4P8tr9qb1ga0084W0D5cvxRcRlZXOKy29S4+QpSCioMIIkHSgvYUIH6ouSqd2pIKfY8tnt1SMbxoJQ==", "signatures": [{"sig": "MEUCIQDkpiGjm1hOf+oO43/UJywseje+N64IguCQ02/TXQXqZgIgVMxEKGbBV0k/YXR+wLdatyez9d8809tfy4c58B2YC0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.3.11", "description": "Security header middleware collection for express", "directories": {}, "dependencies": {"platform": "1.0.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.14.x", "connect": "2.11.x", "supertest": "0.8.x"}}, "0.2.1": {"name": "helmet", "version": "0.2.1", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.2.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "5e5e6d4d7761e199368fc63426cec9bea39b10d3", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.2.1.tgz", "integrity": "sha512-T4FBzJ9+GUB/Or0mTRSG7kTdgG23uCqjqvv+6Vo9bpK4oYPEkR6Kn4bNkKHrHTDPrB8RFaiaWlVLo8sAgIhsqw==", "signatures": [{"sig": "MEUCIQCv1xY8fMHDKWvhg68Tdz5eKZic2GZpL9K8ho4TQqhlqwIgSoBbP//eYJWp7hdXQv3wbHYS8zahTGTFRbwg4N6h69Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.4.3", "description": "Security header middleware collection for Express/Connect", "directories": {}, "dependencies": {"platform": "1.0.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.14.x", "connect": "2.11.x", "supertest": "0.8.x"}}, "0.2.2": {"name": "helmet", "version": "0.2.2", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts", "crossdomain.xml"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.2.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "037411b970d911707670a2252bb61b9ce26289bd", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.2.2.tgz", "integrity": "sha512-QzrqfYh5btljLv3bBxCHIE+UU0BndMGO6hyaNPXv8DHZSvUwbwNv2rY8s12vpDGnEYsO75oRtuzaVTsaaXB6Qw==", "signatures": [{"sig": "MEQCIFeaas7N/doMdjNnlUi0ARe9fr4v3JUfMK4ovT5tvo/FAiBt8sI9bnUY+i1vY96Qqqf6AGjLR8foiOO123DACKQc6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.4.3", "description": "Security header middleware collection for Express/Connect", "directories": {}, "dependencies": {"platform": "1.1.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.20.x", "connect": "2.18.x", "supertest": "0.13.x"}}, "0.2.3": {"name": "helmet", "version": "0.2.3", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts", "crossdomain.xml"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.2.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "81e9ec750ac5826b3c762ef2259ad9268144c86e", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.2.3.tgz", "integrity": "sha512-trMvIYiAr0JixzdphzgD1r2C6ln2pPZWnRBJru9qBlZi0SLvC/NirT35SzF78MqfRScS6cqnV/hxHM7RrAACHg==", "signatures": [{"sig": "MEYCIQC9X5Zt1KJ6UuSZU94UaEfpLzCkkn4io6nKUvgOHxoHJQIhAOPztfElYWPHGNanQa6SfFwE4bPsm91XnQgvgB2C8CLm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.3.11", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"platform": "1.1.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.20.x", "connect": "2.18.x", "supertest": "0.13.x"}}, "0.2.4": {"name": "helmet", "version": "0.2.4", "keywords": ["security", "headers", "express", "x-frame-options", "csp", "hsts", "crossdomain.xml"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.2.4", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "6a4962d4ed607e6dcb5afefb6a8c14ce08584e12", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.2.4.tgz", "integrity": "sha512-K6XY7Gx+jWcwhIChqtgiaBwi4R616j3jj6kHRpWjBnze85pInlVUqrlRrNhmilHeifhtLMxtQA0D6eFx+7dINA==", "signatures": [{"sig": "MEUCIQDQQ3TkfInOrchhNfH5W3xw/i5y9uGrjQueTl04xmH5ZAIgLhdNZQ2Cli+VEgqF6zWQWgQ5xn/8BoYAQ573zE1W1KQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "engines": {"node": ">= 0.6.6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git"}, "_npmVersion": "1.4.3", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"platform": "1.1.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.20.x", "connect": "2.19.x", "supertest": "0.13.x"}}, "0.3.0": {"name": "helmet", "version": "0.3.0", "keywords": ["security", "headers", "express", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.3.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "8d4853fb24c37b4327dc9ba180de8c786c59953c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.3.0.tgz", "integrity": "sha512-bX/OAOE7gg/QSwROupFXuM3DoP2cpAlSwYSzFF/b74kNh23fHwKatGSKWfS+lFm1E0BGOxjOKmsSnlZJagK6MQ==", "signatures": [{"sig": "MEQCIHFpXXepdgGQpbom6do2v2fxBTntR56Fp5otP8SUsAaMAiBInAc4bl2QheyLuwTw99OuNMQNb/kYMWYbyjgM3vyuhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "_shasum": "8d4853fb24c37b4327dc9ba180de8c786c59953c", "engines": {"node": ">= 0.6.6"}, "gitHead": "d0352449003030bfc3ffb0a92fe33937e20746f5", "scripts": {"hint": "jshint .", "test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"connect": "3.0.x", "camelize": "0.1.x", "platform": "1.2.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.20.x", "sinon": "1.10.x", "jshint": "2.5.x", "supertest": "0.13.x"}}, "0.3.1": {"name": "helmet", "version": "0.3.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.3.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "1b652950e4ec9e65c9549af31983ae995d0f9044", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.3.1.tgz", "integrity": "sha512-/O4N/zYhCtfJ5KSM153XBYG4Kq0NjYY3kZWzKaJjQNI6byhYFaYZe+GxzMpGbZe76+bMwsFbtumHG+QcOKqV0A==", "signatures": [{"sig": "MEQCICNHDF9A+phMPMZsH1OxSFeZQD+fKBlAi062Vyhr+mPMAiBT5k/zMu7Kuhcnc9Y3WNnefABMGM8x7zW6cK6Kkc+rQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "_shasum": "1b652950e4ec9e65c9549af31983ae995d0f9044", "engines": {"node": ">= 0.6.6"}, "gitHead": "8ac99ff75a13b6b689550eb1bf6a19662a08d8fc", "scripts": {"hint": "jshint .", "test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"connect": "3.0.x", "camelize": "0.1.x", "platform": "1.2.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.20.x", "sinon": "1.10.x", "jshint": "2.5.x", "supertest": "0.13.x"}}, "0.3.2": {"name": "helmet", "version": "0.3.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.3.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "7e828dbf0658f28717bee3c190f0e39a080ab052", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.3.2.tgz", "integrity": "sha512-7LyCDI5ZLv4djQfLCKD8kZE7LwpT9zhxEy9QF2n2n8rtmFiTIGyqeu1gBSGJpTQyHmf7SfJdPHz+0Ke7Dgg3OA==", "signatures": [{"sig": "MEQCIAK5isuRJS1OXQejZ00iOEsC7XDO/GQa91rBc9tBzso5AiAX9NZISuTGDGsTtQqV8PFYDz1eo0raOpa+HBg0gfvNUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "_shasum": "7e828dbf0658f28717bee3c190f0e39a080ab052", "engines": {"node": ">= 0.6.6"}, "gitHead": "4442a1c2667f91674420862cd65153327f3672f0", "scripts": {"hint": "jshint .", "test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"connect": "3.0.x", "camelize": "0.1.x", "platform": "1.2.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.20.x", "sinon": "1.10.x", "jshint": "2.5.x", "supertest": "0.13.x"}}, "0.4.0": {"name": "helmet", "version": "0.4.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.4.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "851ff16e70700d66194fe05c7b27fb02b0f05ca4", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.4.0.tgz", "integrity": "sha512-04nl2j2xBxAPcRjAfHjtev2nP0z/EJWPsdFLD8AD3lKdJ9Lk5g7+f33WuIMlvjte3aAs9EOmVcGz+gzntQqtig==", "signatures": [{"sig": "MEUCIQD7vG7sEoKItBF1in1d8ksLUuTtYilstSZibYFtYAeQFwIgESWQhbZxsr8YhDjMlbKCvYGMJPdqmBQlZmcQZ1gCaPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "_shasum": "851ff16e70700d66194fe05c7b27fb02b0f05ca4", "engines": {"node": ">= 0.6.6"}, "gitHead": "c7b995e4a5332a319b8baa6d91b6bdc29776ec2c", "scripts": {"hint": "jshint .", "test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"connect": "3.0.x", "camelize": "1.0.x", "platform": "1.2.x", "underscore": "1.6.x"}, "devDependencies": {"mocha": "1.20.x", "sinon": "1.10.x", "jshint": "2.5.x", "supertest": "0.13.x"}}, "0.4.1": {"name": "helmet", "version": "0.4.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.4.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "5f2fe259ba3352f0c00c18f48555612d851cc783", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.4.1.tgz", "integrity": "sha512-baXDAKnbOVNzvFzgmRZsL3F8KUPK+wd+H+UF+mfwrVY+ckDSWIo6gdAMMe/J9b6rzRauTIJOgR2JZ2/mQoL9zg==", "signatures": [{"sig": "MEUCIQCrygdvZzsrNcSzNDO5G2qEqSUoDtLVRdSPsLcK2g4HOQIgGfGGRguZ7BNgqOH0KObqsnSR7+BDhO4JH7Qfc9Q9qmI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "_shasum": "5f2fe259ba3352f0c00c18f48555612d851cc783", "engines": {"node": ">= 0.6.6"}, "gitHead": "cbfcceee41f688a8e1f4cd0f59fd708b0f5c1218", "scripts": {"hint": "jshint .", "test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"connect": "3.0.x", "camelize": "1.0.x", "platform": "1.2.x", "underscore": "1.6.x", "helmet-crossdomain": "^0.1.0"}, "devDependencies": {"mocha": "1.20.x", "sinon": "1.10.x", "jshint": "2.5.x", "supertest": "0.13.x"}}, "0.4.2": {"name": "helmet", "version": "0.4.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.4.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "b65596af821dd3318d612ed55f2b678a4b678962", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.4.2.tgz", "integrity": "sha512-bLHbvtEzbrk6w08TN91Ygj88xCkXXM0BTmtb5VQWeirwwgaA+QgoE2ttCAkygEFHYWM/FqtcNMotNrzTxIu7tg==", "signatures": [{"sig": "MEUCIQDbQ1J+np5/cn4RXM+hCQiTCUFVTtppOim2qVb5SzKnaAIgT6024Gq0uA5R6FXJlGDHpRB3uIvynFSPA68FFhYOQnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index", "_from": ".", "_shasum": "b65596af821dd3318d612ed55f2b678a4b678962", "engines": {"node": ">= 0.6.6"}, "gitHead": "e457f731d6b9c66d975b9c53b05ecdbe6add1244", "scripts": {"hint": "jshint .", "test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"connect": "3.0.x", "camelize": "1.0.x", "platform": "1.2.x", "underscore": "1.6.x", "helmet-crossdomain": "^0.1.0"}, "devDependencies": {"mocha": "1.20.x", "sinon": "1.10.x", "jshint": "2.5.x", "supertest": "0.13.x"}}, "0.5.0": {"name": "helmet", "version": "0.5.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.5.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/evilpacket/helmet", "bugs": {"url": "https://github.com/evilpacket/helmet/issues"}, "dist": {"shasum": "71f83cd9b0ea3bdce89b37d52aca380a6fd12d35", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.5.0.tgz", "integrity": "sha512-Q5aEOgSxoJUsYfadh0oxOEt++SwcNItvE1C6CCXTJ4Qaa3e+UopZLFbLGJHyuEBnl46BKolNCTHzQgyVIIBK8Q==", "signatures": [{"sig": "MEUCIQDG4I2pvVlYO4UNUCuEj//YYaYjhgCfGkgcivG+9O9e9AIgd56253onrAGJGSZsVRMIetqu2Bhj7QSLLlroGz5gxNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "71f83cd9b0ea3bdce89b37d52aca380a6fd12d35", "engines": {"node": ">= 0.10.0"}, "gitHead": "db7a4b2a4517a8c1a02c9478e842326eeacb1c4a", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/evilpacket/helmet.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"hsts": "0.1.0", "connect": "3.3.1", "nocache": "0.1.0", "ienoopen": "0.1.0", "frameguard": "0.2.0", "helmet-csp": "0.1.0", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.0", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.0.1", "sinon": "^1.11.1"}}, "0.5.1": {"name": "helmet", "version": "0.5.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.5.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "bfef39984923ee68919e64451acf841f98577ff0", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.5.1.tgz", "integrity": "sha512-oKNQX2NiqtGR1hG8kesKOY8wn+MypjJjwY/qyACQGC8gj0ijAB/m3yopaKUZePhVbY1Bc9zVgF62bB9tl6l0OA==", "signatures": [{"sig": "MEUCIG6/R5v4UXfQNLhqRBoXubdFDIxWzg6DFXfhTHTs1ihBAiEAwIq3HqQhbJCHD3i04jj2QY1jQUrvPSvwaqzoPnvy4Hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "bfef39984923ee68919e64451acf841f98577ff0", "engines": {"node": ">= 0.10.0"}, "gitHead": "36fd45cf0fb541bf7509fa3f13ac966320133bc3", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"hsts": "0.1.0", "connect": "3.3.1", "nocache": "0.1.0", "ienoopen": "0.1.0", "frameguard": "0.2.0", "helmet-csp": "0.1.1", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.0", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.0.1", "sinon": "^1.11.1"}}, "0.5.2": {"name": "helmet", "version": "0.5.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.5.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "f60a9e2246f78dbdf3565ed6c333004de5a44fa1", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.5.2.tgz", "integrity": "sha512-gTdSHlRU/7IZgdy4FpRZqMFNpX6/qC5KIi668c93ybu2mGiudBqvuhxPwOPlRdX62xG4jOt4A4h+3ZOm7t7w9A==", "signatures": [{"sig": "MEUCID6B0qOa23VancFB9LsP5uKarlwfmzOM7S7oKcdYL8xPAiEA2K4PWCyRDseSO9cEc20unhAYUipsQAAPEVkMlnrn0/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "f60a9e2246f78dbdf3565ed6c333004de5a44fa1", "engines": {"node": ">= 0.10.0"}, "gitHead": "8ae88fc2f8335150e02016f6b32b38e61d7ffe2e", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"hsts": "0.1.0", "connect": "3.3.3", "nocache": "0.1.0", "ienoopen": "0.1.0", "frameguard": "0.2.0", "helmet-csp": "0.1.2", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.0", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.0.1", "sinon": "^1.12.1"}}, "0.5.3": {"name": "helmet", "version": "0.5.3", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.5.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "31b9f2cf8e322a30da4a64babb1a08ca2e5f57ce", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.5.3.tgz", "integrity": "sha512-/hI/9VWkY9YFDbHzbE9LOA9B+Z2wSRoktaAdYGyc0Ly6bcj+Y2h1FCL5PFM9X5Ku5keh2AYKKd3lCHxBLU80lA==", "signatures": [{"sig": "MEYCIQDK/jgWfGoq3YTex0KLZHUbKAc1FwOgX2nO/6PiNvffVQIhANY0qFbHFE3FsygpnvF3LGsZvWqRLfgm3MKi8cvHNGq6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "31b9f2cf8e322a30da4a64babb1a08ca2e5f57ce", "engines": {"node": ">= 0.10.0"}, "gitHead": "7e15a20e59f4a49b637588c72086362e2d58d6ac", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"hsts": "0.1.0", "connect": "3.3.3", "nocache": "0.1.0", "ienoopen": "0.1.0", "frameguard": "0.2.0", "helmet-csp": "0.1.2", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.1", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.0.1", "sinon": "^1.12.1"}}, "0.5.4": {"name": "helmet", "version": "0.5.4", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.5.4", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "59b84a7dce1bf3366ef0a1cbb1f5aecc5eac89d3", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.5.4.tgz", "integrity": "sha512-BHetO1cBcSxeAeBcYrkbF1V7KviMQXs7FZ4hXG7pnF3Luzem8MMUNLAQmP3MsUwM1jWuBat/cOrl+qxhuDTp4g==", "signatures": [{"sig": "MEYCIQDYBQDpwrBx/OEcMle98R8jcv7uffG8HAgwKeZolh+BbwIhALsQmTZZpo8q5Si68pZGsFwUBMHUS61vnGeA1O7zs+Ny", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "59b84a7dce1bf3366ef0a1cbb1f5aecc5eac89d3", "engines": {"node": ">= 0.10.0"}, "gitHead": "fd4fda598252c85546e2725ddbe702b91c257d0c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"hsts": "0.1.0", "connect": "3.3.3", "nocache": "0.2.0", "ienoopen": "0.1.0", "frameguard": "0.2.0", "helmet-csp": "0.1.2", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.1", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.0.1", "sinon": "^1.12.1"}}, "0.6.0": {"name": "helmet", "version": "0.6.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.6.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "81bfd3565e2e5c886b9144d0a77d1d3624b1005b", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.6.0.tgz", "integrity": "sha512-sMLMmeRxlyXvxehIC9k98GrYQRnOyh/mvMdS5dItmJT74xv30JW/sTNqQXwzEIdBufzzEaMyiZsWlTGm3VVSmA==", "signatures": [{"sig": "MEQCIDXB79xXoocvyWbePUrZHvYad5xfAQTLYi7MRPLs84M+AiBZYJMShjgKsHqb9Zr8bX6BvCC1BHhD/ZQ/o1g+crk6Zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "81bfd3565e2e5c886b9144d0a77d1d3624b1005b", "engines": {"node": ">= 0.10.0"}, "gitHead": "84d5cc73e98828383c5d3a13c60d78964f3db432", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Security middleware collection for Express/Connect", "directories": {}, "dependencies": {"hsts": "0.1.0", "connect": "3.3.4", "nocache": "0.2.0", "ienoopen": "0.1.0", "frameguard": "0.2.0", "helmet-csp": "0.2.0", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.1", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.1.0", "sinon": "^1.12.2"}}, "0.6.1": {"name": "helmet", "version": "0.6.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.6.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "f1e10be09ca197dc99f73b6ed43e9e19327c4c84", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.6.1.tgz", "integrity": "sha512-EIgmS3RlYsoe61PQNZ92ioo0gIBcnmgegm2P8jyeCH6FOvBx6J6bk2vePhv0QHIAlIdhDSe8IRd6piPA1K3Krw==", "signatures": [{"sig": "MEQCIEVGFf3QozESMvs68VE4nrW6SQ+M64p0WckgyazscT11AiAk/OXOGsEwOnQRexE4hDBz0Qmm1Zu0T1qQNJ5fOQ14ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "f1e10be09ca197dc99f73b6ed43e9e19327c4c84", "engines": {"node": ">= 0.10.0"}, "gitHead": "32eb649c3838d84b691947267e04709a796c2ded", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Security middleware collection for Express/Connect", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"hsts": "0.1.0", "connect": "3.3.4", "nocache": "0.2.0", "ienoopen": "0.1.0", "frameguard": "0.2.0", "helmet-csp": "0.2.0", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.1", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.1.0", "sinon": "^1.12.2"}}, "0.6.2": {"name": "helmet", "version": "0.6.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.6.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "88eddd3504c2acd237bfa54a41f54ed7fd218957", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.6.2.tgz", "integrity": "sha512-PGloWrY9vz/Bqn4KyViD+7Twc/BQr/KSj1w8vJWrZyIH/yYP3aFM6e5e31ONrip/uyZGNMTLVzhydsbF4sqV/A==", "signatures": [{"sig": "MEYCIQCEGVUUSbPU5fpQU2H8xV5XhQccQNbxCM5hqWJPmRfpjQIhAJr4iiV3Dvo7x3QYDVqhrljFLhuSJJJLJdgn1KTSws2v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "88eddd3504c2acd237bfa54a41f54ed7fd218957", "engines": {"node": ">= 0.10.0"}, "gitHead": "779816425abe1c599bcbc22eda94f69ec64ab10e", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Security middleware collection for Express/Connect", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"hsts": "0.1.0", "connect": "3.3.4", "nocache": "0.2.0", "ienoopen": "0.1.0", "frameguard": "0.2.1", "helmet-csp": "0.2.1", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.1.0", "sinon": "^1.12.2"}}, "0.7.0": {"name": "helmet", "version": "0.7.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.7.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "30995f76d0dab12d4e50c6dc4a021e87476e12c1", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.7.0.tgz", "integrity": "sha512-Jki1FnvJuGXhxcG82pXHEzWfbInOKnNM9qXa+eGdkhQ3/x8C63y3i/SjY59YynOyxcJtwOpopSW/YdJ/AsiC/w==", "signatures": [{"sig": "MEQCIBIINmhhmtoV+MnYn8WV6SsHmuKIjWjmW9rdWbZBkPVJAiACKtFcRE5yStpFB3DsbTiuL2eTIslfCYfjv1RA+7/2pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "30995f76d0dab12d4e50c6dc4a021e87476e12c1", "engines": {"node": ">= 0.10.0"}, "gitHead": "64f8cd807061f752da641bd8f3e3a3f2754715ea", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Security middleware collection for Express/Connect", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"hpkp": "0.1.0", "hsts": "0.1.0", "connect": "3.3.4", "nocache": "0.2.0", "ienoopen": "0.1.0", "frameguard": "0.2.1", "helmet-csp": "0.2.1", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.1.0", "sinon": "^1.12.2"}}, "0.7.1": {"name": "helmet", "version": "0.7.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "crossdomain.xml", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.7.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "83c503175dce0c17a6056764695728b07b0d901d", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.7.1.tgz", "integrity": "sha512-Ls/ukGu03GtlGGA/Y+7Li9ifz1QX+nlxASYuiT6tiV52yrJiVz+FZ6U12Ea4A3B+tSeXVGkemHz1/HMo2dDdqw==", "signatures": [{"sig": "MEUCIQDWZ6QXhaXCTbWTyIaIoMuSsi47rMN64RBgi/EP0D97vAIgQkhmDbjTahA+W72MyfaQ22bj9xi++K71bjjxjFQrGtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "83c503175dce0c17a6056764695728b07b0d901d", "engines": {"node": ">= 0.10.0"}, "gitHead": "013a135d475c547e7ddfab9d75a6423aa45b9ead", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Security middleware collection for Express/Connect", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"hpkp": "0.1.0", "hsts": "0.1.1", "connect": "3.3.5", "nocache": "0.2.0", "ienoopen": "0.1.0", "frameguard": "0.2.1", "helmet-csp": "0.2.2", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1"}}, "0.8.0": {"name": "helmet", "version": "0.8.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.8.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "a6c12e9e6f80611329a3b4af34fd075ebdaea7fe", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.8.0.tgz", "integrity": "sha512-E<PERSON><PERSON>3ZbU6EOZfEWMvCVevZQ95xbD6V9yJYYw4S7uO1wk7CR3e36JOnhWLTR7jLwnktwTHXUhPWkuOSamzlSf0Sg==", "signatures": [{"sig": "MEUCIBAIWnNaX0wNlFQDEg2tvdMF0dj8rQyl1IYjTNO2+4SaAiEAnJmbc6yz64v/DW7gMspL7rv/iI5xBH4gjpPaZbjqDVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "a6c12e9e6f80611329a3b4af34fd075ebdaea7fe", "engines": {"node": ">= 0.10.0"}, "gitHead": "e99bcb86e73a319463281e8aac9a914181900083", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Security middleware collection for Express/Connect", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"depd": "1.0.1", "hpkp": "0.1.0", "hsts": "0.1.2", "connect": "3.3.5", "nocache": "0.2.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.2.3", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "0.9.0": {"name": "helmet", "version": "0.9.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "helmet@0.9.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "40f278d7b249f79fee60c427dbabbd425aaf58b9", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.9.0.tgz", "integrity": "sha512-xjO13c0az/wPG57uvE3Syq/PHBxLNfgIZq/vsaVjp5/JbB3s70sWiUp+MDqoVAYHsd36sARXo4yJQaJhuGLIsw==", "signatures": [{"sig": "MEQCICPIPvSjg0WA1wCUN0jSWBnVMf1qskuyRAza/+OgrOgNAiAYxuFpPv1VAnJr1yjrzYvDjKjak7v/7Vo+jDDxYh3sdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "40f278d7b249f79fee60c427dbabbd425aaf58b9", "engines": {"node": ">= 0.10.0"}, "gitHead": "57f9cc548d0db5d4389536b70f73fc86ae239c8c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"depd": "1.0.1", "hpkp": "0.1.0", "hsts": "0.1.2", "connect": "3.3.5", "nocache": "0.3.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.2.3", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "0.9.1": {"name": "helmet", "version": "0.9.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@0.9.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "99512e3b21d7170d65679279adfb681810569707", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.9.1.tgz", "integrity": "sha512-2MbSvPYInL3dtegGLugTsVvsNkLGUnT612ZiEx0AriKMwtpC7u0UQSHq7GqmVVAG9f8TCMRtrPquI8xzSZ0m8A==", "signatures": [{"sig": "MEYCIQDF2hXpIC4s5zykxV5U5XOxE2Bz6JPcXTm3Oai6jjqRqgIhAJB0Fjzax09HcIABLv5GViqh/7LxOJafGJpy3Q2rnCR9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "99512e3b21d7170d65679279adfb681810569707", "engines": {"node": ">= 0.10.0"}, "gitHead": "ba60bc2fb936ffdc2d3241b7128b78b4ec5e7b1f", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"depd": "1.0.1", "hpkp": "0.1.0", "hsts": "0.1.3", "connect": "3.3.5", "nocache": "0.3.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.2.3", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "0.10.0": {"name": "helmet", "version": "0.10.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@0.10.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "0ac5a3f6887a565403f903feb7df790398285753", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.10.0.tgz", "integrity": "sha512-2QkG2c8jb2g3zios98aYpKyKo/KaNOp14aA266uNAbHZABkUmwqJuVcpmic6ko8zvMaWybqkQ6Fi+M7bGAVW6Q==", "signatures": [{"sig": "MEUCIGXHRywsMf5TRuXeC281F0Zs4AAeBqWhYP9AV2J9aK9VAiEAjuYY/Y8c3K4wFPikP/IdgxPtS28LPiDE9a1D40b1P60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "0ac5a3f6887a565403f903feb7df790398285753", "engines": {"node": ">= 0.10.0"}, "gitHead": "4fd31da7bd7f010de34ede3db56eecac4b39c300", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.12.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"depd": "1.0.1", "hpkp": "0.2.0", "hsts": "0.1.3", "connect": "3.3.5", "nocache": "0.3.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.2.3", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "0.11.0": {"name": "helmet", "version": "0.11.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@0.11.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "1fbbc8debb7ea579183f01c23a25cd0f4719f98c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.11.0.tgz", "integrity": "sha512-kZNmrLnbOcaCxuhC/lz4VScw1B2A4Tz3T9Dj9OlgNaV9LQFEs+ob4Ias4A1LO3409VI35WNwB8HkSQJs8rhn3A==", "signatures": [{"sig": "MEQCIFoomJs7OVCyDnivGOToABdCEOFCCbWubyGaD9EfePWbAiAP2Bd/zblK/FxIbddAQiTqUHhNIco3lLYAw6yM0zwueg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "1fbbc8debb7ea579183f01c23a25cd0f4719f98c", "engines": {"node": ">= 0.10.0"}, "gitHead": "5e2110b06c69e201290179fa78422d7baa314e9c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {"depd": "1.1.0", "hpkp": "0.2.0", "hsts": "0.1.3", "connect": "3.4.0", "nocache": "0.3.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.3.0", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "0.12.0": {"name": "helmet", "version": "0.12.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@0.12.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "efe9054513386f5665bab0877bd36cc6eff3a00c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.12.0.tgz", "integrity": "sha512-XDTDUZDGY0tZmwLM/dm7qBOgu2WQyBsqOF05zBOSvGdnSp6X7LXoy7pJDmt2pyD4SpeWfL+uPoiluo/+X0sXkg==", "signatures": [{"sig": "MEUCIQC6F5z/XQanPC60jMYcnXT/O33XGJsGZd0yMp/mu+bmjQIgAxX7+N7g6mgvsueu8NMXWmdvsqFMrrrEqr+duTUj7uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "efe9054513386f5665bab0877bd36cc6eff3a00c", "engines": {"node": ">= 0.10.0"}, "gitHead": "6158182ab76a85a808a29a597edbed9b4bb795fc", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {"depd": "1.1.0", "hpkp": "0.2.0", "hsts": "0.2.0", "connect": "3.4.0", "nocache": "0.3.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.3.0", "hide-powered-by": "0.1.0", "x-xss-protection": "0.1.2", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "0.13.0": {"name": "helmet", "version": "0.13.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@0.13.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "77fbaa37da57977f00af4e9d5fe4a1691e3e87bc", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.13.0.tgz", "integrity": "sha512-pB2PQ4kgcvTo2rF112xyiHX//qzsKiClYzOivVyQcFJaOCN7yBBerSqKrXqE3ZUru84gU+jZjwdTqUuWeQOV4Q==", "signatures": [{"sig": "MEQCIHOxPhtk9aunyovxNrBTAsh/gV3dhepWcsUzJvn1vZilAiBlXfG4h7fFKXvtKVpvIecnWhl1PML4NQXk3J6FSP5m9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "77fbaa37da57977f00af4e9d5fe4a1691e3e87bc", "engines": {"node": ">= 0.10.0"}, "gitHead": "0802c5a6db34eb6e882332526252d5e42cc562ed", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"depd": "1.1.0", "hpkp": "0.2.0", "hsts": "0.2.0", "connect": "3.4.0", "nocache": "0.3.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.3.0", "hide-powered-by": "0.1.0", "x-xss-protection": "0.2.0", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "0.14.0": {"name": "helmet", "version": "0.14.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@0.14.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "1f3432d056e0398b5987a8af9aef05713fe8f1b7", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.14.0.tgz", "integrity": "sha512-ljNI6ZO09zJC54gQBYnuBcfpQYf2ArMQEs2RTNmBd0CWP5IBRdSfU/44kngdn45koJlDTpHEindk/jZS5l6XUw==", "signatures": [{"sig": "MEYCIQCbinSxjmgG3e8Ih4bOWeiNOBGgyJpS8v6QaaCK7ZhsUQIhAP+OkXKoXbh/MSsLPKM35eb3AaXVEQlsUf73iClDFbYl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "1f3432d056e0398b5987a8af9aef05713fe8f1b7", "engines": {"node": ">= 0.10.0"}, "gitHead": "22ba881f14255e0ec23dbbaaa15905ef9506bf7c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.3.10", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"depd": "1.1.0", "hpkp": "0.2.0", "hsts": "0.2.0", "connect": "3.4.0", "nocache": "0.4.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.3.0", "hide-powered-by": "0.1.0", "x-xss-protection": "0.2.0", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "0.15.0": {"name": "helmet", "version": "0.15.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@0.15.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "05178a6ca779b98244706c810d0eb6e701ce099d", "tarball": "https://registry.npmjs.org/helmet/-/helmet-0.15.0.tgz", "integrity": "sha512-DlnZbzgxCdw2dwfEQuLtcSU28zFTSoNqfIx2aGYmIldthVN8nwYPIpEtpQSAbGETpUBCkGbxImqgCd9e0+gI4A==", "signatures": [{"sig": "MEUCIQDoS0VNo2iA9+5COwdrJqsA61jDSlGvBHSfjoFrCyMrDwIgfurjqE6k0wWpQ/jZ29TPPV+Kl3xucuczBIyHr+gSSzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "05178a6ca779b98244706c810d0eb6e701ce099d", "engines": {"node": ">= 0.10.0"}, "gitHead": "364700593b03880446987ac3a79b601e2b907b7d", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"depd": "1.1.0", "hpkp": "0.3.0", "hsts": "0.2.0", "connect": "3.4.0", "nocache": "0.4.0", "ienoopen": "0.1.0", "frameguard": "0.2.2", "helmet-csp": "0.3.0", "hide-powered-by": "0.1.0", "x-xss-protection": "0.2.0", "helmet-crossdomain": "0.1.0", "dont-sniff-mimetype": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "proxyquire": "^1.4.0"}}, "1.0.0": {"name": "helmet", "version": "1.0.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@1.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "240ff4cd559c340ac1f2802cb819b2ef5db30e4a", "tarball": "https://registry.npmjs.org/helmet/-/helmet-1.0.0.tgz", "integrity": "sha512-pNfZmy6u4QzxUq5JoobJu0C/fHGPSpD0PglmFUJ74gBCB/bgKHqg4d9ZDa2mG6cDHVYX9i9AP1I7lHNuv1+WDg==", "signatures": [{"sig": "MEUCIHwcUnar9Xna5eyhmIytIJlI7Jw4NiJI9gprRvshZuaFAiEA4R3pHXF2RHl5NoaVvaSOcXHSWhP5/xF4KcSqZJPWedo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "240ff4cd559c340ac1f2802cb819b2ef5db30e4a", "engines": {"node": ">= 0.10.0"}, "gitHead": "141ffc091a3b8a568fd80fb1250b0476dc19466f", "scripts": {"test": "standard && mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"hpkp": "1.0.0", "hsts": "1.0.0", "connect": "3.4.0", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "1.0.0", "helmet-csp": "1.0.0", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "standard": "^5.4.1"}}, "1.0.1": {"name": "helmet", "version": "1.0.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@1.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "2b89584d6ecc6e2efb494ee003af5ed0eabb4a59", "tarball": "https://registry.npmjs.org/helmet/-/helmet-1.0.1.tgz", "integrity": "sha512-Fmi9kiMsGpMnTt9LlnxZ/lrmxWbbNt1zd/fiB8/EbYt+4XJtY2GnDJArXoj/GAZemCG1GRuEs1L8bn6fmyuPdQ==", "signatures": [{"sig": "MEYCIQCHL53GK+L3HAjtm48dd8b576dC+ANXhbSpwQkIDXkqswIhAPjbR1R27VyGXkWXD/E1sepHtiKpwbTSnXlaXfMDHXP+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "2b89584d6ecc6e2efb494ee003af5ed0eabb4a59", "engines": {"node": ">= 0.10.0"}, "gitHead": "6974d2ce05ec9eb576b194a026fd146a1106c5b9", "scripts": {"test": "standard && mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "5.2.0", "dependencies": {"hpkp": "1.0.0", "hsts": "1.0.0", "connect": "3.4.0", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "1.0.0", "helmet-csp": "1.0.1", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "standard": "^5.4.1"}}, "1.0.2": {"name": "helmet", "version": "1.0.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@1.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "45ef94709ccc4221c86dfe3966b64835f399ae69", "tarball": "https://registry.npmjs.org/helmet/-/helmet-1.0.2.tgz", "integrity": "sha512-8CsX4sX9wbuKgM1APbWZf6uIV6k3iHvqc1iShr2U+nK2WoJwbaxtP7V6MiXoJVu76GOXctELkvgFUgLYqvAffA==", "signatures": [{"sig": "MEYCIQDrrhMi7GAPWwUDyGk+DstkXXAJe1KuQuy6odV/Q5lVMwIhANgHZMhiEcr78lSYlnpMMLo6+77DFzUpThN4b7kRKa4F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "45ef94709ccc4221c86dfe3966b64835f399ae69", "engines": {"node": ">= 0.10.0"}, "gitHead": "4c53767515a9495e1d4c1cb5f5b65b8fdaf365b4", "scripts": {"test": "standard && mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"hpkp": "1.0.0", "hsts": "1.0.0", "connect": "3.4.0", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "1.0.0", "helmet-csp": "1.0.3", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "standard": "^5.4.1"}}, "1.1.0": {"name": "helmet", "version": "1.1.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@1.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "1dd59e6b4568fdbe4abbdb60cb4174eb033829a6", "tarball": "https://registry.npmjs.org/helmet/-/helmet-1.1.0.tgz", "integrity": "sha512-OxFOf9lIvemtcbPDMRUOZ88kZBpDiRRVEqWT/Rhdq3+zQR5hyAvlfgvy67uiGY6l+1qMhSS6+zacP69zgeqn7w==", "signatures": [{"sig": "MEQCIE8VRJwEzxQ2JFaKxernN6jta0qbefUNIky7iLCxftLZAiA9kwxHUYtxvALYDcyWMHOqwQsH4VyEGipA/t9KpfOV5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "1dd59e6b4568fdbe4abbdb60cb4174eb033829a6", "engines": {"node": ">= 0.10.0"}, "gitHead": "837ba110b536cdb3f85598ba805e64368ce52fc1", "scripts": {"test": "standard && mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.5.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"hpkp": "1.0.0", "hsts": "1.0.0", "connect": "3.4.0", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "1.0.0", "helmet-csp": "1.0.3", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.2.1", "sinon": "^1.14.1", "standard": "^5.4.1"}}, "1.2.0": {"name": "helmet", "version": "1.2.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@1.2.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "af66ea539d34bc148b336af2bf485eff09389edf", "tarball": "https://registry.npmjs.org/helmet/-/helmet-1.2.0.tgz", "integrity": "sha512-OhRr3OehadCam10Hwc1ownUVhHUkXuYVhJ5xNDVpVsnga/usX9h/aP0rF8vVLElvqTnVnI8cipMLSKaKWdDn0w==", "signatures": [{"sig": "MEQCICKt1W+mdDp0FcG11kzPKHhVdkO9pkrrKbGRpb8E9eTNAiBa0ThTlp4UK4P8rsJaXRb7vTNB87vMPHU/Zc3x3RxxJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "af66ea539d34bc148b336af2bf485eff09389edf", "engines": {"node": ">= 0.10.0"}, "gitHead": "263de4902cf917d01491f562563754d46bc0053d", "scripts": {"test": "standard && mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.7.5", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "5.7.0", "dependencies": {"hpkp": "1.0.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "1.1.0", "helmet-csp": "1.1.0", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^6.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-1.2.0.tgz_1456758561869_0.0049660042859613895", "host": "packages-5-east.internal.npmjs.com"}}, "1.3.0": {"name": "helmet", "version": "1.3.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@1.3.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "e1b59c5484f7ac081a48cc7634139b4ec38cf8b5", "tarball": "https://registry.npmjs.org/helmet/-/helmet-1.3.0.tgz", "integrity": "sha512-UfOa3K1Dz1amvpWdXv/k1MuAk+YTFZ2Wlj/g6feqJfpUor9vSawwBbfWfpLnUs5r7gSNs7tGk++HkjlzDLJSvw==", "signatures": [{"sig": "MEQCIFBhbvrjQMKkFe0jMc2Yi/yo5Nv2i8HAJPMxMldvrxjsAiBFQwLcs3QBKVwBm0yDemEAed6Ty0qpnFUkykSjuIPejw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "e1b59c5484f7ac081a48cc7634139b4ec38cf8b5", "engines": {"node": ">= 0.10.0"}, "gitHead": "aaf9130997ece10ec5cf03b4d6a60f730c016bc2", "scripts": {"test": "standard && mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.7.5", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "5.7.0", "dependencies": {"hpkp": "1.1.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "1.1.0", "helmet-csp": "1.1.0", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^6.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-1.3.0.tgz_1456881859978_0.06553985876962543", "host": "packages-11-east.internal.npmjs.com"}}, "2.0.0": {"name": "helmet", "version": "2.0.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@2.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "5c63e6133bd5297a879cb03d0347631d3af72722", "tarball": "https://registry.npmjs.org/helmet/-/helmet-2.0.0.tgz", "integrity": "sha512-2CIOltOGo3Bb5sVbGuG/brHIEmlCEHfpdUc3KAvHso6BDOSVhSuCgbWqr3GEx3Uyz7yNdTXtYypsr55WM2TVGw==", "signatures": [{"sig": "MEUCIQCUyaOHubSjJq+Ec3hlggUxNDPy4kDkbFJXHWDPB3O6CQIgAkWX3hY0VKqwGc4i7d8tdZsvh+4tkZ4YUk9POUTd+xk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "5c63e6133bd5297a879cb03d0347631d3af72722", "engines": {"node": ">= 0.10.0"}, "gitHead": "9c8f0473872c3d1caef218ae947a16974d23b8fb", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.8.8", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "6.0.0", "dependencies": {"hpkp": "1.1.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "2.0.0", "helmet-csp": "1.1.0", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^6.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-2.0.0.tgz_1461954215046_0.3089020897168666", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.0": {"name": "helmet", "version": "2.1.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@2.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "03504494611d2c5f5585c491aa398254ee9ffae4", "tarball": "https://registry.npmjs.org/helmet/-/helmet-2.1.0.tgz", "integrity": "sha512-5ZwYZf4EciYQnS2+lHHYjDLlrEsB5TlhSDfx3CVMd7TaoN4c8lLmWuLWMzhEpv0EHx1cukKiqSDKoQQ7tRUrKw==", "signatures": [{"sig": "MEUCIQChJLp/68jUedrVKLUKev2wTG2u0pvTq3fnD7ZdVy/MOgIgOKp0mlptioLzeVs8ldRjG9lMlqgwC4Q11urUWAIx+Pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "03504494611d2c5f5585c491aa398254ee9ffae4", "engines": {"node": ">= 0.10.0"}, "gitHead": "c840e440d075e82fee279cab353d8ba15a42bce2", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.9.2", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"hpkp": "1.1.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "2.0.0", "helmet-csp": "1.2.0", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-2.1.0.tgz_1463615882283_0.1518576426897198", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.1": {"name": "helmet", "version": "2.1.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@2.1.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "b2cae8b0f9975124970e7a542f1eaddce0db34c8", "tarball": "https://registry.npmjs.org/helmet/-/helmet-2.1.1.tgz", "integrity": "sha512-x8fz/FEFwyi3F+tIcc7ey1sCrPsUTm2UFm27Qv5LGaik1cMWFUDIoT5GTdXr61NU2Wwc7zfmXhuVue0lTq3AZA==", "signatures": [{"sig": "MEYCIQCuGsYm/f0rmlaXNcaMU/cec8XfLqE3bzGUWziHsM3XmQIhAIT3FnrHT2dXh47lm7CpUbGhE5vvkrL5ikbwQ1/QN20b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "b2cae8b0f9975124970e7a542f1eaddce0db34c8", "engines": {"node": ">= 0.10.0"}, "gitHead": "b6d6c32f20a52457a8946755e77d0b712c474751", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.9.6", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"hpkp": "1.1.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.0", "ienoopen": "1.0.0", "frameguard": "2.0.0", "helmet-csp": "1.2.1", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-2.1.1.tgz_1465592161808_0.7241842993535101", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.2": {"name": "helmet", "version": "2.1.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@2.1.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "c9e213e8cbe3ed7d9ed816581de0dc2e7469a9cd", "tarball": "https://registry.npmjs.org/helmet/-/helmet-2.1.2.tgz", "integrity": "sha512-uJOSpBAcB6Tn3Rxr0Gto6OuhzkQor26hFlRJnm8fWK2/AyvFGk5hBWQQWNrukM0JnEk0hRku9fONtpy5ZzGpvw==", "signatures": [{"sig": "MEUCIHkGtF6n11yuN8hIKfvsorrQtpEQdi5f/RvUSUOZKC0fAiEAk/1lhDHDi7KNY1EUHqwjML+eTlm8McfahTTdDbVbMXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "c9e213e8cbe3ed7d9ed816581de0dc2e7469a9cd", "engines": {"node": ">= 0.10.0"}, "gitHead": "df23283e465de52e1a38ee3b2acc5a46d8ba0e8b", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"hpkp": "1.1.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.1", "ienoopen": "1.0.0", "frameguard": "2.0.0", "helmet-csp": "1.2.2", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-2.1.2.tgz_1469644494525_0.8797276350669563", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.3": {"name": "helmet", "version": "2.1.3", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@2.1.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "2e51be8497bee3bbcff29d09d48592ba8cd20994", "tarball": "https://registry.npmjs.org/helmet/-/helmet-2.1.3.tgz", "integrity": "sha512-q+LrgAvLh4HAkYwqd8PI6nz0yi9N43w310/ZaUSna9KrwW214PmZp8uW9VSB5qFHHMbte6SIZGav758kpYFPgw==", "signatures": [{"sig": "MEYCIQDpymQDg6bxcrHhVu8IZWzwgrdx58La/NzEO4Lj9hM9cQIhAIewx1iWn0uCQewHgzA/P17nodZH1gdi2qLVlI+oFFvm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "2e51be8497bee3bbcff29d09d48592ba8cd20994", "engines": {"node": ">= 0.10.0"}, "gitHead": "07809492edb53309535f673d22e4b4bffbf01a92", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "6.4.0", "dependencies": {"hpkp": "1.1.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.1", "ienoopen": "1.0.0", "frameguard": "2.0.0", "helmet-csp": "1.2.2", "hide-powered-by": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-2.1.3.tgz_1473260025059_0.4917513767722994", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.0": {"name": "helmet", "version": "2.2.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@2.2.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "fa0737d113fba4bd29d1b39650ac679ad673b948", "tarball": "https://registry.npmjs.org/helmet/-/helmet-2.2.0.tgz", "integrity": "sha512-nJf2+VTKIv+8NIQ2QA/CnStusceA4aLTiYq/nyzXCQAIx7UhNtAf/53l4KkrjeEYZ5jriFEtE4ihbBiPhvHTLQ==", "signatures": [{"sig": "MEYCIQC/UBPAuKfRoZ7iy5Uqv6hoNAE8e+Hvasg8u5bcILuT3QIhAM7zVJz8JzoPN66oyMakjIhcB/FYLJasyb7hUylaWhVZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "fa0737d113fba4bd29d1b39650ac679ad673b948", "engines": {"node": ">= 0.10.0"}, "gitHead": "54597f55ebd2f05a1bff3b4fb8d8a55e7f0d54ca", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"hpkp": "1.1.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.1", "ienoopen": "1.0.0", "frameguard": "2.0.0", "helmet-csp": "1.2.2", "hide-powered-by": "1.0.0", "referrer-policy": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-2.2.0.tgz_1474055762981_0.22089014295488596", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.0": {"name": "helmet", "version": "2.3.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@2.3.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "d655c85b55b0a3bf722a4c2c66e48b78b4161b91", "tarball": "https://registry.npmjs.org/helmet/-/helmet-2.3.0.tgz", "integrity": "sha512-SUwlUg+fUYBuUa+BY+miNTtJbGStsfo4BTvqHzZyktpPL4/u5hxSZQPFw+e2q2SUQczTbaRPtltoRwcJiEgPmQ==", "signatures": [{"sig": "MEUCIQDyxw5R/K4WBmXjdiOw12p16U8irVkswAjYr71HiVLgQAIgEykIR1iJgSjlo5IB24GkysDLEed93Mc3cAsqukJpxhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "d655c85b55b0a3bf722a4c2c66e48b78b4161b91", "engines": {"node": ">= 0.10.0"}, "gitHead": "f694279774150bce7fe2d39b08dae786fdd91adc", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"hpkp": "1.2.0", "hsts": "1.0.0", "connect": "3.4.1", "nocache": "1.0.1", "ienoopen": "1.0.0", "frameguard": "2.0.0", "helmet-csp": "1.2.2", "hide-powered-by": "1.0.0", "referrer-policy": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-2.3.0.tgz_1475275979265_0.7096879319287837", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "helmet", "version": "3.0.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/helmetjs/helmet#readme", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "8f7753987493ac210fd0d9177f0ed6d91bce6a5d", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.0.0.tgz", "integrity": "sha512-qImDOeYSwqPgCuKvj07DO/WSRQ5eXbLyNRURKSnZW4B899lhJVxOKQhBxKR/2AWp8Zf8wes36h/Ked/tQxNNyA==", "signatures": [{"sig": "MEUCIF6yabnMkrxAV1Jpw5tLdBw1IA/zzvMFLVXSFMabojiCAiEAnUaCGA6U9gDrfyEk7FYMPg+orI5RIqxB3E2k1+z/kIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "8f7753987493ac210fd0d9177f0ed6d91bce6a5d", "engines": {"node": ">= 0.10.0"}, "gitHead": "4620816723ce9f3403c8f8fae3929cd7544e8e0b", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "4.0.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.4.1", "nocache": "2.0.0", "ienoopen": "1.0.0", "frameguard": "3.0.0", "helmet-csp": "2.0.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.0.0.tgz_1477688635709_0.4980172235518694", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.0": {"name": "helmet", "version": "3.1.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "64449547398e51b063fe1c75e7cb0274a557ea09", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.1.0.tgz", "integrity": "sha512-Ec9fRJ9CsIExXP3M/uhSMcyK7pHUx9pATInHTxoWM8I/RAi8sbaOwNFAWf+kPI1VIMA8rjUbhq4SCvhCuR2bAg==", "signatures": [{"sig": "MEUCIHbP5XaNLgUy9gOYKDorVKfHg1p6BwJczwAKzgQZKQqsAiEAvG9xeuz75G4hPKmdpM6SYHBS7K5qURJUZtQRw/+2u2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "64449547398e51b063fe1c75e7cb0274a557ea09", "engines": {"node": ">= 0.10.0"}, "gitHead": "6d81663e1fd885a8073681d9cb3113ff32014874", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "4.0.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.4.1", "nocache": "2.0.0", "ienoopen": "1.0.0", "frameguard": "3.0.0", "helmet-csp": "2.1.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.1.0.tgz_1478207483553_0.017537688370794058", "host": "packages-18-east.internal.npmjs.com"}}, "3.2.0": {"name": "helmet", "version": "3.2.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.2.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "7ca9172c91c01ca792eee2ed350a030b1d1ea0ce", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.2.0.tgz", "integrity": "sha512-+d3V8aYA/40cmZM+gjraY/jeY5zgXflHOnsfKaer2A7y7XqZlJffX4u9dxX1NmIAgqobLyRgqmXOZ6mcE0+sdg==", "signatures": [{"sig": "MEQCIBf6ZHAfZ65vOSI+dsjlACXsEUPp1K9eKMP6NYViC4jVAiBTAclK/S/9I2jquLpbY7V8+lTMfAAerjTvA4iSewyc5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "7ca9172c91c01ca792eee2ed350a030b1d1ea0ce", "engines": {"node": ">= 0.10.0"}, "gitHead": "de3cae2029495659885075d2a46c6ba44a623897", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "7.3.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.4.1", "nocache": "2.0.0", "ienoopen": "1.0.0", "frameguard": "3.0.0", "helmet-csp": "2.2.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.0.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.2.0.tgz_1482431130521_0.01673686201684177", "host": "packages-18-east.internal.npmjs.com"}}, "3.3.0": {"name": "helmet", "version": "3.3.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.3.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "8eec02d8191ba97a6bdeccbf35f38326285136fd", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.3.0.tgz", "integrity": "sha512-zYrkMwXHIvN1m3H1ghwSAg6ljeP3oS62Nk9RfFfJh57JWpT7Uv4FJl7T62/LIKqiupP0jtl1MeHr8I+gncKtYg==", "signatures": [{"sig": "MEYCIQDHtWSLJHS7lUx0Wwx5Q/jRWVXuBZvnRwTRvRSRXdBD5AIhAILA4GIc8roKqeRFFeJMP0z18noTwca4ysNJuwfcKYW6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "8eec02d8191ba97a6bdeccbf35f38326285136fd", "engines": {"node": ">= 0.10.0"}, "gitHead": "5e6f31fa190f775f202fbca356a03861eb1cff2d", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.5.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "frameguard": "3.0.0", "helmet-csp": "2.2.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^2.4.5", "sinon": "^1.17.3", "standard": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.3.0.tgz_1483222151331_0.7482182041276246", "host": "packages-12-west.internal.npmjs.com"}}, "3.4.0": {"name": "helmet", "version": "3.4.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.4.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "05a9437486b05ca219ed8d21dc5e3b6ec0c18118", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.4.0.tgz", "integrity": "sha512-AD+Hpq2GGkrJn+ctmDlQpdWB/B90u5g7Yvp1TG1L5C7cDlu+EOOj9BZ73LBheCouz3Tv6DVCbA9SNdLqt26DOg==", "signatures": [{"sig": "MEYCIQDaALqCUm50aRUCgp6KmMEL0LRcRyHNzMQdkyHCIiyWLAIhANT3TIcHkMBLU+VeIb1Gd9BTZBOBbXCeURrSSt2hFbTH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "05a9437486b05ca219ed8d21dc5e3b6ec0c18118", "engines": {"node": ">= 0.10.0"}, "gitHead": "5160ad235eb3f7395e396a5a08cd5381d16c004a", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.5.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "frameguard": "3.0.0", "helmet-csp": "2.3.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^1.17.7", "standard": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.4.0.tgz_1484351878027_0.7850308339111507", "host": "packages-18-east.internal.npmjs.com"}}, "3.4.1": {"name": "helmet", "version": "3.4.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.4.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "27d37629227f25a110f2a128bfe1b1028648a397", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.4.1.tgz", "integrity": "sha512-gxKiltjy5Pm11HBBLvo3DB5+ehJxVotE7Rqu9zoN74jkC352e3Ila/lAg/YxIxFeBeTxCg+2q8X4h56jW3NuNA==", "signatures": [{"sig": "MEYCIQC1HDMt7SVXEX8l69R3f5+L69OwxZJPy8aeGKtdIa9aoAIhAPzDrsDAM+qrzwOdXAFNZ6fYfbsUnI95cRcJpK3I+hsZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "27d37629227f25a110f2a128bfe1b1028648a397", "engines": {"node": ">= 0.10.0"}, "gitHead": "c18c03cc90bd7ad6c961e8473dbb021ea74af7ca", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "4.3.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "7.6.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.6.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "frameguard": "3.0.0", "helmet-csp": "2.3.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^1.17.7", "standard": "^8.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.4.1.tgz_1487983836091_0.9630669991020113", "host": "packages-18-east.internal.npmjs.com"}}, "3.5.0": {"name": "helmet", "version": "3.5.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.5.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "e1d6de27d2e3317d3182e00d672df3d0e1e12539", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.5.0.tgz", "integrity": "sha512-hUBGtDjUNoqgYcf1R2XYkh/nrFRBvSBSQUPClpkz1b/U/lKldgkyQ1Bxta2mnhOBdgT75aVVmiIstPG6OI34LA==", "signatures": [{"sig": "MEUCIHWZEhL0COww8nFAELxdqIA7Oq9xKdMXIGIl+3R7nKX0AiEAxH2WljyC/plp2bmQ9dgwRdU64PypvZN6VMQK/6bw5pA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "e1d6de27d2e3317d3182e00d672df3d0e1e12539", "engines": {"node": ">= 0.10.0"}, "gitHead": "ebd0d35495470708be42750204060a7f96bbd01d", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "4.4.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "7.7.1", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.6.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "frameguard": "3.0.0", "helmet-csp": "2.4.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^1.17.7", "standard": "^9.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.5.0.tgz_1489081709227_0.882290153298527", "host": "packages-12-west.internal.npmjs.com"}}, "3.6.0": {"name": "helmet", "version": "3.6.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.6.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "cf391ecdba9c8a8ee7aec66cff4f147797e204d3", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.6.0.tgz", "integrity": "sha512-1Hh0OMBOczB1p53gzqEykybF0d1ZN7Rw5BA6JfNlyNKv1j/ymBM6DotmkDp69AUjdgiDtSMjjYIfqagKZo8MqQ==", "signatures": [{"sig": "MEQCICwvj51CWpBd3PYrY/3HZysJ/P9dSqIp03oesuqBSnBaAiA/+B7h7sT/6/Ltp2wHBQmGxZv5APpsd2QTYwHeTVnI4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "cf391ecdba9c8a8ee7aec66cff4f147797e204d3", "engines": {"node": ">= 0.10.0"}, "gitHead": "3d76e64724c4933f005aace744f7225823656cc4", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.6.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.4.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^1.17.7", "standard": "^9.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.6.0.tgz_1493938133460_0.7470406317152083", "host": "packages-18-east.internal.npmjs.com"}}, "3.6.1": {"name": "helmet", "version": "3.6.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.6.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "91f3aa7fa4c94671595fb568dfd8c28489a388be", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.6.1.tgz", "integrity": "sha512-5PjENSZImoaduNI9CyRf5n0/dUWESSWwzPR7FtuxI3McqswLBjMJ2IPKu94BjlA95dVsdgo/fGktgQCSaPxpSw==", "signatures": [{"sig": "MEUCIFAanvhJIQsT9wcAZuSfs/Uu9WK5SxU80yI9YAiJXgjWAiEA18ha0iTq3K9fa5Y+ZPZP6lzEHGnIdiiXpoBC+ChI+F4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "91f3aa7fa4c94671595fb568dfd8c28489a388be", "engines": {"node": ">= 0.10.0"}, "gitHead": "60db9c568708fede85b8140cb9cde3f8e7c5e41c", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.6.2", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.4.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^1.17.7", "standard": "^9.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.6.1.tgz_1495405342070_0.26339506451040506", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "helmet", "version": "3.7.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.7.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "8a90d47ee47f8f6c7d267152bb8429e5ad4de809", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.7.0.tgz", "integrity": "sha512-UxDqio4BEgqcl4HtuFQFKLw70573qDiX03/89lHr8JOs/U5c0EJergK0eePAVZfyuLLeEqUNqjW8SYKWkOYxrg==", "signatures": [{"sig": "MEUCIGE2LXkBC8I84RSUI9We1bgksGEqpmhKiBI24fYtcbXEAiEA7gLMWVevJBDTV4clXkLiHxp0ZS8OoHjngXHW267AL8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "146594fa67e1ab958fe5d405c9b497aae7341ff7", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "8.2.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.0.0", "connect": "3.6.2", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.5.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^2.2.0", "standard": "^10.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.7.0.tgz_1500653872674_0.604413230670616", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "helmet", "version": "3.8.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.8.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "bba0bfeebe4832967188e4cd233a3d444903e1a7", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.8.0.tgz", "integrity": "sha512-66RfO0koYNDIX94d8iXc0Fg+EfuQ2fvDr104bTiCno3RyD+igovNOFSe9F/X3ROrcCceLYQgxRSFD5P7fSveuA==", "signatures": [{"sig": "MEUCIDxn+BJR6MX0mL8exkzz2uKApNwaDX5wq3BF8I9VGSWzAiEAjUMYFfZ+KUa0H0+aMhGCdjsp0pe4+aykk4YP8GsZDzo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "3ca899155e80d73b62b350225f42119e0e86d612", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "8.2.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "connect": "3.6.2", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.5.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^2.2.0", "standard": "^10.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.8.0.tgz_1500668697399_0.7737908675335348", "host": "s3://npm-registry-packages"}}, "3.8.1": {"name": "helmet", "version": "3.8.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.8.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "bef2b68ffbaa19759e858c19cca7db213bb58b2d", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.8.1.tgz", "integrity": "sha512-HzcpQ74kE1gNFvTd8fI/Nz2N0b0Aa/38dSiSVt/ijkwjc50tUp5siXTE9lTBibQ4JlRzp/35Qf+j2bZgHYwg1g==", "signatures": [{"sig": "MEUCIGmLGjfjtJjtKcC4sMj5NujWaex54t3gsugnJokgvnfAAiEAnAfObCYMQpwmZL2xjOge+AN6kjHXsMqI60OqdwPB/b0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "5587ecc927cd53392c366457a8d856c421b1cba7", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "connect": "3.6.2", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.5.1", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^2.2.0", "standard": "^10.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.8.1.tgz_1501262889653_0.11929011764004827", "host": "s3://npm-registry-packages"}}, "3.8.2": {"name": "helmet", "version": "3.8.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.8.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "64f988b8e9d8773ad201da455b8b6a754c229aaa", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.8.2.tgz", "integrity": "sha512-JMxSuCPSYgzOIlb19clar2XFuoGR0hv6VGgMEY2oyP9ZuHCZYPPBCbVGFA8holYQOshf7tAETRi5bsGQPYFb1g==", "signatures": [{"sig": "MEYCIQDxytJLDc+z+/3GSywd7iApJkIe1zAOmj/ahFdFClzZIwIhAP3A+z2nP11BwyJNF+mBY4hY2tTs6SQzy4SZP7Y+jrxz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "c2d0810cd84305c990316c8b0d7f08aa590256ca", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "connect": "3.6.5", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.5.1", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^2.2.0", "standard": "^10.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.8.2.tgz_1506521438664_0.7718416952993721", "host": "s3://npm-registry-packages"}}, "3.9.0": {"name": "helmet", "version": "3.9.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.9.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "7b2cf015a2d109bca83ede7924420799c0e67dee", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.9.0.tgz", "integrity": "sha512-czCyS77TyanWlfVSoGlb9GBJV2Q2zJayKxU5uBw0N1TzDTs/qVNh1SL8Q688KU0i0Sb7lQ/oLtnaEqXzl2yWvA==", "signatures": [{"sig": "MEYCIQChdtGzbfy+a4Yw8pFVLhYHCXsBluI6Zm0UHZbztN+DKQIhALf4Zjns3Ckl91g0HhB1QG9e33H/Tuwfwno7iWCfoZEA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "27029cd46a43bf526c66c62b3f2bd49e5fb4cff7", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.6.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^3.2.0", "sinon": "^2.2.0", "connect": "^3.6.5", "standard": "^10.0.2", "supertest": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.9.0.tgz_1507909395806_0.3425999120809138", "host": "s3://npm-registry-packages"}}, "3.10.0": {"name": "helmet", "version": "3.10.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.10.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "96a2a9fec53c26009d3d6265c6cfdada38ddfa7f", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.10.0.tgz", "integrity": "sha512-wVu5jSeImztLqNQPc4hqGr1DG0Ki2UJVmQ1KTugIrtl1f4Zw5SqVqh6QPyw5b6/Jo/iAnyTt+pcehB0RdEJsbw==", "signatures": [{"sig": "MEUCICa6y30PzL9nlhz0sSFEXzjwDUPLHEEMhrov1y8nHgI0AiEAhtIA0+xwD4ByYXKN7ZiRxdt7xV1DZm2CTIDGhQxOqqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "1369653b2af831841e3261ca63e1540455ae9d34", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.7.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "devDependencies": {"mocha": "^5.0.0", "sinon": "^4.2.0", "connect": "^3.6.5", "standard": "^10.0.3", "supertest": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet-3.10.0.tgz_1516726937784_0.9514134072232991", "host": "s3://npm-registry-packages"}}, "3.11.0": {"name": "helmet", "version": "3.11.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.11.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "5eacccc0b5b61d786e29aa3fc5650abf73e1824f", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.11.0.tgz", "fileCount": 7, "integrity": "sha512-Xqf6VXmjpZoyH4reGyeBCO5nHH0NVeRQnx23LFj6AK9ocPRgZJfSH6zZ8SvNO2tB+fKhsqy1RSjIjWHVvH1X+w==", "signatures": [{"sig": "MEQCIHETK253UwkBU9BXz6wMUX+h4AKaytjxAGhkK4LboUzPAiBI5/JGdg53jlgNMXn0yBOAyiskd8sdlrp/dSVUleKG5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19001}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "2db13e90fdc146db0f72dbc105e8595405932687", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.7.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.0.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.0", "sinon": "^4.2.0", "connect": "^3.6.5", "standard": "^10.0.3", "supertest": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.11.0_1518189647590_0.008028129604281897", "host": "s3://npm-registry-packages"}}, "3.12.0": {"name": "helmet", "version": "3.12.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.12.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "2098e35cf4e51c64c2f1d38670b7d382a377d92c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.12.0.tgz", "fileCount": 7, "integrity": "sha512-CgkctpvreQLL6X3EL2Igs/92+75ZFIsrob9/Rdwf2hQCBGH/DxLk4xFPxAAl6jYnnus/YXfFEVXHEJf8TJTwlA==", "signatures": [{"sig": "MEUCICPahl9L77m9XLYSNBr96i3Vkk8DHPZPOtKf4sXm4L47AiEA7XlkxrZ/d7YNWwZM2bNa/LzmDkhqsdIphRpo2L8hId8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19094}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "89f6b9092db43d69b874dceb4271ffc16b11e5e1", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.0", "frameguard": "3.0.0", "helmet-csp": "2.7.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.0", "sinon": "^4.2.0", "connect": "^3.6.5", "standard": "^10.0.3", "supertest": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.12.0_1520008695040_0.9105528250445578", "host": "s3://npm-registry-packages"}}, "3.12.1": {"name": "helmet", "version": "3.12.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.12.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "8b05bbd60f3966d70f13dad0de2c1d6c1a8303f1", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.12.1.tgz", "fileCount": 7, "integrity": "sha512-/CsAcbPIHgiGde395IkHUZyRLW126RJ6AtxFy6Y6bxhd44Qq8cZ5BBFZ0xNUSbcgX57j32Emh3OhWz/0XgAB5Q==", "signatures": [{"sig": "MEYCIQDksEdnSBOQM3K+lwXIEDp7QtrKRap1fnEppVX7ZlKgSgIhAJlcFyeBUqgQMstRb2eh59RD98JN6s4R73tOaw9JOyAd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/KAgCRA9TVsSAnZWagAAjqAP+gL3YxQwoWPoJfk94sBq\nFNnpLACUQs7pgq86AA9IfJga5DmUCETDlLoN3QshqjNiaEkwNcaA4cfDO157\nxT51EaIl7/gpJ3RU2Je69m5lGJXZSmu3NFdHbTVPstYLZ+9nWbyJBzeBbAJ9\n8gmoUpo3gpfYpcvD5uRiUd65VhlUfNZGpWuMPvvVXEecbB4mC3iP63FPLkJF\nEyAGhimh/Ff//0Klz/MjOPXt+jKncVlpvKjru5romhv+PnrCAl2E0QBNIvIA\nROhoT4zRedoa85iYoo9qmU+sIG795z6ym3PWGbWnMhMWYob+2dGcnrbER1sb\nlrpE4C0RbDPj1OG7GozE4IL1ylSXEnA97UpLuc/ksxZ6bQl9M20S3YwDFqQ0\n1Qe5B+2Yrv4tgsc725gDT7ynprkdvyeStaq+JzJORNSt6OAK53mYvTEl6UtT\nSEe2N6SO/fJk8tG+l9LUZCgKgla+c9lqoXNeAarLAJf0ByK52TdTYn+22Bsg\nJKlJaD65Ut+n4PErSgLUE6pT0Jp2JJmzgVg3dqIuZiGod8tA05+BN7S0OA6b\npjHW2Lcqiq+ZVeAvCCGX0QKhWguUABqSJSuxtJy9UGB/rR9grSogazy3ZZMt\nFEmolOGaylBUTZ98sXdcFDUY/CVGuf2igmwA9JYIvhlD93qdaBavzY7SRl6t\n7E5B\r\n=/1P4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "65d04cb7a078058a8328bdcaecd8b5c972fabd28", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "5.10.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "10.1.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.1", "frameguard": "3.0.0", "helmet-csp": "2.7.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.0", "sinon": "^4.2.0", "connect": "^3.6.5", "standard": "^11.0.1", "supertest": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.12.1_1526505502277_0.2827033746017731", "host": "s3://npm-registry-packages"}}, "3.12.2": {"name": "helmet", "version": "3.12.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.12.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "e49ce4d08bc8289b50398e3ea99b1f64843697fa", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.12.2.tgz", "fileCount": 6, "integrity": "sha512-T30zdqKOPuKckHYwI4aHDTSjfj9CMuAm8tUer4KsgyF6t+DJz2bnLlkLm1UGQowC8uagvmgQHoBgpyHDsGGdnw==", "signatures": [{"sig": "MEUCIG183XCmlndKqnNDf0ShqxLR4xECOEkk5AT9J2HkkfNZAiEAxkoqDCUwoQE+ibL3khnov9mu3eyA+bi9QgFUCFXIUTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUmYPCRA9TVsSAnZWagAA8v4P/iHLDjfdhu74MkaHzo9o\n3iUu2+BEPIY0Ds06ZgrPheSbzCUpwAjAxfHsIuD5Vj/ahvPeKjyPxBgsqQi6\n+iAiq7Clc/3ZQ6UHk3k5UidBtM/Y2jWvDcOmwlOMijrYpGwr6uKz+VWKWKIt\ntW+u91aiK0tFozr9yR24JGORYKfxk0KKlnYk5y/5O7Iu5D1XzRqn0UPhe/wv\nIZEj3lRck92sWcGHx5z10dwsVx8idPzOqoGsk3TvIAMbWdt/C7KcxoRcw2Zc\nDOgOgHLBoQkuhButcjaJpKZoTS605X6KldzkeVcagdHTFw6ng8ZvhyXFwMHe\nkItH/CgG8moXzj30KHDT6FoJUHA7ahNh8swTRn86DGZYXArECp8UcfeWnG02\neqZ/ddEv8meQPpI2lkwU/rj9citljI7YWNnL+nSOMNGviuKKymzmwfNd/ERE\nv8/g1RwKOrAUbHzkhLyva9VMvjpkEWMMKNdHDhMtiDkqkfs3Oeo+0+TyWFcw\nQ3ba11xlPigOPN1KQzajgMYO3+TitT/w0kirSZuw1yHy2Qb48+3gP4MXYTVU\nMMwmUKFXQfMWxZfziPXdcLj/y2nY6swyr5HirYBuJCZRv1i7Pq5G9KLP/Q0V\nIaVZWZ7z6HoUw32VZ9Kw857lemnTagkE896wW5F9AV5G4rBf2PdDp+MykeAL\ntOie\r\n=LUf1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "119a5c929dfd333d0dab6136534de8d5191a0b5b", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.1", "frameguard": "3.0.0", "helmet-csp": "2.7.1", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.0", "sinon": "^4.2.0", "connect": "^3.6.5", "standard": "^11.0.1", "supertest": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.12.2_1532126735396_0.8056254748623832", "host": "s3://npm-registry-packages"}}, "3.13.0": {"name": "helmet", "version": "3.13.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.13.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "d6d46763538f77b437be77f06d0af42078b2c656", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.13.0.tgz", "fileCount": 6, "integrity": "sha512-rCYnlbOBkeP6fCo4sXZNu91vIAWlbVgolwnUANtnzPANRf2kJZ2a6yjRnCqG23Tyl2/ExvJ8bDg4xUdNCIWnrw==", "signatures": [{"sig": "MEQCIAVBOi3Vo8QOT3avgtgnfQKkGZLAO9AeHY2Wm6E4nALNAiAZ33zjVjcocbZ11WagtRkeq5pqh/wdzgOCKEo3q9l94w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVN1bCRA9TVsSAnZWagAAJ/QP/28GeMIka+qEj4cjGKTC\nFag+t4SqpeFqWKwzObruNX+7CNJYqQnksrcurrG3ZXyOhgyz2PDcCUzJOV7y\nQDBnvbDIfHOWNZxAe12IM2tAz61Gxr6J2hXZ3lwZXXuDBlAlWC0mVWexs3aL\nsGXjuz+dUSVv8cIlOC9PL2P/C3M1NG9QN0KSj/r5+zNhdpEu65De90bxElXu\nht2Rqa9A43YqSHU4psV3MIjTApCljRPzYTHWpZIruVjF3APYFDyB14TagpQN\n2dbn6SJxRijN6ZpvGtvIgac58mim/OwNuTWihUqAhVDaSXVvo+a+zAaT4bOC\niBHPLc9j3fKYS48K9zk6qZT1m3hRHQu4+rtWhf5FuKDy89+T4UDpr9+7NPHc\ngrkPUKOiIPPo8AynxNMBoVziUaw/2nkurfLLfJ9QomZvTOGZj3oNn7BP2w/k\n6JDHdfWe3+iC9YIusQUIkOMaFnfsOCO76o4+Q1zCKuWtpFfKlZszNjRcoH+/\nKTmxF9ysslYHEPu6F1qo3XJ47u69FW8s6+4bH89YCmiIuS14vv/7CumWvi0i\ndJ5Y7gmGZgEbn0tUQRgqYBLLGPsUOFjLctC7X3BI/hM6uyed9v7TYZ1fxLuN\nPhEDSrz1pdROANgCPDLEP9VQqavd9qxcA6QE+KKaisKKrAFBeDhboLuhi0o2\nI3zl\r\n=5ppx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "d75a4a34002bcd66d06723b175c50105fd4e4e11", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.1", "frameguard": "3.0.0", "helmet-csp": "2.7.1", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0", "helmet-crossdomain": "0.3.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.0", "sinon": "^4.2.0", "connect": "^3.6.5", "standard": "^11.0.1", "supertest": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.13.0_1532288347619_0.8861850656553154", "host": "s3://npm-registry-packages"}}, "3.14.0": {"name": "helmet", "version": "3.14.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.14.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "ff99e0467fe3e9205300071370024dd6e6690317", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.14.0.tgz", "fileCount": 6, "integrity": "sha512-VUOjHxegTX/dIr1KeU4ZrIkP8k0/nKKD6vNBT1LCS2+q5KO1oMFKGMVdMw/pUcxXbn/z8yP9rfyMZSTpxXPPMg==", "signatures": [{"sig": "MEUCIEozUbr8LZ9H5H021h6PzAaVLd0ooyNjGhEV8asItYviAiEAtNmQfCBbsBrpvpDaOkAVOopqsJFjsdmE+hJfYzGQx50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvOwxCRA9TVsSAnZWagAAG80P/0lsAGpEIZU/qNJVm8pX\nANi49nSqjuJtqO98ZZcYNvaq2TmtJaUW3+ziRqxGbta05RUvSjyqwWnaM8qQ\nv12laWGiq716Mw2U2w24BQks2NB7RujuZk7+i8UKDhNQHYJp1BaQ/AYKayDC\nvCdyqspiL1oWacfCH/3qFQ+rvOsrdaoYHCbNfqKhppq3hqnP45WvhiHV8Ujl\nWdZ/yR3BX2Mw2jo9T/0Gu/NXl4D3twkeOkdEvAtflL5ZyR3Y9yiugvz2nZWv\nfJ9u+4uAu+nGXkbyfWCuzNImZ9eCQajAjnzFZkVd5VX3xPciiUhkWMZQkMmp\nb+Hy5Cd0oHpI7kbfmENe8KOu4SiZJxLwmd/HKfOwhQj62Q8pAoTi8mcMpdOf\nDCq9MPpzAQjshSGRLCfRCC3rj84kpT1bFV8ewWNwkzcWX0DqIZ2rdlc7/1oW\n5VNkAlBY+WUV68tiosIecZG+8nWA5XYAbJYe8AoJcdaNSy0ydITtWn6omNbT\nOjHDKYrEM/gqLMC/xYNa3o5Hf9rhdbiLV64BZhqwHL9m37JfNvfOVglisaME\n9g7CGXgYe90jrOhh0O4BogYFqKYUdTYTQJLhXiFrfE0uaVOjj3SrCS3abAwU\nnyZE/M05riwMFqepWQ4crA/2nE4rklH4fuVV3IjJHXtpAxgpiBAazcUY/PpP\nx6vs\r\n=o/3s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "d81dc40af8867a6c33ae51005dc1f6744bed2c06", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.1", "frameguard": "3.0.0", "helmet-csp": "2.7.1", "feature-policy": "0.1.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0", "helmet-crossdomain": "0.3.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.0", "sinon": "^4.2.0", "connect": "^3.6.5", "standard": "^11.0.1", "supertest": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.14.0_1539107889236_0.11668528728252792", "host": "s3://npm-registry-packages"}}, "3.15.0": {"name": "helmet", "version": "3.15.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.15.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "fe0bb80e05d9eec589e3cbecaf5384409a3a64c9", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.15.0.tgz", "fileCount": 6, "integrity": "sha512-j9JjtAnWJj09lqe/PEICrhuDaX30TeokXJ9tW6ZPhVH0+LMoihDeJ58CdWeTGzM66p6EiIODmgAaWfdeIWI4Gg==", "signatures": [{"sig": "MEUCIQDInaEhVKa3oix8t4tO8aCr3h3k1zyireFy945dFU+4ZwIgVwcxCToyS9gFm1WfvdES/75b3LEvHHyc0xAGhEm0yQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4xcTCRA9TVsSAnZWagAABxYQAIUT6MqsJbo0NMLsAZOx\nYRGR/6Uu2rg86MdEMof2wd8tEUxcWe2sJXKNZtXJzTNI40k/i/drp99rNtg0\nOh4v3iKkip07qGQkMO+xPlQ5rv0gXi4A8WAMldpNoB0CinzBS7vnl8M4MRUp\naNw7oMxXbjHaFr3lwkQbK3oYR7fnDHnraUJTUWoOiK8G0BMpnz4wwxabFpm4\n7ek3YlMSAtE4/Ys0n3UIojd/XT9HIB3NW36rqr024ly5QEALSVJsE+oVmj7d\n1PW/0t9Nfjdnnj74M4VAjDzI1MR30fbnY6Qz2JsRDB2Sc8AFgJPhdNrOlwoP\nE3zpYX3/1MVG6c0GnUP58ZKfwhtRW/58X9Wx52njQEPhaktFQ08Wk6KihwP3\nN1ENWXsKdJ1ulpGPq8dRHAECBv6M01GLMoAnO3XLqicuMI72MyXOu34a712e\n9ZsBqAbSC7MRvptQYr9uP1wUiqXJwm5W64yCtrXRzYQDHzexQ2MZLHS5knny\nAYia5HB8FWGhp/yy8aW22C5APeqhqMjEbT2OjhLJf7zz3axKxla2oGPVTVXX\nD0YXGsbRUsgusYOPCFC5k4klqFDmC8rXbh61s1jKK0wcU4IE9wNmt5i9n3bv\nNETVPDMiwVtpFchmfGZrEddbnDDRrlR6f0egahvAB/zL/X06/0qi1GDPER4T\n2K6l\r\n=FOs4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "d36ae472a576a224380e3378254b476ad4884c1b", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "11.0.0", "dependencies": {"hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.1", "frameguard": "3.0.0", "helmet-csp": "2.7.1", "feature-policy": "0.2.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0", "helmet-crossdomain": "0.3.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.0", "sinon": "^4.2.0", "connect": "^3.6.5", "standard": "^11.0.1", "supertest": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.15.0_1541609234739_0.5190824052196228", "host": "s3://npm-registry-packages"}}, "3.15.1": {"name": "helmet", "version": "3.15.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "http://andyet.net/team/baldwin", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.15.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues"}, "dist": {"shasum": "2c80d1a59138b6f23929605afca4b1c88b3298ec", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.15.1.tgz", "fileCount": 6, "integrity": "sha512-hgoNe/sjKlKNvJ3g9Gz149H14BjMMWOCmW/DTXl7IfyKGtIK37GePwZrHNfr4aPXdKVyXcTj26RgRFbPKDy9lw==", "signatures": [{"sig": "MEUCIAURPsb9t1XhsiQjobvqz8q+H/4ITDXV1cDo6PXzPlRAAiEAoZOIDJPuQRO7pkZCyKdp02UOTYwKQ+Z3W0TntANQeAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19323, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYGKKCRA9TVsSAnZWagAAP6wP/irm1S8g7/KIJR3g1Zk6\nTxxBFaruK2UdPYPkW/qmVGmcUHYU5d/naQJfdXZgrYqLG4f6mRPTFDDt0JPJ\n63514exGudOfHq2jFxvj/r1mRYpo87cy1vNoj1qQa+8l52m/tMK0fvbqsORq\nsJ0dYb9OASe23S+4FFPJg8cY0NnX8RSFvLs0y3iiYY16B6IrFalffmsvC5Kq\nUr0M6Fw4te//KMQkLhmFSv/uigIqYQlC4ga3PJuzNg4vvoK1v/LJAjnNru//\nnHSEKpH1+5V/sxm3ZMMGy2YVJ8aIdq01Nu6NrIvfwdK6QhZtbBdsHYAwqglJ\ncbAPgvCwyqjZnTTPkygY7xKMV5rSnaosuJImrahJXvBWDCCRUE1G1sQZzCub\nvqITdGo0SoJ9uhxy15K+mDx4kf6WK1B7WebgY04NSZVGJMs3POKVqD4K3OeO\nmcwR+asKuaWIjlaxUrBcFaGtgylpjt8MdF/Z5Zz5jFicsGNlHl5WzBopVV/y\nkJbs9NsUv1+72DJ/iY8yCyYODF/xPgHeQWsNsQkH/socwLKnwY1l++/TNVPq\nq69U4QJbYEXeZ9sAEKGuoUEhVG1B9FoWEiRfcpBUbV82+iwLpHvamnDXY/1g\nC7zuXagIZlsAYTXiU9uVR5Np+e4ijnJPvX2SF2LNChmqF4cntBS3XlLlIU8N\nxKZK\r\n=e1gq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">= 0.10.0"}, "gitHead": "f545a949646ae291f65e286989e5508504e29cb2", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.1.0", "nocache": "2.0.0", "ienoopen": "1.0.0", "expect-ct": "0.1.1", "frameguard": "3.0.0", "helmet-csp": "2.7.1", "feature-policy": "0.2.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0", "helmet-crossdomain": "0.3.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "sinon": "^7.2.3", "connect": "^3.6.6", "standard": "^12.0.1", "supertest": "^3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.15.1_1549820553436_0.5799217996177721", "host": "s3://npm-registry-packages"}}, "3.16.0": {"name": "helmet", "version": "3.16.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.16.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "7df41a4bfe4c83d90147c1e30d70893f92a9d97c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.16.0.tgz", "fileCount": 7, "integrity": "sha512-rsTKRogc5OYGlvSHuq5QsmOsOzF6uDoMqpfh+Np8r23+QxDq+SUx90Rf8HyIKQVl7H6NswZEwfcykinbAeZ6UQ==", "signatures": [{"sig": "MEQCIH0K/R1524bKPta/+RhiRdyAjnzfviaaMfbp0DWT+vGaAiBfz35rRQSd1ad8FySMBQWKDzgehAZ+Z5NxopE/XoucGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchVaECRA9TVsSAnZWagAArTUP/3lvAnYRvNjpVcEwOCR9\nWDPIgu8L4zSQ7ox+w8/44jrkt04Q3tVK2tN0hPWPCIo9Wr04rdBmRj6TOkLO\nLasWCpcReHh/LenOTXCRqZbltHxZFWPzWd57CX/T1usCAP7bk9IuSLJyC8Yz\nkb9OQuN4O+6h9u3h0ksjVHFh0Ah2BBSXYSG4zyecYawJKN/esSycCyr10xFM\ntUVITbb9ScZQznrKuNFi254uhEJu0PZin5+hmKPPxG48FRhEbNi5UX9QBf5l\nH/WHo+w5I1I5KKiPHnwP2AahXX9nLS/7Q4CdtQAfLgwXq5aH46qz2y7F2kjU\nYaDItPcp4YMvzPdOPtU/Z23aOuOXCR1wrH6pf4t3g+1E2ykZQ3qrlrD/+spo\n/ee2mMKbtjDB9R72xTiUHJEDu9DODusqr7Hp12oe7irXXvR9e70znYPdFmDf\nGHgKO2Sxj0lWHFtpMMeE42g7Pb4WSj5TZQtqUHH61awXNBF5YfYqXKCT7J7M\n2KCoqRksvqPkwtI7JUdYkp2J+hCljJ5wyWTp7bQOiMVs9asGsKb5Qg2EI1RE\nqfhRYtP0eF8j9ZCf/3mReNV329Qrr12qzPhPgr5/hRmSqbG+iyxscFjxMbgP\nssKGIDG4h6VC48VB7nQFIYspjFrkV0WgbjVl1IDVOb3EgJRKRDmtR4QpMLpy\nGyxz\r\n=QUtb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "123e93ced0ad903b72cfd95da0537355ccd4abb9", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "11.10.1", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.0.0", "ienoopen": "1.1.0", "expect-ct": "0.1.1", "frameguard": "3.0.0", "helmet-csp": "2.7.1", "feature-policy": "0.2.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.1.0", "x-xss-protection": "1.1.0", "helmet-crossdomain": "0.3.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.0.2", "sinon": "^7.2.7", "connect": "^3.6.6", "standard": "^12.0.1", "supertest": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.16.0_1552242307724_0.9331992343028068", "host": "s3://npm-registry-packages"}}, "3.17.0": {"name": "helmet", "version": "3.17.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.17.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "6348767c4ee8f273485ecd60476edcb490ef6125", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.17.0.tgz", "fileCount": 9, "integrity": "sha512-3cJBDC9G4t2D/tcGfW7Y2pnbBSyAjYShtxTTg0wiXafkkGiK/GJyoCcHwux4kBHzG42nWE+IiXdsX0NtvhPuWw==", "signatures": [{"sig": "MEUCIQC9xT8jQu4IzMhLVMtfvuU6PjDThM1SB9Lx416zrOZF6wIgUiTMNB2OXcbgJ+zPYUaTWk7IfQ1AOTFgikeLc51rAvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczGP4CRA9TVsSAnZWagAAAj0P/jKr+v4Ke+B/eHLlbt2y\nOaNXr2ky5E3+hqYjtzJcV2fnBYbefiCQvhUh+zO9DdlpUog8diZpTuYgGizj\n6T7joM7teV+tAbhiWXJypsOdhoZbMpudnDQQrxfEv3fvR5jMAZy6B7zNzoqO\nAxdOpZK59TGSkxbAMbEAL889VNRtK4NgQfwSBX2G1reRvTYgdUgd1TA4KZBC\nnpXWVUXDnCRUea6fy3RBxZrGXLvlF6/Y1J3fhEELV6Pp26W+8AIO3hwxvkXB\nVbEVDprg/Z+7LPSSSdnT093WZsY4sOX0kWx3LJEpEcewegJL4vt8QT0z86L6\nbRXw0sOhNrrdXPLmBgoPajswsazYehL9z7Zda7Zido0CmBcMzYJm06AVS7jr\n+0wbWT/0zaTSxvg2D2uFYlsvpkYvXbsB+2B8xrmdiqrSangZB/cdOaRvFu2O\nrihs8oFjbEnK8N70qYuJbZdl0BQTuuJ8F4f/YKYkgoWjGFdhGv2HJ4D5lJgZ\njFbUKnAKp2bBq8lSdjpEPnmu3yjkO0zPKDISu1En5hqmbJKGuXbuCvsF8hTX\nqLjI1YGQdhLDQVB1wtyBLkdJ8pVBeQtcpNJaT+YFpH4484Uvy8Gpqc7NwfNT\nnRqnqoOh1cq7NtgdZpoxccFr17bYUPC8jVvPD2wkTfly/RsnQd72y19HVRGd\nezgN\r\n=04Iu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "87f8d15b6708f728c7969ccc5aee1a1517a076b5", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.0.0", "ienoopen": "1.1.0", "expect-ct": "0.1.1", "frameguard": "3.0.0", "helmet-csp": "2.7.1", "feature-policy": "0.2.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.1.0", "helmet-crossdomain": "0.3.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.4", "sinon": "^7.3.2", "connect": "^3.6.6", "standard": "^12.0.1", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.17.0_1556898807824_0.9191110621693026", "host": "s3://npm-registry-packages"}}, "3.18.0": {"name": "helmet", "version": "3.18.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.18.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "37666f7c861bd1ff3015e0cdb903a43501e3da3e", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.18.0.tgz", "fileCount": 9, "integrity": "sha512-TsKlGE5UVkV0NiQ4PllV9EVfZklPjyzcMEMjWlyI/8S6epqgRT+4s4GHVgc25x0TixsKvp3L7c91HQQt5l0+QA==", "signatures": [{"sig": "MEYCIQDZQYtyU4/snjGg8arXwMAWFOROYPClWjM6EFgcTT9+hQIhAPmZwVFAWL3Itd/S93r+BhnMQJo2JqbjQR0ukSLX7C8b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczz3KCRA9TVsSAnZWagAAaS0QAIf+TW3OoklKnDfTWsQo\nhadJyskLOr9bgRYaxdyB/SMVgwyTxTGOM5EI//yYLZnU8aVizqJ9SnAxj2z/\nnkusoNj17d6brz2bOfNltMiuTIdJwzResRWVwCc76xph15+rNKaoVCtNRLCg\nRYc9Wl+FLWSmoX7HPjSzl9RpJSv8jCJetiTLYqYsowLyc/LfZ6zrjz1saSYR\nt+cXLQqqs00v9YmAmdyxuOrQ+fpjkOyBpSja0Lvm2CeKBgFSyCrNLkkHfhW2\nxMv/EKFlzFK4XWuBFgW0oVs3PvTx2Z1Is7MAFycMnbHoF3I6l+BYxkXvQeQh\nnGYn4N6erl4hzEwMUrH6FPSjJJJn1jlbOVU7gv9zyN3M294+Xi28LVR0VOa9\nsLw4IafCy4p7Nrjv9cGuvcIprA95AZXuRSJ4BDOVj3112GDKaO4DcSAg1+Cv\nDi+OFz+TptUpDWmGzgfLM8KitsagJZE/BxZbvrRAKSbVvZSBVMh6ujgax0mV\n7L9j5PL859jKBzozz4nYU6Ty/sqX3Q6U5R2/l7+liSoTKDubMFyO39buc6b9\nHygntxsjrP6dRDv7CbkYGEQoMY7PBxnmgEEQOSrn5ZXu49xYVWtVof0gXu9J\nXYLTwqbkBOtZpSqdIZShdsFH9l+/nCu3wud+qHT9FTH3bS1x507Xi3rbXGc+\nuk/C\r\n=vvFP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "238d3a4c35b6f2ea13b12957dd0cd0a6e332df88", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.7.1", "feature-policy": "0.3.0", "hide-powered-by": "1.0.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.1.0", "helmet-crossdomain": "0.3.0", "dont-sniff-mimetype": "1.0.0", "dns-prefetch-control": "0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.4", "sinon": "^7.3.2", "connect": "^3.6.6", "standard": "^12.0.1", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.18.0_1557085641738_0.8896597661916843", "host": "s3://npm-registry-packages"}}, "3.19.0": {"name": "helmet", "version": "3.19.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.19.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "02c524dd69e03b0af20dce7bc9929ff951081a29", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.19.0.tgz", "fileCount": 9, "integrity": "sha512-l58Q3unSpYatlurvFzkCbTRQ8oWUmdXbOs7h+pnwQbFJRhRJDjER6UMyqHxp9iFtWPcVA05VLcUGSi0EXIv7GA==", "signatures": [{"sig": "MEUCICAJlLd3ypKR4klPtPh5i7kUwy6YHX8eU1V/4K480fsWAiEAk5ilkfQWknbdeV7Frcm1+HIgysFZ/zBJuMDqSnb5yg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLysSCRA9TVsSAnZWagAAbgAP/3iG0pFN1L/sHH219m2O\ncs5/q4DME830lm9poQuDAYr7RRHaqgCu6MPD8cl2t6LNATOnDWethf4gM5e7\nSV0+Y8lQ/ZIwcBbmfrciVSKqShmLaVhdCMCrKrFXDNaNvN6gWMF8J4zCiqs+\n0Tv+w4jyUyHOiG1izc/KiSNL+urxMXUMsMNzBif8vXl5ASEIigx62mVH4W3c\nDrIWAVQjHo0zom8iSrhXfjx01+UR601ojm/Dw7Xuo7qTHYYFXV9KZ3R3e+EW\niV5LUq3524Hpj5rjlEj1AyHE6kwzpiu+E3sc13NGPeeC5a9VfkVBKMiA/Ipb\nF/frROrRzhnWjs0GqBUzmbWlDfCteRLYCLjOXeNa4QW3rkOe4Wk6Z5QbI6lY\nr3iY+i3WuQYCApFuLw7nr/t3Oazd78N/kwvda299r+/zajsevr+YuuDMrWI5\nKaS4nnTjHONAq5RLYqKh0+R7OqJ5akVSKpbl8J1F0jkWlHtH4XCuvIoFER1g\nOxzhI6DZu2bum/92osYuhdBMHmAdWAuWEQvyVxb1HgEKMQk1ll+dBhoRGRa7\n1DDxMH6pxTgmNTpmon5/0hgz3RKCTE+V2k5A+v2RM0igX15MVrsogWs0lTv4\nM/ngtNNRLmNmH9zmXixqksKbocE5TewGdth5ABqx9t47BMVvSk/Xum8X1dWY\nEl5q\r\n=lijk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "17707ae8853762d0015a31a9495ad0bfb91809ea", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.7.1", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.2.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.4", "sinon": "^7.3.2", "connect": "^3.7.0", "standard": "^13.0.2", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.19.0_1563372306175_0.821016274479281", "host": "s3://npm-registry-packages"}}, "3.20.0": {"name": "helmet", "version": "3.20.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.20.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "8a9383bf8230a461cafe8bc763423fbde110d2fc", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.20.0.tgz", "fileCount": 9, "integrity": "sha512-Ob+TqmQFZ5f7WgP8kBbAzNPsbf6p1lOj5r+327/ymw/IILWih3wcx9u/u/S8Mwv5wbBkO7Li6x5s23t3COhUKw==", "signatures": [{"sig": "MEQCIFL1BmsKRNZz1aUDWUjGBRU7KhcRao9+chgWxWxNaIAUAiADndAchNzPu4N4MPmcDgHj0CIn8xMzCWPEsuOBjdl3FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdOGzTCRA9TVsSAnZWagAADEgP/R9n/+ZyPCMNXlZdJw9a\n7PCdTgLZ9SzA3qbGQMRwJBWWPZsGng5zCEevL3sg1zd4trzpAJcRzeYK49e1\nBMnwkA6chZ8y1xaPWLQj0d+GOpexCwouQFGRVcQTkCYHz92S7mRIJdFkNo3r\nPgsV5wymb7toIUlVKm7+ytEB8ufBvYUR321Hb+OODldQJsWjGYebPk6WMgQO\nwzGy9I6xC5Kg2LE70EF+XMYEBBxNPsjehY9/oUffD+F1GSuiSSmljgUh9sUk\n0w1BXlmkZMNTWn54u9ZXMeLWX1VWCh/rluy+r/gW6atWZwZRgwt/sLBYPJeW\nDtRQWoBwVugZLecEoFgD+zs+7EKejHe6P4Ry63IeigSzOggPko4yDjurAE5C\n33M7ZhVRDipzMf5TcQqrrv1mVHfbgftiiJO7uLUy+hNQvEOV6cmJX7ZUEItl\n/dDebHGT2MPEGZzgm2I/k3rg3ANrMv/KY9bsuW/u0CxZJ/ISlkI9HiDDhiew\nzwZESDGz/isHQyKMZMk9wE0tGxAigEyoCRoN+FSpQV3oHoAFcYXpW7m3bIDO\nqJGedV+rQ+v+yLcHXFIoHbbvq3JO6YiGHKARYnAkwjThcfcAfGIiI+1l9PaG\nxrB3bgXVeGssCVnwHji1yc8xuduQB0wLPus4yVXPIatYDXcBLlonVV94uqxN\ndHMT\r\n=TarW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "b2a3700383c8f4b1dd0c77eb4d068a80df6da28a", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.8.0", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.2.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.0", "sinon": "^7.3.2", "connect": "^3.7.0", "standard": "^13.1.0", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.20.0_1563978962999_0.7212943079959231", "host": "s3://npm-registry-packages"}}, "3.20.1": {"name": "helmet", "version": "3.20.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.20.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "802fcb39ac6865208cbc6879d3502e582c6f777e", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.20.1.tgz", "fileCount": 9, "integrity": "sha512-em+X5Wz/f0yqoRsBnpnVy3wJHSiIeskX3FQn30szBh1tILaOeSRRLkShuUVFlk/o4qTYjWxdHg4FrRe45iBWHg==", "signatures": [{"sig": "MEQCIAtW2ijQSoUIwkQ+YXFEWnRv7WXOQExnZa5yo7L1+dl8AiBZ5tPpAv8TlSkCP/WJIN1il+TgOK4NXFLAHLXgp2Ipaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZsTVCRA9TVsSAnZWagAAp80P+QEkRR311Iytslm27Sdt\nuDndNe3Lbf0E9/L3wnZzn6IKLtmjrsUWQWPeV1n6lOIOXcCPcLOOZC33gFEA\nj6W6vrIBfPqAYEQraOrlTt0HTd4Ya36ghne6/aOYdicRqFCQSKf7Z6UxQltG\nbqcH0G+/4HGDm/FgfZ/z2CPPVCFHHP3cWie21i96HY/Jfrg4mEgCoLUvbRp6\nuwf/WriR3r2/HHrXMgtLIvMOaKzNPsXusoLHxFG5mRG5dME8M/R2CK6LvlfJ\nMUqCNIuUUV7XO519SQhF/vQdPdc8ykpl21pnWGyjeQ+a3qs1vyCHb7eLtuL6\nqqrAG227DvDzxafU0JM1/tuEtSONTrFBC5h0NaGWxLMGed8PL6S9TKvL1KvL\nIe94J6wDudNFH1alkNzCUJjcyDV09b6UyE/SqF5/Vxw/LSAgBSYdyWuaXy+r\neLzmYacBiWQCc5kcwtEKpB8LOp0yT//r01Y3DNPBWQDZa41ok2rWCUtOBatp\nZHdjSr3XXPCRZCpbo1uEs3FfKSg6RrSGDFtOaqzyS4Zp05X+r1R/qlOH0D5c\nxYT+4vab9t5BWalZbKocIqXcOaVfzgjJGTnS/SXV1kMNcMwwiJxY2lEcjJfR\nI9XA/10OqBd7Cp0pCSQSkdkAWCkv7a6mwr4ZtqoLDQn2Uqb7AuMuIvTj4f5e\nzCpy\r\n=8Z2y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "968fabdcde3cbadf93597f0430eb2197a02afcd0", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.9.0", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.2.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.0", "sinon": "^7.4.1", "connect": "^3.7.0", "standard": "^14.0.2", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.20.1_1567016148438_0.40287710441854707", "host": "s3://npm-registry-packages"}}, "3.21.0": {"name": "helmet", "version": "3.21.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.21.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e7c5e2ed3b8b7f42d2e387004a87198b295132cc", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.21.0.tgz", "fileCount": 8, "integrity": "sha512-TS3GryQMPR7n/heNnGC0Cl3Ess30g8C6EtqZyylf+Y2/kF4lM8JinOR90rzIICsw4ymWTvji4OhDmqsqxkLrcg==", "signatures": [{"sig": "MEUCIA9aFTDGum+POopKTh5dxYUgqFaIMFJtCY0JUFFkEZjFAiEA9QX/9J7Y1XuRBpa+LxKHSCqB83opdw6Kjkm3zw3xj2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdb9E4CRA9TVsSAnZWagAA9psP/RrCjTPNqEU/HNjWKgRd\nZtp50/JlZUl7CIV0RhaVrB3bhLQ/Mjm6IItPcbDWG9qebfnYSu9/80vdJoHB\ncxFbnmQa6imLUHmCTd35Dw6M/9RxlfqMg2pK4/6dkBGlCHrUSn4BMyTK89TT\nkQadmNt5v3by/f/ppIy6xELHyCu3pOjFP33qBwVWHiVyByZW6MbuT3rhm4wI\nkFA9XCXuHXoXrtdIihLuoXHEbYLn35VIDpUMoApe0XYFf51UMbn9l6EIG0MC\nz8hmPBGWN85iLqAxa9PTvlgIlygN8q8umi2gSI1rwWu6W/qb1rIeHpDKqlqD\nbu4fTNlxhEF+LWfN9uFevligcY6ytNlYjV3r8jABOCFGfnu+N/mjIccq/Wcg\nwHi/CyRow1Aaatethd9hGl3IoEo9yuwf22uKe/U3LY5MG5HIsLoEwzjGAuZJ\noh+VjFqZUHSv7uafv6JQvdjxXjQEfqkIqwOxtnt0NdQ93BHOHEvz9c2GMKEY\nBmIsNBef7ZumdHbG3HK2PiVGl66Rqzy4ZTe9CVLtXlVu662LtWtfoHXFJtP4\nl7u5ApltyqcwfcXE1sm3XNxNZYz3wEOsp6Gl61fdncnJtf2l7JFg7P1BUQ0p\n2nv3KV1ncUD33HZpQIRWdHtEXePAgjNuPxlK7Wfe8U7T6puncKGjAw+aFMq3\n8qAF\r\n=56vJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "0dad3c27179a12dc51af73dc2ca63f5840326628", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "12.9.1", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.9.1", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.0", "sinon": "^7.4.2", "connect": "^3.7.0", "standard": "^14.1.0", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.21.0_1567609143449_0.8642321584674693", "host": "s3://npm-registry-packages"}}, "3.21.1": {"name": "helmet", "version": "3.21.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.21.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "b0ab7c63fc30df2434be27e7e292a9523b3147e9", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.21.1.tgz", "fileCount": 8, "integrity": "sha512-IC/54Lxvvad2YiUdgLmPlNFKLhNuG++waTF5KPYq/Feo3NNhqMFbcLAlbVkai+9q0+4uxjxGPJ9bNykG+3zZNg==", "signatures": [{"sig": "MEQCIG0dswg80a00kI82z1IDt8Ila9sJ6Z9O1ENsHx33PpPYAiA/69Mf03ngpBqWl9+Hbnvrvr+jeLURbUQDPDY4l0ObLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhRUYCRA9TVsSAnZWagAAr5cQAJ/+gTmiBowKYvWZ2LYy\n5Bw2wMwF1mO7PXFAEdk70mNSDJBBSSQuf/6F3RjUyF7YJzDO7Q9sN3PSBtGS\nlm3bN8Qp+fVkFwr6iiJR/2VLslzeWKFFxeSBG1jbjNhZ20g2+9lorxSkpjdV\nNeP01/O0YxfTJa2gTKC0kSJTsMXQN2ge3qQ0QS2pYYV/getPHvLThciFFcWo\nRR9V/TtQfuV/xp7EUZ8YHYqdIT+PBIPrB871rZ20kxvO/NqzXZdLk6fuSY3m\n4FAxTGEj869DtBMF4FFm+T0EqmqH9oAeymRk3q6LkbnmsinmvQcUP7tsfKcG\nC3i80H1vya2uPvbINFPi5Tj493YAHhc9HaaFtv5/ckNpp01RVcHwsY/YAO1s\nR7rIAeeU5+okzUilkBAZudY1xhv527r5uf1C+z6R7+1fxwrSMcpghJV8HbGT\n83Xb+rX1U83VzY9x6V2/hG+Sr7G3Nv+ctTtBV723+0q1T3VPSbkbX1tOoZBQ\nkeaGDg7RCoRrtzgf3O+6tDF+LNxISpIMqvfs72lmxHfkD3paYnDyWFyqm90G\nbysA/1JkD7JkDwOmO6uzDCnHKxLXN7UfNsKE6n9mVM270yDqi1/5oe/ERyGh\nJytrHFBs+DL/piQrfJtJvCPOP1s76289nT05NBqwJjQCbuOZbn08RdAwnWg4\npGIS\r\n=dp5h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "5d964d459abf807dcd6d614072fa6509b8641c3f", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.9.2", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.0", "sinon": "^7.4.2", "connect": "^3.7.0", "standard": "^14.3.1", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.21.1_1569002775502_0.35771205324240407", "host": "s3://npm-registry-packages"}}, "3.21.2": {"name": "helmet", "version": "3.21.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.21.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "7e2a19d5f6d898a77b5d2858e8e4bb2cda59f19f", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.21.2.tgz", "fileCount": 8, "integrity": "sha512-okUo+MeWgg00cKB8Csblu8EXgcIoDyb5ZS/3u0W4spCimeVuCUvVZ6Vj3O2VJ1Sxpyb8jCDvzu0L1KKT11pkIg==", "signatures": [{"sig": "MEUCIG2KfD36+E3umCBP98O18ggWOBswQODuEaqOLqPkU8oPAiEAkNf/9HnZronA5gJLKFJI/KZOIRkMPSYff4zLMdIqCHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrhPFCRA9TVsSAnZWagAAYqMP/1WK7olnbSzB5emRHMmY\nxfptO3MGAqQ33cEmEXEuB/pFMcb+fwz33Lo7g2PzdpPtG1O/uYvF/TlFf6Or\nrsHqk7H37kNbU0e1paosEBVVEq9OMiYT/Y6WHFxNFYDvMW0NNRVeWhJcfRi9\nojwEakaiKCPpT7z4DxG7UAxfJAZbtQ4ih3xJQ2ElodD4vzPMSBEuJ5VUob1A\n9jB69UfRZxDv+wKNqGB8EJcS4z1AcMKES1dMFLV/NLDmziiVAIbulSZCirX6\nWxDwAjSEVIKatI+D7kbF0tnvTP9BBKR/0y9rRXoZeUEkbLV/iEDzn7nZoHYJ\nEaJfbaTzDepDWEO9rOEV1KNIDCGplSjmBREBuFMdC3IcpgbhMhf7zbWLXPDp\nzULtKJHxIRZpJT3+7K1N5a5BbSaxMD1euwsH53Y3l1mbHmAkz6PVrdPE6wfI\n5sMXB5US5RGcLKWoauyz9skbDo7DD7Elkkh5xwHM4CuMOos1k4vALjO36GaR\n6eIHWYpP/D+T5uVvLgXmgWEwyLzWSGJ0/HuUJbbQJJthnJlKjLmosxWlzTE0\ngEhNODh6/UdcZg31aS52oEBDF+51a+5Wj2kosr1VCktNMkfpGejlPHUhEeLn\nrNLF36T7cNW435W6OAcGZI1wfJ/1SrIYKZBMA6ip8SQqnBeh8NU7TumFHvM7\nK2dL\r\n=lzQY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "2b37fcf91c775a4cc63a29694168e87b8a9ac9ed", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.9.4", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "sinon": "^7.5.0", "connect": "^3.7.0", "standard": "^14.3.1", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.21.2_1571689413311_0.1323257772368207", "host": "s3://npm-registry-packages"}}, "3.21.3": {"name": "helmet", "version": "3.21.3", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.21.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "15777aae82a4d2678c104fd18195a4012f429b67", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.21.3.tgz", "fileCount": 8, "integrity": "sha512-8OjGNdpG3WQhPO71fSy2fT4X3FSNutU1LDeAf+YS+Vil6r+fE7w8per5mNed6egGYbZl3QhKXgFzMYSwys+YQw==", "signatures": [{"sig": "MEUCIEPXD9fprMpPYaK2/3qRltz5Q1fMkZp5//FrUltJ/ok0AiEA6XPT7si3/G4yhmmbcHydI4H8DpGUkY68/vi1zlCcTIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVFvTCRA9TVsSAnZWagAAtxUQAIJCn03hggCPcuG6gmzR\nMy/qmj2uzpvWppz1edtPBzhS3KyL4NQBvsKV3rBhLoAbIyFMw0TtHt0GZ6V+\nhgxNV7b1YilJZVM0Xoibp3mBnTYrFv3v3wfmNN3DT1tW5j3qidj+JMi3+2aG\nGpldpj/Nf2OR/xicDlOvsii5PmBzbwwjCdGPAeLUjUpQG5DB52nF1AE7Xdzz\ngpNtutZPMpRcJ4LYlNHhwGc4XP3DokujEuuZD7oRsfcbvbWf+FLY7phL3dLV\nmRXrDc3LvilHJz4013v+ILM2eujk3kHi+T/yQWACzJFQjctcsGPPq6i4hBEe\nxx8wCngq4yNLCguW7q7aJi7R7HIu1HARArmS7k2cGVQm+6Shm2XQCxhScRBl\nkq17wd8K7mcEKz99gBCnVZkLJFJ/qwJgH2BjU+gOYaOUorTNv84iKpXQdujT\nERTEZX9X90en354HwpGrtqogR82k7vIqStIwQUaO0hqJJcxP/rswIkzdOoiP\nnxcd62ITR66HH6XIlG7jCk9ggoj+atvf+4G17JrIIju1M6o1p10jn+xJAUXV\ndKxQUVweyYdBIiuj/lWOSkJOqSbQDCUgX5fU5E8RW69fTM0cVUoUbJcbmeTM\npzY1Y8pZN2OYrXSeJS8UvY4jnEn5GxQLW3wu2wPoqMlvaLmTvHzAmLZCS6sd\neTp2\r\n=XS/x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "903c88e90f71f0fe331053e32f5a0e02aeb7cae3", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "13.8.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.9.5", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^7.0.1", "sinon": "^9.0.0", "connect": "^3.7.0", "standard": "^14.3.1", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.21.3_1582586834980_0.7350323741716489", "host": "s3://npm-registry-packages"}}, "3.22.0": {"name": "helmet", "version": "3.22.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.22.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "3a6f11d931799145f0aff15dbc563cff9e13131f", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.22.0.tgz", "fileCount": 8, "integrity": "sha512-Xrqicn2nm1ZIUxP3YGuTBmbDL04neKsIT583Sjh0FkiwKDXYCMUqGqC88w3NUvVXtA75JyR2Jn6jw6ZEMOD+ZA==", "signatures": [{"sig": "MEUCIA4BoizHLQVeSpe4/AuPaT657jHWd8gia4FswMuay1/oAiEAkPHt1qD9yh5ogvp3TJj8KVDkRquOSOuGnO89KzpxTGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeel2WCRA9TVsSAnZWagAA7UIP/RKnLl/q98+28Q0P2lrT\nlpIEe0zm+5STKvvIv8ncmbuC8vqdeu43mvovikq6BIpEpY8nCo31Vivoj0UE\nOBo+oOA0vIOneP4UOw691jB8JXzkVfEVthvgp1jH1JaKMZjAcYRRg87GF7Mt\netFp6MUIXIG5+Yk4VgodM+olRDKAuU/Ier2Q9cAqz5Z6RIsOVMiHz+cRYnCA\nvGLqU1uodvUXPwvtWis2sR+79GvWQtsjwzj4tTJjp4i5S+taVujkVKhxgx2a\nSEQN9pHI/JRBFLdotQmh38LoQdHJ6XqUkAq6/KusFdTv186DJg2GwO4vAla7\nF70yDqwLrilKYIHSjhuq7o/RmEWc3PR2XJ+vs0k/IdyvqGdw97IW0xyXcFcO\nd3yZd3mHzbBDNCfEljszvyLMh6rTAT5cAcAJvKucSkwRB4Al1IDr8dXxkchT\nj6DEFfJBaeeKT8Zes7cQ5z5cKawYZWoteev/YXfmupBpaMuZ8/YLcful3rVN\nnrCFSb73QQ59mRq3Wb8KfCdcK5UoZZInFhvI5GjycWdF819fij6AKgiZPmq8\n+qw8Of40YVKJ1QncGMS87ajVpii/5HN9TfjexB2HAQaKuoJF6FLSsymQJ2C2\n2FNF1nrbFcTC4noQaG2yQCkQ66OAYuLlW/JRI2vYC9xaZbDcDV7ngl8GpCFI\nzOgi\r\n=g2aA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "engines": {"node": ">=4.0.0"}, "gitHead": "6b78d652686fddb06b2626e1edceddad46a523a9", "scripts": {"test": "mocha", "pretest": "standard --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"globals": ["describe", "it", "beforeEach", "after<PERSON>ach"]}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.10.0", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^7.1.1", "sinon": "^9.0.1", "connect": "^3.7.0", "standard": "^14.3.3", "supertest": "^4.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.22.0_1585077652362_0.3862461717553327", "host": "s3://npm-registry-packages"}}, "3.22.1": {"name": "helmet", "version": "3.22.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.22.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "6e1023f4f60b25ee729f535813407d4d37a623e2", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.22.1.tgz", "fileCount": 8, "integrity": "sha512-6/mBacEbbskpmZiNg6rqfC8obo4n8+w7cofaJkQjqQevq+Hlz9ocIUupFLNLnCCtVkThDS8ov+StHMQ8mM/Jjw==", "signatures": [{"sig": "MEQCIEbqLBr47W/n7QzI51Ewoljdx4p/yaEJoy+AjvixMEODAiAtyfu/W8sGOQlMnUcEUg6SUTRhrYTMDFwGMsUlDspYtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4VizCRA9TVsSAnZWagAALqoP/jd6efkwblin+FA4fCHq\nUKbBcFFN2P3kRQrNTcPz5/fTc29kOaOq7LvZo3orROMy+CyKob1X2idU9YYN\na/el9+APsTcKCRo1KY/c3zfHmcS5aPqNGmbGAJbFfMneQTRPW+ZUPBfUc8d3\nyhvTlwfTn0d828F7JWQPKbAPT/eHdlcSt9rrwUymMuKmikQJMaF1sErxd27H\nBBTYRP4yXD5KRznYn6IWHL7faAU0zTfQYh3+NJlAWRDhTezKCdazgVQyaVsy\n0wE4J4sumEWMHDU6kwkG5AxYCSfhpGGeCS575Pstt645bdVF3g65vFMWpcFA\n5pLC6aPD9QaMgxL7I/u42IeilS8nmBmnZtpOJkkd/WIfpbf/gFWPO7Bqvrnq\nu3dQdOmGgdLRJ9UWhOxRku05H4N01rdi2xdz4IJUpSwBJH/NAwfPQ1/iflnb\n7NqqXM+f+AwddnXczzR9dwCqidnOct5DbvYtoTm+w5ZmYdoiXjUY/Yyo2BtT\nYaYMy70eQZFv+RMTM+hzT/TLAxNk8MWTtrZ22eLj5sONed04qYEWHGSmzquK\n87Yhewc8DnkpBElv52gDKj1Z3urJqebp8S/OA5u9aVaalW7043U8Kf+OtjC2\n36o+wrMch7ARclXy1LwaNAZjunGUaKmOp1VwA1Q5DXsMYAm/lRknASDwUK0g\n1/OW\r\n=IbSk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=4.0.0"}, "gitHead": "3713f2c538858fe91d6825dfabc38f2384f2ee00", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write '**/*{md,js,json,ts}'", "pretest": "npm run lint", "lint:eslint": "eslint '**/*.ts'", "lint:prettier": "prettier --check '**/*{md,js,json,ts}'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.10.0", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.2.0", "connect": "^3.7.0", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.5", "@types/depd": "^1.1.32", "@types/jest": "^26.0.0", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.9", "@typescript-eslint/parser": "^3.2.0", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.22.1_1591826610933_0.30134700131763537", "host": "s3://npm-registry-packages"}}, "3.23.0": {"name": "helmet", "version": "3.23.0", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.23.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "b9377e96f8f9b8e42ff902238af28fa44b4b91bd", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.23.0.tgz", "fileCount": 8, "integrity": "sha512-/AKPymGd+mJsFN43IkX+nf8J11V51bxLNYReQZmWrVx7M/FEOs2OEE6U1YIt8Y00rpOupbIeVWv5woEGja1Pug==", "signatures": [{"sig": "MEUCIQDqp0HTyLoEBjW0lN30hkdOJTIv8fcfU6npdZPCidFwzQIgD7uxGIwQV/1SlN1fDhUTNGpoh+D17vIsi/Kso/ppiCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe45EjCRA9TVsSAnZWagAAJisP/iCYXm8iWtH01GmQNMNQ\nFpnjc+ml2MpQX9U8s8ZegBXUrMARvU1Pt77DCxnPBIOGtP76vdbjNkflheJX\nJRd/d4hWG5UpE+zPRC/UY2qDUVVoPwqnznRLoUH5UNy2WGvRRZER2aOplRNf\n8cIHko5ULEaMo4rbylLvHxpIJmlvs2if64hRVJh/lnGV32ZavOEKCsui8Z5r\nRs4YbTqITXvMXqTRKIeTDA7vTMqRHLjuuSNOZIr8H0WuozrwYyUYGA0HuBzz\nW2iikbvdRlc/xP9dQsFE9WQu0hurW5brv7s+J3erRzm0vtMVh7+7fONvKTTc\nn1CAfz9ot7FXV1QUCMRyAiWlrM1Y1ALJy/BBgNdvku4JQuYW4qRHmCQxiaqd\n7LPSRNHKsNlFLYUVMaES4DGEl91TY4uwGtYI1QZ0gu26bmvrMJLYEERtl1kR\nPZTrMkYbveuGUD665H4VRZFp7y0CG6eqK0SnqYZWbewaCoW813ICSp4gewQ1\npgYiPcLN+3lxmSz3TB4O3mNzbAaDVIwB4xfNGiSpIaGfyI23CV6K2MDNmCZq\nB+9tmEqjbxsqoe6nyHaM/cdfnwf38cP5pfaDi28uA9Lq4thR6/Q0G7vt1Dlo\nvtcAKt1bS7c7+OKtsZJp3rrWL9IGHEsododAiyp0nVJpBh1PCLte0xNzwlQA\nNRcm\r\n=mjLO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=4.0.0"}, "gitHead": "d93cdb6be25ca43979c8f98a91deefb303ee0d9f", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write '**/*{md,js,json,ts}'", "pretest": "npm run lint", "lint:eslint": "eslint '**/*.ts'", "lint:prettier": "prettier --check '**/*{md,js,json,ts}'", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "ienoopen": "1.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.10.0", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.2.0", "connect": "^3.7.0", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.5", "@types/depd": "^1.1.32", "@types/jest": "^26.0.0", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.9", "@typescript-eslint/parser": "^3.2.0", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.23.0_1591972130447_0.3637717869368424", "host": "s3://npm-registry-packages"}}, "3.23.1": {"name": "helmet", "version": "3.23.1", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.23.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "97067661c678d6c8d730dda001406f1946a6c6d1", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.23.1.tgz", "fileCount": 9, "integrity": "sha512-e034HHfRK4065BFjYbffn5jXaTWWrhTNgmLIppsGEOjpdDB1MBQkWlAFW/auULXAu6uKk2X76n7a7gvz5sSjkg==", "signatures": [{"sig": "MEQCIFVCbY6ikMUfWkMdhf92frUPS0Whig22asA6fq3KA6hFAiA0UTQn7XjAFfhmxXKhWHWE69lsXExE0cNF3j84Z+Creg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6PKLCRA9TVsSAnZWagAARG4P/R3FU/yAcqEzaC9gcVeS\nu9Z520S3PFFqf5CmwTiZDqooOpikACA3I9nIY2/mGaIUVOlz140mYD9Uqb93\nQcpQpxHax+uVXs/lbB1DM9JgHptL7jPZz12bfAwxrgYybi8BMpGasWGysqP3\nKvTHFh85a2pcqXP4sKbovXz1NNYxhHYsq55M1++45vICHYqQB4esabXyEvxu\nlzqzRARE35k7wJ8KUb2GmRRwcj1gJcRoHE4EuLbusxnSzKKcBubUIJl9ekuz\nPgldKV/g5bLbfBGv+5qDCfu/HJd1C5UwC5Xl1eVa8XO/Ap7mlRX99zxZ/IOO\nSjqcdvfIH+7KzUgtgoUNuronApzvHzvZ+xmeVEESgTBZqsPtruZ6PPdMhNTc\nxrhjGkISvzfJMFN+tzTdTHRtwvvgepac7jikyKH+yxyjyHU/MLbaRU8mPhft\ngiNfKxVAAZz5SY+P/zOpVVx2YBREQMz2PapYmDZB9mht7EJqL+MKa94HhjQA\nfd2B59Oppp0y/NAMir/c1gYaGY9XENrHx9czeDdnYviHiAuUh0qh5If+6ZpF\n8O7ip8Yxc8UmOniY1B6i2+fKrY/7bRRji1HZzdEr2CcfCHlsbWBxp7PrLgTe\nqhhwzmu8Fv18wYxSmfsDbmHcEFGadbglihtmS5TGV7ESL1sbJwUAcExKYF1/\nPWOb\r\n=fFJH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=4.0.0"}, "gitHead": "b120eb42cd5439d7f945def61a0c89f61193e657", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write '**/*{md,js,json,ts}'", "pretest": "npm run lint", "lint:eslint": "eslint '**/*.ts'", "lint:prettier": "prettier --check '**/*{md,js,json,ts}'", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.10.0", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0", "dns-prefetch-control": "0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.2.0", "connect": "^3.7.0", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.5", "@types/depd": "^1.1.32", "@types/jest": "^26.0.0", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.9", "@typescript-eslint/parser": "^3.2.0", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.23.1_1592324746937_0.7684762776675336", "host": "s3://npm-registry-packages"}}, "3.23.2": {"name": "helmet", "version": "3.23.2", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.23.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "390bb6f3f5e593f90f441bdd91000912c543a898", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.23.2.tgz", "fileCount": 10, "integrity": "sha512-pe0UiHw3aHbP8Lon9McCq4AN2XLUMSbhwxJnUY6U2t8wTda7F1SsYg0/pBa1BPugaRqAtx9e1/FyF6E9PsUU5A==", "signatures": [{"sig": "MEQCIAW+dpY7fGgkLF93R1/F0OxQg4ethQM0AVkSZxbt/hLQAiAIyCmVk3xLEvNM+P+Y1DC1N9G/2hFMXXJP1yCxmKyYSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hg/CRA9TVsSAnZWagAApmsP/2gfYsPBq7SzmUygkK2N\npTmbzishutLp46Ne9yL7B0uwkU98Aauhuzb5HrdYoP8YrWoXzinAQ51Ba012\n07AVzUd4Q1fSIkN/A6yOxChkEkWWmzaw/dzXJVTwWKRx6C7aY9azMh/CiVQm\nJagQkR7n8Ghed6PXTOWcjb1hh+TBGoMbbBB5Vdqn6eUsiQ7ypxxHVU2vOHC2\nx54JWW6oEOj64/bUFh27d5bveyrke6Pbni76o/OAfmZbu3ZmV6gImOnOXqlD\nqxdZbty2mO8IEjAFLy9IcqfptNkAsF7AXC1C4RGEKp5XpTMT9B/5V8iECW3e\nXiI1DttQlQBYo6c24LMk+a4YIdNVR/ha6JSHecUU2y6Xbg/xMHBA3BMCar0x\nCFAhoX4mPaNm4cmEwuLGkKPcnT5RaP+s6EEffwF4oejGqVJe87p5gEIG4I4H\n885ardC8hg7nAOM9PmVOAaJrvZVs6cVRAMcWpKRXX4OccbwERzBDMZdGT5JE\nOZPKR2kfwJ+7yFBivS++NRC6v0/ba+tNhe6+far5oj4OcWew2mVwIYIiP02W\n5ShNk4CcfvhxPUXBPzTc2KX+PTjlKy04aaDqzq5weMcgnw1F8XazSMOOsFsu\n2mh/2VAYdur3xFUhkQfQ09nxOLWJjGXKRcV/fNA1W2pp5G62m/wfM+hv4gmo\nxcWb\r\n=sPWr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=4.0.0"}, "gitHead": "16243e6403bfa3b9381a424304747fa26d349b79", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write '**/*{md,js,json,ts}'", "pretest": "npm run lint", "lint:eslint": "eslint '**/*.ts'", "lint:prettier": "prettier --check '**/*{md,js,json,ts}'", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "expect-ct": "0.2.0", "frameguard": "3.1.0", "helmet-csp": "2.10.0", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.3.1", "connect": "^3.7.0", "ts-jest": "^26.1.1", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.5", "@types/depd": "^1.1.32", "@types/jest": "^26.0.0", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.9", "@typescript-eslint/parser": "^3.4.0", "@typescript-eslint/eslint-plugin": "^3.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.23.2_1592924222830_0.1321660526341626", "host": "s3://npm-registry-packages"}}, "3.23.3": {"name": "helmet", "version": "3.23.3", "keywords": ["security", "headers", "express", "connect", "x-frame-options", "x-powered-by", "csp", "hsts", "clickjack"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@3.23.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "5ba30209c5f73ded4ab65746a3a11bedd4579ab7", "tarball": "https://registry.npmjs.org/helmet/-/helmet-3.23.3.tgz", "fileCount": 12, "integrity": "sha512-U3MeYdzPJQhtvqAVBPntVgAvNSOJyagwZwyKsFdyRa8TV3pOKVFljalPOCxbw5Wwf2kncGhmP0qHjyazIdNdSA==", "signatures": [{"sig": "MEYCIQCkhwlPzdHtZF5DGv5d9Zr27k6/WvhtGwTMs0tn+/CF7AIhAORbkKw+TXR4Z4Em8yVpkKuJ6eaMLMz6DcQsNMV4aksu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9jalCRA9TVsSAnZWagAAbNYP/RiCv+BjZLKRwr/8FQAQ\nWzaf8lDWkmk1xOgauSeOSqSn7WaXuRaMb+yqcfdxNKle3WYsPlYyieJJkGYT\nSeWB0S7DeKh2ZdAe54vLNjzPHzwbu+1LPnAw8KmWWh1/UI8noHYSmLEC34qK\n1V1cl3OYdGohicg411O84JCicWXDmqPnt0v3+ukTpsy76dgbwGISeuJ3fJGV\nR9Jxn9AiN30nCyQDdpVIJzSNsm4wM00kKLpYmsJnewc0tYZqKhg1X+hbM2h+\niYpIPtigTYvIzsC+4jlfVTb8h/iHWomYlo/oISKQe0sQL5xstARfh13SGnQw\nk5Z4GxGMc1l8hXWPWM+QhjFqFkABOnvJIviGudZ9A7R1IPxlZFe4xVY4L4or\nf2ZbyoXXsPYw6rX6C7OeL/JZd86H/FLSTXPN/sBV+xEbFlRbDosn6ZlXWXJl\nim9J6WNihbG7a06iQQetl6U1JyLLJL3AV0+CJO7dPYwQDlTNMsDIF1w3mqmr\nu8aaeCfUrhiBxEWtoFGnAwaZP3jEsfaCV5JTsVAbqzkrWkWZ7551A0zOKcxs\n+Te9Kf7I1FkQrSBzLqJ1rbX4zGKEMOWoRCvVJ43kchzTHC+aPjdU7kVXGVLq\nj9wft3gJ2yD+ICh91TyQwXp2nYz7WB9drZza0bAQJ77LbbF2G6Zb3Cbs7Vso\n4AE5\r\n=Z/kx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=4.0.0"}, "gitHead": "3edd5e1514798066db78c24aca80192961888a2f", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write '**/*{md,js,json,ts}'", "pretest": "npm run lint", "lint:eslint": "eslint '**/*.ts'", "lint:prettier": "prettier --check '**/*{md,js,json,ts}'", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"depd": "2.0.0", "hpkp": "2.0.0", "hsts": "2.2.0", "nocache": "2.1.0", "helmet-csp": "2.10.0", "feature-policy": "0.3.0", "hide-powered-by": "1.1.0", "referrer-policy": "1.2.0", "x-xss-protection": "1.3.0", "helmet-crossdomain": "0.4.0", "dont-sniff-mimetype": "1.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.1.0", "eslint": "^7.3.1", "connect": "^3.7.0", "ts-jest": "^26.1.1", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.5", "@types/depd": "^1.1.32", "@types/jest": "^26.0.3", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.9", "@typescript-eslint/parser": "^3.4.0", "@typescript-eslint/eslint-plugin": "^3.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_3.23.3_1593194148736_0.09633874774682116", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.1": {"name": "helmet", "version": "4.0.0-alpha.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.0.0-alpha.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "4a77dddb8b3697f2386aac4d641355a0f0b92a5f", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.0.0-alpha.1.tgz", "fileCount": 31, "integrity": "sha512-dSuoDv9rh6MuFqEWGz3TEdwoZEp90FYLt+MpXNqzXPVehtEsopLbGWSAV7M+1xS6UrFSz6fbl3CCQO5t1DCzFA==", "signatures": [{"sig": "MEUCIHZVMqlLI9N34/w+RJXznu2jZlvk8ZXvf1RE67cL92ZIAiEAzxLJmW6J1yDPNpjkqlvDRdoAdGEJ4wmIw/nUvcx1nZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfC2JLCRA9TVsSAnZWagAAlvwP/0hhdHKqdx9Sah91puZ8\n2cRJ8UyT/9zi8HjfrnL5gj6SDtaLmL9kwYK0KYvDQ+Ce74Nx95CsNCMVHm9y\ne4xS6b+UjtXmQrtK1NEskjiBXwfV8fjcJR+9pl2jiK2xJO57+LK4Q6G2sYB0\n1NfiiSfN9XuqcY9NYuwqQgc4u1tNt01DF2QznMWTeyspzJfQih2JeRMUDkqV\nvq0Pnnrdrqo6xBr+2v4pnElYdZ2GcGi1eh5DQOpgNwc/7yaKdhPgapYy+UfY\nmpkcoSx0XRAMcP9PIkJJpiKOaERuI7R4IiXEJZhxq3ar91bgzioou7H6IUT9\nsRNubBdy6K2B3/4ytSAm8t+wkwEKcCSEq/2yMKND+LpsPD23664JcrM/rqQr\n2gZe3XdsMMXf2GD1ovcS3rSRCC+1CrrkDacCKwLhBwvFmrWO77RLa9iuIhBY\n2svQ6h7N1zd+Hm+7VZ4LFTJYnkkAHJ7gp9Y31WVluFbsrbwvHGYa/HGzcpp3\n4ZfCU7PkSFvQCHwszNozyEKywBrQNsw1hRy7Oh4ksE7ZemgkH62sVWvG3UTg\nZx8jzaUmw+s8Wj5aJ4RbzojNtuSNyvP6Z7Ce0L+UM2aIbp6CJ6gNf/1Sicnz\n3Dm3Dw95ueERU+uhXy70Dn6BrvS7wRTAVc9vg/nxK5rDLW6G3ikI2dLCi9nY\nGI7Q\r\n=OcS7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "2f8d9a74681ba63efe6c341588bc39ca6d81d15a", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.1.0", "eslint": "^7.3.1", "connect": "^3.7.0", "ts-jest": "^26.1.1", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.5", "@types/jest": "^26.0.3", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^3.4.0", "@typescript-eslint/eslint-plugin": "^3.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.0.0-alpha.1_1594581579098_0.5420474582239272", "host": "s3://npm-registry-packages"}}, "4.0.0-rc.1": {"name": "helmet", "version": "4.0.0-rc.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.0.0-rc.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "4f4e09c1eac2dbc57aa5d330185e06e3ab08515d", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.0.0-rc.1.tgz", "fileCount": 31, "integrity": "sha512-kNrF8Rf8xV3ZLdT/JKb0Id2rU4A8nTcwAXE8ml69Ifuy0k/OjMJdTNMqqSpLwK5PhAf46GVSkr+qabXlKccTGw==", "signatures": [{"sig": "MEUCIHZrrkpWqV5Ek7IWGV7dgCVI4GVDd0J2LarJ238yq1qdAiEAuWyCgh5kIWKeQ978A/B1cJ4l3PBQcNTKmTlvEM+nJYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIFWnCRA9TVsSAnZWagAAdpYP/1wCu4X102ZDTsDEqfcF\noe1JrFDV7/iWCdWtN8FrXl61bteDOG/qOOI38vJnqo99E2Vb4DWf+lunFrFb\njzXVsbkM33BlhzemMUCLfXpyOwLhKIXOUjkiwFR4W7qrOjNbq7eT/De3r8pn\nv/pyTXaxKexRbecirM2GcY+t2XcMH+sBvYumbaNTV8KcX2fygpuP5psgUwzF\nc+osEWxbHdA1cOgvmkgZ/3U9GzLT/RqLXnxS7/UUdEYIhdJuPqEPXAYMTpdu\nLtBMZ36MfEzoloUnClkAjVI8ZhN09HOOc5JWwltULopvbyhSg+b/SbudMus3\nFP2q8qrcpw3b48qKmRTa8r/j2soKkhaZG49ByMZQMiwCr2Dit9w0c1YrprCG\nYTm1JshsW130QVZS2E9l83Kk4Hb6ux4JU5CQloXxQVdsSc+WcRrk5X8s7QTg\nLwO6imFRq29pX1rPIIVME0qwJw2d7CN6ttc2ExHm+3sHda32lm+kyIkcXRkY\nLup9t7RSWQU9qff6N81hg4kMRmgQ6plYQgdRHSz5mQc7tWJInlwlYyiDKU1N\niU7gpC28OZZ5bR61BwkcXcQZDRqNAAjOINpbX4Nh0Ro4KiACvDBU4Jk6ScH8\nybE8rwEufyOjkUxGTJu5bvQoXfIEm1xPnD3uzF2I+0k15vXVxZFqODu8kMlb\nyCNO\r\n=1F8h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "c813f916a1aef82d586e3b411b7df7c6de0046de", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.1.0", "eslint": "^7.4.0", "connect": "^3.7.0", "ts-jest": "^26.1.2", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.7", "@types/jest": "^26.0.4", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^3.6.1", "@typescript-eslint/eslint-plugin": "^3.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.0.0-rc.1_1595954599018_0.8307628984821653", "host": "s3://npm-registry-packages"}}, "4.0.0-rc.2": {"name": "helmet", "version": "4.0.0-rc.2", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.0.0-rc.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "f01a99db739971c386fa9b287f7cfd47ea0e1509", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.0.0-rc.2.tgz", "fileCount": 31, "integrity": "sha512-V9vEGkzt8YFKC1RYSh9/g86aLEmO3xxgQNJpewqReq6h4kaJPPUdzGWs5bRgez255foOmg5m6iEm12o9q+Va8A==", "signatures": [{"sig": "MEYCIQDRvtYezhx/L6kGHcuvdkCiXD6otSYsEiIaMT6UeGoZEQIhAO++ffwpAn60+GY77iGzc/T+44Aux51W+iNWLv8UQS0c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIIYdCRA9TVsSAnZWagAADuIP/0+h5YoAx+dzsFEwN1Fi\n/O40ZoUpmNcY5ect+FyCKFo7OlPceMeg2l88tGe8fYTa+DW8kjkWITQK/iMt\nqhZe3xLPj4sSicaxulNwc78CSDbLru1d+FWnSnXKQq9G6qb/MmWQ6x2kzgwq\nKXC/Q4qdRMSghYOufy3Cdk2QXIeGUCt0lh7+FOSOj1s756M4uroUEhgxBnel\nRFsdiUGEbDP2Px/XocgERr0FoaZcMY1Z36UhsWq4yn5m1hZbzuQkYDJZg3DN\nT85WGCZkRSHdr8NgiBKnWr+AdfPoZcN1h5xyhQtka0xvdiS/sWKyDhRqQx2d\n3WOHH0yepNVbCeiUCPyIrWpKJNEnMRdXNQ9ANejq1ugT6yzyl18flmhCsmb1\nF1iHYdsgXSMTyZtEtbPxN5l/9NE7rua5oc4BV+P5pYClxsiuk3PUlwekfzAl\n8syYLmmJsxCcSr2dQlbrcIZB647aPJDNQgVG+PSDXyGdlh0SbEV9xFEzfcYg\nOWtL2+rSkDQEVx2Dkj0kfeyFrN4kepmIZrb2/3QikxYeJoNIokN9V/M2j+7S\nOPTkWlVxSk7b3kmh+F9jKnrvI4xnCzuFyrD3KxO7Ofq7zby107bUzlYAaR1A\nBQQZnX5KfEKvo0UkmbKiZyyEcduxdm8dK0z72keI2s5b3GSQ66yatllUcgKt\nUOmR\r\n=EUm4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "83508e86ad5b3ae4a1214053aaf9da4b85dc5645", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.1.0", "eslint": "^7.4.0", "connect": "^3.7.0", "ts-jest": "^26.1.2", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.7", "@types/jest": "^26.0.4", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^3.6.1", "@typescript-eslint/eslint-plugin": "^3.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.0.0-rc.2_1595967004879_0.5650166282493896", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "helmet", "version": "4.0.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "34c187894ed001834f997c688f2b2df19846b193", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.0.0.tgz", "fileCount": 31, "integrity": "sha512-HyoRKKHhWhO6+EBfgRLkuZR4/+NXc1nJB7x0bWwW89i9eoPciK0qUqyZNOA/zowpgrW9C4+J5toqMkZrpBOlkg==", "signatures": [{"sig": "MEUCIQDhGWKWFnNoJkOjroplVR805cToyGT/PjJPCCp3RYB0HgIgG0P3+enoNPxmRlCfkOtD8HyRhOqwbhBP6dn5jFOocBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJtKdCRA9TVsSAnZWagAA7VEP/37pz9zedGXCxb9aQs50\numcaRnqB3RKZz4zcJSSb48msl5VkS9KWv2vJf+65MvnamDNVdPcyEgPFioh3\nYuNFMgvN/CxAPJBlrNidiymJc9eJYaEnpFbM/4iRRLtUXAOLfCsEfsRb+K9o\nrJ1jJWtmaxUfIySWKo5JRVJaIZY2Gwpzo3xirL95T3nTMlXz7kXVfpWim8lR\nqoQBbSxKvLEG0u/uJol1mxC6aB/jLWsFs3YCRx3jT47ijsKHSxJB78IQuzRE\nlLWg6T6RPM6lfQTwHg6YikWTvDigvRi3D64+S6XIDEbWdup3OGx2QtFK80+J\n+UhHyUsHdbUow2K74BAInLiJPYwBfkjmgqCnlMH1BzZoVc7nFaJ0gjvA5kZi\nmGCuxvo+aQXvm4ce/3C37KJQEiSC+3FkGN0sF9lIXqu27quOOFaZHU3UcR4b\nIJ5fqxmbcm/9GzvMSLCFMM+y2hnzQkreDQn2HfGy+2Bf75nhmPZhsp7UI1F1\nv6uqyOK4v/4rM4byTxlbZ7W53v6p1SxV6R93OgQQlqzoZvJQWMqxhg7KC4Bd\nEKnOgGN6QtB0DdSldXiI4r7QwncjhvsrwgcQIWSKowbrYFpj14uaSGLpfDnD\nB5iw/km8kEWwWNN6TPbcBg7BSZJ+23KbDhgIBPCpGCmHt8ywNL5fTPk0QWr/\nu2bP\r\n=amKl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "bdb09348c17c78698b0c94f0f6cc6b3968cd43f9", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.2.2", "eslint": "^7.6.0", "connect": "^3.7.0", "ts-jest": "^26.1.4", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.7", "@types/jest": "^26.0.8", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^3.7.1", "@typescript-eslint/eslint-plugin": "^3.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.0.0_1596379804790_0.11257184514406049", "host": "s3://npm-registry-packages"}}, "4.1.0-rc.1": {"name": "helmet", "version": "4.1.0-rc.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.1.0-rc.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "11792c8d85366097d2752028d97a16c72054c214", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.1.0-rc.1.tgz", "fileCount": 31, "integrity": "sha512-YTchsrjVYMSjMoQs9Xk2LinjmbfRkP3qxS7MTSJlOsFNdVjnrRxgsn41Nd/bkxTHoAjLPaxWHkb9P1l33BA+HQ==", "signatures": [{"sig": "MEUCIEnXU8T+4sObZDhFL3j7CRDYqY9xPK4Ecyojgh7bNWCzAiEA4LeXgAFs/lXagsXU9l/UxrxhCs2wLqmY+u4yXp5FEaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMVreCRA9TVsSAnZWagAAPtEP/2n5DYdimjDAlzIdb6k2\nVyg910NKh2F84DxTQk/+B3wc9s0i7paNQmSGtDsf6FJ7L43mEWvDfXCs8Kez\nJ8jZYNOL2jqLQWoA6RC1JvaEhB+YPsfY5n4vu9keUkaBT3ru7s4bnlHesta/\nJmOuT3qUpft7vetoHbcHsWB88w3kG/+MpN6XwlNVElSVA+qTvhLARhUyf1UC\nKGM6ABlYFWiJnvS9wli0XrMMs5UGvSh2TE9/B4xKadrwY97q3m+Z4NWCHoEQ\nc8hALndyUPuaBXtXwvqolTBllx003kbT3PCkFhaiAYL7qMma2KvDgviCT3L6\nzKcPD+PIy4CazE/fbjgQRNCu19SpHqRWYLyMhN62OfAE48t8QTW/UzbDVCK8\n3kaD1+ilRRFPaxkzUedwNfNKEuDElV6EOcE6i8+HRMPPq/so1SX/h7mrQaGb\nd4hzGQmjj6NvJd+hUBQCFZp95Iw+VFsQi5m/NQAy7U0p1qvdpkMCFWSTQEJq\nn9yHb+K+6Q/YMTtqy9QfKSAcf3vL2cXdFESPO8teKl+/KT8AdqOFjr1yRTfo\npqNLlL0pqISPivRxdmqAQ1N2+s/ulx26iwZCFJ+ikEshcx1YHhS3JtEukf6Y\nBwTiP9/blItHXUDAIll4hczALLzfAB/qzQeKPYJ1wZ0Z+1RMzsYwRtniDlaA\ntWLM\r\n=eGRc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "10dd1384973767062fa12c2b051d794387340666", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.2.2", "eslint": "^7.6.0", "connect": "^3.7.0", "ts-jest": "^26.1.4", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.7", "@types/jest": "^26.0.9", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.1.0-rc.1_1597070045539_0.8622972534816544", "host": "s3://npm-registry-packages"}}, "4.1.0-rc.2": {"name": "helmet", "version": "4.1.0-rc.2", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.1.0-rc.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "12bc8f5c6f25350398fcefb0b522d048db60e934", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.1.0-rc.2.tgz", "fileCount": 31, "integrity": "sha512-Ywt8Pfs54lvzguQ7JcSDo0+02VxMuDpkmPkt6nt08HHRA1h5UqSdvQquhEtotq9tQCx4icRSjSQpTIB9KU4PBA==", "signatures": [{"sig": "MEUCIAcT8OH/uE7O9KzlBh+ZNrDJAaxloGwkwMU0rI/c7PujAiEAllF6w2NTCKKA5laCRE3A2DXtLcMatrQCvE68UbOESbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM0IlCRA9TVsSAnZWagAACJUP/0tmzh0trOHbuqz0tUYP\ndNNqvmlRp8U7u1LkuMstidWZ0OxVvaPHg/efdXNHX8fiDF0ksLoQtq0ZYtfA\n7gE0M27O7tSTlvJKnjMSusY2osYirOIhQKrrPe616YrYyzIzkutDK6GSTBp0\nq+MKgkqjH3zbhgFkCSHG83H0iHo3tqE6BgmgAy3cMKDa74/OInQOAItMrspi\njzGx7jHNhDHwY9Db3uBQJ7syEwyZpZFaieVkySkZp3ZCVdGXIEWvJmTtYpO/\nZ+oWRkBucEAp966pN/IParRfkgj96T25c6gHxy832xUnO3M9eqITZml6FRRr\n9yWpvWxeG7zEuXgXE/zA30AqqmgXYzEj5+UaU7Qlcdt+2YFKqH8Gfdwsn36V\nHAcx5kpmMv9fqhtpQZXLSnTHtnt9DkiXOpA0jUNaf7/wX9EYIY+vGfcKfYNR\nJDNB4AtoAMKtvuFTf2SFR6+VZ/QuvTAYDuwij3AfwBVTIa8nelN756uAF+fQ\nQ7WKuaIsj/v/KtVgO5kYOra2e/N+ZN4k2V8606fvnhfWToCYncMGHKbcUooo\nL3IZLPzQ4q/fIHDd7Or92vb5VIFe2Yvsk6b8xfCNlmB8E3hdze9R2UZBGlsq\nJuZy8ppg+6jc5KJWlvn3A6ABObGBlhc7PbpAauCrEJTBbMXqG6RRaoMNrWep\nsCZr\r\n=GMQr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "8b6756f0eadef9e135d5961daf0c894fb091f6e8", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.2.2", "eslint": "^7.6.0", "connect": "^3.7.0", "ts-jest": "^26.1.4", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.7", "@types/jest": "^26.0.9", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.1.0-rc.2_1597194788464_0.05804522854838212", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "helmet", "version": "4.1.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "6f3a34e8f18502d6e52518428b23aa4ddaf84b38", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.1.0.tgz", "fileCount": 31, "integrity": "sha512-KWy75fYN8hOG2Rhl8e5B3WhOzb0by1boQum85TiddIE9iu6gV+TXbUjVC17wfej0o/ZUpqB9kxM0NFCZRMzf+Q==", "signatures": [{"sig": "MEQCIEMMlry635fvHVLCEWOAa9RA3l7rtEN2Z/j4BalS8LeIAiAgMOdbzxhDQ/4vinCrZJVLdcMqndsS44rEYeCfRzVUXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfN+7BCRA9TVsSAnZWagAAsDwP+gKfp0MWzUyPMEoNXE2Q\nmWebqyBDWAUmTYTIao/Y/6bV8lgnlNs4KLkCH51xaHpKDei59BvHTH2fU1L+\ncstY7rfasTsd+ohwI52XV+gRlFCM2XfbijIUJRtLd5VtZV52ZTqT/GMoWF3y\n6/MPgxPCuC2xkh+uVKZJTcRhnMNKf7j1IuVRr3XINX+EYczolH1dYzX9ifjW\nHFThaH4Ev1PLtEOwwklhDjZQp9N4U9G/5V/ok0aDOXUkBSzjDJO+xuUUHoDU\nk/eI5E4sn+TzNxAiyoWk6Tzku3dN/zaiGMrfza/GDtMN6xHZKdHOxIhgYGwc\nWgEp+Rp/CSQgjMVApGul0hjeQWW77TEttmDO4V6XBVxE0xbNPFMxidHcTm8O\nEqppZj2HSZx5lUow96wtG37ySMIuER/pK0Xj5/ygoZIJZ2p60bDMdFyAo+BR\nQ84Wd8dTsEbrq8WWorB/YnwW2mlbTBVDN0CgqRHuZHSJrzmNhf1BCcj/w9sU\nHJjtbr1klPyhTsao9yOmFQ//vK9SYPnGroikxuHvaSrvDUKV3KLGpZIXTlZd\nG3wd6N4kVhvhiIwqxi2XieRzRIG+uBcXGDoQldu7Noc37v8wVuOEOK3dFhrW\nxycGeuIuCcrkspjS84u5PqthotCItmCx7WwenFiumqFNq6x1p3GqBQp58Ntv\nmA29\r\n=e7Rd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "0b1137f75f27f4448ca6116697a06bf0c3a99c71", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.2.2", "eslint": "^7.6.0", "connect": "^3.7.0", "ts-jest": "^26.1.4", "prettier": "^2.0.5", "supertest": "^4.0.2", "typescript": "^3.9.7", "@types/jest": "^26.0.9", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^3.8.0", "@typescript-eslint/eslint-plugin": "^3.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.1.0_1597501120512_0.7944938225049563", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "helmet", "version": "4.1.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.1.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "751f0e273d809ace9c172073e0003bed27d27a4a", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.1.1.tgz", "fileCount": 31, "integrity": "sha512-Avg4XxSBrehD94mkRwEljnO+6RZx7AGfk8Wa6K1nxaU+hbXlFOhlOIMgPfFqOYQB/dBCsTpootTGuiOG+CHiQA==", "signatures": [{"sig": "MEUCIQD8RK+rEjX8w6gUXf7F0FuAkKl1cM9aNv4StpOeJjo6nQIgMZDDoHP9ZJOWB1G5ZAoasRR27obk5Cf55fPzVq90Vec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWpVACRA9TVsSAnZWagAACq4P/2bntcxl7dFlAVCxR5+T\nXN0SaUapb9y+Qs+n4yri5bcwAN8QU48FniJ9lEiPK5W5pkbA3vZ0HVo20+yA\nEwp5OV2LSZG5DYdPvAwNjCggl3yWXZfJw0t5tDN9dNWoQmkga2moVFSVyA6T\n9HcyGjZlzrhPG9tAc69RpHN+Se/K7I2+rnnYvrPHrL2jB5DLIX8QD2eCIVUF\nge5bGS/xSc0H5SI3MjCv7X4KTNKl1Bm85OSvtKfV4CgoJn97i7bZhYbmQcBJ\nYcGhYob7GrEdvzSNl8F1/zvK3MSDbpemDqDuEfn3o0VeVbg6vlQJZIhU4vtF\nKiv5j7XgdlT1nPvifyO5HzbwAY4/XpxA06EZhblhBjqsfWbVCYfnny0NoHXZ\neG/JVGBuFJfHLui/mcyhQuZAg5LrKzdgQT9zCk8qcjmCFlesDKhwPbxJkM10\nFVa0susk+ayvq841jBrhurC5p5PhnRHrPNRHB897CYp9WLLF//fGKzz/jcHc\nJXIA6lvkWc+xHf8mpx0A1FvfwckkZCFKZLqu4GnwIJJL4bvmgiSFwm2hzbuB\nX/bP9CZh1/JoMo7w7c4HQ/jzGa91vNPo6fXaky5hk9f6MvmhsJX+4vDtpyDI\nAd1P6PKDXbgOz/nD7pA2hsJQuPY6TR7BllVvRqSH13cIwaATw7Xqu479CAo3\nlglH\r\n=LfFo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "e95fe5ab37ea7bc310ce5558c5ecec8a318fe296", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "rm -rf dist", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.2", "eslint": "^7.8.1", "connect": "^3.7.0", "ts-jest": "^26.3.0", "prettier": "^2.1.1", "supertest": "^4.0.2", "typescript": "^4.0.2", "@types/jest": "^26.0.13", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.1.1_1599771968046_0.3816869438963557", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "helmet", "version": "4.2.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.2.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e81f5613cc1c90402af581794dc9878ad078b237", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.2.0.tgz", "fileCount": 31, "integrity": "sha512-aoiSxXMd0ks1ojYpSCFoCRzgv4rY/uB9jKStaw8PkXwsdLYa/Gq+Nc5l0soH0cwBIsLAlujPnx4HLQs+LaXCrQ==", "signatures": [{"sig": "MEUCIQDQ9DbUdEXVoikDLtbDu1YHOm0kZ1DBIauD7lLRlRTYbQIgJtOfgHqcvUl25PDPZJsyLtTKWg0yuqzEF/HH1KYZhxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfny1nCRA9TVsSAnZWagAAH28P/11u/y8h4KFcdNfHjuy1\ndZ9+Zd7uZTvBoQ6GuZha9ujTZ27XnviCH006xDlunLXx7JTw1ooHGXuzXDoN\n94P6wpGFvF8le2t++Ui2D4ng7dnKYMH02Z6qOWAEx5WSs7nWEFEEJgjpk5Vj\n5tIVEB4NbtufQ3aqo5LdmTJM2sM+GIe8OrnT7Mi0aATo481kLAQj8O2EaZQf\nPBka0uSSLeeCNjSAzrHSNRdS8toQ6+AQzW0o1K+pmO0n5BjcZX0W9+L1rsMt\nZAcS0MIubDDDUR8UqPGCW+NZs99qURM1Rey2dseBnsxeMZB20081x7mJqn6m\nAB8xQOWwbBTfhxTh5wGdqpaI8dUCkrzqKnNO1CT2OzPWQL6RoyPNb3voJoWi\n3IXg7WHWxhRkzujNx9T0vCdNQVOb1riOU2b84GaRaSPAO1YWnL0VKlXFBb+1\nhgVINXhsgVRQ0t7kW1tcgVkcdHczpEhj9LkTVYgTXayv38xH2h9ihQr3RJ4g\n0DQMrvPyOdyLC/fSOg0xOzvCqE8SUy5mO8pO2EDrZssmlEXAJkTXbyhvREO7\n9X4XSpRzyuB3ghSfdpxuId4evRsYybhvDPBI99/KUZeNDBNdOc1J3WIopHHc\ndHo8okEqeZmWLKU6t3Y37FDKs5SlnzwCCQ1kkMucjKREUySQxu+GSZ7F0dzO\nMHcd\r\n=ebzJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "d491d281eb1cc55380046532d24fbc314af836e0", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "14.14.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.1", "eslint": "^7.12.1", "connect": "^3.7.0", "ts-jest": "^26.4.3", "prettier": "^2.1.2", "supertest": "^6.0.0", "typescript": "^4.0.5", "@types/jest": "^26.0.15", "@types/connect": "^3.4.33", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^4.6.0", "@typescript-eslint/eslint-plugin": "^4.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.2.0_1604267366935_0.4404901598095434", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "helmet", "version": "4.3.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.3.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "1ceac7191c4643469277eb16ccbe32ca3cf14e76", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.3.0.tgz", "fileCount": 31, "integrity": "sha512-Ab4a7b3Jt+E7Ifqc4jZ3oApIJ2JR+oAtLxT7Zhj9YyEY7/EtgVRbRABbUnpVwhjTh0vpsAwY/CdRxyPgTBAcbw==", "signatures": [{"sig": "MEUCIQDzUPtIfq3qh4WsJCuJ9OeS/hS0uQBxpXVDAi/roPzncAIgcPkr7AUBQXe867fwboO7laXic1C7lL++FAv9L1viloM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6PS6CRA9TVsSAnZWagAALQgP/jqhawVMONbox+vdy94j\nyltjim0srRgpFhYHOc/12UyhU5VLFyAHZMg4V4TT8+DO98PYHt/jB9wqKhTi\nQGkjTWwIubTz4JXKuP/oVqF9o5pXqn3qG63VWNnTHyY2phtRnMAtf6D1kMC5\nnlXzn0SISC7VHjKMHfMcYpCVlhpqYezZypXzH/+twsztlrVZdfdxli6geat1\nC2qvmJx+BIaLS+qBJYGZjqrG/m9QP7Otih1sk/A4wifuklfTrfPq19WCWShZ\nfn0WWErMeIvQJ4xE24JH2ciipJYLSc6e8sGs2vILLlRNTD08llCgDXH/2PBs\nrdXz5/rY+e022x2HZt31fOEje2TOgH0PrhMcu++NuQC2z9BAy8x+3SLWjh7d\nhuL2e53WfunBNrUS+ifI0PjB943XAFIWsXQBObVAE/A4G+N8i3tD/tGY0kAj\nNHcnnXaTqPXVGaeXXA+e5yyctk6ALHjFOfBZRmUw1IEtZwCUTx95+LdIYxOP\nZLsGYU2jwvS6gWkbIYwiJWN/axd5RSQsRunL8l0PfoiUiOEXC9LvD+hWX5uU\n3n9TEleCU0dJE79Be4ioRdlgtVV/JXBPgN9Z9InK82nU6jHFAkETAD2h979G\n5ZmoDrl090A4YF0qGzl4/B/q5toTPFB0aTf/V4UJzGsoHm5nWq8AW6bqZvwl\nlRGF\r\n=GtWq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "4a721ec2d21366af0e7a34ae4d9cb142c2389923", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "7.0.15", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "15.4.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.16.0", "connect": "^3.7.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "supertest": "^6.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "@types/connect": "^3.4.34", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.3.0_1609102521844_0.06934645847022769", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "helmet", "version": "4.3.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.3.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e374e2fb3f8da045d0233f13299b1fde6e59e4e1", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.3.1.tgz", "fileCount": 31, "integrity": "sha512-WsafDyKsIexB0+pUNkq3rL1rB5GVAghR68TP8ssM9DPEMzfBiluEQlVzJ/FEj6Vq2Ag3CNuxf7aYMjXrN0X49Q==", "signatures": [{"sig": "MEUCIQCKPDfMbE88lQN5k7e1MlqPP1WfekyPMY9znYQZQwNwCgIgKoPtdwcC7miyRJjpYuswJap/KqosKPTJkKsaPpKXdKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70432, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6QyvCRA9TVsSAnZWagAAPBwP/2sgfVi63rWiwoyFvMOG\n4NA9ji2ORLCNnsXcAi/7Tb+lFXTqAvA3hEUfIU3Bd9x51CcfgjHLg++8y8LY\nFDJlmI5yeoCiwPp1zlrQbdim57SfXGz+FbOy0u4PZwUf67CWUeL6uoB8fwlT\nyR5D0BPGyyr9QXSirPidqERmaEhPMPa7Y4b+J3O8UUjQzBFq4xT4hfw9bS8w\ngdzvK+pteAAGxxWGrDQ874v8mK5NR/vGQE/Sh6Z3aZhMDcRJIlinX/YyoLEB\n4/SG/GU6rklqaalhI/aDugDSTN02W88zBD0n/8hpejta/sCZ1CAIP7/EQZbE\nqEIfDj/zvDP8lS7rl9gx9rMp2GfSs/GP7SFOD2Eif1bxQXCX0ydEu19OPYsh\nUPR8k1epohchES1iF8QK5V6aOZoJB6VgqLX9Kbhs586fZukxgDVavUnrhycJ\nY27XZ71CZW1cOoDmEYE4gA/g37hpnv1pV5BlRTsyeOT6uiF4psb5xqYm/gkm\nClIk7PD9ima3ECK9q4WaS4eaoKLfqistD+uXARXscq0ynO0PEM5vXfooyfwT\n+YZYHSkngn1DDbHeFmWXgYNfeDjPRNkj41gdHPdLLPp+asvOrUNRbdP6mHM4\nVWticZ0HZrdhdNMGmBCQ+eN2eyH/NoTdvHpdsUEUxv9TZqYAkHkOwQ0nEoHX\nsM1B\r\n=arpD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "042ee406c753c492a84d7c771010e38477f8c5cb", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "7.0.15", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "15.4.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.16.0", "connect": "^3.7.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "supertest": "^6.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "@types/connect": "^3.4.34", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.3.1_1609108654780_0.9238562051912198", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "helmet", "version": "4.4.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.4.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "84532525b29bf32288c6c9cb3e3038af9a8aa958", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.4.0.tgz", "fileCount": 33, "integrity": "sha512-TDBjY2QVTHGyHeyAC42ldgXC5Z0rZQT25/MhB4oKOkzXpseXVPpRv4nJLJOod6D4A/jvM/6IEO3f9Km9j0Yjug==", "signatures": [{"sig": "MEUCIDanmURyLYFLpOSPUu1eXCSFIE2Rq1ynda+RaHd/RVhVAiEA5Y0W6XTf9HidSshTmkk+htHQ2joNrnzS3ORLvICa3sk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBPRGCRA9TVsSAnZWagAA82gP/0xWBxbY3x8WtErJgTj3\nrcP9KuKEWKu7hNKwfOBVASZD1xTw1BbN3XPwSgOXMCwvvEqiomqRarEIEsaK\nUI2JN66iE0OiVnmo/2VsVuC37yeoGPHfUGOamOn9Sf+W48B3rLfejpbPIxQN\nD/HOhwDCMsUWH/0/cDLx15J6hOhZrxhnww0wgtGCzcszpc95Fan6rxf0p+Dt\nxJEOA1scfWJNQE50W0LsTkZCaOHI6uZy0KU04MgVJZAWeOtl4n3aIpPdji4V\nMGsqtouVBPuC5+KS/ZLJMCy3ZmYxvdBJrRueqfbAxd+2OG3Y7kxII5MPAjUc\n6yMsJ4qBNVzsnpGUhvs8Vl3tqvqsrF/Pwv6+4B0ZZjPfsGPCE9pDNxfR9nr5\nFXqoV6UVzxlH5ripULK22FUXQ7y4Jta7UwgmW/SDchojgvHhvG4Fm2/53gg5\nbDZ80MHnIWzOS29UY5S2VQefshdQmlSdO6DQWMpV/wjdUamxQM5Vm8ZnW/Mo\ngJm1ijgz08qXIQa6bJxTju0aY11pKKrMnwHKbC9D13sRNOcIiBs5ycU3dqk0\n9hRFkEduIx/iVSmElZ9kfz/uZ6w1C4EY5E4Wnn9yh1TaBnmWgeCbEhWtSL1x\nnLs+7cTBJqUOTA//S71nfeeRc8hdLa3BDgrMI2UEwufc4j0a07/khOs8YfUD\nwhdi\r\n=GKYp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "be085ec71320bedf0f1b8ca3dfaa26478e73b776", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc && npm run format", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "15.5.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.16.0", "connect": "^3.7.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "supertest": "^6.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "@types/connect": "^3.4.34", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.4.0_1610937413730_0.9195423247984156", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "helmet", "version": "4.4.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.4.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "a17e1444d81d7a83ddc6e6f9bc6e2055b994efe7", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.4.1.tgz", "fileCount": 31, "integrity": "sha512-G8tp0wUMI7i8wkMk2xLcEvESg5PiCitFMYgGRc/PwULB0RVhTP5GFdxOwvJwp9XVha8CuS8mnhmE8I/8dx/pbw==", "signatures": [{"sig": "MEQCIHldRVBsn9HFX1RAqvgKrH8kAvUR8TgAcEwtf2oOyP4AAiAPunR7/kXd1f+JNTqI+i1w+LFL2vOLP9cVE3Ad/BW7Pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBdSdCRA9TVsSAnZWagAABSkP/A7Ulkg5qewqQf1JgWZn\ntti7xOr7pqybc9X/g3F/09GVGRomWuyDeWKjhqkoRfbOIEf6wnA47C1DoSY1\nMZfhW+/krdRPebOD0QSWyRDD5+lmGn9dDAIE6QSkfFcdsxxWaBtmxdMBuVXF\nGcUYlEuRl9uOAcPaqCP7yGZ1CUflOSTvmW8L1OFnNEkkrxTb+Zy1rs6u5ScY\noxA6zgECNXsEKJ/QONCBVrY1TfoqF/ROVE2zxjaprtx+HYdU90iUdfdziHTS\nWEdwgvQpWWl4st6glNYRKVaoGfFzo/V0HDeHpgYZCixNuMhgFS20+nf6o3aA\npPJ8gUt/g6xPkSjLS/XD5tt/xJCQ1EP/RhVcKXzYVRfPB/rr/x4Ptp02bHpk\nnwYODK5zoXVt6jCbyn7LLJ9AyhtuQZBXYrRtIXCynmCmSJ3QsYgwFunjsMMh\nxeyDGoclfyV104uaEPh9XyHggpAudDLTHAVl6oWKDOzJD58q32XTP/7bDiti\nK4fnlzjvhHULPApvGVtGmymCDI+krUreaGge6NBTt6EY/NybYLzdvuyPYSoI\nmxEEmoXjBUNEcyaFLadiCc6VvmiXWLRog1jrLO5xNrdYBB6LK6HElqwStvJO\n0KY7sx1qF869SSO3sS5LQqYjPPl2QOFdJ5VHiHDo1m8KaLWx5FEYVLNGy/SE\nKwOG\r\n=qxiF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "77bf87b09039a24c3172bc2cc0a5a5b24804fd09", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc && npm run format", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "7.4.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "15.6.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.16.0", "connect": "^3.7.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "supertest": "^6.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "@types/connect": "^3.4.34", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^4.11.0", "@typescript-eslint/eslint-plugin": "^4.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.4.1_1610994844564_0.23124564572940076", "host": "s3://npm-registry-packages"}}, "4.5.0-rc.1": {"name": "helmet", "version": "4.5.0-rc.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.5.0-rc.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "7dd374a4627dc55eaac239012c2463903ef63df3", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.5.0-rc.1.tgz", "fileCount": 37, "integrity": "sha512-9HDH3MB3CNNtVtePCO4Py2IA9U7/AAQW1KjheM9RmFWyIbkCN2Q5tu+YuUydFSxpPSxYXgnuWUufaadFppATlQ==", "signatures": [{"sig": "MEUCIDuLtbRqiOeqtd+F22IJbE/BWdaFf/IPJWWXVPdb4nsuAiEAvZZQ6VTF52iSaSG5J8cZjWh0Au8dj4XVzihK9GC2kYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgajF1CRA9TVsSAnZWagAAwxYP/1+iIJpKjZKveH4zMOMT\ndLl0ZjnqCKJZ3Y6cbz/bP+EFlJRGs6kzA+2K7qMKA8AwxHnxnuS5ZdKgyq4l\namj+rPhDae1tY5IZmCp4mV+gmWeSLwONnIGBWH/5Eto5CKADd+PhnUNgKbsm\n5lFnly+l7i04NQ5j1EvkTnL0bSh1/mUzyiEiLSDC0d7273CElgulufIt+kTf\n9KTgv4Sq7ZD/MdvA5n6fBNHRPm2IIzrYCIqf8KT+qPZIvNSYXedm72FSvpPh\nESD7NDxvBKwaqMu4xGr3o2vCNPSWDVD/J6Fy+m0XhLd/+hO3lr6DJbGDk59e\nA2OqRXU5TMtOyB+wsPeGf7pgbd+Fiba71tBULx0oCGc9WR5ZDfei4WlP2HfI\nUbvEuistPLRjkWmH9BaonQbpAPskfIeY8xlExMwhr/ARmsVXGWfwu72gWsq/\ngoAkTCGokSd+ZZdyKhj5MoM+oM8j2O9a4V9ACzorLKALMPU6KZPq2zyqgvOD\nOtkiNf0EX5HX1VzKjZhXXf5CJjoNFg1VWVOfe+zVZumQpTMVTK4ji6rleSz6\nJYRhpjcIr/NplOZkWFMPTJI7OMdbcaYy0Hlr4sJ7u++WCNILnZ/C/IW5lbmt\nqIm2EkJpxlbPxkjB03tlQETCiYJt+4pf2kbXQ7JEQvufcAbKngjph5ZYoAaN\n5VKr\r\n=i+Ci\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "289adbf76e5872b0f750ec2228396abfe31853e2", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc && npm run format", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "7.6.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "15.12.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.19.0", "connect": "^3.7.0", "ts-jest": "^26.5.1", "prettier": "^2.2.1", "supertest": "^6.1.3", "typescript": "^4.1.5", "@types/jest": "^26.0.20", "@types/connect": "^3.4.34", "@types/supertest": "^2.0.10", "@typescript-eslint/parser": "^4.15.0", "@typescript-eslint/eslint-plugin": "^4.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.5.0-rc.1_1617572213311_0.16393486410697333", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "helmet", "version": "4.5.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.5.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "da5a75d30cbd9f60628e9463a85dc7725806c2ea", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.5.0.tgz", "fileCount": 37, "integrity": "sha512-GfxdTaKarneWOpxmiVb/1YsY+fIwDOxVUGrvNEM1MC8W6Z2PREfkXiWF4XHQdvkyXwUTHuY4DRwB0uH/Q6BVyQ==", "signatures": [{"sig": "MEYCIQCvnXBdgP+7xPegQmd4TqOlISrt1BIncxO5Y2jTCiHzLQIhALne51pJi3Eps681s4dks42gUOwgyCzWDGkQTf9Lmf9Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge0YzCRA9TVsSAnZWagAAQkcP/0wL9isEFBmkkoQftvc4\nCKNQTkaOtfYRHFbe49HHfzMQAoF3/a3qa6e1hYy41jG92OqQsOMBHAMCvu49\n7wQa3oP8U7MqAQyDduoh4tW4PRzJlr6qk9gW0R/H8vCf6Nqyx+ts3jAc6MBl\n8FlzFlrD/NwjD8wWcekFUn1a0eC0YHADjMD76uWQzgTt8PxF3FtNKlIebKTt\ndxWkrXGwPFJPEIZs5RjRDsl3+XcyVeO8Hd6liAqMfZhRKA/bgwYjw4HzN2jL\nzwBLmGKFgrLkxCzQH7suWCnKgXynR7Z9U4rcjPRKDXlMFaXBOUwLtq+iG7Y8\nXQn9bhADlSn2TuXknTG6Wzc+OC5nCH2E0zPYLBtiUDLe9uMd9uPL+VDT2oeX\nsN3GZoIbBoNvgRBM3ntRFqGP0JvMuQQouYS7UNvU18fLDcP/Ubx+QXExx5ub\nwytJs/oGmqu8mItgUuz3hlxppr0TID3DwJGHP03EIqVrpTK8UgYPZvjJd0Na\nvWFwSFRdaAchNnpqWkkjIj3r5wC6YSxJCa5Lf9PHdBOM1UqI+G0TdKS88sRq\nwqOusG9E1D3XcWwRbZjH3XtBJI5LEYWFMNb/DVK8fPsbYZ9NOlp/mg/2IVK/\nEbLPpKUD0Cit2TTZ2Gn3xvF9S9cDLMddMfiUgwPU9HUvQxzPe3Q5lBQKpdFZ\n2tb5\r\n=iTs9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">=10.0.0"}, "gitHead": "24fbd90c5ba5c27ea79aa4bbc0f55b76c1728e76", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc && npm run format", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "connect": "^3.7.0", "ts-jest": "^26.5.4", "prettier": "^2.2.1", "supertest": "^6.1.3", "typescript": "^4.2.4", "@types/jest": "^26.0.22", "@types/connect": "^3.4.34", "@types/supertest": "^2.0.11", "@typescript-eslint/parser": "^4.21.0", "@typescript-eslint/eslint-plugin": "^4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.5.0_1618691635104_0.7188035396017192", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "helmet", "version": "4.6.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@4.6.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "579971196ba93c5978eb019e4e8ec0e50076b4df", "tarball": "https://registry.npmjs.org/helmet/-/helmet-4.6.0.tgz", "fileCount": 37, "integrity": "sha512-HVqALKZlR95ROkrnesdhbbZJFi/rIVSoNq6f3jA/9u6MIbTsPh3xZwihjeI5+DO/2sOV6HMHooXcEOuwskHpTg==", "signatures": [{"sig": "MEYCIQC2yM3P0tTNRBggVuhbKsZhVM/cVxNpssKt6iy8+GdkAgIhAPzLnriu2NvjHrWJCL+ppvS1SggtN7YODmyACDtLF82W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgjsGFCRA9TVsSAnZWagAAFWkQAIjlA3Uz42qnRAI6ex+C\nf9Eb9GUcPlU6WGZ6ARm+Su1n1Qr4Ao9vBk0I9F34IrKbzT+GND6DhIi5dTuo\nB+2DRlLONUuwHzpZpbT7VSSAZoG6P47gWNSZvm4H1TerT8WS/JN55bZGBPuk\nuYAhFMgpv6ovJxlYSpOiXs6m03MtOsB1BA91bM2MVQ7W7me+ttN9KTWzDiT1\nM9MrjHZiR2iMEaC46w01nbzAJ35dfdgWtSjQ5/3aW9hpeRBhaYJs4NuNoqfq\n8mUzdWHRx1uRNTU+sok8O9TFq2EYVw5uWGAIUtVaDrykeO3XIRQzNh7naiZf\nTYrWhPJPTdG5gZfpUNGx1zCUQLHe0N13FGvIx6I+k2M3Tq4+mzwILKZUK4OU\nwtAdKxWsahNUzdrddKbV9snsczFhyGv8e+/OhTch5em3uXCqmf2pEGYC3g9k\nS7j8A2d32nGWPcy79ItHiZoclGoHoMvjPk07ioxKnj973hkd4P+u1+GQiXVt\nTkJO9Zx+7pib9pT9hyB+DYpm2pNCr26pmgXgn0Z2U8hfYKQUCVkykdyYt/9J\n7UmLOWP3F9H73UwDQ9iw6RL0num9oxHCmbC2UcMuz0iUe41s0TAREfTjqncZ\n/SLr2QQgd8VK+ALfwd6pnQEvga5DdWNfusXkrayinN0W0qY5fM8ISi9dauH8\n/05K\r\n=J04T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "types": "dist/index.d.ts", "engines": {"node": ">=10.0.0"}, "gitHead": "e740944ee6fb9a823f276cd5f8b8728daa9ffa0f", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "build": "npm run clean && tsc && npm run format", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build", "build-middleware-package": "npm run build && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "connect": "^3.7.0", "ts-jest": "^26.5.4", "prettier": "^2.2.1", "supertest": "^6.1.3", "typescript": "^4.2.4", "@types/jest": "^26.0.22", "@types/connect": "^3.4.34", "@types/supertest": "^2.0.11", "@typescript-eslint/parser": "^4.21.0", "@typescript-eslint/eslint-plugin": "^4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_4.6.0_1619968389021_0.20433491183496", "host": "s3://npm-registry-packages"}}, "5.0.0-beta.1": {"name": "helmet", "version": "5.0.0-beta.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@5.0.0-beta.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "5160ef34c8b5f9cc50095646ddf434f6180c5e94", "tarball": "https://registry.npmjs.org/helmet/-/helmet-5.0.0-beta.1.tgz", "fileCount": 23, "integrity": "sha512-uY5GJcH1/6bQ4lI+6RED6TrNFt+ixXxegn8KdgTr6OvnDZ2Ha+HKk6Ms2l6fd2gVMdwJLbxauYIvHcNSj3CDyw==", "signatures": [{"sig": "MEQCIAeKkZlNAVoPvX72CF1KGNK0/v8l39q2ofFu4xz9tU4IAiAfCTHvirWBR+lmNOI682U9OjpslOy5UO9onavvbQw1dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrS6ICRA9TVsSAnZWagAAuc0P/RiOJwLSclGidljFuiuU\nVSEas237iZreZaNutmIQZOPPLd7XZcZC2DpcB3u8FLPfgRLDXPKWeZebVf2c\njTUw7mbtGQf7ALUuZcWFhfHuxtRcdqJiaZBypvZtfaH+hrQzi3R8zHiJnpFr\nyG2YthPscE0AIhqu6qikI6ig8/darc8VLrBYuqNhl+PdpKNl+3R5YDcuE+Lc\nXCO9VqEL6Vn+u6ycJ60ebbrkuyT2FtMsNQ8Ys8fnfuKNCwIefU9+0gDf75LX\naTe9oS2F3HmCIfbkW5s2gDtKL15PzLtupPNpAXWxnHMlqPVK9yOfiE3GJyu2\niYjFP5rDpQ5iZXYsaWER9sWsY4mNgj36bj6VhbkAZJsDXW/EfjTWtqtoCqgt\nbGC8ZJETP1/JMwbcMPemer3DruwBoS9MYAzQ6WDen7qQSUB5O8tF3CarrNSd\nSz1AZyOmod/ScsE1RsixQsTZCI1gzWYk6ZG/PEmc7trnZXtNIPYiKxygkrmc\nXN7Xcsk4HZzxCMlponSr4H5EK0pcTNJD7fXG6mfJg3HZiqrju+j538a2GQFP\nxgJvl9hH/sSNtr1BcCO7c8Xlkj3Cd0zufvyepvmwXKKds3j+LRlK8dFHWDdI\n+e5YC8vSmd6IOQKHg7PG5Yae7pw/AqLdzoScrqlO/RxaVogPhSztFOtkhkje\nvzWF\r\n=u8zc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "gitHead": "f89be9b2a6b5d17b3c6332ad404f6d663119b371", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "build-helmet": "npm run clean && node ./bin/build-helmet.js && prettier --write --config .prettierrc-dist.cjs --ignore-path /dev/null dist", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build-helmet", "build-middleware-package": "npm run clean && tsc --emitDeclarationOnly -p tsconfig-types.json && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "17.0.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^27.3.1", "eslint": "^8.3.0", "rollup": "^2.60.2", "connect": "^3.7.0", "ts-jest": "^27.0.7", "prettier": "^2.4.1", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/connect": "^3.4.35", "@types/supertest": "^2.0.11", "@rollup/plugin-typescript": "^8.3.0", "@typescript-eslint/parser": "^5.4.0", "@typescript-eslint/eslint-plugin": "^5.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_5.0.0-beta.1_1638739592333_0.23064325661585716", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "helmet", "version": "5.0.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@5.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "3084c827f51b3c7680cfe99d5d8f29ec39a74fb9", "tarball": "https://registry.npmjs.org/helmet/-/helmet-5.0.0.tgz", "fileCount": 23, "integrity": "sha512-wCuTCJZnEKXagvjcZiAnXkzS4lh8mTJ/JhhC5XjH5vPvSzzX/8Y88u6mfE3F66itB6UIA7uZEekXJsbdFTOiPw==", "signatures": [{"sig": "MEQCIGq4Ls58v6JOY54oyWERAalc6+a1IcYJsYEL5ppdYhO/AiARuddo0Fr8ai6EGqewllTVZTAzuRU8BpyGYJ8xE+bK8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0ekuCRA9TVsSAnZWagAAOBQP/0vHJFOIIaw7IhYFZ2aG\noPzrw2eLgm3/eysOKLHYSzS1oCILOdGSIwa/TLKIsyj6iXtBemygPDTLUCI2\nFOHE4B1ndKpHSzgPovkj+rw7zldhv0AEDeL57veCtpoLoIFYtfIyF/oCESCS\nBbGPFP3AcqtqihH4tKGgmRxQhzShWte2nSd3WtgOoHw44y9XGPAf9rmEQP/b\nFyIu09+jNASS8ruIPdRT+ZmNjLpKKoIKdVXIictebZ+nbzaa8OB2KGHW0CdC\ngX54pefPufI9WxN/TMQ7sP+28IJ3D8DzNm/MUlwVz4URQ1SQYhy2Hsjrz4mi\nlON5jWDvKBtp7PzDZpZo5yvMQFjPuxPzALGf0LbpURnKQ0uozswfcavNcEGd\nn64jRgEiHeWY8MpKAjEeOexzNmkCtl4Z40ba7nSCjTkKiEISGKKA9JPvsvC3\nGBehV5I64k99CYaUIwObJnukD2CLU3VMzmhZPlE+e/ARDwvOTWsXFWoygAFQ\nl3iy+IqIwjP8mvO7iQukZoCXMSFz+FtC9sUwz3IlH2gv/BxJHZEauY1WbgCA\neJ5jRzsU6teYppnB7TuG1g+7iTqgEveV+vQhamjIsggm1qB4y/t6WfFJw7Bn\nzFZ0jJakKAmwfgiWY5MpOMnjQKE0l4dOGkeGbZvpzlbUZnp7wlyAmFcB1VWo\nGyF/\r\n=pYZx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "gitHead": "a2549bd4688d9e711aea64a7fdffb07f4136f6cf", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "build-helmet": "npm run clean && node ./bin/build-helmet.js && prettier --write --config .prettierrc-dist.cjs --ignore-path /dev/null dist", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build-helmet", "build-middleware-package": "npm run clean && tsc --emitDeclarationOnly -p tsconfig-types.json && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "17.0.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.3.1", "eslint": "^8.3.0", "rollup": "^2.60.2", "connect": "^3.7.0", "ts-jest": "^27.0.7", "prettier": "^2.4.1", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/connect": "^3.4.35", "@types/supertest": "^2.0.11", "@rollup/plugin-typescript": "^8.3.0", "@typescript-eslint/parser": "^5.4.0", "@typescript-eslint/eslint-plugin": "^5.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_5.0.0_1641146670651_0.4377811867699932", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "helmet", "version": "5.0.1", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@5.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e8b2aacf881dd5cffadbb640f23155f23de485ae", "tarball": "https://registry.npmjs.org/helmet/-/helmet-5.0.1.tgz", "fileCount": 23, "integrity": "sha512-iyYpGYH2nbQVaQtauYDnemWg45S2RyGvJ+iKj+V9jp7Dc1NTtAJHmD+hFOSYS7Xdwe1GeyVEYSydggXLOg6TKQ==", "signatures": [{"sig": "MEQCICLV4jUKe6WQaigVOIo0iGagGTN89GlRgILz84XIX+wgAiBtn1ui7JweAM21SWSOA5O1AiqUoV8a5VUEbig6iU8NNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0xVtCRA9TVsSAnZWagAAHNsP/jjdxhJlrxV9grRS3z69\nxzMuZKwRWqjaRAlMfA1uEzv/FoNK3M2ra/AMGunx/d2DvfmdRZfYnX8sE48l\ndrHxWpF5m1gPuss0hRlB9quIE7IA1u1fcLSNcOi+I96u6pFe2xJweCOTLysI\n9wJZ0zxoMfK5+RjRh35nan+E2GPI9A8s7lC3qDIXw0Qp5ZFVGRaWhVArva5+\nhoG/wN/isd9CJshixgxe3QDdTnFUTXjhFYRxau0GYs5BrNF5jgGUkd1lvL2w\nFSzDTRJvn/+RuTfe4PQb5A578FJZAqP27lsv04pqa5KuocQACB76y1qV9C+W\nHxmijVHrwhZenInZZuuNMrhSv0h1Y5PTjiJHNA5KGy0V3txY+ZPGKfzqSZfP\nUjxKXNW/eOCFqFrtEIAtm5TxB0Rcfjfo8wCaRKHi4I7p1mvspXm4abQvRNbo\nT7SV9ytRELT3UxetN9X0p8W38nPS8ES3jSWtJywETHowsJTLayptR7I7XbJT\n0mocZESmBrTTqnTgbQreXHsJYcszpq0icWiAkfODBD+DR4PBfOYI4gcxLIbC\nt8zGHDjrfqlhmIbhl+gbbyZwvo+o5BOyVErvYmpucYN+Ar9OyW31NpdqLvB8\nJ6xSWrM+9VvxwsK69YBy5GqMq4MnE17tazjT5Unm7PneghdpCGp2a1TGqTfR\npiq4\r\n=i3gD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "gitHead": "2e1497f9607b1a60aa715c7f2cefb8c1682cad1e", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "clean": "node ./bin/clean.js", "format": "prettier --write \"**/*{md,js,json,ts}\"", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "build-helmet": "npm run clean && node ./bin/build-helmet.js && prettier --write --config .prettierrc-dist.cjs --ignore-path /dev/null dist", "lint:prettier": "prettier --check \"**/*{md,js,json,ts}\"", "prepublishOnly": "npm run build-helmet", "build-middleware-package": "npm run clean && tsc --emitDeclarationOnly -p tsconfig-types.json && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "17.0.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.3.1", "eslint": "^8.3.0", "rollup": "^2.60.2", "connect": "^3.7.0", "ts-jest": "^27.0.7", "prettier": "^2.4.1", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/connect": "^3.4.35", "@types/supertest": "^2.0.11", "@rollup/plugin-typescript": "^8.3.0", "@typescript-eslint/parser": "^5.4.0", "@typescript-eslint/eslint-plugin": "^5.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_5.0.1_1641223533711_0.5633725969059749", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "helmet", "version": "5.0.2", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@5.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "3264ec6bab96c82deaf65e3403c369424cb2366c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-5.0.2.tgz", "fileCount": 24, "integrity": "sha512-QWlwUZZ8BtlvwYVTSDTBChGf8EOcQ2LkGMnQJxSzD1mUu8CCjXJZq/BXP8eWw4kikRnzlhtYo3lCk0ucmYA3Vg==", "signatures": [{"sig": "MEUCIQDAirV/4uFuuDn5gYZSurYJaGO6ytm39lJTIJGmQj98AwIgQRp3ccB8aedCigvsWf5liwDpadCdqy/E5uQfWKbvOSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7Gp+CRA9TVsSAnZWagAAN+UP/3cnc9wEGhYE0vURglFi\ndzp9RoJJhY6jHNlk462HAndlzbLMLWYiwqHkgu8UJqhB4MHwK6vQWarGJImf\ndxuKuiTJ6r478fhuH7mItNaybUtT0sUw7334TCpMNaMaYDw283HGNi0ZFCtN\nkLJyhX6Mt1cefsfWmXB98HnfILRqkokZiYLgdKKXS9zP+hWeHz8goN7ev+OF\nhP2bJEAhlT5vIWqL98R43Hy1XS9/yR4sWlN4RmDHUfgpVKgx9v0ZzCO+uIxj\nXmrLY9pf3p81t2IelJ0b35pvi+JuaR7TMx/Asz+Fa2kaqvmYB7ePuQLZ1Cz6\nUDo7BqJJHIDVF50ZeXwoeNhN5igdo3LdEctrWvfhn7ZxMLpb6i6PfzMDc4ZG\nYK97i9GDt1BzCFuElpj5bEZBYKFXAtkrVMkJV+w+NH5P7OoMlRWSBK/7E73K\nGWZmwiAPW3vJrY1JGBsCQmaSE9O0OJq04KziIjmlvu4AkyR6H+SrU/j6m5BM\nwexdWZFKR5WsuLh4HSYJBQU4t/Bh2afZfaD1sD1rtV06NfXdHN04LlN4wJMz\n8QmaGd2ItOZNzrCd2de88j0VyN2x4mZNI05eLQ3dQrzriDmAxCdQvUk04rxU\nTDsMCaft6zC3sfnyCV+D0/eQ204d6PJ97EgnDPYRsyJoK/aGdmirBLokegYt\nhrIW\r\n=+MyW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/types/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=12.0.0"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "gitHead": "f0d38d6aea0a73c6da47e47ef8ba2b4ec324c40a", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "clean": "node ./bin/clean.js", "format": "prettier --write .", "pretest": "npm run lint", "lint:eslint": "eslint \"**/*.ts\"", "build-helmet": "npm run clean && node ./bin/build-helmet.js && prettier --write --config .prettierrc-dist.cjs --ignore-path /dev/null dist", "lint:prettier": "prettier --check .", "prepublishOnly": "npm run build-helmet", "build-middleware-package": "npm run clean && tsc --emitDeclarationOnly -p tsconfig-types.json && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "17.3.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.3.1", "eslint": "^8.3.0", "rollup": "^2.60.2", "connect": "^3.7.0", "ts-jest": "^27.0.7", "prettier": "^2.4.1", "supertest": "^6.1.6", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/connect": "^3.4.35", "@types/supertest": "^2.0.11", "@rollup/plugin-typescript": "^8.3.0", "@typescript-eslint/parser": "^5.4.0", "@typescript-eslint/eslint-plugin": "^5.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_5.0.2_1642883710517_0.26430282581238385", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "helmet", "version": "5.1.0", "keywords": ["express", "security", "headers"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@5.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "e98a5d4bf89ab8119c856018a3bcc82addadcd47", "tarball": "https://registry.npmjs.org/helmet/-/helmet-5.1.0.tgz", "fileCount": 24, "integrity": "sha512-klsunXs8rgNSZoaUrNeuCiWUxyc+wzucnEnFejUg3/A+CaF589k9qepLZZ1Jehnzig7YbD4hEuscGXuBY3fq+g==", "signatures": [{"sig": "MEQCIGPc9rjteQkVinaxs2cNOQKb8aQvHd85BbDx7dtmSjl7AiAPGdTl0/Etvqe2VJW3XYHpIZEMOw9Wu4dMq1hnkNTWOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJig9aLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWZQ/+PElKc6j7bIQKCMVtAxa+zTzXOrQJrHDK1lgdEJUQ65EQgf3f\r\nh61pRd9efG0QJv2QKsjargVpHIeTQd4VAZzEZck0fORfruRWuJtXBAm2LA1h\r\n7jeYyUdQF0Ehk1dnM2OysHMN4O4FtJjpc254+UyOvcnrrFJ27WgR/89pvzEV\r\n6866Aslsd96naQyk1VvSkEWhmWeuLLmSvh4S79mhqS0codYHxJVALs/knlLT\r\neK7rQNPi9YahCHKFKAh3AqM1T+zyNrraQf74iq01PdbpzxibyV/LMlhMF2d1\r\nCBlbc4bTKZ4eAkaIlVUBCXgDVZ+2s+UYyArNSaE2G+SgqXz8xrsxjoReFnlm\r\n1CcdHtR3Lix+YlOIIL1MEjT5SHjaNBKw2nZ5AJh6ylHU4B3FAKxL5nMU6JXx\r\nZ9tWmgcYm4x32FXeEOGfldSy62TNTQFKiLlFQ6eORHGlrKdTD5I6EeDBYl7a\r\nOU04DBqOszPjzxC+9j250Zi9VWo39OisvSrpn/Yl1e9wPvCVKm20N7R5xIMn\r\nkGFJzCJAyLqptUYfQ2+ZLfyhlwVO5Gj3LTm50ARgil/0tF5SmWIbSleIbRYl\r\n920aghbNppKZa9LhHCDcGBjLX4SOYdW4idio+kPn0LOJsREBRK3BBR3HLVaN\r\n6+RksAmo3TVJU5jWM+PXoGVWknQgUicGisY=\r\n=faWs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/types/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=12.0.0"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "gitHead": "4d4d0df174aa09a82b92102a719c67bd233cc935", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "clean": "node ./bin/clean.js", "format": "prettier --write .", "pretest": "npm run lint", "lint:eslint": "eslint .", "build-helmet": "npm run clean && node ./bin/build-helmet.js && prettier --write --config .prettierrc-dist.cjs --ignore-path /dev/null dist", "lint:prettier": "prettier --check .", "prepublishOnly": "npm run build-helmet", "build-middleware-package": "npm run clean && tsc --emitDeclarationOnly -p tsconfig-types.json && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "18.0.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.0", "eslint": "^8.15.0", "rollup": "^2.73.0", "connect": "^3.7.0", "ts-jest": "^28.0.2", "prettier": "^2.6.2", "supertest": "^6.2.3", "typescript": "^4.6.4", "@types/jest": "^27.5.1", "@types/connect": "^3.4.35", "@types/supertest": "^2.0.12", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.25.0", "@typescript-eslint/eslint-plugin": "^5.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_5.1.0_1652807307286_0.612409611469412", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "helmet", "version": "5.1.1", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@5.1.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "609823c5c2e78aea62dd9afc8f544ca409da5e85", "tarball": "https://registry.npmjs.org/helmet/-/helmet-5.1.1.tgz", "fileCount": 24, "integrity": "sha512-/yX0oVZBggA9cLJh8aw3PPCfedBnbd7J2aowjzsaWwZh7/UFY0nccn/aHAggIgWUFfnykX8GKd3a1pSbrmlcVQ==", "signatures": [{"sig": "MEUCIQD0WdWNJ33zUABiJ+MX53VAV/0CT0dSAwrCtGdb1Mla4AIgQvuEJWatpAUuGdqwSmV8g64/N/4lRVsMvwmvCoZ92tY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3Aw/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOWw/+K7uWCKz0oNPgpfrflUwbs19pzS2BvS2B1sFiIRlxgupxutNR\r\nd2XSydZGoqDke3ChhOiBvZe7Ts5xSTv9WkqanmBSKbZqkMrECR/fhx2T3YMC\r\nGF/zwk3lSKry3J3VZWXuPZIqn6y5ZJ5XKYafIgjsS1tRCvMmr7UzQjkCD0oq\r\nw8ZExvfVlUP6eoD+S5iBTXHd5zwNI+uhZGcAt1E3yGZL2qad4wQJtaaNeZCd\r\no/M9ozWC9vBXh7n/XTpxPXVRILTJ/1ZihJyAy6w9RP6hvufA0u6+NcSYTgU2\r\nnO+Np9kG8jaAJ7tP2QIz/2x6R2fKKdckcI5X03E+x1wI74++s+x0H5jl9QJF\r\nMB/JGj+jei+TLVGUn/gcKpWxhbKXu6yZxU+H/w4iEtiGvUxZ+/C0PG2LOm+s\r\nW2SgNZ4jjhknaHHyADbFh7ka/oECv1oup2XPwcEmwlYJa7VVU5UwkMRJ30Vs\r\nWF1jt4c4efhN62/tCPQHGQsF+dF0p7/k+UVuKSRQQoNGxYB4UDUU6NQYLzDv\r\n9ivGr2QX1z3KIHa/mj88hzSCRB7PrfiNoUHUI8jJdCYdpHeeGSPSl8uGzeqM\r\nu9m7eO+F5pv7CQ9C7RefRlOAZGP37Me01YXaiFX2cMlw4Lu378AhgCnbS5JQ\r\n8pbgViTIwY2dploEiMMGaL0vba87WA8qFcc=\r\n=phmY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "type": "module", "types": "./dist/types/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=12.0.0"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "gitHead": "0e95b6de40c1f3d11425bc881f0af322b2ccecd9", "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "clean": "node ./bin/clean.js", "format": "prettier --write .", "pretest": "npm run lint", "lint:eslint": "eslint .", "build-helmet": "npm run clean && node ./bin/build-helmet.js && prettier --write --config .prettierrc-dist.cjs --ignore-path /dev/null dist", "lint:prettier": "prettier --check .", "prepublishOnly": "npm run build-helmet", "build-middleware-package": "npm run clean && tsc --emitDeclarationOnly -p tsconfig-types.json && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "18.6.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.20.0", "rollup": "^2.77.0", "connect": "^3.7.0", "ts-jest": "^28.0.7", "prettier": "^2.7.1", "supertest": "^6.2.4", "typescript": "^4.7.4", "@types/jest": "^28.1.6", "@types/connect": "^3.4.35", "@types/supertest": "^2.0.12", "@rollup/plugin-typescript": "^8.3.3", "@typescript-eslint/parser": "^5.30.7", "@typescript-eslint/eslint-plugin": "^5.30.7"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_5.1.1_1658588223763_0.357436136695493", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "helmet", "version": "6.0.0", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "8e183820ddccd7729a206ad73c577b264f495595", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.0.0.tgz", "fileCount": 24, "integrity": "sha512-FO9RpR1wNJepH/GbLPQVtkE2eESglXL641p7SdyoT4LngHFJcZheHMoyUcjCZF4qpuMMO1u5q6RK0l9Ux8JBcg==", "signatures": [{"sig": "MEQCIEgGWtPtomglUCem7gG6IZjkw99Pkuqq3rgFEZf19/T/AiBac/yRvS9YWO0F1pZFIdlhAeWFeSO5VC//RX/hmj2zkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCOMHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovQw/9HcPJxV+lp4b/6nVWR90iP44uwuZnY6ZT9LkqK0zz3Kd9MM+m\r\nqFoDzFTFh5prOXSvafI617bkKLFeJgMAV6OPPuQncXDGCt54UpyO/QnkzJQu\r\nzru25Wy76++2UHSlhhIV9SADc5cMd/uL2YkwohgPsiu8Z+Z3freTtREF9P7/\r\nFxBDSTqt70bEUO8H0efp9ZZIoUnNibUT86kRugeExT+yuowHbueD9GuCJySn\r\nl6FyQ+aFRcSs2p0hUN2i363b7wXkmwt/rpfGhUr0yG9Z5nFvKpxfEo3a4gNd\r\ntXERh4Iv01t1bIZDZJYuhMZsYfcVQ5MiewTPvAoWxm0exijn+meAre4ZEiTP\r\n7ysq+1L3GQJpkdfIuiVhGXr5anlpEulisQSBSQKly+pVh9HD6z2eWohvjHSy\r\ngHBxRcQVIqdgtUZ5sTC2aQ5XFUy7DyiizmtfSlcMaG6Wss3dkn8gi5OEtruT\r\nvIyO+l4ftRQvs6CFBxoKHrK+/kwyml/usO9Vo7RWQAaP4bTneT29yHVpDNDR\r\nSh5S3qHLKOTcPw9VLC+gMOypmd/Fc+OseQO2/GzSXjUWCzU7FJVdVwAgTWRn\r\nT2jCyN3uKPSbCyDS0YAE99hctK0GMKEyYEYIQgRICWaWn/cUMWCebp9Ahf2E\r\neRUSw4jMQKs//uY0z3TUs/UJwGgQTWjjlJ8=\r\n=jO/b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "type": "module", "_from": "file:helmet-6.0.0.tar.gz", "types": "./dist/types/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "clean": "node ./bin/clean.js", "format": "prettier --write .", "pretest": "npm run lint", "lint:eslint": "eslint .", "build-helmet": "npm run clean && node ./bin/build-helmet.js && prettier --write --config .prettierrc-dist.cjs --ignore-path /dev/null dist", "lint:prettier": "prettier --check .", "prepublishOnly": "npm run build-helmet", "build-middleware-package": "npm run clean && tsc --emitDeclarationOnly -p tsconfig-types.json && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/qq/x5403kds2yldd8j97x9_y5hh0000gn/T/tmp.1lsJs2Nu5s/helmet-6.0.0.tar.gz", "_integrity": "sha512-FO9RpR1wNJepH/GbLPQVtkE2eESglXL641p7SdyoT4LngHFJcZheHMoyUcjCZF4qpuMMO1u5q6RK0l9Ux8JBcg==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.20.0", "rollup": "^2.77.0", "connect": "^3.7.0", "ts-jest": "^28.0.7", "prettier": "^2.7.1", "supertest": "^6.2.4", "typescript": "^4.7.4", "@types/jest": "^28.1.6", "@types/connect": "^3.4.35", "@types/supertest": "^2.0.12", "@rollup/plugin-typescript": "^8.3.3", "@typescript-eslint/parser": "^5.30.7", "@typescript-eslint/eslint-plugin": "^5.30.7"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.0.0_1661526791132_0.9716920688474986", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "helmet", "version": "6.0.1", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "52ec353638b2e87f14fe079d142b368ac11e79a4", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.0.1.tgz", "fileCount": 24, "integrity": "sha512-8wo+VdQhTMVBMCITYZaGTbE4lvlthelPYSvoyNvk4RECTmrVjMerp9RfUOQXZWLvCcAn1pKj7ZRxK4lI9Alrcw==", "signatures": [{"sig": "MEUCIQCXRYqGCWn+61QWTzarjl3f+N8QJcNpM2v17rJfNnpL4gIgJ9N3jnFRqxGkGUTUyC62cf6n5udAOiIb+Bk5TGE/18s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhifIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofvQ/+IDk/YFJraXfhTSVWI6TuGsOvn1hPU+WVVZ4ZKh5XwI1FQv2H\r\nB51fBVdximpuuHZYkaM97As7umLzz8ijkbTyQTBM7iuZF978nu7xLS7Yr9Pt\r\nSf+/ccAJCIeq2czOefB/4UXPnW6IVXsiaZcS97QLgWehnUNJ/07cPQTfW0Yt\r\nL/aGS8qj+rQ00+vl9IGuiVC4xmfsHaTl5paN6nTIeySvMw/ElVhHPzj0vTpK\r\n1ynCGhnEZ7DWcAykV0peWFPSERRuZ6TnfvpiMr8xvLj3uh6l1d7sj657s/wV\r\nIsngvp4ADAOpy9w6stiSshFiZN7vzkgZv1uq2HTA0XzvvrfwVSBmG+Hw6Lpe\r\nk+wIouxxHxssv++r8f6z3VmZWlG3bfI/psEDsZdFaXVqTRY2HOdgZF96Zauq\r\n9putUqq4uxft9KcmYpeuPALD+2R55cRa265+muTlwtMrPvGrwYHi8BT7ASjl\r\nNfXOJfWbdmRrucM7kyB73Qjvc3Y6693As/y8JMcbPm4RwNo+FsKIvsdelmA4\r\nnl7kAHV1m1O6tUYE9g7nVN7ZFStO9evoTL1I21Tcyr8ZVs7/qGgNt2qIv/9M\r\nJ9skAxkQWZDpd1MMBgnr1++CLLgPEfZEwd9ZQV824Pt8ZtvM09JRXrAan7LO\r\nI41LDSejVtPYY7Pji/svgQfFVHNM7f5s8Lo=\r\n=rd50\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/cjs/index.js", "type": "module", "_from": "file:helmet-6.0.1.tar.gz", "types": "./dist/types/index.d.ts", "module": "./dist/esm/index.js", "engines": {"node": ">=14.0.0"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"lint": "npm run lint:eslint && npm run lint:prettier", "test": "jest", "clean": "node ./bin/clean.js", "format": "prettier --write .", "pretest": "npm run lint", "lint:eslint": "eslint .", "build-helmet": "npm run clean && node ./bin/build-helmet.js && prettier --write --config .prettierrc-dist.cjs --ignore-path /dev/null dist", "lint:prettier": "prettier --check .", "prepublishOnly": "npm run build-helmet", "build-middleware-package": "npm run clean && tsc --emitDeclarationOnly -p tsconfig-types.json && node ./bin/build-middleware-package.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/home/<USER>/helmet-6.0.1.tar.gz", "_integrity": "sha512-8wo+VdQhTMVBMCITYZaGTbE4lvlthelPYSvoyNvk4RECTmrVjMerp9RfUOQXZWLvCcAn1pKj7ZRxK4lI9Alrcw==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "8.8.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "18.1.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.20.0", "rollup": "^2.77.0", "connect": "^3.7.0", "ts-jest": "^28.0.7", "prettier": "^2.7.1", "supertest": "^6.2.4", "typescript": "^4.7.4", "@types/jest": "^28.1.6", "@types/connect": "^3.4.35", "@types/supertest": "^2.0.12", "@rollup/plugin-typescript": "^8.3.3", "@typescript-eslint/parser": "^5.30.7", "@typescript-eslint/eslint-plugin": "^5.30.7"}, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.0.1_1669736392537_0.7918179333071309", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "helmet", "version": "6.1.0", "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "96f4a38a2b2d498afb6db525336cea157ca28083", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.1.0.tgz", "fileCount": 8, "integrity": "sha512-/si++qV9sJFr6Se7e5lxRCpikVgHac0qpLEVLiBaIgXGHVVwwe0EaOR6++gwB5KYSzD8uW3QZ5+Zq/IsU2Cj+A==", "signatures": [{"sig": "MEQCIDhnm5nPA8xGAVqLe/sGXW9dGeJlSfXjONxhmxJYR5KPAiA5CcXz+mRNX5Ti1rEvNXZ+j6QrsX0VB2824njeCTzoXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMZV5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptMQ/+J8nMbE0s54O+iAUdfPGf7hkIlfXHdnx5XE3zpZf9tUqU8yxb\r\nXsQb8fj1lq/Mk3E8MJm2IFtjTlDjSDtnCVzdY1D3ynqRvrhyxUJLR0336ban\r\nl7s2zMSc224FQhix2p5nAG6sckWvjWrscGrql42P5VzHwYUM19x0c/Ix09kv\r\ncs7FO+5hnERljktriZN0YZPOtz4i+QFdV+Tk5+k2I9DQJuRm9VMcOldvyzbi\r\nczSliM8oiH6tZCzK2LfxEhN53oF75ne3VxSsWKi6Atg0K8CMePDC9ePPNvJA\r\nyQmWV9gAcqTxL4zgPQEtF+scFLp8e3/4vhBQctdJGfhYhTjwAfLs+PJIr80Z\r\nAE6m76RBDHzOeBAiHZxch1ZR7f/xgfLljvuDus69dDUds/0rWozmByTh3EAF\r\nWTHxdUXGGlBNOmmlse0rHB9SFGDQM1PQMgZmPSn8Z4zSrYh7gpTgNsuXEibN\r\nEElJXCJ6idyjFGWXLHZfQ2oAgrdVOUJBWcyiD89vnHk279udDG9dlQVyRoGO\r\nRw8sREsfYejbOcUl9vDneS36PT8pTCcdPw+ePtlLjlNV1tAd9ayW2HiFQxxH\r\nwmYRR0qjEDmkv6P2giaY8945Cw7AZ8CZMz86IZm9V9K2t8ukRyWgjA3DkdJ+\r\nNSwjH6zuA4IG4cXfillSGhiY/GDxzvEK2js=\r\n=Ldop\r\n-----END PGP SIGNATURE-----\r\n"}, "_from": "file:/tmp/helmetkgMJ0D/helmet-6.1.0.crushed.tgz", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.cjs"}}}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmetkgMJ0D/helmet-6.1.0.crushed.tgz", "_integrity": "sha512-/si++qV9sJFr6Se7e5lxRCpikVgHac0qpLEVLiBaIgXGHVVwwe0EaOR6++gwB5KYSzD8uW3QZ5+Zq/IsU2Cj+A==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "[![npm version](https://badge.fury.io/js/helmet.svg)](https://badge.fury.io/js/helmet) [![FOSSA Status](https://app.fossa.io/api/projects/git%2Bhttps%3A%2F%2Fgithub.com%2Fhelmetjs%2Fhelmet.svg?type=shield)](https://app.fossa.io/projects/git%2Bhttps%3A%2F%", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.1.0_1680971129600_0.31599646626450495", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "helmet", "version": "6.1.1", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.1.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "a20d93787c33bc0834686fd70584a491befe1391", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.1.1.tgz", "fileCount": 8, "integrity": "sha512-+lpiPR2U5Z9gZnJiLEhK+ta7Z+NSbWyyQCdOP051T8vpPOfd4I5p66cdTgt5hsRXYbJjWNwmga3zjwTHr2rSpw==", "signatures": [{"sig": "MEUCIC1JjWCEIePxsicMixVFD+32jaxwS5IYeb72CfvIo80oAiEAvr44IcqnSeZ7BjbhUvyQSAgKDJ+QuD3rrCYIJLh3aB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMZnmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqA7Q//UPCc+/njnYB7h+4MeBlPq0AKrzH4semET7adC3RmactltMn0\r\nUKysJN0fc3Far7yiqG8g+avj8gJArJcwqySpWZDHc3tIDR+m8c6IsxiwVoVR\r\n/EfQ+WkfC0ArOnzk+JkJReSAwibGXqd8Bkgk9JX1l8ehZU8xlTDg7Ps6xMlV\r\n6FV0fEldzbMzb3S97Rdd2/e/Wgc4w+QT+GsBI2jB11lyl3ByKAo4SLxGYB/a\r\nulq3fsWfHarL+0KcMx2qV9LN3pvrkVWEObqw5cSiRueG2IL6udd1546MnPRn\r\nSGEPa6l7Nr6miTjaVdKAGlBqVMVCqnvL8qJqTvqBzMcSxlwDlPKnA6DvvB8y\r\n9pEyUOH4W0kzWT0dI/GqtM5mxPdCcYbu9CvxTTkgU5HL8gV2i0cLYrSSOrpl\r\nZLVuQR+mGfXr0Q5KhaNff65tHmT6tOz9do73PNhhdwS9t8XcBvHOvaSlo8J4\r\nytf/jlVNa69f3L9T5bmfr1yvLG2fCQZZ+4gP12I6EcGkms8aFr1wxET+zfQU\r\nap2ug/e9ZZH0asP5u+RH0uF80Z0ItlN6NZiyeV3H28YrfXHZ0QxKPclHWIiO\r\nAOL6mzmev0Kc2kmCEdhEjoAf9BLn9ySd2/WRzNczE4tHX15Ux8GR4GSYH5/Z\r\nLZKS0rgcJR3MVpbd64/clXjC/ONxLWPLkOA=\r\n=X5F0\r\n-----END PGP SIGNATURE-----\r\n"}, "_from": "file:/tmp/helmetRKyEg8/helmet-6.1.1.crushed.tgz", "types": "./index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.cjs"}}}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmetRKyEg8/helmet-6.1.1.crushed.tgz", "_integrity": "sha512-+lpiPR2U5Z9gZnJiLEhK+ta7Z+NSbWyyQCdOP051T8vpPOfd4I5p66cdTgt5hsRXYbJjWNwmga3zjwTHr2rSpw==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.1.1_1680972262031_0.7617799987466694", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "helmet", "version": "6.1.2", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.1.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "063af3d2c7fa25c09f377aaede7b8a9dffbbe761", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.1.2.tgz", "fileCount": 8, "integrity": "sha512-n39kcD7ntw83PMeqqjSOPrY/VpIH+XniLoxFXLfK6UCgTTN9XkOGp2YWKB/zfgZS+zRQdiHo3fgNmy0l5xoFPg==", "signatures": [{"sig": "MEQCIFjo4B3QtP6oPSZ4+PZzAy4GiC+UtqZiUZW4eLHg7KwqAiB2idicaCof9LUn8WDU5Mr2AxrwtGGeKD3HxUOmRE0OlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMuHwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWdQ/+Jxlwlt5xi0wlwxUXDdkWXqme4U/0F5bkPCas6AeileoJGutD\r\nPlUyvqaINZv/Zf+UOXRPoj0omYdpTbFLfTcaSuM9oat9tNOwUS9HCE15ugUP\r\nkjG/FA1yS6K959xi42OkewPwSMPwfrp8soLwRXnaa02h0ENBzRWn6SMQdo9m\r\nqiJIL3HSSowVKb8soAIyAe2GYezA1IlUr6Sn6dpLeSaK/a57Bhny5ULkLzz9\r\nAhjaMkOEKo41vczzls2uEJ4iRhzi300GR4V3m0nP01Hfez2ZKEmgRk0Vjfln\r\nufuu+HDqFeh823RVekMxf7pKbE0w+P+9cikkSDcYKQ1MTjpndyS+ldwSyQWe\r\nM6r1mY5Ce0iI/lF48WDOGDw5kEdUT+04CiTMacNEeIPN2klv0H93i4rGiJMf\r\nsXuWqbfJM4nq9i1EU9Tpgku5vd5VMycauHm3lEBzpLYIBE1NbnQG3OstCCS0\r\nLZmWoNdQVqqxQpgFaF8YZXfWQyonkheFJb5VccHx79r384RZ3Q0NUuw1fKk0\r\ntuKBAP3jbegEXb++dmRJJzWctgVfqIbXZtdcyBW4KWEEgz8CyfPJkzPfCR6d\r\ndvWFQwNW4sAN2ekSavYhDYFBiestO8mQI/ZaHFAo0mFxcG9qkxCfCFvhISHQ\r\nko4Osxp25xh3TP0jmT1ALDx56EsOz2/qg+o=\r\n=dkYq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.cjs", "_from": "file:/tmp/helmet0wlL2I/helmet-6.1.2.crushed.tgz", "types": "./index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.cjs"}}}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmet0wlL2I/helmet-6.1.2.crushed.tgz", "_integrity": "sha512-n39kcD7ntw83PMeqqjSOPrY/VpIH+XniLoxFXLfK6UCgTTN9XkOGp2YWKB/zfgZS+zRQdiHo3fgNmy0l5xoFPg==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.1.2_1681056240161_0.6911384351908654", "host": "s3://npm-registry-packages"}}, "6.1.3": {"name": "helmet", "version": "6.1.3", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.1.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "9fc666c232c19a96b1bdd1a816f3322904f28098", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.1.3.tgz", "fileCount": 8, "integrity": "sha512-W92WikLkFSwuQ+bvdSayleIwFu9kbYmGcOlVz5EPnrB5iP2ezAiP0+sWxndta1Nn2r4zFhxGygIU/gVKrxIrUg==", "signatures": [{"sig": "MEQCIAcr0oQoqcLs5P8jVcC9YjU/bf/32oPUS2uDc3/MeYjLAiBLtG+J5SHq14dJ+IOFC0liQO5X3Z2MkgvJRHx0ZjW4DQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNFI+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVMQ//T4+keU6XCFHRlNmiivm8qUjEHSL7UxxxfK8OeFrkmCiJKYKl\r\nhH8YsBxTY7iuIG0HeUYd/dlHraZvudmazwunC826PrQHZakN/xZu64mZWH/T\r\nwZTIJP4Vy6acY6qM/P51Nnslk+P0+iKtJZw7Pk+vs6PaEOl3oN5INPVIGLNP\r\nb2mlA0b1dXZS2H36cbxuC7gpSnAkom46B6rcM8CbbUjo0I0U7BTW2atYTi1l\r\nYVFshEk7vUg7Z1f97blhDAocagOPcT1k1NTFYKUaVFjG1r+cFRTk/THRuz4D\r\n03NsAjzmvdhddlsyOrlSV4HdE6XSBq/a295IdnaFPxYDAGEyo8/Cr6pNNj0w\r\nG3R5T17etJB5XjvaklebIfk+RY9yZzJPsBBTtP5WkR4DtiUo+dXfHEtRSXDU\r\nLNCuKB/cV9NJ0PRQzEBwYwgYk8iC9ZaZ/+HvHUmK0Gu6H9v07KnfOXXmKTMZ\r\npay0hkXpOgE1ZzbVeGG4iGtNCrdmCaTtLgFVzqnICndvXAwGDp5NY16PVao+\r\nwISzFO+TssQJmToCFNICbUTpluPTzwP56Pn6u3jGx3bZtl9nLV3pvduhMD+q\r\n//doWMlcU/rJjZAkEj0r5xf8RMkBovuoF1IuMYnlhXk20NueuquO6YKs8b+0\r\n492Q3Pj0tL0iUQONJIRs19ZhxH8vxCdOdA0=\r\n=tiAS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.cjs", "_from": "file:/tmp/helmetdM8AGq/helmet-6.1.3.crushed.tgz", "types": "./index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.cjs"}}}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmetdM8AGq/helmet-6.1.3.crushed.tgz", "_integrity": "sha512-W92WikLkFSwuQ+bvdSayleIwFu9kbYmGcOlVz5EPnrB5iP2ezAiP0+sWxndta1Nn2r4zFhxGygIU/gVKrxIrUg==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.1.3_1681150526489_0.9758501690576835", "host": "s3://npm-registry-packages"}}, "6.1.4": {"name": "helmet", "version": "6.1.4", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.1.4", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "a57dc478532e811f7dfd64b69f9023dad1c6e28e", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.1.4.tgz", "fileCount": 8, "integrity": "sha512-nCJIriKjOYcRmHRpK+Z5DayEh6xwOKO0U0qzWL3MEIwsB8OSxxBu/mUE9U5PRH8ln/+WySlK2eYe5FOlo0v9aA==", "signatures": [{"sig": "MEUCIGLfGYeMAZjOfFwpFGp80YiUPtUaD+TAVHptD3n0PZPfAiEAyJJl4nE4f5OWa+a5pT69gYOOLCXRkjHqh+lQgMw/M5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNJhlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog4Q/8CQB2rUXVpcRZ8vPysEkSm7J7Lme3dfQkXz105rpnCsU4daiq\r\nEC+Qn12bLFzPV0djkym7q8cvcIPFP5K/oCUhhL7gh+A/OgwKQSdgrx/c9/m/\r\nn1W81BVY0KyuUw9v7HOxCDT+1mOl2sKcT9r5vdPGpIrGnQVF1n9B+KYQTPcV\r\nHgsU5PbJJamWGEQoNKVF1fujLTylEKnofhUuFgNo/GFDoXJhMgmfR9J9OmyS\r\nbJo9FuWjIWhXMuxwna37HzxDoswHPPTy2oKVbOMhs3dvWYVWBojz6M+ett5z\r\njFtlIX6LqWHamyDDpFv1C/jUegO2DgjwoBu9ZWTmRMewWKMEX9tQuY5EJ60W\r\nfsFw/9KPwokWuAhxbiXOOgusqjM6dSV834nhtZSAUHM4MoPoA5dLE2aGnTTQ\r\nspJJw7MxPWJK/uqOkVt6warI6+R589E75eEyX7mvIHl8zPSjSTU4K/8zFz/y\r\n/uX90j1uHZsLZjA5AJj4bqLN9wDLWvRGzx+DrJKLq8/YC4fYAryPdIeqWv/X\r\nw3YksQG7ixP53X0qHsdbzS4a2/FFhNDK8gLmNio+vZgo8zo9Ho+XRQ7CObsv\r\nqbc97TEDRYuQ60Dn+9RTnprGEynk9wF056ipAenzXivbVfj1k57D0dOO/aM/\r\nAPT+J/D+EQB+qWNCHhKq/tqo7SHmgEh0kXU=\r\n=UDvP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.cjs", "_from": "file:/tmp/helmetrdIJvf/helmet-6.1.4.crushed.tgz", "types": "./index.d.ts", "engines": {"node": ">=14.0.0"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.cjs"}}}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmetrdIJvf/helmet-6.1.4.crushed.tgz", "_integrity": "sha512-nCJIriKjOYcRmHRpK+Z5DayEh6xwOKO0U0qzWL3MEIwsB8OSxxBu/mUE9U5PRH8ln/+WySlK2eYe5FOlo0v9aA==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.1.4_1681168485200_0.1402954005750745", "host": "s3://npm-registry-packages"}}, "6.1.5": {"name": "helmet", "version": "6.1.5", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.1.5", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "2153387f6d73cce6efdfd85d3a65417cfb7db80c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.1.5.tgz", "fileCount": 9, "integrity": "sha512-UgAvdoG0BhF9vcCh/j0bWtElo2ZHHk6OzC98NLCM6zK03DEVSM0vUAtT7iR+oTo2Mi6sGelAH3tL6B/uUWxV4g==", "signatures": [{"sig": "MEUCIHm0EPWs9j1GnuwpzK0s7uuk0BhQPdrZVDASh6/hdHUHAiEA8xS5wp2s16yYPESSFjm6esHVnVkCiHOrUPfZ37D11uI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNWzQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiYBAAiuEqs8/O7qkoayO6/0jMGw/0utX/2+6C/KzXMekLGsSuK1mw\r\nv4ab50ME93NPmczis7V201LHolmi794uxKbIO2DOI3ZpGlGFzVNfjAPdd+jL\r\nPUYCw+7dg/2hX2pWDcMtCztsJ8hQdV4pNaQJ5W7RXnYZTTN2OLET1hol3mYx\r\nXDQubAfrV0VXal7+iBJR8cJKyT7SYA+fjzDp80hhhza+r38z7NrIXdRWeZFA\r\nhQLrTMV5xRYuDnhNsl4J86gCw+5effDuJa+ZJnWAmdjc9Lsgcqvk5ax9gLU0\r\nLqdG/oku8XdNdabJSE5N0RNMpupjrMPi9WuxQqylhMKmrQxn+16SL+CddWBp\r\nKu0eECrEcqNsaMqUSL042bC1NhpLhGCv8NxaAOX8uwQ/oq6vSW9t28Czkvn4\r\ng/+Y9QUxKJ3Wc7AuLbREEvBy15xfbo+ObgxGnr+w0ThN6b+ej/zKk891dehx\r\nff6VGEbAI8ODvPT6gECmUf73m+bm5s6lCw4P0t+X7pe0ZyXUhrk6NpjqL9d2\r\nsGsN/qQh+7MiZGZ6ZSFvZxOMunrJAGE+9arhTpXJ7rK98vpQMdWsG8oQ2w8V\r\nKmIGF7Z9zw8kuklQr8+rbJn4JZeIaMgV6peS60ZK47Bd4HstgVu+v4Hhre7p\r\n2mOajmlBAoiOf6dDogk8XHHNopqGE8E4TkQ=\r\n=m5td\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.cjs", "_from": "file:/tmp/helmetCZRxGf/helmet-6.1.5.crushed.tgz", "types": "./index.d.cts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./index.mjs", "require": "./index.cjs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmetCZRxGf/helmet-6.1.5.crushed.tgz", "_integrity": "sha512-UgAvdoG0BhF9vcCh/j0bWtElo2ZHHk6OzC98NLCM6zK03DEVSM0vUAtT7iR+oTo2Mi6sGelAH3tL6B/uUWxV4g==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.1.5_1681222864346_0.27406549636118704", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "helmet", "version": "6.2.0", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@6.2.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "c29d62014be4c70b8ef092c9c5e54c8c26b8e16e", "tarball": "https://registry.npmjs.org/helmet/-/helmet-6.2.0.tgz", "fileCount": 9, "integrity": "sha512-<PERSON><PERSON>lwuXLLqbrIOltR6tFQXShj/+7Cyp0gLi6uAb8qMdFh/YBBFbKSgQ6nbXmScYd8emMctuthmgIa7tUfo9Rtyg==", "signatures": [{"sig": "MEQCIAxe6N7ge9+JnPsJ0kuWmkFE/JcdcQSZ/auRUIS+qNoiAiArlEV7LRByBqUgzIseufiWOfpECN6aOvwBwaOuRN89fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105392}, "main": "./index.cjs", "_from": "file:/tmp/helmetdnyIRk/helmet-6.2.0.crushed.tgz", "types": "./index.d.cts", "engines": {"node": ">=14.0.0"}, "exports": {"import": "./index.mjs", "require": "./index.cjs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmetdnyIRk/helmet-6.2.0.crushed.tgz", "_integrity": "sha512-<PERSON><PERSON>lwuXLLqbrIOltR6tFQXShj/+7Cyp0gLi6uAb8qMdFh/YBBFbKSgQ6nbXmScYd8emMctuthmgIa7tUfo9Rtyg==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_6.2.0_1683411320698_0.20297662201170885", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "helmet", "version": "7.0.0", "keywords": ["express", "security", "headers", "backend"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@7.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "ac3011ba82fa2467f58075afa58a49427ba6212d", "tarball": "https://registry.npmjs.org/helmet/-/helmet-7.0.0.tgz", "fileCount": 9, "integrity": "sha512-MsIgYmdBh460ZZ8cJC81q4XJknjG567wzEmv46WOBblDb6TUd3z8/GhgmsM9pn8g2B80tAJ4m5/d3Bi1KrSUBQ==", "signatures": [{"sig": "MEUCIEda6DamUoD1sCnwplD/3lteO/1lLVc8ek7MxH0p0j8YAiEA0MED0eSEeqC8jpWRZUX1Tfw1DuQlQ05fh5lu9Y0mtOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101219}, "main": "./index.cjs", "_from": "file:/tmp/helmetkyknry/helmet-7.0.0.crushed.tgz", "types": "./index.d.cts", "engines": {"node": ">=16.0.0"}, "exports": {"import": "./index.mjs", "require": "./index.cjs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmetkyknry/helmet-7.0.0.crushed.tgz", "_integrity": "sha512-MsIgYmdBh460ZZ8cJC81q4XJknjG567wzEmv46WOBblDb6TUd3z8/GhgmsM9pn8g2B80tAJ4m5/d3Bi1KrSUBQ==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_7.0.0_1683412674990_0.2099021517642372", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "helmet", "version": "7.1.0", "keywords": ["express", "security", "headers", "backend", "content-security-policy", "cross-origin-embedder-policy", "cross-origin-opener-policy", "cross-origin-resource-policy", "origin-agent-cluster", "referrer-policy", "strict-transport-security", "x-content-type-options", "x-dns-prefetch-control", "x-download-options", "x-frame-options", "x-permitted-cross-domain-policies", "x-powered-by", "x-xss-protection"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@7.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "287279e00f8a3763d5dccbaf1e5ee39b8c3784ca", "tarball": "https://registry.npmjs.org/helmet/-/helmet-7.1.0.tgz", "fileCount": 9, "integrity": "sha512-g+HZqgfbpXdCkme/Cd/mZkV0aV3BZZZSugecH03kl38m/Kmdx8jKjBikpDj2cr+Iynv4KpYEviojNdTJActJAg==", "signatures": [{"sig": "MEQCIDDKhUiQDxx7mrXebtDmXKc0XC4Jpz7OCvFs9sP2ua4KAiBLrOVYCmsNps+dKFCYevROtyuCiKaVuXcLW71AhC7huQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101878}, "main": "./index.cjs", "_from": "file:/tmp/helmeto37SAU/helmet-7.1.0.crushed.tgz", "types": "./index.d.cts", "engines": {"node": ">=16.0.0"}, "exports": {"import": "./index.mjs", "require": "./index.cjs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmeto37SAU/helmet-7.1.0.crushed.tgz", "_integrity": "sha512-g+HZqgfbpXdCkme/Cd/mZkV0aV3BZZZSugecH03kl38m/Kmdx8jKjBikpDj2cr+Iynv4KpYEviojNdTJActJAg==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "20.9.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_7.1.0_1699367169324_0.3140762383301645", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "helmet", "version": "7.2.0", "keywords": ["express", "security", "headers", "backend", "content-security-policy", "cross-origin-embedder-policy", "cross-origin-opener-policy", "cross-origin-resource-policy", "origin-agent-cluster", "referrer-policy", "strict-transport-security", "x-content-type-options", "x-dns-prefetch-control", "x-download-options", "x-frame-options", "x-permitted-cross-domain-policies", "x-powered-by", "x-xss-protection"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@7.2.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "8b2dcc425b4a46c88f6953481b40453cbe66b167", "tarball": "https://registry.npmjs.org/helmet/-/helmet-7.2.0.tgz", "fileCount": 9, "integrity": "sha512-ZRiwvN089JfMXokizgqEPXsl2Guk094yExfoDXR0cBYWxtBbaSww/w+vT4WEJsBW2iTUi1GgZ6swmoug3Oy4Xw==", "signatures": [{"sig": "MEUCIQDg2PjzwnJsbjPGUhaXaTPlxWLaqQTveRpAtEW2lpyhGgIgT5czPiCBj3eXVJl23UrODuiIGLtXAgC+ArgGv89CQcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104149}, "main": "./index.cjs", "_from": "file:/tmp/helmet7QBTVg/helmet-7.2.0.crushed.tgz", "types": "./index.d.cts", "engines": {"node": ">=16.0.0"}, "exports": {"import": "./index.mjs", "require": "./index.cjs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmet7QBTVg/helmet-7.2.0.crushed.tgz", "_integrity": "sha512-ZRiwvN089JfMXokizgqEPXsl2Guk094yExfoDXR0cBYWxtBbaSww/w+vT4WEJsBW2iTUi1GgZ6swmoug3Oy4Xw==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_7.2.0_1727562622037_0.44323710148204865", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "helmet", "version": "8.0.0", "keywords": ["express", "security", "headers", "backend", "content-security-policy", "cross-origin-embedder-policy", "cross-origin-opener-policy", "cross-origin-resource-policy", "origin-agent-cluster", "referrer-policy", "strict-transport-security", "x-content-type-options", "x-dns-prefetch-control", "x-download-options", "x-frame-options", "x-permitted-cross-domain-policies", "x-powered-by", "x-xss-protection"], "author": {"url": "https://evilpacket.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "helmet@8.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://evanhahn.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "dist": {"shasum": "05370fb1953aa7b81bd0ddfa459221247be6ea5c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-8.0.0.tgz", "fileCount": 9, "integrity": "sha512-VyusHLEIIO5mjQPUI1wpOAEu+wl6Q0998jzTxqUYGE45xCIcAxy3MsbEK/yyJUJ3ADeMoB6MornPH6GMWAf+Pw==", "signatures": [{"sig": "MEQCIAawSzgieuR+lB5yvfzyLc/DdE3DOP1/B3Nr0uUOxZhqAiAkHjLGERIsRSQYmSHVspLDbqywZjjSd2y1nXn0FlsO+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103289}, "main": "./index.cjs", "_from": "file:/tmp/helmetW4vMH3/helmet-8.0.0.crushed.tgz", "types": "./index.d.cts", "engines": {"node": ">=18.0.0"}, "exports": {"import": "./index.mjs", "require": "./index.cjs"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_resolved": "/tmp/helmetW4vMH3/helmet-8.0.0.crushed.tgz", "_integrity": "sha512-VyusHLEIIO5mjQPUI1wpOAEu+wl6Q0998jzTxqUYGE45xCIcAxy3MsbEK/yyJUJ3ADeMoB6MornPH6GMWAf+Pw==", "repository": {"url": "git://github.com/helmetjs/helmet.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "help secure Express/Connect apps with various HTTP headers", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helmet_8.0.0_1727563144453_0.2637632347992158", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "helmet", "description": "help secure Express/Connect apps with various HTTP headers", "version": "8.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://evilpacket.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://evanhahn.com"}], "homepage": "https://helmetjs.github.io/", "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/helmetjs/helmet.git"}, "license": "MIT", "keywords": ["express", "security", "headers", "backend", "content-security-policy", "cross-origin-embedder-policy", "cross-origin-opener-policy", "cross-origin-resource-policy", "origin-agent-cluster", "referrer-policy", "strict-transport-security", "x-content-type-options", "x-dns-prefetch-control", "x-download-options", "x-frame-options", "x-permitted-cross-domain-policies", "x-powered-by", "x-xss-protection"], "engines": {"node": ">=18.0.0"}, "exports": {"import": "./index.mjs", "require": "./index.cjs"}, "main": "./index.cjs", "types": "./index.d.cts", "_id": "helmet@8.1.0", "_integrity": "sha512-jOiHyAZsmnr8LqoPGmCjYAaiuWwjAPLgY8ZX2XrmHawt99/u1y6RgrZMTeoPfpUbV96HOalYgz1qzkRbw54Pmg==", "_resolved": "/tmp/helmetRgBE3Y/helmet-8.1.0.crushed.tgz", "_from": "file:/tmp/helmetRgBE3Y/helmet-8.1.0.crushed.tgz", "_nodeVersion": "18.20.7", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-jOiHyAZsmnr8LqoPGmCjYAaiuWwjAPLgY8ZX2XrmHawt99/u1y6RgrZMTeoPfpUbV96HOalYgz1qzkRbw54Pmg==", "shasum": "f96d23fedc89e9476ecb5198181009c804b8b38c", "tarball": "https://registry.npmjs.org/helmet/-/helmet-8.1.0.tgz", "fileCount": 9, "unpackedSize": 103678, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCRJFSmZsOCLpUSWZI+b/eIKujUQm7AFlf3As/Jyy7+3AIgBTYoWMTNiqYQocgt7I7YaV11F90Sw+7pKCjXuLeYTWU="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helmet_8.1.0_1742238046293_0.6642346415884528"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-02-02T18:58:33.918Z", "modified": "2025-03-17T19:00:46.663Z", "0.0.1": "2012-02-02T18:58:35.661Z", "0.0.2": "2012-02-03T17:21:41.732Z", "0.0.3": "2012-02-04T17:22:42.384Z", "0.0.4": "2012-06-01T04:02:53.099Z", "0.0.5": "2012-06-06T00:17:14.270Z", "0.0.6": "2012-06-12T07:02:54.870Z", "0.0.7": "2012-09-26T16:09:19.337Z", "0.0.8": "2013-03-13T14:57:11.756Z", "0.0.9": "2013-03-14T01:33:29.242Z", "0.0.10": "2013-07-11T21:50:44.408Z", "0.0.11": "2013-07-23T17:38:06.412Z", "0.1.0": "2013-07-23T23:42:31.421Z", "0.1.1": "2013-10-22T18:31:10.612Z", "0.1.2": "2013-11-03T00:31:07.927Z", "0.1.3": "2014-03-27T23:40:11.812Z", "0.2.0": "2014-03-31T16:13:30.603Z", "0.2.1": "2014-04-13T16:35:18.010Z", "0.2.2": "2014-06-01T22:27:54.018Z", "0.2.3": "2014-06-02T05:28:05.090Z", "0.2.4": "2014-06-04T19:21:42.976Z", "0.3.0": "2014-06-27T06:46:36.181Z", "0.3.1": "2014-06-27T06:56:13.348Z", "0.3.2": "2014-06-30T17:38:03.035Z", "0.4.0": "2014-07-18T00:31:00.023Z", "0.4.1": "2014-08-24T15:39:41.095Z", "0.4.2": "2014-10-16T23:36:41.410Z", "0.5.0": "2014-10-28T17:22:30.063Z", "0.5.1": "2014-11-09T15:52:21.409Z", "0.5.2": "2014-11-16T20:46:33.421Z", "0.5.3": "2014-12-08T13:23:26.640Z", "0.5.4": "2014-12-21T20:37:52.045Z", "0.6.0": "2015-01-22T06:08:02.846Z", "0.6.1": "2015-02-13T17:07:25.633Z", "0.6.2": "2015-03-02T23:21:30.512Z", "0.7.0": "2015-03-05T22:37:13.744Z", "0.7.1": "2015-03-23T15:57:21.291Z", "0.8.0": "2015-04-22T01:12:45.441Z", "0.9.0": "2015-04-24T21:38:39.806Z", "0.9.1": "2015-06-02T21:53:59.592Z", "0.10.0": "2015-07-09T01:11:39.457Z", "0.11.0": "2015-09-18T23:30:50.327Z", "0.12.0": "2015-09-22T19:12:02.820Z", "0.13.0": "2015-10-23T17:34:10.404Z", "0.14.0": "2015-11-01T15:50:55.084Z", "0.15.0": "2015-11-26T18:43:37.588Z", "1.0.0": "2015-12-19T02:01:27.693Z", "1.0.1": "2015-12-19T15:59:03.005Z", "1.0.2": "2016-01-08T18:51:15.454Z", "1.1.0": "2016-01-13T05:44:04.992Z", "1.2.0": "2016-02-29T15:09:24.401Z", "1.3.0": "2016-03-02T01:24:22.026Z", "2.0.0": "2016-04-29T18:23:37.346Z", "2.1.0": "2016-05-18T23:58:04.739Z", "2.1.1": "2016-06-10T20:56:04.553Z", "2.1.2": "2016-07-27T18:34:58.260Z", "2.1.3": "2016-09-07T14:53:45.311Z", "2.2.0": "2016-09-16T19:56:03.224Z", "2.3.0": "2016-09-30T22:52:59.511Z", "3.0.0": "2016-10-28T21:03:55.938Z", "3.1.0": "2016-11-03T21:11:24.110Z", "3.2.0": "2016-12-22T18:25:32.665Z", "3.3.0": "2016-12-31T22:09:11.580Z", "3.4.0": "2017-01-13T23:58:00.056Z", "3.4.1": "2017-02-25T00:50:37.913Z", "3.5.0": "2017-03-09T17:48:31.110Z", "3.6.0": "2017-05-04T22:48:55.451Z", "3.6.1": "2017-05-21T22:22:22.236Z", "3.7.0": "2017-07-21T16:17:52.806Z", "3.8.0": "2017-07-21T20:24:57.548Z", "3.8.1": "2017-07-28T17:28:09.803Z", "3.8.2": "2017-09-27T14:10:38.745Z", "3.9.0": "2017-10-13T15:43:16.851Z", "3.10.0": "2018-01-23T17:02:19.624Z", "3.11.0": "2018-02-09T15:20:48.548Z", "3.12.0": "2018-03-02T16:38:15.145Z", "3.12.1": "2018-05-16T21:18:23.307Z", "3.12.2": "2018-07-20T22:45:35.472Z", "3.13.0": "2018-07-22T19:39:07.749Z", "3.14.0": "2018-10-09T17:58:09.361Z", "3.15.0": "2018-11-07T16:47:14.883Z", "3.15.1": "2019-02-10T17:42:33.536Z", "3.16.0": "2019-03-10T18:25:07.863Z", "3.17.0": "2019-05-03T15:53:27.940Z", "3.18.0": "2019-05-05T19:47:21.888Z", "3.19.0": "2019-07-17T14:05:06.336Z", "3.20.0": "2019-07-24T14:36:03.197Z", "3.20.1": "2019-08-28T18:15:48.569Z", "3.21.0": "2019-09-04T14:59:03.663Z", "3.21.1": "2019-09-20T18:06:15.663Z", "3.21.2": "2019-10-21T20:23:33.489Z", "3.21.3": "2020-02-24T23:27:15.085Z", "3.22.0": "2020-03-24T19:20:52.468Z", "3.22.1": "2020-06-10T22:03:31.214Z", "3.23.0": "2020-06-12T14:28:50.602Z", "3.23.1": "2020-06-16T16:25:47.024Z", "3.23.2": "2020-06-23T14:57:02.953Z", "3.23.3": "2020-06-26T17:55:48.874Z", "4.0.0-alpha.1": "2020-07-12T19:19:39.259Z", "4.0.0-rc.1": "2020-07-28T16:43:19.184Z", "4.0.0-rc.2": "2020-07-28T20:10:05.000Z", "4.0.0": "2020-08-02T14:50:04.928Z", "4.1.0-rc.1": "2020-08-10T14:34:05.659Z", "4.1.0-rc.2": "2020-08-12T01:13:08.588Z", "4.1.0": "2020-08-15T14:18:40.647Z", "4.1.1": "2020-09-10T21:06:08.200Z", "4.2.0": "2020-11-01T21:49:27.103Z", "4.3.0": "2020-12-27T20:55:21.963Z", "4.3.1": "2020-12-27T22:37:34.923Z", "4.4.0": "2021-01-18T02:36:53.886Z", "4.4.1": "2021-01-18T18:34:04.680Z", "4.5.0-rc.1": "2021-04-04T21:36:53.520Z", "4.5.0": "2021-04-17T20:33:55.282Z", "4.6.0": "2021-05-02T15:13:09.307Z", "5.0.0-beta.1": "2021-12-05T21:26:32.476Z", "5.0.0": "2022-01-02T18:04:30.821Z", "5.0.1": "2022-01-03T15:25:33.883Z", "5.0.2": "2022-01-22T20:35:10.705Z", "5.1.0": "2022-05-17T17:08:27.424Z", "5.1.1": "2022-07-23T14:57:03.933Z", "6.0.0": "2022-08-26T15:13:11.346Z", "6.0.1": "2022-11-29T15:39:52.711Z", "6.1.0": "2023-04-08T16:25:29.872Z", "6.1.1": "2023-04-08T16:44:22.169Z", "6.1.2": "2023-04-09T16:04:00.361Z", "6.1.3": "2023-04-10T18:15:26.683Z", "6.1.4": "2023-04-10T23:14:45.412Z", "6.1.5": "2023-04-11T14:21:04.517Z", "6.2.0": "2023-05-06T22:15:20.903Z", "7.0.0": "2023-05-06T22:37:55.176Z", "7.1.0": "2023-11-07T14:26:09.552Z", "7.2.0": "2024-09-28T22:30:22.234Z", "8.0.0": "2024-09-28T22:39:04.651Z", "8.1.0": "2025-03-17T19:00:46.493Z"}, "bugs": {"url": "https://github.com/helmetjs/helmet/issues", "email": "<EMAIL>"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://evilpacket.net"}, "license": "MIT", "homepage": "https://helmetjs.github.io/", "keywords": ["express", "security", "headers", "backend", "content-security-policy", "cross-origin-embedder-policy", "cross-origin-opener-policy", "cross-origin-resource-policy", "origin-agent-cluster", "referrer-policy", "strict-transport-security", "x-content-type-options", "x-dns-prefetch-control", "x-download-options", "x-frame-options", "x-permitted-cross-domain-policies", "x-powered-by", "x-xss-protection"], "repository": {"type": "git", "url": "git://github.com/helmetjs/helmet.git"}, "description": "help secure Express/Connect apps with various HTTP headers", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://evanhahn.com"}], "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# Helmet\n\nHelp secure Express apps by setting HTTP response headers.\n\n```javascript\nimport helmet from \"helmet\";\n\nconst app = express();\n\napp.use(helmet());\n```\n\nHelmet sets the following headers by default:\n\n- [`Content-Security-Policy`](#content-security-policy): A powerful allow-list of what can happen on your page which mitigates many attacks\n- [`Cross-Origin-Opener-Policy`](#cross-origin-opener-policy): Helps process-isolate your page\n- [`Cross-Origin-Resource-Policy`](#cross-origin-resource-policy): Blocks others from loading your resources cross-origin\n- [`Origin-Agent-Cluster`](#origin-agent-cluster): Changes process isolation to be origin-based\n- [`Referrer-Policy`](#referrer-policy): Controls the [`Referer`][Referer] header\n- [`Strict-Transport-Security`](#strict-transport-security): Tells browsers to prefer HTTPS\n- [`X-Content-Type-Options`](#x-content-type-options): Avoids [MIME sniffing]\n- [`X-DNS-Prefetch-Control`](#x-dns-prefetch-control): Controls DNS prefetching\n- [`X-Download-Options`](#x-download-options): Forces downloads to be saved (Internet Explorer only)\n- [`X-Frame-Options`](#x-frame-options): Legacy header that mitigates [clickjacking] attacks\n- [`X-Permitted-Cross-Domain-Policies`](#x-permitted-cross-domain-policies): Controls cross-domain behavior for Adobe products, like Acrobat\n- [`X-Powered-By`](#x-powered-by): Info about the web server. Removed because it could be used in simple attacks\n- [`X-XSS-Protection`](#x-xss-protection): Legacy header that tries to mitigate [XSS attacks][XSS], but makes things worse, so Helmet disables it\n\nEach header can be configured. For example, here's how you configure the `Content-Security-Policy` header:\n\n```js\n// Configure the Content-Security-Policy header.\napp.use(\n  helmet({\n    contentSecurityPolicy: {\n      directives: {\n        \"script-src\": [\"'self'\", \"example.com\"],\n      },\n    },\n  }),\n);\n```\n\nHeaders can also be disabled. For example, here's how you disable the `Content-Security-Policy` and `X-Download-Options` headers:\n\n```js\n// Disable the Content-Security-Policy and X-Download-Options headers\napp.use(\n  helmet({\n    contentSecurityPolicy: false,\n    xDownloadOptions: false,\n  }),\n);\n```\n\n## Reference\n\n<details id=\"content-security-policy\">\n<summary><code>Content-Security-Policy</code></summary>\n\nDefault:\n\n```http\nContent-Security-Policy: default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests\n```\n\nThe `Content-Security-Policy` header mitigates a large number of attacks, such as [cross-site scripting][XSS]. See [MDN's introductory article on Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP).\n\nThis header is powerful but likely requires some configuration for your specific app.\n\nTo configure this header, pass an object with a nested `directives` object. Each key is a directive name in camel case (such as `defaultSrc`) or kebab case (such as `default-src`). Each value is an array (or other iterable) of strings or functions for that directive. If a function appears in the array, it will be called with the request and response objects.\n\n```javascript\n// Sets all of the defaults, but overrides `script-src`\n// and disables the default `style-src`.\napp.use(\n  helmet({\n    contentSecurityPolicy: {\n      directives: {\n        \"script-src\": [\"'self'\", \"example.com\"],\n        \"style-src\": null,\n      },\n    },\n  }),\n);\n```\n\n```js\n// Sets the `script-src` directive to\n// \"'self' 'nonce-e33cc...'\"\n// (or similar)\napp.use((req, res, next) => {\n  res.locals.cspNonce = crypto.randomBytes(32).toString(\"hex\");\n  next();\n});\napp.use(\n  helmet({\n    contentSecurityPolicy: {\n      directives: {\n        scriptSrc: [\"'self'\", (req, res) => `'nonce-${res.locals.cspNonce}'`],\n      },\n    },\n  }),\n);\n```\n\nThese directives are merged into a default policy, which you can disable by setting `useDefaults` to `false`.\n\n```javascript\n// Sets \"Content-Security-Policy: default-src 'self';\n// script-src 'self' example.com;object-src 'none';\n// upgrade-insecure-requests\"\napp.use(\n  helmet({\n    contentSecurityPolicy: {\n      useDefaults: false,\n      directives: {\n        defaultSrc: [\"'self'\"],\n        scriptSrc: [\"'self'\", \"example.com\"],\n        objectSrc: [\"'none'\"],\n        upgradeInsecureRequests: [],\n      },\n    },\n  }),\n);\n```\n\nYou can get the default directives object with `helmet.contentSecurityPolicy.getDefaultDirectives()`. Here is the default policy (formatted for readability):\n\n```\ndefault-src 'self';\nbase-uri 'self';\nfont-src 'self' https: data:;\nform-action 'self';\nframe-ancestors 'self';\nimg-src 'self' data:;\nobject-src 'none';\nscript-src 'self';\nscript-src-attr 'none';\nstyle-src 'self' https: 'unsafe-inline';\nupgrade-insecure-requests\n```\n\nThe `default-src` directive can be explicitly disabled by setting its value to `helmet.contentSecurityPolicy.dangerouslyDisableDefaultSrc`, but this is not recommended.\n\nYou can set the [`Content-Security-Policy-Report-Only`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy-Report-Only) instead:\n\n```javascript\n// Sets the Content-Security-Policy-Report-Only header\napp.use(\n  helmet({\n    contentSecurityPolicy: {\n      directives: {\n        /* ... */\n      },\n      reportOnly: true,\n    },\n  }),\n);\n```\n\nHelmet performs very little validation on your CSP. You should rely on CSP checkers like [CSP Evaluator](https://csp-evaluator.withgoogle.com/) instead.\n\nTo disable the `Content-Security-Policy` header:\n\n```js\napp.use(\n  helmet({\n    contentSecurityPolicy: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.contentSecurityPolicy())`.\n\n</details>\n\n<details id=\"cross-origin-embedder-policy\">\n<summary><code>Cross-Origin-Embedder-Policy</code></summary>\n\nThis header is not set by default.\n\nThe `Cross-Origin-Embedder-Policy` header helps control what resources can be loaded cross-origin. See [MDN's article on this header](https://developer.cdn.mozilla.net/en-US/docs/Web/HTTP/Headers/Cross-Origin-Embedder-Policy) for more.\n\n```js\n// Helmet does not set Cross-Origin-Embedder-Policy\n// by default.\napp.use(helmet());\n\n// Sets \"Cross-Origin-Embedder-Policy: require-corp\"\napp.use(helmet({ crossOriginEmbedderPolicy: true }));\n\n// Sets \"Cross-Origin-Embedder-Policy: credentialless\"\napp.use(helmet({ crossOriginEmbedderPolicy: { policy: \"credentialless\" } }));\n```\n\nYou can use this as standalone middleware with `app.use(helmet.crossOriginEmbedderPolicy())`.\n\n</details>\n\n<details id=\"cross-origin-opener-policy\">\n<summary><code>Cross-Origin-Opener-Policy</code></summary>\n\nDefault:\n\n```http\nCross-Origin-Opener-Policy: same-origin\n```\n\nThe `Cross-Origin-Opener-Policy` header helps process-isolate your page. For more, see [MDN's article on this header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Opener-Policy).\n\n```js\n// Sets \"Cross-Origin-Opener-Policy: same-origin\"\napp.use(helmet());\n\n// Sets \"Cross-Origin-Opener-Policy: same-origin-allow-popups\"\napp.use(\n  helmet({\n    crossOriginOpenerPolicy: { policy: \"same-origin-allow-popups\" },\n  }),\n);\n```\n\nTo disable the `Cross-Origin-Opener-Policy` header:\n\n```js\napp.use(\n  helmet({\n    crossOriginOpenerPolicy: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.crossOriginOpenerPolicy())`.\n\n</details>\n\n<details id=\"cross-origin-resource-policy\">\n<summary><code>Cross-Origin-Resource-Policy</code></summary>\n\nDefault:\n\n```http\nCross-Origin-Resource-Policy: same-origin\n```\n\nThe `Cross-Origin-Resource-Policy` header blocks others from loading your resources cross-origin in some cases. For more, see [\"Consider deploying Cross-Origin Resource Policy\"](https://resourcepolicy.fyi/) and [MDN's article on this header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Resource-Policy).\n\n```js\n// Sets \"Cross-Origin-Resource-Policy: same-origin\"\napp.use(helmet());\n\n// Sets \"Cross-Origin-Resource-Policy: same-site\"\napp.use(helmet({ crossOriginResourcePolicy: { policy: \"same-site\" } }));\n```\n\nTo disable the `Cross-Origin-Resource-Policy` header:\n\n```js\napp.use(\n  helmet({\n    crossOriginResourcePolicy: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.crossOriginResourcePolicy())`.\n\n</details>\n\n<details id=\"origin-agent-cluster\">\n<summary><code>Origin-Agent-Cluster</code></summary>\n\nDefault:\n\n```http\nOrigin-Agent-Cluster: ?1\n```\n\nThe `Origin-Agent-Cluster` header provides a mechanism to allow web applications to isolate their origins from other processes. Read more about it [in the spec](https://whatpr.org/html/6214/origin.html#origin-keyed-agent-clusters).\n\nThis header takes no options and is set by default.\n\n```js\n// Sets \"Origin-Agent-Cluster: ?1\"\napp.use(helmet());\n```\n\nTo disable the `Origin-Agent-Cluster` header:\n\n```js\napp.use(\n  helmet({\n    originAgentCluster: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.originAgentCluster())`.\n\n</details>\n\n<details id=\"referrer-policy\">\n<summary><code>Referrer-Policy</code></summary>\n\nDefault:\n\n```http\nReferrer-Policy: no-referrer\n```\n\nThe `Referrer-Policy` header which controls what information is set in [the `Referer` request header][Referer]. See [\"Referer header: privacy and security concerns\"](https://developer.mozilla.org/en-US/docs/Web/Security/Referer_header:_privacy_and_security_concerns) and [the header's documentation](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Referrer-Policy) on MDN for more.\n\n```js\n// Sets \"Referrer-Policy: no-referrer\"\napp.use(helmet());\n```\n\n`policy` is a string or array of strings representing the policy. If passed as an array, it will be joined with commas, which is useful when setting [a fallback policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Referrer-Policy#Specifying_a_fallback_policy). It defaults to `no-referrer`.\n\n```js\n// Sets \"Referrer-Policy: no-referrer\"\napp.use(\n  helmet({\n    referrerPolicy: {\n      policy: \"no-referrer\",\n    },\n  }),\n);\n\n// Sets \"Referrer-Policy: origin,unsafe-url\"\napp.use(\n  helmet({\n    referrerPolicy: {\n      policy: [\"origin\", \"unsafe-url\"],\n    },\n  }),\n);\n```\n\nTo disable the `Referrer-Policy` header:\n\n```js\napp.use(\n  helmet({\n    referrerPolicy: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.referrerPolicy())`.\n\n</details>\n\n<details id=\"strict-transport-security\">\n<summary><code>Strict-Transport-Security</code></summary>\n\nDefault:\n\n```http\nStrict-Transport-Security: max-age=31536000; includeSubDomains\n```\n\nThe `Strict-Transport-Security` header tells browsers to prefer HTTPS instead of insecure HTTP. See [the documentation on MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Strict-Transport-Security) for more.\n\n```js\n// Sets \"Strict-Transport-Security: max-age=31536000; includeSubDomains\"\napp.use(helmet());\n```\n\n`maxAge` is the number of seconds browsers should remember to prefer HTTPS. If passed a non-integer, the value is rounded down. It defaults to 365 days.\n\n`includeSubDomains` is a boolean which dictates whether to include the `includeSubDomains` directive, which makes this policy extend to subdomains. It defaults to `true`.\n\n`preload` is a boolean. If true, it adds the `preload` directive, expressing intent to add your HSTS policy to browsers. See [the \"Preloading Strict Transport Security\" section on MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Strict-Transport-Security#Preloading_Strict_Transport_Security) for more. It defaults to `false`.\n\n```js\n// Sets \"Strict-Transport-Security: max-age=123456; includeSubDomains\"\napp.use(\n  helmet({\n    strictTransportSecurity: {\n      maxAge: 123456,\n    },\n  }),\n);\n\n// Sets \"Strict-Transport-Security: max-age=123456\"\napp.use(\n  helmet({\n    strictTransportSecurity: {\n      maxAge: 123456,\n      includeSubDomains: false,\n    },\n  }),\n);\n\n// Sets \"Strict-Transport-Security: max-age=123456; includeSubDomains; preload\"\napp.use(\n  helmet({\n    strictTransportSecurity: {\n      maxAge: 63072000,\n      preload: true,\n    },\n  }),\n);\n```\n\nTo disable the `Strict-Transport-Security` header:\n\n```js\napp.use(\n  helmet({\n    strictTransportSecurity: false,\n  }),\n);\n```\n\nYou may wish to disable this header for local development, as it can make your browser force redirects from `http://localhost` to `https://localhost`, which may not be desirable if you develop multiple apps using `localhost`. See [this issue](https://github.com/helmetjs/helmet/issues/451) for more discussion.\n\nYou can use this as standalone middleware with `app.use(helmet.strictTransportSecurity())`.\n\n</details>\n\n<details id=\"x-content-type-options\">\n<summary><code>X-Content-Type-Options</code></summary>\n\nDefault:\n\n```http\nX-Content-Type-Options: nosniff\n```\n\nThe `X-Content-Type-Options` mitigates [MIME type sniffing](https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types#MIME_sniffing) which can cause security issues. See [documentation for this header on MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Content-Type-Options) for more.\n\nThis header takes no options and is set by default.\n\n```js\n// Sets \"X-Content-Type-Options: nosniff\"\napp.use(helmet());\n```\n\nTo disable the `X-Content-Type-Options` header:\n\n```js\napp.use(\n  helmet({\n    xContentTypeOptions: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.xContentTypeOptions())`.\n\n</details>\n\n<details id=\"x-dns-prefetch-control\">\n<summary><code>X-DNS-Prefetch-Control</code></summary>\n\nDefault:\n\n```http\nX-DNS-Prefetch-Control: off\n```\n\nThe `X-DNS-Prefetch-Control` header helps control DNS prefetching, which can improve user privacy at the expense of performance. See [documentation on MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-DNS-Prefetch-Control) for more.\n\n```js\n// Sets \"X-DNS-Prefetch-Control: off\"\napp.use(helmet());\n```\n\n`allow` is a boolean dictating whether to enable DNS prefetching. It defaults to `false`.\n\nExamples:\n\n```js\n// Sets \"X-DNS-Prefetch-Control: off\"\napp.use(\n  helmet({\n    xDnsPrefetchControl: { allow: false },\n  }),\n);\n\n// Sets \"X-DNS-Prefetch-Control: on\"\napp.use(\n  helmet({\n    xDnsPrefetchControl: { allow: true },\n  }),\n);\n```\n\nTo disable the `X-DNS-Prefetch-Control` header and use the browser's default value:\n\n```js\napp.use(\n  helmet({\n    xDnsPrefetchControl: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.xDnsPrefetchControl())`.\n\n</details>\n\n<details id=\"x-download-options\">\n<summary><code>X-Download-Options</code></summary>\n\nDefault:\n\n```http\nX-Download-Options: noopen\n```\n\nThe `X-Download-Options` header is specific to Internet Explorer 8. It forces potentially-unsafe downloads to be saved, mitigating execution of HTML in your site's context. For more, see [this old post on MSDN](https://docs.microsoft.com/en-us/archive/blogs/ie/ie8-security-part-v-comprehensive-protection).\n\nThis header takes no options and is set by default.\n\n```js\n// Sets \"X-Download-Options: noopen\"\napp.use(helmet());\n```\n\nTo disable the `X-Download-Options` header:\n\n```js\napp.use(\n  helmet({\n    xDownloadOptions: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.xDownloadOptions())`.\n\n</details>\n\n<details id=\"x-frame-options\">\n<summary><code>X-Frame-Options</code></summary>\n\nDefault:\n\n```http\nX-Frame-Options: SAMEORIGIN\n```\n\nThe legacy `X-Frame-Options` header to help you mitigate [clickjacking attacks](https://en.wikipedia.org/wiki/Clickjacking). This header is superseded by [the `frame-ancestors` Content Security Policy directive](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/frame-ancestors) but is still useful on old browsers or if no CSP is used. For more, see [the documentation on MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options).\n\n```js\n// Sets \"X-Frame-Options: SAMEORIGIN\"\napp.use(helmet());\n```\n\n`action` is a string that specifies which directive to use—either `DENY` or `SAMEORIGIN`. (A legacy directive, `ALLOW-FROM`, is not supported by Helmet. [Read more here.](https://github.com/helmetjs/helmet/wiki/How-to-use-X%E2%80%93Frame%E2%80%93Options's-%60ALLOW%E2%80%93FROM%60-directive)) It defaults to `SAMEORIGIN`.\n\nExamples:\n\n```js\n// Sets \"X-Frame-Options: DENY\"\napp.use(\n  helmet({\n    xFrameOptions: { action: \"deny\" },\n  }),\n);\n\n// Sets \"X-Frame-Options: SAMEORIGIN\"\napp.use(\n  helmet({\n    xFrameOptions: { action: \"sameorigin\" },\n  }),\n);\n```\n\nTo disable the `X-Frame-Options` header:\n\n```js\napp.use(\n  helmet({\n    xFrameOptions: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.xFrameOptions())`.\n\n</details>\n\n<details id=\"x-permitted-cross-domain-policies\">\n<summary><code>X-Permitted-Cross-Domain-Policies</code></summary>\n\nDefault:\n\n```http\nX-Permitted-Cross-Domain-Policies: none\n```\n\nThe `X-Permitted-Cross-Domain-Policies` header tells some clients (mostly Adobe products) your domain's policy for loading cross-domain content. See [the description on OWASP](https://owasp.org/www-project-secure-headers/) for more.\n\n```js\n// Sets \"X-Permitted-Cross-Domain-Policies: none\"\napp.use(helmet());\n```\n\n`permittedPolicies` is a string that must be `\"none\"`, `\"master-only\"`, `\"by-content-type\"`, or `\"all\"`. It defaults to `\"none\"`.\n\nExamples:\n\n```js\n// Sets \"X-Permitted-Cross-Domain-Policies: none\"\napp.use(\n  helmet({\n    xPermittedCrossDomainPolicies: {\n      permittedPolicies: \"none\",\n    },\n  }),\n);\n\n// Sets \"X-Permitted-Cross-Domain-Policies: by-content-type\"\napp.use(\n  helmet({\n    xPermittedCrossDomainPolicies: {\n      permittedPolicies: \"by-content-type\",\n    },\n  }),\n);\n```\n\nTo disable the `X-Permitted-Cross-Domain-Policies` header:\n\n```js\napp.use(\n  helmet({\n    xPermittedCrossDomainPolicies: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.xPermittedCrossDomainPolicies())`.\n\n</details>\n\n<details id=\"x-powered-by\">\n<summary><code>X-Powered-By</code></summary>\n\nDefault: the `X-Powered-By` header, if present, is removed.\n\nHelmet removes the `X-Powered-By` header, which is set by default in Express and some other frameworks. Removing the header offers very limited security benefits (see [this discussion](https://github.com/expressjs/express/pull/2813#issuecomment-159270428)) and is mostly removed to save bandwidth, but may thwart simplistic attackers.\n\nNote: [Express has a built-in way to disable the `X-Powered-By` header](https://stackoverflow.com/a/12484642/804100), which you may wish to use instead.\n\nThe removal of this header takes no options. The header is removed by default.\n\nTo disable this behavior:\n\n```js\n// Not required, but recommended for Express users:\napp.disable(\"x-powered-by\");\n\n// Ask Helmet to ignore the X-Powered-By header.\napp.use(\n  helmet({\n    xPoweredBy: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.xPoweredBy())`.\n\n</details>\n\n<details id=\"x-xss-protection\">\n<summary><code>X-XSS-Protection</code></summary>\n\nDefault:\n\n```http\nX-XSS-Protection: 0\n```\n\nHelmet disables browsers' buggy cross-site scripting filter by setting the legacy `X-XSS-Protection` header to `0`. See [discussion about disabling the header here](https://github.com/helmetjs/helmet/issues/230) and [documentation on MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-XSS-Protection).\n\nThis header takes no options and is set by default.\n\nTo disable the `X-XSS-Protection` header:\n\n```js\n// This is not recommended.\napp.use(\n  helmet({\n    xXssProtection: false,\n  }),\n);\n```\n\nYou can use this as standalone middleware with `app.use(helmet.xXssProtection())`.\n\n</details>\n\n[Referer]: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Referer\n[MIME sniffing]: https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types#mime_sniffing\n[Clickjacking]: https://en.wikipedia.org/wiki/Clickjacking\n[XSS]: https://developer.mozilla.org/en-US/docs/Glossary/Cross-site_scripting\n", "readmeFilename": "README.md", "users": {"285858315": true, "fmm": true, "rsp": true, "vbv": true, "dyaa": true, "leny": true, "meeh": true, "nazy": true, "pl0x": true, "tpkn": true, "vunb": true, "wzbg": true, "bengi": true, "denji": true, "dnero": true, "irnnr": true, "jpepe": true, "kikar": true, "kikna": true, "lcdss": true, "madeo": true, "miloc": true, "nagra": true, "panlw": true, "phydo": true, "pmasa": true, "thotk": true, "timdp": true, "travm": true, "wuotr": true, "yatsu": true, "yizen": true, "ajduke": true, "alek-s": true, "bojand": true, "chrisx": true, "daizch": true, "evalon": true, "fpenno": true, "gfilip": true, "glebec": true, "ierceg": true, "isa424": true, "itesic": true, "itskdk": true, "kiknag": true, "knoja4": true, "kperch": true, "lonjoy": true, "ngpvnk": true, "nsaboo": true, "nuwaio": true, "owillo": true, "potnox": true, "quafoo": true, "rajiff": true, "samobo": true, "shriek": true, "tambeb": true, "tcrowe": true, "tomi77": true, "wickie": true, "zhoutk": true, "abhif18": true, "astesio": true, "barenko": true, "bjoerge": true, "creamov": true, "duooduo": true, "endsoul": true, "ferrari": true, "fr-esco": true, "gstroup": true, "hckhanh": true, "hitalos": true, "itonyyo": true, "jcottam": true, "jkabore": true, "keybouh": true, "maxidev": true, "nilz3ro": true, "occsceo": true, "pixel67": true, "progmer": true, "repeale": true, "rnsloan": true, "sejoker": true, "shivayl": true, "silva23": true, "subchen": true, "touskar": true, "ungurys": true, "vboctor": true, "yanghcc": true, "1cr18ni9": true, "abhisekp": true, "aegypius": true, "akinjide": true, "alanshaw": true, "alexkval": true, "amaghfur": true, "astaltin": true, "bchociej": true, "buru1020": true, "chpoon92": true, "clholzin": true, "danday74": true, "davepoon": true, "dburdese": true, "eb.coder": true, "fabioper": true, "fortruth": true, "ghanbari": true, "gurunate": true, "hollobit": true, "hugovila": true, "iamninad": true, "jmsherry": true, "jojohess": true, "koskokos": true, "lanbolin": true, "leodutra": true, "leonzhao": true, "madarche": true, "makenova": true, "merrickp": true, "novograd": true, "oncletom": true, "onel0p3z": true, "paraself": true, "pwarelis": true, "rbartoli": true, "rmanalan": true, "santypk4": true, "shieldax": true, "shiva127": true, "softwind": true, "vishwasc": true, "abuelwafa": true, "alexbudin": true, "andreaj24": true, "antixrist": true, "aprilchen": true, "asadm2706": true, "cilindrox": true, "daviddias": true, "ddkothari": true, "dlpowless": true, "dmoraschi": true, "edmondnow": true, "edwardxyt": true, "firerishi": true, "fistynuts": true, "gochomugo": true, "hash-bang": true, "heartnett": true, "icirellik": true, "igorissen": true, "joseph320": true, "mauricedb": true, "max_devjs": true, "mikestaub": true, "milfromoz": true, "mr-smiley": true, "pastahito": true, "pthoresen": true, "ramzesucr": true, "rbecheras": true, "riverside": true, "rossdavis": true, "sasquatch": true, "shujianbu": true, "slmcassio": true, "snowdream": true, "txredking": true, "undertuga": true, "vteixeira": true, "alshamiri2": true, "ashish.npm": true, "avivharuzi": true, "bkimminich": true, "byossarian": true, "chirag8642": true, "dccunni171": true, "dwayneford": true, "guyharwood": true, "guzhongren": true, "igoreliasm": true, "isaacvitor": true, "keithpepin": true, "leizongmin": true, "lesthertod": true, "madriver44": true, "magicpanda": true, "manjunathd": true, "monkeymonk": true, "morogasper": true, "mrpanigale": true, "nickleefly": true, "oldthunder": true, "raycharles": true, "ricardweii": true, "rocket0191": true, "sasivarnan": true, "tarkeshwar": true, "tiancheng9": true, "tripleaxis": true, "unapersona": true, "wenhsiaoyi": true, "wizardzloy": true, "xchangebit": true, "arkanciscan": true, "arnoldstoba": true, "cbetancourt": true, "esperluette": true, "fengmiaosen": true, "garenyondem": true, "jamesbedont": true, "karlbateman": true, "kobleistvan": true, "kodekracker": true, "mevlutsahin": true, "mlohscheidt": true, "nisimjoseph": true, "poppowerlb2": true, "raisiqueira": true, "scytalezero": true, "tanmaypatel": true, "themadjoker": true, "tobyforever": true, "wangnan0610": true, "abhijitkalta": true, "adrian110288": true, "andrewyang96": true, "donecharlton": true, "dpjayasekara": true, "floriannagel": true, "ghostcode521": true, "grantcarthew": true, "hugojosefson": true, "ivan.marquez": true, "joey.dossche": true, "justdomepaul": true, "khurramijazm": true, "martinspinks": true, "matiasmarani": true, "natterstefan": true, "philiiiiiipp": true, "processbrain": true, "shekharreddy": true, "stevepsharpe": true, "superchenney": true, "wfalkwallace": true, "depoisfalamos": true, "fchienvuhoang": true, "jeremygaither": true, "joel-ericsson": true, "markthethomas": true, "mdedirudianto": true, "piyushmakhija": true, "scottfreecode": true, "anudeep.webdev": true, "bradleybossard": true, "danielbankhead": true, "matteospampani": true, "natarajanmca11": true, "paintedbicycle": true, "shanewholloway": true, "spiros.politis": true, "swapnil_mishra": true, "thomasfoster96": true, "andrew.medvedev": true, "charlietango592": true, "germanattanasio": true, "pierreliefauche": true, "codekraft-studio": true, "horrorandtropics": true, "netoperatorwibby": true, "shashankpallerla": true, "ognjen.jevremovic": true, "obsessiveprogrammer": true, "programmer.severson": true, "boopathisakthivel.in": true, "daniel-lewis-bsc-hons": true}}