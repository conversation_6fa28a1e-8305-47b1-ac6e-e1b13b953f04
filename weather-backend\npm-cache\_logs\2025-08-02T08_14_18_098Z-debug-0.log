0 verbose cli D:\node-js\node.exe D:\node-js\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.3
2 info using node@v22.18.0
3 silly config load:file:D:\node-js\node_modules\npm\npmrc
4 silly config load:file:F:\front\weather-backend\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:D:\node-js\node_cache\etc\npmrc
7 verbose title npm install
8 verbose argv "install" "--cache" "./npm-cache"
9 verbose logfile logs-max:10 dir:F:\front\weather-backend\npm-cache\_logs\2025-08-02T08_14_18_098Z-
10 verbose logfile F:\front\weather-backend\npm-cache\_logs\2025-08-02T08_14_18_098Z-debug-0.log
11 silly logfile done cleaning log files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 silly idealTree buildDeps
14 silly fetch manifest express@^4.18.2
15 silly packumentCache full:https://registry.npmjs.org/express cache-miss
16 http fetch GET 200 https://registry.npmjs.org/express 1418ms (cache miss)
17 silly packumentCache full:https://registry.npmjs.org/express set size:undefined disposed:false
18 silly fetch manifest cors@^2.8.5
19 silly packumentCache full:https://registry.npmjs.org/cors cache-miss
20 http fetch GET 200 https://registry.npmjs.org/cors 331ms (cache miss)
21 silly packumentCache full:https://registry.npmjs.org/cors set size:undefined disposed:false
22 silly fetch manifest helmet@^7.1.0
23 silly packumentCache full:https://registry.npmjs.org/helmet cache-miss
24 http fetch GET 200 https://registry.npmjs.org/helmet 786ms (cache miss)
25 silly packumentCache full:https://registry.npmjs.org/helmet set size:undefined disposed:false
26 silly fetch manifest dotenv@^16.3.1
27 silly packumentCache full:https://registry.npmjs.org/dotenv cache-miss
28 http fetch GET 200 https://registry.npmjs.org/npm 2838ms
29 http fetch GET 200 https://registry.npmjs.org/dotenv 3252ms (cache miss)
30 silly packumentCache full:https://registry.npmjs.org/dotenv set size:undefined disposed:false
31 silly fetch manifest axios@^1.6.2
32 silly packumentCache full:https://registry.npmjs.org/axios cache-miss
33 http fetch GET 200 https://registry.npmjs.org/axios 783ms (cache miss)
34 silly packumentCache full:https://registry.npmjs.org/axios set size:undefined disposed:false
35 silly fetch manifest node-cache@^5.1.2
36 silly packumentCache full:https://registry.npmjs.org/node-cache cache-miss
37 http fetch GET 200 https://registry.npmjs.org/node-cache 1136ms (cache miss)
38 silly packumentCache full:https://registry.npmjs.org/node-cache set size:undefined disposed:false
39 silly fetch manifest express-rate-limit@^7.1.5
40 silly packumentCache full:https://registry.npmjs.org/express-rate-limit cache-miss
41 http fetch GET 200 https://registry.npmjs.org/express-rate-limit 1150ms (cache miss)
42 silly packumentCache full:https://registry.npmjs.org/express-rate-limit set size:undefined disposed:false
43 silly fetch manifest morgan@^1.10.0
44 silly packumentCache full:https://registry.npmjs.org/morgan cache-miss
45 http fetch GET 200 https://registry.npmjs.org/morgan 386ms (cache miss)
46 silly packumentCache full:https://registry.npmjs.org/morgan set size:undefined disposed:false
47 silly fetch manifest nodemon@^3.0.2
48 silly packumentCache full:https://registry.npmjs.org/nodemon cache-miss
49 http fetch GET 200 https://registry.npmjs.org/nodemon 1266ms (cache miss)
50 silly packumentCache full:https://registry.npmjs.org/nodemon set size:undefined disposed:false
51 silly fetch manifest jest@^29.7.0
52 silly packumentCache full:https://registry.npmjs.org/jest cache-miss
53 http fetch GET 200 https://registry.npmjs.org/jest 1384ms (cache miss)
54 silly packumentCache full:https://registry.npmjs.org/jest set size:undefined disposed:false
55 silly fetch manifest node-notifier@^8.0.1 || ^9.0.0 || ^10.0.0
56 silly packumentCache full:https://registry.npmjs.org/node-notifier cache-miss
57 http fetch GET 200 https://registry.npmjs.org/node-notifier 1021ms (cache miss)
58 silly packumentCache full:https://registry.npmjs.org/node-notifier set size:undefined disposed:false
59 silly fetch manifest supertest@^6.3.3
60 silly packumentCache full:https://registry.npmjs.org/supertest cache-miss
61 http fetch GET 200 https://registry.npmjs.org/supertest 1427ms (cache miss)
62 silly packumentCache full:https://registry.npmjs.org/supertest set size:undefined disposed:false
63 silly placeDep ROOT axios@1.11.0 OK for: weather-backend@1.0.0 want: ^1.6.2
64 silly placeDep ROOT cors@2.8.5 OK for: weather-backend@1.0.0 want: ^2.8.5
65 silly placeDep ROOT dotenv@16.6.1 OK for: weather-backend@1.0.0 want: ^16.3.1
66 silly placeDep ROOT express@4.21.2 OK for: weather-backend@1.0.0 want: ^4.18.2
67 silly placeDep ROOT express-rate-limit@7.5.1 OK for: weather-backend@1.0.0 want: ^7.1.5
68 silly placeDep ROOT helmet@7.2.0 OK for: weather-backend@1.0.0 want: ^7.1.0
69 silly placeDep ROOT jest@29.7.0 OK for: weather-backend@1.0.0 want: ^29.7.0
70 silly placeDep ROOT morgan@1.10.1 OK for: weather-backend@1.0.0 want: ^1.10.0
71 silly placeDep ROOT node-cache@5.1.2 OK for: weather-backend@1.0.0 want: ^5.1.2
72 silly placeDep ROOT nodemon@3.1.10 OK for: weather-backend@1.0.0 want: ^3.0.2
73 silly placeDep ROOT supertest@6.3.4 OK for: weather-backend@1.0.0 want: ^6.3.3
74 silly fetch manifest follow-redirects@^1.15.6
75 silly packumentCache full:https://registry.npmjs.org/follow-redirects cache-miss
76 silly fetch manifest form-data@^4.0.4
77 silly packumentCache full:https://registry.npmjs.org/form-data cache-miss
78 silly fetch manifest proxy-from-env@^1.1.0
79 silly packumentCache full:https://registry.npmjs.org/proxy-from-env cache-miss
80 silly fetch manifest vary@^1
81 silly packumentCache full:https://registry.npmjs.org/vary cache-miss
82 silly fetch manifest object-assign@^4
83 silly packumentCache full:https://registry.npmjs.org/object-assign cache-miss
84 silly fetch manifest qs@6.13.0
85 silly packumentCache full:https://registry.npmjs.org/qs cache-miss
86 silly fetch manifest depd@2.0.0
87 silly packumentCache full:https://registry.npmjs.org/depd cache-miss
88 silly fetch manifest etag@~1.8.1
89 silly packumentCache full:https://registry.npmjs.org/etag cache-miss
90 silly fetch manifest send@0.19.0
91 silly packumentCache full:https://registry.npmjs.org/send cache-miss
92 silly fetch manifest vary@~1.1.2
93 silly packumentCache full:https://registry.npmjs.org/vary cache-miss
94 silly fetch manifest debug@2.6.9
95 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
96 silly fetch manifest fresh@0.5.2
97 silly packumentCache full:https://registry.npmjs.org/fresh cache-miss
98 silly fetch manifest cookie@0.7.1
99 silly packumentCache full:https://registry.npmjs.org/cookie cache-miss
100 silly fetch manifest accepts@~1.3.8
101 silly packumentCache full:https://registry.npmjs.org/accepts cache-miss
102 silly fetch manifest methods@~1.1.2
103 silly packumentCache full:https://registry.npmjs.org/methods cache-miss
104 http fetch GET 200 https://registry.npmjs.org/form-data 414ms (cache miss)
105 silly packumentCache full:https://registry.npmjs.org/form-data set size:undefined disposed:false
106 silly fetch manifest type-is@~1.6.18
107 silly packumentCache full:https://registry.npmjs.org/type-is cache-miss
108 http fetch GET 200 https://registry.npmjs.org/follow-redirects 641ms (cache miss)
109 silly packumentCache full:https://registry.npmjs.org/follow-redirects set size:undefined disposed:false
110 silly fetch manifest parseurl@~1.3.3
111 silly packumentCache full:https://registry.npmjs.org/parseurl cache-miss
112 http fetch GET 200 https://registry.npmjs.org/parseurl 393ms (cache miss)
113 silly packumentCache full:https://registry.npmjs.org/parseurl set size:undefined disposed:false
114 silly fetch manifest statuses@2.0.1
115 silly packumentCache full:https://registry.npmjs.org/statuses cache-miss
116 http fetch GET 200 https://registry.npmjs.org/vary 1323ms (cache miss)
117 silly packumentCache full:https://registry.npmjs.org/vary set size:undefined disposed:false
118 silly fetch manifest encodeurl@~2.0.0
119 silly packumentCache full:https://registry.npmjs.org/encodeurl cache-miss
120 http fetch GET 200 https://registry.npmjs.org/statuses 371ms (cache miss)
121 silly packumentCache full:https://registry.npmjs.org/statuses set size:undefined disposed:false
122 silly fetch manifest proxy-addr@~2.0.7
123 silly packumentCache full:https://registry.npmjs.org/proxy-addr cache-miss
124 http fetch GET 200 https://registry.npmjs.org/vary 1428ms (cache miss)
125 silly packumentCache full:https://registry.npmjs.org/vary set size:undefined disposed:false
126 silly fetch manifest body-parser@1.20.3
127 silly packumentCache full:https://registry.npmjs.org/body-parser cache-miss
128 http fetch GET 200 https://registry.npmjs.org/fresh 1526ms (cache miss)
129 silly packumentCache full:https://registry.npmjs.org/fresh set size:undefined disposed:false
130 silly fetch manifest escape-html@~1.0.3
131 silly packumentCache full:https://registry.npmjs.org/escape-html cache-miss
132 http fetch GET 200 https://registry.npmjs.org/type-is 1148ms (cache miss)
133 silly packumentCache full:https://registry.npmjs.org/type-is set size:undefined disposed:false
134 silly fetch manifest http-errors@2.0.0
135 silly packumentCache full:https://registry.npmjs.org/http-errors cache-miss
136 http fetch GET 200 https://registry.npmjs.org/encodeurl 344ms (cache miss)
137 silly packumentCache full:https://registry.npmjs.org/encodeurl set size:undefined disposed:false
138 silly fetch manifest on-finished@2.4.1
139 silly packumentCache full:https://registry.npmjs.org/on-finished cache-miss
140 http fetch GET 200 https://registry.npmjs.org/depd 1692ms (cache miss)
141 silly packumentCache full:https://registry.npmjs.org/depd set size:undefined disposed:false
142 silly fetch manifest safe-buffer@5.2.1
143 silly packumentCache full:https://registry.npmjs.org/safe-buffer cache-miss
144 http fetch GET 200 https://registry.npmjs.org/debug 1710ms (cache miss)
145 silly packumentCache full:https://registry.npmjs.org/debug set size:undefined disposed:false
146 silly fetch manifest utils-merge@1.0.1
147 silly packumentCache full:https://registry.npmjs.org/utils-merge cache-miss
148 http fetch GET 200 https://registry.npmjs.org/utils-merge 186ms (cache miss)
149 silly packumentCache full:https://registry.npmjs.org/utils-merge set size:undefined disposed:false
150 silly fetch manifest content-type@~1.0.4
151 silly packumentCache full:https://registry.npmjs.org/content-type cache-miss
152 http fetch GET 200 https://registry.npmjs.org/body-parser 503ms (cache miss)
153 silly packumentCache full:https://registry.npmjs.org/body-parser set size:undefined disposed:false
154 silly fetch manifest finalhandler@1.3.1
155 silly packumentCache full:https://registry.npmjs.org/finalhandler cache-miss
156 http fetch GET 200 https://registry.npmjs.org/http-errors 419ms (cache miss)
157 silly packumentCache full:https://registry.npmjs.org/http-errors set size:undefined disposed:false
158 silly fetch manifest range-parser@~1.2.1
159 silly packumentCache full:https://registry.npmjs.org/range-parser cache-miss
160 http fetch GET 200 https://registry.npmjs.org/send 2071ms (cache miss)
161 silly packumentCache full:https://registry.npmjs.org/send set size:undefined disposed:false
162 silly fetch manifest serve-static@1.16.2
163 silly packumentCache full:https://registry.npmjs.org/serve-static cache-miss
164 http fetch GET 200 https://registry.npmjs.org/safe-buffer 541ms (cache miss)
165 silly packumentCache full:https://registry.npmjs.org/safe-buffer set size:undefined disposed:false
166 silly fetch manifest array-flatten@1.1.1
167 silly packumentCache full:https://registry.npmjs.org/array-flatten cache-miss
168 http fetch GET 200 https://registry.npmjs.org/escape-html 724ms (cache miss)
169 silly packumentCache full:https://registry.npmjs.org/escape-html set size:undefined disposed:false
170 silly fetch manifest path-to-regexp@0.1.12
171 silly packumentCache full:https://registry.npmjs.org/path-to-regexp cache-miss
172 http fetch GET 200 https://registry.npmjs.org/proxy-from-env 2309ms (cache miss)
173 silly packumentCache full:https://registry.npmjs.org/proxy-from-env set size:undefined disposed:false
174 silly fetch manifest setprototypeof@1.2.0
175 silly packumentCache full:https://registry.npmjs.org/setprototypeof cache-miss
176 http fetch GET 200 https://registry.npmjs.org/finalhandler 460ms (cache miss)
177 silly packumentCache full:https://registry.npmjs.org/finalhandler set size:undefined disposed:false
178 silly fetch manifest cookie-signature@1.0.6
179 silly packumentCache full:https://registry.npmjs.org/cookie-signature cache-miss
180 http fetch GET 200 https://registry.npmjs.org/on-finished 777ms (cache miss)
181 silly packumentCache full:https://registry.npmjs.org/on-finished set size:undefined disposed:false
182 silly fetch manifest merge-descriptors@1.0.3
183 silly packumentCache full:https://registry.npmjs.org/merge-descriptors cache-miss
184 http fetch GET 200 https://registry.npmjs.org/object-assign 2454ms (cache miss)
185 silly packumentCache full:https://registry.npmjs.org/object-assign set size:undefined disposed:false
186 silly fetch manifest content-disposition@0.5.4
187 silly packumentCache full:https://registry.npmjs.org/content-disposition cache-miss
188 http fetch GET 200 https://registry.npmjs.org/range-parser 478ms (cache miss)
189 silly packumentCache full:https://registry.npmjs.org/range-parser set size:undefined disposed:false
190 silly fetch manifest jest-cli@^29.7.0
191 silly packumentCache full:https://registry.npmjs.org/jest-cli cache-miss
192 http fetch GET 200 https://registry.npmjs.org/methods 2523ms (cache miss)
193 silly packumentCache full:https://registry.npmjs.org/methods set size:undefined disposed:false
194 silly fetch manifest @jest/core@^29.7.0
195 silly packumentCache full:https://registry.npmjs.org/@jest%2fcore cache-miss
196 http fetch GET 200 https://registry.npmjs.org/qs 2606ms (cache miss)
197 silly packumentCache full:https://registry.npmjs.org/qs set size:undefined disposed:false
198 silly fetch manifest @jest/types@^29.6.3
199 silly packumentCache full:https://registry.npmjs.org/@jest%2ftypes cache-miss
200 http fetch GET 200 https://registry.npmjs.org/array-flatten 376ms (cache miss)
201 silly packumentCache full:https://registry.npmjs.org/array-flatten set size:undefined disposed:false
202 silly fetch manifest import-local@^3.0.2
203 silly packumentCache full:https://registry.npmjs.org/import-local cache-miss
204 http fetch GET 200 https://registry.npmjs.org/accepts 2626ms (cache miss)
205 silly packumentCache full:https://registry.npmjs.org/accepts set size:undefined disposed:false
206 silly fetch manifest basic-auth@~2.0.1
207 silly packumentCache full:https://registry.npmjs.org/basic-auth cache-miss
208 http fetch GET 200 https://registry.npmjs.org/content-disposition 227ms (cache miss)
209 silly packumentCache full:https://registry.npmjs.org/content-disposition set size:undefined disposed:false
210 silly fetch manifest depd@~2.0.0
211 silly packumentCache full:https://registry.npmjs.org/depd cache-miss
212 http cache https://registry.npmjs.org/depd 33ms (cache hit)
213 silly packumentCache full:https://registry.npmjs.org/depd set size:40182 disposed:false
214 silly fetch manifest on-finished@~2.3.0
215 silly packumentCache full:https://registry.npmjs.org/on-finished cache-miss
216 http cache https://registry.npmjs.org/on-finished 6ms (cache hit)
217 silly packumentCache full:https://registry.npmjs.org/on-finished set size:23212 disposed:false
218 silly fetch manifest on-headers@~1.1.0
219 silly packumentCache full:https://registry.npmjs.org/on-headers cache-miss
220 http fetch GET 200 https://registry.npmjs.org/path-to-regexp 498ms (cache miss)
221 silly packumentCache full:https://registry.npmjs.org/path-to-regexp set size:undefined disposed:false
222 silly fetch manifest clone@2.x
223 silly packumentCache full:https://registry.npmjs.org/clone cache-miss
224 http fetch GET 200 https://registry.npmjs.org/cookie-signature 444ms (cache miss)
225 silly packumentCache full:https://registry.npmjs.org/cookie-signature set size:undefined disposed:false
226 silly fetch manifest debug@^4
227 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
228 http cache https://registry.npmjs.org/debug 5ms (cache hit)
229 silly packumentCache full:https://registry.npmjs.org/debug set size:193521 disposed:false
230 silly fetch manifest touch@^3.1.0
231 silly packumentCache full:https://registry.npmjs.org/touch cache-miss
232 http fetch GET 200 https://registry.npmjs.org/import-local 400ms (cache miss)
233 silly packumentCache full:https://registry.npmjs.org/import-local set size:undefined disposed:false
234 silly fetch manifest semver@^7.5.3
235 silly packumentCache full:https://registry.npmjs.org/semver cache-miss
236 http fetch GET 200 https://registry.npmjs.org/clone 345ms (cache miss)
237 silly packumentCache full:https://registry.npmjs.org/clone set size:undefined disposed:false
238 silly fetch manifest chokidar@^3.5.2
239 silly packumentCache full:https://registry.npmjs.org/chokidar cache-miss
240 http fetch GET 200 https://registry.npmjs.org/@jest%2fcore 720ms (cache miss)
241 silly packumentCache full:https://registry.npmjs.org/@jest%2fcore set size:undefined disposed:false
242 silly fetch manifest minimatch@^3.1.2
243 silly packumentCache full:https://registry.npmjs.org/minimatch cache-miss
244 http fetch GET 200 https://registry.npmjs.org/serve-static 1244ms (cache miss)
245 silly packumentCache full:https://registry.npmjs.org/serve-static set size:undefined disposed:false
246 silly fetch manifest undefsafe@^2.0.5
247 silly packumentCache full:https://registry.npmjs.org/undefsafe cache-miss
248 http fetch GET 200 https://registry.npmjs.org/touch 484ms (cache miss)
249 silly packumentCache full:https://registry.npmjs.org/touch set size:undefined disposed:false
250 silly fetch manifest pstree.remy@^1.1.8
251 silly packumentCache full:https://registry.npmjs.org/pstree.remy cache-miss
252 http fetch GET 200 https://registry.npmjs.org/merge-descriptors 911ms (cache miss)
253 silly packumentCache full:https://registry.npmjs.org/merge-descriptors set size:undefined disposed:false
254 silly fetch manifest supports-color@^5.5.0
255 silly packumentCache full:https://registry.npmjs.org/supports-color cache-miss
256 http fetch GET 200 https://registry.npmjs.org/basic-auth 740ms (cache miss)
257 silly packumentCache full:https://registry.npmjs.org/basic-auth set size:undefined disposed:false
258 silly fetch manifest ignore-by-default@^1.0.1
259 silly packumentCache full:https://registry.npmjs.org/ignore-by-default cache-miss
260 http fetch GET 200 https://registry.npmjs.org/on-headers 651ms (cache miss)
261 silly packumentCache full:https://registry.npmjs.org/on-headers set size:undefined disposed:false
262 silly fetch manifest simple-update-notifier@^2.0.0
263 silly packumentCache full:https://registry.npmjs.org/simple-update-notifier cache-miss
264 http fetch GET 200 https://registry.npmjs.org/content-type 1851ms (cache miss)
265 silly packumentCache full:https://registry.npmjs.org/content-type set size:undefined disposed:false
266 silly fetch manifest methods@^1.1.2
267 silly packumentCache full:https://registry.npmjs.org/methods cache-miss
268 http cache https://registry.npmjs.org/methods 4ms (cache hit)
269 silly packumentCache full:https://registry.npmjs.org/methods set size:12600 disposed:false
270 silly fetch manifest superagent@^8.1.2
271 silly packumentCache full:https://registry.npmjs.org/superagent cache-miss
272 http fetch GET 200 https://registry.npmjs.org/ignore-by-default 505ms (cache miss)
273 silly packumentCache full:https://registry.npmjs.org/ignore-by-default set size:undefined disposed:false
274 http fetch GET 200 https://registry.npmjs.org/chokidar 932ms (cache miss)
275 silly packumentCache full:https://registry.npmjs.org/chokidar set size:undefined disposed:false
276 http fetch GET 200 https://registry.npmjs.org/pstree.remy 706ms (cache miss)
277 silly packumentCache full:https://registry.npmjs.org/pstree.remy set size:undefined disposed:false
278 http fetch GET 200 https://registry.npmjs.org/undefsafe 761ms (cache miss)
279 silly packumentCache full:https://registry.npmjs.org/undefsafe set size:undefined disposed:false
280 http fetch GET 200 https://registry.npmjs.org/supports-color 871ms (cache miss)
281 silly packumentCache full:https://registry.npmjs.org/supports-color set size:undefined disposed:false
282 http fetch GET 200 https://registry.npmjs.org/cookie 4327ms (cache miss)
283 silly packumentCache full:https://registry.npmjs.org/cookie set size:undefined disposed:false
284 http fetch GET 200 https://registry.npmjs.org/minimatch 1087ms (cache miss)
285 silly packumentCache full:https://registry.npmjs.org/minimatch set size:undefined disposed:false
286 http fetch GET 200 https://registry.npmjs.org/proxy-addr 3070ms (cache miss)
287 silly packumentCache full:https://registry.npmjs.org/proxy-addr set size:undefined disposed:false
288 http fetch GET 200 https://registry.npmjs.org/simple-update-notifier 1234ms (cache miss)
289 silly packumentCache full:https://registry.npmjs.org/simple-update-notifier set size:undefined disposed:false
290 http fetch GET 200 https://registry.npmjs.org/semver 1675ms (cache miss)
291 silly packumentCache full:https://registry.npmjs.org/semver set size:undefined disposed:false
292 http fetch GET 200 https://registry.npmjs.org/superagent 1343ms (cache miss)
293 silly packumentCache full:https://registry.npmjs.org/superagent set size:undefined disposed:false
294 http fetch GET 200 https://registry.npmjs.org/@jest%2ftypes 2549ms (cache miss)
295 silly packumentCache full:https://registry.npmjs.org/@jest%2ftypes set size:undefined disposed:false
296 http fetch GET 200 https://registry.npmjs.org/setprototypeof 3751ms (cache miss)
297 silly packumentCache full:https://registry.npmjs.org/setprototypeof set size:undefined disposed:false
298 http fetch GET 200 https://registry.npmjs.org/etag 7259ms (cache miss)
299 silly packumentCache full:https://registry.npmjs.org/etag set size:undefined disposed:false
300 http fetch GET 200 https://registry.npmjs.org/jest-cli 5648ms (cache miss)
301 silly packumentCache full:https://registry.npmjs.org/jest-cli set size:undefined disposed:false
302 silly placeDep ROOT follow-redirects@1.15.11 OK for: axios@1.11.0 want: ^1.15.6
303 silly placeDep ROOT form-data@4.0.4 OK for: axios@1.11.0 want: ^4.0.4
304 silly placeDep ROOT proxy-from-env@1.1.0 OK for: axios@1.11.0 want: ^1.1.0
305 silly fetch manifest hasown@^2.0.2
306 silly packumentCache full:https://registry.npmjs.org/hasown cache-miss
307 silly fetch manifest asynckit@^0.4.0
308 silly packumentCache full:https://registry.npmjs.org/asynckit cache-miss
309 silly fetch manifest mime-types@^2.1.12
310 silly packumentCache full:https://registry.npmjs.org/mime-types cache-miss
311 silly fetch manifest combined-stream@^1.0.8
312 silly packumentCache full:https://registry.npmjs.org/combined-stream cache-miss
313 silly fetch manifest es-set-tostringtag@^2.1.0
314 silly packumentCache full:https://registry.npmjs.org/es-set-tostringtag cache-miss
315 http fetch GET 200 https://registry.npmjs.org/hasown 257ms (cache miss)
316 silly packumentCache full:https://registry.npmjs.org/hasown set size:undefined disposed:false
317 http fetch GET 200 https://registry.npmjs.org/es-set-tostringtag 448ms (cache miss)
318 silly packumentCache full:https://registry.npmjs.org/es-set-tostringtag set size:undefined disposed:false
319 http fetch GET 200 https://registry.npmjs.org/combined-stream 631ms (cache miss)
320 silly packumentCache full:https://registry.npmjs.org/combined-stream set size:undefined disposed:false
321 http fetch GET 200 https://registry.npmjs.org/asynckit 795ms (cache miss)
322 silly packumentCache full:https://registry.npmjs.org/asynckit set size:undefined disposed:false
323 http fetch GET 200 https://registry.npmjs.org/mime-types 1291ms (cache miss)
324 silly packumentCache full:https://registry.npmjs.org/mime-types set size:undefined disposed:false
325 silly placeDep ROOT object-assign@4.1.1 OK for: cors@2.8.5 want: ^4
326 silly placeDep ROOT vary@1.1.2 OK for: cors@2.8.5 want: ^1
327 silly placeDep ROOT accepts@1.3.8 OK for: express@4.21.2 want: ~1.3.8
328 silly placeDep ROOT array-flatten@1.1.1 OK for: express@4.21.2 want: 1.1.1
329 silly placeDep ROOT body-parser@1.20.3 OK for: express@4.21.2 want: 1.20.3
330 silly placeDep ROOT content-disposition@0.5.4 OK for: express@4.21.2 want: 0.5.4
331 silly placeDep ROOT content-type@1.0.5 OK for: express@4.21.2 want: ~1.0.4
332 silly placeDep ROOT cookie@0.7.1 OK for: express@4.21.2 want: 0.7.1
333 silly placeDep ROOT cookie-signature@1.0.6 OK for: express@4.21.2 want: 1.0.6
334 silly placeDep ROOT debug@2.6.9 OK for: express@4.21.2 want: 2.6.9
335 silly placeDep ROOT depd@2.0.0 OK for: express@4.21.2 want: 2.0.0
336 silly placeDep ROOT encodeurl@2.0.0 OK for: express@4.21.2 want: ~2.0.0
337 silly placeDep ROOT escape-html@1.0.3 OK for: express@4.21.2 want: ~1.0.3
338 silly placeDep ROOT etag@1.8.1 OK for: express@4.21.2 want: ~1.8.1
339 silly placeDep ROOT finalhandler@1.3.1 OK for: express@4.21.2 want: 1.3.1
340 silly placeDep ROOT fresh@0.5.2 OK for: express@4.21.2 want: 0.5.2
341 silly placeDep ROOT http-errors@2.0.0 OK for: express@4.21.2 want: 2.0.0
342 silly placeDep ROOT merge-descriptors@1.0.3 OK for: express@4.21.2 want: 1.0.3
343 silly placeDep ROOT methods@1.1.2 OK for: express@4.21.2 want: ~1.1.2
344 silly placeDep ROOT on-finished@2.4.1 OK for: express@4.21.2 want: 2.4.1
345 silly placeDep ROOT parseurl@1.3.3 OK for: express@4.21.2 want: ~1.3.3
346 silly placeDep ROOT path-to-regexp@0.1.12 OK for: express@4.21.2 want: 0.1.12
347 silly placeDep ROOT proxy-addr@2.0.7 OK for: express@4.21.2 want: ~2.0.7
348 silly placeDep ROOT qs@6.13.0 OK for: express@4.21.2 want: 6.13.0
349 silly placeDep ROOT range-parser@1.2.1 OK for: express@4.21.2 want: ~1.2.1
350 silly placeDep ROOT safe-buffer@5.2.1 OK for: express@4.21.2 want: 5.2.1
351 silly placeDep ROOT send@0.19.0 OK for: express@4.21.2 want: 0.19.0
352 silly placeDep ROOT serve-static@1.16.2 OK for: express@4.21.2 want: 1.16.2
353 silly placeDep ROOT setprototypeof@1.2.0 OK for: express@4.21.2 want: 1.2.0
354 silly placeDep ROOT statuses@2.0.1 OK for: express@4.21.2 want: 2.0.1
355 silly placeDep ROOT type-is@1.6.18 OK for: express@4.21.2 want: ~1.6.18
356 silly placeDep ROOT utils-merge@1.0.1 OK for: express@4.21.2 want: 1.0.1
357 silly fetch manifest mime-types@~2.1.34
358 silly packumentCache full:https://registry.npmjs.org/mime-types cache-miss
359 silly fetch manifest negotiator@0.6.3
360 silly packumentCache full:https://registry.npmjs.org/negotiator cache-miss
361 silly fetch manifest bytes@3.1.2
362 silly packumentCache full:https://registry.npmjs.org/bytes cache-miss
363 silly fetch manifest unpipe@1.0.0
364 silly packumentCache full:https://registry.npmjs.org/unpipe cache-miss
365 silly fetch manifest destroy@1.2.0
366 silly packumentCache full:https://registry.npmjs.org/destroy cache-miss
367 silly fetch manifest raw-body@2.5.2
368 silly packumentCache full:https://registry.npmjs.org/raw-body cache-miss
369 silly fetch manifest iconv-lite@0.4.24
370 silly packumentCache full:https://registry.npmjs.org/iconv-lite cache-miss
371 silly fetch manifest content-type@~1.0.5
372 silly packumentCache full:https://registry.npmjs.org/content-type cache-miss
373 silly fetch manifest ms@2.0.0
374 silly packumentCache full:https://registry.npmjs.org/ms cache-miss
375 silly fetch manifest unpipe@~1.0.0
376 silly packumentCache full:https://registry.npmjs.org/unpipe cache-miss
377 silly fetch manifest inherits@2.0.4
378 silly packumentCache full:https://registry.npmjs.org/inherits cache-miss
379 silly fetch manifest toidentifier@1.0.1
380 silly packumentCache full:https://registry.npmjs.org/toidentifier cache-miss
381 silly fetch manifest ee-first@1.1.1
382 silly packumentCache full:https://registry.npmjs.org/ee-first cache-miss
383 silly fetch manifest forwarded@0.2.0
384 silly packumentCache full:https://registry.npmjs.org/forwarded cache-miss
385 silly fetch manifest ipaddr.js@1.9.1
386 silly packumentCache full:https://registry.npmjs.org/ipaddr.js cache-miss
387 http cache https://registry.npmjs.org/content-type 18ms (cache hit)
388 silly packumentCache full:https://registry.npmjs.org/content-type set size:17311 disposed:false
389 silly fetch manifest side-channel@^1.0.6
390 silly packumentCache full:https://registry.npmjs.org/side-channel cache-miss
391 http cache https://registry.npmjs.org/mime-types 23ms (cache hit)
392 silly packumentCache full:https://registry.npmjs.org/mime-types set size:136716 disposed:false
393 silly fetch manifest ms@2.1.3
394 silly packumentCache full:https://registry.npmjs.org/ms cache-miss
395 http fetch GET 200 https://registry.npmjs.org/inherits 180ms (cache miss)
396 silly packumentCache full:https://registry.npmjs.org/inherits set size:undefined disposed:false
397 silly fetch manifest mime@1.6.0
398 silly packumentCache full:https://registry.npmjs.org/mime cache-miss
399 http fetch GET 200 https://registry.npmjs.org/ee-first 186ms (cache miss)
400 silly packumentCache full:https://registry.npmjs.org/ee-first set size:undefined disposed:false
401 silly fetch manifest destroy@1.2.0
402 silly packumentCache full:https://registry.npmjs.org/destroy cache-miss
403 http fetch GET 200 https://registry.npmjs.org/forwarded 190ms (cache miss)
404 silly packumentCache full:https://registry.npmjs.org/forwarded set size:undefined disposed:false
405 silly fetch manifest encodeurl@~1.0.2
406 silly packumentCache full:https://registry.npmjs.org/encodeurl cache-miss
407 http cache https://registry.npmjs.org/encodeurl 4ms (cache hit)
408 silly packumentCache full:https://registry.npmjs.org/encodeurl set size:12296 disposed:false
409 silly fetch manifest mime-types@~2.1.24
410 silly packumentCache full:https://registry.npmjs.org/mime-types cache-hit
411 silly fetch manifest media-typer@0.3.0
412 silly packumentCache full:https://registry.npmjs.org/media-typer cache-miss
413 http fetch GET 200 https://registry.npmjs.org/destroy 294ms (cache miss)
414 silly packumentCache full:https://registry.npmjs.org/destroy set size:undefined disposed:false
415 http fetch GET 200 https://registry.npmjs.org/iconv-lite 338ms (cache miss)
416 silly packumentCache full:https://registry.npmjs.org/iconv-lite set size:undefined disposed:false
417 http fetch GET 200 https://registry.npmjs.org/side-channel 336ms (cache miss)
418 silly packumentCache full:https://registry.npmjs.org/side-channel set size:undefined disposed:false
419 http fetch GET 200 https://registry.npmjs.org/bytes 500ms (cache miss)
420 silly packumentCache full:https://registry.npmjs.org/bytes set size:undefined disposed:false
421 http fetch GET 200 https://registry.npmjs.org/unpipe 503ms (cache miss)
422 silly packumentCache full:https://registry.npmjs.org/unpipe set size:undefined disposed:false
423 http fetch GET 200 https://registry.npmjs.org/media-typer 322ms (cache miss)
424 silly packumentCache full:https://registry.npmjs.org/media-typer set size:undefined disposed:false
425 http fetch GET 200 https://registry.npmjs.org/mime 352ms (cache miss)
426 silly packumentCache full:https://registry.npmjs.org/mime set size:undefined disposed:false
427 http fetch GET 200 https://registry.npmjs.org/destroy 383ms (cache miss)
428 silly packumentCache full:https://registry.npmjs.org/destroy set size:undefined disposed:false
429 http fetch GET 200 https://registry.npmjs.org/raw-body 577ms (cache miss)
430 silly packumentCache full:https://registry.npmjs.org/raw-body set size:undefined disposed:false
431 http fetch GET 200 https://registry.npmjs.org/ipaddr.js 618ms (cache miss)
432 silly packumentCache full:https://registry.npmjs.org/ipaddr.js set size:undefined disposed:false
433 http fetch GET 200 https://registry.npmjs.org/ms 714ms (cache miss)
434 silly packumentCache full:https://registry.npmjs.org/ms set size:undefined disposed:false
435 http fetch GET 200 https://registry.npmjs.org/toidentifier 852ms (cache miss)
436 silly packumentCache full:https://registry.npmjs.org/toidentifier set size:undefined disposed:false
437 http fetch GET 200 https://registry.npmjs.org/negotiator 1039ms (cache miss)
438 silly packumentCache full:https://registry.npmjs.org/negotiator set size:undefined disposed:false
439 http fetch GET 200 https://registry.npmjs.org/ms 1160ms (cache miss)
440 silly packumentCache full:https://registry.npmjs.org/ms set size:undefined disposed:false
441 http fetch GET 200 https://registry.npmjs.org/unpipe 1844ms (cache miss)
442 silly packumentCache full:https://registry.npmjs.org/unpipe set size:undefined disposed:false
443 silly placeDep ROOT mime-types@2.1.35 OK for: accepts@1.3.8 want: ~2.1.34
444 silly placeDep ROOT negotiator@0.6.3 OK for: accepts@1.3.8 want: 0.6.3
445 silly fetch manifest mime-db@1.52.0
446 silly packumentCache full:https://registry.npmjs.org/mime-db cache-miss
447 http fetch GET 200 https://registry.npmjs.org/mime-db 988ms (cache miss)
448 silly packumentCache full:https://registry.npmjs.org/mime-db set size:undefined disposed:false
449 silly placeDep ROOT bytes@3.1.2 OK for: body-parser@1.20.3 want: 3.1.2
450 silly placeDep ROOT destroy@1.2.0 OK for: body-parser@1.20.3 want: 1.2.0
451 silly placeDep ROOT iconv-lite@0.4.24 OK for: body-parser@1.20.3 want: 0.4.24
452 silly placeDep ROOT raw-body@2.5.2 OK for: body-parser@1.20.3 want: 2.5.2
453 silly placeDep ROOT unpipe@1.0.0 OK for: body-parser@1.20.3 want: 1.0.0
454 silly fetch manifest safer-buffer@>= 2.1.2 < 3
455 silly packumentCache full:https://registry.npmjs.org/safer-buffer cache-miss
456 http fetch GET 200 https://registry.npmjs.org/safer-buffer 480ms (cache miss)
457 silly packumentCache full:https://registry.npmjs.org/safer-buffer set size:undefined disposed:false
458 silly placeDep ROOT ms@2.0.0 OK for: debug@2.6.9 want: 2.0.0
459 silly placeDep ROOT asynckit@0.4.0 OK for: form-data@4.0.4 want: ^0.4.0
460 silly placeDep ROOT combined-stream@1.0.8 OK for: form-data@4.0.4 want: ^1.0.8
461 silly placeDep ROOT es-set-tostringtag@2.1.0 OK for: form-data@4.0.4 want: ^2.1.0
462 silly placeDep ROOT hasown@2.0.2 OK for: form-data@4.0.4 want: ^2.0.2
463 silly fetch manifest delayed-stream@~1.0.0
464 silly packumentCache full:https://registry.npmjs.org/delayed-stream cache-miss
465 silly fetch manifest es-errors@^1.3.0
466 silly packumentCache full:https://registry.npmjs.org/es-errors cache-miss
467 silly fetch manifest get-intrinsic@^1.2.6
468 silly packumentCache full:https://registry.npmjs.org/get-intrinsic cache-miss
469 silly fetch manifest has-tostringtag@^1.0.2
470 silly packumentCache full:https://registry.npmjs.org/has-tostringtag cache-miss
471 silly fetch manifest function-bind@^1.1.2
472 silly packumentCache full:https://registry.npmjs.org/function-bind cache-miss
473 http fetch GET 200 https://registry.npmjs.org/function-bind 185ms (cache miss)
474 silly packumentCache full:https://registry.npmjs.org/function-bind set size:undefined disposed:false
475 http fetch GET 200 https://registry.npmjs.org/get-intrinsic 251ms (cache miss)
476 silly packumentCache full:https://registry.npmjs.org/get-intrinsic set size:undefined disposed:false
477 http fetch GET 200 https://registry.npmjs.org/es-errors 351ms (cache miss)
478 silly packumentCache full:https://registry.npmjs.org/es-errors set size:undefined disposed:false
479 http fetch GET 200 https://registry.npmjs.org/has-tostringtag 760ms (cache miss)
480 silly packumentCache full:https://registry.npmjs.org/has-tostringtag set size:undefined disposed:false
481 http fetch GET 200 https://registry.npmjs.org/delayed-stream 1598ms (cache miss)
482 silly packumentCache full:https://registry.npmjs.org/delayed-stream set size:undefined disposed:false
483 silly placeDep ROOT delayed-stream@1.0.0 OK for: combined-stream@1.0.8 want: ~1.0.0
484 silly placeDep ROOT es-errors@1.3.0 OK for: es-set-tostringtag@2.1.0 want: ^1.3.0
485 silly placeDep ROOT get-intrinsic@1.3.0 OK for: es-set-tostringtag@2.1.0 want: ^1.2.6
486 silly placeDep ROOT has-tostringtag@1.0.2 OK for: es-set-tostringtag@2.1.0 want: ^1.0.2
487 silly fetch manifest call-bind-apply-helpers@^1.0.2
488 silly packumentCache full:https://registry.npmjs.org/call-bind-apply-helpers cache-miss
489 silly fetch manifest es-define-property@^1.0.1
490 silly packumentCache full:https://registry.npmjs.org/es-define-property cache-miss
491 silly fetch manifest es-object-atoms@^1.1.1
492 silly packumentCache full:https://registry.npmjs.org/es-object-atoms cache-miss
493 silly fetch manifest get-proto@^1.0.1
494 silly packumentCache full:https://registry.npmjs.org/get-proto cache-miss
495 silly fetch manifest gopd@^1.2.0
496 silly packumentCache full:https://registry.npmjs.org/gopd cache-miss
497 silly fetch manifest has-symbols@^1.1.0
498 silly packumentCache full:https://registry.npmjs.org/has-symbols cache-miss
499 silly fetch manifest math-intrinsics@^1.1.0
500 silly packumentCache full:https://registry.npmjs.org/math-intrinsics cache-miss
501 silly fetch manifest has-symbols@^1.0.3
502 silly packumentCache full:https://registry.npmjs.org/has-symbols cache-miss
503 http fetch GET 200 https://registry.npmjs.org/es-define-property 204ms (cache miss)
504 silly packumentCache full:https://registry.npmjs.org/es-define-property set size:undefined disposed:false
505 http fetch GET 200 https://registry.npmjs.org/gopd 278ms (cache miss)
506 silly packumentCache full:https://registry.npmjs.org/gopd set size:undefined disposed:false
507 http fetch GET 200 https://registry.npmjs.org/get-proto 521ms (cache miss)
508 silly packumentCache full:https://registry.npmjs.org/get-proto set size:undefined disposed:false
509 http fetch GET 200 https://registry.npmjs.org/call-bind-apply-helpers 794ms (cache miss)
510 silly packumentCache full:https://registry.npmjs.org/call-bind-apply-helpers set size:undefined disposed:false
511 http fetch GET 200 https://registry.npmjs.org/has-symbols 962ms (cache miss)
512 silly packumentCache full:https://registry.npmjs.org/has-symbols set size:undefined disposed:false
513 http fetch GET 200 https://registry.npmjs.org/es-object-atoms 1095ms (cache miss)
514 silly packumentCache full:https://registry.npmjs.org/es-object-atoms set size:undefined disposed:false
515 http fetch GET 200 https://registry.npmjs.org/has-symbols 1152ms (cache miss)
516 silly packumentCache full:https://registry.npmjs.org/has-symbols set size:undefined disposed:false
517 http fetch GET 200 https://registry.npmjs.org/math-intrinsics 1169ms (cache miss)
518 silly packumentCache full:https://registry.npmjs.org/math-intrinsics set size:undefined disposed:false
519 silly placeDep ROOT call-bind-apply-helpers@1.0.2 OK for: get-intrinsic@1.3.0 want: ^1.0.2
520 silly placeDep ROOT es-define-property@1.0.1 OK for: get-intrinsic@1.3.0 want: ^1.0.1
521 silly placeDep ROOT es-object-atoms@1.1.1 OK for: get-intrinsic@1.3.0 want: ^1.1.1
522 silly placeDep ROOT function-bind@1.1.2 OK for: get-intrinsic@1.3.0 want: ^1.1.2
523 silly placeDep ROOT get-proto@1.0.1 OK for: get-intrinsic@1.3.0 want: ^1.0.1
524 silly placeDep ROOT gopd@1.2.0 OK for: get-intrinsic@1.3.0 want: ^1.2.0
525 silly placeDep ROOT has-symbols@1.1.0 OK for: get-intrinsic@1.3.0 want: ^1.1.0
526 silly placeDep ROOT math-intrinsics@1.1.0 OK for: get-intrinsic@1.3.0 want: ^1.1.0
527 silly fetch manifest dunder-proto@^1.0.1
528 silly packumentCache full:https://registry.npmjs.org/dunder-proto cache-miss
529 http fetch GET 200 https://registry.npmjs.org/dunder-proto 266ms (cache miss)
530 silly packumentCache full:https://registry.npmjs.org/dunder-proto set size:undefined disposed:false
531 silly placeDep ROOT dunder-proto@1.0.1 OK for: get-proto@1.0.1 want: ^1.0.1
532 silly placeDep ROOT inherits@2.0.4 OK for: http-errors@2.0.0 want: 2.0.4
533 silly placeDep ROOT toidentifier@1.0.1 OK for: http-errors@2.0.0 want: 1.0.1
534 silly placeDep ROOT safer-buffer@2.1.2 OK for: iconv-lite@0.4.24 want: >= 2.1.2 < 3
535 silly placeDep ROOT @jest/core@29.7.0 OK for: jest@29.7.0 want: ^29.7.0
536 silly placeDep ROOT @jest/types@29.6.3 OK for: jest@29.7.0 want: ^29.6.3
537 silly placeDep ROOT import-local@3.2.0 OK for: jest@29.7.0 want: ^3.0.2
538 silly placeDep ROOT jest-cli@29.7.0 OK for: jest@29.7.0 want: ^29.7.0
539 silly fetch manifest exit@^0.1.2
540 silly packumentCache full:https://registry.npmjs.org/exit cache-miss
541 silly fetch manifest chalk@^4.0.0
542 silly packumentCache full:https://registry.npmjs.org/chalk cache-miss
543 silly fetch manifest slash@^3.0.0
544 silly packumentCache full:https://registry.npmjs.org/slash cache-miss
545 silly fetch manifest ci-info@^3.2.0
546 silly packumentCache full:https://registry.npmjs.org/ci-info cache-miss
547 silly fetch manifest jest-util@^29.7.0
548 silly packumentCache full:https://registry.npmjs.org/jest-util cache-miss
549 silly fetch manifest micromatch@^4.0.4
550 silly packumentCache full:https://registry.npmjs.org/micromatch cache-miss
551 silly fetch manifest strip-ansi@^6.0.0
552 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-miss
553 silly fetch manifest @types/node@*
554 silly packumentCache full:https://registry.npmjs.org/@types%2fnode cache-miss
555 silly fetch manifest graceful-fs@^4.2.9
556 silly packumentCache full:https://registry.npmjs.org/graceful-fs cache-miss
557 silly fetch manifest jest-config@^29.7.0
558 silly packumentCache full:https://registry.npmjs.org/jest-config cache-miss
559 silly fetch manifest jest-runner@^29.7.0
560 silly packumentCache full:https://registry.npmjs.org/jest-runner cache-miss
561 silly fetch manifest ansi-escapes@^4.2.1
562 silly packumentCache full:https://registry.npmjs.org/ansi-escapes cache-miss
563 silly fetch manifest jest-resolve@^29.7.0
564 silly packumentCache full:https://registry.npmjs.org/jest-resolve cache-miss
565 silly fetch manifest jest-runtime@^29.7.0
566 silly packumentCache full:https://registry.npmjs.org/jest-runtime cache-miss
567 silly fetch manifest jest-watcher@^29.7.0
568 silly packumentCache full:https://registry.npmjs.org/jest-watcher cache-miss
569 http fetch GET 200 https://registry.npmjs.org/strip-ansi 186ms (cache miss)
570 silly packumentCache full:https://registry.npmjs.org/strip-ansi set size:undefined disposed:false
571 silly fetch manifest @jest/console@^29.7.0
572 silly packumentCache full:https://registry.npmjs.org/@jest%2fconsole cache-miss
573 http fetch GET 200 https://registry.npmjs.org/exit 216ms (cache miss)
574 silly packumentCache full:https://registry.npmjs.org/exit set size:undefined disposed:false
575 silly fetch manifest jest-snapshot@^29.7.0
576 silly packumentCache full:https://registry.npmjs.org/jest-snapshot cache-miss
577 http fetch GET 200 https://registry.npmjs.org/micromatch 367ms (cache miss)
578 silly packumentCache full:https://registry.npmjs.org/micromatch set size:undefined disposed:false
579 silly fetch manifest jest-validate@^29.7.0
580 silly packumentCache full:https://registry.npmjs.org/jest-validate cache-miss
581 http fetch GET 200 https://registry.npmjs.org/slash 387ms (cache miss)
582 silly packumentCache full:https://registry.npmjs.org/slash set size:undefined disposed:false
583 silly fetch manifest pretty-format@^29.7.0
584 silly packumentCache full:https://registry.npmjs.org/pretty-format cache-miss
585 http fetch GET 200 https://registry.npmjs.org/ci-info 807ms (cache miss)
586 silly packumentCache full:https://registry.npmjs.org/ci-info set size:undefined disposed:false
587 silly fetch manifest jest-haste-map@^29.7.0
588 silly packumentCache full:https://registry.npmjs.org/jest-haste-map cache-miss
589 http fetch GET 200 https://registry.npmjs.org/@jest%2fconsole 681ms (cache miss)
590 silly packumentCache full:https://registry.npmjs.org/@jest%2fconsole set size:undefined disposed:false
591 silly fetch manifest @jest/reporters@^29.7.0
592 silly packumentCache full:https://registry.npmjs.org/@jest%2freporters cache-miss
593 http fetch GET 200 https://registry.npmjs.org/graceful-fs 925ms (cache miss)
594 silly packumentCache full:https://registry.npmjs.org/graceful-fs set size:undefined disposed:false
595 silly fetch manifest @jest/transform@^29.7.0
596 silly packumentCache full:https://registry.npmjs.org/@jest%2ftransform cache-miss
597 http fetch GET 200 https://registry.npmjs.org/ansi-escapes 1070ms (cache miss)
598 silly packumentCache full:https://registry.npmjs.org/ansi-escapes set size:undefined disposed:false
599 silly fetch manifest jest-regex-util@^29.6.3
600 silly packumentCache full:https://registry.npmjs.org/jest-regex-util cache-miss
601 http fetch GET 200 https://registry.npmjs.org/jest-util 1296ms (cache miss)
602 silly packumentCache full:https://registry.npmjs.org/jest-util set size:undefined disposed:false
603 silly fetch manifest @jest/test-result@^29.7.0
604 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-result cache-miss
605 http fetch GET 200 https://registry.npmjs.org/jest-snapshot 1181ms (cache miss)
606 silly packumentCache full:https://registry.npmjs.org/jest-snapshot set size:undefined disposed:false
607 silly fetch manifest jest-message-util@^29.7.0
608 silly packumentCache full:https://registry.npmjs.org/jest-message-util cache-miss
609 http fetch GET 200 https://registry.npmjs.org/jest-validate 1133ms (cache miss)
610 silly packumentCache full:https://registry.npmjs.org/jest-validate set size:undefined disposed:false
611 silly fetch manifest jest-changed-files@^29.7.0
612 silly packumentCache full:https://registry.npmjs.org/jest-changed-files cache-miss
613 http fetch GET 200 https://registry.npmjs.org/chalk 1879ms (cache miss)
614 silly packumentCache full:https://registry.npmjs.org/chalk set size:undefined disposed:false
615 silly fetch manifest jest-resolve-dependencies@^29.7.0
616 silly packumentCache full:https://registry.npmjs.org/jest-resolve-dependencies cache-miss
617 http fetch GET 200 https://registry.npmjs.org/@jest%2freporters 1126ms (cache miss)
618 silly packumentCache full:https://registry.npmjs.org/@jest%2freporters set size:undefined disposed:false
619 silly fetch manifest @types/node@*
620 silly packumentCache full:https://registry.npmjs.org/@types%2fnode cache-miss
621 http fetch GET 200 https://registry.npmjs.org/jest-resolve 2102ms (cache miss)
622 silly packumentCache full:https://registry.npmjs.org/jest-resolve set size:undefined disposed:false
623 silly fetch manifest @types/yargs@^17.0.8
624 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs cache-miss
625 http fetch GET 200 https://registry.npmjs.org/jest-changed-files 640ms (cache miss)
626 silly packumentCache full:https://registry.npmjs.org/jest-changed-files set size:undefined disposed:false
627 silly fetch manifest @jest/schemas@^29.6.3
628 silly packumentCache full:https://registry.npmjs.org/@jest%2fschemas cache-miss
629 http fetch GET 200 https://registry.npmjs.org/jest-message-util 823ms (cache miss)
630 silly packumentCache full:https://registry.npmjs.org/jest-message-util set size:undefined disposed:false
631 silly fetch manifest @types/istanbul-reports@^3.0.0
632 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-reports cache-miss
633 http fetch GET 200 https://registry.npmjs.org/jest-watcher 2375ms (cache miss)
634 silly packumentCache full:https://registry.npmjs.org/jest-watcher set size:undefined disposed:false
635 silly fetch manifest @types/istanbul-lib-coverage@^2.0.0
636 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage cache-miss
637 http fetch GET 200 https://registry.npmjs.org/@jest%2ftest-result 1223ms (cache miss)
638 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-result set size:undefined disposed:false
639 silly fetch manifest pkg-dir@^4.2.0
640 silly packumentCache full:https://registry.npmjs.org/pkg-dir cache-miss
641 http fetch GET 200 https://registry.npmjs.org/jest-runtime 2565ms (cache miss)
642 silly packumentCache full:https://registry.npmjs.org/jest-runtime set size:undefined disposed:false
643 silly fetch manifest resolve-cwd@^3.0.0
644 silly packumentCache full:https://registry.npmjs.org/resolve-cwd cache-miss
645 http fetch GET 200 https://registry.npmjs.org/jest-resolve-dependencies 1095ms (cache miss)
646 silly packumentCache full:https://registry.npmjs.org/jest-resolve-dependencies set size:undefined disposed:false
647 silly fetch manifest yargs@^17.3.1
648 silly packumentCache full:https://registry.npmjs.org/yargs cache-miss
649 http fetch GET 200 https://registry.npmjs.org/jest-regex-util 1974ms (cache miss)
650 silly packumentCache full:https://registry.npmjs.org/jest-regex-util set size:undefined disposed:false
651 silly fetch manifest create-jest@^29.7.0
652 silly packumentCache full:https://registry.npmjs.org/create-jest cache-miss
653 http fetch GET 200 https://registry.npmjs.org/@types%2fistanbul-reports 891ms (cache miss)
654 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-reports set size:undefined disposed:false
655 silly fetch manifest jest-config@^29.7.0
656 silly packumentCache full:https://registry.npmjs.org/jest-config cache-miss
657 http fetch GET 200 https://registry.npmjs.org/pkg-dir 678ms (cache miss)
658 silly packumentCache full:https://registry.npmjs.org/pkg-dir set size:undefined disposed:false
659 http fetch GET 200 https://registry.npmjs.org/@types%2fistanbul-lib-coverage 1023ms (cache miss)
660 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage set size:undefined disposed:false
661 http fetch GET 200 https://registry.npmjs.org/create-jest 591ms (cache miss)
662 silly packumentCache full:https://registry.npmjs.org/create-jest set size:undefined disposed:false
663 http fetch GET 200 https://registry.npmjs.org/jest-config 4087ms (cache miss)
664 silly packumentCache full:https://registry.npmjs.org/jest-config set size:undefined disposed:false
665 http fetch GET 200 https://registry.npmjs.org/yargs 1147ms (cache miss)
666 silly packumentCache full:https://registry.npmjs.org/yargs set size:undefined disposed:false
667 http fetch GET 200 https://registry.npmjs.org/@jest%2fschemas 2134ms (cache miss)
668 silly packumentCache full:https://registry.npmjs.org/@jest%2fschemas set size:undefined disposed:false
669 http fetch GET 200 https://registry.npmjs.org/@jest%2ftransform 3428ms (cache miss)
670 silly packumentCache full:https://registry.npmjs.org/@jest%2ftransform set size:undefined disposed:false
671 http fetch GET 200 https://registry.npmjs.org/jest-config 1413ms (cache miss)
672 silly packumentCache full:https://registry.npmjs.org/jest-config set size:undefined disposed:false
673 http fetch GET 200 https://registry.npmjs.org/resolve-cwd 1993ms (cache miss)
674 silly packumentCache full:https://registry.npmjs.org/resolve-cwd set size:undefined disposed:false
675 http fetch GET 200 https://registry.npmjs.org/@types%2fyargs 2555ms (cache miss)
676 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs set size:undefined disposed:false
677 http fetch GET 200 https://registry.npmjs.org/jest-haste-map 3984ms (cache miss)
678 silly packumentCache full:https://registry.npmjs.org/jest-haste-map set size:undefined disposed:false
679 http fetch GET 200 https://registry.npmjs.org/pretty-format 8408ms (cache miss)
680 silly packumentCache full:https://registry.npmjs.org/pretty-format set size:undefined disposed:false
681 http fetch GET 200 https://registry.npmjs.org/@types%2fnode 8554ms (cache miss)
682 silly packumentCache full:https://registry.npmjs.org/@types%2fnode set size:undefined disposed:false
683 http fetch GET 200 https://registry.npmjs.org/jest-runner 15936ms (cache miss)
684 silly packumentCache full:https://registry.npmjs.org/jest-runner set size:undefined disposed:false
685 http fetch GET 200 https://registry.npmjs.org/@types%2fnode 39623ms (cache miss)
686 silly packumentCache full:https://registry.npmjs.org/@types%2fnode set size:undefined disposed:false
687 silly fetch manifest ts-node@>=9.0.0
688 silly packumentCache full:https://registry.npmjs.org/ts-node cache-miss
689 http fetch GET 200 https://registry.npmjs.org/ts-node 2497ms (cache miss)
690 silly packumentCache full:https://registry.npmjs.org/ts-node set size:undefined disposed:false
691 silly fetch manifest @swc/core@>=1.2.50
692 silly packumentCache full:https://registry.npmjs.org/@swc%2fcore cache-miss
