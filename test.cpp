#include <iostream>
#include <vector>
#include <cmath>
#include <GLFW/glfw3.h>
#include <glad/glad.h>

// 组合数计算函数
int comb(int n, int r) {
    int result = 1;
    for (int i = 1; i <= r; ++i) {
        result *= (n - (r - i));
        result /= i;
    }
    return result;
}

class CurveDrawer {
public:
    // 获取插值点
    std::vector<std::vector<double>> getInterpolationPoints(const std::vector<std::vector<double>>& controlPoints, const std::vector<double>& tList) {
        int n = controlPoints.size() - 1;
        std::vector<std::vector<double>> interPoints;
        for (double t : tList) {
            std::vector<double> Bt(3, 0.0);
            for (size_t i = 0; i < controlPoints.size(); ++i) {
                for (size_t j = 0; j < 3; ++j) {
                    Bt[j] += comb(n, i) * std::pow(1 - t, n - i) * std::pow(t, i) * controlPoints[i][j];
                }
            }
            interPoints.push_back(Bt);
        }
        return interPoints;
    }

    // 获取控制点列表
    std::vector<std::vector<double>> getControlPointList(const std::vector<std::vector<double>>& pointsArray, double k1 = -1, double k2 = 1) {
        std::vector<std::vector<double>> points = pointsArray;
        int index = points.size() - 2;
        std::vector<std::vector<double>> res;
        for (int i = 0; i < index; ++i) {
            std::vector<std::vector<double>> tmp = {points[i], points[i + 1], points[i + 2]};
            std::vector<double> p1 = tmp[0];
            std::vector<double> p2 = tmp[1];
            std::vector<double> p3 = tmp[2];

            double l1, l2;
            if (k1 == -1) {
                l1 = std::sqrt(std::pow(p1[0] - p2[0], 2) + std::pow(p1[1] - p2[1], 2) + std::pow(p1[2] - p2[2], 2));
            }
            l2 = std::sqrt(std::pow(p2[0] - p3[0], 2) + std::pow(p2[1] - p3[1], 2) + std::pow(p2[2] - p3[2], 2));
            k1 = l1 / (l1 + l2);
            k2 = l2 / (l1 + l2);

            std::vector<double> p01 = {k1 * p1[0] + (1 - k1) * p2[0], k1 * p1[1] + (1 - k1) * p2[1], k1 * p1[2] + (1 - k1) * p2[2]};
            std::vector<double> p02 = {(1 - k2) * p2[0] + k2 * p3[0], (1 - k2) * p2[1] + k2 * p3[1], (1 - k2) * p2[2] + k2 * p3[2]};
            std::vector<double> p00 = {k2 * p01[0] + (1 - k2) * p02[0], k2 * p01[1] + (1 - k2) * p02[1], k2 * p01[2] + (1 - k2) * p02[2]};

            std::vector<double> sub = {p2[0] - p00[0], p2[1] - p00[1], p2[2] - p00[2]};
            std::vector<double> p12 = {p01[0] + sub[0], p01[1] + sub[1], p01[2] + sub[2]};
            std::vector<double> p21 = {p02[0] + sub[0], p02[1] + sub[1], p02[2] + sub[2]};

            res.push_back(p12);
            res.push_back(p21);
        }

        std::vector<double> pFirst = {points[0][0] + 0.1 * (res[0][0] - points[0][0]), points[0][1] + 0.1 * (res[0][1] - points[0][1]), points[0][2] + 0.1 * (res[0][2] - points[0][2])};
        std::vector<double> pEnd = {points.back()[0] + 0.1 * (res.back()[0] - points.back()[0]), points.back()[1] + 0.1 * (res.back()[1] - points.back()[1]), points.back()[2] + 0.1 * (res.back()[2] - points.back()[2])};
        res.insert(res.begin(), pFirst);
        res.push_back(pEnd);

        return res;
    }

    // 绘制曲线
    void plot_points(const std::vector<std::vector<double>>& points) {
        std::vector<std::vector<double>> controlP = getControlPointList(points);
        int l = points.size() - 1;

        // GLFW初始化
        if (!glfwInit()) {
            std::cerr << "Failed to initialize GLFW" << std::endl;
            return;
        }

        // 设置GLFW窗口属性
        glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
        glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 3);
        glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);

        // 创建窗口对象
        GLFWwindow* window = glfwCreateWindow(800, 600, "3D Curve", nullptr, nullptr);
        if (window == nullptr) {
            std::cerr << "Failed to create GLFW window" << std::endl;
            glfwTerminate();
            return;
        }

        // 设置当前上下文
        glfwMakeContextCurrent(window);

        // 初始化GLAD
        if (!gladLoadGLLoader((GLADloadproc)glfwGetProcAddress)) {
            std::cerr << "Failed to initialize GLAD" << std::endl;
            glfwDestroyWindow(window);
            glfwTerminate();
            return;
        }

        std::vector<double> t;
        for (int i = 0; i < 50; ++i) {
            t.push_back((double)i / 49);
        }

        // 渲染循环
        while (!glfwWindowShouldClose(window)) {
            // 处理输入事件
            processInput(window);

            // 渲染指令
            glClearColor(0.2f, 0.3f, 0.3f, 1.0f);
            glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

            // 绘制曲线
            for (int i = 0; i < l; ++i) {
                std::vector<std::vector<double>> p_adjusted = {points[i], controlP[2 * i], controlP[2 * i + 1], points[i + 1]};
                std::vector<std::vector<double>> interPoints_adjusted = getInterpolationPoints(p_adjusted, t);
                std::vector<double> x_adjusted, y_adjusted, z_adjusted;
                for (const auto& point : interPoints_adjusted) {
                    x_adjusted.push_back(point[0]);
                    y_adjusted.push_back(point[1]);
                    z_adjusted.push_back(point[2]);
                }

                glBegin(GL_LINE_STRIP);
                for (size_t j = 0; j < x_adjusted.size(); ++j) {
                    glVertex3d(x_adjusted[j], y_adjusted[j], z_adjusted[j]);
                }
                glEnd();
            }

            // 交换缓冲并且检查触发事件
            glfwSwapBuffers(window);
            glfwPollEvents();
        }

        // 释放资源
        glfwTerminate();
    }

private:
    // 处理窗口输入的函数（例如关闭窗口等操作）
    void processInput(GLFWwindow* window) {
        if (glfwGetKey(window, GLFW_KEY_ESCAPE) == GLFW_PRESS) {
            glfwSetWindowShouldClose(window, true);
        }
    }
};