{"_id": "pstree.remy", "_rev": "9-6402ca83c692f8759d2570614e352fb5", "name": "pstree.remy", "description": "Collects the full tree of processes from /proc", "dist-tags": {"latest": "1.1.8"}, "versions": {"1.1.0": {"name": "pstree.remy", "version": "1.1.0", "main": "lib/index.js", "scripts": {"test": "NO_PS=1 tap tests/*.test.js"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {"ps-tree": "^1.1.0"}, "description": "Collects the full tree of processes from /proc", "gitHead": "3239a7af6cfe64338aa05c03b1ab1b51af4c274e", "_id": "pstree.remy@1.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-q5I5vLRMVtdWa8n/3UEzZX7Lfghzrg9eG2IKk2ENLSofKRCXVqMvMUHxCKgXNaqH/8ebhBxrqftHWnyTFweJ5Q==", "shasum": "f2af27265bd3e5b32bbfcc10e80bac55ba78688b", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHkRFOGxLpskajkD9IHITePu6S4G+PTlIbXlSWnw1MqnAiAkSR1ubqrD8hXccsiSifBNwRQnYrmLAZsbePdJPvioLA=="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy-1.1.0.tgz_1513769072864_0.8684165142476559"}}, "1.1.1": {"name": "pstree.remy", "version": "1.1.1", "main": "lib/index.js", "scripts": {"test": "NO_PS=1 tap tests/*.test.js"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "gitHead": "c682635a70908d80e70917643f6706935009c117", "_id": "pstree.remy@1.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.10.0", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PEj1O+zecMF55b/wnZXuO3viaJvaqfTjcY+U9NwS+c6DC9JO/cj3+hCg0El1RjJevF1Yx/j/eLuZiIsQZ6bQbA==", "shasum": "2f1f2962af233e774d8fdffee3fdbde143997336", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.1.tgz", "fileCount": 8, "unpackedSize": 13820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9mhXCRA9TVsSAnZWagAAkkYP/iaKWekoZb82TK+wLNN8\nzpq68ewCz/brGgatjr6Jppe1bPPq2tqc0y07kKDg/r+A6VMp+gq+8YkEabaD\nogxUyz8smrXWKVr8yJEkZG8/eRf5botRTENZE6sDCr/FjjlINsEakecqEOA2\nTLyJUSTRzgTUBJEfrbG+Q92D+yoINAFqFXjZVrefk2EiepRs3vFcAx/yTGVt\nJhVGmHnwhRWVrRaOJd8PToG7jWvBxCGbrsAUPfdonKWPIkGzNGjq2PLSDBLT\n69c2u9v8Gt6xsfk8zq0pmjbOt9d5UqrxSwI8R7XS5Hw3RBySY/qATTj4xNH1\nVvy+RjbSIRjbL235rwPDdyyDEW1zKxQOERRv/yJ6D6rTmMPtwPiTzYJnIbIn\n9k9t8TILVfk/vbTXXIuoIeKDP7c7KmbwK1abh535SUN1xscXN7l9j5seNG3S\notO8k2Ca90OyPPhwmH9ryZTm6WvyaOoA/JwyUZjy9aEltB00lGabqetqYQb9\nTYH0aofRRhRlj2ylzxVW8gBM+MZeYpnhlGwfv5M0ECMSkXAQrYECEwXtPfgX\nXymwfh7E4YHEsSreo4LZevV3X3Gan7mN5KDfew0XYCzPMQMnnWZWn+UZkRdp\ntvVvni8KVcBnS3mlkK5uWznVxZcqMrTxNgIbq+m/lzhk2MNeFdd+ODQZ1H84\nMKbt\r\n=HM8T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDc5oT/aY4SUhhTrYK5HKPo/umE8BGqVRj3E+F0xtk77QIgYGhLAPc0KF4GL0EHAnkKbgFZVR+EO2yfwvnf/6MfhS0="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy_1.1.1_1542875223023_0.5814988530090033"}, "_hasShrinkwrap": false}, "1.1.2": {"name": "pstree.remy", "version": "1.1.2", "main": "lib/index.js", "scripts": {"test": "NO_PS=1 tap tests/*.test.js"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "gitHead": "ae7856d1d6235eae872d9df1e2c299b80b4af998", "_id": "pstree.remy@1.1.2", "_npmVersion": "6.4.1", "_nodeVersion": "8.10.0", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vL6NLxNHzkNTjGJUpMm5PLC+94/0tTlC1vkP9bdU0pOHih+EujMjgMTwfZopZvHWRFbqJ5Y73OMoau50PewDDA==", "shasum": "4448bbeb4b2af1fed242afc8dc7416a6f504951a", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.2.tgz", "fileCount": 8, "unpackedSize": 13878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9pDZCRA9TVsSAnZWagAAfbQP/0FZqdBPduLSfdlPVkVu\nZ1TFiZ23GrIuXw3uCe6krzwlx5jCxcq576Za6EhudOYT8SqGqLd5EtfhOuxc\nhl3rdwt+dT9e6qE/1+nf4Oo/47sfBduvCRKemLEyAez0SRpZLr07sOcTdIHj\nH3R+BLmyq7errWxToBH6Vh22Bnd3UDbYtrOAiynZKxQqLco78dffvt7B+Xw+\nXAyX/LqB+wSwFKJvxEO8r1tbV0SYEDVYwN4TnYi051ySEyY7yK2c8Xd4sDNk\nN2A7Zov9iyDxK/5tPWebzUiK3TlzS6axfXldvs0Zwo5sDj9vczzCaNSJmmKG\nd3n+GNjEZ4XeK7ywLySH9pfTNUJl+pMYQHSZyC+fZH+TfTPNGHsoeprXs2kw\ne+Id5Qs1a1lEmVZXrmD6scix4rGj++O0lZhY2kPJLkxcjw0jfKYCQrof0yoS\nz+PL0NEJ71grzwIEDVoJdr8GwqLdNssyHRELUKhS3n4s3Ff3APEwBxGouojj\nV7r4VEdQRJSXdVXlEy75zUaqoOhaihbh4Ghr9E6bfM2PoqsssWAhBbdfgNUe\njUo6IjWOdy1KdhgLKraZ0pWc5xb485nJB4tXVF55PeqGfLCbEJtjOklAbIre\nRqFRBootFgRJ35xHIxWgOo8jbt8p6qWLy+pZcYNFzdKz0WFPiG+ChufctGqw\npoYA\r\n=ve28\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIsVZnoEQy5mbeR/wXHHd0HECN2e//p8i+eJjLHG+CXAIga0xFo768dsTfIvnTW+UZP2Cu55hZp52CyN8PKwvOYEM="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy_1.1.2_1542885592551_0.17382300625508695"}, "_hasShrinkwrap": false}, "1.1.3": {"name": "pstree.remy", "version": "1.1.3", "main": "lib/index.js", "scripts": {"test": "tap tests/*.test.js"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "gitHead": "90ea7e7f44baad059fca6cd08bc8df49200659fe", "_id": "pstree.remy@1.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "8.10.0", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tGkOSvpMp0j7hskA4izvP0onujJSEAYO0SViHtjJX5a+4cqxVXiq32opahQO4HkA7bnHsRk9xgrf6LVvG/Q9UA==", "shasum": "0a557a6b2ab647cd782bc9f3e2b9e7ad71a9e36c", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.3.tgz", "fileCount": 9, "unpackedSize": 12606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDqtQCRA9TVsSAnZWagAAocgP+wb1SynMQa7JZs5UULds\nmdDWpB+Yu8TfOCkrM8xOJl7+7HoNYCoiI54IfRFTsyeHGXFqMSJu2f7IiZTb\n75BhzMS/Pc4w74hA019oNvHX9+bAeZm9feD3kOkHGfKYpRjanvyJBhUiWxq0\nH89Ik7wPxICZH4ndnpoXqW8MvDv0dvQSi3wn/YlbQW6F5YYtTbblWIhKJZtq\nD83T7lY16RJzJH8+6L0NwSrX1dbLGeoG8D3F7dpSLGk0W/bxQh3ffgoKofCZ\nyW933z8KFfzFZA88kY5ioruKFoAsYjwoaXyOHkbAD6rIvoGsHrUpIdfVzTnh\nSrqRLoOYT60Ea7No1b5GARW/48vVqQ1nrHB4e6Gb48lM2ILQR+kIuTGnK6im\npNdX27X/odDF/9i23V1PtQs+GnHynGUiStOzo7uXM+LC/8qa0f0nYldc/mgH\n4OM0/9t+clc3gPuSczn6noIxWr2CAhv2enFpFpmlB6mjKLdwfoDZtPFo7pQP\nn8E/vfAEruHM0z3FwI2nfNUcvSlLes/Z3Ki5Wx7hm/fYybhAYgy8i22RhKA7\n+IX6ig45TPYuUNWyI6abDllUja3NEuXQRKrG6yAMDSfMiQKVr0gPkdjylRI3\nYdFNowMLnZJs6HnlZ+4rRowRIZiFNdMNlQpxPir1omDYJGITmYRGMa0XL0jv\nRv+m\r\n=1kO5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG4FD5F419KfZM6AxbMTGpF9heGAgM9NJJ13CCmA/uf/AiAMJgRvc231BSXrdOALFJyXhVG993VN3Jc3TMxtu6MBGg=="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy_1.1.3_1544465231914_0.47046098510360324"}, "_hasShrinkwrap": false}, "1.1.4": {"name": "pstree.remy", "version": "1.1.4", "main": "lib/index.js", "scripts": {"test": "tap tests/*.test.js"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "gitHead": "0b7cbe7972e1a46c9cc299aaed1445fe550e085c", "_id": "pstree.remy@1.1.4", "_npmVersion": "6.4.1", "_nodeVersion": "8.10.0", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3kSyTN/iTJMxtL87idnFgTyOp2vQ6B/49QcHUO26kh2M2qahlUivFI1zWJ9FRFPoB+KgcP820JMOuIhkBJAP3Q==", "shasum": "a03d5dbc06ba639fb6dd4874644c4bad9882ec21", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.4.tgz", "fileCount": 9, "unpackedSize": 12606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDrtdCRA9TVsSAnZWagAAKEsP/RQAutL1SKLJ2+ODAs+D\n/bNxFt9Dddk2PPqQMGRuoU5q043PPWYYaSyOCdeB9CVUosF6yEFpzbMN1qV8\n935OJBxKTy2ZfAyOGuXtmMEyq/4EMuMat6M+BztObn1j64HqOu+ue/aJHYuZ\nfumqwwE39DYkhQRPL9M2A9AQNebHI0PfZtryn42Xk3ONKqS7OupLm1+XbclH\n9DZ2P6Mq2PAsXdSD3dON+ZZxD2dLRdw6o0tG18pUl69efc9LWw/tW5S+1Ltr\nD6CN+Q/v/odYdUqBwIVLfCXg9NwlMO6rAZn4lpoqlklq9B5NlAiZclBkOsZV\nGN4hDJ5hXBsHlDtHv6TQFrysVa++tHlLkW99QO3QEYaghJGels9aB/x1GuY6\nQPSy3pqGVtTSyTl4kz5ar18/WUenScbDLUDUvgz9bUJvSXwvIgOljIDTHlq8\nH9PkOsXilxA/gDZDtjBHFPt9B4s1zurMbaFVOJb3d5C7qW4cqcEmQOIbbXj3\nB16pg7QS8qNcZaTCWVKS6ZScDy7vDKfGITg+A0vstT5P9m8m2K72eV8/dDOl\nAR+1dhhZpIlGvUXS5/trHG/RpkHDesBcNiQnLUlUhkOVLPrFcJEQdWVh7hdj\nuuMMz6umqHb86gX5KYdjv+nHXSivwlkNDuPkMcBSS0Srx4UE99frYOSid27c\nxZ1i\r\n=162+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIANQXkr6gTrXPjM10qsZFKSZOisEe0vJ7Y6xcXMQAm5MAiEApWe8yCtYi5vUlOCU/rWunNxqwpUeQFm0/6K7J5BAnUU="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy_1.1.4_1544469340824_0.6489857917696713"}, "_hasShrinkwrap": false}, "1.1.5": {"name": "pstree.remy", "version": "1.1.5", "main": "lib/index.js", "scripts": {"test": "tap tests/*.test.js"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "gitHead": "880e1d506b48ed508dc6aef0208dd0399e3b44d6", "_id": "pstree.remy@1.1.5", "_npmVersion": "6.4.1", "_nodeVersion": "8.10.0", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qxOUs7+/NMfoa0sEZuWirQ8tKoBvFXtOOerNrVcHlXmNcMxIDtpEfVByyc9ad2ZRt+01Y6eAwQAMZCwG/1vwvg==", "shasum": "18645bbea9cc206eb1c0f12c5ea0fdf02550694b", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.5.tgz", "fileCount": 9, "unpackedSize": 12621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcE9dQCRA9TVsSAnZWagAAVP8P/0nKdjZONgwhTEKKH17T\nAw+m+Juf3H5vd6l8GRLUqKXibvew+hstq7xGLKChQvcI12jbYe8g3LFnW7+r\nUGzfmCApEENUH7lL8dX2MorvfYvaGqVn+fsdnuDweJGGKS+5PtKsIcCbq1a2\nybterXkbAhnCgdMF/xVklTtzH4Cmhi9DkmnEmczBeuA7++NqffG0NZ+KmLgb\nNt0mo1Bo4OEeXE2F0QFYqb3uEB/o17pfuSNis1jejv5nnvmLoQ0miuokCsxA\nKD3QPS8ZiQ/3J+1eoTmKGNM1e9nq0nBkb9EXUrCWH5VYZgqCGolcsDeQAvvN\nfz1wYl4R9uWpWekoAPa5w7Sr8cXxAbUKwqpRDwINR96Yi3/uMQyGN4q4dhbS\nM10TzQOrEd/W0BncLC5AqDH4TL0gtZ5WZG5DjZFGNlNpWkQSXE8HkKbE0EG9\nsV8V5ZGVnHW57m+DrzFImuBDfZKVMpVVTpUZatDr6akNdxv0zuZSi9/kfw7Q\np2hrt7m3nPnKJhVJFZ0oGJCMbOC5s1wC7JPvh8wU20x63Wn1sN1VmbffmryD\nTEVwfY98h5dPpJ/8WA5MdPBbSmYcgrjg/5LP7th/jecyXc6BCTrpa75hLdNb\nM4Qk1A6j3AC9My+yqK3P2oPTOqFJljB4pHE5JK+x1+Af6smeU1/A61qdqki3\nAZHC\r\n=1Fpk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOK9mPLUCmkTg5Po8VJFFj073ReNJQx8wji0eCj3qIGQIhAI5xCDevLtKyHOmt5iiKpMICsd+EkW/dO+Z/VnZL8jka"}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy_1.1.5_1544804175491_0.10538741199854207"}, "_hasShrinkwrap": false}, "1.1.6": {"name": "pstree.remy", "version": "1.1.6", "main": "lib/index.js", "scripts": {"test": "tap tests/*.test.js"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "gitHead": "880e1d506b48ed508dc6aef0208dd0399e3b44d6", "_id": "pstree.remy@1.1.6", "_npmVersion": "6.4.1", "_nodeVersion": "8.10.0", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NdF35+QsqD7EgNEI5mkI/X+UwaxVEbQaz9f4IooEmMUv6ZPmlTQYGjBPJGgrlzNdjSvIy4MWMg6Q6vCgBO2K+w==", "shasum": "73a55aad9e2d95814927131fbf4dc1b62d259f47", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.6.tgz", "fileCount": 9, "unpackedSize": 12622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcE9nYCRA9TVsSAnZWagAACZYQAJxtJpf1DC+O0anOtlZz\n36EY1+9uplTigj8AWN/t6bKE+Zxpl+Tp6/3iKgFmTFeD/pK2PIEk9u8OQyqw\nEYim4ELMrO/1tUi7qmWxeFLVOrizTKRT28XcqG2ADHSrsArZioPO0Eavocgt\nfE1cQ1Gb9I0WDp8PXgAJZNPbHf/PKEJyDx6Gz6joIC1SELkn/DZWyc1Styp/\nq/eyEtmEiM3XfU/2zHAnewOLXtmgtBXp6ZDJyKYs8S7Ej+UNwYbHCQm+gXAh\nuVV3StX6AZ3HOmVaq1OKoc7zNTAbbKK5/VqbDzA82I2PGDTqAU+/u0VFaME5\nVz6KO2EfqVbMWHOXABX6DYM0vVVOqk6CZJy+eZFlyl4e/bj/TSGBeSKkrR8n\nB9ykVj9DQiJ45pLjpp1CBQ0vT9ShnZKd/grLibXHldEc/7NcqFr5QJmbtnOK\nyPKRtzkRhXXe9/G11xHjLVWUWCZNLoIPuH+bvNVeqsOsSYr7GJAsdRlKR8Wt\nmLEbcwctR/4yRxQvATfYj1je/l+TCPfsqDkO5MUnU1IvLqzC/Ngwjdin7grb\nXJn0Ta9TF/OdRGbinGOOn2L3lPDU9AEFav3sHrg6x7BrlWwC0dncVGuA2DHz\n12xMJuLvTZ+nH8Cs8E9oGOrl2fFBDQOBC6n/yo6cJ9Mroj3lA4jXDmAae2SA\nyXVx\r\n=cRoe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDcZmjU47YvTjtEwRqrYuYPYKZNmZLVydlSwIfzRB41hAiEA4bYjQ+uQG2+8jakt8A46g+MxZLAle3c70bih6kU9tNs="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy_1.1.6_1544804823707_0.3752670535489697"}, "_hasShrinkwrap": false}, "1.1.7": {"name": "pstree.remy", "version": "1.1.7", "main": "lib/index.js", "scripts": {"test": "tap tests/*.test.js", "_prepublish": "npm test"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "gitHead": "44da1174fc8a0c68d7bc30cacad9cec28c50f64e", "_id": "pstree.remy@1.1.7", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.1", "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xsMgrUwRpuGskEzBFkH8NmTimbZ5PcPup0LA8JJkHIm2IMUbQcpo3yeLNWVrufEYjh8YwtSVh0xz6UeWc5Oh5A==", "shasum": "c76963a28047ed61542dc361aa26ee55a7fa15f3", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.7.tgz", "fileCount": 10, "unpackedSize": 13779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7rjCCRA9TVsSAnZWagAAtxIP/2e40kOGpmd44OswAcXF\nJ1cogI9DFlAmzMYJBHl4l54jKoN6WUqxfNKhF4qH9D3S1Izh4dTg8Z4xqC9I\nqeC4SgeoXPV65flq0+cbRMfXmI9QDlgy80HY0PBZqZYU3sM587nNxkNKv7T2\nWn8l5ApsFpBJa1hqnIU2HRTuRtUXRH1erVPtcvA0xBPFzr1CwMwmM56krqdX\n1ZjQpgj/jBdoBN5i0JaukVoh7t5RD/llBJn2bB0HEhB7oMKMUywoELS55ZMR\nWydtqUyldD+uHcvWgyEwLJIZxPQSQuw+gTxemoUs0USzb+Qz8E+detdcyJGe\n9UvLx79Kpcm+EHTb09v50RtW4szcnz83w+xzrh3+e82PP/IeOhfp8HjbgwId\nZCt6cm4MuL3acG1L9A2Dh9BQSGZHq9Hh9sUgmCwJRynOuDmazNieBzsgrpfV\nSRo8jitqfX8vzltmXFGGTwt7HYRICAfNssLklKiWp0U/dk2AIrketuAlj5Fa\n2Sf0DKi2LqANJ4mBd/8SVhyD/JxhSdnJ1YsPs5AXKBHw7N6HctTremaEvikI\nKwKNl/qBXuiAOrQyeh2LqA+nc9gSDFuu4G7e3bIMC7GFXaoyG6P47VYxMLI2\nv8/4qnw9td+puRRVxX3IvwPccg1PQY2I0fHsdnwfZ4vDyGc/wHiaXqjMpn1d\nIqQb\r\n=6nX7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGMP6I+T2rUvo/fnQZtS9wVT/QWSmigIUNJYFdmEmX0NAiEAmNf6ZCP7v9LIfFZhdQQWGJSLawavGPd1/8Q7TIGcaLc="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy_1.1.7_1559148737521_0.016928364077606783"}, "_hasShrinkwrap": false}, "1.1.8": {"name": "pstree.remy", "version": "1.1.8", "main": "lib/index.js", "prettier": {"trailingComma": "es5", "semi": true, "singleQuote": true}, "scripts": {"test": "tap tests/*.test.js", "_prepublish": "npm test"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/remy/pstree.git"}, "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc", "gitHead": "4b21b3fce1fe22e722a942f11b85e419be2c85b5", "bugs": {"url": "https://github.com/remy/pstree/issues"}, "homepage": "https://github.com/remy/pstree#readme", "_id": "pstree.remy@1.1.8", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w==", "shasum": "c242224f4a67c21f686839bbdb4ac282b8373d3a", "tarball": "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz", "fileCount": 11, "unpackedSize": 14977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJev/+jCRA9TVsSAnZWagAAXHIP/2DAUhTQmPUDroJF7s2r\nmo9oop30MPeMM9gGC+t06+CmtJr/pDTpdBigvW/OA89jLU3/A2Fn6SusUY1o\nh2ShrdrqgS7x/NsGUi01nxberrcDJSATJ2UdDX113I8c14s/xbcLBubkxrSL\nXToviaSvy+bInEXEwCNAvQJrkDUZFp/UGrksC5qspOnT7UzXDpw9ZAyih3fa\ncRk2nUs+a9tWewIUh9Ee0v0G2rX8HmFDAcjJ7RL7X/H9Exy6fWNYpjxsyxjC\nFULOXECNkBIUazcVN9YzC+2cXzkU5NObzdWny3Fvfu6uVAzPmYciWysDesk5\nl83HIy1Jn/G03CwDeQAtsvovibNQaYVTV+zSo91YVNsnJGJe89BjDHzIQsh3\nFXPG9hgX0u96z/OPsfSu0mmBkEGQ7vtfkt1uB9nuyJ2VVa0tuOwrd440zzou\nv7dWPOgDUsJLCwLMUxbVl1So1AL2JGQeBKlD1XZNb7WPBg7iYP0dBXIekUv2\nZag9gSR1Ly1dmwTBunE/Q1Ml/hifY3QuCbMOSC/0pNixtFeadRoQznHEDSQd\n4l1vKyISp/qgF696R2jQmcM0xWFWHiXhiS9hWYUIMqpfbFCf0yc3XzddP03D\nAuJRtLslOFHZGt5z26sJfGXyyZs/rTivdL923MZXM8OviCuwyTYlbYZsHKdF\nZClt\r\n=keAJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDm9ai1WXYXzzhUK2IuCflO6I6It+aFAkgA53oSDLi3FAiEAp+feGT5eXJsXD739UqpYxSg6lsQyqjEt9w7swBCK9mE="}]}, "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "_npmUser": {"name": "remy", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pstree.remy_1.1.8_1589641123169_0.8093311091158859"}, "_hasShrinkwrap": false}}, "readme": "# pstree.remy\n\n> Cross platform ps-tree (including unix flavours without ps)\n\n## Installation\n\n```shel\nnpm install pstree.remy\n```\n\n## Usage\n\n```js\nconst psTree = psTree require('pstree.remy');\n\npsTree(PID, (err, pids) => {\n  if (err) {\n    console.error(err);\n  }\n  console.log(pids)\n});\n\nconsole.log(psTree.hasPS\n  ? \"This platform has the ps shell command\"\n  : \"This platform does not have the ps shell command\");\n```\n", "maintainers": [{"name": "remy", "email": "<EMAIL>"}], "time": {"modified": "2022-05-13T14:59:47.521Z", "created": "2017-12-20T11:24:33.756Z", "1.1.0": "2017-12-20T11:24:33.756Z", "1.1.1": "2018-11-22T08:27:03.171Z", "1.1.2": "2018-11-22T11:19:52.689Z", "1.1.3": "2018-12-10T18:07:12.192Z", "1.1.4": "2018-12-10T19:15:40.958Z", "1.1.5": "2018-12-14T16:16:15.619Z", "1.1.6": "2018-12-14T16:27:03.888Z", "1.1.7": "2019-05-29T16:52:17.640Z", "1.1.8": "2020-05-16T14:58:43.262Z"}, "keywords": ["ps", "pstree", "ps tree"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "readmeFilename": "README.md", "homepage": "https://github.com/remy/pstree#readme", "repository": {"type": "git", "url": "git+https://github.com/remy/pstree.git"}, "bugs": {"url": "https://github.com/remy/pstree/issues"}}