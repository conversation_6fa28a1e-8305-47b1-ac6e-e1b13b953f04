一维差分数组通常构造方式：

![image-20241126162134727](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126162134727.png)

构造差分数组，可以让我们对该数组用前缀和的方式得到原数组：

b数组构造不一定要以上的方式:

![image-20241126163536303](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126163536303.png)

假定a数组全部为零，[1,1]区间全部加上a1,....[n,n]+a[n]这就构造出来了差分数组，把它存在a数组里面

例如:[1,1]插入a[1]，就是0+a[1],第二项-a[1],那么此时a[1]=a[1]

[2,2]插入a[2],就是0+a[2]-a[1],第三项-a[2]

[3,3]插入a[3]，就是0+a[3]-a[2],第四项-a[3]



# 使用差分数组

如果给b[l]+c,则a[l]=b1+...+b[l]+c它也会加上c,但是 a[l+1],...a[n]也会加上c,则我们需要把b[l+1]-c

若要用在[l,r]区间内的话:用O(1)的时间给一段区间加上一个固定值c

b[l]+c,b[r+1]-c

[一维差分简单题](https://www.acwing.com/activity/content/code/content/7712239/)

```c++

#include<iostream>
using namespace std;

const int N=1e5+10;

int main()
{
    int n,m,a[N],b[N];
    scanf("%d%d",&n,&m);
    for(int i=1;i<=n;i++)
    {
        scanf("%d",&a[i]);
        //构造差分
        b[i]=a[i]-a[i-1];
    }

    while(m--)
    {
        int l,r,c;
        scanf("%d%d%d",&l,&r,&c);
        //修改差分
        b[l]+=c;
        b[r+1]-=c;
    }
    for(int i=1;i<=n;i++)
    {
        //通过修改后的差分得到修改后的原数组
      a[i]=b[i]+a[i-1];
     printf("%d ",a[i]);
    }
    return 0;
}

```

```c++
//按上述方式差分得到的数组
#include<iostream>
#include<algorithm>
using namespace std;

const int N = 100010;

int n, m;
int a[N], b[N];

void insert(int l, int r,int c)
{
	b[l] += c;
	b[r + 1] -= c;
}

int main()
{
	scanf_s("%d%d", &n, &m);
	for (int i = 1;i <= n;i++)scanf_s("%d", &a[i]);
	for (int i = 1;i <= n;i++)insert(i, i, a[i]);
	while (m--)
	{
		int l, r, c;
		scanf_s("%d%d%d", &l, &r, &c);
		insert(l, r, c);
	}
	for (int i = 1;i <= n;i++)
	{
		b[i] += b[i - 1];
	}
	for (int i = 1;i <= n;i++)printf("%d ", b[i]);
	return 0;
}
```

# 使用二维差分数组
