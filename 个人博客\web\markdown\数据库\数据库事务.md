# 事务

![image-20241111144039924](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111144039924.png)

# 事务的操作

## 1.演示

建表

![image-20241111144306659](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111144306659.png)

语句

![image-20241111144550343](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111144550343.png)

结果

![image-20241111144612656](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111144612656.png)

## 2.报错

![image-20241111144800829](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111144800829.png)

## mysql中事务会自动提交

## 上面每一句都是一句事务，我们需要能够控制事务

## 3.控制事务提交指令

![image-20241111145319076](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111145319076.png)

## 4.手动提交事务：当我们设置set。。。。。。语句后，我们每一个DML（同事务）都是需要手动提交的

![image-20241111145414761](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111145414761.png)

## 5.遇到报错执行回滚事务：rollback；

![image-20241111145728619](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111145728619.png)

# 2.演示2

![image-20241111145854444](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111145854444.png)

## 进行异常判定

![image-20241111150114103](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111150114103.png)

1.要是无异常，执行commit；

2.有异常，执行rollback回滚事务；

## 事务的一个案例

![image-20241111150708489](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111150708489.png)

# 事务的四大特性

![image-20241111152610395](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241111152610395.png)