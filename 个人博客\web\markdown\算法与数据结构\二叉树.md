# 二叉树

## 1.数据结构定义

```c++
typedef struct Node
{
 int data;//也可以是其他类型的data,比如char
 struct Node *lchild,*rchild;
}*BiTree,BiNode;//BiTree用来定义整棵树,BiNode用来定义树结点
```

## 2.创建二叉树

```c++
void CreateBiTree(BiTree *T)
{
  int ch;
  cin>>ch;
  if(ch='0')
  	T=NULL;//空树
  	else
  	{
	T=new BiNode;
    T->data=ch;
    CreateBiTree(T->lchild);
    CreateBiTree(T->rchild);
	}
}
```

```tex
结构体用点（.），结构体指针用箭头（->）
也就是说点的左边必须为实体，箭头左边必须为指针。
```

