const request = require('supertest');
const app = require('../src/app');

describe('Weather API', () => {
  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    });
  });

  describe('GET /api/weather/current', () => {
    it('should return current weather for valid city', async () => {
      const response = await request(app)
        .get('/api/weather/current?city=Beijing')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('location');
      expect(response.body.data).toHaveProperty('current');
      expect(response.body.data).toHaveProperty('weather');
    });

    it('should return 400 for missing city parameter', async () => {
      const response = await request(app)
        .get('/api/weather/current')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).toHaveProperty('code', 'MISSING_CITY');
    });

    it('should return 400 for invalid city name', async () => {
      const response = await request(app)
        .get('/api/weather/current?city=123!@#')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).toHaveProperty('code', 'INVALID_CITY');
    });
  });

  describe('GET /api/weather/coordinates', () => {
    it('should return weather for valid coordinates', async () => {
      const response = await request(app)
        .get('/api/weather/coordinates?lat=39.9042&lon=116.4074')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
    });

    it('should return 400 for missing coordinates', async () => {
      const response = await request(app)
        .get('/api/weather/coordinates?lat=39.9042')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).toHaveProperty('code', 'MISSING_COORDINATES');
    });

    it('should return 400 for invalid coordinates', async () => {
      const response = await request(app)
        .get('/api/weather/coordinates?lat=999&lon=999')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).toHaveProperty('code', 'INVALID_COORDINATES');
    });
  });

  describe('GET /api/weather/forecast', () => {
    it('should return forecast for valid city', async () => {
      const response = await request(app)
        .get('/api/weather/forecast?city=Beijing&days=3')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('forecast');
      expect(Array.isArray(response.body.data.forecast)).toBe(true);
    });
  });

  describe('GET /api/weather/search', () => {
    it('should return cities for valid search query', async () => {
      const response = await request(app)
        .get('/api/weather/search?q=Beijing')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should return 400 for missing search query', async () => {
      const response = await request(app)
        .get('/api/weather/search')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).toHaveProperty('code', 'MISSING_QUERY');
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting', async () => {
      // 发送大量请求来测试速率限制
      const requests = Array(10).fill().map(() => 
        request(app).get('/api/weather/current?city=Beijing')
      );

      const responses = await Promise.all(requests);
      
      // 检查是否有请求被限制
      const hasRateLimitedResponse = responses.some(res => res.status === 429);
      
      // 注意：在测试环境中可能不会触发速率限制，这取决于配置
      // 这个测试主要是为了确保速率限制中间件正常工作
    });
  });
});
