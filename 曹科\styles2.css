body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
  }
  
  header {
    background-color: #333;
    color: white;
    padding: 10px;
  }
  
  nav ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
  }
  
  nav ul li {
    margin-right: 20px;
  }
  
  nav ul li a {
    color: white;
    text-decoration: none;
  }
  
  .user-info {
    display: flex;
    align-items: center;
    margin-left: auto;
  }
  
  .user-info img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
  }
  
  .user-info a {
    color: white;
    text-decoration: none;
    margin-left: 10px;
  }
  
  main {
    display: flex;
    padding: 20px;
  }
  
  .article-list article {
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .article-title a {
    color: #333;
    text-decoration: none;
    font-size: 20px;
  }
  
  .article-summary {
    margin-top: 10px;
    color: #666;
  }
  
  .article-info {
    margin-top: 10px;
    color: #999;
  }
  
  aside {
    width: 300px;
    margin-left: 20px;
  }
  
  aside h3 {
    font-size: 18px;
    margin-bottom: 10px;
  }
  
  aside ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
  }
  
  aside ul li {
    margin-bottom: 5px;
  }
  
  aside ul li a {
    color: #333;
    text-decoration: none;
  }
  
  footer {
    background-color: #333;
    color: white;
    padding: 10px;
    text-align: center;
  }
  
  footer ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
  }
  
  footer ul li {
    margin-right: 20px;
  }
  
  footer ul li a {
    color: white;
    text-decoration: none;
  }