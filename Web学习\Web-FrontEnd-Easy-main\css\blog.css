@charset "utf-8";
.mid{
    height: 100vh;
    width: 100vw;
}

.content{
    padding-top: 60px;
    padding-left: 20%;
    display: flex;
    flex-direction: column;
}

.con{
    display: flex;
    width: 650px;
    height: 200px;
    background: rgba(233, 175, 146, 0.2);
    backdrop-filter: blur(0.2);
    float: left;
    clear: left;
    margin-top: 30px;
    backdrop-filter: blur(30px);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(68, 79, 87, 0.8);
}
.c1{
    animation: c1load 0.5s linear;
}
.c2{
    animation: c2load 1s linear;
}
.c3{
    animation: c3load 1.5s linear;
}

.c1 .img{
    background-image: url("../images/project1.png");
}

.c2 .img{
    background-image: url("../images/project22.png");
    
}

.c3 .img{
    background-image: url("../images/project32.png");
}

.imghid{
    overflow: hidden;
    width: 250px;
}
.con .img{
    width: 230px;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position:center;
    opacity: 0.8;
    transition: 0.5s ease 0s;
}
.con .img:hover{
    transform: scale(1.1);
}
.con .txt{
    width: 400px;
    padding: 0 40px;
    color: rgb(25, 39, 49);
    
}
.con .txt h2{
    color: rgb(25, 39, 49);
}
.pri{
    height: 500px;
    width: 300px;
    float: right;
    clear: right;
    position: fixed;
    margin-top: -650px;
    margin-left: 1100px;
    animation: priload 2s linear;
}

.pri .prihead{
    height: 250px;
    background-color: rgb(255, 210, 198);
    border-radius: 20px;
    padding-top: 50px;
    box-shadow: 0 0 8px rgba(39, 69, 88,0.6);
}

.prihead a{
    display: block;
    overflow: hidden;
    border-radius: 50%;
    width: 90px;
    margin: auto;
    border: 3px groove rgb(251, 113, 45);
}
.pri .prihead .tx{
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background-image: url("../images/tx.png");
    background-size: cover;
    transition: 0.5s ease 0s;
}
.prihead .tx:hover{
    transform: scale(1.1);
}

.prihead h2{
    color: rgb(44, 40, 32);
    text-align: center;
}
.prihead p{
    color: rgb(52, 29, 24);
    text-align: center;
}

.pri .tag{
    height: 200px;
    background-color: rgb(243, 232, 229);
    margin-top: 50px;
    border-radius: 20px;
    box-shadow: 0 0 8px rgba(39, 69, 88,0.6);
}

.tag h2{
    text-align: center;
    padding-top: 20px;
    color: rgb(63, 24, 21);
    font-weight: bolder;
}

.tag p{
    word-wrap: break-word;
    display: block;
    width: 220px;
    padding-left: 40px;
    color: rgb(255, 144, 85);
}


@keyframes c1load{
    0%{
        opacity: 0;
    }
    70%{
        opacity: 0;
        margin-top: 100px;
    }
    100%{
        opacity: 1;
        margin-top: 40px;
    }
}
@keyframes c2load{
    0%{
        
        opacity: 0;
    }
    80%{
        opacity: 0;
        margin-top: 100px;
    }
    100%{
        opacity: 1;
    }
}
@keyframes c3load{
    0%{
        
        opacity: 0;
    }
    90%{
        opacity: 0;
        margin-top: 100px;
    }
    100%{
        opacity: 1;
    }
}
@keyframes priload{
    0%{
        opacity: 0;
    }
    80%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}