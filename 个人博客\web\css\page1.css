* {
    margin: 0;
    padding: 0;
}

body {
    background-color: #b31b11;
    background-image: url(../images/page1.png);
    background-size: 100vw, 100vh;
    margin: 0;
    padding: 0;
}

header {
    background: rgba(255, 255, 255, 0);
    color: white;
    padding: 10px 0;
    text-align: center;
}

nav ul {
    list-style-type: none;
    padding: 0;
}

nav ul li {
    display: inline;
    margin: 0 15px;
    list-style: none;
}

nav a {
    color: white;
    text-decoration: none;
}

nav a:hover {
    text-decoration: underline;
}


footer {
    text-align: center;
    padding: 10px 0;
    background: rgba(255, 255, 255, 0);
    color: white;
    position: fixed;
    bottom: 0;
}

.left {
    position: fixed;
    left: 0;
    top: 41px;
    width: 250px;
    height: 100vh;
    text-align: center;
    line-height: 100vh;
}

.wrapper {
    margin: 0 auto;
    width: 1000px;
    height: 100vh;
    overflow: hidden;
}


p {
    text-align: center;
    margin-bottom: 30px;
    /* 设置段落之间的间隔，可以根据需要调整 */
    color: aliceblue
}

h1 {
    text-align: center;
    margin-bottom: 10px;
}

a {
    text-decoration: none;
    color: inherit;
}

.right-link {
    position: fixed;
    right: 0;
    top: 50%;
    transform: translateY(-50%);

    /* 添加宽高属性 */
    width: 200px;
    height: 200px;

    /* 可以添加背景色等样式，以便更清晰地看到它 */
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-align: center;
    line-height: 200px;
}