# 构造器：

![image-20241126214918839](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126214918839.png)

构造器:类似于我们c++中的构造函数，它也是一种函数

![image-20241126215456812](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126215456812.png)

![image-20241126220029361](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126220029361.png)

Alt+insert可以直接调用构造函数，提升效率

# java类定义

1.定义类时除了定义类的属性，类的方法之外还要定义类的构造器，我们至少要构造一个空构造器，定义构造器的快捷键是Alt+insert键

2.构造器的作用:在创建对象实例时，我们应该通过new关键字来调用构造函数，根据传入参数的不同会有不同的初始化效果

3.如果我们的类的属性和构造器的参数的名字一样，我们用this.参数=参数的方式来初始化(相当与没有引入指针)

```
package 类与对象;

import java.sql.SQLOutput;

public class person {
    //特性-属性-名词
    String name;//姓名
    int age;//年龄
    double height;//身高
    //特性-方法-动词
    void study()
    {
        System.out.println("学语文");
        System.out.println("学数学");
        System.out.println("学英语");
        System.out.println("学化学");
        System.out.println("学物理");
    }
    public person()//空构造器
    {
        System.out.println("调用空构造器");
    }
    public person(String n,int a,double h)//重载一个非空构造器,带多个参数
    {
        this.name=n;
        this.age=a;
        this.height=h;
    }

    public person(double height) {
        this.height = height;
    }
}

```

![image-20241126221014811](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126221014811.png)

![image-20241126221050335](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20241126221050335.png)