# 1-2 极限

（1）数列极限

![Snipaste_2024-10-25_15-10-33](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-25_15-10-33.png)

![Snipaste_2024-10-25_15-27-42](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-25_15-27-42.png)

概念理解：从某一项开始（N以后的)，a的邻域包括这之后的所有项，就是数列极限

（4）原数列有极限，其他子列都有极限

例题

![Snipaste_2024-10-25_15-31-25](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-25_15-31-25.png)



![Snipaste_2024-10-25_15-36-31](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-25_15-36-31.png)

![Snipaste_2024-10-25_15-44-19](C:\Users\<USER>\Pictures\Camera Roll\算法\Snipaste_2024-10-25_15-44-19.png)
