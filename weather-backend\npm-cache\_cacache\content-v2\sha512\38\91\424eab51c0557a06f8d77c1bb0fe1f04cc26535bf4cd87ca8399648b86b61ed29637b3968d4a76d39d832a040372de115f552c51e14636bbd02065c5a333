{"_id": "delayed-stream", "_rev": "23-fbb445ffaf4b8fd4d11e28793929c6e1", "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "delayed-stream", "description": "TBD", "version": "0.0.0", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {}, "_id": "delayed-stream@0.0.0", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "4bfe02a4a905b80aff019c3edcc53cadef03db8f", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.0.tgz", "integrity": "sha512-njaZcaax8SpmNUrrYVnabphUgJf1pO1rOXfGpIHBctHTkF68H9EyqXiZWFjtSEUjrZ8kiXG+63uftfCtovA4XA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBNZ21lHxFqH5WmE1QsBJ0v/X1nFIn1cZcjyKCp1yr27AiEA/FShvLFeq6VjMbhQcakzEtG3G4N2a9Cy1hIAGKq2gTU="}]}, "scripts": {}}, "0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "version": "0.0.1", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.8"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "_id": "delayed-stream@0.0.1", "_engineSupported": false, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "5a708417b75b3ba0aa9ac02c5f0006e252c53c24", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.1.tgz", "integrity": "sha512-X1noqInXZJ6TkZpDcd35XTGzrO/uheM6IGoo7aKTsQ2R7wGHz0iOBQYO+2p7mfKDjEhrwUcKaKIK3uJ64QpQ7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID3ByFdtR1AnLrlqBTVMtOHHC0fijtuxiRtTqWZe6QMjAiEAtOmH58KBek9epU/mHy9nUr5I5B7x4UYiynkF+gm3A/s="}]}, "scripts": {}}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "version": "0.0.2", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.0"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "_id": "delayed-stream@0.0.2", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "f122955bbbe3f930237111905801dee6b882b5f1", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.2.tgz", "integrity": "sha512-P5W63pK/yOAkj8b3rdFk/EWUQnbEe0+HoSlVRpRNbMM/sE1rweoL3zY58GSpPjhjArEVORt5Fp7n55Z5u1x6bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCodHG3xvj7k7vLQkq2LUsaegBnxyhK66DWWWJY3lQp5AIhAJPtQ7T9pP53sFjtWjwBLZPsagwv/ILxIbr9rv4hYrDY"}]}, "scripts": {}}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "version": "0.0.3", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.0"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "_id": "delayed-stream@0.0.3", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.8-pre", "_defaultsLoaded": true, "dist": {"shasum": "c504cc899c309fca4a2ac907c6b71957c5f1d272", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.3.tgz", "integrity": "sha512-TQTloJQPm6omkjiFUU2053BEp2fJBR6MvV9I8aegEGr1qYm+Edx+8bSUYLqIkJrT3Dnr+QdivfcLhxU4TiFXzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCh973x+MnmEbHptzfv4tb9E8vrrLFt+i83KcyO/cuBjwIhALj9NBYGVLzd+WNIG5SZOY8CJsL3ksEHiNl8yscBh9ut"}]}, "scripts": {}}, "0.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "version": "0.0.4", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.0"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "_id": "delayed-stream@0.0.4", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "318e307e9c061635942f414ca460cc8143ed8c08", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.4.tgz", "integrity": "sha512-Otq5+B7DSg1EcdpG3TOfYUtv9Um8YTmY77tDTGEkGz49NK9Vj7cLLXhJnQAC9SqF603HWOSjQ5cA+Vmq5C53Tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFK5vj0TXhxgE8+sUcXmNhbP+w2FUBJZRtZQVXT36oHlAiEA73tX5MwhMtMB+xVql7iT2Igo45flAtEsLB5vNNws5G0="}]}, "scripts": {}}, "0.0.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "version": "0.0.5", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.0"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "_id": "delayed-stream@0.0.5", "_engineSupported": true, "_npmVersion": "1.0.3", "_nodeVersion": "v0.4.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "d4b1f43a93e8296dfe02694f4680bc37a313c73f", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.5.tgz", "integrity": "sha512-v+7uBd1pqe5YtgPacIIbZ8HuHeLFVNe4mUEyFDXL6KiqzEykjbw+5mXZXpGFgNVasdL4jWKgaKIXrEHiynN1LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhYBNqwRVyvnvbjxnOGtAs/u1mANyCUHRCv3rzEva99gIhAMEW/V6DB64aX2OSGDsu32xUof7F5B7D0peZsuzgCp0J"}]}, "scripts": {}}, "0.0.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "version": "0.0.6", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.0"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "bugs": {"url": "https://github.com/felixge/node-delayed-stream/issues"}, "_id": "delayed-stream@0.0.6", "dist": {"shasum": "a2646cb7ec3d5d7774614670a7a65de0c173edbc", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.6.tgz", "integrity": "sha512-Si7mB08fdumvLNFddq3HQOoYf8BUxfITyZi+0RBn1sbojFm8c4gD1+3se7qVEji1uiVVLYE0Np0laaS9E+j6ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2e3H6ZAPtRCN6Rjk6PBWH9r0PWbr0I3letXYlZOF4zAIhAK2BbNwDwsRTeJlf97rfse3ZKWGA0QxeG9Kp3hkWO5XW"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "felix<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}]}, "0.0.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "license": "MIT", "version": "0.0.7", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "gitHead": "b419f294ad485b0328c4ceb94173b93ae3ab209d", "bugs": {"url": "https://github.com/felixge/node-delayed-stream/issues"}, "_id": "delayed-stream@0.0.7", "_shasum": "dfe96aad0341e033178139c9eca5a8bd41c778fc", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.6.4", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "dfe96aad0341e033178139c9eca5a8bd41c778fc", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-0.0.7.tgz", "integrity": "sha512-<PERSON>2MLughDb2lNHW38TJZ3LRM3yn8YFr9gD1FjY1OHBfB4cN0v24uIAdHqeC2uyEnMqtsvsPxMG/skLOjgDc3Tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDATHj2fecnR2JZccuMFPGxGLerdmk8TsAVGjvYitx61AiBRI/Yi0QXO/VCGVqfmjW+MYwl9rK0Kk7x0+AThcw8B7w=="}]}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}]}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "name": "delayed-stream", "description": "Buffers events from a stream until you are ready to handle them.", "license": "MIT", "version": "1.0.0", "homepage": "https://github.com/felixge/node-delayed-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "main": "./lib/delayed_stream", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "far": "0.0.1"}, "gitHead": "07a9dc99fb8f1a488160026b9ad77493f766fb84", "bugs": {"url": "https://github.com/felixge/node-delayed-stream/issues"}, "_id": "delayed-stream@1.0.0", "_shasum": "df3ae199acadfb7d440aaae0b29e2272b24ec619", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.6.4", "_npmUser": {"name": "apechimp", "email": "<EMAIL>"}, "dist": {"shasum": "df3ae199acadfb7d440aaae0b29e2272b24ec619", "tarball": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFNS104hxMHj6ggfFwKlDS+fkW7akHCxWzE/LW+sTFBqAiEAxvSeEJzWu03PAzAyNykkudaBtkUvVdsDua12o2B7hMg="}]}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "apechimp", "email": "<EMAIL>"}]}}, "maintainers": [{"email": "<EMAIL>", "name": "apechimp"}, {"email": "<EMAIL>", "name": "felix<PERSON>"}], "time": {"modified": "2022-06-14T23:48:45.969Z", "created": "2011-05-22T13:50:32.615Z", "0.0.0": "2011-05-22T13:50:34.131Z", "0.0.1": "2011-05-22T20:51:10.182Z", "0.0.2": "2011-05-22T20:54:55.117Z", "0.0.3": "2011-05-22T21:39:43.149Z", "0.0.4": "2011-05-24T08:02:37.327Z", "0.0.5": "2011-05-24T08:09:45.283Z", "0.0.6": "2014-11-23T13:53:23.249Z", "0.0.7": "2015-04-30T21:58:57.409Z", "1.0.0": "2015-04-30T22:10:29.726Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "repository": {"type": "git", "url": "git://github.com/felixge/node-delayed-stream.git"}, "readme": "# delayed-stream\n\nBuffers events from a stream until you are ready to handle them.\n\n## Installation\n\n``` bash\nnpm install delayed-stream\n```\n\n## Usage\n\nThe following example shows how to write a http echo server that delays its\nresponse by 1000 ms.\n\n``` javascript\nvar DelayedStream = require('delayed-stream');\nvar http = require('http');\n\nhttp.createServer(function(req, res) {\n  var delayed = DelayedStream.create(req);\n\n  setTimeout(function() {\n    res.writeHead(200);\n    delayed.pipe(res);\n  }, 1000);\n});\n```\n\nIf you are not using `Stream#pipe`, you can also manually release the buffered\nevents by calling `delayedStream.resume()`:\n\n``` javascript\nvar delayed = DelayedStream.create(req);\n\nsetTimeout(function() {\n  // Emit all buffered events and resume underlaying source\n  delayed.resume();\n}, 1000);\n```\n\n## Implementation\n\nIn order to use this meta stream properly, here are a few things you should\nknow about the implementation.\n\n### Event Buffering / Proxying\n\nAll events of the `source` stream are hijacked by overwriting the `source.emit`\nmethod. Until node implements a catch-all event listener, this is the only way.\n\nHowever, delayed-stream still continues to emit all events it captures on the\n`source`, regardless of whether you have released the delayed stream yet or\nnot.\n\nUpon creation, delayed-stream captures all `source` events and stores them in\nan internal event buffer. Once `delayedStream.release()` is called, all\nbuffered events are emitted on the `delayedStream`, and the event buffer is\ncleared. After that, delayed-stream merely acts as a proxy for the underlaying\nsource.\n\n### Error handling\n\nError events on `source` are buffered / proxied just like any other events.\nHowever, `delayedStream.create` attaches a no-op `'error'` listener to the\n`source`. This way you only have to handle errors on the `delayedStream`\nobject, rather than in two places.\n\n### Buffer limits\n\ndelayed-stream provides a `maxDataSize` property that can be used to limit\nthe amount of data being buffered. In order to protect you from bad `source`\nstreams that don't react to `source.pause()`, this feature is enabled by\ndefault.\n\n## API\n\n### DelayedStream.create(source, [options])\n\nReturns a new `delayedStream`. Available options are:\n\n* `pauseStream`\n* `maxDataSize`\n\nThe description for those properties can be found below.\n\n### delayedStream.source\n\nThe `source` stream managed by this object. This is useful if you are\npassing your `delayedStream` around, and you still want to access properties\non the `source` object.\n\n### delayedStream.pauseStream = true\n\nWhether to pause the underlaying `source` when calling\n`DelayedStream.create()`. Modifying this property afterwards has no effect.\n\n### delayedStream.maxDataSize = 1024 * 1024\n\nThe amount of data to buffer before emitting an `error`.\n\nIf the underlaying source is emitting `Buffer` objects, the `maxDataSize`\nrefers to bytes.\n\nIf the underlaying source is emitting JavaScript strings, the size refers to\ncharacters.\n\nIf you know what you are doing, you can set this property to `Infinity` to\ndisable this feature. You can also modify this property during runtime.\n\n### delayedStream.dataSize = 0\n\nThe amount of data buffered so far.\n\n### delayedStream.readable\n\nAn ECMA5 getter that returns the value of `source.readable`.\n\n### delayedStream.resume()\n\nIf the `delayedStream` has not been released so far, `delayedStream.release()`\nis called.\n\nIn either case, `source.resume()` is called.\n\n### delayedStream.pause()\n\nCalls `source.pause()`.\n\n### delayedStream.pipe(dest)\n\nCalls `delayedStream.resume()` and then proxies the arguments to `source.pipe`.\n\n### delayedStream.release()\n\nEmits and clears all events that have been buffered up so far. This does not\nresume the underlaying source, use `delayedStream.resume()` instead.\n\n## License\n\ndelayed-stream is licensed under the MIT license.\n", "homepage": "https://github.com/felixge/node-delayed-stream", "bugs": {"url": "https://github.com/felixge/node-delayed-stream/issues"}, "readmeFilename": "Readme.md", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "users": {"mojaray2k": true, "ganeshkbhat": true}}