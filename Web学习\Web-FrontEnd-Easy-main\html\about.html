<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于</title>
    <link rel="stylesheet" href="../css/nav.css">
    <link rel="stylesheet" href="../css/body.css">
    <link rel="stylesheet" href="../css/about.css">
</head>
<body>
    <nav class="homenav">
        <div class="navtitle"><strong>Kaltist</strong>|MY PROFILE</div>
        <a href="home.html">主页</a>
        <a href="about.html">个人简介</a>
        <a href="blog.html">BLOG</a>
          <a href="join.html">了解更多</a>
        <div class="navanim about"></div>
        <div class="none"></div>
    </nav>

    <div class="mid">
        <div class="headimg">
            <h1>About Me</h1>
        </div>
        <div class="ab out1">
            <div class="txt">
                <h2>关于我</h2>
                <p>郑州大学计算机科学与技术大二学生</p>
                <p>兴趣广泛，什么都看，什么都玩，喜欢体验不同世界观</p>
            </div>
            <div class="imghid">
                <div class="img"></div>
            </div>
        </div>
        <div class="ab out2">
            <div class="imghid">
                <div class="img"></div>
            </div>
            <div class="txt">
                <h2>关于网站</h2>
                <p>该网站由HTML，CSS，JS完成，是很好的前端基本功练习demo <br></p>
                <p>后续考虑通过vue进行重构并衔接简单后端</p>
            </div>
        </div>
        <div class="ab out3">
            <div class="txt">
                <h2>步步为营，稳扎稳打</h2>
                <h2>扎营渐进，蚕食河南</h2>
            </div>
            <div class="imghid">
                <div class="img"></div>
            </div>
        </div>
    </div>

    <footer class="foot">
        <p>2024 | Kaltist的网站</p>
    </footer>
</body>
</html>