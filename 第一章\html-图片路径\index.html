<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <p>这是一个绝对路径的运用，直接深入到盘符里面到文件名</p>
    <img src="F:\1.jpg" alt="这是一个云岚小姐姐">
    <p>相对路径即表示当前文件相对的位置,若在同一目录下(即属于同级关系)直接输入名字即可运用</p>
    <img src="3.jpg" alt="">
    <p>其他相对路径,同级用"./或者不写"来索引,子级用"/"来索引,也即"/"访问下一级,"../"访问上一级</p>
    <img src="./images/1.jpg" alt="">
    <p>"../"返回到父级关系</p>
    <img src="../html-图片路径/3.jpg" alt="">
    <p>网络地址就是网络服务器中访问的</p>
    <img src="https://pic.3gbizhi.com/uploadmark/20231024/995f4848795b8d86b4f71733e8cdbbff.jpg" alt="">
</body>
</html>