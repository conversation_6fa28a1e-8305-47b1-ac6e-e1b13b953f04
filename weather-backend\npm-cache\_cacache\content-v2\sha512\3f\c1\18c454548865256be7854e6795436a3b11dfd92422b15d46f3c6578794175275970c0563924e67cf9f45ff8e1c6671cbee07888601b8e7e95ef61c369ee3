{"_id": "proxy-addr", "_rev": "77-41e1164cbb766978c9fa0fb635769e74", "name": "proxy-addr", "dist-tags": {"latest": "2.0.7"}, "versions": {"0.0.0": {"name": "proxy-addr", "version": "0.0.0", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/proxy-ip", "bugs": {"url": "https://github.com/expressjs/proxy-ip/issues"}, "dist": {"shasum": "37ab96289d7a98de73b9e485141638c9c9971c49", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-0.0.0.tgz", "integrity": "sha512-RP0kkDy5Jj1/7z+oi6VmIJ+efum96EDGxsUVr7R9kxEqjiKo+QUx7+lfrvXtPpTD8rwo0LbueLUM3Ck0OozhxA==", "signatures": [{"sig": "MEUCIG/7NRFFtdMXWJf0lBCPvcJpcC62VTJAx2BPBYu5mDhtAiEA1Bh5ItLSR+Q/Wvj/KKPQwq8AbTOGY3OCD2qwRtP4zVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/proxy-ip.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"ip": "0.3.0"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1"}}, "0.0.1": {"name": "proxy-addr", "version": "0.0.1", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/proxy-ip", "bugs": {"url": "https://github.com/expressjs/proxy-ip/issues"}, "dist": {"shasum": "452212b85e83fbca3d5ad80c7316620a3bd36cc3", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-0.0.1.tgz", "integrity": "sha512-+Po4xQ+ciK+J/betkLRaag3sA8MN5C5kpByyCPGBiDXpzdYRs2XhUHhkY07NTWl4ldb+zAxqbnfrCo0f6r/2HA==", "signatures": [{"sig": "MEQCIGJjRVBWo5CKbY64O6vMw5hjp1zrrai3RcS4ptRzupOPAiBGFdohOxLYalr6OInTE8lI7yz1V9GywulJKby0BGsIZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/proxy-ip.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Determine address of proxied request", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1"}}, "1.0.0": {"name": "proxy-addr", "version": "1.0.0", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/proxy-ip", "bugs": {"url": "https://github.com/expressjs/proxy-ip/issues"}, "dist": {"shasum": "478617ab0fba70e0c3dae9cf57469e36dd2febaf", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.0.tgz", "integrity": "sha512-3DpYuuyvGtMQnKGZndrSCLGOEOg7lpNbVmI+Wtpn/3WKjaCFN3M+wRNiMnTGQD2rtTuBd8qXHmdoEjU6PLl68A==", "signatures": [{"sig": "MEUCIBQtYHHq8fw6+HYukvj0YBvabS4gtdShRzL1X51BXvKhAiEArMZ1JyHe6r5Re9i9UaFCBcLUay1v+1jiXS9Qi8VdcvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/proxy-ip.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"ipaddr.js": "0.1.2"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1"}}, "1.0.1": {"name": "proxy-addr", "version": "1.0.1", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/proxy-addr", "bugs": {"url": "https://github.com/expressjs/proxy-addr/issues"}, "dist": {"shasum": "c7c566d5eb4e3fad67eeb9c77c5558ccc39b88a8", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.1.tgz", "integrity": "sha512-rIUGzBlSfkJMWWCgsd4N5wvVSNAcJZg//UwPZumDIbScHRUzuSOjBmIdyICiKkB9yArv+er9qC6RA/NL3AWc6A==", "signatures": [{"sig": "MEUCIH5ZDY1nlrMRWdqqilB5Y94CZCBERZeQu4f9r9Cdf33yAiEA6+bZbQNAMgdiSpa2RrUyArCGn3Hd9Oqtd7h9znR+b00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/proxy-addr.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"ipaddr.js": "0.1.2"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.2": {"name": "proxy-addr", "version": "1.0.2", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "b322f905aa4f4bd3ce60550295eabbbb07c92143", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.2.tgz", "integrity": "sha512-wI084pCBCfbxn9rBKVpkvA4+uU9zj6bh/3KIeJm8JzpLDQ003FETjrDKeGuz3wJTG3xFK7vP+QeFUvvYDAL+Eg==", "signatures": [{"sig": "MEUCIQCv2YRuLJAJxQU5fVAL+3DAGCZ3rCYPpVYt1MPCLG8lYAIgcBtju3f346rDJCIS6syh0/0OCqUjI1QkfIBdbwnf7JQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "b322f905aa4f4bd3ce60550295eabbbb07c92143", "engines": {"node": ">= 0.6"}, "gitHead": "63a0f679bd4b074b7391fa464cb779275f24b1da", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.21", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"ipaddr.js": "0.1.3"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.0", "istanbul": "0.3.2", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.3": {"name": "proxy-addr", "version": "1.0.3", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "17d824aac844707441249da6d1ea5e889007cdd6", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.3.tgz", "integrity": "sha512-DQOzOm7QSLOXlXnznm4qWTgJd+uX1OauOM/nNAghevQuyOgRbP2aPdx3tPdYxRmIMZ+ApeYtIu0zXJ/823EfQw==", "signatures": [{"sig": "MEYCIQCFeJ3zE6TP0QGaCUhOdcXoYxjYIXbQSixjh1aeRE/atQIhAM6JXVLZORp9MiY5wcyulYfrSinFPT7DvHuFDO7By5Hq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "17d824aac844707441249da6d1ea5e889007cdd6", "engines": {"node": ">= 0.6"}, "gitHead": "0a9006a64de938d879f1e4b38b275c1c46524dfd", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.21", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "0.1.3"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.0", "istanbul": "0.3.2", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.4": {"name": "proxy-addr", "version": "1.0.4", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "51dbebbb22cc0eb04b77a76d871b75970f198cdd", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.4.tgz", "integrity": "sha512-We+BF6Vv1H8VwiiEqNPNO5BKtEEsl+H7qomDLntJQagAdErK7fAFR7VCJqvr1GEt8mADW1gpTp8I6BFWK+dbsg==", "signatures": [{"sig": "MEUCIQDQCJ+gSpAJ9PsVuwPo35fR+vP4Vb1UhSDvbJosHIccjAIgVashwYXt0KzCsu5vWCbmVGGJ3eu1eVxYLqkIK3bysPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "51dbebbb22cc0eb04b77a76d871b75970f198cdd", "engines": {"node": ">= 0.6"}, "gitHead": "f72c40adfeeb81fa905f017030d2ecd1cd4d1821", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.21", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "0.1.5"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.2", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.5": {"name": "proxy-addr", "version": "1.0.5", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "17ad518b637a21a64746319f39fbc72c8628f63b", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.5.tgz", "integrity": "sha512-0LgR6ijhrduw3LYAxslq4y3Di03TROao+NuzUve4PTDWn7OhfwPHgDcg3wpz2zg8gjAVfVer9SREUkU1U4Xzsw==", "signatures": [{"sig": "MEUCIA1ZTrU4xGpZy6ddHl97Ag02IwPatq0i5PDZQvwMm34EAiEA/B/txQYvAYqp25ml1ksBjxZY3VVwHa/JeS3by/pPPCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "17ad518b637a21a64746319f39fbc72c8628f63b", "engines": {"node": ">= 0.6"}, "gitHead": "dd768c48b99fb65b2b753269b8eea675e86da3fd", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.28", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "0.1.6"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.6": {"name": "proxy-addr", "version": "1.0.6", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "fce3a4c486bf2e188ad1e76e18399a79d02c0e72", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.6.tgz", "integrity": "sha512-V42q0bScEFrrOZEDlMleZ+OxZnRvQgNblNZa5P+DLUpj42EkagGUnNwibO0bkR5KI3EdCpaestFDECKoxRCAqQ==", "signatures": [{"sig": "MEUCIQDhPYiPkgRa7PRAhc9znXyXNidU7J3Nc4ITiqegdNIFRgIgQPzmxo1i+aXXDkANRJJcLDfIVjDIvlk3ZFhUEB22GAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "fce3a4c486bf2e188ad1e76e18399a79d02c0e72", "engines": {"node": ">= 0.6"}, "gitHead": "550cade433f7a7d7cbcdebbd0f9e1cb94aed5e26", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.28", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "0.1.8"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.7": {"name": "proxy-addr", "version": "1.0.7", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "6e2655aa9c56b014f09734a7e6d558cc77751939", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.7.tgz", "integrity": "sha512-aNModyD7D743SgXhftJ6fICYBeWSDpLj0pmfh+0H38QjFWWCAGjfUiF5JtJDappTsGpTO0GNmIO5TcS9/1RbUg==", "signatures": [{"sig": "MEYCIQDunXxlKiCjFhMA6qQNaXNo3TyhRWg6RcqrFT1ezhQR0QIhALTtGbW4t26gqv1Ceb+h0y03XtohsHttEO+F7jo+Nkow", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "6e2655aa9c56b014f09734a7e6d558cc77751939", "engines": {"node": ">= 0.6"}, "gitHead": "917fa69ae1a4c3e2962d89461b6945538b763b28", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.28", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "0.1.9"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.8", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.8": {"name": "proxy-addr", "version": "1.0.8", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "db54ec878bcc1053d57646609219b3715678bafe", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.8.tgz", "integrity": "sha512-/FO23Z4yT7Qvhh3/cPeK2WqH1nkE2pP2522hPGLvVDXneKE8dLluv7zQ+Sipk4QJ5twIwtb1W1rx623+gYdNDQ==", "signatures": [{"sig": "MEUCIF45jnr1/kHI8f1R2JCtsFFqu+FEO4nd8rElb72RVW8XAiEAw+4BqdbXwuuJa42+8bCww6N4dZxGoLsjaGR36eLhyu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "db54ec878bcc1053d57646609219b3715678bafe", "engines": {"node": ">= 0.6"}, "gitHead": "b32d9bda51c92f67a5c2c7b4f81971dbef41783c", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.28", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.0.1"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.9", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.9": {"name": "proxy-addr", "version": "1.0.9", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "8ac877a230f80f10bf9e5bf42584cde87bd219a6", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.9.tgz", "integrity": "sha512-feyw4ZckAWemoTgYMWM85p2beWtMs56qH4YUfUCqUZ/fSQfPaFZglUsiXOELYsv8XguUWd++reToKU7ndrtFTw==", "signatures": [{"sig": "MEUCIDT5pFvKocqKp61WLJsVhzGdbBQp6fOcN2I4ulW+bcvmAiEAm9+SHAl2HCPDTEsev/iI4z2eGsxQ5y14RVkksiaoPdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "8ac877a230f80f10bf9e5bf42584cde87bd219a6", "engines": {"node": ">= 0.6"}, "gitHead": "3941f1bbfb34b746b0dc01d8563c56b2a3464789", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.28", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.0.4"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.1", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.0.10": {"name": "proxy-addr", "version": "1.0.10", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.0.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "0d40a82f801fc355567d2ecb65efe3f077f121c5", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.0.10.tgz", "integrity": "sha512-iq6kR9KN32aFvXjDyC8nIrm203AHeIBPjL6dpaHgSdbpTO8KoPlD0xG92xwwtkCL9+yt1LE5VwpEk43TyP38Dg==", "signatures": [{"sig": "MEUCIQDEfcXtxMHsuRJqQ9NBFklaopg5JA3gYSpG75rTVeP0dgIgb02gBmeE3xVjAUOP3paAoI8SrpeFJ/mX+hJb4vVDJTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "0d40a82f801fc355567d2ecb65efe3f077f121c5", "engines": {"node": ">= 0.6"}, "gitHead": "0cdb6444100a7930285ed2555d0c3c687690a7a5", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/proxy-addr", "type": "git"}, "_npmVersion": "1.4.28", "description": "Determine address of proxied request", "directories": {}, "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.0.5"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.1", "benchmark": "1.0.0", "beautify-benchmark": "0.2.4"}}, "1.1.0": {"name": "proxy-addr", "version": "1.1.0", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "dbbd6aa8c37108889193a37d92e78fd3da6d1a2d", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.1.0.tgz", "integrity": "sha512-LKsBLmK5ugJRw6aC9q8MMXj7lLZgIBH1x2d9SqAFj4oCAg/A6y/oXtXmUMGRv9TCQyvRX1gPppL/uDwYNerSvg==", "signatures": [{"sig": "MEYCIQD6FOBpZZdFGr4SF3HOaklKduLa5Yd9+Q70o0vXCh3EugIhAJqLQER0r8i/MWvTTO+kXdneE51/1SCK4+c77V+zS1MP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "dbbd6aa8c37108889193a37d92e78fd3da6d1a2d", "engines": {"node": ">= 0.6"}, "gitHead": "78d203e77642698359390ef4fb03028ed5437a7e", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.1.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.3", "benchmark": "2.1.0", "beautify-benchmark": "0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-1.1.0.tgz_1462170012661_0.9743298299144953", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.1": {"name": "proxy-addr", "version": "1.1.1", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "cfc323b3c0f55ca0df72d820f6e8836cd4507e2f", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.1.1.tgz", "integrity": "sha512-Zpf3blhne6TFG9fjMPJ703cxTZBjc0juYnT9yS0ArV/xCTyixCSzUmBkdjQaduuRrOyz0DC5MIYW2WgQYMRAsQ==", "signatures": [{"sig": "MEQCIA11LylepfohbJM7+cTiAj2NFGZVmJV1i050FKadWq87AiBRX+0UWRZzzJfOy/tulDwerkSwIBl8IN8LMsUqsf1bRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "cfc323b3c0f55ca0df72d820f6e8836cd4507e2f", "engines": {"node": ">= 0.6"}, "gitHead": "fc4ee6765b6fbde5600be60d45bb5585424899a5", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.1.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.3", "benchmark": "2.1.0", "beautify-benchmark": "0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-1.1.1.tgz_1462335155814_0.404950185213238", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.2": {"name": "proxy-addr", "version": "1.1.2", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "b4cc5f22610d9535824c123aef9d3cf73c40ba37", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.1.2.tgz", "integrity": "sha512-iKa3eD8bwiIq2neI5Lo2YvwouR0Sak3BYK8A9Z6QWqniWsIhpy4AA22e1oEMarbHloV2xrnVlY26lvaJWbuQow==", "signatures": [{"sig": "MEQCIA6vrXPJlwoOTtU/IAixlrg3J5eVwR00M22jn60IRk5DAiAKo3EIuH040baYTdkd5ekFd6aOTegL6r8ngB06puJQfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "b4cc5f22610d9535824c123aef9d3cf73c40ba37", "engines": {"node": ">= 0.6"}, "gitHead": "28c34525632884a6d5e69a9165d7420b3f972d8b", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.1.1"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.3", "benchmark": "2.1.0", "beautify-benchmark": "0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-1.1.2.tgz_1464573376704_0.6896329398732632", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.3": {"name": "proxy-addr", "version": "1.1.3", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "dc97502f5722e888467b3fa2297a7b1ff47df074", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.1.3.tgz", "integrity": "sha512-DqElUqAHcWG3dnqlR57Jnvz4exDZ7wsTOLwYaXNPtiOl9l5vyjtD6+7s3nAT1QknFLJsq4C6QzhCogM05QoGNA==", "signatures": [{"sig": "MEUCIQCn9/Cc80QKdIEwAVvU9F07t63bYiOaJlTwaVEcAdC4cwIgJfqR97i4ptbwNkO3TdS58D4FpOOucPp18lw4YicnIVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "dc97502f5722e888467b3fa2297a7b1ff47df074", "engines": {"node": ">= 0.6"}, "gitHead": "0724490937983255e7687812594c97c36ebca90b", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.2.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.5", "benchmark": "2.1.3", "beautify-benchmark": "0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-1.1.3.tgz_1484460061440_0.03347301739268005", "host": "packages-18-east.internal.npmjs.com"}}, "1.1.4": {"name": "proxy-addr", "version": "1.1.4", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "27e545f6960a44a627d9b44467e35c1b6b4ce2f3", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.1.4.tgz", "integrity": "sha512-G/noElq/twFBfS4WnyIyGwJgBeUTmsiMnpy1H8jRsBBcP0vn9ncxIZKA2PkK5guEUbb+98BduZnrupe1AN3ziw==", "signatures": [{"sig": "MEUCIFNvmNE/V+F5GJsfxJkeAshASdc1rUcChd5NZOdK47ySAiEA4ob2Dc+h3Cc5Q42e3gAU2u+IDugFSbXIP3anOzZGrw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "27e545f6960a44a627d9b44467e35c1b6b4ce2f3", "engines": {"node": ">= 0.6"}, "gitHead": "4c636264c036d9825e8a3cf50555a272e3246fe6", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.5", "benchmark": "2.1.3", "beautify-benchmark": "0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-1.1.4.tgz_1490396252699_0.9566438721958548", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.5": {"name": "proxy-addr", "version": "1.1.5", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@1.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "71c0ee3b102de3f202f3b64f608d173fcba1a918", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-1.1.5.tgz", "integrity": "sha512-av1MQ5vwTiMICwU75KSf/vJ6a+AXP0MtP+aYBqm2RFlire7BP6sWlfOLc8+6wIQrywycqSpJWm5zNkYFkRARWA==", "signatures": [{"sig": "MEQCID6DXrrWGC90ge5uw0D6Gulw0xIZzFEF83/mZNm9KCFiAiATXehB6y+xKmoGnSSgYxsA4vfLE5WmzEDSVibMcNbXTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "71c0ee3b102de3f202f3b64f608d173fcba1a918", "engines": {"node": ">= 0.6"}, "gitHead": "f40ceab074ec2f92399d112793d9ad1c9d96e146", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.4.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.5", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-1.1.5.tgz_1501030838206_0.9159589342307299", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "proxy-addr", "version": "2.0.0", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "f816044dcce8b830d4b43809705be3637cbb3c4a", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.0.tgz", "integrity": "sha512-YgNn8vg3bnNTTANKor3DXPl5khodlFKOGSyg2Wss5Lp6B76CPiQPsRR+RC0Jz+tntPKuJyvzuiTEKYkdagn7ag==", "signatures": [{"sig": "MEQCIApmda3WnnWeZOvKQtchIb0iFtcpG1iKiVBnH58kTiU8AiB3ch9sbvbZiLByhlxuI++b+EPEfVO8yBJmX5rMx0iv1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.10"}, "gitHead": "2fd80a9bf9152854e94ce36b47f8078aa72145fd", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "6.11.2", "dependencies": {"forwarded": "~0.1.0", "ipaddr.js": "1.4.0"}, "devDependencies": {"nyc": "10.3.2", "mocha": "3.5.0", "eslint": "3.19.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-2.0.0.tgz_1502247616459_0.6272426785435528", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "proxy-addr", "version": "2.0.1", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "a6f82fdbcaf6fea35d635b0be0997b79a8feef66", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.1.tgz", "integrity": "sha512-Dn79xjqI6AhrhewWR74oiJEnUU/R+9Vr/5N1GUdTipyGySMQmUBo9y8cZ0RbDD8k5ZCQfo2DS7eIbW1fBorENQ==", "signatures": [{"sig": "MEUCIQD7BfCCCsa96+6RHda9uQZ0TFcHx8GFA92rm+6aJ+OJGgIgUSGwKwyw6Us5hU852AsxCaUJe1cRbOnzqZzXY0sJwJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "a6f82fdbcaf6fea35d635b0be0997b79a8feef66", "engines": {"node": ">= 0.10"}, "gitHead": "461898d3c6f950fe954ecd9121ec63cfcd88cd28", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"forwarded": "~0.1.1", "ipaddr.js": "1.5.2"}, "devDependencies": {"nyc": "10.3.2", "mocha": "3.5.2", "eslint": "3.19.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-2.0.1.tgz_1505092292777_0.8947560833767056", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "proxy-addr", "version": "2.0.2", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@2.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "6571504f47bb988ec8180253f85dd7e14952bdec", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.2.tgz", "integrity": "sha512-KUZAIYMW9tc/GDAILd4uQ43IGv/3rbfMHyl7SIQOmXWz+q0q1dV5ZSBCszGxxa2R7wrSElcraWFySSamShbxZQ==", "signatures": [{"sig": "MEYCIQCEYVglO/IqYQ8hm4KHc0IrUWXPwsthkVh0CBH8akPj2AIhAMMtbvWMbTNIBpHQXSoItXuxcXP3S4Fq33pFxSZ3LByh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "6571504f47bb988ec8180253f85dd7e14952bdec", "engines": {"node": ">= 0.10"}, "gitHead": "7c1bc4c5c05bd5285af710baabf87421d950f689", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.5.2"}, "devDependencies": {"nyc": "10.3.2", "mocha": "3.5.3", "eslint": "3.19.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr-2.0.2.tgz_1506303664796_0.10817809496074915", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "proxy-addr", "version": "2.0.3", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@2.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "355f262505a621646b3130a728eb647e22055341", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.3.tgz", "fileCount": 5, "integrity": "sha512-jQTChiCJteusULxjBp8+jftSQE5Obdl3k4cnmLA6WXtK6XFuWRnvVL7aCiBqaLPM8c4ph0S4tKna8XvmIwEnXQ==", "signatures": [{"sig": "MEUCIGps3Tl+8OJbmzRjIsenFj4NbSGAt0ia9jXMFMJA/iibAiEAzMVgEX6Rm1h455MMVqCJBDwbOz2mbZOPPEztJy0FlwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15421}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.10"}, "gitHead": "d841ab7a2150963793ad2dc121df014f426f2964", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "6.13.0", "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "10.3.2", "mocha": "3.5.3", "eslint": "4.18.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "6.0.0", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr_2.0.3_1519101976942_0.28350766039209363", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "proxy-addr", "version": "2.0.4", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@2.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "ecfc733bf22ff8c6f407fa275327b9ab67e48b93", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.4.tgz", "fileCount": 5, "integrity": "sha512-5erio2h9jp5CHGwcybmxmVqHmnCBZeewlfJ0pex+UW7Qny7OOZXTtH56TGNyBizkgiOwhJtMKrVzDTeKcySZwA==", "signatures": [{"sig": "MEYCIQC4RhMfI6Co9LMCklyrk2Z9NPKpxFV6O13HrMB1hXGr+QIhAPeTC5K9nQUviktpMPatdUvekz55YaVAclMdUzi4qrom", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWhQfCRA9TVsSAnZWagAAFskP/iUjllmYavgjxF64jpeM\nraRtpxz4dYvgZKb0Shf45w/ljvtBz5yo5y6B+jKYleSwjO5pX68VK4yfor0C\nJ2o86btV0NvLjNxBduVw3kTDW+kOPPpmQKE4BrcpYN9otM/ZpBCN6BWhA4Gs\nyFb8U++i/ZbF2S+Yj3vCCcRCo6SYmALTm6jQb9hAoV5nlAJq2rLutZrrh5H5\ntrQkblvVZaepYn+CV8Kb6VpHJW0JrrFN5lqMBlSEWVOTfDPTthgoL9E2WzAQ\nJuOkHpUiccObb+8fO2UpYXvfuSQdkTGSQ1lVkNIIdXfVUepGuUVzkVoHq4Bc\nqIlo5pjXBuvx53B1/aPwozHVqaxB/0j56whqmCyp83fazPJrZCqjeYq1sUP0\nrT7sZLL/5cpzwZMKAsso3KGdwNyK0tQytutKq57Z/STENFu7QocY4m/T03KM\nDVdERzPRotEWuFzIoG77KqX1Cb/kmfi2O7YI4XwBWM9MmQ1Ru5/bHkyUErkC\nbMM+6A1oa8j+J8udfHxvLs84O4blHZeY0Lx5lkMpV8N1CNRDe4nB2d1fAj1M\nAkbA2G8ykH6lNnlyFUlw8Vse8I+JvZKb5JTw+FFUqMsgTMX9pQzR+F2yunJk\nlF8cwLVVH5QHBPlm9tp2AUjUD/NeRg3ozOnOrtne7muuFSsTOi+L7+cbdrue\nG1Oi\r\n=AGmG\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.10"}, "gitHead": "0942626d371d6d4e4cd5c59f4be7e55c81efd357", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "10.3.2", "mocha": "3.5.3", "eslint": "4.19.1", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.13.0", "eslint-plugin-promise": "3.8.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr_2.0.4_1532630047681_0.018082968185300086", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "proxy-addr", "version": "2.0.5", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@2.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "34cbd64a2d81f4b1fd21e76f9f06c8a45299ee34", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.5.tgz", "fileCount": 5, "integrity": "sha512-t/7RxHXPH6cJtP0pRG6smSr9QJidhB+3kXu0KgXnbGYMgzEnUxRQ4/LDdfOwZEMyIh3/xHb8PX3t+lfL9z+YVQ==", "signatures": [{"sig": "MEUCIFXEZ7CX/QLzt5NdflhJv68qF5bi8urgBt33s0z1LCyBAiEA/Q85alDhm0bhcEK2lr8pTuzJEokuNPaCS9uUKhEjB8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctgL5CRA9TVsSAnZWagAA/YQP/2wTt4ACbgaN+8tJspqr\nIAPCxN9E559sCCjGf3fneSCUOl0IvxT32Ao9Ue8vYyWyCjf+YIBbp1TO9ZAL\nJc54x/0mKjozz0pKideKOaUGv5R8BLDsxH+bWhXNkcgROj0TDYpUHmDVH89n\n/0/7auhigu++wbh8Fhcq7bciUhfCeajUprc1zjeqvTfSnDywl4ZhgM1EKvcL\nddXoczeIXiXgf1yC5ZNqVZSl8umaVbCC9MUnW0/zRWmzFN4sFPa5cHfMdiN0\nPK4cKqvrzu7RFQQSRKbp8DhI87/RS/r84AoJWSNoHWFfzYNZLArdqf0Ry5Cm\n2zmTlzfKyeLeK5kyCBmzc6JRlaseMDb0S8owIxDM0rIcWzyYsfrTTc4f6yNF\naFaPU/cPdYYuzdFnV8HelkO+04/NCVMl21oHuBhdL20mrWi4g03lfWPnovE4\nI8p4MTaxAlbDTmYOcpevz3Dytl5m2xE4zu0kRrC9j35r9Yrr1Xf3tTphi8bF\nIBE53bbY4Yq79AM17O8WiKETPI+bx398e++5PBFB/6m/tzU1lsRE61GNqM90\nGxUqmc/vG+xIPZUYPvGIhJ+l0VIDrcCIKWdvVQTYinN6nmTnQZwNLMpuRDWH\nL6IxtUF7Bq5hPku9BMa58UhqkBdJ7dIC12+NO9/XGgA80nHaURTNvAXWOxMY\nssnp\r\n=fWr9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}, "gitHead": "6dec756fafa35a2666e0f298a82ea6b1ac504f52", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "8.15.1", "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.3.0", "mocha": "6.1.3", "eslint": "5.16.0", "benchmark": "2.1.4", "deep-equal": "1.0.1", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.1", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr_2.0.5_1555432184327_0.9083476593014095", "host": "s3://npm-registry-packages"}}, "2.0.6": {"name": "proxy-addr", "version": "2.0.6", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@2.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "fdc2336505447d3f2f2c638ed272caf614bbb2bf", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.6.tgz", "fileCount": 5, "integrity": "sha512-dh/frvCBVmSsDYzw6n926jv974gddhkFPfiN8hPOi30Wax25QZyZEGveluCgliBnqmuM+UJmBErbAUFIoDbjOw==", "signatures": [{"sig": "MEQCIG7MvF6XJry23vEuATSjw9rxuNIkqMgFJQ1KXdf8U+kdAiA31uxt1JtdKdJONqrcmyBzdOzoAmFY819ATUj/N+0rQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeU3j6CRA9TVsSAnZWagAAdk8P/3dpsRRiz8JVDo5LtJXD\nTPfDdmnVCDglO77aWEiW7Q0EJVj1Vb8IINxXJTeXbRMYtHokWpulc8Qd9ucl\nxaJc1CkZscZ3UxxauTkuIQXcdq25htpwOpS12fhV1MsHpIIrDoZ1sa6pUyTj\nJz3O6x5dWPY3KRjaMrl2UBFVvqc7VbDEZrwgRUJ2D04nW3xN3mWQDPU/WqBA\nFtslR3qUR5zNlNsdkLQrmMd+HZ1IiOdmZ2l9mJAiHoKgDBmhASB5Zra3dXuO\nAhDuDd9CHZ1Mvw2NgaugIN3/EBw7KDBqqsZTb7EbZEoZJF+MBTlRvsRUERP5\nkYXrGuxTklRRwrmSC3I9730P17JsySsTI9Nj/NPfTtZwIwL1Pz4jrJ/F3hiS\n3c2WrnWmXGNKwRV6gXK+ik4QgES7LtwmSh7TAv5Juka7aR1jmCs/5FRUezIW\nwNCKTj7KqN8v0xlPKu3gsfymMrmbKngmmA9BfH9xWjmno+s2tHX8Bh0C6jUq\nFUT9eaTrBFCAil64wIcFFgHrljtsRn47+naw8vexCARKZL9rf1ZAaqY5PXps\nhVQcRXieZyljiEt0sG260M4rZhXrBTPdao9UnoEypYRMANZzlc3D7ae4spgy\nS0aA0P63G6TMsQqAr58q1Xqe5S8QoWW/wkUvcDAK/EZj9ApfGK0CRHg0IoNL\nY6Br\r\n=Ww5y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}, "gitHead": "9f78739c5333ebea49442235ce720f1d37605706", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "nyc --reporter=text npm test", "test-travis": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "13.8.0", "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.9.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.0.0", "mocha": "7.0.1", "eslint": "6.8.0", "benchmark": "2.1.4", "deep-equal": "1.0.1", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "11.0.0", "eslint-plugin-import": "2.20.1", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.0", "eslint-plugin-markdown": "1.0.1", "eslint-plugin-standard": "4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr_2.0.6_1582528762078_0.9304045391970575", "host": "s3://npm-registry-packages"}}, "2.0.7": {"name": "proxy-addr", "version": "2.0.7", "keywords": ["ip", "proxy", "x-forwarded-for"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-addr@2.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/proxy-addr#readme", "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "dist": {"shasum": "f19fe69ceab311eeb94b42e70e8c2070f9ba1025", "tarball": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "fileCount": 5, "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "signatures": [{"sig": "MEQCIHHghL6ucMSlP+cipw7nxvRd7xqa+/2wL3Rt0yiz+abUAiB7tf4oWxGxlUnTx+0xhymgXUXGPGhHC66azpYs2T787A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtYX4CRA9TVsSAnZWagAAwJkP/28zIDWqk0YLDQSNIfDd\nUecLG6NXXv+HmWQlZpotbQwV3wMX8HQDVPV6n23xVlQDICmd238+or07W1jG\nq7/XkQOfu8BILZZExCs7GseWmiNW1iVXWmh1VdER0YM5OYBP3054qrIOrjeg\nEYwGwaMQPcu15mSsJ31Zmziarwj4wRww2bgg7qviF81h+7Ny2kmdKWMQFDHW\nS3T2ogn5Ln/es1nilHPzIKJba4hRr3dtljfyZnQuV2morAdf4gn8k0X60JUY\npET6ggjHrGHHDYL9hjs33kxI952zl+1W64ZZu+3E/imA064fCGUJk/+FFFCv\nMW9hWMqKyAqQtsnVH2e4wsMeGa3WIH0GyMR0O1ro6eVa3FDAz35tGluTmoNf\nedkKX1cY+WTw+obV10KmUlIGzTagw0e6YJ8vDzmprlozXb/eyqAba+i2rqsN\ny1sqTPQZbzBZW9WOcvwDBISwZonHzxlMUZTcHU72HUn4/HmS48u1BTUKpTP7\nbV8BT6ONikU80Imx3fEaEy7Trn+aPACGrHgDh3ReRo16Po0jALYLQxPJyi2k\neAnpFAq35k6boKIRYMP7s/H7sjEEbJZDsYE2zXQjcHXEPU8LSequ3RnbvD+y\n9lKRcQjfzc+CaotwhJfB0P48t7YRwFdtvqpX3VnrlQpw8vSM9Dyb7yllJ/kj\nUlYV\r\n=shR7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}, "gitHead": "1cdd2f78e0fcc23ceae4723e6b837345c2499772", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Determine address of proxied request", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "8.4.0", "eslint": "7.26.0", "benchmark": "2.1.4", "deep-equal": "1.0.1", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.23.4", "eslint-plugin-promise": "4.3.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.0", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-addr_2.0.7_1622509048350_0.7648840093614298", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-05-05T02:38:49.512Z", "modified": "2025-05-14T14:56:25.085Z", "0.0.0": "2014-05-05T02:38:49.512Z", "0.0.1": "2014-05-05T02:44:46.419Z", "1.0.0": "2014-05-08T14:52:39.430Z", "1.0.1": "2014-06-03T14:49:59.893Z", "1.0.2": "2014-09-18T17:32:14.933Z", "1.0.3": "2014-09-21T19:40:43.122Z", "1.0.4": "2014-11-23T20:35:27.606Z", "1.0.5": "2015-01-09T03:16:54.215Z", "1.0.6": "2015-02-01T19:54:32.976Z", "1.0.7": "2015-03-17T04:47:05.972Z", "1.0.8": "2015-05-11T02:56:29.699Z", "1.0.9": "2015-12-01T20:55:36.227Z", "1.0.10": "2015-12-10T03:27:48.082Z", "1.1.0": "2016-05-02T06:20:13.693Z", "1.1.1": "2016-05-04T04:12:38.179Z", "1.1.2": "2016-05-30T01:56:19.142Z", "1.1.3": "2017-01-15T06:01:02.224Z", "1.1.4": "2017-03-24T22:57:34.521Z", "1.1.5": "2017-07-26T01:00:40.146Z", "2.0.0": "2017-08-09T03:00:17.324Z", "2.0.1": "2017-09-11T01:11:33.860Z", "2.0.2": "2017-09-25T01:41:05.676Z", "2.0.3": "2018-02-20T04:46:17.003Z", "2.0.4": "2018-07-26T18:34:07.754Z", "2.0.5": "2019-04-16T16:29:44.431Z", "2.0.6": "2020-02-24T07:19:22.199Z", "2.0.7": "2021-06-01T00:57:28.507Z"}, "bugs": {"url": "https://github.com/jshttp/proxy-addr/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/proxy-addr#readme", "keywords": ["ip", "proxy", "x-forwarded-for"], "repository": {"url": "git+https://github.com/jshttp/proxy-addr.git", "type": "git"}, "description": "Determine address of proxied request", "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# proxy-addr\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nDetermine address of proxied request\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install proxy-addr\n```\n\n## API\n\n```js\nvar proxyaddr = require('proxy-addr')\n```\n\n### proxyaddr(req, trust)\n\nReturn the address of the request, using the given `trust` parameter.\n\nThe `trust` argument is a function that returns `true` if you trust\nthe address, `false` if you don't. The closest untrusted address is\nreturned.\n\n```js\nproxyaddr(req, function (addr) { return addr === '127.0.0.1' })\nproxyaddr(req, function (addr, i) { return i < 1 })\n```\n\nThe `trust` arugment may also be a single IP address string or an\narray of trusted addresses, as plain IP addresses, CIDR-formatted\nstrings, or IP/netmask strings.\n\n```js\nproxyaddr(req, '127.0.0.1')\nproxyaddr(req, ['*********/8', '10.0.0.0/8'])\nproxyaddr(req, ['*********/*********', '***********/***********'])\n```\n\nThis module also supports IPv6. Your IPv6 addresses will be normalized\nautomatically (i.e. `fe80::00ed:1` equals `fe80:0:0:0:0:0:ed:1`).\n\n```js\nproxyaddr(req, '::1')\nproxyaddr(req, ['::1/128', 'fe80::/10'])\n```\n\nThis module will automatically work with IPv4-mapped IPv6 addresses\nas well to support node.js in IPv6-only mode. This means that you do\nnot have to specify both `::ffff:a00:1` and `********`.\n\nAs a convenience, this module also takes certain pre-defined names\nin addition to IP addresses, which expand into IP addresses:\n\n```js\nproxyaddr(req, 'loopback')\nproxyaddr(req, ['loopback', 'fc00:ac:1ab5:fff::1/64'])\n```\n\n  * `loopback`: IPv4 and IPv6 loopback addresses (like `::1` and\n    `127.0.0.1`).\n  * `linklocal`: IPv4 and IPv6 link-local addresses (like\n    `fe80::1:1:1:1` and `***********`).\n  * `uniquelocal`: IPv4 private addresses and IPv6 unique-local\n    addresses (like `fc00:ac:1ab5:fff::1` and `***********`).\n\nWhen `trust` is specified as a function, it will be called for each\naddress to determine if it is a trusted address. The function is\ngiven two arguments: `addr` and `i`, where `addr` is a string of\nthe address to check and `i` is a number that represents the distance\nfrom the socket address.\n\n### proxyaddr.all(req, [trust])\n\nReturn all the addresses of the request, optionally stopping at the\nfirst untrusted. This array is ordered from closest to furthest\n(i.e. `arr[0] === req.connection.remoteAddress`).\n\n```js\nproxyaddr.all(req)\n```\n\nThe optional `trust` argument takes the same arguments as `trust`\ndoes in `proxyaddr(req, trust)`.\n\n```js\nproxyaddr.all(req, 'loopback')\n```\n\n### proxyaddr.compile(val)\n\nCompiles argument `val` into a `trust` function. This function takes\nthe same arguments as `trust` does in `proxyaddr(req, trust)` and\nreturns a function suitable for `proxyaddr(req, trust)`.\n\n```js\nvar trust = proxyaddr.compile('loopback')\nvar addr = proxyaddr(req, trust)\n```\n\nThis function is meant to be optimized for use against every request.\nIt is recommend to compile a trust function up-front for the trusted\nconfiguration and pass that to `proxyaddr(req, trust)` for each request.\n\n## Testing\n\n```sh\n$ npm test\n```\n\n## Benchmarks\n\n```sh\n$ npm run-script bench\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/proxy-addr/master?label=ci\n[ci-url]: https://github.com/jshttp/proxy-addr/actions?query=workflow%3Aci\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/proxy-addr/master\n[coveralls-url]: https://coveralls.io/r/jshttp/proxy-addr?branch=master\n[node-image]: https://badgen.net/npm/node/proxy-addr\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/proxy-addr\n[npm-url]: https://npmjs.org/package/proxy-addr\n[npm-version-image]: https://badgen.net/npm/v/proxy-addr\n", "readmeFilename": "README.md", "users": {"nex": true, "isayme": true, "quafoo": true, "nathantu": true, "robermac": true, "mariusc23": true, "mojaray2k": true, "ashish.npm": true, "rocket0191": true, "simplyianm": true, "ganeshkbhat": true, "wangnan0610": true, "markthethomas": true, "zzz1233210731": true}}