#include <iostream>
#include <algorithm>
using namespace std;
int a[1001];
int main()
{
    a[1001] = {0};
    int count = 0; // 退出的人数
    int k = -1;    // 数组从0开始，编号从-1开始，每一次从下一个人开始数
    int n, m;
    cout << n << m; // 人数,数到退出数
    while (count < n)
    {
        int num = 0;
        while (num < m)
        {
            k = (k + 1) % n; // 循环处理下标
            if (a[k] == 0)
            {
                num++;
            }
            if (num == m)
            {
                count++;
                a[k] = -1;
                cout << k + 1 << ' ';
            }
        }
    }
}