{"_id": "type-is", "_rev": "113-b79e39b5271ff00e580bac66a18a59f3", "name": "type-is", "dist-tags": {"latest": "2.0.1", "next": "2.0.0"}, "versions": {"1.0.0": {"name": "type-is", "version": "1.0.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.0.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "4ff424e97349a1ee1910b4bfc488595ecdc443fc", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.0.0.tgz", "integrity": "sha512-CLdmAJgLeMtSPcTFX3eDdC1+ysfYoVdcYjMtuDtg23/fhHXoP5quNsvobr05ZNlG7og+oHQ4bosEzJX++DlIzQ==", "signatures": [{"sig": "MEYCIQDjsr+oxP/cIuPolQ9BVtXkZ68wvYl7WCda/GJAgemSxwIhALHf64R9rEshToAivcKG3qgVVdU6w6SyIM1WzkDmHqtH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/type-is.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Infer the content type if a request", "directories": {}, "dependencies": {"mime": "~1.2.11"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.1": {"name": "type-is", "version": "1.0.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.0.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "ae09d93953c7846f5c083192837575ab363408f1", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.0.1.tgz", "integrity": "sha512-YtjULaHVOJ8YVdHXp8YUIw+CI5rFjNz9wt/FvTrvbJt1OWXStWcrTrs9rzMeQSTfS7qCyBzpTArQqbelD72UyQ==", "signatures": [{"sig": "MEQCIHbmDTHPMP/CQQIYfssSTTtMvW2XEDIiQ8xTBvRjgJmNAiAOgtiE7IBdtuBCdoddFvndwMaGiRw9w4gW5zasxOMe2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "mocha --require should --reporter spec"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.6", "description": "Infer the content type if a request", "directories": {}, "dependencies": {"mime": "~1.2.11"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.1.0": {"name": "type-is", "version": "1.1.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.1.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "d0245ec8b2676668d59dd0cf3255060676a57db6", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.1.0.tgz", "integrity": "sha512-bGTR<PERSON>lk7i/YYyx/d1xYm6gLrALwTcY2HLwsVqAIPKJjjtlI/rGXRgjQrcOln2fcHCbAqi0hrueZ2yPnHvCipQ==", "signatures": [{"sig": "MEUCIFVp6v5UUL7a5E9PgmRBW/SbmwWI3am+bm0W/w8cTwE0AiEAtFGnV87izs0frv4HkAaURj6b0dtQjBO5hCbfqe01km8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.6", "description": "Infer the content type if a request", "directories": {}, "dependencies": {"mime": "~1.2.11"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.2.0": {"name": "type-is", "version": "1.2.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.2.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "a9aaa3f2014850d4813663f6c714cf6318195138", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.2.0.tgz", "integrity": "sha512-xYm8r1IQeUEVnAL5eaHJ2CYAZy8YJVJ4TDUH13hxJ3bsL9maDnQc9wXhkyqUrY7VfAl/galhFDmynNqE8HnWEg==", "signatures": [{"sig": "MEUCIQD8/a+grgLBe70ifnn7sjK9GtMd9Jr7g55id4+70jLcYQIgfJjfnqJNVzmtlOLNsahMliV2ejeC/oCT/EHGYvHud9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.3", "description": "Infer the content type if a request", "directories": {}, "dependencies": {"mime": "1.2.11"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.2.1": {"name": "type-is", "version": "1.2.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.2.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "73d448080a4f1dd18acb1eefff62968c5b5d54a2", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.2.1.tgz", "integrity": "sha512-6/sfH4bn0JhSRWTHv1dGhkfIyftWIkYPtpiNRM/G5/45RazNmI8WaeE76vBQOZNijVYkmmxqOTJiwBcRMlBbQw==", "signatures": [{"sig": "MEQCIBpAucx9xpMly6GjwyrpOvRzOJrbdQ9CchHf65VKkZaMAiA1Q8vXlj2/FL+5MM+wv4KBis2+Ni7HkyLw008bDw+nyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "73d448080a4f1dd18acb1eefff62968c5b5d54a2", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.9", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "1.0.0"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.2.2": {"name": "type-is", "version": "1.2.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.2.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "dfdbf7cffa57cea0f9b1b55b96f629454e0eee97", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.2.2.tgz", "integrity": "sha512-cjx3+RhPaTIp5fyZUUYa9+NanqHMKv0MzjYb87RWXavnOTes61QWfnG+mC+8abfkKo7lRV8kwxFSP8jL6OtrKw==", "signatures": [{"sig": "MEUCIBVgne6LkDoyYO2uxbNDbrBfAiBSG+32mS5/tJlPuGFUAiEAxWsLXMLQ38F2rlf+dVRLtqAD2xvprBa2vtvbs4iDFoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.3", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "1.0.0"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.3.0": {"name": "type-is", "version": "1.3.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.3.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "131df06aca1476419f95de3e38f2efef8b249c20", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.3.0.tgz", "integrity": "sha512-sBNA2/5T8h3ihCL3/3ujfMRY4gNmU0uSzGgeZnIaDnBmf737mkLJIaBgtgyzbjpW14imgczu221pO+aeOT+x0Q==", "signatures": [{"sig": "MEYCIQDrJLORL9IfXf9dkXRcv49EXJhQdtocAHcXyk3cg33/zAIhAOMfFAPq9Qk1ykz2ORktZ0Xc9QE6yD34iV1bV7hQRyne", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.3", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "1.0.0", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "*", "should": "*", "istanbul": "0.2.10"}}, "1.3.1": {"name": "type-is", "version": "1.3.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.3.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "a6789b5a52138289ade1ef8f6d9f2874ffd70b6b", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.3.1.tgz", "integrity": "sha512-PLks4DIqAA9z7zHH0VuUv0aZ36t6cq8/K0y0OdHJtTkfSbGHhNvKh3pw1PPakXkjlAskC4apJlxeYcGpKZWvkA==", "signatures": [{"sig": "MEYCIQCu708YBuSiwXmdLIZmAk3qFMxdeFP6+nlEDWcBQhEaKgIhAPG26eTVRbXfaSEBE0vT8HMmOwN6oG25gdj/JSCz8jiz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --require should --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.3", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "1.0.0", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "*", "should": "*", "istanbul": "0.2.10"}}, "1.3.2": {"name": "type-is", "version": "1.3.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.3.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "4f2a5dc58775ca1630250afc7186f8b36309d1bb", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.3.2.tgz", "integrity": "sha512-sdIhnvhWEyIP2DKjj1o9tL31m8vFxDfLPD56KXz2absqY5AF2QYkJC7Wrw2fkzsZA9mv+PCtgyB7EqYOgR+r3Q==", "signatures": [{"sig": "MEUCIQDzheoLelO9oFafx5ByqLanpm/3NOlymY74t3uzG0RXPAIgGlozc8tIBNgNF1qWyKSzd3Rgz8hjJ1IdvyRpSmcb81k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "4f2a5dc58775ca1630250afc7186f8b36309d1bb", "engines": {"node": ">= 0.8"}, "gitHead": "d76790909638d4cf1785e09858db5576f91f710f", "scripts": {"test": "mocha --require should --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.16", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~1.0.1", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "*", "should": "*", "istanbul": "0.2.11"}}, "1.4.0": {"name": "type-is", "version": "1.4.0", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.4.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/type-is", "bugs": {"url": "https://github.com/expressjs/type-is/issues"}, "dist": {"shasum": "de51d78a2ccb19a8fa2e137b06784f6b39a88059", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.4.0.tgz", "integrity": "sha512-LmWmvhpDMep36TWOb6Xok2Ngvap4s+rYBwWowK/hSr3kNIFfxLd3afKoHvVfLlNzSCIHb8FkjpzTcL5HZFAXaw==", "signatures": [{"sig": "MEUCIF7ZqB1B7O+MC4k0c8PxM6mu9OPw9aKBXuSUGaIifBLzAiEAg+SAVNvaj4fH5iAHK7HNuCA3zaD9obOGKWBhPNvl8wM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "de51d78a2ccb19a8fa2e137b06784f6b39a88059", "engines": {"node": ">= 0.8"}, "gitHead": "f0483c28a704eaef3da9c0f8d9a2fc9dc6d50d3f", "scripts": {"test": "mocha --require should --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/type-is", "type": "git"}, "_npmVersion": "1.4.21", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.0", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "1", "should": "4", "istanbul": "~0.3.0"}}, "1.5.0": {"name": "type-is", "version": "1.5.0", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.5.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "e3539711529c5ee4e7cd9f5bed27487cb819f823", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.5.0.tgz", "integrity": "sha512-omivIx5ZYDiXIsgO/qMzBmZeIJkOn87sgk5ofDbTVGD+574bLQ/hRrEJD+i865S82fV/I2XNGwF1jcvtaXlfDg==", "signatures": [{"sig": "MEUCICzPOpb4WOrdDVtQ3p2IqckR5BqnDhJFk6Q49EO4XujfAiEAx+jBxz1I2QM5NxpUrpq5ENMI1cc4/o1ABAgcLGczrGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "e3539711529c5ee4e7cd9f5bed27487cb819f823", "engines": {"node": ">= 0.8"}, "gitHead": "1cff718285478905d97bbf6cf666e0ce1c0284e3", "scripts": {"test": "mocha --require should --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.21", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.0", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "1", "should": "4", "istanbul": "~0.3.0"}}, "1.5.1": {"name": "type-is", "version": "1.5.1", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.5.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "5c1e62d874f79199fb16b34d16972dba376ccbed", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.5.1.tgz", "integrity": "sha512-pTEYdpzmUV4nHhsShDnje7FQa2UsqEyw7Zqae5qOKBILkvCNPokgKKG3A4rJeU6dvLaE3+Rw+Z0eWtLUOSFjAA==", "signatures": [{"sig": "MEUCIQCT6sR7Q5eBib8TOnKioc/BFfwfTST8Imfdtal7aWH2MAIgSMD4l5z2ED/1/VVBQkommYX2hpEkX0QHXckTDQNlsaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "5c1e62d874f79199fb16b34d16972dba376ccbed", "engines": {"node": ">= 0.6"}, "gitHead": "74d33287453bf7c166f6410fc608c1c7588070ae", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.21", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.1", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "1", "istanbul": "~0.3.0"}}, "1.5.2": {"name": "type-is", "version": "1.5.2", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.5.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "8291bbe845a904acfaffd05a41fdeb234bfa9e5f", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.5.2.tgz", "integrity": "sha512-wtS3BNBYRLF0nSop3WQ4ZKdK3IY3O7UGzO3RMPEe5ruTJkXKkkcYZKp0600jQFAwZGBDRXWnsbICsKoO3T5pPA==", "signatures": [{"sig": "MEUCIQCRf2TFoWC2DFM2nOYAxAdsazGDwUgV6/oQ971X3LhRbgIgBEPNDKVjwIfo85+wf1N7hLOlvbhHWFDlctERPsSy7gw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "8291bbe845a904acfaffd05a41fdeb234bfa9e5f", "engines": {"node": ">= 0.6"}, "gitHead": "53b2d3f2c0177ac89576055d327d543291d36879", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.21", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.2", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "1", "istanbul": "~0.3.0"}}, "1.5.3": {"name": "type-is", "version": "1.5.3", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.5.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "b7fb92d0abc628393f10dd260932cca65fe9ff68", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.5.3.tgz", "integrity": "sha512-dpX+qPO89XyhBL2KAS5yhwwX0tzQ92gU18IcvMPAKBTAbnZQAZQSlRFdYsUlbsAEuXDnrn/gfl2uwHWt3YDDrA==", "signatures": [{"sig": "MEYCIQCnJEIvRdw3uGZ059jHObxoa5FeA/DG+4jrOuh3+tn2VAIhAPvSmoAJEvZr/rnOe8APduzliMJBlkmJKqITCtrHbaEL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "b7fb92d0abc628393f10dd260932cca65fe9ff68", "engines": {"node": ">= 0.6"}, "gitHead": "202b4823bcc0aeda3595c14a03fdcb2c60cb0ebf", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.21", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.3", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "~0.3.0"}}, "1.5.4": {"name": "type-is", "version": "1.5.4", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.5.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "f2afe8635dcf2d159096202be6e120423fa19837", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.5.4.tgz", "integrity": "sha512-bh9/1GBX5N50VQM67DxvDXF1q5RvJUv6neef3u73hW/o1o5TB5UWj0T6bTxnKwM87gVPCx2ab5rzfQErnN7NBQ==", "signatures": [{"sig": "MEYCIQD5+acM3Q8C4Ec60FvPp9pqMGltCj2iozGTNpqk0RtwNAIhAIZyPM86705Jez6aYdUVArR9CKUPVfgjXFZrtO6PBOw5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "f2afe8635dcf2d159096202be6e120423fa19837", "engines": {"node": ">= 0.6"}, "gitHead": "d604e7a69ce986692e9f241e21b9abe6d4f77eb0", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.21", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.4", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "~0.3.2"}}, "1.5.5": {"name": "type-is", "version": "1.5.5", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.5.5", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "45248af57f96366d0326ea0868f6bc8607dc4b21", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.5.5.tgz", "integrity": "sha512-7SoYw4Iv8n47nT6loTary+9S8c4twzxJzEX3cYiYZLdSCfnrAwTbb+9RYpaNAU7eUekwOivpNt79LuzUEi4BtQ==", "signatures": [{"sig": "MEYCIQCcUsu5gUseiH5bh84+VRAgIz7EoGHgdOsHEAp6i2I/7wIhALRbWawxr1ZcV7ljuE1ioS7xIppJAMQLEnhz9Q/Hc8/X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "45248af57f96366d0326ea0868f6bc8607dc4b21", "engines": {"node": ">= 0.6"}, "gitHead": "b13dc3fa142ad60bea775181ba5f50364042691f", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.7", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "1.5.6": {"name": "type-is", "version": "1.5.6", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.5.6", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "5be39670ac699b4d0f59df84264cb05be1c9998b", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.5.6.tgz", "integrity": "sha512-ZZ0MsMhtVkJkHpZHqEKgh7h7zoLym5N7T+cX5TAyukLX2IIKx4vJR8B3umYusBDNr2Z6cikmpymO0a8dI4xyIA==", "signatures": [{"sig": "MEUCIFB+QosfJQ8r5A74KUvfZe2FF+rzx/1hgF2yPeMrfolGAiEA/yBwASuIS5gVq0kUTNT/nKHlctimHD8ITs74+ChiJwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "5be39670ac699b4d0f59df84264cb05be1c9998b", "engines": {"node": ">= 0.6"}, "gitHead": "18f74f0f51c066c1485344c2e8d88c86c00d3bea", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.8", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "1.5.7": {"name": "type-is", "version": "1.5.7", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.5.7", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "b9368a593cc6ef7d0645e78b2f4c64cbecd05e90", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.5.7.tgz", "integrity": "sha512-of68V0oUmVH4thGc1cLR3sKdICPsaL7kzpYc7FX1pcagY4eIllhyMqQcoOq289f+xj2orm8oPWwsCwxiCgVJbQ==", "signatures": [{"sig": "MEYCIQCZZAJD3oOZ6I4+gTC7hc5sg4yl24eEZtc1bH38HJqwxgIhAJ9uTVPluKW+tw5hAKLsELtK0W2GJjegzzzqSL6NC/RB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "b9368a593cc6ef7d0645e78b2f4c64cbecd05e90", "engines": {"node": ">= 0.6"}, "gitHead": "f4335cc563a98ee80366f04f67c50cef089ae803", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.9", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "1.6.0": {"name": "type-is", "version": "1.6.0", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.6.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "efcb9223fafad5a03be14d8f6c9e1785f2c0e7c3", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.0.tgz", "integrity": "sha512-CqVrPKax6n5hAqPdjcnAINWQd9GH5Hm3epiXvR1KWFcE+P/RuLdO40w3MZ0m2SOQhozG9O5M6c8XfEI1oym40Q==", "signatures": [{"sig": "MEQCID0DOY2wn8I7zmBq9EfAjEEabjgJmLYY/SDoQt+4NhSLAiAPie/Gsg3DyfaMobMxgKO4u+dSMTfB8zqVq1e2MpLdTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "efcb9223fafad5a03be14d8f6c9e1785f2c0e7c3", "engines": {"node": ">= 0.6"}, "gitHead": "8386837f91cfbf9f21f02758dee36655a901e1c4", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.9", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "1.6.1": {"name": "type-is", "version": "1.6.1", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.6.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "49addecb0f6831cbc1d34ba929f0f3a4f21b0f2e", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.1.tgz", "integrity": "sha512-ZNg3Qk/0sIYxNfm4OV8dppvO3al2LKe3fXuNbVk/HzbO5akyf+egZZpfBprLoHnfm8klTile5WvteLXBX7cIrA==", "signatures": [{"sig": "MEUCIHKCTGH15BKQfLW8hwWFIK3p+06FJiZkj5RCLtWnyVxFAiEAnTUuy41lyc78ByiPEiSyyx1NqLJ+kHII2NNWvMXLoXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "49addecb0f6831cbc1d34ba929f0f3a4f21b0f2e", "engines": {"node": ">= 0.6"}, "gitHead": "339a7df4d8fed268b0f12d0fdab91d39f88d6f4e", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.10", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.7"}}, "1.6.2": {"name": "type-is", "version": "1.6.2", "keywords": ["content", "type", "checking"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "type-is@1.6.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "694e83e5d110417e681cea278227f264ae406e33", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.2.tgz", "integrity": "sha512-y/Bam+fYWp2AJkG9BHYW3R62W9R4E4NFujhY+PqhZGbJLrsdGD84qux9YN69QwHbAMujexJrQ3Pszzqp6TFpCg==", "signatures": [{"sig": "MEUCIQDagKYM20dODStZRv9LDXK9H7Wpg5prKop+Ybjr7rwDZQIgPEC4Uwx5RpLd+3FH8zHeaWISfHvcVzaKG1ZfoAXLUs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "694e83e5d110417e681cea278227f264ae406e33", "engines": {"node": ">= 0.6"}, "gitHead": "4e33e2fbb1f0daa6ec8c5444dbb60e44292ae314", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.0.11", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.9"}}, "1.6.3": {"name": "type-is", "version": "1.6.3", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "d87d201777f76dfc526ac202679715d41a28c580", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.3.tgz", "integrity": "sha512-Md9DP4oiC2rPRJLzs4ngYMIlnRpP2d+ZowJLMMZzgwYJG7AlkeiTkB4ii/ShtfUa8tjVA00LqUYcjTEVEGtU9w==", "signatures": [{"sig": "MEUCIQDpKxq0677D+P23+zduTvN5AyA174AQ74DzrpEx5ekynwIgCFwSjYJTDkkRo/ShfR8meGXwgTtKwZIPE0bQYH/XERQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "d87d201777f76dfc526ac202679715d41a28c580", "engines": {"node": ">= 0.6"}, "gitHead": "294dff1c93d2ccb9a56191d37e390a8d2ad02e6f", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.1", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.14"}}, "1.6.4": {"name": "type-is", "version": "1.6.4", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "d76fe92f0bcf7b0cf16b64d095e248f71079c318", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.4.tgz", "integrity": "sha512-0pTZ+V/YzE9Ugs3EtX7QavdltqWG0XWbK6k/+TfjH78B9dYsaMLlGV9sGB48j0VYl494y/xk7Y0Gl4c+O4Mfbw==", "signatures": [{"sig": "MEUCIEXYepuOUukn9ZLiMbQtPMOb+T+mJ3c1boSKbf9IOi4YAiEA3mdXaJGx86k/QM1WIoiQAkvmKyFvU8NpxmsTbp0pusA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "d76fe92f0bcf7b0cf16b64d095e248f71079c318", "engines": {"node": ">= 0.6"}, "gitHead": "0edac23cef38f02ded0e072af65a078865af5b66", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.2", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.17"}}, "1.6.5": {"name": "type-is", "version": "1.6.5", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.5", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "92129495c7b7563eaf923b447382c6c471f95de4", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.5.tgz", "integrity": "sha512-G8AbS/1yQ0dN1xmU9sM0/FQCZIB5YvC1iR10t951cRAJtUgyxFKpYcdDfYLXiJ2ay63/3de0kC0aAp4GvSvufQ==", "signatures": [{"sig": "MEQCIC1wlVUMORPG3U5XDlGWCiQ67S4/lv8kcNxyzybfVSbkAiBhO5LrT3LXqzwgdmcUpxP4wog3MPlfIvBAIAeic3+d4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "92129495c7b7563eaf923b447382c6c471f95de4", "engines": {"node": ">= 0.6"}, "gitHead": "b5fd0918ecc05113d32dbb97b02bb18cb635b059", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.3", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.17"}}, "1.6.6": {"name": "type-is", "version": "1.6.6", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.6", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "398799519b62360f55c3cd6c486294531975926c", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.6.tgz", "integrity": "sha512-kBAr7Aqr5VAXrEvXzkLE3EZTxzFviKOX8+qgaKDw7awKUA59lfjnPh2bs0LaHHGdN9p2PE7g839KWiwO2bckPg==", "signatures": [{"sig": "MEUCIEX1AvvMc6PFNsOmSyFmPiMUIOtMv93xM4Qp9B0TaclWAiEAvY8Fru7sowQmJo0AvsxF8zJs6EDe5w+TYa43rJpUPko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "398799519b62360f55c3cd6c486294531975926c", "engines": {"node": ">= 0.6"}, "gitHead": "f2b12fce6172bf91f771d8898055d6efa0e30422", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.4", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.17"}}, "1.6.7": {"name": "type-is", "version": "1.6.7", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.7", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "5ec2bc7c7debc37f586d518c0747ab901f76bcec", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.7.tgz", "integrity": "sha512-JXEIIlhWBDqCSZS4cy8HpibUJk0yZAwQorGjGFx1qOQ9y5JA6zDLqcD5BAVQzv83hSvZLx50GVN52PFRnzISHA==", "signatures": [{"sig": "MEUCIFrLtUs+gcSwIw5LUPPwKZkX46RTb1SZIUUpJBU3mRTbAiEA5kANzkwl94iCdLzQ8TMMVxHfDs3eQjamvdlHWk66sNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "5ec2bc7c7debc37f586d518c0747ab901f76bcec", "engines": {"node": ">= 0.6"}, "gitHead": "f162e9e971c19d28c348bb9b9ef660d17fcf1ba0", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.5", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.18"}}, "1.6.8": {"name": "type-is", "version": "1.6.8", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.8", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "3bac8c0c852754c855143e206d4a16e908bf0315", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.8.tgz", "integrity": "sha512-xDBvCf8fFRs4QSVwK7iM8WaMxglDFR6USgJ/bKJOWFfC1LBI11pzQDpyyUEQudGN5dxTUB/XU44Siq/18fX74A==", "signatures": [{"sig": "MEYCIQC2s/rqY8/nbdtlIanUKys+4zc8GL0AzgwSu6V4IPgO2AIhALbL5cpExkj3YLBjNLdcR1GXfw7Nwtpz0qE7u0vSm5Wz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "3bac8c0c852754c855143e206d4a16e908bf0315", "engines": {"node": ">= 0.6"}, "gitHead": "6c93143cead7c596072133491b84f03a05403d3e", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.6", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.19"}}, "1.6.9": {"name": "type-is", "version": "1.6.9", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.9", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "87f3e88b92ff5ac30fbc1acf9a9d00cbc38b3d7a", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.9.tgz", "integrity": "sha512-9rUXcYSqvKqg3UnwwkiBKZxqOluihsCeyuVTktMbNnPriqzW46e3ZUGUdG+JQmH9/3qeGsKwLtQijYsPatavkA==", "signatures": [{"sig": "MEUCIBDkBY4p3l1mZge3y/vJDXPY/NGo+wqKEfOMW7/wAjXHAiEAzB4OjctSVuY/zcFXwU1znWlkbznyZHozNjVIEhZ3R7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "87f3e88b92ff5ac30fbc1acf9a9d00cbc38b3d7a", "engines": {"node": ">= 0.6"}, "gitHead": "2f5999d6f2d88f2f36eeb1e8db78c2ec43fdbf13", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.7", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.21"}}, "1.6.10": {"name": "type-is", "version": "1.6.10", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.10", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "d27e995b20d8c2a543f3420573f690a3929fd75a", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.10.tgz", "integrity": "sha512-7SASBU4gxbf91Zpn7gbEKwMX/dz4zymYSD4RP0SVisDIl1w8wqytilOXFJezHsUf0kVOiJVwswxRb7p7py/qUA==", "signatures": [{"sig": "MEQCIG9+7ZR2TN9zkKKs25ImdSoo3+bqh/xzV3ZazlfrBBAEAiBth9fQXa/t8ipCEx6c/rDP38GLwGYAUd6JIFRk257TLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "d27e995b20d8c2a543f3420573f690a3929fd75a", "engines": {"node": ">= 0.6"}, "gitHead": "072de04e5c6bd4a3dd089dbd70ec2b1d505625a9", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.8", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.1"}}, "1.6.11": {"name": "type-is", "version": "1.6.11", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "ritch", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "42ecde7970f2363738b986c0351efba5aa531648", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.11.tgz", "integrity": "sha512-LXayRc13t68vhOiY0UDvlWq2HiqGISjyhL30Hp7R/Y+wFfsB+rpWenCIgbS8ciUtLp962K8EUkbpw76FN1uHWA==", "signatures": [{"sig": "MEUCIQCyNS0mAGShdbZ+9tZj+KWipncK3tFymXd2ThmeyCYWMQIgenpFUqLQr8MItuEeFHQx4xqrZKcdPR/e3k4IJkQ9dB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "42ecde7970f2363738b986c0351efba5aa531648", "engines": {"node": ">= 0.6"}, "gitHead": "8e60e3e78aef84928e0e6c09da950f6950adcdd2", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.9", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.2"}}, "1.6.12": {"name": "type-is", "version": "1.6.12", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "0352a9dfbfff040fe668cc153cc95829c354173e", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.12.tgz", "integrity": "sha512-Sp2FHV/pnM3RiYUkUuo5DgbhSP7Wo2OewD63Plfz4Z6ZZkS7ppdajl+qMcELJWRwyS+sZlyiViVO3a6JsLgm+g==", "signatures": [{"sig": "MEQCIGRHsULXy/G5lDDm92PL+RWp1v4kH35Mc3UOFJJ2lsZZAiBSJg3ydSZsFwSKG9dnCGSDZ16pK1nHmFDA6K5/ZfHhwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "0352a9dfbfff040fe668cc153cc95829c354173e", "engines": {"node": ">= 0.6"}, "gitHead": "7ba49c0ccc8e34f4321768c0b13c2ebcccaae28c", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.10", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/type-is-1.6.12.tgz_1456726142464_0.8247741810046136", "host": "packages-9-west.internal.npmjs.com"}}, "1.6.13": {"name": "type-is", "version": "1.6.13", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is#readme", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "6e83ba7bc30cd33a7bb0b7fb00737a2085bf9d08", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.13.tgz", "integrity": "sha512-34S1refwO9EIvJN6Yy1IBYL4kpuVsR1E6AoCRmdDw1DWcHJA5qXfROk4hcnqy9uNS5iZ+P0E/sx8e5n4vFFTsA==", "signatures": [{"sig": "MEYCIQC3zvA5usW08ULCPCdiu+AIJ/Vt9mhlkn3yaDoCOD6/sQIhAJq16pAvSmOrLxeroXs4UXS5iWwtFYZtrcvxMk+Fxrgp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "6e83ba7bc30cd33a7bb0b7fb00737a2085bf9d08", "engines": {"node": ">= 0.6"}, "gitHead": "88c47523fff910343b3ca7d4928dad40f21ea6cd", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/type-is.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Infer the content-type of a request.", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"mime-types": "~2.1.11", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "2.10.2", "istanbul": "0.4.3", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/type-is-1.6.13.tgz_1463622049206_0.9134831207338721", "host": "packages-16-east.internal.npmjs.com"}}, "1.6.14": {"name": "type-is", "version": "1.6.14", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "e219639c17ded1ca0789092dd54a03826b817cb2", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.14.tgz", "integrity": "sha512-pM3GvwuTj7H0LexCt3FK6R9KcP0SYRnmjZfHQ7RtuZkLVSfvQZmXKvSiHTDu+RFdkwyj9ZRnWXtvOZWlHiMgGQ==", "signatures": [{"sig": "MEQCICA0ylNA/NL/mYwOaogFBDSFLuGyDUCPdtJdl9XfbAtTAiAxjpUMpKYHeWLBAJkTbmN0qwLkSHN2tY9mjHhkChMd0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "e219639c17ded1ca0789092dd54a03826b817cb2", "engines": {"node": ">= 0.6"}, "gitHead": "f88151e69d91c5ed42e29dea78f5566403a5a7ad", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/type-is", "type": "git"}, "_npmVersion": "1.4.28", "description": "Infer the content-type of a request.", "directories": {}, "dependencies": {"mime-types": "~2.1.13", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "2.10.2", "istanbul": "0.4.5", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/type-is-1.6.14.tgz_1479517858770_0.4908413903322071", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.15": {"name": "type-is", "version": "1.6.15", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is#readme", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "cab10fb4909e441c82842eafe1ad646c81804410", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.15.tgz", "integrity": "sha512-0uqZYZDiBICTVXEsNcDLueZLPgZ8FgGe8lmVDQ0FcVFUeaxsPbFWiz60ZChVw8VELIt7iGuCehOrZSYjYteWKQ==", "signatures": [{"sig": "MEUCICZDDCZaQwIPugioVc3wRku3kr2/BBC3U4Ce/8IJ31F2AiEAyUpWxAOhdX6apo4JZggFSa56xHmnWuw3oUPv2fCiNUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "cab10fb4909e441c82842eafe1ad646c81804410", "engines": {"node": ">= 0.6"}, "gitHead": "9e88be851cc628364ad8842433dce32437ea4e73", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/type-is.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Infer the content-type of a request.", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"mime-types": "~2.1.15", "media-typer": "0.3.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/type-is-1.6.15.tgz_1491016789014_0.6958203655667603", "host": "packages-18-east.internal.npmjs.com"}}, "1.6.16": {"name": "type-is", "version": "1.6.16", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is#readme", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "f89ce341541c672b25ee7ae3c73dee3b2be50194", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.16.tgz", "fileCount": 5, "integrity": "sha512-HRkVv/5qY2G6I8iab9cI7v1bOIdhm94dVjQCPFElW9W+3GeDOSHmy2EBYe4VTApuzolPcmgFTN3ftVJRKR2J9Q==", "signatures": [{"sig": "MEYCIQCK2RS2zmi5jNfYztTFKfVFogZbkiaezhyxD8lrY7xuZgIhAMeYswepLf5FEwfLRuQrWtJmAxsOp6htXvh445tOy2ai", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16739}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.6"}, "gitHead": "dc723b95e2c52c689cf9d4cefbc5d91e74f7524a", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/type-is.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Infer the content-type of a request.", "directories": {}, "_nodeVersion": "6.13.0", "dependencies": {"mime-types": "~2.1.18", "media-typer": "0.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/type-is_1.6.16_1518812522921_0.03331830182177953", "host": "s3://npm-registry-packages"}}, "1.6.17": {"name": "type-is", "version": "1.6.17", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.17", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is#readme", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "9ef72233f08ffbe83b8fa3c93f4f93ecbc330bc2", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.17.tgz", "fileCount": 5, "integrity": "sha512-j<PERSON>ZzkOoAPVyQ9vlZ4xEJ4BBbHC4a7hbY1xqyCPe6AiQVVqfbZEulJm0VpqK4B+096O1VQi0l6OBGH210ejx/bA==", "signatures": [{"sig": "MEUCIGRRHFdMNXEpRfySQKsXl6mwY+LwsPqN18Gm7JgEBnP/AiEA2q16UzWwzdose6tVNjIY1HOHIo+6jisMmYqC1lyoQGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwduDCRA9TVsSAnZWagAAjUMP/AlxKf7FVSUFr1IeznKR\nUIrZtUn1zbEIksxVqt68ZC2Ktj7nvcriZP6UkWPskaL8gQ5u/MRWOKZ3loVU\nzZoskOp+WXZdcLr8+HG6FRytmfRPj2wm04+D0YiRAfdBQJKXI7U3U9EWAkw6\niBWG0mFTQNfrgoArRKfqFbCJrkBy25Z3ouOo6L5MbmRCVpYFmS17Vv+HxlBe\nLB2cpnA4fnJCs6XvrxcyTLBPR/8jL8KeBLLpatLDJeqE3L5M9b/yvpjyPYXc\nBnfPIWGCu5vQjAtVRpLx54C3I7QFK+wbturRwyTbEu+S6MTNy9IboHlg6a0p\nBpds8UpJ83EgQ9oh+GAklzNNJyXwf/ygZ8L7hkL1fTjvLp78+r9k1ojcvEbn\nH8oNRpgkFau77MaPVWznVV4qgQKF2wRx9bDcTnRspJ8Jemgjk7nwwhZe8L42\nB5ZEUwVPGAPWPMXRsvkF0VSVFerGGgUluleBqsGe2xE9kk24vPv/4cAZFZnu\nwCsN7xtyLs8m6/YkOe5U5PJMW0RHt76gfghMwDzZIcZBNZECCC4PA3hqbGve\n1wsdSnzR+ELRi9GYLyab+5NO3OK78WTWw8MrnFYtwMS8uscc4cAl1WGnhkvu\nR2zjksu035ojjFOaGN7QxVdx4ZOP0Y0hnRhKVmEUHZ0Z0Yqo1Q0OHR5Egmlm\nhg/k\r\n=EGkU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "c22b4afcd251c5205d1bb49e6d6835b16233121a", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/type-is.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Infer the content-type of a request.", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"mime-types": "~2.1.24", "media-typer": "0.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.4", "eslint": "5.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-is_1.6.17_1556208514463_0.06453249157999785", "host": "s3://npm-registry-packages"}}, "1.6.18": {"name": "type-is", "version": "1.6.18", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@1.6.18", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is#readme", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "4e552cd05df09467dcbc4ef739de89f2cf37c131", "tarball": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "fileCount": 5, "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "signatures": [{"sig": "MEUCIAffRcZ90EQToocm427p2o5BLIKlSZhlTVXpDl47EFJLAiEAg2ROOgWFZWjuDLW1D7NrR4+fwEOdm75VZ77X1zC2n2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcww7VCRA9TVsSAnZWagAADo4P/isJIJ9PaSvaRKD2jXlI\nfnZaodUUUdgiQfzG0uL2nvb7F4iHg6ddCEG5ofw4MzDQAXpsmv2r7F/3CVE7\n6KvUo5mVJ/KngeH95DxUcef/bTTAbCFdht7gbTFpZ0UKM4Ow3iuvgyvz/3aI\nJgkxqw8WgY/IdFk5NVZ3g5x8kGWXD1Llf44LLptYZ3R9J2u73CDP3ft9nE1Y\np9NAu3X0NH11U6IFNS+T62hehOfdsJUjY1XZc6142m7KsXjiDxISiX2tj0kg\n1DsW7oOJeWnbOJSxxfiI4Np1T0gRupjYfFBg/Fsfl6p+qcOAgQYMZTqv2iR+\nWSD9QuL/QYiYKqfAiumnC3uxPkT6AUqohIzk5HUSXrJcuyCmFWNDIO3MllC7\nW+9Ac6+qkN+dMGRx9hWS632uyb6AxbbulXNPbv//JwGzSyu+gLSkOQgk0vto\ngMYi7HUbQQquVBftMs7OqZ3HiP4q1gMr1H4PuoUUQw1FzxgSi6gY8hJriqM/\nPSKVym4y0Umict5DJnirgtSIAArTLVHAmEcY1XRFJB43HLrkNdcCpVH6FfRh\nvZ3dQsN5HQA0ioRyCstwsjDAbEzYStPIXmBOdBFmNnRIMoBe/16HbNZbO208\nKODasC9g3GHFn/IGgr3h8gW+WD76ISD5zUFshm0w4eJGx9XKtzhhjicnm6PO\nO1q0\r\n=WOqY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "bfebe3d4ac312debccb7dbbc79242e2581dea5f0", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/type-is.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Infer the content-type of a request.", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"mime-types": "~2.1.24", "media-typer": "0.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.4", "eslint": "5.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-is_1.6.18_1556287189103_0.20416863530873397", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "type-is", "version": "2.0.0", "keywords": ["content", "type", "checking"], "license": "MIT", "_id": "type-is@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/type-is#readme", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "dist": {"shasum": "7d249c2e2af716665cc149575dadb8b3858653af", "tarball": "https://registry.npmjs.org/type-is/-/type-is-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-gd0sGezQYCbWSbkZr75mln4YBidWUN60+devscpLF5mtRDUpiaTvKpBNrdaCvel1NdR2k6vclXybU5fBd2i+nw==", "signatures": [{"sig": "MEQCIBhzTqTErs91DLnFW4ojSrKoqkuI3aQhGh6XFG9EVg1FAiAwUhKpjJY2TXZBuA2NWP3iTVUDf/UQhOQ3NCD/sD2Tww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21348}, "engines": {"node": ">= 0.6"}, "gitHead": "0d79e2d0c5737206d0f688769c5acffee1de953f", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test:debug": "mocha --reporter spec --check-leaks --inspect --inspect-brk test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/type-is.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Infer the content-type of a request.", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"mime-types": "^3.0.0", "media-typer": "^1.1.0", "content-type": "^1.0.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.1", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/type-is_2.0.0_1725125288247_0.8582864676821773", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "type-is", "description": "Infer the content-type of a request.", "version": "2.0.1", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test:debug": "mocha --reporter spec --check-leaks --inspect --inspect-brk test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "keywords": ["content", "type", "checking"], "_id": "type-is@2.0.1", "gitHead": "4a16e0850ec60234a45c4f546bf759ae161c6a36", "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "homepage": "https://github.com/jshttp/type-is#readme", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==", "shasum": "64f6cf03f92fce4015c2b224793f6bdd4b068c97", "tarball": "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz", "fileCount": 5, "unpackedSize": 21269, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIE5L+/qTUCwB+EiPWBST/CXJ8WdJBRs3n7WJtkDB/bgRAiBpEw/y/D/duf9Y+j5tnZxckcv/uhkt8cKe19wFcBHGhA=="}]}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/type-is_2.0.1_1743038401388_0.12399995026228616"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-12-28T00:06:19.362Z", "modified": "2025-03-27T01:20:01.769Z", "1.0.0": "2013-12-28T00:06:19.362Z", "1.0.1": "2014-03-30T07:59:36.618Z", "1.1.0": "2014-04-13T00:23:29.651Z", "1.2.0": "2014-05-12T03:30:58.338Z", "1.2.1": "2014-06-04T04:37:46.796Z", "1.2.2": "2014-06-20T01:21:35.783Z", "1.3.0": "2014-06-20T02:04:10.178Z", "1.3.1": "2014-06-20T03:04:57.712Z", "1.3.2": "2014-06-25T01:04:21.979Z", "1.4.0": "2014-09-02T08:46:55.515Z", "1.5.0": "2014-09-05T19:49:01.787Z", "1.5.1": "2014-09-08T06:33:30.713Z", "1.5.2": "2014-09-29T05:30:21.355Z", "1.5.3": "2014-11-09T22:46:49.093Z", "1.5.4": "2014-12-11T02:42:44.888Z", "1.5.5": "2014-12-31T05:26:13.133Z", "1.5.6": "2015-01-30T05:31:55.125Z", "1.5.7": "2015-02-10T05:35:50.380Z", "1.6.0": "2015-02-13T03:55:36.313Z", "1.6.1": "2015-03-14T04:20:19.211Z", "1.6.2": "2015-05-11T05:52:18.626Z", "1.6.3": "2015-06-08T18:58:33.938Z", "1.6.4": "2015-07-02T01:22:52.689Z", "1.6.5": "2015-07-17T03:36:46.181Z", "1.6.6": "2015-07-31T17:12:11.577Z", "1.6.7": "2015-08-20T18:16:35.954Z", "1.6.8": "2015-09-04T14:57:13.431Z", "1.6.9": "2015-09-28T04:16:01.110Z", "1.6.10": "2015-12-01T19:07:05.618Z", "1.6.11": "2016-01-30T05:09:52.834Z", "1.6.12": "2016-02-29T06:09:05.369Z", "1.6.13": "2016-05-19T01:40:52.083Z", "1.6.14": "2016-11-19T01:11:00.743Z", "1.6.15": "2017-04-01T03:19:49.693Z", "1.6.16": "2018-02-16T20:22:03.004Z", "1.6.17": "2019-04-25T16:08:34.582Z", "1.6.18": "2019-04-26T13:59:49.224Z", "2.0.0": "2024-08-31T17:28:08.399Z", "2.0.1": "2025-03-27T01:20:01.576Z"}, "bugs": {"url": "https://github.com/jshttp/type-is/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/type-is#readme", "keywords": ["content", "type", "checking"], "repository": {"type": "git", "url": "git+https://github.com/jshttp/type-is.git"}, "description": "Infer the content-type of a request.", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "readme": "# type-is\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nInfer the content-type of a request.\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install type-is\n```\n\n## API\n\n```js\nvar http = require('http')\nvar typeis = require('type-is')\n\nhttp.createServer(function (req, res) {\n  var istext = typeis(req, ['text/*'])\n  res.end('you ' + (istext ? 'sent' : 'did not send') + ' me text')\n})\n```\n\n### typeis(request, types)\n\nChecks if the `request` is one of the `types`. If the request has no body,\neven if there is a `Content-Type` header, then `null` is returned. If the\n`Content-Type` header is invalid or does not matches any of the `types`, then\n`false` is returned. Otherwise, a string of the type that matched is returned.\n\nThe `request` argument is expected to be a Node.js HTTP request. The `types`\nargument is an array of type strings.\n\nEach type in the `types` array can be one of the following:\n\n- A file extension name such as `json`. This name will be returned if matched.\n- A mime type such as `application/json`.\n- A mime type with a wildcard such as `*/*` or `*/json` or `application/*`.\n  The full mime type will be returned if matched.\n- A suffix such as `+json`. This can be combined with a wildcard such as\n  `*/vnd+json` or `application/*+json`. The full mime type will be returned\n  if matched.\n\nSome examples to illustrate the inputs and returned value:\n\n```js\n// req.headers.content-type = 'application/json'\n\ntypeis(req, ['json']) // => 'json'\ntypeis(req, ['html', 'json']) // => 'json'\ntypeis(req, ['application/*']) // => 'application/json'\ntypeis(req, ['application/json']) // => 'application/json'\n\ntypeis(req, ['html']) // => false\n```\n\n### typeis.hasBody(request)\n\nReturns a Boolean if the given `request` has a body, regardless of the\n`Content-Type` header.\n\nHaving a body has no relation to how large the body is (it may be 0 bytes).\nThis is similar to how file existence works. If a body does exist, then this\nindicates that there is data to read from the Node.js request stream.\n\n```js\nif (typeis.hasBody(req)) {\n  // read the body, since there is one\n\n  req.on('data', function (chunk) {\n    // ...\n  })\n}\n```\n\n### typeis.is(mediaType, types)\n\nChecks if the `mediaType` is one of the `types`. If the `mediaType` is invalid\nor does not matches any of the `types`, then `false` is returned. Otherwise, a\nstring of the type that matched is returned.\n\nThe `mediaType` argument is expected to be a\n[media type](https://tools.ietf.org/html/rfc6838) string. The `types` argument\nis an array of type strings.\n\nEach type in the `types` array can be one of the following:\n\n- A file extension name such as `json`. This name will be returned if matched.\n- A mime type such as `application/json`.\n- A mime type with a wildcard such as `*/*` or `*/json` or `application/*`.\n  The full mime type will be returned if matched.\n- A suffix such as `+json`. This can be combined with a wildcard such as\n  `*/vnd+json` or `application/*+json`. The full mime type will be returned\n  if matched.\n\nSome examples to illustrate the inputs and returned value:\n\n```js\nvar mediaType = 'application/json'\n\ntypeis.is(mediaType, ['json']) // => 'json'\ntypeis.is(mediaType, ['html', 'json']) // => 'json'\ntypeis.is(mediaType, ['application/*']) // => 'application/json'\ntypeis.is(mediaType, ['application/json']) // => 'application/json'\n\ntypeis.is(mediaType, ['html']) // => false\n```\n\n### typeis.match(expected, actual)\n\nMatch the type string `expected` with `actual`, taking in to account wildcards.\nA wildcard can only be in the type of the subtype part of a media type and only\nin the `expected` value (as `actual` should be the real media type to match). A\nsuffix can still be included even with a wildcard subtype. If an input is\nmalformed, `false` will be returned.\n\n```js\ntypeis.match('text/html', 'text/html') // => true\ntypeis.match('*/html', 'text/html') // => true\ntypeis.match('text/*', 'text/html') // => true\ntypeis.match('*/*', 'text/html') // => true\ntypeis.match('*/*+json', 'application/x-custom+json') // => true\n```\n\n### typeis.normalize(type)\n\nNormalize a `type` string. This works by performing the following:\n\n- If the `type` is not a string, `false` is returned.\n- If the string starts with `+` (so it is a `+suffix` shorthand like `+json`),\n  then it is expanded to contain the complete wildcard notation of `*/*+suffix`.\n- If the string contains a `/`, then it is returned as the type.\n- Else the string is assumed to be a file extension and the mapped media type is\n  returned, or `false` is there is no mapping.\n\nThis includes two special mappings:\n\n- `'multipart'` -> `'multipart/*'`\n- `'urlencoded'` -> `'application/x-www-form-urlencoded'`\n\n## Examples\n\n### Example body parser\n\n```js\nvar express = require('express')\nvar typeis = require('type-is')\n\nvar app = express()\n\napp.use(function bodyParser (req, res, next) {\n  if (!typeis.hasBody(req)) {\n    return next()\n  }\n\n  switch (typeis(req, ['urlencoded', 'json', 'multipart'])) {\n    case 'urlencoded':\n      // parse urlencoded body\n      throw new Error('implement urlencoded body parsing')\n    case 'json':\n      // parse json body\n      throw new Error('implement json body parsing')\n    case 'multipart':\n      // parse multipart body\n      throw new Error('implement multipart body parsing')\n    default:\n      // 415 error code\n      res.statusCode = 415\n      res.end()\n      break\n  }\n})\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/jshttp/type-is/master?label=ci\n[ci-url]: https://github.com/jshttp/type-is/actions/workflows/ci.yml\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/type-is/master\n[coveralls-url]: https://coveralls.io/r/jshttp/type-is?branch=master\n[node-version-image]: https://badgen.net/npm/node/type-is\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/type-is\n[npm-url]: https://npmjs.org/package/type-is\n[npm-version-image]: https://badgen.net/npm/v/type-is\n[travis-image]: https://badgen.net/travis/jshttp/type-is/master\n[travis-url]: https://travis-ci.org/jshttp/type-is\n", "readmeFilename": "README.md", "users": {"ubi": true, "eyson": true, "iwill": true, "semir2": true, "x4devs": true, "flyslow": true, "kparkov": true, "parroit": true, "thiagoh": true, "konamgil": true, "makediff": true, "moimikey": true, "xgheaven": true, "yash3492": true, "mojaray2k": true, "rbecheras": true, "snowdream": true, "goodseller": true, "raycharles": true, "shuoshubao": true, "simplyianm": true, "ganeshkbhat": true, "jamescostian": true, "mansoorulhaq": true, "shanewholloway": true}}