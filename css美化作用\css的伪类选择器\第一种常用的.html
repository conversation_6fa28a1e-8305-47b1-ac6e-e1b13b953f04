<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        /* <!-- 伪类选择器是a:link/visited/hover/// --> */
        /* <!-- a:link选择没有访问过的链接 --> */
        a {
            text-decoration: none;
        }

        a:link {
            color: black;
        }

        /* a:visited选择已经访问过的链接 */
        a:visited {
            color: pink;
        }

        /* a:hover选择鼠标停留的链接 */
        a:hover {
            color: aqua;
        }

        a:active {
            color: red
        }

        /* 链接伪类用love hate原则来写 */
    </style>
</head>

<body>
    <a href="#">小猪佩奇</a>
    <a href="xxxxx.pxp">原本</a>
</body>

</html>