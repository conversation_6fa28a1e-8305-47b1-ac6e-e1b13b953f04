{"_id": "exit", "_rev": "27-4824f97312f53203e9f7ec2990fb970e", "name": "exit", "description": "A replacement for process.exit that ensures stdio are fully drained before exiting.", "dist-tags": {"latest": "0.1.2"}, "versions": {"0.1.0": {"name": "exit", "description": "A replacement for process.exit that ensures stdio are fully drained before exiting.", "version": "0.1.0", "homepage": "https://github.com/cowboy/node-exit", "author": {"name": "\"Cowboy\" <PERSON>", "url": "http://benalman.com/"}, "repository": {"type": "git", "url": "git://github.com/cowboy/node-exit.git"}, "bugs": {"url": "https://github.com/cowboy/node-exit/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/cowboy/node-exit/blob/master/LICENSE-MIT"}], "main": "lib/exit", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt nodeunit"}, "devDependencies": {"grunt-contrib-jshint": "~0.6.4", "grunt-contrib-nodeunit": "~0.2.0", "grunt-contrib-watch": "~0.5.3", "grunt": "~0.4.1", "async": "~0.2.9", "diff": "~1.0.7"}, "keywords": ["exit", "process", "stdio", "stdout", "stderr", "drain", "flush", "3584"], "readme": "# exit [![Build Status](https://secure.travis-ci.org/cowboy/node-exit.png?branch=master)](http://travis-ci.org/cowboy/node-exit)\n\nA replacement for process.exit that ensures stdio are fully drained before exiting.\n\nTo make a long story short, if `process.exit` is called on Windows, script output is often truncated when pipe-redirecting `stdout` or `stderr`. This module attempts to work around this issue by waiting until those streams have been completely drained before actually calling `process.exit`.\n\nSee [Node.js issue #3584](https://github.com/joyent/node/issues/3584) for further reference.\n\nTested in OS X 10.8, Windows 7 on Node.js 0.8.25 and 0.10.18.\n\nBased on some code by [@vladikoff](https://github.com/vladikoff).\n\n## Getting Started\nInstall the module with: `npm install exit`\n\n```javascript\nvar exit = require('exit');\n\n// These lines should appear in the output, EVEN ON WINDOWS.\nconsole.log(\"foo\");\nconsole.error(\"bar\");\n\n// process.exit(5);\nexit(5);\n\n// These lines shouldn't appear in the output.\nconsole.log(\"foo\");\nconsole.error(\"bar\");\n```\n\n## Don't believe me? Try it for yourself.\n\nIn Windows, clone the repo and cd to the `test\\fixtures` directory. The only difference between [log.js](test/fixtures/log.js) and [log-broken.js](test/fixtures/log-broken.js) is that the former uses `exit` while the latter calls `process.exit` directly.\n\n```\nC:\\node-exit\\test\\fixtures>node log.js 0 10 stdout stderr 2>&1 | find \"std\"\n[stdout] testing 0\n[stderr] testing 0\n[stdout] testing 1\n[stderr] testing 1\n[stdout] testing 2\n[stderr] testing 2\n[stdout] testing 3\n[stderr] testing 3\n[stdout] testing 4\n[stderr] testing 4\n[stdout] testing 5\n[stderr] testing 5\n[stdout] testing 6\n[stderr] testing 6\n[stdout] testing 7\n[stderr] testing 7\n[stdout] testing 8\n[stderr] testing 8\n[stdout] testing 9\n[stderr] testing 9\n\nC:\\node-exit\\test\\fixtures>node log-broken.js 0 10 stdout stderr 2>&1 | find \"std\"\n\nC:\\node-exit\\test\\fixtures>\n```\n\n## Contributing\nIn lieu of a formal styleguide, take care to maintain the existing coding style. Add unit tests for any new or changed functionality. Lint and test your code using [Grunt](http://gruntjs.com/).\n\n## Release History\n2013-09-20 - v0.1.0 - Initial release.\n\n## License\nCopyright (c) 2013 \"Cowboy\" Ben Alman  \nLicensed under the MIT license.\n", "readmeFilename": "README.md", "_id": "exit@0.1.0", "dist": {"shasum": "19c3ad63bacc715b3eda7accedebf42a73424fd8", "tarball": "https://registry.npmjs.org/exit/-/exit-0.1.0.tgz", "integrity": "sha512-NczZ4hhDq4uWmzGtlybQKpDGY49cGhrUxvcudcLdaeFMZa1SXgdRWkIUNTjsmZZZoiX8RL74yG8++jCF+lq7uA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEtejl/CeGVkgqDyrT2RkAt+nOYii3QXzfubrk8qIQ9pAiArJY6T0D6UFLZzAeaxMR2367WqP68yXo19Ynnzjpg2/w=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "cowboy", "email": "<EMAIL>"}, "maintainers": [{"name": "cowboy", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "exit", "description": "A replacement for process.exit that ensures stdio are fully drained before exiting.", "version": "0.1.1", "homepage": "https://github.com/cowboy/node-exit", "author": {"name": "\"Cowboy\" <PERSON>", "url": "http://benalman.com/"}, "repository": {"type": "git", "url": "git://github.com/cowboy/node-exit.git"}, "bugs": {"url": "https://github.com/cowboy/node-exit/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/cowboy/node-exit/blob/master/LICENSE-MIT"}], "main": "lib/exit", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt nodeunit"}, "devDependencies": {"grunt-contrib-jshint": "~0.6.4", "grunt-contrib-nodeunit": "~0.2.0", "grunt-contrib-watch": "~0.5.3", "grunt": "~0.4.1", "which": "~1.0.5"}, "keywords": ["exit", "process", "stdio", "stdout", "stderr", "drain", "flush", "3584"], "readme": "# exit [![Build Status](https://secure.travis-ci.org/cowboy/node-exit.png?branch=master)](http://travis-ci.org/cowboy/node-exit)\n\nA replacement for process.exit that ensures stdio are fully drained before exiting.\n\nTo make a long story short, if `process.exit` is called on Windows, script output is often truncated when pipe-redirecting `stdout` or `stderr`. This module attempts to work around this issue by waiting until those streams have been completely drained before actually calling `process.exit`.\n\nSee [Node.js issue #3584](https://github.com/joyent/node/issues/3584) for further reference.\n\nTested in OS X 10.8, Windows 7 on Node.js 0.8.25 and 0.10.18.\n\nBased on some code by [@vladikoff](https://github.com/vladikoff).\n\n## Getting Started\nInstall the module with: `npm install exit`\n\n```javascript\nvar exit = require('exit');\n\n// These lines should appear in the output, EVEN ON WINDOWS.\nconsole.log(\"omg\");\nconsole.error(\"yay\");\n\n// process.exit(5);\nexit(5);\n\n// These lines shouldn't appear in the output.\nconsole.log(\"wtf\");\nconsole.error(\"bro\");\n```\n\n## Don't believe me? Try it for yourself.\n\nIn Windows, clone the repo and cd to the `test\\fixtures` directory. The only difference between [log.js](test/fixtures/log.js) and [log-broken.js](test/fixtures/log-broken.js) is that the former uses `exit` while the latter calls `process.exit` directly.\n\nThis test was done using cmd.exe, but you can see the same results using `| grep \"std\"` in either PowerShell or git-bash.\n\n```\nC:\\node-exit\\test\\fixtures>node log.js 0 10 stdout stderr 2>&1 | find \"std\"\nstdout 0\nstderr 0\nstdout 1\nstderr 1\nstdout 2\nstderr 2\nstdout 3\nstderr 3\nstdout 4\nstderr 4\nstdout 5\nstderr 5\nstdout 6\nstderr 6\nstdout 7\nstderr 7\nstdout 8\nstderr 8\nstdout 9\nstderr 9\n\nC:\\node-exit\\test\\fixtures>node log-broken.js 0 10 stdout stderr 2>&1 | find \"std\"\n\nC:\\node-exit\\test\\fixtures>\n```\n\n## Contributing\nIn lieu of a formal styleguide, take care to maintain the existing coding style. Add unit tests for any new or changed functionality. Lint and test your code using [Grunt](http://gruntjs.com/).\n\n## Release History\n2013-09-26 - v0.1.1 - Fixed some bugs. It seems to actually work now!  \n2013-09-20 - v0.1.0 - Initial release.\n\n## License\nCopyright (c) 2013 \"Cowboy\" Ben Alman  \nLicensed under the MIT license.\n", "readmeFilename": "README.md", "_id": "exit@0.1.1", "dist": {"shasum": "8bf4af1e41fdb092476006764db9f750dfcddc4e", "tarball": "https://registry.npmjs.org/exit/-/exit-0.1.1.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>L9cOGDw5WWKzjJoIfxXdrtlt8E51acaZUdPZ73idYY/x4KLbthgbIuKr20lZ64KGKGY7DSwUfQzf/Ynytw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHkIYGkGTSoj0P1w0eoNqmDMOZB+wdB0dmiDd0DljO7SAiADpdLOCaO068r3Z2oBRkKu92M9r/IDWrL2CJfARuEk1A=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "cowboy", "email": "<EMAIL>"}, "maintainers": [{"name": "cowboy", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "exit", "description": "A replacement for process.exit that ensures stdio are fully drained before exiting.", "version": "0.1.2", "homepage": "https://github.com/cowboy/node-exit", "author": {"name": "\"Cowboy\" <PERSON>", "url": "http://benalman.com/"}, "repository": {"type": "git", "url": "git://github.com/cowboy/node-exit.git"}, "bugs": {"url": "https://github.com/cowboy/node-exit/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/cowboy/node-exit/blob/master/LICENSE-MIT"}], "main": "lib/exit", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt nodeunit"}, "devDependencies": {"grunt-contrib-jshint": "~0.6.4", "grunt-contrib-nodeunit": "~0.2.0", "grunt-contrib-watch": "~0.5.3", "grunt": "~0.4.1", "which": "~1.0.5"}, "keywords": ["exit", "process", "stdio", "stdout", "stderr", "drain", "flush", "3584"], "readme": "# exit [![Build Status](https://secure.travis-ci.org/cowboy/node-exit.png?branch=master)](http://travis-ci.org/cowboy/node-exit)\n\nA replacement for process.exit that ensures stdio are fully drained before exiting.\n\nTo make a long story short, if `process.exit` is called on Windows, script output is often truncated when pipe-redirecting `stdout` or `stderr`. This module attempts to work around this issue by waiting until those streams have been completely drained before actually calling `process.exit`.\n\nSee [Node.js issue #3584](https://github.com/joyent/node/issues/3584) for further reference.\n\nTested in OS X 10.8, Windows 7 on Node.js 0.8.25 and 0.10.18.\n\nBased on some code by [@vladikoff](https://github.com/vladikoff).\n\n## Getting Started\nInstall the module with: `npm install exit`\n\n```javascript\nvar exit = require('exit');\n\n// These lines should appear in the output, EVEN ON WINDOWS.\nconsole.log(\"omg\");\nconsole.error(\"yay\");\n\n// process.exit(5);\nexit(5);\n\n// These lines shouldn't appear in the output.\nconsole.log(\"wtf\");\nconsole.error(\"bro\");\n```\n\n## Don't believe me? Try it for yourself.\n\nIn Windows, clone the repo and cd to the `test\\fixtures` directory. The only difference between [log.js](test/fixtures/log.js) and [log-broken.js](test/fixtures/log-broken.js) is that the former uses `exit` while the latter calls `process.exit` directly.\n\nThis test was done using cmd.exe, but you can see the same results using `| grep \"std\"` in either PowerShell or git-bash.\n\n```\nC:\\node-exit\\test\\fixtures>node log.js 0 10 stdout stderr 2>&1 | find \"std\"\nstdout 0\nstderr 0\nstdout 1\nstderr 1\nstdout 2\nstderr 2\nstdout 3\nstderr 3\nstdout 4\nstderr 4\nstdout 5\nstderr 5\nstdout 6\nstderr 6\nstdout 7\nstderr 7\nstdout 8\nstderr 8\nstdout 9\nstderr 9\n\nC:\\node-exit\\test\\fixtures>node log-broken.js 0 10 stdout stderr 2>&1 | find \"std\"\n\nC:\\node-exit\\test\\fixtures>\n```\n\n## Contributing\nIn lieu of a formal styleguide, take care to maintain the existing coding style. Add unit tests for any new or changed functionality. Lint and test your code using [Grunt](http://gruntjs.com/).\n\n## Release History\n2013-11-26 - v0.1.2 - Fixed a bug with hanging processes.  \n2013-09-26 - v0.1.1 - Fixed some bugs. It seems to actually work now!  \n2013-09-20 - v0.1.0 - Initial release.\n\n## License\nCopyright (c) 2013 \"Cowboy\" Ben Alman  \nLicensed under the MIT license.\n", "readmeFilename": "README.md", "_id": "exit@0.1.2", "dist": {"shasum": "0632638f8d877cc82107d30a0fff1a17cba1cd0c", "tarball": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "integrity": "sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSMRcrwOkRC78/6olnT8OK0tmbrZC61YzQ52pLFdZJTQIgGS1tN4Pmh/dAXuMIhh0o3KHFFmkjcgDV63T7neNGLO4="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "cowboy", "email": "<EMAIL>"}, "maintainers": [{"name": "cowboy", "email": "<EMAIL>"}], "directories": {}}}, "readme": "# exit [![Build Status](https://secure.travis-ci.org/cowboy/node-exit.png?branch=master)](http://travis-ci.org/cowboy/node-exit)\n\nA replacement for process.exit that ensures stdio are fully drained before exiting.\n\nTo make a long story short, if `process.exit` is called on Windows, script output is often truncated when pipe-redirecting `stdout` or `stderr`. This module attempts to work around this issue by waiting until those streams have been completely drained before actually calling `process.exit`.\n\nSee [Node.js issue #3584](https://github.com/joyent/node/issues/3584) for further reference.\n\nTested in OS X 10.8, Windows 7 on Node.js 0.8.25 and 0.10.18.\n\nBased on some code by [@vladikoff](https://github.com/vladikoff).\n\n## Getting Started\nInstall the module with: `npm install exit`\n\n```javascript\nvar exit = require('exit');\n\n// These lines should appear in the output, EVEN ON WINDOWS.\nconsole.log(\"foo\");\nconsole.error(\"bar\");\n\n// process.exit(5);\nexit(5);\n\n// These lines shouldn't appear in the output.\nconsole.log(\"foo\");\nconsole.error(\"bar\");\n```\n\n## Don't believe me? Try it for yourself.\n\nIn Windows, clone the repo and cd to the `test\\fixtures` directory. The only difference between [log.js](test/fixtures/log.js) and [log-broken.js](test/fixtures/log-broken.js) is that the former uses `exit` while the latter calls `process.exit` directly.\n\n```\nC:\\node-exit\\test\\fixtures>node log.js 0 10 stdout stderr 2>&1 | find \"std\"\n[stdout] testing 0\n[stderr] testing 0\n[stdout] testing 1\n[stderr] testing 1\n[stdout] testing 2\n[stderr] testing 2\n[stdout] testing 3\n[stderr] testing 3\n[stdout] testing 4\n[stderr] testing 4\n[stdout] testing 5\n[stderr] testing 5\n[stdout] testing 6\n[stderr] testing 6\n[stdout] testing 7\n[stderr] testing 7\n[stdout] testing 8\n[stderr] testing 8\n[stdout] testing 9\n[stderr] testing 9\n\nC:\\node-exit\\test\\fixtures>node log-broken.js 0 10 stdout stderr 2>&1 | find \"std\"\n\nC:\\node-exit\\test\\fixtures>\n```\n\n## Contributing\nIn lieu of a formal styleguide, take care to maintain the existing coding style. Add unit tests for any new or changed functionality. Lint and test your code using [Grunt](http://gruntjs.com/).\n\n## Release History\n2013-09-20 - v0.1.0 - Initial release.\n\n## License\nCopyright (c) 2013 \"Cowboy\" Ben Alman  \nLicensed under the MIT license.\n", "maintainers": [{"name": "cowboy", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:07:30.572Z", "created": "2013-09-20T19:01:19.705Z", "0.1.0": "2013-09-20T19:01:21.371Z", "0.1.1": "2013-09-26T17:01:42.779Z", "0.1.2": "2013-11-26T19:24:48.883Z"}, "author": {"name": "\"Cowboy\" <PERSON>", "url": "http://benalman.com/"}, "repository": {"type": "git", "url": "git://github.com/cowboy/node-exit.git"}, "users": {"tomi77": true, "j3kz": true, "rouabhi": true, "itonyyo": true, "mccoyjordan": true, "bapinney": true, "mojaray2k": true, "faraoman": true, "cwagner": true, "flumpus-dev": true}}